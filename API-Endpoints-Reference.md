# RiaShop API Endpoints Reference

This document provides a comprehensive list of all available API endpoints in the RiaShop API.

## Base URL Structure
```
{base_url}/{module}/{action}?logtoken={logtoken}&token={token}
```

## Authentication Endpoints

### System Health
- `GET /` - API health check and tenant validation

### Device Authentication
- `GET /devices/` - Device authentication and user login
- `GET /devices/config` - Device configuration
- `GET /devices/debug` - Device debug information
- `GET /devices/locations` - Device locations
- `GET /devices/logs` - Device logs
- `GET /devices/meta` - Device metadata
- `GET /devices/need_sync` - Check if device needs synchronization
- `GET /devices/ping` - Device ping
- `GET /devices/refresh` - Refresh device
- `GET /devices/request_sync` - Request device synchronization
- `GET /devices/restrictions` - Device restrictions
- `GET /devices/rights` - Device rights
- `GET /devices/states` - Device states
- `GET /devices/sync` - Device synchronization
- `GET /devices/sync_all` - Synchronize all devices
- `GET /devices/sync_bulk` - Bulk device synchronization
- `GET /devices/task` - Device tasks

## User Management

### Users
- `GET /users/` - Get users list
- `POST /users/` - Create new user
- `PUT /users/` - Update user
- `DELETE /users/` - Delete user
- `GET /users/bank` - User bank information
- `GET /users/control` - User control information
- `GET /users/deleted` - Deleted users
- `GET /users/is_sync` - Check user sync status
- `GET /users/payments` - User payment methods
- `GET /users/price_categories` - User price categories
- `GET /users/sell_units_rules` - User selling unit rules
- `GET /users/seller` - User seller information
- `GET /users/stats` - User statistics
- `POST /users/undelete` - Restore deleted user
- `GET /users/zones` - User zones

### Addresses
- `GET /addresses/` - Get addresses
- `GET /addresses/deleted` - Deleted addresses
- `GET /addresses/geoloc` - Address geolocation

### Profiles
- `GET /profiles/` - Get user profiles

## Product Management

### Products
- `GET /products/` - Get products catalog
- `POST /products/classify` - Classify products
- `GET /products/relations` - Product relations
- `GET /products/resellers` - Product resellers
- `GET /products/stats` - Product statistics

### Categories
- `GET /categories/` - Get categories
- `POST /categories/` - Create category
- `PUT /categories/` - Update category

### Brands
- `GET /brands/` - Get brands
- `GET /brands/count` - Brand count

### Prices
- `GET /prices/` - Get prices
- `GET /prices/categories` - Price categories
- `GET /prices/tvas` - Tax rates (TVA)

### Stocks
- `GET /stocks/` - Get stock information
- `GET /stocks/deposits` - Stock deposits
- `GET /stocks/stats` - Stock statistics

## Order Management

### Orders
- `GET /orders/` - Get orders
- `POST /orders/complete` - Complete order
- `GET /orders/get-list` - Get order list
- `GET /orders/ids` - Get order IDs
- `GET /orders/need_sync` - Orders needing sync
- `POST /orders/pay` - Process order payment
- `GET /orders/pdf` - **Generate order PDF** (supports id parameter)
- `GET /orders/piece` - Order piece information
- `GET /orders/products` - Order products
- `GET /orders/seller` - Order seller information
- `GET /orders/signature` - Order signature
- `GET /orders/state` - Order state
- `GET /orders/stats` - Order statistics
- `GET /orders/totals` - Order totals
- `POST /orders/unmask` - Unmask order

### Delivery Notes (BL)
- `GET /bl/` - Get delivery notes
- `POST /bl/complete` - Complete delivery note
- `GET /bl/pdf` - **Generate delivery note PDF** (supports id parameter)
- `GET /bl/piece` - Delivery note piece
- `GET /bl/state` - Delivery note state
- `GET /bl/tracking` - Delivery tracking

### Invoices
- `GET /invoices/` - Get invoices
- `POST /invoices/complete` - Complete invoice
- `GET /invoices/stats` - Invoice statistics

### Returns
- `GET /return/` - Get returns
- `POST /return/complete` - Complete return
- `GET /return/pdf` - **Generate return PDF** (supports id parameter)
- `GET /return/piece` - Return piece
- `GET /return/state` - Return state
- `GET /return/states` - Return states

## Inventory & Logistics

### Delivery
- `GET /delivery/event` - Delivery events
- `GET /delivery/expedition_period` - Expedition periods
- `GET /delivery/holiday` - Delivery holidays
- `GET /delivery/packages` - Delivery packages

### Serials
- `GET /serials/` - Get serial numbers

### Bordereaux
- `GET /bordereaux/` - Get bordereaux

## Communication & Collaboration

### Chats
- `GET /chats/` - Get chats
- `GET /chats/conversations` - Chat conversations
- `GET /chats/messages` - Chat messages

### Calls
- `GET /calls/` - Get calls
- `GET /calls/need_sync` - Calls needing sync

### Notifications
- `GET /notifications/` - Get notifications
- `POST /notifications/send` - Send notification

### Email
- `POST /email/send` - Send email

## Content Management

### Images
- `GET /images/` - Get images
- `GET /images/file` - **Download image file** (supports id parameter)
- `GET /images/object` - Get image object

### Documents
- `GET /documents/file` - **Download document file** (supports id parameter)
- `POST /documents/file` - **Upload document file** (multipart/form-data)
- `GET /documents/objects` - Get document objects
- `GET /documents/types` - Get document types

### Notes
- `GET /notes/` - Get notes

## System & Configuration

### Fields
- `GET /fields/object_values` - Get field object values
- `GET /fields/restricted_values` - Get field restricted values

### Banks
- `GET /banks/` - Get bank information

### Check-in
- `GET /check-in/` - Check-in functionality

### Activity
- `GET /activity/` - Get activity logs

## Internationalization

### I18n
- `GET /i18n/` - Get internationalization data
- `GET /i18n/countries` - Get countries list
- `GET /i18n/currencies` - Get currencies list
- `GET /i18n/languages` - Get languages list

## Data Management

### Exports
- `GET /exports/` - Get exports status and list
- `POST /exports/` - **Create comprehensive data exports**
  - **Products Export** (cls_id: 1) - CSV/Excel with customizable columns, categories, brands, languages
  - **Categories Export** (cls_id: 2) - CSV/Excel with hierarchy support, image sizes, exclusions
  - **Orders Export** (cls_id: 3) - Excel with dual-sheet layout (orders + order lines), advanced filtering
  - **Delivery Notes Export** (cls_id: 4) - Excel format with seller/depot filtering
  - Supports Excel (.xls) and CSV formats
  - Supports Mac encoding and Excel-specific formatting
  - Advanced filtering by date ranges, categories, brands, users, states
  - Multi-language support for product exports
  - Image export capabilities with size specifications

### Imports
- `GET /imports/` - Get imports status and list

### Search
- `GET /search/` - Search functionality
- `POST /search/rebuild` - Rebuild search index

### Reports
- `GET /reports/` - Get reports
- `GET /reports/need_sync` - Reports needing sync
- `GET /reports/pdf` - **Generate report PDF** (supports id parameter)
- `GET /reports/ref` - Report reference

## Synchronization

### RiaShop Sync
- `GET /riashopsync/action` - Sync actions
- `GET /riashopsync/config` - Sync configuration
- `GET /riashopsync/file` - Sync files
- `GET /riashopsync/logs` - Sync logs
- `GET /riashopsync/need_sync` - Items needing sync
- `GET /riashopsync/status` - Sync status

### General Sync
- `POST /sync/salesforce_img_send` - Send images to Salesforce
- `POST /sync/salesforce_linears_send` - Send linear data to Salesforce
- `POST /sync/salesforce_orders_send` - Send orders to Salesforce
- `POST /sync/salesforce_save_row` - Save row to Salesforce
- `POST /sync/salesforce_users_send` - Send users to Salesforce

## Multi-tenant Management

### Tenants
- `POST /tenants/create` - Create tenant
- `GET /tenants/websites` - Get tenant websites
- `GET /tenants/yuto` - Yuto tenant information

## Marketing & Promotions

### Promotions
- `GET /promotions/specials` - Get special promotions

### Rewards
- `GET /rewards/products` - Reward products
- `GET /rewards/stats` - Reward statistics

### Wishlists
- `GET /wishlists/` - Get wishlists

## Advanced Features

### Actions
- `GET /actions/` - Get actions
- `GET /actions/hierarchy` - Action hierarchy
- `GET /actions/history` - Action history
- `GET /actions/restrictions` - Action restrictions

### Relations
- `GET /relations/` - Get relations

### Raiseds
- `POST /raiseds/complete` - Complete raised items

## PDF Generation & File Operations

### PDF Generation Endpoints
- `GET /orders/pdf` - **Generate Order PDF** (id parameter required)
- `GET /bl/pdf` - **Generate Delivery Note PDF** (id parameter required)
- `GET /return/pdf` - **Generate Return PDF** (id parameter required)
- `GET /reports/pdf` - **Generate Report PDF** (id parameter required)

### File Download Endpoints
- `GET /documents/file` - **Download Document File** (id parameter required)
- `GET /images/file` - **Download Image File** (id parameter required)
- `GET /riashopsync/file` - **Download Sync File** (sync-related files)

### File Upload Endpoints
- `POST /documents/file` - **Upload Document File** (multipart/form-data)
- `POST /upload-temp/` - **Temporary File Upload**

### Export File Generation
- `POST /exports/` - **Generate Export Files** (CSV/Excel)
  - Products export to CSV/Excel
  - Categories export to CSV/Excel
  - Orders export to Excel (dual-sheet)
  - Delivery notes export to Excel
  - Advanced filtering and formatting options

## Utility Files
- `GET /robots.txt` - Robots.txt file
- `/utils.php` - Utility functions (not a direct endpoint)

## Notes

1. **Authentication Required**: Most endpoints require both `logtoken` and `token` parameters
2. **HTTP Methods**: Each endpoint supports specific HTTP methods (GET, POST, PUT, DELETE)
3. **Parameters**: Each endpoint has specific required and optional parameters
4. **Response Format**: All endpoints return JSON in the standard RiaShop format
5. **PHP 5.6 Compatible**: All endpoints are built with PHP 5.6 compatibility

For detailed parameter information and request/response examples, refer to the Postman collection and README documentation.
