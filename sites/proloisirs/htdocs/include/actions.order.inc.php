<?php
	require_once('view.product.inc.php');
	require_once('view.order.inc.php');
	require_once('view.emails.inc.php');

	$redirection 	= isset($redirection) && trim($redirection) != '' ? $redirection : $config['url_cart'];
	$ord_id 		= isset($_SESSION['ord_id']) ? $_SESSION['ord_id'] : false;

	$ctrl_connect 	= isset($ctrl_connect)  && $ctrl_connect === true;				// Contrôler la connexion
	$ctrl_order 	= isset($ctrl_order)  && $ctrl_order === true;					// Contrôler l'existance d'un panier en cours
	$reload_cart 	= isset($reload_cart) && $reload_cart === true;					// Recharger les informations du panier après avoir réaliser l'action

	$is_ajax 		= ( !isset($no_ajax) || $no_ajax === false ) && IS_AJAX;		// Appel en AJAX
	$in_popup		= isset($in_popup) && $in_popup === true;						// Appel dans une popup
	$get_order		= isset($get_order) && $get_order === true;						// Récupère les informations du panier en cours

	if( $ctrl_connect ){
		if( !gu_users_is_connected() ){
			$dest = rew_rewritemap_translate_get( $redirection, i18n::getLang() );
			header('Location: '.rew_rewritemap_translate_get( '/connexion/', i18n::getLang() ).'?dest='.urlencode($dest));
			exit;
		}
	}

	// Vérifie qu'il dispose d'une commande en cours et qu'elle est à l'état de panier
	if( $ctrl_order ){
		if( !isset($_SESSION['ord_id']) || !ord_orders_exists($_SESSION['ord_id'], $config['user_id'], $config['ar_state_ord_exists']) ){
			header('Location: '.$config['url_cart']);
			exit;
		}
	}

	// Toutes les actions suivantes ne peuvent être fait que sur une commande à l'état de panier / panier sauvegardé
	if( isset($_SESSION['ord_id']) && !ord_orders_exists($_SESSION['ord_id'], $config['user_id'], $config['ar_state_ord_exists']) ){
		unset( $_SESSION['ord_id'] );
	}

	# TODO : compléter cette variable en fonction des différents ajouts possible (batch, par référence...)
	$create_if_not_exists = isset($_POST['add-to-cart']) || isset($_POST['add_nom_v']) || isset($_POST['add-collection']);

	// Création d'un panier s'il n'existe pas
	if( $create_if_not_exists ){
		if( !ord_carts_add_if_not_exists() ){
			$error[] = "Une erreur inattendue est survenue lors de la création de votre panier.<br />Veuillez réessayer ou prendre <a href=\"#param[url contact]#\">contact avec nous</a>.";
		}
	}

	// Modification d'une adresse de livraison.
	if( isset($_POST['save-adr']) ){
		if( isset($_POST['is_delivery']) ){
			if( $_POST['adr-id'] == $_SESSION['usr_adr_invoices'] ){
				$_POST['adr-id'] = 0;
			}
		}

		$user = $config['user'];

		$error = false;

		$_POST['title'] = isset($_POST['title']) ? $_POST['title'] : '';
		$_POST['firstname'] = isset($_POST['firstname']) ? proloisirs_sanitize_string(trim($_POST['firstname'])) : '';
		$_POST['lastname'] = isset($_POST['lastname']) ? proloisirs_sanitize_string(trim($_POST['lastname'])) : '';
		$_POST['address1'] = isset($_POST['address1']) ? proloisirs_sanitize_string(trim($_POST['address1'])) : '';
		$_POST['address2'] = isset($_POST['address2']) ? proloisirs_sanitize_string(trim($_POST['address2'])) : '';
		$_POST['city'] = isset($_POST['city']) ? proloisirs_sanitize_string(trim($_POST['city'])) : '';
		$_POST['zipcode'] = isset($_POST['zipcode']) ? trim($_POST['zipcode']) : '';
		$_POST['email'] = isset($_POST['email']) ? trim($_POST['email']) : (isset($user['email']) ? $user['email'] : '');
		$_POST['phone'] = isset($_POST['phone']) ? proloisirs_sanitize_phone_number($_POST['phone']) : '';
		$_POST['country'] = isset($_POST['country']) ? mb_strtoupper(trim($_POST['country'])) : 'FR_FRANCE';

		if( !$_POST['title'] || !$_POST['firstname'] || !$_POST['lastname'] || !$_POST['address1'] || !$_POST['zipcode'] || !$_POST['city'] || !$_POST['country'] || !$_POST['phone'] || !$_POST['email'] ){
			$error = 1;
		}elseif( $_POST['country'] === 'FR_FRANCE' && !iszipcode($_POST['zipcode']) ){
			$error = 2;
		}elseif( !isphone($_POST['phone']) || mb_strlen($_POST['phone']) !== 10 ){
			$error = 3;
		}elseif( !gu_valid_email($_POST['email']) ){
			$error = 7;
		}elseif(strlen($_POST['address1']) > 35 || strlen($_POST['address2']) > 35){// Vérification de la limite de tailles des champs adresses
			$error = 6;
		}

		if( !$error ){
			$address_id = isset($_POST['adr-id']) ? $_POST['adr-id'] : 0;
			$res_country = explode('_', $_POST['country']);

			if( !$address_id ){
				$address_id = gu_adresses_add($config['user_id'], 1, $_POST['title'], $_POST['firstname'], $_POST['lastname'], '', '', $_POST['address1'], $_POST['address2'], $_POST['zipcode'], $_POST['city'], proloisirs_sanitize_string($res_country[1]), $_POST['phone'], '', '', '', $_POST['title'], $_POST['email'], $res_country[0]);

				if( !$address_id ){
					$error = 4;
				}
			}else{
				$res = gu_adresses_update($config['user_id'], $address_id, 1, $_POST['title'], $_POST['firstname'], $_POST['lastname'], '', '', $_POST['address1'], $_POST['address2'], $_POST['zipcode'], $_POST['city'], proloisirs_sanitize_string($res_country[1]), $_POST['phone'], '', '', '', false, $_POST['title'], $_POST['email'], $res_country[0]);

				if( !$res ){
					$error = 5;
				}
			}
		}

		if( $error ){
			switch( $error ){
				case 1:
					$error = 'Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.<br>Les champs marqués d\'une « * » sont obligatoires.';
					break;
				case 2:
					$error = 'Le code postal saisi ne semble pas correct.';
					break;
				case 3:
					$error = 'Veuillez renseigner un numéro de téléphone (fixe ou mobile) valide.';
					break;
				case 4:
					$error = 'Une erreur inattendue est survenue lors de l\'enregistrement de votre nouvelle adresse de livraison.<br>Merci de prendre <a href="#param[url contact]#">contact avec nous</a> pour nous signaler l\'erreur.';
					break;
				case 5:
					$error = 'Une erreur inattendue est survenue lors de de la mise à jour de votre adresse.<br>Merci de prendre <a href="#param[url contact]#">contact avec nous</a> pour nous signaler l\'erreur.';
				case 6:
					$error = "Les champs d'adresses sont limités à 35 caractères. \nMerci de vérifier votre saisie.";
					break;
				case 7:
					$error = 'Veuillez renseigner une adresse email valide.';
					break;
			}
		}else{
			$redirection = isset($_POST['in_account']) ? '/mon-compte/adresses/' : '/commander/livraison/';

			if( !isset($_POST['in_account']) ){
				ord_orders_adr_delivery_set($_SESSION['ord_id'], $address_id);
			}

			header('Location: '.$redirection);
			exit;
		}
	}

	// Supprime une adresse de livraison alternative
	if( isset($_POST['del-adr']) ){
		$redirection = '/commander/livraison/';
		if( isset($_POST['in_account']) ){
			$redirection = '/mon-compte/adresses/';
		}

		if( isset($_POST['adr-other-id']) && is_numeric($_POST['adr-other-id']) && $_POST['adr-other-id'] ){
			if( !gu_adresses_set_masked($_POST['adr-other-id']) ){
				$error = "Une erreur inattendue est survenue lors de la suppression de votre adresse livraison. <br />Merci de prendre <a href=\"#param[url contact]#\">contact avec nous</a> pour nous signaler l'erreur.";
			}else{
				$_SESSION['del-adr-ok'] = true;
			}
		}
	}

	// Reprendre un panier sauvegardé
	if (isset($_POST['resume-cart'])) {
		// Sauvegarde le panier en cours
		if (isset($_SESSION['ord_id'])) {
			ord_orders_update_status($_SESSION['ord_id'], _STATE_BASKET_SAVE);
		}

		ord_orders_update_status( $_GET['ord'], _STATE_BASKET );
		$_SESSION['ord_id'] = $_GET['ord'];
	}

	// Toutes les actions qui suivent nécessite qu'un panier soit déjà créé
	if( isset($_SESSION['ord_id']) && ord_orders_exists($_SESSION['ord_id'], $config['user_id'], $config['ar_state_ord_exists']) ){

		if( isset($_POST['next-cart-step']) ){
			$redirect = $config['url_cart_select_delivery'];

			if( proloisirs_order_is_home_delivery($_SESSION['ord_id']) ){
				if( 	!ord_orders_set_relay($_SESSION['ord_id'], null)
					|| 	!ord_orders_set_dlv_store($_SESSION['ord_id'])
				){
					$error = i18n::get("Une erreur inattendue est survenue. Veuillez réessayer.");
				}
				$redirect = $config['url_cart_delivery'];
			}

			if( isset($_POST['dlv-check']) ) {
				$usr_consent = false;
				fld_object_values_set($_SESSION['ord_id'], $config['fld_ord_requires_ease'], '');

				if( isset($_POST['dlv-access']) && $_POST['dlv-access'] == 1 ){
					if( fld_object_values_set($_SESSION['ord_id'], $config['fld_ord_requires_ease'], 'Oui') ){
						$usr_consent = true;
					}
				}

				if( !$usr_consent ){
					$error = i18n::get('Votre panier comporte un produit volumineux soumis à un système de livraison spécifique. Vous devez attester de l\'accessibilité de votre domicile afin de finaliser votre commande.');
				}
			}

			if( !isset($error) && isset($_POST['pickup-check']) ) {
				$usr_consent = false;
				fld_object_values_set($_SESSION['ord_id'], $config['fld_ord_pickup_store_ease'], '');

				if( isset($_POST['pickup-access']) && $_POST['pickup-access'] == 1 ){
					if( fld_object_values_set($_SESSION['ord_id'], $config['fld_ord_pickup_store_ease'], 'Oui') ){
						$usr_consent = true;
					}
				}

				if( !$usr_consent ){
					$error = i18n::get('Vous devez attester avoir pris connaissance des contraintes de dimensions et de poids des colis, et avoir pris mes dispositions afin de récupérer votre commande en magasin.');
				}
			}

			if( !isset($error) ){
				$redirect = $config['url_cart_delivery'];
				header('Location: '.$redirect);
				exit;
			}
		}

		if (isset($_POST['add-collection'])) { // Ajout au panier (à partir d'une collection)
			$redirection = $config['url_cart'];

			$obj_add_to_cart = array();
			foreach ($_POST['qty'] as $key => $qty) {
				if (!is_numeric($qty) || $qty <= 0) {
					continue;
				}

				if (isset($_POST['opt']) && array_key_exists($key, $_POST['opt'])) {
					$data = array();

					foreach ($_POST['opt'][$key] as $optkey => $optval) {
						$data['opt-'.$optkey] = $optval;
					}

					$data = array_merge($data, array(
						'qty' => array($key => $_POST['qty'][$key]),
						'prd' => array($key => $_POST['prd'][$key]),
						'col' => array($key => $_POST['col'][$key]),
						'line' => array($key => $_POST['line'][$key])
					));

					$res = proloisirs_add_nomenclature($data);
					$obj_add_to_cart = array_merge($obj_add_to_cart, $res['obj_add_to_cart']);

					if ($res['error']) {
						$error = $res['error'];
					}else{
						$_SESSION['analytics'] = 'add-to-cart';
					}
				}else{
					$data = array(
						'qty' => array($key => $_POST['qty'][$key]),
						'prd' => array($key => $_POST['prd'][$key]),
						'col' => array($key => $_POST['col'][$key]),
						'line' => array($key => $_POST['line'][$key])
					);

					if (isset($_POST['select-child'][$key]) && is_numeric($_POST['select-child'][$key]) && $_POST['select-child'][$key]) {
						$data['select-child'] = $_POST['select-child'][$key];
					}

					$res = proloisirs_add_to_cart($data);

					$obj_add_to_cart = array_merge($obj_add_to_cart, $res['obj_add_to_cart']);
					if ($res['error']) {
						$error = $res['error'];
					}else{
						$_SESSION['analytics'] = 'add-to-cart';
					}
				}
			}
		} elseif (isset($_POST['add_nom_v'])) { // Ajout au panier (produit nomenclature)
			$redirection = $config['url_cart'];

			if (!isset($_POST['qty'])) {
				$error = "Un ou plusieurs paramètres obligatoires sont manquants. <br />Veuillez réessayer ou prendre <a href=\"#param[url contact]#\">contact avec nous</a>.";
			} else {
				$res = proloisirs_add_nomenclature($_POST);
				$obj_add_to_cart = $res['obj_add_to_cart'];
				if ($res['error']) {
					$error = $res['error'];
				}else{
					$_SESSION['analytics'] = 'add-to-cart';
				}
			}
		} elseif (isset($_POST['add-to-cart'])) { // Ajout au panier (produit enfant ou standard)
			$redirection = $config['url_cart'];

			if( !isset($_POST['qty']) ){
				$error = "Un ou plusieurs paramètres obligatoires sont manquants. <br />Veuillez réessayer ou prendre <a href=\"#param[url contact]#\">contact avec nous</a>.";
			}else{
				$res = proloisirs_add_to_cart($_POST);
				$obj_add_to_cart = $res['obj_add_to_cart'];
				if ($res['error']) {
					$error = $res['error'];
				}else{
					$_SESSION['analytics'] = 'add-to-cart';
				}
			}
		}

		// Mise à jour d'une ligne de commande
		if( isset($_POST['upd-line-cart']) ){
			if (isset($_POST['qty'], $_POST['prd'], $_POST['line'])) {
				foreach ($_POST['qty'] as $key => $qty) {
					if (!is_numeric($qty) || $qty <= 0) {
						continue;
					}

					if (!is_array($_POST['prd']) || !array_key_exists($key, $_POST['prd']) || !is_array($_POST['line']) || !array_key_exists($key, $_POST['line'])) {
						$error = "Un ou plusieurs paramètres obligatoires sont manquants. <br />Veuillez réessayer ou prendre <a href=\"#param[url contact]#\">contact avec nous</a>.";
					} else {
						$col_id = 0;
						$line = $_POST['line'][$key];
						$prd_id = $_POST['prd'][$key];

						if (isset($_POST['col-id']) && is_numeric($_POST['col-id']) && $_POST['col-id']) {
							if (!prd_colisage_classify_exists($prd_id, $_POST['col-id'])) {
								$error = i18n::get("Une erreur inattendue est survenue lors de la mise à jour de la quantité.\nMerci de prendre contact avec nous pour nous signaler l'erreur.");
							}

							$col_id = $_POST['col-id'];
						}

						$rproduct = prd_products_get_simple($prd_id);
						if (!$rproduct || !ria_mysql_num_rows($rproduct)) {
							$error = i18n::get("Une erreur inattendue est survenue lors de la mise à jour de la quantité.\nMerci de prendre contact avec nous pour nous signaler l'erreur.");
						}

						if (!isset($error)) {
							$product = ria_mysql_fetch_assoc($rproduct);

							$out = false;
							$ch_array = null;
							$childline = -1;

							if ($product['nomenclature_type'] == NM_TYP_VARIABLE) {
								$oprd = ria_mysql_fetch_assoc(ord_products_get($_SESSION['ord_id'], false, $prd_id, '', $_POST['line']));

								$ch_array = array();
								$childline = $oprd['child-line'];

								if ($rchild = ord_products_get($_SESSION['ord_id'], false, 0, '', null, false, -1, $oprd['id'], $oprd['child-line'])) {
									while ($c = ria_mysql_fetch_array($rchild)) {
										$ch_array[$c['opt']] = $c['id'];
									}
								}

								$stock_min = view_product_stock($product, $ch_array);
								if ($qty > $stock_min) {
									$qty = $stock_min;
								}
							} else {
								$res_stock = view_product_stock($product);
								if ($qty > $res_stock) {
									$qty = $res_stock;
								}
							}

							if (!$out) {
								if ($qty<=0) {
									$error = i18n::get("L'article souhaite est actuellement indisponible.");
								} else {
									// Détermine un conditionnement
									$val = fld_object_values_get($prd_id, $config['fld_prd_col_ldd']);
									if (is_numeric($val) && $val != 0 && $qty < $val) {
										$error = str_replace(
											'#param[quantité minimal]#',
											(int)$val,
											i18n::get('La quantité minimale commandable pour ce produit est de #param[quantité minimal]#.')
										);
									}

									if (!isset($error)) {
										$reseller = false;
										if( is_app_borne() && isset($_SESSION['CONNECTED_BORNE_USER']) ){
											$reseller = [
												'id' => $_SESSION['CONNECTED_BORNE_USER']['id'],
												'wst_id' => $config['get_prd_params']['prs_wst_id']
											];
										}

										// Prépare l'insertion dans le panier
										if (!ord_products_update($_SESSION['ord_id'], $prd_id, $qty, $childline, $col_id, $line, false, $ch_array, $reseller, 0)) {
											$error = i18n::get("Une erreur inattendue est survenue lors de la mise à jour de la quantité.\nMerci de prendre contact avec nous pour nous signaler l'erreur.");
										}else{
											// Met à jour les quantités sur les produits liés
											$ar_linked = proloisirs_product_line_get_products($_SESSION['ord_id'], $prd_id);

											if( $ar_linked ){
												foreach($ar_linked as $lnk){
													ord_products_update($_SESSION['ord_id'], $lnk['id'], $qty);
												}
											}
										}

										if (!isset($error) && !calculated_port_in_order()) {
											$error = i18n::get("Une erreur inattendue est survenue lors de la mise à jour de la quantité.\nMerci de prendre contact avec nous pour nous signaler l'erreur.");
										}
									}
								}
							} else {
								$error = i18n::get('Une erreur est survenue pendant la mise à jour de vos quantités.');
							}
						}
					}
				}
			}

			/* if( isset($_POST['qty'], $_POST['prd'], $_POST['line']) ){
				foreach( $_POST['qty'] as $key=>$qty ){
					if (!is_numeric($qty) || $qty <= 0) {
						continue;
					}

					if( !is_array($_POST['prd']) || !array_key_exists($key, $_POST['prd']) || !is_array($_POST['line']) || !array_key_exists($key, $_POST['line']) ){
						$error = "Un ou plusieurs paramètres obligatoires sont manquants. <br />Veuillez réessayer ou prendre <a href=\"#param[url contact]#\">contact avec nous</a>.";
					}else{
						$colisage = array_key_exists($key, $_POST['col']) && is_numeric($_POST['col'][$key]) && $_POST['col'][$key] > 0 ? $_POST['col'][$key] : false;

						$r_oprd = ord_products_get( $_SESSION['ord_id'], false, $_POST['prd'][ $key ], '', ($_POST['line'][ $key ] != "0" ? $_POST['line'][ $key ] : null), false, 0, 0, -1, false, false, 0, false, false, $colisage );
						if( !$r_oprd || !ria_mysql_num_rows($r_oprd) ){
							$error[] = "Une erreur inattendue est survenue lors de la mise à jour de votre panier.<br />Veuillez réessayer ou prendre <a href=\"#param[url contact]#\">contact avec nous</a>.";
						}else{
							$oprd = ria_mysql_fetch_assoc( $r_oprd );

							$product = ria_mysql_fetch_assoc( prd_products_get_simple($oprd['id']) );
							if( !$config['ord_product_nostock'] && $product['follow_stock'] ){
								if ($config['use_sales_unit']) {
									$unit_sell = fld_object_values_get( $product['id'], _FLD_PRD_SALES_UNIT );

									if (is_numeric($unit_sell) && $unit_sell > 0) {
										$product['stock'] = floor( $product['stock'] / $unit_sell ) * $unit_sell;
									}
								}

								if( $qty >= $product['stock']){
									$_SESSION['cart_qty_max'] = array( 'prd_id' => $oprd['id'], 'stock' => $product['stock'] );
									$qty = $product['stock'];
								}
							}

							if( !ord_products_update($_SESSION['ord_id'], $oprd['id'], $qty, -1, ($colisage ? $colisage : 0), $oprd['line']) ){
								$error[] = "Une erreur inattendue est survenue lors de la mise à jour de votre panier.<br />Veuillez réessayer ou prendre <a href=\"#param[url contact]#\">contact avec nous</a>.";
							}
						}
					}
				}
			} */

			if( !isset($error) ){
				$reload_cart = true;
			}
		}

		// Suppression d'une ligne de commande
		if( isset($_POST['del-line-cart']) ){
			$is_ok = true;
			foreach (array('prd', 'line') as $key=>$val) {
				if (!isset($_POST[ $val ]) || !is_numeric($_POST[ $val ])) {
					if ($key == 1 && $_POST[ $val ] < 0) {
						$is_ok = false;
					}elseif ($_POST[ $val ] <= 0) {
						$is_ok = false;
					}
				}
			}

			if (!$is_ok) {
				$error = "Un ou plusieurs paramètres obligatoires sont manquants, l'article n'a donc pas été retiré de votre panier.<br />Veuillez réessayer ou prendre <a href=\"#param[url contact]#\">contact avec nous</a>.";
			}else{
				$r_oprd = ord_products_get( $_SESSION['ord_id'], false, $_POST['prd'], '', $_POST['line'] );

				if( !$r_oprd || !ria_mysql_num_rows($r_oprd) ){
					$error[] = "Une erreur inattendue est survenue lors de la suppression de l'article.<br />Veuillez réessayer ou prendre <a href=\"#param[url contact]#\">contact avec nous</a>.";
				}else{
					$oprd = ria_mysql_fetch_assoc( $r_oprd );
					$chl = is_numeric( $oprd['child-line'] ) && $oprd['child-line'] >= 0 ? $oprd['child-line'] : -1;

					if( !ord_products_del($_SESSION['ord_id'], $_POST['prd'], $_POST['line'], $chl) ){
						$error[] = "Une erreur inattendue est survenue lors de la suppression de l'article de votre panier.<br />Veuillez réessayer ou prendre <a href=\"#param[url contact]#\">contact avec nous</a>.";
					}else{
						// Met à jour les quantités sur les produits liés
						$ar_linked = proloisirs_product_line_get_products($_SESSION['ord_id'], $_POST['prd']);

						if( $ar_linked ){
							foreach($ar_linked as $lnk){
								ord_products_del($_SESSION['ord_id'], $lnk['id']);
							}
						}
					}
				}
			}
		}

		// Controle du code promotion
		$ord_pmt_test = ord_orders_get_pmt_id($_SESSION['ord_id']);
		if ($ord_pmt_test) {
			$rcode = pmt_codes_get($ord_pmt_test);

			if (!$rcode || !ria_mysql_num_rows($rcode)) {
				$info = 'Le code promotion présent dans votre commande n\'est plus disponible. Celui-ci a donc été retiré.';
				pmt_codes_cancel($_SESSION['ord_id']);
			} else {
				$code = ria_mysql_fetch_array($rcode);
				$applicable = pmt_codes_is_applicable($code['code'], $_SESSION['ord_id']);

				if ($applicable !== true) {
					pmt_codes_cancel($_SESSION['ord_id']);
				}
			}
		}

		// Ajout d'un code promotion
		if (isset($_POST['add-code-promo'])) {
			if (trim($_POST['code-promo']) != '') {
				$applicable = pmt_codes_is_applicable($_POST['code-promo'], $_SESSION['ord_id']);
				$applicable = true;
				if ($applicable === true) {
					// Le code est valide et peut s'appliquer à la commande
					if (!pmt_codes_apply($_POST['code-promo'], $_SESSION['ord_id'])) {
						$error = 'Une erreur inconnue est survenue lors de l\'application de votre code promotion sur votre commande.';
					}
				} else {
					$_SESSION['error_codepromo'] = true;
					$error = pmt_err_describe($applicable, $_POST['code-promo']);
					$error = str_replace('Toutes les conditions ci-dessous :', '', $error);
					if ($applicable == '-10') {
						if (!isset($config['user_id']) || !is_numeric($config['user_id']) || !$config['user_id']) {
							$error .= ' ' . "\n" . i18n::get('Veuillez vous connecter.');
						}
					}
				}
			}
		}

		// Suppression d'un code promotion
		if (isset($_POST['del-code-promo'])) {
			pmt_codes_cancel($_SESSION['ord_id']);
		}

		// Gestion de l'ajout au panier d'un produit offert après le choix de l'internaute
		if( isset($_POST['add-list-offert']) ){
			// print '<pre>';print_r($_POST);print '</pre>';exit;
			$can_control_order = true;
			if( !isset($_POST['cod_id']) || !is_numeric($_POST['cod_id']) || !$_POST['cod_id'] ){
				$error = i18n::get("Une erreur inattendue s'est produite lors de la sélection de votre produit offert. Merci de réessayer ou prendre contact pour nous signaler le problème.");
			}elseif( !isset($_POST['prd-choose']) || !is_numeric($_POST['prd-choose']) || !$_POST['prd-choose'] ){
				$error = i18n::get("Une erreur inattendue s'est produite lors de la sélection de votre produit offert. Merci de réessayer ou prendre contact pour nous signaler le problème.");
			}else{
				$qty = isset($_POST['qty'][$_POST['prd-choose']]) && is_numeric($_POST['qty'][$_POST['prd-choose']]) && $_POST['qty'][$_POST['prd-choose']]>0 ? $_POST['qty'][$_POST['prd-choose']] : 0;

				if( !ord_products_add_offer($_SESSION['ord_id'], $_POST['prd-choose'], $_POST['cod_id'], $qty, true) ){
					$error = i18n::get("Une erreur inattendue s'est produite lors de la sélection de votre produit offert. Merci de réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
		}

		// Choix d'une adresse de livraison
		if( isset($_POST['adr-other'])){
			if( 	!ord_orders_set_relay($_SESSION['ord_id'], null)
				|| 	!ord_orders_set_dlv_store($_SESSION['ord_id'])
				|| 	!ord_orders_adr_delivery_set($_SESSION['ord_id'], isset($_POST['adr-other-id']) ? $_POST['adr-other-id'] : false)
			){
				$error = "Une erreur inattendue est survenue lors de la sélection de l'adresse de livraison. <br />Merci de prendre <a href=\"#param[url contact]#\">contact avec nous</a> pour nous signaler l'erreur.";
			}
			$redirection = '/commander/recapitulatif/';
		}

		// Choix de l'adresse de facturation pour la livraison
		if( isset($_POST['adr-default']) ){
			if( 	!ord_orders_set_relay($_SESSION['ord_id'], null)
				|| 	!ord_orders_set_dlv_store($_SESSION['ord_id'])
				|| 	!ord_orders_adr_delivery_set($_SESSION['ord_id'])
			){
				$error = "Une erreur inattendue est survenue lors de la sélection de l'adresse de livraison. <br />Merci de prendre <a href=\"#param[url contact]#\">contact avec nous</a> pour nous signaler l'erreur.";
			}

			$redirection = '/commander/consignes/';
		}

		// Passage à l'étape de livraison
		if (isset($_POST['step-delivery'])) {
			$redirection = '/commander/livraison/';
		}

		// Mise à jour du service de livraison d'une commande
		if (isset($_POST['upd-srv'])) {
			$srv_id = false;
			if (isset($_POST['srv_id']) && is_numeric($_POST['srv_id']) && $_POST['srv_id'] > 0) {
				$srv_id = $_POST['srv_id'];
			}

			ord_orders_set_dlv_service( $_SESSION['ord_id'], $srv_id );
		}

		// Sauvegarder mon panier
		if (isset($_POST['save-cart'])) {
			$redirection = $config['url_cart'];

			if (!ord_orders_update_status($_SESSION['ord_id'], _STATE_BASKET_SAVE)) {
				$error = "Une erreur inattendue s'est produite lors de la sauvegarde de votre panier.";
			}else{
				$_SESSION['success-save-cart'] = true;
				unset($_SESSION['ord_id']);
			}
		}

		// Paiement d'une commande
		if (isset($_POST['pay-order'])) {
			if (!isset($_POST['cgv'])) {
				$error = "Merci de cocher la case concernant l'acception des conditions générales de vente pour valider votre commande.";
			}elseif (!isset($_POST['choose-pay'])) {
				$error = "Veuillez choisir un moyen de paiement";
			}

			if (!isset($error)) {
				switch ($_POST['choose-pay']) {
					case _PAY_PAYPAL : {
						require_once('PayPal.inc.php');
						try {
							PayPal::doPayment();
							exit;
						}
						catch (exception $e) {
							// On log l'erreur
							error_log('PayPal : ' . $e->getMessage());
						}

						break;
					}
					case _PAY_CHEQUE_X: {
						$total_ttc = ord_orders_get_total( $_SESSION['ord_id'], true );

						$schedule = ord_cheque_get_schedule( $total_ttc, 3, date('Y-m-d') );
						foreach( $schedule as $date=>$amount ){
							ord_installments_add( _PAY_CHEQUE_X, 1, $_SESSION['ord_id'], $total_ttc, $amount, false, $date );
						}

						ord_orders_pay_type_set( $_SESSION['ord_id'], _PAY_CHEQUE_X );
						if( ord_orders_state_update($_SESSION['ord_id'], _STATE_WAIT_PAY) ){
							header('Location: '.$config['url_cart_complete']);
							exit;
						}

						break;
					}
					case _PAY_YESBYCASH : {
						require_once('YesByCash.inc.php');

						$yesbycash = new YesByCash();
						$yesbycash->_doPayment( $_SESSION['ord_id'] );

						break;
					}
					case _PAY_CB : {
						break;
					}
					case 'PAY_CASE': {
						// La commande est rattachée aux bornes
						ord_orders_set_website( $_SESSION['ord_id'], 26 );

						ord_orders_pay_type_set( $_SESSION['ord_id'], _PAY_CHEQUE );
						if( ord_orders_state_update($_SESSION['ord_id'], _STATE_WAIT_PAY) ){
							if( ord_orders_state_update($_SESSION['ord_id'], _STATE_PAY_CONFIRM) ){
								header('Location: '.$config['url_cart_complete']);
								exit;
							}
						}
					}
					default : {
						if (in_array($_POST['choose-pay'], array(_PAY_COMPTE, _PAY_VIREMENT, _PAY_CHEQUE, _PAY_ESPECE))) {
							ord_orders_pay_type_set( $_SESSION['ord_id'], $_POST['choose-pay'] );

							if( ord_orders_state_update($_SESSION['ord_id'], _STATE_WAIT_PAY) ){
								header('Location: '.$config['url_cart_complete']);
								exit;
							}
						}

						break;
					}
				}
			}
		}

		// Enregistre la livraison à domicile ou en magasin
		if( isset($_POST['choose-dlv']) ){

			if( !in_array($_POST['choose-dlv'], array('home', 'store')) || !proloisirs_order_set_home_delivery($_POST['choose-dlv'] === 'home') ){
				$error = i18n::get('Une erreur inattendue s\'est produite lors de l\'enregistrement du choix de livraison.');
			}else if( $_POST['choose-dlv'] !== 'home' ){
				$store = get_store_selected();

				ord_orders_adr_delivery_set($_SESSION['ord_id']);
				ord_orders_set_dlv_store($_SESSION['ord_id'], $store['id']);
				$redirection = '/commander/recapitulatif/';
			}else{
				ord_orders_set_dlv_store($_SESSION['ord_id']);
				$redirection = '/commander/livraison/';
			}
		}

		// Promotion spéciale produit offert
		if (isset($_POST['get-offer-color'])) {
			if (!is_numeric($_POST['get-offer-color']) || !$_POST['get-offer-color']) {
				$error = "Merci de choisir la couleur de la housse de bain de soleil offerte";
			} else {
				if (!ord_products_add_offer($_SESSION['ord_id'], $_POST['get-offer-color'], $config['pmt_relax']['id'], 1, true)) {
					$error = "Une erreur inattendue s'est produite lors du choix de la couleur de la housse de bain de soleil offerte. \nMerci de réeassyer ou prendre contact avec nous pour nous signaler le problème.";
				} else {
					$_SESSION['last-get-offer-color'] = $_POST['get-offer-color'];
				}
			}
		}
	}

	calculated_port_in_order();

	if( $get_order ){
		if( isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) && $_SESSION['ord_id'] > 0 ){
			$order = ria_mysql_fetch_assoc( ord_orders_get_with_adresses( $config['user_id'], $_SESSION['ord_id']) );

			// Ici on va vérifier quel est le type de livraison possible afin d'assigner la bonne valeur de fld_ord_is_ldd
			// Attention, la fonction proloisirs_order_get_delivery_type prend que compte les valeurs de fld_prd_delivery_type
			// Et pas le is_only_ldd des frais de surcharge, (qui n'est à priori pris en compte que pour afficher ou non les choix de livraison sur le panier)
			$ord_dlv_type = proloisirs_order_get_delivery_type($order['id']);
			if (isset($ord_dlv_type)){
				if( $ord_dlv_type == _PL_DLV_TYPE_NONE ){
					proloisirs_order_set_home_delivery(null);
				}
				else if( $ord_dlv_type == _PL_DLV_TYPE_HOME ){
					ord_orders_set_dlv_store($_SESSION['ord_id']);
					proloisirs_order_set_home_delivery(true);
				}else if( $ord_dlv_type == _PL_DLV_TYPE_STORE ){
					$store = get_store_selected();
					ord_orders_adr_delivery_set($_SESSION['ord_id']);
					ord_orders_set_dlv_store($_SESSION['ord_id'], $store['id']);
					proloisirs_order_set_home_delivery(false);
				}
				else if( !proloisirs_order_is_home_delivery_defined($order['id']) ){
					$ord_dlv_type = proloisirs_order_get_delivery_type($order['id']);
					proloisirs_order_set_home_delivery($ord_dlv_type == _PL_DLV_TYPE_NONE ? null : ($ord_dlv_type == _PL_DLV_TYPE_HOME));
				}
			}

			$is_ldd = proloisirs_order_is_home_delivery($order['id']);

			if (!$is_ldd && (!is_numeric($order['str_id']) || !$order['str_id'])) {
				if (isset($config['reseller_id']) && is_numeric($config['reseller_id']) && $config['reseller_id']) {
					$store = get_store_selected();

					if (is_array($store) && sizeof($store)) {
						ord_orders_set_dlv_store($_SESSION['ord_id'], $store['id']);
					}
				}
			}
		}
	}

	if (isset($_POST['modify-adr'])) {
		$completed = false;
	}

	// Gestion de la redirection ou du retour AJAX
	if( sizeof($_POST)>0 && !isset($completed) ){
		if( $in_popup ){
			if( isset($error) ){
				$g_error = $error;
			}
		}elseif( !$is_ajax ){
			if( !isset($error) ){

				if( isset($_SESSION['ord_id']) ){
					ord_orders_promotions_verified( $_SESSION['ord_id'] );
				}

				$url = rew_rewritemap_translate_get( $redirection, i18n::getLang() );
				if( in_array($redirection, array($config['url_cart_delivery'], $config['url_cart_review'], $config['url_cart_complete'])) ){
					$url = $config['site_ssl_url'].$url;
				}

				header('Location: '.$url);
				exit;

			}
		}else{
			if( isset($error) ){
				print json_encode(array(
					'success' => 0,
					'content' => display_messages('ERROR', $error, false)
				));
			}else{
				// Vérification des promotions spéciales
				if( !isset($_SESSION['ord_id']) ){
					$reload_cart = false;
				}

				if( $reload_cart ){
					ord_orders_promotions_verified( $_SESSION['ord_id'] );
				}

				require_once('view.site.inc.php');
				load_promotions_specials($config);

				print json_encode(array(
					'success' => 1,
					'content' => array(
						'cart' 		=> !$reload_cart ? '' : view_cart_details( $_SESSION['ord_id'] ),
						'miniCart' 	=> !$reload_cart ? '' : view_mini_cart()
					)
				));
			}

			exit;
		}

		if( isset($_GET['ajax']) ){
			exit;
		}
	}
?>
