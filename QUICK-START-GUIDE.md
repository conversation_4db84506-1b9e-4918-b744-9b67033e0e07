# RiaShop API Quick Start Guide

This guide will help you get started with the RiaShop API documentation and testing within 5 minutes.

## Prerequisites

- <PERSON>man installed on your computer
- Access to a RiaShop instance
- Valid tenant credentials (logtoken)

## Step 1: Import Postman Collection

1. Open Postman
2. Click "Import" button
3. Select the `riashop-api-documentation.json` file
4. Click "Import"

The collection will be imported with all endpoints organized by functionality.

## Step 2: Set Up Environment Variables

1. In Postman, click the gear icon (⚙️) in the top right
2. Click "Add" to create a new environment
3. Name it "RiaShop API"
4. Add the following variables:

| Variable Name | Initial Value | Current Value | Description |
|---------------|---------------|---------------|-------------|
| `base_url` | `http://localhost/backend/htdocs/api` | `http://localhost/backend/htdocs/api` | Your API base URL |
| `logtoken` | `your_tenant_token_here` | `your_actual_token` | Your tenant token |
| `token` | `` | `` | User token (will be set after authentication) |
| `email` | `<EMAIL>` | `your_email` | Your user email |
| `password` | `password_hash` | `your_password_hash` | Your password (MD5 hashed) |
| `device_id` | `postman_device` | `postman_device` | Device identifier |

5. Click "Add" then "Close"
6. Select your new environment from the dropdown in the top right

## Step 3: Update Configuration

### Update Base URL
Replace `http://localhost/backend/htdocs/api` with your actual API URL:
- Local development: `http://localhost/backend/htdocs/api`
- Production: `https://your-domain.com/backend/htdocs/api`
- Docker: `http://localhost:8080/backend/htdocs/api` (adjust port as needed)

### Get Your Tenant Token (logtoken)
The logtoken is an MD5 hash that identifies your tenant. Contact your system administrator or check your RiaShop configuration to get this value.

### Prepare User Credentials
- **Email**: Your RiaShop user email
- **Password**: MD5 hash of your password (not plain text)

To generate MD5 hash of your password:
```bash
# Linux/Mac
echo -n "your_password" | md5sum

# Online tool
# Use any online MD5 generator with your password
```

## Step 4: Test API Connection

### 4.1 Health Check
1. Open the "Authentication & System" folder
2. Click "API Health Check"
3. Click "Send"
4. You should get a response like:
```json
{
  "result": true,
  "time": "2024-01-01 12:00:00",
  "message": "",
  "content": {
    "tnt_name": "Your Tenant Name"
  }
}
```

### 4.2 User Authentication
1. Click "Device Authentication"
2. Update the query parameters with your credentials
3. Click "Send"
4. If successful, copy the `token` from the response
5. Update your environment variable `token` with this value

## Step 5: Test Core Endpoints

### 5.1 Get Users
1. Open "Users Management" folder
2. Click "Get Users"
3. Click "Send"
4. You should see a list of users

### 5.2 Get Products
1. Open "Products Management" folder
2. Click "Get Products"
3. Click "Send"
4. You should see a list of products

### 5.3 Get Categories
1. Open "Categories Management" folder
2. Click "Get Categories"
3. Click "Send"
4. You should see a list of categories

### 5.4 Test PDF Generation
1. Open "PDF Generation & Documents" folder
2. Click "Generate Order PDF"
3. Update the `id` parameter with a valid order ID
4. Click "Send"
5. You should receive a PDF file download

### 5.5 Test Data Export
1. Open "Data Export & Import" folder
2. Click "Export Products"
3. Modify the request body as needed
4. Click "Send"
5. Check the export status and download the generated file

## Step 6: Understanding Responses

All API responses follow this format:
```json
{
  "result": boolean,           // true = success, false = error
  "time": "YYYY-MM-DD HH:MM:SS", // Response timestamp
  "message": "string",         // Error message (if result is false)
  "content": {}               // Response data (if result is true)
}
```

### Success Response Example
```json
{
  "result": true,
  "time": "2024-01-01 12:00:00",
  "message": "",
  "content": [
    {
      "id": 1,
      "name": "Product Name",
      "ref": "PROD001"
    }
  ]
}
```

### Error Response Example
```json
{
  "result": false,
  "time": "2024-01-01 12:00:00",
  "message": "Invalid parameters",
  "content": {}
}
```

## Common Issues & Solutions

### Issue 1: "Invalid logtoken"
**Solution**: Verify your tenant token is correct and properly set in environment variables.

### Issue 2: "Token required"
**Solution**: 
1. First authenticate using the "Device Authentication" endpoint
2. Copy the returned token to your environment variables
3. Retry the request

### Issue 3: "Connection refused"
**Solution**: 
1. Verify your base_url is correct
2. Ensure the RiaShop instance is running
3. Check network connectivity

### Issue 4: "Invalid email/password"
**Solution**:
1. Verify email is correct
2. Ensure password is MD5 hashed, not plain text
3. Check user exists and has proper permissions

## Next Steps

1. **Explore Endpoints**: Browse through all available endpoints in the collection
2. **Read Documentation**: Check `README-API-Documentation.md` for detailed information
3. **Endpoint Reference**: Use `API-Endpoints-Reference.md` for complete endpoint list
4. **Customize**: Modify requests for your specific use cases
5. **Automate**: Use Postman's scripting features for automated testing

## Tips for Success

1. **Save Requests**: Save modified requests for future use
2. **Use Variables**: Leverage environment variables for different environments
3. **Test Incrementally**: Start with simple GET requests before trying POST/PUT/DELETE
4. **Check Logs**: Monitor server logs for detailed error information
5. **Backup**: Export your customized collection for backup

## Support

- **Documentation**: Refer to the complete documentation files
- **Source Code**: Check `/backend/htdocs/api/` for implementation details
- **Logs**: Check server error logs for debugging information

## Security Notes

- Never commit real credentials to version control
- Use HTTPS in production environments
- Rotate tokens regularly
- Limit API access to necessary IP addresses

You're now ready to explore and test the RiaShop API! Start with the basic endpoints and gradually explore more advanced functionality as needed.
