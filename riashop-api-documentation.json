{"info": {"_postman_id": "riashop-api-collection", "name": "RiaShop API Documentation", "description": "Comprehensive API documentation for RiaShop custom API located in /backend/htdocs/api\n\n## Authentication\n- **logtoken**: Required for all requests - identifies the tenant/client\n- **token**: Optional for device/user authentication (required for some endpoints)\n\n## Base URL\nSet the `{{base_url}}` environment variable to your API base URL (e.g., https://api.riashop.fr or http://localhost/backend/htdocs/api)\n\n## Response Format\nAll responses follow this JSON structure:\n```json\n{\n  \"result\": boolean,\n  \"time\": \"YYYY-MM-DD HH:MM:SS\",\n  \"message\": \"Error message if result is false\",\n  \"content\": {} // Response data if result is true\n}\n```\n\n## HTTP Methods\n- **GET**: Retrieve data\n- **POST**: Create new records\n- **PUT**: Update existing records\n- **DELETE**: Remove records", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication & System", "item": [{"name": "API Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/?logtoken={{logtoken}}", "host": ["{{base_url}}"], "path": [""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}", "description": "Tenant identification token"}]}, "description": "Basic API health check and tenant validation"}}, {"name": "<PERSON>ce Au<PERSON>cation", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/devices/?logtoken={{logtoken}}&email={{email}}&password={{password}}&device={{device_id}}", "host": ["{{base_url}}"], "path": ["devices", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "email", "value": "{{email}}", "description": "User email address"}, {"key": "password", "value": "{{password}}", "description": "User password (MD5 hashed)"}, {"key": "device", "value": "{{device_id}}", "description": "Device identifier"}]}, "description": "Authenticate device and get user token"}}]}, {"name": "Users Management", "item": [{"name": "Get Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/?logtoken={{logtoken}}&token={{token}}&limit=50", "host": ["{{base_url}}"], "path": ["users", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}, {"key": "id", "value": "", "description": "User ID or array of IDs", "disabled": true}, {"key": "email", "value": "", "description": "Filter by email", "disabled": true}, {"key": "ref", "value": "", "description": "Filter by reference", "disabled": true}, {"key": "prf", "value": "", "description": "Filter by profile ID", "disabled": true}, {"key": "start", "value": "0", "description": "Pagination start", "disabled": true}, {"key": "limit", "value": "50", "description": "Maximum records to return"}]}, "description": "Retrieve list of users with optional filtering"}}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"ref\": \"USER001\",\n  \"title\": \"1\",\n  \"firstname\": \"<PERSON>\",\n  \"lastname\": \"<PERSON><PERSON>\",\n  \"society\": \"Example Corp\",\n  \"siret\": \"12345678901234\",\n  \"address1\": \"123 Main St\",\n  \"address2\": \"\",\n  \"zipcode\": \"12345\",\n  \"city\": \"Paris\",\n  \"country\": \"France\",\n  \"phone\": \"+***********\",\n  \"fax\": \"\",\n  \"prc\": \"1\",\n  \"mobile\": \"+***********\",\n  \"prf\": \"2\"\n}"}, "url": {"raw": "{{base_url}}/users/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["users", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Create a new user account"}}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"123\",\n  \"email\": \"<EMAIL>\",\n  \"firstname\": \"<PERSON>\",\n  \"lastname\": \"<PERSON>\",\n  \"phone\": \"+***********\"\n}"}, "url": {"raw": "{{base_url}}/users/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["users", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Update existing user information"}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"123\"\n}"}, "url": {"raw": "{{base_url}}/users/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["users", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Delete a user account"}}]}, {"name": "Products Management", "item": [{"name": "Get Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products/?logtoken={{logtoken}}&token={{token}}&limit=50", "host": ["{{base_url}}"], "path": ["products", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}, {"key": "id", "value": "", "description": "Product ID or array of IDs", "disabled": true}, {"key": "ref", "value": "", "description": "Product reference", "disabled": true}, {"key": "limit", "value": "50", "description": "Maximum records to return"}]}, "description": "Retrieve product catalog with detailed information"}}]}, {"name": "Categories Management", "item": [{"name": "Get Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["categories", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}, {"key": "id", "value": "", "description": "Category ID or array of IDs", "disabled": true}, {"key": "parent", "value": "", "description": "Parent category ID", "disabled": true}]}, "description": "Retrieve product categories hierarchy"}}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Category\",\n  \"title\": \"Category Title\",\n  \"publish\": true,\n  \"desc\": \"Category description\",\n  \"parent\": 0,\n  \"ref\": \"CAT001\"\n}"}, "url": {"raw": "{{base_url}}/categories/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["categories", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Create a new product category"}}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 123,\n  \"name\": \"Updated Category\",\n  \"title\": \"Updated Title\",\n  \"publish\": true,\n  \"desc\": \"Updated description\"\n}"}, "url": {"raw": "{{base_url}}/categories/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["categories", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Update existing category information"}}]}, {"name": "Orders Management", "item": [{"name": "Get Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/orders/?logtoken={{logtoken}}&token={{token}}&limit=50", "host": ["{{base_url}}"], "path": ["orders", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}, {"key": "id", "value": "", "description": "Order ID or array of IDs", "disabled": true}, {"key": "usr", "value": "", "description": "User ID filter", "disabled": true}, {"key": "limit", "value": "50", "description": "Maximum records to return"}]}, "description": "Retrieve orders with detailed information"}}, {"name": "Get Order Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/orders/stats/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["orders", "stats", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Get order statistics and analytics"}}, {"name": "Complete Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 123,\n  \"complete\": true\n}"}, "url": {"raw": "{{base_url}}/orders/complete/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["orders", "complete", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Mark order as complete"}}]}, {"name": "Brands Management", "item": [{"name": "Get Brands", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/brands/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["brands", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}, {"key": "id", "value": "", "description": "Brand ID or array of IDs", "disabled": true}, {"key": "start", "value": "0", "description": "Pagination start", "disabled": true}, {"key": "limit", "value": "50", "description": "Maximum records to return", "disabled": true}]}, "description": "Retrieve product brands information"}}]}, {"name": "Images & Documents", "item": [{"name": "Get Images", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/images/?logtoken={{logtoken}}&token={{token}}&id=123", "host": ["{{base_url}}"], "path": ["images", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}, {"key": "id", "value": "123", "description": "Image ID or array of IDs"}]}, "description": "Retrieve image information and metadata"}}, {"name": "Get Image File", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/images/file/?logtoken={{logtoken}}&token={{token}}&id=123", "host": ["{{base_url}}"], "path": ["images", "file", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}, {"key": "id", "value": "123", "description": "Image ID"}]}, "description": "Download image file"}}, {"name": "Get Documents", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/documents/objects/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["documents", "objects", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Get document objects"}}]}, {"name": "PDF Generation & Documents", "item": [{"name": "Generate Order PDF", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/orders/pdf/?logtoken={{logtoken}}&token={{token}}&id=123", "host": ["{{base_url}}"], "path": ["orders", "pdf", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}, {"key": "id", "value": "123", "description": "Order ID to generate PDF for"}]}, "description": "Generate and download PDF for a specific order"}}, {"name": "Generate Delivery Note PDF", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/bl/pdf/?logtoken={{logtoken}}&token={{token}}&id=123", "host": ["{{base_url}}"], "path": ["bl", "pdf", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}, {"key": "id", "value": "123", "description": "Delivery note ID to generate PDF for"}]}, "description": "Generate and download PDF for a delivery note (bon de livraison)"}}, {"name": "Generate Return PDF", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/return/pdf/?logtoken={{logtoken}}&token={{token}}&id=123", "host": ["{{base_url}}"], "path": ["return", "pdf", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}, {"key": "id", "value": "123", "description": "Return ID to generate PDF for"}]}, "description": "Generate and download PDF for a return document"}}, {"name": "Generate Report PDF", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/reports/pdf/?logtoken={{logtoken}}&token={{token}}&id=123", "host": ["{{base_url}}"], "path": ["reports", "pdf", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}, {"key": "id", "value": "123", "description": "Report ID to generate PDF for"}]}, "description": "Generate and download PDF for a report"}}, {"name": "Download Document File", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/documents/file/?logtoken={{logtoken}}&token={{token}}&id=123", "host": ["{{base_url}}"], "path": ["documents", "file", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}, {"key": "id", "value": "123", "description": "Document ID to download"}]}, "description": "Download a document file by ID"}}, {"name": "Upload Document File", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "doc", "type": "file", "src": "", "description": "Document file to upload"}, {"key": "type", "value": "1", "description": "Document type ID"}, {"key": "filename", "value": "document.pdf", "description": "Original filename"}, {"key": "name", "value": "Document Name", "description": "Document display name"}, {"key": "desc", "value": "Document description", "description": "Document description"}]}, "url": {"raw": "{{base_url}}/documents/file/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["documents", "file", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Upload a new document file"}}]}, {"name": "Data Export & Import", "item": [{"name": "Export Products", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cls_id\": 1,\n  \"exp_id\": 1,\n  \"wst_id\": 0,\n  \"cat\": 0,\n  \"catchilds\": true,\n  \"cols\": [\"id\", \"ref\", \"title\", \"price\"],\n  \"flds\": [],\n  \"thumb\": {\"main\": true, \"second\": false},\n  \"childonly\": false,\n  \"no_html\": true,\n  \"brand\": 0,\n  \"lngs\": [\"fr\"],\n  \"for_excel\": false,\n  \"for_mac\": false,\n  \"seo\": false\n}"}, "url": {"raw": "{{base_url}}/exports/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["exports", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Export products to CSV/Excel format with customizable columns and filters"}}, {"name": "Export Categories", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cls_id\": 2,\n  \"exp_id\": 1,\n  \"wst_id\": 0,\n  \"heads\": [\"id\", \"name\", \"title\", \"parent\"],\n  \"cat\": 0,\n  \"recursive\": true,\n  \"ar_image_sizes\": [],\n  \"exclude_cat_ids\": [],\n  \"header_row\": [\"ID\", \"Name\", \"Title\", \"Parent\"],\n  \"for_excel\": false,\n  \"for_mac\": false,\n  \"seo\": false\n}"}, "url": {"raw": "{{base_url}}/exports/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["exports", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Export categories to CSV/Excel format with hierarchy support"}}, {"name": "Export Orders", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cls_id\": 3,\n  \"exp_id\": 1,\n  \"wst_id\": 0,\n  \"key\": {\n    \"id\": [],\n    \"parent_id\": [],\n    \"ref\": [],\n    \"piece\": []\n  },\n  \"filter\": {\n    \"is_web\": null,\n    \"origin\": \"\",\n    \"wst_id\": 0,\n    \"pay_id\": 0,\n    \"seller_id\": false,\n    \"pmt_id\": false,\n    \"state_id\": 0,\n    \"usr_id\": 0,\n    \"fld\": [],\n    \"parent_id\": 0,\n    \"total_ht\": {\"min\": 0, \"max\": 10000}\n  },\n  \"period\": {\n    \"start\": \"2024-01-01\",\n    \"end\": \"2024-12-31\"\n  }\n}"}, "url": {"raw": "{{base_url}}/exports/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["exports", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Export orders to Excel format with detailed filtering options and dual-sheet layout"}}, {"name": "Export Delivery Notes", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cls_id\": 4,\n  \"exp_id\": 1,\n  \"wst_id\": 0,\n  \"key\": [],\n  \"filter\": {\n    \"seller_id\": false,\n    \"dps_id\": 0\n  },\n  \"period\": {\n    \"start\": \"2024-01-01\",\n    \"end\": \"2024-12-31\"\n  }\n}"}, "url": {"raw": "{{base_url}}/exports/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["exports", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Export delivery notes (bons de livraison) to Excel format"}}, {"name": "Get Exports Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/exports/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["exports", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Get list of exports and their status"}}, {"name": "Get Imports", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/imports/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["imports", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Get list of imports and their status"}}]}, {"name": "Inventory & Stocks", "item": [{"name": "Get Stocks", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/stocks/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["stocks", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Get stock information"}}, {"name": "Get Stock Deposits", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/stocks/deposits/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["stocks", "deposits", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Get stock deposits information"}}, {"name": "Get Serials", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/serials/?logtoken={{logtoken}}&token={{token}}", "host": ["{{base_url}}"], "path": ["serials", ""], "query": [{"key": "logtoken", "value": "{{log<PERSON>en}}"}, {"key": "token", "value": "{{token}}"}]}, "description": "Get serial numbers information"}}]}], "variable": [{"key": "base_url", "value": "http://localhost/backend/htdocs/api", "description": "API base URL"}, {"key": "logtoken", "value": "your_tenant_token_here", "description": "Tenant identification token (MD5 hash)"}, {"key": "token", "value": "your_user_token_here", "description": "User authentication token"}, {"key": "email", "value": "<EMAIL>", "description": "User email for authentication"}, {"key": "password", "value": "password_hash", "description": "User password (MD5 hashed)"}, {"key": "device_id", "value": "device123", "description": "Device identifier"}]}