<?php

/**	\file products.php
 *	Ce fichier affiche la liste des produits contenus dans une pièce de vente. Il est utilisé
 *	comme include par le fichier htdocs/admin/orders/order.php ainsi qu'en Ajax par le fichier
 *	htdocs/admin/js/default.js.
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

if( !isset($ord) || !$ord ){
	if( isset($_GET['ord']) ){
		$ord = ria_mysql_fetch_array(ord_orders_get_masked($_GET['ord'], true));
		if (!$ord) exit;
	}

	// Le paramètre $ord est obligatoire pour le fonctionnement de ce script
	if( !isset($ord) || !$ord ){
		header('Location: /admin/orders/orders.php');
		exit;
	}

	if( isset($_GET['ord-child']) ){
		$ord_child = ria_mysql_fetch_array(ord_orders_get_masked($_GET['ord-child'], true));
	}
}

// La table des informations de la commande est soit issus de $ord (initialisé en amont)
$ord_viewed = &$ord;
if (isset($ord_child)) {
	$ord_viewed = &$ord_child;
}

$errors = array();

$modified_product = array(
	'prd_id' => '',
	'col_id' => '',
	'line_id' => '',
	'child_id' => '',
	'edt_col' => '',
);

// La mise à jour d'une commande n'est possible que :
//  - si elle n'est pas encore synchronisée
//  - apparatient à un statut non confirmée
$can_update_order = false;

$inc_devis = isset($config['ord_products_devis']) && $config['ord_products_devis'];
if( trim($ord_viewed['piece']) == '' && (in_array($ord['state_id'], ord_states_get_uncompleted($inc_devis)) || $ord['state_id'] == _STATE_MODEL) ){
	$can_update_order = true;
}

if ($can_update_order && isset($_POST['ord-prd']) && is_array($_POST['ord-prd'])) {
	$ord_is_free = 'Oui' == fld_object_values_get($ord_viewed['id'], _FLD_PRD_ORD_FREE);
	$r_ord_prd = isset($ord_child)
		? ord_products_get( $ord['id'], array('line_pos'=>'asc'), 0, '', null, false, 1, 0, -1, false, false, 0, false, false, false, $ord_child['id'] )
		: ord_products_get( $ord_viewed['id'], array('line_pos'=>'asc'), 0, '', null, false, 1);

	if ($r_ord_prd && ria_mysql_num_rows($r_ord_prd)) {
		while ($ord_prd = ria_mysql_fetch_assoc($r_ord_prd)) {
			$prd_id = $ord_prd['id'];
			$line_id = $ord_prd['line'];
			$child_line = $ord_prd['child-line'];

			if (!isset($_POST['ord-prd'][$prd_id]) || !isset($_POST['ord-prd'][$prd_id][$line_id][0]) || !isset($_POST['ord-prd'][$prd_id][$line_id][0]['is_modified'])) {
				continue;
			}

			$price_ht = isset($_POST['ord-prd'][$prd_id][$line_id][0]['price_ht']) ? $_POST['ord-prd'][$prd_id][$line_id][0]['price_ht'] : $ord_prd['price_ht'];
			$qte = isset($_POST['ord-prd'][$prd_id][$line_id][0]['qte']) ? $_POST['ord-prd'][$prd_id][$line_id][0]['qte'] : $ord_prd['qte'];
			$name = isset($_POST['ord-prd'][$prd_id][$line_id][0]['name']) ? $_POST['ord-prd'][$prd_id][$line_id][0]['name'] : $ord_prd['name'];
			$discount = isset($_POST['ord-prd'][$prd_id][$line_id][0]['discount']) ? $_POST['ord-prd'][$prd_id][$line_id][0]['discount'] : $ord_prd['discount'];
			$discount_type = isset($_POST['ord-prd'][$prd_id][$line_id][0]['discount_type']) ? $_POST['ord-prd'][$prd_id][$line_id][0]['discount_type'] : $ord_prd['discount_type'];
			$r_ord_prd_subs = ord_subscriptions_get( $ord_viewed['id'], false, $prd_id);
			$sub_id = 0;
			if ($r_ord_prd_subs && ria_mysql_num_rows($r_ord_prd_subs)) {
				$sub_id = ria_mysql_result($r_ord_prd_subs, 0, 'sub_id');
			}

			$prd_is_free = false;
			if ($ord_is_free) {
				$prd_is_free = 'Oui' === fld_object_values_get(array($ord_prd['ord_id'], $prd_id, $line_id), _FLD_PRD_ORD_FREE, '', true);
			}

			$colisage_id = $ord_prd['col_id'];

			$is_nomenclature = prd_nomenclatures_options_exists($prd_id);
			$childs = array();
			if ($is_nomenclature){
				$r_nomenclature_products = ord_products_get( $ord_viewed['id'], array('name' => 'asc'), 0, '', null, false, -1, $prd_id, $child_line );
				while($nmc_product = ria_mysql_fetch_assoc($r_nomenclature_products)){
					$nmc_id = fld_object_values_get(array($ord_viewed['id'], $nmc_product['id'], $nmc_product['line']), _FLD_PRD_ORD_OPT_ID, '', true);
					$childs[$nmc_id] = $nmc_product['id'];
				}
			}

			if ($ord_is_free && $prd_is_free) {
				$modified_line_id =
					ord_products_update_free(
						$ord_viewed['id'],
						$ord_prd['ref'],
						$name,
						$price_ht,
						$qte,
						!is_null($ord_prd['date-livr']) ? $ord_prd['date-livr'] : false,
						!is_null($ord_prd['notes']) ? $ord_prd['notes'] : false,
						!is_null($ord_prd['tva_rate']) ? $ord_prd['tva_rate'] : false,
						!is_null($ord_prd['cod']) ? $ord_prd['cod'] : 0,
						$sub_id,
						$colisage_id,
						true,
						false,
						$prd_id,
						$line_id,
						!is_null($ord_prd['sell_points']) ? $ord_prd['sell_points'] : false,
						!$ord_prd['ord_child_id'] ? false : $ord_prd['ord_child_id'],
						$child_line,
						sizeof($childs) ? $childs : null
					);

				$modified_product = array(
					'prd_id' => $prd_id,
					'col_id' => $colisage_id,
					'line_id' => $modified_line_id,
					'child_id' => '',
					'edt_col' => $_POST['ord-prd'][$prd_id][$line_id][0]['is_modified'],
				);

				if ($modified_line_id === false) {
					$errors[] = _('Erreur lors de la modification d\'un produit de la commande');
				}
			} else {
				$modified_line_id = ord_products_add_free(
					$ord['id'],
					$ord_prd['ref'],
					$name,
					$price_ht,
					$qte,
					$ord_prd['date-livr'],
					$ord_prd['notes'],
					$ord_prd['tva_rate'],
					$ord_prd['cod'],
					$sub_id,
					false,
					$colisage_id,
					0,
					true,
					true,
					true,
					false,
					$prd_id,
					isset($ord_child) && $ord_child['id'] != 0 ? $ord_child['id'] : false,
					$child_line,
					sizeof($childs) ? $childs : null,
					$colisage_id !== false ? $line_id : false,
					$ord_prd['prd_pos']
				);

				$modified_product = array(
					'prd_id' => $prd_id,
					'col_id' => $colisage_id,
					'line_id' => $modified_line_id,
					'child_id' => '',
					'edt_col' => $_POST['ord-prd'][$prd_id][$line_id][0]['is_modified'],
				);

				if ($modified_line_id === false) {
					$errors[] = _('Erreur lors de la modification d\'un produit de la commande.');
				}
			}

			if ($_POST['ord-prd'][$prd_id][$line_id][0]['is_modified'] == "discount"){
				if (!ord_products_set_discount($ord['id'], $prd_id, $line_id, $discount_type, $discount)){
					$errors[] = _("Une erreur est survenue lors de l'enregistrement de la remise sur le produit");
				}
			}

			ord_orders_update_totals($ord_viewed['id']);
			$ord_viewed = ria_mysql_fetch_array(ord_orders_get_masked($ord_viewed['id'], true));
		}
	}
}

// Gère la suppression d'une ligne d'article ou d'un interligne
if ($can_update_order && isset($_POST['del-ord-prd']) && $_POST['del-ord-prd'] && isset($_POST['ord-prd-select']) && is_array($_POST['ord-prd-select'])) {
	$i = 0;
	foreach ($_POST['ord-prd-select'] as $key => $uniq_id) {
		$col_id = $_POST['col'][$uniq_id];
		$prd_id = $_POST['prd'][$uniq_id];
		$line_id = $_POST['line'][$uniq_id];
		$child_line = $_POST['child-line'][$uniq_id];
		if ($prd_id == 0){
			if (!ord_products_del_spacing($ord_viewed['id'], $line_id)) {
				$errors[] = _('Erreur lors de la suppression de l\'interligne de la commande');
			}
		} else {
			$product = ria_mysql_fetch_assoc(ord_products_get($ord_viewed['id'], false, $prd_id, '', $line_id, false, 0, 0, -1, false, false, 0, false, false, $col_id));
			if (!ord_products_del($ord_viewed['id'], $prd_id, $line_id, $product['child-line'] !== null ? $product['child-line'] : -1, 0, $i + 1 == count($_POST['ord-prd-select']))) {
				$errors[] = _('Erreur lors de la suppression d\'un produit de la commande');
			}
		}
		$i++;
	}
}

$r_ord_prd = isset($ord_child)
	? ord_products_get( $ord['id'], array('line_pos' => 'asc', 'line' => 'asc'), 0, '', null, false, 1, 0, -1, false, false, 0, false, false, false, $ord_child['id'] )
	: ord_products_get( $ord_viewed['id'], array('line_pos' => 'asc', 'line' => 'asc'), 0, '', null, false, 1 );

	if (ria_mysql_num_rows($r_ord_prd)){
		mysql_data_seek( $r_ord_prd, 0 );
?>
<td colspan="2" class="multi-colspan<?php print isset($ord_child) ? ' td-padding-0' : '' ?>">
	<?php
	foreach ($errors as $error) {
		echo '<div class="error">' . htmlspecialchars($error) . '</div>';
	}
	?>
	<table id="ord-products-articles" class="print-table">
		<thead class="th-head-second">
			<tr>
			<?php if ($can_update_order) { ?>
				<th class="thead-none col-check print-none"></th>
				<th class="col-check print-none" data-label="<?php print _('Tout cocher :'); ?> "><?php if ($can_update_order) { ?><input type="checkbox" id="checkall"/><?php } ?></th>
			<?php } ?>
				<th colspan="2" class="thead-none ref-des">
					<span class="col125px inline-block"><?php print _('Référence')?></span>
					<span class="th-prd-comment inline-block"><?php print _('Désignation (69 caractères max..)')?></span>
				</th>
				<th class="col100px align-right thead-none"><?php print _('Prix Unitaire')?></th>
				<th class="col80px align-right thead-none"><?php print _('Quantité')?></th>
				<th class="align-right thead-none discount"><?php print _('Remise')?></th>
				<th class="col80px align-right thead-none"><?php print _('Total')?></th>
				<th id="ord-pos" class="col40px thead-none print-none"></th>
			</tr>
		</thead>
		<tbody>
		<?php
			// Affiche la liste des produits contenus dans cette pièce
			$btn_count = $current = 0;
			$count = ria_mysql_num_rows($r_ord_prd);
			$last_prd = array('id' => 0, 'line' => 0);
			while( $prd = ria_mysql_fetch_assoc($r_ord_prd) ){
				if( $prd['id']!=0 ){
					// Si le produit parent est une nomenclature variable, l'article ne sera pas affiché comme ligne de commande
					// L'affichage des composants d'une nomenclature variable est géré autrement (voir "Produits nomenclaturés uniquement")
					if (is_numeric($prd['parent']) && $prd['parent']) {
						$type_nomen = prd_products_get_nomenclature_type($prd['parent']);
						if (in_array($type_nomen, array(NM_TYP_VARIABLE))) {
							continue;
						}
					}

					$categories = prd_products_categories_get($prd['id']);
					$r_bl_prd = ord_bl_products_get(0, $ord['id'], false, false, false, $prd['id'], $prd['line']);
					$bl_prd = false;
					if ($r_bl_prd && ria_mysql_num_rows($r_bl_prd)) {
						$bl_prd = ria_mysql_fetch_assoc($r_bl_prd);
					}
					$is_colisage = prd_colisage_classify_exists($prd['id']);
					$colisage_id = 0;
					if ($is_colisage && $colisage_id = fld_object_values_get(array($ord['id'], $prd['id'], $prd['line']), _FLD_PRD_COL_ORD_PRODUCT) ){
						$r_colisage = prd_colisage_types_get(parseInt($colisage_id));
						$colisage = ria_mysql_fetch_assoc($r_colisage);
					} else {
						$is_colisage = false;
					}

					// Récupération de la remise spéciale de la ligne de commande
					$fld_discount = fld_object_values_get(array($prd['ord_id'], $prd['id'], $prd['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);

					$uniqid = uniqid( $ord['id'].$prd['id'].$prd['line'].$colisage_id );

					print '<tr class="ord-prd-row ria-row-orderable" id="line-'.$prd['id'].','.$prd['line'].'" >';

					// Si la pièce est modifiable
					if( $can_update_order ){

						// Affiche la colonne contenant le menu flottant permettant d'agir sur la ligne
						print ' <td class="td-engrenage print-none">
									'.( $can_update_order ? '<a href="#" id="btn-'.$btn_count.'" class="display-ord-products-options edit-cat"></a>' : '' ).'
									<span class="menu-cols ord-products-menu-cols">
										<span class="cols">
											<span class="col">
												<input class="btn-link large prd-add" type="button" name="prd-add" value="'._('Ajouter un article').'" />
											</span>';
						if( $config['ord_models_actived'] ) {
							print '         <span class="col">
												<input class="btn-link large prd-add-model" type="button" name="prd-add-model" value="'._('Ajouter un article à partir d\'un modèle').'" />
											</span>';
						}
						print '             <span class="col">
												<input class="btn-link large prd-add-spacing" type="button" name="prd-add-spacing" value="'._('Ajouter un interligne').'" />
											</span>
											<span class="col">
												<input class="btn-link large del-prd" type="button" name="del-prd" value="'._('Supprimer cet article').'" />
											</span>
										</span>
									</span>
								</td>';

						// Affiche la case à cocher permettant de sélectionner le produit
						print ' <td class="ord-prd-info print-none">
									<input type="checkbox" class="ord-prd-id checkbox uniqid print-none" name="ord-prd-select[]" value="' . $uniqid . '" data-name="' . htmlspecialchars($prd['name']) . '" data-id="' . $prd['id'] . '" />
									<input type="hidden" name="prd[' . $uniqid . ']" value="' . $prd['id'] . '" />
									<input type="hidden" name="col[' . $uniqid . ']" value="' . $colisage_id . '" />
									<input type="hidden" name="line[' . $uniqid . ']" value="' . $prd['line'] . '" />
									<input type="hidden" name="pos[' . $uniqid . ']" value="' . $prd['prd_pos'] . '" />
									<input type="hidden" name="child-line[' . $uniqid . ']" value="0" />
								</td>';
					}

					print '     <td colspan="2" class="multi-colspan">
									<div class="ord-prd-ref-name">
										<div class="ord-prd-nmc-row-main">';

					// Affiche la référence du produit
					print '<span class="td-prd-ref-name-1">';
					if( $cat = ria_mysql_fetch_array($categories) ) {
						print '     <a href="/admin/catalog/product.php?prd='.$prd['id'].'&amp;cat='.$cat['cat'].'">'.htmlspecialchars($prd['ref']).'</a>';
					} else {
						print htmlspecialchars($prd['ref']);
					}
					print '</span>';

					$input_class = '';
					if ($prd['id'] == $modified_product['prd_id'] && $colisage_id == $modified_product['col_id'] && $prd['line'] == $modified_product['line_id']){
						$input_class = ' input-edit-success';
					}

					// Désignation du produit et commentaires éventuels
					if( $can_update_order ){ // Si la pièce est modifiable
						print ' <span class="td-prd-designation">
									<input type="text" class="ord-prd-input ord-prd-name-input" name="ord-prd['. $prd['id'] .']['. $prd['line'] .'][0][name]" value="' . htmlspecialchars($prd['name']) . '" />';
						print '     <span class="ord-prd-name-txt">'.($is_colisage ? ' - '.htmlspecialchars($colisage['name']).' ('.parseInt($colisage['qte']).')' : '') . ($bl_prd ? '<br/>' . _('Colis n°') . htmlspecialchars($bl_prd['colis']) : '').'</span>';
						print '     <textarea rows="2" cols="20" placeholder="'._('Commentaires...').'" class="ord-prd-input ord-prd-comment-input">'.htmlspecialchars($prd['notes']).'</textarea>';
						print ' </span>';
					} else { // Si la pièce n'est pas modifiable
						print ' <span id="padding-top-5">
									<span class="ord-prd-name-txt">'.htmlspecialchars($prd['name']).' '.($is_colisage ? ' - '.htmlspecialchars($colisage['name']).' ('.parseInt($colisage['qte']).')' : '') . ($bl_prd ? '<br/>' . _('Colis n°') . htmlspecialchars($bl_prd['colis']) : '').'</span>';
						if( trim($prd['notes']) ){
							print ' <br /><br /><span class="ord-prd-notes-txt">'.nl2br( htmlspecialchars($prd['notes']) ).'</span>';
						}
						print '</span>';
					}

					print ' </div>';

					// Produits nomenclaturés uniquement
					$is_nomenclature = prd_nomenclatures_options_exists($prd['id']);
					if($is_nomenclature){
						$r_nomenclature_products = ord_products_get( $ord['id'], array('name' => 'asc'), 0, '', null, false, -1, $prd['id'], $prd['child-line'] );
						if ($r_nomenclature_products && ria_mysql_num_rows($r_nomenclature_products)){
							$last_name = $last_comment = '';
							$qte = $count = 0;
							$last_nomenclature_product_id = $last_nomenclature_product_child_line = 0;
							while ($product = ria_mysql_fetch_assoc($r_nomenclature_products)){
								$input_class = '';
								$uniqid = uniqid($ord['id'].$product['id'].$product['child-line']);
								if (trim($last_name) != '' && $last_name != $product['name']) {
									if ($last_nomenclature_product_id == $modified_product['prd_id'] && $last_nomenclature_product_child_line == $modified_product['child_id']){
										$input_class = ' input-edit-success';
									}

									// Désignation du produit et commentaires éventuels
									print ' <span class="td-prd-designation">
												<span class="prd-designation-title">';
									if( $can_update_order ){ // Si la pièce est modifiable
										print '
													<input type="text" class="ord-prd-input ord-prd-name-input" name="ord-prd[' . htmlspecialchars($last_nomenclature_product_id) . '][0][' . htmlspecialchars($last_nomenclature_product_child_line) . '][name]" value="' . htmlspecialchars($last_name) . '" maxlength="69"/>';
									} else { // Si la pièce n'est pas modifiable
										print '
													<span class="ord-prd-name-txt">'.htmlspecialchars($last_name).'</span>';
									}

									print '<span class="ord-prd-qte"> x' .$qte.'</span>
										</span>
									';

									if ( $can_update_order ) {
										print '<textarea rows="2" cols="20" placeholder="'._('Commentaires...').'" class="ord-prd-input ord-prd-comment-input">'.htmlspecialchars($last_comment).'</textarea>';
									}else{
										if( trim($last_comment) ){
											print '<span class="ord-prd-notes-txt">'.nl2br( htmlspecialchars($last_comment) ).'</span>';
										}
									}

									print '
											</span>
										</div>';
									$count = 0;
									$qte = 0;
								}

								if(!$count){
									print ' <div class="ord-prd-nmc-row">
												<span class="ord-prd-info-1">
													<input type="hidden" class="uniqid" name="uniqid" value="' . $uniqid . '"/>
													<input type="hidden" name="prd[' . $uniqid . ']" value="' . $product['id'] . '" />
													<input type="hidden" name="col[' . $uniqid . ']" value="0" />
													<input type="hidden" name="line[' . $uniqid . ']" value="0" />
													<input type="hidden" name="child-line[' . $uniqid . ']" value="'.$product['child-line'].'" />
												</span>';
									$categories = prd_products_categories_get($product['id']);
									if( $cat = ria_mysql_fetch_array($categories) ) {
										print ' <span class="td-prd-ref-name-1"><a href="/admin/catalog/product.php?prd='.$product['id'].'&amp;cat='.$cat['cat'].'">'.htmlspecialchars($product['ref']).'</a></span>';
									} else {
										print ' <span class="td-prd-designation">'.htmlspecialchars($product['ref']).'</span>';
									}
									$count++;
								}
								$qte += $product['qte'];
								$last_name = $product['name'];
								$last_comment = $product['notes'];
								$last_nomenclature_product_id = $product['id'];
								$last_nomenclature_product_child_line = $product['child-line'];
							}
							$input_class = '';
							if ($last_nomenclature_product_id == $modified_product['prd_id'] && $last_nomenclature_product_child_line == $modified_product['child_id']){
								$input_class = ' input-edit-success';
							}
							print ' <span class="td-prd-designation">
										<span class="prd-designation-title">';
							if ( $can_update_order ) {
								print '
											<input type="text" class="ord-prd-input ord-prd-name-input" name="ord-prd[' . htmlspecialchars($last_nomenclature_product_id) . '][0][' . htmlspecialchars($last_nomenclature_product_child_line) . '][name]" value="' . htmlspecialchars($last_name) . '" />
								';
							} else {
								print '
											<span class="ord-prd-name-txt">'.htmlspecialchars($last_name).'</span>
								';
							}
							print '         <span class="ord-prd-qte"> x'.$qte. ' </span>
										</span>
							';

							if ( $can_update_order ) {
								print '<textarea rows="2" cols="20" placeholder="'._('Commentaires...').'" class="ord-prd-input ord-prd-comment-input">'.htmlspecialchars($last_comment).'</textarea>';
							}else{
								if( trim($last_comment) ){
									print '<span class="ord-prd-notes-txt">'.nl2br( htmlspecialchars($last_comment) ).'</span>';
								}
							}

							print '
									</span>
								</div>';
						}
					}
				print '
						</div>
					</td>'; //Fin div ord-prd-ref-name

					// Prix unitaire
					if( $can_update_order ){ // Si la pièce est modifiable
						print '<td class="align-right td-prd-prix-unitaire">
								<input type="text" class="ord-prd-input ord-prd-price-input' . ($modified_product['edt_col'] == "price" ? $input_class : '') . '" name="ord-prd[' . $prd['id'] . '][' . $prd['line'] . '][0][price_ht]" data-value="' . $prd['price_ht'] . '" value="' . floatval($prd['price_ht']) . '" />';
					} else {
						print ' <td class="align-right td-prd-prix-unitaire">
									<span class="ord-prd-price-txt">'.ria_number_format($prd['price_ht'], NumberFormatter::CURRENCY, 2, $prc['money_code']).'</span>';
					}
					print ' </td>';

					// Quantité
					if ( $can_update_order) { // Si la pièce est modifiable
						print ' <td class="align-right td-prd-quantite">
									<input type="text" class="ord-prd-input ord-prd-qte-input' . ($modified_product['edt_col'] == "qte" ? $input_class : '') . '" name="ord-prd[' . $prd['id'] . '][' . $prd['line'] . '][0][qte]" data-value="' . $prd['qte'] . '" value="' . str_replace(',', '.', floatval($prd['qte'])) . '" />';
					} else { // Si la pièce n'est pas modifiable
						print '<td class="align-right td-prd-quantite">
									<span class="ord-prd-qte-txt">'.$prd['qte'].'</span>';
					}
					print ' </td>';

					// Remise et type de remise (€ ou %)
					if( (!is_numeric($fld_discount) || $fld_discount <= 0) && $can_update_order ){ // Si la pièce est modifiable
						print ' <td class="align-right td-remise td-prd-remise">
									<input type="text" class="ord-prd-input ord-prd-discount-input' . ($modified_product['edt_col'] == 'discount' ? $input_class : '') . '" name="ord-prd[' . $prd['id'] . '][' . $prd['line'] . '][0][discount]" data-value="' . ($prd['discount'] !== null ? $prd['discount'] : 0) . '" value="' . floatval($prd['discount'] !== null ? $prd['discount'] : 0) . '" />
									<select class="ord-prd-input ord-prd-discount-select" name="ord-prd[' . $prd['id'] . '][' . $prd['line'] . '][0][discount_type]">
										<option value="0"'. ($prd['discount_type'] === '0' ? ' selected="selected"' : '' ) .'>En euros</option>
										<option value="1"'. ($prd['discount_type'] === '1' ? ' selected="selected"' : '' ) .'>En %</option>
									</select>';
					}else{
						print ' <td class="align-right td-prd-remise">';

						$discount = (!is_numeric($fld_discount) || $fld_discount <= 0) && $prd['discount'] ? $prd['discount'] : 0;
						if( !$prd['discount_type'] ){
							print '<span class="ord-prd-discount-txt">'.ria_number_format($discount, NumberFormatter::CURRENCY, 2, $prc['money_code']).'</span>';
						}else{
							print '<span class="ord-prd-discount-txt">'.ria_number_format($discount, NumberFormatter::PERCENT, 2).'</span>';
						}
					}

					print '</td>';

					// Total HT de la ligne
					$total_ht_displayed = $prd['total_ht'];
					if( (!is_numeric($fld_discount) || $fld_discount <= 0) && $prd['discount'] > 0 ){
						if( $prd['discount_type'] === '0' ){ // Euros
							$total_ht_displayed = $prd['total_ht'] - $prd['discount'];
						}else{ // %
							$total_ht_displayed = $prd['total_ht'] * (1-($prd['discount']/100));
						}
					}

					print '<td class="align-right td-prd-total">'.ria_number_format($total_ht_displayed, NumberFormatter::CURRENCY, 2, $prc['money_code']).'<input type="hidden" class="ord-prd-total-ht" value="'.$prd['total_ht'].'" /></td>';

					// Déplacement vers le haut ou vers le bas de la ligne (uniquement si la pièce est modifiable)
					print '<td headers="ord-pos" class="ria-cell-move align-center print-none">';
					if( $can_update_order ){
						print '	<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
					}
					print '</td>
						</tr>';
				} else {
					$input_class = '';
					$uniqid = uniqid($ord['id'].$prd['id'].$prd['line']);
					if ($modified_product['prd_id'] == 0 && $modified_product['child_id'] == 0 && $modified_product['line_id'] == $prd['line']){
						$input_class = ' input-edit-success';
					}
					print '<tr class="ord-prd-row ria-row-orderable" id="line-'.$prd['id'].','.$prd['line'].'">';
					if ($can_update_order) {
						print ' <td class="td-engrenage">
									'.( $can_update_order ? '<a href="#" id="btn-'.$btn_count.'" class="edit-cat display-ord-products-options"></a>' : '' ).'
									<span class="menu-cols ord-products-menu-cols">
										<span class="cols">
											<span class="col">
												<input class="btn-link large prd-add" type="button" name="prd-add" value="'._('Ajouter un article').'" />
											</span>';
						if( $config['ord_models_actived'] ) {
							print '         <span class="col">
												<input class="btn-link large prd-add-model" type="button" name="prd-add-model" value="'._('Ajouter un article à partir d\'un modèle').'" />
											</span>';
						}
						print '             <span class="col">
												<input class="btn-link large prd-add-spacing" type="button" name="prd-add-spacing" value="'._('Ajouter un interligne').'" />
											</span>
											<span class="col">
												<input class="btn-link large del-prd" type="button" name="del-prd" value="'._('Supprimer l\'interligne').'"/>
											</span>
										</span>
									</span>
								</td>';
					}
					if ($can_update_order) {
						print ' <td class="ord-prd-info td-style-4 print-none">
									<input type="checkbox" class="ord-prd-id checkbox uniqid" name="ord-prd-select[]" value="' . $uniqid . '" data-name="" />
									<input type="hidden" name="prd[' . $uniqid . ']" value="0" />
									<input type="hidden" name="col[' . $uniqid . ']" value="0" />
									<input type="hidden" name="pos[' . $uniqid . ']" value="' . $prd['prd_pos'] . '" />
									<input type="hidden" name="line[' . $uniqid . ']" value="'.$prd['line'].'" />
									<input type="hidden" name="child-line[' . $uniqid . ']" value="0" />
								</td>';
					}

					// Commentaires
					print '<td colspan="6" class="multi-colspan">';
					if( $ord_viewed['piece'] ){ // Pièce non modifiable
						print nl2br( htmlspecialchars($prd['notes']) );
					}else{ // Pièce modifiable
						print '<textarea rows="2" cols="20" placeholder="'._('Interligne...').'" class="ord-prd-input ord-prd-comment-input' . ($modified_product['edt_col'] == "comment" ? $input_class : '') . ($ord_viewed['piece'] ? ' readonly' : '') . '" name="ord-prd[0]['.htmlspecialchars($prd['line']).'][0][comment]"'. ($ord_viewed['piece'] ? ' readonly="readonly"' : '') .'>'.htmlspecialchars($prd['notes']).'</textarea>';
					}
					print '</td>';

					// Déplacement vers le haut ou vers le bas (si la pièce est modifiable)
					print '<td headers="ord-pos" class="ria-cell-move align-center print-none">';
					if( $can_update_order ){
						print '<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
					}
					print '     </td>
							</tr>';
				}
				$last_prd = $prd;
				$btn_count++;
				$current++;
			}

			if( $can_update_order ){
				print ' <tr>
							<td colspan="9"  class="multi-colspan last-td td-engrenage print-none">
								'.( $can_update_order ? '<a href="#" class="edit-cat display-ord-products-options"></a>' : '' ).'
								<span class="menu-cols ord-products-menu-cols">
									<span class="cols">
										<span class="col">
											<input class="btn-link large prd-add" type="button" name="prd-add" value="'._('Ajouter un article').'"/>
										</span>';
				if( $config['ord_models_actived'] ){
					print '<span class="col">
											<input class="btn-link large prd-add-model" type="button" name="prd-add-model" value="'._('Ajouter un article à partir d\'un modèle').'" />
										</span>';
				}
				print '<span class="col">
											<input class="btn-link large prd-add-spacing" type="button" name="prd-add-spacing" value="'._('Ajouter un interligne').'" />
										</span>
									</span>
								</span>
							</td>
						</tr>';
			}
		?>
		</tbody>
		<tfoot>
			<tr>
				<th colspan="<?php print $can_update_order ? '7' : '5' ?>" class="align-right"><?php print _('Total HT :'); ?></th>
				<td colspan="2" class="multi-colspan"><?php print ria_number_format($ord_viewed['total_ht'], NumberFormatter::CURRENCY, 2, $prc['money_code']); ?></td>
			</tr>
			<tr>
				<th colspan="<?php print $can_update_order ? '7' : '5' ?>" class="align-right"><?php print _('TVA :'); ?></th>
				<td colspan="2" class="multi-colspan"><?php print ria_number_format($ord_viewed['total_ttc']-$ord_viewed['total_ht'], NumberFormatter::CURRENCY, 2, $prc['money_code']); ?></td>
			</tr>
			<tr>
				<th colspan="<?php print $can_update_order ? '7' : '5' ?>" class="align-right"><?php print _('Total TTC :'); ?></th>
				<td colspan="2" class="multi-colspan"><?php print ria_number_format($ord_viewed['total_ttc'], NumberFormatter::CURRENCY, 2, $prc['money_code']); ?></td>
			</tr>
		</tfoot>
	</table>
</td>
<?php } else { ?>
	<td colspan="2" class="td-engrenage multi-colspan">
		<?php
			if( $can_update_order ){
				print '<a href="#" class="edit-cat display-ord-products-options"></a>';
			}
		?>
		<span class="menu-cols ord-products-menu-cols">
			<span class="cols">
				<span class="col">
					<input class="btn-link large prd-add" type="button" name="prd-add" value="<?php print _('Ajouter un article'); ?>"/>
				</span>
				<?php if( $config['ord_models_actived'] ) { ?>
					<span class="col">
						<input class="btn-link large prd-add-model" type="button" name="prd-add-model" value="<?php print _('Ajouter un article à partir d\'un modèle'); ?>" />
					</span>
				<?php } ?>
				<span class="col">
					<input class="btn-link large prd-add-spacing" type="button" name="prd-add-spacing" value="<?php print _('Ajouter un interligne'); ?>" />
				</span>
			</span>
		</span>
		<?php print _('Aucun article dans la commande'); ?>
	</td>
<?php } ?>

<script><!--
	$(document).ready(function(){
		$("#checkall").click(function(){
			if($(this).is(":checked")){
				$(".checkbox").prop("checked", true);
			} else {
				$(".checkbox").prop("checked", false);
			}
		});

		$(".checkbox").click(function(){
			var all_checked = true;
			$(".checkbox").each(function(){
				if (!$(this).is(":checked")){
					all_checked = false;
					return false;
				}
			});
			if (all_checked == true){
				$("#checkall").prop('checked', true);
			} else {
				$("#checkall").prop('checked', false);
			}
		});

	});
//--></script>