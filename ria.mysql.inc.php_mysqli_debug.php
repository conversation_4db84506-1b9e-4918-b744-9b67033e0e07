<?php

/** \defgroup model_db_access Gestion des requêtes à la base de données MySQLi
 *  \ingroup system
 *	Ce module comprend les fonctions nécessaires à la surcharge des fonctions MySQLi.
 *
 *	@{
 */

// --- NOUVELLES DEFINITIONS ET VARIABLES GLOBALES POUR LE LOGGING DE PERFORMANCE ---
if (!defined('PERF_SQL_LOG_DIR')) {
    // Répertoire où les logs de performance par URL seront stockés
    // Assurez-vous que ce chemin est correct et accessible en écriture par le serveur web
    define('PERF_SQL_LOG_DIR', '/apps/instances/76408.pierreoteiza0001/www/perf_sql_by_url_mysqli');
}

// Variable globale pour accumuler les données de performance de la requête HTTP actuelle
$GLOBALS['ria_perf_log_data_mysqli'] = array(
    'total_time' => 0,
    'query_count' => 0,
    'request_url' => null,       // URL complète de la requête actuelle
    'page_call_time' => null,    // Timestamp de l'appel de la page
    'log_file_path' => null,     // Chemin complet du fichier de log pour cette URL type
    'shutdown_function_registered' => false
);
// --- FIN NOUVELLES DEFINITIONS ---


// --- Initialisation Globale ---
// Cette variable globale stockera les configurations et les objets de connexion.
global $ria_db_connect;
if (!is_array($ria_db_connect)) {
    $ria_db_connect = [];
}

// Définir les constantes de type de résultat pour mysqli_fetch_array si elles ne le sont pas
// pour maintenir la compatibilité avec les appels utilisant MYSQL_ASSOC, etc.
if (!defined('MYSQL_ASSOC')) define('MYSQL_ASSOC', 1); // MYSQLI_ASSOC
if (!defined('MYSQL_NUM')) define('MYSQL_NUM', 2);   // MYSQLI_NUM
if (!defined('MYSQL_BOTH')) define('MYSQL_BOTH', 3);  // MYSQLI_BOTH

// Il est crucial que _DB_RIASHOP et _DB_DEFAULT soient définis avant leur utilisation.
// Ces définitions devraient idéalement être dans un fichier de configuration central.
// Exemple :
// if (!defined('_DB_RIASHOP')) { define('_DB_RIASHOP', 'ma_cle_riashop'); }
// if (!defined('_DB_DEFAULT')) { define('_DB_DEFAULT', _DB_RIASHOP); }


/**
 * Établit ou récupère une connexion à une base de données MySQLi.
 * Les paramètres de connexion sont lus depuis les variables d'environnement la première fois
 * pour une clé donnée, ou si la connexion doit être rechargée.
 *
 * @param string $db_connect_key Obligatoire, clé de configuration de la connexion (ex: _DB_RIASHOP).
 * @param bool $reload Optionnel, par défaut à False. Mettre True pour forcer la reconnexion.
 * @return mysqli|bool L'objet de connexion MySQLi si établie, False sinon.
 */
function ria_mysql_connection( $db_connect_key, $reload = false ) {
    global $ria_db_connect;

    // S'assurer que la clé _DB_RIASHOP est disponible si c'est la seule gérée par getenv ici.
    // Cela devrait être défini globalement.
    if (!defined('_DB_RIASHOP')) {
        error_log('[critical] Constante _DB_RIASHOP non définie. Impossible de configurer la connexion depuis getenv.');
        // Pourrait retourner false ou die, selon la criticité. Ici on va loguer et continuer,
        // la logique suivante devrait attraper si $db_connect_key n'est pas configurable.
    }

    // Initialiser la configuration pour cette clé si elle n'existe pas
    // et si c'est la clé spécifique gérée par getenv.
    if (!isset($ria_db_connect[$db_connect_key]['server'])) {
        if (defined('_DB_RIASHOP') && $db_connect_key === _DB_RIASHOP) {
            $db_server = getenv('ENVRIA_BDD_SERVER');
            $db_user   = getenv('ENVRIA_BDD_LOGIN');
            $db_pass   = getenv('ENVRIA_BDD_PWD');
            $db_name   = getenv('ENVRIA_BDD_NAME');

            if ($db_server === false || $db_user === false || $db_pass === false || $db_name === false) {
                error_log('[critical] Une ou plusieurs variables d\'environnement ENVRIA_BDD_* ne sont pas définies pour _DB_RIASHOP.');
                if (php_sapi_name() !== 'cli' && !headers_sent()) header( 'HTTP/1.1 503 Service Unavailable', true, 503 );
                die( 'Configuration de base de données incomplète via les variables d\'environnement pour _DB_RIASHOP.' );
            }

            $ria_db_connect[$db_connect_key] = [
                'server'            => $db_server,
                'user'              => $db_user,
                'password'          => $db_pass,
                'base'              => $db_name,
                'link_identifier'   => null
            ];
        } else if (!defined('_DB_RIASHOP') && !isset($ria_db_connect[$db_connect_key])) {
            error_log('[warning] Tentative d\'utilisation d\'une clé de connexion ('.$db_connect_key.') non initialisée et _DB_RIASHOP non définie pour fallback getenv.');
            // Pas de configuration trouvée, ni de moyen de l'initialiser via getenv.
        }
    }

    // Vérifier si la configuration pour la clé demandée existe maintenant.
    if (!isset($ria_db_connect[$db_connect_key]) || !is_array($ria_db_connect[$db_connect_key]) || !isset($ria_db_connect[$db_connect_key]['server'])) {
        error_log('[critical] Configuration de base de données non trouvée ou incomplète pour la clé : ' . $db_connect_key);
        return false;
    }

    $config = &$ria_db_connect[$db_connect_key];

    if ($reload && isset($config['link_identifier']) && $config['link_identifier'] instanceof mysqli) {
        @mysqli_close($config['link_identifier']);
        $config['link_identifier'] = null;
    }

    if (!$reload && isset($config['link_identifier']) && $config['link_identifier'] instanceof mysqli) {
        if (@mysqli_ping($config['link_identifier'])) {
            return $config['link_identifier'];
        } else {
            @mysqli_close($config['link_identifier']);
            $config['link_identifier'] = null;
            error_log('[warning] Connexion MySQLi perdue pour la clé ' . $db_connect_key . ', tentative de reconnexion.');
        }
    }

    // Utiliser @ pour mysqli_connect pour gérer l'erreur manuellement avec mysqli_connect_errno/error
    $link_identifier = @mysqli_connect($config['server'], $config['user'], $config['password'], $config['base']);

    if (!$link_identifier) {
        error_log('[critical] Base de données indisponible (mysqli_connect) pour la clé '.$db_connect_key.' : Serveur : ' . $config['server'] . ' - Erreur (' . mysqli_connect_errno() . '): ' . mysqli_connect_error());
        if (php_sapi_name() !== 'cli' && !headers_sent()) header('HTTP/1.1 503 Service Unavailable', true, 503);
        die('Base de données indisponible (connexion)');
    }

    if (!mysqli_set_charset($link_identifier, 'utf8')) {
        error_log('[critical] Erreur sur la définition de l\'encodage UTF-8 (mysqli_set_charset) pour la clé '.$db_connect_key.': ' . mysqli_error($link_identifier));
        if (php_sapi_name() !== 'cli' && !headers_sent()) header('HTTP/1.1 503 Service Unavailable', true, 503);
        mysqli_close($link_identifier);
        die('Erreur sur la définition de l\'encodage');
    }

    $config['link_identifier'] = $link_identifier;
    return $link_identifier;
}


/**
 * Exécute une requête SQL en utilisant MySQLi.
 * @param string $sql Obligatoire, la requête SQL.
 * @param string|false $db_connect_key Optionnel, clé de la connexion à utiliser (défaut: _DB_DEFAULT).
 * @return mysqli_result|bool Résultat de la requête ou false en cas d'erreur.
 */
function ria_mysql_query($sql, $db_connect_key=false){
    global $ria_debug_timer, $ria_db_selected, $ar_debug_request_bdd;

    if (!defined('_DB_DEFAULT')) {
        if (defined('_DB_RIASHOP')) {
            define('_DB_DEFAULT', _DB_RIASHOP);
        } else {
            error_log('[critical] _DB_DEFAULT et _DB_RIASHOP ne sont pas définis. Impossible de déterminer une connexion par défaut.');
            // Gérer cette situation critique, peut-être en définissant une clé par défaut absolue ou en terminant.
            // Pour l'exemple, on va supposer une clé par défaut mais c'est à corriger.
            define('_DB_DEFAULT', 'fallback_default_key'); // Très mauvais, à corriger
        }
    }

    if ($db_connect_key === false) {
        $db_connect_key = _DB_DEFAULT;
        if (isset($ria_db_selected) && $ria_db_selected !== false) {
            $db_connect_key = $ria_db_selected;
        }
    }

    // --- INITIALISATION DES DONNEES DE LOG DE PERFORMANCE POUR CET APPEL HTTP (si première requête) ---
    if ($GLOBALS['ria_perf_log_data_mysqli']['request_url'] === null) {
        $GLOBALS['ria_perf_log_data_mysqli']['page_call_time'] = isset($_SERVER['REQUEST_TIME_FLOAT']) ? $_SERVER['REQUEST_TIME_FLOAT'] : (isset($_SERVER['REQUEST_TIME']) ? (float)$_SERVER['REQUEST_TIME'] : microtime(true));

        $filename_base = 'unknown_url';
        if (php_sapi_name() === 'cli') {
            $cli_script_name = isset($_SERVER['SCRIPT_FILENAME']) ? $_SERVER['SCRIPT_FILENAME'] : 'unknown_script_cli';
            $GLOBALS['ria_perf_log_data_mysqli']['request_url'] = 'CLI:' . $cli_script_name;
            $filename_base = preg_replace('/[^a-zA-Z0-9_-]/', '_', basename($cli_script_name));
        } else {
            $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http");
            $host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'unknown_host';

            $full_request_uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '/unknown_uri';
            $GLOBALS['ria_perf_log_data_mysqli']['request_url'] = $protocol . "://" . $host . $full_request_uri;

            $path_for_filename = isset($_SERVER['SCRIPT_NAME']) ? $_SERVER['SCRIPT_NAME'] : (isset($_SERVER['PHP_SELF']) ? $_SERVER['PHP_SELF'] : '/unknown_path');
            $url_structure_for_filename = $host . $path_for_filename;
            $filename_base = md5($url_structure_for_filename);
        }
        $GLOBALS['ria_perf_log_data_mysqli']['log_file_path'] = PERF_SQL_LOG_DIR . '/' . $filename_base . '.log';

        if (!is_dir(PERF_SQL_LOG_DIR)) {
            @mkdir(PERF_SQL_LOG_DIR, 0775, true);
        }
    }
    // --- FIN INITIALISATION LOG ---

    $link_identifier = ria_mysql_connection( $db_connect_key );
    if( $link_identifier === false ){
        // Log d'échec de connexion
        $pageCallTimestampForError = isset($_SERVER['REQUEST_TIME_FLOAT']) ? $_SERVER['REQUEST_TIME_FLOAT'] : (isset($_SERVER['REQUEST_TIME']) ? (float)$_SERVER['REQUEST_TIME'] : microtime(true));
        $pageCallTimeFormattedForError = date('Y-m-d H:i:s.u', $pageCallTimestampForError);
        $requestUrlForError = $GLOBALS['ria_perf_log_data_mysqli']['request_url'] ? $GLOBALS['ria_perf_log_data_mysqli']['request_url'] : 'N/A';
        $log_message_fail_conn = sprintf(
            "[%s] URL: %s | DB_CONN_FAIL_FOR_KEY: %s | SQL_ATTEMPTED: %s",
            $pageCallTimeFormattedForError, $requestUrlForError,
            is_scalar($db_connect_key) ? (string)$db_connect_key : gettype($db_connect_key),
            preg_replace('/\s+/', ' ', trim($sql))
        );
        error_log("RIA_MYSQLI_QUERY_ERROR: " . $log_message_fail_conn);
        return false;
    }

    $start = microtime( true );
    $res_query = mysqli_query( $link_identifier, $sql );
    $end = microtime( true );
    $executionTime = $end - $start;

    // Accumuler les données de performance
    $GLOBALS['ria_perf_log_data_mysqli']['total_time'] += $executionTime;
    $GLOBALS['ria_perf_log_data_mysqli']['query_count']++;

    if (!$GLOBALS['ria_perf_log_data_mysqli']['shutdown_function_registered']) {
        register_shutdown_function('ria_mysqli_log_total_query_time_on_shutdown');
        $GLOBALS['ria_perf_log_data_mysqli']['shutdown_function_registered'] = true;
    }


    // --- Début du code de debug (adapté de l'original) ---
    $fn_strtolower = function_exists('strtolower2') ? 'strtolower2' : 'strtolower';

    if( isset($_GET['Ria_show_SQL_Request']) && in_array($_GET['Ria_show_SQL_Request'], ['debug', 'show']) ){
        if( $_GET['Ria_show_SQL_Request'] == 'show' ){
            print '<br />'.number_format(round($executionTime, 6), 6, ',', '').';'.htmlspecialchars(trim($sql));
        }
        if (!isset($ar_debug_request_bdd) || !is_array($ar_debug_request_bdd)) $ar_debug_request_bdd = [];
        $md5_sql = md5( $fn_strtolower(trim($sql)) );
        if( !isset($ar_debug_request_bdd[ $md5_sql ]) ){
            $ar_debug_request_bdd[ $md5_sql ] = ['SQL' => $sql, 'min' => $executionTime, 'max' => $executionTime, 'sum' => $executionTime, 'count' => 1];
        }else{
            if( $ar_debug_request_bdd[ $md5_sql ]['min'] > $executionTime ) $ar_debug_request_bdd[ $md5_sql ]['min'] = $executionTime;
            if( $ar_debug_request_bdd[ $md5_sql ]['max'] < $executionTime ) $ar_debug_request_bdd[ $md5_sql ]['max'] = $executionTime;
            $ar_debug_request_bdd[ $md5_sql ]['sum'] += $executionTime;
            $ar_debug_request_bdd[ $md5_sql ]['count']++;
        }
    }

    if( ($res_query === false) && !(preg_match("/Duplicate entry/i", ria_mysql_error($db_connect_key))) ){
        $debug_backtrace_options = defined('DEBUG_BACKTRACE_IGNORE_ARGS') ? DEBUG_BACKTRACE_IGNORE_ARGS : 0;
        if (defined('DEBUG_BACKTRACE_PROVIDE_OBJECT')) $debug_backtrace_options |= DEBUG_BACKTRACE_PROVIDE_OBJECT;

        error_log(
            "RIA_MYSQLI_QUERY_ERROR: La requête SQL suivante a échoué :\n"
            .'======================================='."\n"
            .ria_mysql_error($db_connect_key)." (".ria_mysql_errno($db_connect_key).") :\n"
            .'======================================='."\n"
            .$sql."\n".
            '======================================='."\n"
            .'Contexte (backtrace) :'."\n"
            .print_r(debug_backtrace( $debug_backtrace_options, 1 ), true)
        );
    }

    if (function_exists('ria_debug_is_actived') && ria_debug_is_actived() && function_exists('ria_debug_page_excluded') && !ria_debug_page_excluded()) {
        if (!isset($_SESSION['ria_debug']) || !is_array($_SESSION['ria_debug'])) $_SESSION['ria_debug'] = ['get' => [], 'post' => [], 'request' => []];
        $method = 'request';
        if (isset($_SERVER['REQUEST_METHOD']) && in_array(strtoupper($_SERVER['REQUEST_METHOD']), ['GET', 'POST'])) $method = strtolower($_SERVER['REQUEST_METHOD']);
        $current_debug_timer = isset($ria_debug_timer) ? $ria_debug_timer : 'default_timer';
        $script_url = isset($_SERVER['SCRIPT_URL']) ? $_SERVER['SCRIPT_URL'] : (isset($_SERVER['PHP_SELF']) ? $_SERVER['PHP_SELF'] : 'cli');

        $debug_backtrace_options_session = defined('DEBUG_BACKTRACE_IGNORE_ARGS') ? DEBUG_BACKTRACE_IGNORE_ARGS : 0;

        $_SESSION['ria_debug'][ $method ][ $script_url ][ $current_debug_timer ][] = [
            'time' => $executionTime, 'sql'  => $sql, 'server' => $_SERVER, // $_SERVER est très gros
            'backtrace' => debug_backtrace($debug_backtrace_options_session),
        ];
    }
    // --- Fin du code de debug ---
    return $res_query;
}


/**
 * Fonction appelée à la fin du script pour logger le total des temps de requête (version MySQLi).
 */
function ria_mysqli_log_total_query_time_on_shutdown() {
    if ($GLOBALS['ria_perf_log_data_mysqli']['query_count'] > 0 && $GLOBALS['ria_perf_log_data_mysqli']['log_file_path'] !== null) {

        $log_dir = dirname($GLOBALS['ria_perf_log_data_mysqli']['log_file_path']);
        if (!is_dir($log_dir)) {
            if (!@mkdir($log_dir, 0775, true)) {
                error_log("RIA_MYSQLI_PERF_LOG: Impossible de créer le répertoire de log: " . $log_dir);
                return;
            }
        }

        $pageCallTimeFormatted = date('Y-m-d H:i:s.u', $GLOBALS['ria_perf_log_data_mysqli']['page_call_time']);

        $main_script_name = 'N/A';
        if (php_sapi_name() === 'cli') {
            $main_script_name = basename(isset($_SERVER['SCRIPT_FILENAME']) ? $_SERVER['SCRIPT_FILENAME'] : 'cli_script');
        } else {
            $main_script_name = basename(isset($_SERVER['SCRIPT_FILENAME']) ? $_SERVER['SCRIPT_FILENAME'] : (isset($_SERVER['PHP_SELF']) ? $_SERVER['PHP_SELF'] : 'web_script'));
        }

        $log_message_summary = sprintf(
            "[%s] Page: %s | TotalQueryTime: %.6f s | QueryCount: %d | Script: %s",
            $pageCallTimeFormatted,
            $GLOBALS['ria_perf_log_data_mysqli']['request_url'],
            $GLOBALS['ria_perf_log_data_mysqli']['total_time'],
            $GLOBALS['ria_perf_log_data_mysqli']['query_count'],
            $main_script_name
        );

        @error_log($log_message_summary . PHP_EOL, 3, $GLOBALS['ria_perf_log_data_mysqli']['log_file_path']);
    }
}


/**
 * Vérifie si la variable est un résultat MySQLi valide.
 * @param mixed $resource La variable à vérifier.
 * @return bool True si c'est un objet mysqli_result, False sinon.
 */
function ria_mysql_control_ressource( $resource ){
    return ( $resource instanceof mysqli_result );
}

/**
 * Retourne le nombre de lignes affectées par la dernière requête INSERT, UPDATE, REPLACE ou DELETE.
 * @param string|false $db_connect_key Clé de la connexion.
 * @return int Nombre de lignes affectées, -1 en cas d'erreur de connexion.
 */
function ria_mysql_affected_rows( $db_connect_key=false ){
    global $ria_db_selected;
    if (!defined('_DB_DEFAULT')) { if (defined('_DB_RIASHOP')) define('_DB_DEFAULT', _DB_RIASHOP); else define('_DB_DEFAULT', 'fallback_default_key');}

    if ($db_connect_key === false) {
        $db_connect_key = _DB_DEFAULT;
        if (isset($ria_db_selected) && $ria_db_selected !== false) $db_connect_key = $ria_db_selected;
    }
    $link_identifier = ria_mysql_connection( $db_connect_key );
    return $link_identifier ? mysqli_affected_rows( $link_identifier ) : -1;
}

/**
 * Déplace le pointeur de résultat interne.
 * @param mysqli_result $result L'objet résultat.
 * @param int $row_number Le numéro de la ligne.
 * @return bool True en cas de succès, False sinon.
 */
function ria_mysql_data_seek( $result, $row_number ){
    if (!($result instanceof mysqli_result)) return false;
    return mysqli_data_seek( $result, $row_number );
}

/**
 * Retourne le numéro d'erreur de la dernière opération MySQLi pour une connexion,
 * ou le numéro d'erreur de la dernière tentative de connexion si $link_identifier est null.
 * @param string|false $db_connect_key Clé de la connexion.
 * @return int Numéro d'erreur. 0 si pas d'erreur.
 */
function ria_mysql_errno( $db_connect_key=false ){
    global $ria_db_selected;
    if (!defined('_DB_DEFAULT')) { if (defined('_DB_RIASHOP')) define('_DB_DEFAULT', _DB_RIASHOP); else define('_DB_DEFAULT', 'fallback_default_key');}

    if ($db_connect_key === false) {
        $db_connect_key = _DB_DEFAULT;
        if (isset($ria_db_selected) && $ria_db_selected !== false) $db_connect_key = $ria_db_selected;
    }
    $link_identifier = ria_mysql_connection( $db_connect_key, false );
    if ( !$link_identifier ) return mysqli_connect_errno(); // Important: erreur de la tentative de connexion
    return mysqli_errno( $link_identifier );
}

/**
 * Retourne le message d'erreur de la dernière opération MySQLi pour une connexion,
 * ou le message d'erreur de la dernière tentative de connexion.
 * @param string|false $db_connect_key Clé de la connexion.
 * @return string Message d'erreur. Chaîne vide si pas d'erreur.
 */
function ria_mysql_error( $db_connect_key=false ){
    global $ria_db_selected;
    if (!defined('_DB_DEFAULT')) { if (defined('_DB_RIASHOP')) define('_DB_DEFAULT', _DB_RIASHOP); else define('_DB_DEFAULT', 'fallback_default_key');}

    if ($db_connect_key === false) {
        $db_connect_key = _DB_DEFAULT;
        if (isset($ria_db_selected) && $ria_db_selected !== false) $db_connect_key = $ria_db_selected;
    }
    $link_identifier = ria_mysql_connection( $db_connect_key, false );
    if ( !$link_identifier ) return mysqli_connect_error(); // Important: erreur de la tentative de connexion
    return mysqli_error( $link_identifier );
}

/**
 * Échappe les caractères spéciaux d'une chaîne pour l'utiliser dans une requête SQL.
 * @param string $unescaped_string La chaîne à échapper.
 * @return string|false La chaîne échappée, ou false si la connexion par défaut échoue.
 */
function ria_mysql_escape_string( $unescaped_string ){
    // N'a pas besoin de $ria_db_selected, utilise toujours _DB_DEFAULT pour l'instant.
    // Ou alors, il faut lui passer $db_connect_key si on veut plus de flexibilité.
    // Pour la compatibilité avec l'ancien `mysql_escape_string` qui ne prenait pas de lien.
    if (!defined('_DB_DEFAULT')) { if (defined('_DB_RIASHOP')) define('_DB_DEFAULT', _DB_RIASHOP); else define('_DB_DEFAULT', 'fallback_default_key');}

    $db_connect_key = _DB_DEFAULT; // On force la connexion par défaut pour cette fonction
    // car elle n'a pas de paramètre $db_connect_key

    // Si $ria_db_selected est pertinent ici, il faudrait le prendre en compte,
    // mais la signature de la fonction originale ne le permet pas directement.
    // global $ria_db_selected;
    // if (isset($ria_db_selected) && $ria_db_selected !== false) $db_connect_key = $ria_db_selected;


    $link_identifier = ria_mysql_connection( $db_connect_key );
    if ( !$link_identifier ) {
        error_log("RIA_MYSQLI_ESCAPE_STRING_ERROR: Impossible d'obtenir un lien de connexion pour échapper la chaîne. Clé: ".$db_connect_key." Utilisation d'un échappement générique (addslashes, moins sûr).");
        return addslashes($unescaped_string); // Fallback très risqué
    }
    return mysqli_real_escape_string( $link_identifier, $unescaped_string );
}

/**
 * Retourne le nombre de lignes dans un résultat.
 * @param mysqli_result $result L'objet résultat.
 * @return int|false Nombre de lignes, ou false si $result n'est pas valide.
 */
function ria_mysql_num_rows( $result ){
    if (!ria_mysql_control_ressource($result)) return false;
    return mysqli_num_rows( $result );
}

/**
 * Récupère une ligne de résultat sous forme de tableau associatif, numérique, ou les deux.
 * @param mysqli_result $result L'objet résultat.
 * @param int $result_type Type de tableau (MYSQL_ASSOC, MYSQL_NUM, MYSQL_BOTH).
 * @return array|null|false La ligne, ou null/false si plus de lignes ou erreur.
 */
function ria_mysql_fetch_array( $result, $result_type=MYSQL_BOTH ){
    if( !($result instanceof mysqli_result) ){
        $debug_backtrace_options_fa = defined('DEBUG_BACKTRACE_IGNORE_ARGS') ? DEBUG_BACKTRACE_IGNORE_ARGS : 0;
        error_log('RIA_MYSQLI_FETCH_ARRAY_ERROR: $result n\'est pas un mysqli_result. Contexte: '.print_r(debug_backtrace($debug_backtrace_options_fa,1),true));
        return false;
    }
    $mysqli_result_type = MYSQLI_BOTH;
    if ($result_type === MYSQL_ASSOC || $result_type === MYSQLI_ASSOC) $mysqli_result_type = MYSQLI_ASSOC;
    elseif ($result_type === MYSQL_NUM || $result_type === MYSQLI_NUM) $mysqli_result_type = MYSQLI_NUM;

    return mysqli_fetch_array( $result, $mysqli_result_type );
}

/**
 * Récupère une ligne de résultat sous forme de tableau associatif.
 * @param mysqli_result $result L'objet résultat.
 * @return array|null|false La ligne associative, ou null/false.
 */
function ria_mysql_fetch_assoc( $result ){
    if( $result === false ) return false;
    if( !($result instanceof mysqli_result) ){
        $debug_backtrace_options_fas = defined('DEBUG_BACKTRACE_IGNORE_ARGS') ? DEBUG_BACKTRACE_IGNORE_ARGS : 0;
        error_log('RIA_MYSQLI_FETCH_ASSOC_ERROR: $result n\'est pas un mysqli_result. Contexte: '.print_r(debug_backtrace($debug_backtrace_options_fas,1),true));
        return false;
    }
    return mysqli_fetch_assoc( $result );
}

/**
 * Récupère toutes les lignes de résultat sous forme de tableau de tableaux associatifs.
 * @param mysqli_result $result L'objet résultat.
 * @return array Tableau de toutes les lignes.
 */
function ria_mysql_fetch_assoc_all( $result ){
    if ( !($result instanceof mysqli_result) ) return [];
    $response = array();
    if (function_exists('mysqli_fetch_all')) { // PHP >= 5.3.0 et mysqlnd
        $response = mysqli_fetch_all($result, MYSQLI_ASSOC);
    } else {
        while( $r = mysqli_fetch_assoc($result) ){ // Utiliser mysqli_fetch_assoc directement
            $response[] = $r;
        }
    }
    return $response;
}

/**
 * Retourne l'ID généré par la dernière requête INSERT.
 * @param string|false $db_connect_key Clé de la connexion.
 * @return int|string L'ID généré. 0 si la dernière requête n'a pas généré d'ID ou erreur de connexion.
 */
function ria_mysql_insert_id( $db_connect_key=false ){
    global $ria_db_selected;
    if (!defined('_DB_DEFAULT')) { if (defined('_DB_RIASHOP')) define('_DB_DEFAULT', _DB_RIASHOP); else define('_DB_DEFAULT', 'fallback_default_key');}

    if ($db_connect_key === false) {
        $db_connect_key = _DB_DEFAULT;
        if (isset($ria_db_selected) && $ria_db_selected !== false) $db_connect_key = $ria_db_selected;
    }
    $link_identifier = ria_mysql_connection( $db_connect_key );
    if (!$link_identifier) return 0;

    $id_temp = mysqli_insert_id( $link_identifier );
    // Le fallback avec SELECT LAST_INSERT_ID() est moins pertinent avec mysqli_insert_id
    // car mysqli_insert_id est généralement fiable. Si elle retourne 0 ou une chaîne vide,
    // c'est que la requête n'a pas généré d'ID auto-incrémenté.
    // On le conserve pour la compatibilité stricte de comportement si l'ancien code en dépendait.
    if (!$id_temp || $id_temp === "0") { // mysqli_insert_id peut retourner une chaîne "0"
        $res_bdd = @mysqli_query( $link_identifier, "SELECT LAST_INSERT_ID()"); // @ pour éviter warning si la query échoue
        if ($res_bdd && mysqli_num_rows($res_bdd) > 0) {
            $row = mysqli_fetch_row($res_bdd);
            $id_temp = $row[0];
            mysqli_free_result($res_bdd);
        } else {
            // Si $res_bdd est false, mysqli_error($link_identifier) donnerait l'erreur.
            // S'il n'y a pas de rows, LAST_INSERT_ID() n'a pas de valeur pour cette session.
        }
    }
    return $id_temp;
}

/**
 * Récupère la valeur d'un champ spécifique d'une ligne spécifique d'un résultat.
 * @param mysqli_result $result L'objet résultat.
 * @param int $row Le numéro de la ligne (commence à 0).
 * @param mixed $field L'index numérique ou le nom du champ.
 * @return mixed|false La valeur du champ, ou false en cas d'erreur.
 */
function ria_mysql_result( $result, $row, $field=0 ){
    if ( !($result instanceof mysqli_result) ) return false;
    if ( $row < 0 || $row >= mysqli_num_rows( $result ) ) return false;
    if (!mysqli_data_seek( $result, $row )) return false;

    $fetched_row = null;
    if ( is_numeric( $field ) ) {
        $fetched_row = mysqli_fetch_row( $result );
        return ( $fetched_row && isset( $fetched_row[ $field ] ) ) ? $fetched_row[ $field ] : false;
    } else {
        $fetched_row = mysqli_fetch_assoc( $result );
        return ( $fetched_row && isset( $fetched_row[ $field ] ) ) ? $fetched_row[ $field ] : false;
    }
}

/**
 * Récupère une ligne de résultat sous forme de tableau numérique.
 * @param mysqli_result $result L'objet résultat.
 * @return array|null|false La ligne numérique, ou null/false.
 */
function ria_mysql_fetch_row( $result ){
    if ( !($result instanceof mysqli_result) ) return false;
    return mysqli_fetch_row( $result );
}

/**
 * Retourne le nom d'un champ spécifié.
 * @param mysqli_result $result L'objet résultat.
 * @param int $field_offset L'index numérique du champ.
 * @return string|false Le nom du champ, ou false.
 */
function ria_mysql_field_name( $result, $field_offset ){
    if ( !($result instanceof mysqli_result) ) return false;
    $field_info = mysqli_fetch_field_direct( $result, $field_offset );
    return $field_info ? $field_info->name : false;
}

/**
 * Retourne le nom de la table d'un champ spécifié.
 * @param mysqli_result $result L'objet résultat.
 * @param int $field_offset L'index numérique du champ.
 * @return string|false Le nom de la table, ou false.
 */
function ria_mysql_field_table( $result, $field_offset ){
    if ( !($result instanceof mysqli_result) ) return false;
    $field_info = mysqli_fetch_field_direct( $result, $field_offset );
    // 'table' est le nom de l'alias, 'orgtable' est le nom original de la table
    return $field_info ? $field_info->table : false;
}

/**
 * Retourne le nombre de champs dans un résultat.
 * @param mysqli_result $result L'objet résultat.
 * @return int|false Le nombre de champs, ou false si erreur.
 */
function ria_mysql_num_fields( $result ){
    if ( !($result instanceof mysqli_result) ) return false;
    return mysqli_num_fields( $result );
}
/// @}

// \endcond
?>