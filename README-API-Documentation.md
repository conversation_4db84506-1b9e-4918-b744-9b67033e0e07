# RiaShop API Documentation

This documentation provides comprehensive information about the RiaShop custom API located in `/backend/htdocs/api`.

## Overview

The RiaShop API is a RESTful API built with PHP 5.6 compatibility that provides access to all major e-commerce functionalities including user management, product catalog, orders, categories, and more.

## Quick Start

### 1. Import Postman Collection
- Import the `riashop-api-documentation.json` file into Postman
- Set up environment variables (see Configuration section below)
- Start making API calls

### 2. Configuration

Set up the following environment variables in Postman:

| Variable | Description | Example |
|----------|-------------|---------|
| `base_url` | API base URL | `http://localhost/backend/htdocs/api` |
| `logtoken` | Tenant identification token (MD5 hash) | `your_tenant_token_here` |
| `token` | User authentication token | `your_user_token_here` |
| `email` | User email for authentication | `<EMAIL>` |
| `password` | User password (MD5 hashed) | `password_hash` |
| `device_id` | Device identifier | `device123` |

## Authentication

The API uses a two-level authentication system:

### 1. Tenant Authentication (Required)
- **Parameter**: `logtoken`
- **Description**: MD5 hash that identifies the tenant/client
- **Usage**: Required for ALL API requests
- **Location**: Query parameter

### 2. User Authentication (Optional/Required)
- **Parameter**: `token`
- **Description**: User authentication token obtained from device authentication
- **Usage**: Required for most endpoints (except basic health check)
- **Location**: Query parameter

### Authentication Flow
1. Make a health check request with `logtoken` to verify tenant access
2. Authenticate device/user using `/devices/` endpoint to get user `token`
3. Use both `logtoken` and `token` for subsequent API calls

## API Structure

### Base URL Pattern
```
{base_url}/{module}/{action}?logtoken={logtoken}&token={token}
```

### HTTP Methods
- **GET**: Retrieve data
- **POST**: Create new records
- **PUT**: Update existing records
- **DELETE**: Remove records

### Response Format
All API responses follow this consistent JSON structure:

```json
{
  "result": boolean,
  "time": "YYYY-MM-DD HH:MM:SS",
  "message": "Error message if result is false",
  "content": {} // Response data if result is true
}
```

## Available Modules

### Core Modules
- **Authentication & System**: Health checks, device authentication
- **Users Management**: User accounts, profiles, addresses
- **Products Management**: Product catalog, relations, statistics
- **Categories Management**: Product categories hierarchy
- **Orders Management**: Order processing, statistics, completion, **PDF generation**
- **Brands Management**: Product brands information
- **PDF Generation & Documents**: Order PDFs, delivery note PDFs, return PDFs, report PDFs
- **Data Export & Import**: Comprehensive CSV/Excel exports with advanced filtering

### Additional Modules Available
The API includes 40+ modules covering:
- **Addresses**: Address management and geolocation
- **Banks**: Banking information
- **BL (Delivery Notes)**: Delivery note management
- **Calls**: Call management
- **Chats**: Chat and messaging
- **Delivery**: Delivery services and packages
- **Devices**: Device management and synchronization
- **Documents**: Document management
- **Email**: Email sending
- **Exports**: Data export functionality
- **Fields**: Custom field management
- **Images**: Image management
- **Imports**: Data import functionality
- **Invoices**: Invoice management
- **Notes**: Note management
- **Notifications**: Notification system
- **Prices**: Pricing and tariffs
- **Profiles**: User profiles
- **Promotions**: Promotional campaigns
- **Relations**: Relationship management
- **Reports**: Reporting system
- **Returns**: Return management
- **Rewards**: Reward system
- **Search**: Search functionality
- **Serials**: Serial number management
- **Stocks**: Inventory management
- **Sync**: Synchronization services
- **Tenants**: Multi-tenant management
- **Wishlists**: Wishlist management

## PDF Generation & Document Export

### PDF Generation Endpoints
The API provides comprehensive PDF generation capabilities for e-commerce documents:

#### Order PDFs
```bash
GET /orders/pdf/?logtoken={{logtoken}}&token={{token}}&id=123
```
Generates professional PDF documents for orders including customer details, product lists, pricing, and branding.

#### Delivery Note PDFs
```bash
GET /bl/pdf/?logtoken={{logtoken}}&token={{token}}&id=123
```
Creates delivery note (bon de livraison) PDFs with shipping information and product details.

#### Return PDFs
```bash
GET /return/pdf/?logtoken={{logtoken}}&token={{token}}&id=123
```
Generates return authorization PDFs with return details and processing information.

#### Report PDFs
```bash
GET /reports/pdf/?logtoken={{logtoken}}&token={{token}}&id=123
```
Creates custom report PDFs with analytics and business intelligence data.

### Data Export Functionality
The `/exports/` endpoint provides powerful data export capabilities:

#### Product Exports
- **Format**: CSV or Excel
- **Features**: Customizable columns, category filtering, brand filtering, multi-language support
- **Images**: Export product images with size specifications
- **SEO**: Include SEO references and custom fields

#### Category Exports
- **Format**: CSV or Excel
- **Features**: Hierarchical structure, recursive child categories, image size options
- **Filtering**: Exclude specific categories, custom header rows

#### Order Exports
- **Format**: Excel with dual-sheet layout
- **Sheet 1**: Order headers with customer details, addresses, totals
- **Sheet 2**: Order line items with product details
- **Filtering**: Date ranges, payment methods, order states, customer filters
- **Advanced**: Multi-currency support, custom field integration

#### Delivery Note Exports
- **Format**: Excel
- **Features**: Seller filtering, depot filtering, date range filtering
- **Integration**: Links to related orders and customer information

### File Operations

#### Document Management
```bash
# Download document
GET /documents/file/?logtoken={{logtoken}}&token={{token}}&id=123

# Upload document
POST /documents/file/?logtoken={{logtoken}}&token={{token}}
Content-Type: multipart/form-data
```

#### Image Management
```bash
# Download image
GET /images/file/?logtoken={{logtoken}}&token={{token}}&id=123
```

## Common Parameters

### Pagination
- `start`: Starting record number (default: 0)
- `limit`: Maximum records to return (default: 150, max varies by endpoint)
- `offset`: Alternative to start parameter

### Filtering
- `id`: Single ID or array of IDs
- `ref`: Reference code filter
- `email`: Email filter (for users)
- Various module-specific filters

## Error Handling

### HTTP Status Codes
- **200 OK**: Successful request
- **400 Bad Request**: Invalid parameters or request format

### Error Response Format
```json
{
  "result": false,
  "time": "2024-01-01 12:00:00",
  "message": "Error description",
  "content": {},
  "code": "error_code", // Optional
  "ria_code": "ria_error_code", // Optional for RiaShop-specific errors
  "ria_param": "missing_parameter" // Optional for missing parameters
}
```

## Usage Examples

### 1. Health Check
```bash
GET {{base_url}}/?logtoken={{logtoken}}
```

### 2. User Authentication
```bash
GET {{base_url}}/devices/?logtoken={{logtoken}}&email={{email}}&password={{password}}&device={{device_id}}
```

### 3. Get Users
```bash
GET {{base_url}}/users/?logtoken={{logtoken}}&token={{token}}&limit=50
```

### 4. Create User
```bash
POST {{base_url}}/users/?logtoken={{logtoken}}&token={{token}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "ref": "USER001",
  "firstname": "John",
  "lastname": "Doe",
  "society": "Example Corp",
  "phone": "+33123456789",
  "prc": "1"
}
```

## PHP 5.6 Compatibility

This API is built with PHP 5.6 compatibility in mind:
- No namespaces used
- No type declarations
- No return type hints
- Compatible with older PHP syntax
- Uses traditional array syntax

## Security Considerations

1. **Token Security**: Keep logtoken and user tokens secure
2. **HTTPS**: Use HTTPS in production environments
3. **Input Validation**: API performs server-side validation
4. **Rate Limiting**: Consider implementing rate limiting for production use

## Troubleshooting

### Common Issues
1. **Invalid logtoken**: Verify tenant token is correct MD5 hash
2. **Missing token**: Ensure user authentication is completed first
3. **Parameter errors**: Check required parameters for each endpoint
4. **PHP errors**: Verify PHP 5.6+ compatibility

### Debug Mode
The API includes error logging and debug capabilities. Check server logs for detailed error information.

## Support

For additional endpoints or specific functionality not covered in this documentation, refer to the source code in `/backend/htdocs/api/` or contact the development team.

## Version Information

- **API Version**: Based on RiaShop platform
- **PHP Compatibility**: 5.6+
- **Documentation Format**: Postman Collection v2.1.0
- **Last Updated**: 2024-01-01
