<?php
// \cond onlyria

/** \defgroup model_db_access Gestion des requêtes à la base de données MySQL
 *  \ingroup system
 *	Ce module comprend les fonctions nécessaires à la surcharge des fonctions MySQL.
 *
 *	@{
 */

// --- NOUVELLES DEFINITIONS ET VARIABLES GLOBALES POUR LE LOGGING DE PERFORMANCE ---
if (!defined('PERF_SQL_LOG_DIR')) {
    // Répertoire où les logs de performance par URL seront stockés
    define('PERF_SQL_LOG_DIR', '/apps/instances/76408.pierreoteiza0001/www/perf_sql_by_url');
}

// Variable globale pour accumuler les données de performance de la requête HTTP actuelle
$GLOBALS['ria_perf_log_data'] = array(
    'total_time' => 0,
    'query_count' => 0,
    'request_url' => null,       // URL complète de la requête actuelle
    'page_call_time' => null,    // Timestamp de l'appel de la page
    'log_file_path' => null,     // Chemin complet du fichier de log pour cette URL type
    'shutdown_function_registered' => false
);
// --- FIN NOUVELLES DEFINITIONS ---


/** Cette fonction permet détablir une connexion avec une base de données (en local ou à distance)
 *	@param array $db_connect Obligatoire, quelle configuration de connexion utiliser
 *	@param bool $reload Optionnel, par défaut à False, mettre True pour forçer la reconnexion
 *	@return resource|bool La connexion MySQL si elle est établie, False dans le cas contraire
 *	@see https://www.php.net/manual/fr/function.mysql-connect.php
 */
function ria_mysql_connection( $db_connect, $reload=false ){
    global $ria_db_connect;

    if (!defined('_DB_RIASHOP')) {
        // Vous devriez définir _DB_RIASHOP quelque part, ex: define('_DB_RIASHOP', 'unique_key_for_riashop');
        // Pour cet exemple, je vais utiliser une chaîne littérale si non définie, mais ce n'est pas idéal.
        // error_log("Warning: _DB_RIASHOP is not defined.", 0);
    }
    $db_riashop_key = defined('_DB_RIASHOP') ? _DB_RIASHOP : 'riashop_default_key_placeholder';


    // S'assurer que $ria_db_connect est initialisé
    if (!isset($ria_db_connect) || !is_array($ria_db_connect)) {
        $ria_db_connect = array();
    }

    // Configurer la connexion si elle n'est pas déjà dans $ria_db_connect
    // Normalement, cette partie devrait être remplie par une configuration externe.
    if (!array_key_exists($db_riashop_key, $ria_db_connect)) {
        $ria_db_connect[$db_riashop_key] = array(
            'server'            => getenv('ENVRIA_BDD_SERVER'),
            'user'              => getenv('ENVRIA_BDD_LOGIN'),
            'password'          => getenv('ENVRIA_BDD_PWD'),
            'base'              => getenv('ENVRIA_BDD_NAME'),
            'link_identifier'   => false
        );
    }


    if( !is_array($ria_db_connect) || !array_key_exists($db_connect, $ria_db_connect) ){
        // Pas d'accès à PERF_SQL_LOG_DIR ici car $GLOBALS['ria_perf_log_data'] n'est pas encore initialisé
        // On pourrait logguer dans le error_log général du serveur.
        error_log(sprintf("RIA_MYSQL_LOG: Attempt to connect with unknown DB key: %s", is_scalar($db_connect) ? (string)$db_connect : gettype($db_connect)));
        return false;
    }

    if (!$reload && isset($ria_db_connect[ $db_connect ]['link_identifier']) && $ria_db_connect[ $db_connect ]['link_identifier']) {
        return $ria_db_connect[ $db_connect ]['link_identifier'];
    }

    $link_identifier = mysql_connect( $ria_db_connect[ $db_connect ]['server'], $ria_db_connect[ $db_connect ]['user'], $ria_db_connect[ $db_connect ]['password'] );
    if (!$link_identifier) {
        error_log( '[critical] Base de données indisponible, impossible d\'accéder au serveur : '. $ria_db_connect[ $db_connect ]['server'] );
        if (php_sapi_name() !== 'cli') header( 'HTTP/1.1 503 Service Unavailable', true, 503 );
        die( 'Base de données indisponible' );
    }

    if( !@mysql_select_db( $ria_db_connect[ $db_connect ]['base'], $link_identifier ) ){ // Ajout de $link_identifier
        error_log( '[critical] Base de données indisponible, impossible d\'accéder à la base : '. $ria_db_connect[ $db_connect ]['base'] );
        if (php_sapi_name() !== 'cli') header( 'HTTP/1.1 503 Service Unavailable', true, 503 );
        die( 'Base de données indisponible' );
    }

    if( !@mysql_query( 'set names \'utf8\'', $link_identifier ) ){
        error_log( '[critical] Erreur sur la définition de l\'encodage' );
        if (php_sapi_name() !== 'cli') header( 'HTTP/1.1 503 Service Unavailable', true, 503 );
        die( 'Erreur sur la définition de l\'encodage' );
    }

    $ria_db_connect[ $db_connect ]['link_identifier'] = $link_identifier;
    return $link_identifier;
}

/** Surcharge pour la fonction mysql_query().
 *	@param string $sql Obligatoire requête SQL
 *	@param array $db_connect Optionnel, quelle configuration de connexion doit être utilisée (par défaut _DB_DEFAULT)
 *	@return bool False en cas d'erreur sinon le résultat de mysql_query()
 */
function ria_mysql_query($sql, $db_connect=false){
    global $ria_debug_timer, $ria_db_selected, $ar_debug_request_bdd;
    // Utilisation de $GLOBALS pour accéder à $ria_perf_log_data
    // car 'global $ria_perf_log_data;' ne fonctionnerait pas si $ria_perf_log_data est un array assigné directement dans $GLOBALS

    if (!defined('_DB_DEFAULT')) {
        $default_db_key = defined('_DB_RIASHOP') ? _DB_RIASHOP : 'default_connection_key_fallback';
        define('_DB_DEFAULT', $default_db_key);
    }

    if ($db_connect === false) {
        $db_connect = _DB_DEFAULT;
        if (isset($ria_db_selected) && $ria_db_selected !== false) {
            $db_connect = $ria_db_selected;
        }
    }

    // --- INITIALISATION DES DONNEES DE LOG DE PERFORMANCE POUR CET APPEL HTTP (si première requête) ---
    if ($GLOBALS['ria_perf_log_data']['request_url'] === null) {
        $GLOBALS['ria_perf_log_data']['page_call_time'] = isset($_SERVER['REQUEST_TIME_FLOAT']) ? $_SERVER['REQUEST_TIME_FLOAT'] : (isset($_SERVER['REQUEST_TIME']) ? (float)$_SERVER['REQUEST_TIME'] : microtime(true));

        $filename_base = 'unknown_url';
        if (php_sapi_name() === 'cli') {
            $cli_script_name = isset($_SERVER['SCRIPT_FILENAME']) ? $_SERVER['SCRIPT_FILENAME'] : 'unknown_script_cli';
            $GLOBALS['ria_perf_log_data']['request_url'] = 'CLI:' . $cli_script_name;
            $filename_base = preg_replace('/[^a-zA-Z0-9_-]/', '_', basename($cli_script_name));
        } else {
            $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http");
            $host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'unknown_host';

            // URL complète pour le log
            $full_request_uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '/unknown_uri';
            $GLOBALS['ria_perf_log_data']['request_url'] = $protocol . "://" . $host . $full_request_uri;

            // Nom de fichier basé sur host + path (sans query string)
            // Utiliser SCRIPT_NAME qui ne contient pas la query string.
            $path_for_filename = isset($_SERVER['SCRIPT_NAME']) ? $_SERVER['SCRIPT_NAME'] : '/unknown_path';
            $url_structure_for_filename = $host . $path_for_filename;
            $filename_base = md5($url_structure_for_filename);
        }
        $GLOBALS['ria_perf_log_data']['log_file_path'] = PERF_SQL_LOG_DIR . '/' . $filename_base . '.log';

        // S'assurer que le répertoire de log existe
        if (!is_dir(PERF_SQL_LOG_DIR)) {
            @mkdir(PERF_SQL_LOG_DIR, 0775, true); // Tenter de créer, @ pour masquer les erreurs de droits
        }
    }
    // --- FIN INITIALISATION LOG ---

    $link_identifier = ria_mysql_connection( $db_connect );

    if( $link_identifier === false ){
        // Log d'échec de connexion (pourrait aller dans le log d'erreur général)
        // Pour l'instant, on ne le met pas dans le fichier de perf par URL car il est spécifique à LA connexion
        $pageCallTimestampForError = isset($_SERVER['REQUEST_TIME_FLOAT']) ? $_SERVER['REQUEST_TIME_FLOAT'] : (isset($_SERVER['REQUEST_TIME']) ? (float)$_SERVER['REQUEST_TIME'] : microtime(true));
        $pageCallTimeFormattedForError = date('Y-m-d H:i:s.u', $pageCallTimestampForError);
        $requestUrlForError = $GLOBALS['ria_perf_log_data']['request_url'] ? $GLOBALS['ria_perf_log_data']['request_url'] : 'N/A';

        $log_message_fail_conn = sprintf(
            "[%s] URL: %s | DB_CONN_FAIL_FOR_KEY: %s | SQL_ATTEMPTED: %s",
            $pageCallTimeFormattedForError,
            $requestUrlForError,
            is_scalar($db_connect) ? (string)$db_connect : gettype($db_connect),
            preg_replace('/\s+/', ' ', trim($sql))
        );
        // Utiliser error_log standard pour ce type d'erreur critique
        error_log("RIA_MYSQL_QUERY_ERROR: " . $log_message_fail_conn);
        return false;
    }


    $start = microtime( true );
    $res_query = mysql_query( $sql, $link_identifier );
    $end = microtime( true );
    $executionTime = $end - $start;

    // Accumuler les données de performance
    $GLOBALS['ria_perf_log_data']['total_time'] += $executionTime;
    $GLOBALS['ria_perf_log_data']['query_count']++;

    // Enregistrer la fonction de shutdown une seule fois par requête HTTP
    if (!$GLOBALS['ria_perf_log_data']['shutdown_function_registered']) {
        register_shutdown_function('ria_log_total_query_time_on_shutdown');
        $GLOBALS['ria_perf_log_data']['shutdown_function_registered'] = true;
    }

    // --- Partie Debug et Erreur SQL (inchangée, logue toujours le SQL si activé/erreur) ---
    if (!function_exists('strtolower2')) { function strtolower2($string) { return strtolower($string);}}

    if( isset($_GET['Ria_show_SQL_Request']) && in_array($_GET['Ria_show_SQL_Request'], ['debug', 'show']) ){
        if( $_GET['Ria_show_SQL_Request'] == 'show' ){
            print '<br />'.number_format(round($executionTime, 6), 6, ',', '').';'.htmlspecialchars(trim($sql));
        }
        $md5_sql = md5( strtolower2(trim($sql)) );
        if( !isset($ar_debug_request_bdd[ $md5_sql ]) ){
            $ar_debug_request_bdd[ $md5_sql ] = ['SQL' => $sql,'min' => $executionTime,'max' => $executionTime,'sum' => $executionTime,'count' => 1];
        }else{
            if( $ar_debug_request_bdd[ $md5_sql ]['min'] > $executionTime ) $ar_debug_request_bdd[ $md5_sql ]['min'] = $executionTime;
            if( $ar_debug_request_bdd[ $md5_sql ]['max'] < $executionTime ) $ar_debug_request_bdd[ $md5_sql ]['max'] = $executionTime;
            $ar_debug_request_bdd[ $md5_sql ]['sum'] += $executionTime;
            $ar_debug_request_bdd[ $md5_sql ]['count']++;
        }
    }

    $debug_options = 0;
    if (version_compare(PHP_VERSION, '5.3.6', '>=')) { $debug_options = DEBUG_BACKTRACE_PROVIDE_OBJECT | DEBUG_BACKTRACE_IGNORE_ARGS; }
    elseif (version_compare(PHP_VERSION, '5.2.5', '>=')) { $debug_options = DEBUG_BACKTRACE_PROVIDE_OBJECT; }

    if( ($res_query === false) && !(preg_match("/Duplicate entry/", ria_mysql_error($db_connect))) ){
        error_log(
            "RIA_MYSQL_QUERY_ERROR: La requête SQL suivante à échoué :\n"
            .'======================================='."\n"
            .ria_mysql_error($db_connect)." :\n"
            .'======================================='."\n"
            .$sql."\n".
            '======================================='."\n"
            .'Contexte (backtrace) :'."\n"
            .print_r(debug_backtrace( $debug_options, 1 ), true)
        );
    }

    if (!function_exists('ria_debug_is_actived')) { function ria_debug_is_actived() { return false; } }
    if (!function_exists('ria_debug_page_excluded')) { function ria_debug_page_excluded() { return false; } }

    if (ria_debug_is_actived() && !ria_debug_page_excluded()) {
        // ... (log de session pour le debug, inchangé)
        if (!isset($_SESSION['ria_debug']) || !is_array($_SESSION['ria_debug'])) {
            $_SESSION['ria_debug'] = array( 'get' => array(), 'post' => array(), 'request' => array() );
        }
        $method = 'request';
        if (isset($_SERVER['REQUEST_METHOD']) && in_array($_SERVER['REQUEST_METHOD'], array('GET', 'POST'))) {
            $method = strtolower( $_SERVER['REQUEST_METHOD'] );
        }
        $script_url_key = isset($_SERVER['SCRIPT_URL']) ? $_SERVER['SCRIPT_URL'] : (isset($_SERVER['REQUEST_URI']) ? strtok($_SERVER['REQUEST_URI'], '?') : 'unknown_script_url');
        $ria_debug_timer_key = isset($ria_debug_timer) ? $ria_debug_timer : time();
        $_SESSION['ria_debug'][ $method ][ $script_url_key ][ $ria_debug_timer_key ][] = array(
            'time' => $executionTime, 'sql'  => $sql, 'server' => $_SERVER, 'backtrace' => debug_backtrace($debug_options),
        );
    }
    // --- Fin Partie Debug et Erreur SQL ---

    return $res_query;
}


/**
 * Fonction appelée à la fin du script pour logger le total des temps de requête.
 */
function ria_log_total_query_time_on_shutdown() {
    // Utilisation de $GLOBALS car cette fonction est appelée dans un contexte de shutdown
    if ($GLOBALS['ria_perf_log_data']['query_count'] > 0 && $GLOBALS['ria_perf_log_data']['log_file_path'] !== null) {

        // S'assurer que le répertoire de log existe encore (au cas où)
        $log_dir = dirname($GLOBALS['ria_perf_log_data']['log_file_path']);
        if (!is_dir($log_dir)) {
            if (!@mkdir($log_dir, 0775, true)) {
                error_log("RIA_MYSQL_PERF_LOG: Impossible de créer le répertoire de log: " . $log_dir);
                return; // Ne pas continuer si le répertoire ne peut être créé
            }
        }


        $pageCallTimeFormatted = date('Y-m-d H:i:s.u', $GLOBALS['ria_perf_log_data']['page_call_time']);

        $main_script_name = 'N/A';
        if (php_sapi_name() === 'cli') {
            $main_script_name = basename(isset($_SERVER['SCRIPT_FILENAME']) ? $_SERVER['SCRIPT_FILENAME'] : 'cli_script');
        } else {
            $main_script_name = basename(isset($_SERVER['SCRIPT_FILENAME']) ? $_SERVER['SCRIPT_FILENAME'] : 'web_script');
        }

        $log_message_summary = sprintf(
            "[%s] Page: %s | TotalQueryTime: %.6f s | QueryCount: %d | Script: %s",
            $pageCallTimeFormatted,
            $GLOBALS['ria_perf_log_data']['request_url'], // URL complète
            $GLOBALS['ria_perf_log_data']['total_time'],
            $GLOBALS['ria_perf_log_data']['query_count'],
            $main_script_name
        );

        @error_log($log_message_summary . PHP_EOL, 3, $GLOBALS['ria_perf_log_data']['log_file_path']);
    }
}

// ... (Le reste des fonctions ria_mysql_* : ria_mysql_control_ressource, ria_mysql_affected_rows, etc.
//      restent globalement les mêmes que dans la version PHP 5.6 précédente.
//      Assurez-vous que les définitions des constantes _DB_DEFAULT et _DB_RIASHOP
//      sont gérées correctement dans ces fonctions également, comme nous l'avions fait.)


// Exemple de comment les autres fonctions devraient gérer _DB_DEFAULT, etc.
// (Juste pour rappel, cette partie était déjà correcte dans la version précédente)

/** Cette fonction permet de vérifier qu'une ressource est bien un résultat MySQL
 *	@param resource $ressource Obligatoire, ressource à vérifier
 *	@return bool True s'il s'agit bien d'un résultat MySQL, False dans le cas contraire
 */
function ria_mysql_control_ressource( $ressource ){
    $res_type = is_resource($ressource) ? get_resource_type($ressource) : gettype($ressource);
    return (bool) strstr( $res_type, 'mysql' );
}

/** Cette fonction permet de surcharger la fonction mysql_affected_rows().
 *	@param array $db_connect Optionnel, quelle configuration de connexion doit être utilisée (par défaut _DB_DEFAULT)
 *	@return int Le résultat tel que retourné par mysql_affected_rows()
 */
function ria_mysql_affected_rows( $db_connect=false ){
    global $ria_db_selected;
    if (!defined('_DB_DEFAULT')) {
        $default_db_key = defined('_DB_RIASHOP') ? _DB_RIASHOP : 'default_connection_key_fallback';
        define('_DB_DEFAULT', $default_db_key);
    }
    if ($db_connect === false) {
        $db_connect = _DB_DEFAULT;
        if (isset($ria_db_selected) && $ria_db_selected !== false) $db_connect = $ria_db_selected;
    }
    $link_identifier = ria_mysql_connection( $db_connect );
    if ($link_identifier === false) return 0;
    return mysql_affected_rows( $link_identifier );
}

// ... et ainsi de suite pour les autres fonctions wrapper mysql_* ...
// (ria_mysql_data_seek, ria_mysql_errno, ria_mysql_error, etc.)
// Copiez le corps de ces fonctions depuis la version PHP 5.6 précédente que je vous ai fournie.
// Il est important que la logique de sélection de $db_connect et l'appel à ria_mysql_connection
// soient corrects dans chacune d'elles.


/** Cette fonction permet de surcharger la fonction mysql_data_seek(). // ... */
function ria_mysql_data_seek( $result, $row_number ){
    if (!ria_mysql_control_ressource($result)) return false;
    return mysql_data_seek( $result, $row_number );
}

/** Cette fonction permet de surcharger la fonction mysql_errno(). // ... */
function ria_mysql_errno( $db_connect=false ){
    global $ria_db_selected;
    if (!defined('_DB_DEFAULT')) { $default_db_key = defined('_DB_RIASHOP') ? _DB_RIASHOP : 'default_connection_key_fallback'; define('_DB_DEFAULT', $default_db_key); }
    if ($db_connect === false) { $db_connect = _DB_DEFAULT; if (isset($ria_db_selected) && $ria_db_selected !== false) $db_connect = $ria_db_selected; }
    $link_identifier = ria_mysql_connection( $db_connect );
    if ($link_identifier === false) return -1;
    return mysql_errno( $link_identifier );
}

/** Cette fonction permet de surcharger la fonction mysql_error(). // ... */
function ria_mysql_error( $db_connect=false ){
    global $ria_db_selected;
    if (!defined('_DB_DEFAULT')) { $default_db_key = defined('_DB_RIASHOP') ? _DB_RIASHOP : 'default_connection_key_fallback'; define('_DB_DEFAULT', $default_db_key); }
    if ($db_connect === false) { $db_connect = _DB_DEFAULT; if (isset($ria_db_selected) && $ria_db_selected !== false) $db_connect = $ria_db_selected; }
    $link_identifier = ria_mysql_connection( $db_connect );
    if ($link_identifier === false) return "Connection error (ria_mysql_error)";
    return mysql_error( $link_identifier );
}

/** Cette fonction permet de surcharger la fonction mysql_escape_string(). // ... */
function ria_mysql_escape_string( $unescaped_string ){
    if (function_exists('mysql_escape_string')) {
        // Pour utiliser mysql_real_escape_string, il faudrait une connexion.
        // Tentative d'obtenir une connexion par défaut si disponible.
        // Cela rend la fonction plus complexe si elle doit établir une connexion juste pour ça.
        // L'original ne passait pas de $link_identifier, ce qui est un problème pour mysql_real_escape_string.
        // On garde mysql_escape_string pour PHP 5.6 tant qu'elle existe, mais c'est déprécié.
        return mysql_escape_string( $unescaped_string );
    }
    return addslashes($unescaped_string);
}

/** Cette fonction permet de surcharger la fonction mysql_num_rows(). // ... */
function ria_mysql_num_rows( $result ){
    if (!ria_mysql_control_ressource($result)) return false;
    return mysql_num_rows( $result );
}

/** Cette fonction permet de surcharger la fonction mysql_fetch_array(). // ... */
function ria_mysql_fetch_array( $result, $result_type=MYSQL_BOTH ){
    // ... (logique de debug_backtrace pour les erreurs comme avant)
    if( !is_resource($result) || !ria_mysql_control_ressource($result) ){
        // Log error
        return false;
    }
    return mysql_fetch_array( $result, $result_type );
}

/** Cette fonction permet de surcharger la fonction mysql_fetch_assoc(). // ... */
function ria_mysql_fetch_assoc( $result ){
    if( $result === false || !ria_mysql_control_ressource($result) ){
        // Log error
        return false;
    }
    return mysql_fetch_assoc( $result );
}

/** Cette fonction permet de récupérer le résulat mysql sous forme de tableau // ... */
function ria_mysql_fetch_assoc_all( $result ){
    if ($result === false || !ria_mysql_control_ressource($result)) return array();
    $response = array();
    while( $r = ria_mysql_fetch_assoc($result) ) $response[] = $r;
    return $response;
}

/** Cette fonction permet de surcharger la fonction mysql_insert_id(). // ... */
function ria_mysql_insert_id( $db_connect=false ){
    global $ria_db_selected;
    if (!defined('_DB_DEFAULT')) { $default_db_key = defined('_DB_RIASHOP') ? _DB_RIASHOP : 'default_connection_key_fallback'; define('_DB_DEFAULT', $default_db_key); }
    if ($db_connect === false) { $db_connect = _DB_DEFAULT; if (isset($ria_db_selected) && $ria_db_selected !== false) $db_connect = $ria_db_selected; }
    $link_identifier = ria_mysql_connection( $db_connect );
    if ($link_identifier === false) return 0;

    $id_temp = mysql_insert_id( $link_identifier );
    if (!$id_temp) {
        try {
            $res_bdd_insert_id = mysql_query( "SELECT LAST_INSERT_ID()", $link_identifier);
            if ($res_bdd_insert_id && mysql_num_rows($res_bdd_insert_id) > 0) {
                $row_insert_id = mysql_fetch_row($res_bdd_insert_id);
                $id_temp = $row_insert_id[0];
                mysql_free_result($res_bdd_insert_id);
            } else {
                 if ($res_bdd_insert_id === false) {
                    error_log("RIA_MYSQL_ERROR: ria_mysql_insert_id fallback query failed: " . mysql_error($link_identifier));
                }
                $id_temp = 0;
            }
        }
        catch (Exception $e) {
             error_log("RIA_MYSQL_ERROR: Exception in ria_mysql_insert_id fallback: " . $e->getMessage());
             $id_temp = 0;
        }
    }
    return $id_temp;
}

/** Cette fonction permet de surcharger la fonction mysql_result(). // ... */
function ria_mysql_result( $result, $row, $field=0 ){
    if (!ria_mysql_control_ressource($result)) return false;
    return mysql_result( $result, $row, $field );
}

/** Cette fonction permet de surcharger la fonction mysql_fetch_row(). // ... */
function ria_mysql_fetch_row( $result ){
    if (!ria_mysql_control_ressource($result)) return false;
    return mysql_fetch_row( $result );
}

/** Cette fonction permet de surcharger la fonction mysql_field_name(). // ... */
function ria_mysql_field_name( $result, $field_offset ){
    if (!ria_mysql_control_ressource($result)) return false;
    return mysql_field_name( $result, $field_offset );
}

/** Cette fonction permet de surcharger la fonction mysql_field_table(). // ... */
function ria_mysql_field_table( $result, $field_offset ){
    if (!ria_mysql_control_ressource($result)) return false;
    return mysql_field_table( $result, $field_offset );
}

/** Cette fonction permet de surcharger la fonction mysql_num_fields(). // ... */
function ria_mysql_num_fields( $result ){
    if (!ria_mysql_control_ressource($result)) return false;
    return mysql_num_fields( $result );
}

/// @}

// \endcond