START TRANSACTION;

UPDATE prd_products AS pp
    JOIN img_images AS ii
ON ii.img_src_name = CONCAT(pp.prd_ref, '_V')
    SET pp.prd_img_id = ii.img_id
WHERE
    pp.prd_ref LIKE '%MPHM0%'
  AND pp.prd_ref NOT IN ('MPHM003', 'MPHM005');

INSERT INTO img_images_objects (    imo_img_id,
                                    imo_obj_id_0,
                                    imo_tnt_id,
                                    imo_cls_id,
                                    imo_type_id,
                                    imo_obj_id_1,
                                    imo_obj_id_2,
                                    imo_pos,
                                    imo_date_created,
                                    imo_date_modified,
                                    imo_date_deleted,
                                    imo_alt,
                                    imo_publish,
                                    imo_is_main
)
SELECT
    p.prd_img_id,
    p.prd_id,
    29 AS imo_tnt_id,
    1 AS imo_cls_id,
    0 AS imo_type_id,
    0 AS imo_obj_id_1,
    0 AS imo_obj_id_2,
    0 AS imo_pos,
    NOW() AS imo_date_created,
    NOW() AS imo_date_modified,
    NULL AS imo_date_deleted,
    NULL AS imo_alt,
    1 AS imo_publish,
    0 AS imo_is_main
FROM
    prd_products AS p
WHERE
    p.prd_img_id IS NOT NULL
  AND p.prd_ref LIKE '%MPHM0%'
  AND p.prd_ref NOT IN ('MPHM003', 'MPHM005')
  AND NOT EXISTS (
    SELECT 1
    FROM img_images_objects AS imo
    WHERE
        imo.imo_img_id = p.prd_img_id
      AND imo.imo_obj_id_0 = p.prd_id
);

COMMIT;