const webpack = require('webpack');
const { VueLoaderPlugin } = require('vue-loader');
const path = require('path');

module.exports = {
	mode: 'production',
	devtool: "source-map",
	entry: ['./src/js/app.js', './src/scss/app.scss'],
	watch: true,
	output: {
		filename: 'app.js',
		path: path.resolve(__dirname, './htdocs/src'),
	},
	module: {
		rules: [
			{
				test: /\.scss$/,
				use: [
					{
						loader: 'file-loader',
						options: {
							name: 'app.css',
						}
					},
					{
						loader: 'extract-loader'
					},
					{
						loader: 'css-loader?-url'
					},
					{
						loader: 'postcss-loader'
					},
					{
						loader: 'sass-loader'
					}
				]
			},
			{
				test: /\.vue$/,
				loader: 'vue-loader'
			},
			{
				test: /\.js$/,
				exclude: /node_modules/,
				loader: "babel-loader",
				query: {
					presets: ['@babel/preset-env']
				}
			}
		]
	},
	resolve: {
		extensions: ['.js', '.vue', '.json'],
		alias: {
			'vue$': 'vue/dist/vue.esm.js'

		}
	},
	plugins: [
		new VueLoaderPlugin(),

		new webpack.ProvidePlugin({
			Promise: ['es6-promise', 'Promise']
		}),
		new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
	],
	// {
	// 	test: /\.js$/,
	// 	exclude: /node_modules/,
	// 	loader: "babel-loader"
	//   },
};
