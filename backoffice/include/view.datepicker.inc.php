<?php

/** Cette fonction permet d'initier les variables de session permettant l'activation du sélecteur de périodes.
 * 	@return empty
 */
function view_admin_datepicker_init(){
	global $config;


	$datepicker_init = false;

	if( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] > 0 ){
		if( in_array($config['tnt_id'], [977, 998, 1043]) ){
			if( isset($_SESSION['admin_datepicker_init']) && trim($_SESSION['admin_datepicker_init']) != '' ){
				$datepicker_init = $_SESSION['admin_datepicker_init'];
			}
		}else{
			$datepicker_init = cfg_overrides_get_value( 'admin_datepicker_init', 0, $_SESSION['usr_id'] );
		}

		if( !$datepicker_init ){
			$datepicker_init = $config['admin_datepicker_init'];
		}

		$datepicker_init = json_decode( $datepicker_init, true );
	}

	if( !is_array($datepicker_init) || !ria_array_key_exists(['period', 'start', 'end'], $datepicker_init) ){
		$datepicker_init = [
			'period' => 'today',
			'start' => '',
			'end' => ''
		];
	}

	$init_dates = view_admin_datepicker_get_periods_dates();

	switch( $datepicker_init['period'] ){
		case 'today':
			$_SESSION['datepicker_period'] = _('Aujourd\'hui');
			$_SESSION['datepicker_date1'] = $init_dates['today'];
			$_SESSION['datepicker_date2'] = $init_dates['today'];
			break;
		case 'yesterday':
			$_SESSION['datepicker_period'] = _('Hier');
			$_SESSION['datepicker_date1'] = $init_dates['yesterday'];
			$_SESSION['datepicker_date2'] = $init_dates['yesterday'];
			break;
		case 'thisweek':
			$_SESSION['datepicker_period'] = _('Les 7 derniers jours');
			$_SESSION['datepicker_date1'] = $init_dates['thisweek'];
			$_SESSION['datepicker_date2'] = $init_dates['today'];
			break;
		case 'lastMonday':
			$_SESSION['datepicker_period'] = _('La semaine dernière');
			$_SESSION['datepicker_date1'] = $init_dates['lastMonday'];
			$_SESSION['datepicker_date2'] = $init_dates['lastSunday'];
			break;
		case 'last2Monday':
			$_SESSION['datepicker_period'] = _('Les 14 derniers jours');
			$_SESSION['datepicker_date1'] = $init_dates['last2Monday'];
			$_SESSION['datepicker_date2'] = $init_dates['today'];
			break;
		case 'last30days':
			$_SESSION['datepicker_period'] = _('Les 30 derniers jours');
			$_SESSION['datepicker_date1'] = $init_dates['last30days'];
			$_SESSION['datepicker_date2'] = $init_dates['today'];
			break;
		case 'dmonth':
			$_SESSION['datepicker_period'] = _('Ce mois-ci');
			$_SESSION['datepicker_date1'] = $init_dates['dmonth'];
			$_SESSION['datepicker_date2'] = $init_dates['emonth'];
			break;
		case 'dlastmonth':
			$_SESSION['datepicker_period'] = _('Le mois dernier');
			$_SESSION['datepicker_date1'] = $init_dates['dlastmonth'];
			$_SESSION['datepicker_date2'] = $init_dates['elastmonth'];
			break;
		case 'firstjanuary':
			$_SESSION['datepicker_period'] = _('Depuis le 1er janvier');
			$_SESSION['datepicker_date1'] = $init_dates['firstjanuary'];
			$_SESSION['datepicker_date2'] = $init_dates['efirstjanuary'];
			break;
		case 'lastyear':
			$_SESSION['datepicker_period'] = _('L\'année dernière');
			$_SESSION['datepicker_date1'] = $init_dates['lastyear'];
			$_SESSION['datepicker_date2'] = $init_dates['elastyear'];
			break;
		case 'all':
			$_SESSION['datepicker_period'] = _('Toute la période');
			$_SESSION['datepicker_date1'] = $init_dates['all'];
			$_SESSION['datepicker_date2'] = $init_dates['today'];
			break;
		case 'perso':
			$_SESSION['datepicker_period'] = _('Période personnalisée');
			$_SESSION['datepicker_date1'] = $datepicker_init['start'];
			$_SESSION['datepicker_date2'] = $datepicker_init['end'];
			break;
	}
}

/** Cette fonction initialise les dates pour les différentes périodes sur sélecteur de périodes.
 *  Elle est commun à toutes les fonctions traitant du même sujet.
 *  @return Un tableau contenant :
 */
function view_admin_datepicker_get_periods_dates(){
  global $config;

  { // Détermine la date pour "Toute la période" en fonction de la date de création du tenant et celle de la plus vieille commande
		$date_created = tnt_tenants_get_date_created( $config['tnt_id'] );
		if( !isdateheure($date_created) ){
			$date_created = new Datetime();
			$date_created->modify( '-3 years' );
		}else{
			$date_created = new DateTime( $date_created );
		}

		$oldest = ord_orders_get_oldest();
		if( isdateheure($oldest) ){
			$oldest = new DateTime( $oldest );
			if( $oldest < $date_created ){
				$date_created = $oldest;
			}
		}
	}


  $ar_datepicker_init = [
    'all'           => $date_created->format('d/m/Y'),
    'today'         => (new DateTime())->format('d/m/Y'),
    'yesterday'     => (new DateTime())->modify('-1 day')->format('d/m/Y'),
    'thisweek'      => (new DateTime())->modify('-7 days')->format('d/m/Y'),
    'lastMonday'    => (new DateTime())->modify('-2 Monday')->format('d/m/Y'),
    'lastSunday'    => (new DateTime())->modify('last Sunday')->format('d/m/Y'),
    'last2Monday'   => (new DateTime())->modify('-14 day')->format('d/m/Y'),
    'last30days'    => (new DateTime())->modify('-30 day')->format('d/m/Y'),
    'dmonth'        => (new DateTime())->modify('first day of this month')->format('d/m/Y'),
    'emonth'        => (new DateTime())->modify('last day of this month')->format('d/m/Y'),
    'dlastmonth'    => (new DateTime())->modify('first day of previous month')->format('d/m/Y'),
    'elastmonth'    => (new DateTime())->modify('last day of previous month')->format('d/m/Y'),
    'firstjanuary'  => (new DateTime())->format('01/01/Y'),
    'efirstjanuary' => (new DateTime())->format('31/12/Y'),
    'lastyear'      => (new DateTime())->modify('last year')->format('01/01/Y'),
    'elastyear'     => (new DateTime())->modify('last year')->format('31/12/Y')
  ];

  return $ar_datepicker_init;
}

/** Cette fonction permet d'initialiser les variables destinées à la gestion des périodes (dates de début et de fin).
 *	Les périodes sont utilisées pour filtrer les informations dans le back-office.
 *	@param int $volume Optionnel, nombre de résultats
 *	@param string $return Optionnel, url de retour (@deprecated N'est plus prit en compte)
 *	@param array $graph Optionnel, ce paramètre accepte seulement un tableau des identifiants des tableaux de graph
 *	@param array $param Optionnel, paramètre à mettre à true pour l'initialisation : array('id'=>true);
 */
function view_date_initialized( $volume=0, $return='', $graph=false, $param=array() ){
	global $config;

  $init_dates = view_admin_datepicker_get_periods_dates();

	// Variable pour la mise en place des périodes
	$website = wst_websites_get();

	print '	var date_all1 = "'.$init_dates['all'].'";'."\n";
	print ' var date_today = "'.$init_dates['today'].'";'."\n";
	print ' var date_yesterday = "'.$init_dates['yesterday'].'";'."\n";
	print ' var date_thisweek = "'.$init_dates['thisweek'].'";'."\n";
	print ' var date_lastMonday = "'.$init_dates['lastMonday'].'";'."\n";
	print ' var date_lastSunday = "'.$init_dates['lastSunday'].'";'."\n";
	print ' var date_last2Monday = "'.$init_dates['last2Monday'].'";'."\n";
	print ' var date_last30days = "'.$init_dates['last30days'].'";'."\n";
	print ' var date_dmonth = "'.$init_dates['dmonth'].'";'."\n";
	print ' var date_emonth = "'.$init_dates['emonth'].'";'."\n";
	print ' var date_dlastmonth = "'.$init_dates['dlastmonth'].'";'."\n";
	print ' var date_elastmonth = "'.$init_dates['elastmonth'].'";'."\n";
	print ' var date_firstjanuary = "'.$init_dates['firstjanuary'].'";'."\n";
	print ' var date_lastyear = "'.$init_dates['lastyear'].'";'."\n";
	print ' var date_elastyear = "'.$init_dates['elastyear'].'";'."\n";

	$init_view = '';
	if(isset($_SESSION['datepicker_date1'], $_SESSION['datepicker_date2']) && $_SESSION['datepicker_date1']!=$_SESSION['datepicker_date2']){
		$init_view = 'du %s au %s';
		$init_view = sprintf($init_view, dateformat($_SESSION['datepicker_date1']), dateformat($_SESSION['datepicker_date2']));
	} elseif( isset($_SESSION['datepicker_date1'], $_SESSION['datepicker_date2']) ){
		$init_view = dateformat( $_SESSION['datepicker_date1'] );
	} else {
		$init_view = dateformat( date('d/m/Y') );
	}

	$init_date = '';
	if( isset($_SESSION['datepicker_date1'], $_SESSION['datepicker_date2']) && $_SESSION['datepicker_date1']!=$_SESSION['datepicker_date2'] ){
		$init_date = '[\''.$_SESSION['datepicker_date1'].'\',\''.$_SESSION['datepicker_date2'].'\']';
	}elseif( isset($_SESSION['datepicker_date1'], $_SESSION['datepicker_date2']) ){
		$init_date = '\''.$_SESSION['datepicker_date1'].'\'';
	}else {
		$init_date = '\''.date('d/m/Y').'\'';
	}

	print ' var init_view = \''.addslashes( $init_view ).'\';'."\n";
	print ' var init_action = \''.( isset($_SESSION['datepicker_period']) ? addslashes($_SESSION['datepicker_period']) : 'Aujourd\\\'hui' ).'\';'."\n";
	print ' var init_date = '.$init_date.';'."\n";

	// Variable permettant de définir si la page se charge toute seule apres le changement de date si celle ci est a faux le script va creer des champ hidden
	print ' var autoload = '.( isset($param['autoload']) && $param['autoload'] ? 'true' : 'false' ).';'."\n";
	print ' var autorefresh = '.( isset($param['autorefresh']) && $param['autorefresh'] ? 'true' : 'false' ).';'."\n";
	print ' var nb_show_orders = '.($volume>0 ? $volume : 0).';'."\n";
	print '	var nb_show_search = 0;'."\n";
	print '	var refresh_in_ajax = '.( isset($param['refresh_in_ajax']) && $param['refresh_in_ajax'] ? 'true' : 'false' ).';'."\n";

	// Variable permettant d'afficher la popup de nouvelle commande
	print '	var load_popup_new_orders = '.( isset($param['load_popup_new_orders']) && !$param['load_popup_new_orders'] ? 'false' : 'true' ).';'."\n";

	print '	var callback_riadatepcker = '.( isset($param['callback']) && $param['callback'] ? $param['callback'] : 'function(){}' ).';'."\n";

	if( is_array($graph) && sizeof($graph) ){
		print '	var refresh_graph_in_ajax = new Array(\''.implode( '\', \'', $graph ).'\');'."\n";
	}else{
		print ' var refresh_graph_in_ajax = false;'."\n";
	}

	print ' var websites = new Array();'."\n";
	if( $website && ria_mysql_num_rows($website)>1 ){
		print '		autoload = false;'."\n";
		while( $tw = ria_mysql_fetch_array($website) ){
			print '		websites['.$tw['id'].'] = \''.htmlspecialchars( str_replace('\'', "\'", $tw['name']) ).'\';'."\n";
		}
	}
}

/** Met en session la période sélectionnée (pour tout l'espace d'administration)
 *	@param $date1 Obligatoire, date de début
 *	@param $date2 Obligatoire, date de fin
 *	@param int $wst Optionnel, identifiant du site
 */
function view_date_in_session( $date1, $date2, $wst=false ){
	global $config;

	$period = '';
	$date1 = date( 'd/m/Y', strtotime(dateparse( $date1 )) );
	$date2 = date( 'd/m/Y', strtotime(dateparse( $date2 )) );

	$_SESSION['datepicker_date1'] = $date1;
	$_SESSION['datepicker_date2'] = $date2;
	$_SESSION['websitepicker'] = $wst!==false ? $wst : (isset($_SESSION['websitepicker']) && is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker'] ? $_SESSION['websitepicker'] : 0);

	$init_dates = view_admin_datepicker_get_periods_dates();

	if( $date1 == $date2 && $date1 == $init_dates['today']){
		$period = 'today';
		$_SESSION['datepicker_period'] = _('Aujourd\'hui');
	}elseif( $date1 == $date2 && $date1 == $init_dates['yesterday'] ){
		$period = 'yesterday';
		$_SESSION['datepicker_period'] = _('Hier');
	}elseif( $date2 == $init_dates['today'] && $date1 == $init_dates['thisweek'] ){
		$period = 'thisweek';
		$_SESSION['datepicker_period'] = _('Les 7 derniers jours');
	}elseif( $date1 == $init_dates['lastMonday'] && $date2 == $init_dates['lastSunday'] ){
		$period = 'lastMonday';
		$_SESSION['datepicker_period'] = _('La semaine dernière');
	}elseif( $date1 == $init_dates['last2Monday'] && $date2 == $init_dates['today'] ){
		$period = 'last2Monday';
		$_SESSION['datepicker_period'] = _('Les 14 derniers jours');
	}elseif( $date1 == $init_dates['last30days'] && $date2 == $init_dates['today'] ){
		$period = 'last30days';
		$_SESSION['datepicker_period'] = _('Les 30 derniers jours');
	}elseif( $date1 == $init_dates['dmonth'] && $date2 == $init_dates['emonth'] ){
		$period = 'dmonth';
		$_SESSION['datepicker_period'] = _('Ce mois-ci');
	}elseif( $date1 == $init_dates['dlastmonth'] && $date2 == $init_dates['elastmonth'] ){
		$period = 'dlastmonth';
		$_SESSION['datepicker_period'] = _('Le mois dernier');
	}elseif( $date1 == $init_dates['firstjanuary'] && $date2 == $init_dates['today'] ){
		$period = 'firstjanuary';
		$_SESSION['datepicker_period'] = _('Depuis le 1er janvier');
	}elseif( $date1 == $init_dates['lastyear'] && $date2 == $init_dates['elastyear'] ){
		$period = 'lastyear';
		$_SESSION['datepicker_period'] = _('L\'année dernière');
	}elseif( $date1 == $init_dates['all'] && $date2 == $init_dates['today'] ){
		$period = 'all';
		$_SESSION['datepicker_period'] = _('Toute la période');
	}else{
		$period = 'perso';
		$_SESSION['datepicker_period'] = _('Période personnalisée');
	}

	$json_config_session = json_encode([
		'period' 	=> $period,
		'start' 	=> $period == 'perso' ? $date1 : '',
		'end' 		=> $period == 'perso' ? $date2 : '',
	]);

	// Sauvegarde la configuration en base pour l'utilisateur
	if( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] > 0 ){
		cfg_overrides_set_value( 'admin_datepicker_init', $json_config_session, 0, $_SESSION['usr_id'] );
		$_SESSION['admin_datepicker_init'] = $json_config_session;
	}
}