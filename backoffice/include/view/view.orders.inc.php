<?php
  { // Partie dédiée à la gestion des colonnes
    /** Cette fonction permet de charger les colonnes actives dans le listing des commandes.
     *  Elle est chargée par une valeur par défaut puis ensuite selon la personnalisation apportée par l'administrateur connecté.
     *  @return array Un tableau contenant les codes des colonnes.
     */
    function view_admin_orders_load_cols_actived(){
      global $config;

      $ar_cols_actived = [];

      if( isset($config['admin_orders_cols_actived']) && is_array($config['admin_orders_cols_actived']) && count($config['admin_orders_cols_actived']) ){
        $ar_cols_actived = $config['admin_orders_cols_actived'];
      }

      // Charge la personnalisation du compte administrateur
      $get_perso = cfg_overrides_get_value( 'admin_orders_cols_actived', 0, $_SESSION['usr_id'] );
      if( trim($get_perso) != '' ){
        $ar_cols_actived = explode( ', ', $get_perso );
      }


      // TODO : Si une colonne activée n'existe plus alors celle-ci est automatiquement retirée de la configuration

      return $ar_cols_actived;
    }

    /** Cette fonction permet de charger l'ordre des colonnes précédement choisi dans le listing des commandes.
     *  @return array Un tableau contenant les codes des colonnes (la clé sert de position).
     */
    function view_admin_orders_load_cols_position(){
      global $config;

      $ar_cols_position = [];

      // Charge la personnalisation du compte administrateur
      $get_perso = cfg_overrides_get_value( 'admin_orders_cols_position', 0, $_SESSION['usr_id'] );
      if( trim($get_perso) != '' ){
        $ar_cols_position = explode( ',', $get_perso );
      }

      return $ar_cols_position;
    }

    $ar_cols_actived = view_admin_orders_load_cols_actived();

    /** Cette fonction permet de charger les colonnes utilisables sur l'interface des commandes.
     *  @return array Un tableau contenant les colonnes et pour chacune :
     *      - colonne_name : colonne_description
     *      - colonne_name : colonne_description
     *      - colonne_name : colonne_description
     *      - colonne_name : colonne_description
     */
    function view_admin_orders_get_columns( $sort=false ){
      $ar_cols_position = view_admin_orders_load_cols_position();
      $start_pos = count($ar_cols_position) + 1;

      // Colonnes par défaut
      $ar_cols = [
        [
          'id' => 'id',
          'name' => _('Identifiant RiaShop'),
          'type' => 'base',
          'alias' => 'id',
          'format' => FLD_TYPE_INT,
          'width' => '100px',
          'group_id' => 0,
          'group' => _('Commande'),
          'position' => in_array('id', $ar_cols_position) ? array_search('id', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'piece',
          'name' => _('Référence ERP'),
          'type' => 'base',
          'alias' => 'piece',
          'format' => FLD_TYPE_TEXT,
          'width' => '100px',
          'group_id' => 0,
          'group' => _('Commande'),
          'position' => in_array('piece', $ar_cols_position) ? array_search('piece', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'ref',
          'name' => _('Référence'),
          'type' => 'base',
          'alias' => 'ref',
          'format' => FLD_TYPE_TEXT,
          'width' => '100px',
          'group_id' => 0,
          'group' => _('Commande'),
          'position' => in_array('ref', $ar_cols_position) ? array_search('ref', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'date',
          'name' => _('Date'),
          'type' => 'base',
          'alias' => 'date',
          'format' => FLD_TYPE_DATE,
          'width' => '150px',
          'group_id' => 0,
          'group' => _('Commande'),
          'position' => in_array('date', $ar_cols_position) ? array_search('date', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'ord_user',
          'name' => _('Client'),
          'type' => 'user',
          'alias' => '',
          'format' => FLD_TYPE_TEXTAREA,
          'width' => '200px',
          'group_id' => 0,
          'group' => _('Commande'),
          'position' => in_array('ord_user', $ar_cols_position) ? array_search('ord_user', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'adr_inv',
          'name' => _('Adresse de facturation'),
          'type' => 'adr',
          'alias' => 'invoice',
          'format' => FLD_TYPE_TEXTAREA,
          'width' => '200px',
          'group_id' => 0,
          'group' => _('Commande'),
          'position' => in_array('adr_inv', $ar_cols_position) ? array_search('adr_inv', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'adr_dlv',
          'name' => _('Adresse de livraison'),
          'type' => 'adr',
          'alias' => 'delivery',
          'format' => FLD_TYPE_TEXTAREA,
          'width' => '200px',
          'group_id' => 0,
          'group' => _('Commande'),
          'position' => in_array('adr_dlv', $ar_cols_position) ? array_search('adr_dlv', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'pay_name',
          'name' => _('Moyen de paiement'),
          'type' => 'payment',
          'format' => FLD_TYPE_TEXTAREA,
          'width' => '150px',
          'group_id' => 0,
          'group' => _('Commande'),
          'position' => in_array('pay_name', $ar_cols_position) ? array_search('pay_name', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'srv_name',
          'name' => _('Service de livraison'),
          'type' => 'service',
          'format' => FLD_TYPE_TEXT,
          'width' => '150px',
          'group_id' => 0,
          'group' => _('Commande'),
          'position' => in_array('srv_name', $ar_cols_position) ? array_search('srv_name', $ar_cols_position) : ($start_pos++),
        ],
        'stateord' => [
          'id' => 'state',
          'name' => _('Etat'),
          'type' => 'base',
          'alias' => 'state_name',
          'format' => FLD_TYPE_TEXT,
          'width' => '200px',
          'group_id' => 0,
          'group' => _('Commande'),
          'position' => in_array('state', $ar_cols_position) ? array_search('state', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'store',
          'name' => _('Magasin'),
          'type' => 'store',
          'alias' => '',
          'format' => FLD_TYPE_TEXTAREA,
          'width' => '200px',
          'group_id' => 0,
          'group' => _('Commande'),
          'position' => in_array('store', $ar_cols_position) ? array_search('store', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'total_ht',
          'name' => _('Total H.T.'),
          'type' => 'price',
          'format' => FLD_TYPE_FLOAT,
          'width' => '100px',
          'group_id' => 0,
          'group' => _('Commande'),
          'position' => in_array('total_ht', $ar_cols_position) ? array_search('total_ht', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'total_ttc',
          'name' => _('Total T.T.C'),
          'type' => 'price',
          'format' => FLD_TYPE_FLOAT,
          'width' => '100px',
          'group_id' => 0,
          'group' => _('Commande'),
          'position' => in_array('total_ttc', $ar_cols_position) ? array_search('total_ttc', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'lineord_ref',
          'name' => _('Référence des articles'),
          'type' => 'product',
          'alias' => 'ref',
          'format' => FLD_TYPE_TEXT,
          'width' => '100px',
          'group_id' => 1,
          'group' => _('Ligne de commande'),
          'position' => in_array('lineord_ref', $ar_cols_position) ? array_search('lineord_ref', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'lineord_name',
          'name' => _('Désignation des articles'),
          'type' => 'product',
          'alias' => 'name',
          'format' => FLD_TYPE_TEXT,
          'width' => '300px',
          'group_id' => 1,
          'group' => _('Ligne de commande'),
          'position' => in_array('lineord_name', $ar_cols_position) ? array_search('lineord_name', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'lineord_priceht',
          'name' => _('Prix unitaire des articles'),
          'type' => 'product',
          'alias' => 'price_ht',
          'format' => FLD_TYPE_FLOAT,
          'width' => '100px',
          'group_id' => 1,
          'group' => _('Ligne de commande'),
          'position' => in_array('lineord_priceht', $ar_cols_position) ? array_search('lineord_priceht', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'lineord_qte',
          'name' => _('Quantité des articles'),
          'type' => 'product',
          'alias' => 'qte',
          'format' => FLD_TYPE_FLOAT,
          'width' => '100px',
          'group_id' => 1,
          'group' => _('Ligne de commande'),
          'position' => in_array('lineord_qte', $ar_cols_position) ? array_search('lineord_qte', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'lineord_state',
          'name' => _('Etat des articles'),
          'type' => 'product',
          'alias' => 'state_id',
          'format' => FLD_TYPE_TEXT,
          'width' => '200px',
          'group_id' => 1,
          'group' => _('Ligne de commande'),
          'position' => in_array('lineord_state', $ar_cols_position) ? array_search('lineord_state', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'lineord_depot',
          'name' => _('Dépôt des articles'),
          'type' => 'product',
          'alias' => 'line_dps_id',
          'format' => FLD_TYPE_TEXT,
          'width' => '200px',
          'group_id' => 1,
          'group' => _('Ligne de commande'),
          'position' => in_array('lineord_depot', $ar_cols_position) ? array_search('lineord_depot', $ar_cols_position) : ($start_pos++),
        ],
      ];

      if( gu_user_is_authorized('_RGH_ADMIN_ORDER_USE_STATE_LINE') ){
        // Si le statut est géré au niveau de la ligne alors la colonne "Etat" global n'est pas accessible
        unset( $ar_cols['stateord'] );
      }

      // Ajout des colonnes liées aux champs avancés qu'ils soient sur l'entête de commande ou la ligne de commande
      $r_field = fld_fields_get( 0, 0, -2, 0, 0, 0, null, [], false, [], null, [CLS_ORDER, CLS_ORD_PRODUCT], null, false, null, false );
      if( $r_field ){
        while( $field = ria_mysql_fetch_assoc($r_field) ){
          $key = 'fld-';
          $group_id = 1;
          $group = _('Informations complémentaire sur les commandes');

          if( $field['cls_id'] == CLS_ORD_PRODUCT ){
            $key = 'fld-line-';
            $group_id = 3;
            $group = _('Informations complémentaire sur les lignes de commande');
          }

          array_push( $ar_cols, [
            'id' => $key.$field['id'],
            'name' => $field['name'],
            'type' => 'field',
            'format' => $field['type_id'],
            'width' => '100px',
            'group_id' => $group_id,
            'group' => $group,
            'position' => in_array($key.$field['id'], $ar_cols_position) ? array_search($key.$field['id'], $ar_cols_position) : ($start_pos++),
          ] );
        }
      }

      if( $sort === false ){
        $sort = ['position' => SORT_ASC, 'group_id' => SORT_ASC, 'name' => SORT_ASC];
      }

      $ar_cols = array_msort( $ar_cols, $sort );

      return $ar_cols;
    }

    /** Cette fonction permet de déterminer si une colonne est lié à un champ avancé sur l'entête de commande.
     *  @return bool true si s'est le cas, false dans le cas contraire
     */
    function view_admin_orders_has_column_field(){
      global $ar_cols_actived;

      $include_fld = false;

      foreach( $ar_cols_actived as $one_col ){
        if( strstr($one_col, 'fld-') && !strstr($one_col, 'fld-line-') ){
          $include_fld = true;
          break;
        }
      }

      return $include_fld;
    }

    /** Cette fonction permet de déterminer si une colonne est lié à un champ avancé sur une ligne de commande.
     *  @return bool true si s'est le cas, false dans le cas contraire
     */
    function view_admin_orders_has_column_linefield(){
      global $ar_cols_actived;

      $include_fld_line = false;

      foreach( $ar_cols_actived as $one_col ){
        if( strstr($one_col, 'fld-line-') ){
          $include_fld_line = true;
          break;
        }
      }

      return $include_fld_line;
    }

    /** Cette fonction permet de déterminer si une colonne est lié une des adresses de la commande.
     *  @return bool true si s'est le cas, false dans le cas contraire
     */
    function view_admin_orders_has_column_address(){
      global $ar_cols_actived;

      $include_address = false;

      foreach( $ar_cols_actived as $one_col ){
        if( in_array($one_col, ['adr_inv', 'adr_dlv', 'user', 'store']) ){
          $include_address = true;
          break;
        }
      }

      return $include_address;
    }

    /** Cette fonction permet de déterminer si une colonne est lié à un des totaux de la commande.
     *  @return bool true si s'est le cas, false dans le cas contraire
     */
    function view_admin_orders_has_column_totals(){
      global $ar_cols_actived;

      $include_totals = false;

      foreach( $ar_cols_actived as $one_col ){
        if( in_array($one_col, ['total_ht', 'total_ttc']) ){
          $include_totals = true;
          break;
        }
      }

      return $include_totals;
    }

    /** Cette fonction permet de déterminer si une colonne est lié aux status de commande ou ligne de commande.
     *  @return bool true si s'est le cas, false dans le cas contraire
     */
    function view_admin_orders_has_column_states(){
      global $ar_cols_actived;

      $include_states = false;

      foreach( $ar_cols_actived as $one_col ){
        if( in_array($one_col, ['lineord_state']) ){
          $include_states = true;
          break;
        }
      }

      return $include_states;
    }

    /** Cette fonction permet de déterminer si une colonne est lié aux dépôts sur la commande ou ligne de commande.
     *  @return bool true si s'est le cas, false dans le cas contraire
     */
    function view_admin_orders_has_column_deposits(){
      global $ar_cols_actived;

      $include_depot = false;

      foreach( $ar_cols_actived as $one_col ){
        if( in_array($one_col, ['lineord_depot']) ){
          $include_depot = true;
          break;
        }
      }

      return $include_depot;
    }

    /** Cette fonction permet de déterminer si une colonne est lié aux services de livraison sur la commande.
     *  @return bool true si s'est le cas, false dans le cas contraire
     */
    function view_admin_orders_has_column_services(){
      global $ar_cols_actived;

      $include_service = false;

      foreach( $ar_cols_actived as $one_col ){
        if( in_array($one_col, ['srv_name']) ){
          $include_service = true;
          break;
        }
      }

      return $include_service;
    }

    /** Cette fonction permet de déterminer si une colonne est lié aux moyens de paiement
     *  @return bool true si s'est le cas, false dans le cas contraire
     */
    function view_admin_orders_has_column_payments(){
      global $ar_cols_actived;

      $include_payment = false;

      foreach( $ar_cols_actived as $one_col ){
        if( in_array($one_col, ['pay_name']) ){
          $include_payment = true;
          break;
        }
      }

      return $include_payment;
    }

    /** Cette fonction permet de déterminer si une colonne est lié à aux lignes de commandes.
     *  @return bool true si s'est le cas, false dans le cas contraire
     */
    function view_admin_orders_has_column_products(){
      global $ar_cols_actived;

      $include_products = false;

      foreach( $ar_cols_actived as $one_col ){
        if( strstr($one_col, 'lineord_') ){
          $include_products = true;
          break;
        }
      }

      return $include_products;
    }

    /** Cette fonction permet de récupérer tous les identifiants de champs avancés pouvant être utilisés comme colonne.
     *  @return array Un tableau des identifiants
     */
    function view_admin_orders_get_field_ids(){
      global $ar_cols_actived;

      $ar_fld_ids = [];

      foreach( $ar_cols_actived as $one_col ){
        if( strstr($one_col, 'fld-line-') || strstr($one_col, 'fld-') ){
          $ar_fld_ids[] = str_replace( ['fld-line-', 'fld-'], '', $one_col );
        }
      }

      return $ar_fld_ids;
    }
  }

  { // Partie dédiée à la gestion des filtres
    /** Cette fonction permet de charger les filtres actives dans le listing des commandes.
     *  Elle est chargée par une valeur par défaut puis ensuite selon la personnalisation apportée par l'administrateur connecté.
     *  @return array Un tableau contenant les codes des colonnes.
     */
    function view_admin_orders_load_filters_actived(){
      global $config;

      $ar_filters_actived = [];

      if( isset($config['admin_orders_filters_actived']) && is_array($config['admin_orders_filters_actived']) && count($config['admin_orders_filters_actived']) ){
        $ar_filters_actived = $config['admin_orders_filters_actived'];
      }

      // Charge la personnalisation du compte administrateur
      $get_perso = cfg_overrides_get_value( 'admin_orders_filters_actived', 0, $_SESSION['usr_id'] );
      if( trim($get_perso) != '' ){
        $ar_filters_actived = explode( ', ', $get_perso );
      }

      return $ar_filters_actived;
    }

    $ar_filters_actived =  view_admin_orders_load_filters_actived();

    /** Cette fonction permet de charger les filtres à activer sur le listing des commandes
     *  Seul le sélecteur de période est toujours présent.
     *  @param int|array $state Obligatoire, identifiant ou tableau d'identifiant de statut
     *  @param array $dates Obligatoire, dates de restriction des commandes avec deux clés obligatoire (start => date de début, end => date de fin)
     *  @param bool $load_values Optionnel, détermine si les valeurs sont chargées en même temps que les filtres (par défaut à false)
     *  @return Un tableau contenant pour chaque filtre :
     *    -
     */
    function view_admin_orders_get_filters( $state, $dates, $load_values=false ){
      global $config;

      $ar_filters = [
        [
          'id' => 'origin',
          'name' => 'Origine de la commande',
          'allname' => 'Tout le traffic',
          'limit_for_show' => 0,
          'type' => 'static'
        ],
        [
          'id' => 'seller',
          'name' => 'Représentant',
          'allname' => 'Tous les représentant',
          'function' => 'gu_users_get',
          'args' => [0, '', '', PRF_SELLER],
          'cols' => [
            'id' => 'id',
            'value' => ['adr_firstname', 'adr_lastname', 'society']
          ],
          'values' => [],
          'limit_for_show' => 1,
          'use_for_search' => 'id',
          'position' => 0
        ],
        [
          'id' => 'customer',
          'name' => 'Clients',
          'allname' => 'Tous les clients',
          'function' => 'gu_users_get',
          'args' => [0, '', '', [PRF_CUSTOMER, PRF_CUST_PRO]],
          'cols' => [
            'id' => 'id',
            'value' => ['ref', 'adr_firstname', 'adr_lastname', 'society']
          ],
          'values' => [],
          'limit_for_show' => 1,
          'use_for_search' => 'id',
          'position' => 0
        ],
        [
          'id' => 'website',
          'name' => 'Sites web',
          'allname' => 'Tous les sites',
          'function' => 'wst_websites_get',
          'args' => [],
          'cols' => [
            'id' => 'id',
            'value' => ['name']
          ],
          'values' => [],
          'limit_for_show' => 2,
          'use_for_search' => 'id',
          'position' => 1
        ],
        [
          'id' => 'payment',
          'name' => 'Moyens de paiement',
          'allname' => 'Tous les moyens',
          'function' => 'ord_payment_types_get',
          'args' => [],
          'cols' => [
            'id' => 'id',
            'value' => ['name']
          ],
          'values' => [],
          'limit_for_show' => 1,
          'use_for_search' => 'id',
          'position' => 2
        ],
        [
          'id' => 'deposit',
          'name' => 'Dépôts',
          'allname' => 'Tous les dépôts',
          'function' => 'prd_deposits_get',
          'args' => [],
          'cols' => [
            'id' => 'id',
            'value' => ['name'],
          ],
          'values' => [],
          'limit_for_show' => 1,
          'use_for_search' => 'id',
          'position' => 3
        ],
        [
          'id' => 'store',
          'name' => 'Magasins',
          'allname' => 'Tous les magasins',
          'function' => 'dlv_stores_get',
          'args' => [],
          'cols' => [
            'id' => 'id',
            'value' => ['name'],
          ],
          'values' => [],
          'limit_for_show' => 1,
          'use_for_search' => 'id',
          'position' => 3
        ]
      ];

      // Ajout du filtre sur le statut à la ligne selon si le droit d'accès est coché ou non
      if( gu_user_is_authorized('_RGH_ADMIN_ORDER_USE_STATE_LINE') ){
        $ar_filters[] = [
          'id' => 'linestate',
          'name' => 'Etat des lignes de commande',
          'allname' => 'Tous les états',
            'function' => 'ord_states_get',
          'args' => [],
          'cols' => [
            'id' => 'id',
            'value' => ['name']
          ],
          'values' => [],
          'limit_for_show' => 1,
          'use_for_search' => 'id',
          'position' => 0
        ];
      }

      if( !in_array($config['tnt_id'], [977, 998, 1043]) ){
        unset($ar_filters[2]); // Suppression du filtre "Clients" hormis pour Conforama
      }

      // Ajout des filtres liés aux champs avancés qu'ils soient sur l'entête de commande ou la ligne de commande
      $r_field = fld_fields_get( 0, 0, -2, 0, 0, 0, null, [], false, [], null, [CLS_ORDER, CLS_ORD_PRODUCT], null, false, null, false );
      if( $r_field ){
        while( $field = ria_mysql_fetch_assoc($r_field) ){
          $key = 'fld-';
          // $group_id = 1;
          // $group = _('Informations complémentaire sur les commandes');

          if( $field['cls_id'] == CLS_ORD_PRODUCT ){
            $key = 'fld-line-';
            // $group_id = 3;
            // $group = _('Informations complémentaire sur les lignes de commande');
          }

          array_push( $ar_filters, [
            'id' => $key.$field['id'],
            'name' => $field['name'],
            'allname' => 'Aucune restriction',
            'type' => 'field',
            'values' => [],
            'limit_for_show' => 1,
            'use_for_search' => 'value',
            'position' => 100
          ]);
        }
      }

      if( $load_values ){
        view_admin_orders_load_filters_values( $ar_filters, $state, $dates );
      }

      $ar_filters = array_msort( $ar_filters, ['position' => SORT_ASC, 'name' => SORT_ASC] );
      return $ar_filters;
    }

    /** Cette fonction permet de charger les filtres à activer sur le listing des commandes
     *  Seul le sélecteur de période est toujours présent.
     *  @param array $ar_filters Obligatoire, tableau des filtres (tel que retourné par view_admin_orders_get_filters), ce paramètre est mis à jour par la fonction
     *  @param int|array $state Obligatoire, identifiant ou tableau d'identifiant de statut
     *  @param array $dates Obligatoire, dates de restriction des commandes avec deux clés obligatoire (start => date de début, end => date de fin)
     *  @return bool Retourne toujours true
     */
    function view_admin_orders_load_filters_values( &$ar_filters, $state, $dates ){
      global $config;

      $r_data_fields = null;

      $state = control_array_integer( $state, true );
      if( $state === false ){
        return false;
      }

      if( !ria_array_key_exists(['start', 'end'], $dates) ){
        return false;
      }

      foreach( ['start', 'end'] as $key ){
        $dates[ $key ] = dateparse( $dates[ $key ] );
        if( !isdate($dates[ $key ]) ){
          return false;
        }
      }

      foreach( $ar_filters as $key_filter => $one_filter ){
        if( isset($one_filter['type']) && $one_filter['type'] == 'static' ){
          continue;
        }

        // Chargement des données lors que celle proviennent d'une fonction moteur
        if( isset($one_filter['function']) ){
          if( !isset($one_filter['args']) ){
            $one_filter['args'] = [];
          }

          $res = call_user_func_array( $one_filter['function'], $one_filter['args'] );
          if( $res ){
            while( $r = ria_mysql_fetch_assoc($res) ){
              if( $one_filter['function'] == 'ord_states_get' ){
                $has_status = isset($config['admin_visible_order_statuses']) && is_array($config['admin_visible_order_statuses']) && !empty($config['admin_visible_order_statuses']);
                if ( $has_status && !in_array($r[ $one_filter['cols']['id'] ], $config['admin_visible_order_statuses']) ) {
                  continue;
                }
              }
              $value = '';

              foreach( $one_filter['cols']['value'] as $alias ){
                $value .= ' '.$r[ $alias ];
              }

              $ar_filters[ $key_filter ]['values'][] = [
                'id' => $r[ $one_filter['cols']['id'] ],
                'value' => trim( $value )
              ];
            }
          }
        }

        // Chargement des données lorsque celle-ci proviennent d'un champ avancé
        elseif( isset($one_filter['type']) && $one_filter['type'] == 'field' ){
          $fld_id = str_replace( ['fld-line-', 'fld-'], '', $one_filter['id'] );

          if( $r_data_fields === null ){
            // Chargement des valeurs des champs avancés liés aux commandes ou entête de commande,
            // la requête n'est fait qu'une seule fois
            $sql = '
              select distinct pv_fld_id, pv_value, fld_type_id
              from fld_object_values
                join fld_fields on ( (fld_tnt_id = 0 or fld_tnt_id = '.$config['tnt_id'].') and fld_id = pv_fld_id )
                  join ord_orders on (ord_tnt_id = '.$config['tnt_id'].' and ord_id = pv_obj_id_0)
              where pv_tnt_id = '.$config['tnt_id'].'
                and fld_cls_id in ('.CLS_ORDER.', '.CLS_ORD_PRODUCT.')
                  and ord_state_id in ('.implode( ', ', $state ).')
                  and date(ord_date) >= "'.$dates['start'].'"
                  and date(ord_date) <= "'.$dates['end'].'"
            ';

            $r_data_fields = ria_mysql_query( $sql );
          }

          if( $r_data_fields && ria_mysql_num_rows($r_data_fields) ){
            while( $data_fields = ria_mysql_fetch_assoc($r_data_fields) ){
              if( $data_fields['pv_fld_id'] != $fld_id ){
                continue;
              }

              if( trim($data_fields['pv_value']) == '' ){
                continue;
              }

              if( !is_numeric($data_fields['pv_value']) ){
                $res = json_decode($data_fields['pv_value']);
                if( json_last_error() === JSON_ERROR_NONE ){
                  continue;
                }
              }

              $ar_vals = explode( ', ', $data_fields['pv_value'] );
              foreach( $ar_vals as $one_val ){
                $id = md5( $one_val );

                $ar_filters[ $key_filter ]['values'][ $id ] = [
                  'id' => $id,
                  'value' => $one_val
                ];
              }
            }

            ria_mysql_data_seek( $r_data_fields, 0 );
          }
        }

        if( is_array($ar_filters[ $key_filter ]['values']) && count($ar_filters[ $key_filter ]['values']) > 0 ){
          $ar_filters[ $key_filter ]['values'] = array_msort( $ar_filters[ $key_filter ]['values'], ['value' => SORT_ASC] );
        }
      }

      return true;
    }

    /** Cette fonction permet de récupérer les filtres sur les champs avancés qui sont activés.
     *  @return array Un tableau contenant les IDs des champs avancés
     */
    function view_admin_orders_get_filters_field_ids(){
      global $ar_filters_actived;

      $ar_fld_ids = [];

      foreach( $ar_filters_actived as $one_col ){
        if( strstr($one_col, 'fld-line-') || strstr($one_col, 'fld-') ){
          $ar_fld_ids[] = str_replace( ['fld-line-', 'fld-'], '', $one_col );
        }
      }

      return $ar_fld_ids;
    }
  }

  { // Partie dédiée aux lignes de commande
    /** Cette fonction permet de charger les colonnes actives dans le listing des lignes de commande.
     *  Elle est chargée par une valeur par défaut puis ensuite selon la personnalisation apportée par l'administrateur connecté.
     *  @return array Un tableau contenant les codes des colonnes.
     */
    function view_admin_ord_products_load_cols_actived(){
      global $config;

      $ar_cols_actived = [];

      if( isset($config['admin_ord_products_cols_actived']) && is_array($config['admin_ord_products_cols_actived']) && count($config['admin_ord_products_cols_actived']) ){
        $ar_cols_actived = $config['admin_ord_products_cols_actived'];
      }

      // Charge la personnalisation du compte administrateur
      $get_perso = cfg_overrides_get_value( 'admin_ord_products_cols_actived', 0, $_SESSION['usr_id'] );
      if( trim($get_perso) != '' ){
        $ar_cols_actived = explode( ', ', $get_perso );
      }

      return $ar_cols_actived;
    }

    /** Cette fonction permet de charger l'ordre des colonnes précédement choisi dans le listing des commandes.
     *  @return array Un tableau contenant les codes des colonnes (la clé sert de position).
     */
    function view_admin_ord_products_load_cols_position(){
      global $config;

      // return explode(',', 'date,id,piece,adr_inv,adr_dlv,pay_name,total_ht,total_ttc,fld-line-114143');

      $ar_cols_position = [];

      // Charge la personnalisation du compte administrateur
      $get_perso = cfg_overrides_get_value( 'admin_ord_products_cols_position', 0, $_SESSION['usr_id'] );
      if( trim($get_perso) != '' ){
        $ar_cols_position = explode( ',', $get_perso );
      }

      return $ar_cols_position;
    }

    $ar_cols_ord_products_actived = view_admin_ord_products_load_cols_actived();

    /** Cette fonction permet de charger les colonnes utilisables sur l'interface des lignes de commande.
     *  @return array Un tableau contenant les colonnes et pour chacune :
     *      - colonne_name : colonne_description
     *      - colonne_name : colonne_description
     *      - colonne_name : colonne_description
     *      - colonne_name : colonne_description
     */
    function view_admin_ord_products_get_columns( $sort=false ){
      $ar_cols_position = view_admin_ord_products_load_cols_position();
      $start_pos = count($ar_cols_position) + 1;

      // Colonnes par défaut
      $ar_cols = [
        [
          'id' => 'ref',
          'name' => _('Référence'),
          'type' => 'base',
          'alias' => 'ref',
          'format' => FLD_TYPE_TEXT,
          'width' => '100px',
          'group_id' => 0,
          'group' => _('Produit'),
          'position' => in_array('ref', $ar_cols_position) ? array_search('ref', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'name',
          'name' => _('Désignation (69 caractères max...)'),
          'type' => 'base',
          'alias' => 'name',
          'format' => FLD_TYPE_TEXT,
          'width' => '300px',
          'group_id' => 0,
          'group' => _('Produit'),
          'position' => in_array('name', $ar_cols_position) ? array_search('name', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'notes',
          'name' => _('Commentaire'),
          'type' => 'base',
          'alias' => 'notes',
          'format' => FLD_TYPE_TEXTAREA,
          'width' => '300px',
          'group_id' => 0,
          'group' => _('Produit'),
          'position' => in_array('notes', $ar_cols_position) ? array_search('notes', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'priceht',
          'name' => _('Prix Unitaire'),
          'type' => 'base',
          'alias' => 'price_ht',
          'format' => FLD_TYPE_FLOAT,
          'width' => '100px',
          'group_id' => 0,
          'group' => _('Produit'),
          'position' => in_array('priceht', $ar_cols_position) ? array_search('priceht', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'qte',
          'name' => _('Quantité'),
          'type' => 'base',
          'alias' => 'qte',
          'format' => FLD_TYPE_FLOAT,
          'width' => '100px',
          'group_id' => 0,
          'group' => _('Produit'),
          'position' => in_array('qte', $ar_cols_position) ? array_search('qte', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'discount',
          'name' => _('Remise'),
          'type' => 'base',
          'alias' => 'discount',
          'format' => FLD_TYPE_FLOAT,
          'width' => '100px',
          'group_id' => 0,
          'group' => _('Produit'),
          'position' => in_array('discount', $ar_cols_position) ? array_search('discount', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'totalht',
          'name' => _('Total'),
          'type' => 'base',
          'alias' => 'total_ht',
          'format' => FLD_TYPE_FLOAT,
          'width' => '100px',
          'group_id' => 0,
          'group' => _('Produit'),
          'position' => in_array('totalht', $ar_cols_position) ? array_search('totalht', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'state',
          'name' => _('Etat'),
          'type' => 'base',
          'alias' => 'state_line',
          'format' => FLD_TYPE_INT,
          'width' => '200px',
          'group_id' => 0,
          'group' => _('Produit'),
          'position' => in_array('state', $ar_cols_position) ? array_search('state', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'datelivr',
          'name' => _('Date de livraison'),
          'type' => 'base',
          'alias' => 'date-livr',
          'format' => FLD_TYPE_DATE,
          'width' => '150px',
          'group_id' => 0,
          'group' => _('Produit'),
          'position' => in_array('datelivr', $ar_cols_position) ? array_search('datelivr', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'dpsid',
          'name' => _('Dépôt'),
          'type' => 'base',
          'alias' => 'line_dps_id',
          'format' => FLD_TYPE_INT,
          'width' => '200px',
          'group_id' => 0,
          'group' => _('Produit'),
          'position' => in_array('dpsid', $ar_cols_position) ? array_search('dpsid', $ar_cols_position) : ($start_pos++),
        ],
        [
          'id' => 'purchase_avg',
          'name' => _('Prix d\'achat'),
          'type' => 'base',
          'alias' => 'purchase_avg',
          'format' => FLD_TYPE_FLOAT,
          'width' => '100px',
          'group_id' => 0,
          'group' => _('Produit'),
          'position' => in_array('purchase_avg', $ar_cols_position) ? array_search('purchase_avg', $ar_cols_position) : ($start_pos++),
        ],
      ];

      // Ajout des colonnes liées aux champs avancés qu'ils soient sur l'entête de commande ou la ligne de commande
      $r_field = fld_fields_get( 0, 0, -2, 0, 0, 0, null, [], false, [], null, [CLS_ORD_PRODUCT], null, false, null, false );
      if( $r_field ){
        while( $field = ria_mysql_fetch_assoc($r_field) ){
          $key = 'fld-';
          $group_id = 1;
          $group = _('Informations complémentaire sur les commandes');

          if( $field['cls_id'] == CLS_ORD_PRODUCT ){
            $key = 'fld-line-';
            $group_id = 3;
            $group = _('Informations complémentaire sur les lignes de commande');
          }

          array_push( $ar_cols, [
            'id' => $key.$field['id'],
            'name' => $field['name'],
            'type' => 'field',
            'format' => $field['type_id'],
            'width' => '100px',
            'group_id' => $group_id,
            'group' => $group,
            'position' => in_array($key.$field['id'], $ar_cols_position) ? array_search($key.$field['id'], $ar_cols_position) : ($start_pos++),
          ] );
        }
      }

      if( $sort === false ){
        $sort = ['position' => SORT_ASC, 'group_id' => SORT_ASC, 'name' => SORT_ASC];
      }

      $ar_cols = array_msort( $ar_cols, $sort );

      return $ar_cols;
    }

    /** Cette fonction permet de déterminer si une colonne est lié à un champ avancé sur les lignes de commande.
     *  @return bool true si s'est le cas, false dans le cas contraire
     */
    function view_admin_ord_products_has_column_field(){
      global $ar_cols_ord_products_actived;

      $include_fld = false;

      foreach( $ar_cols_ord_products_actived as $one_col ){
        if( strstr($one_col, 'fld-') || strstr($one_col, 'fld-line-') ){
          $include_fld = true;
          break;
        }
      }

      return $include_fld;
    }

    /** Cette fonction permet de récupérer tous les identifiants de champs avancés pouvant être utilisés comme colonne sur le listing des lignes de
     *  commade.
     *  @return array Un tableau des identifiants
     */
    function view_admin_ord_products_get_field_ids(){
      global $ar_cols_ord_products_actived;

      $ar_fld_ids = [];

      foreach( $ar_cols_ord_products_actived as $one_col ){
        if( strstr($one_col, 'fld-line-') || strstr($one_col, 'fld-') ){
          $ar_fld_ids[] = str_replace( ['fld-line-', 'fld-'], '', $one_col );
        }
      }

      return $ar_fld_ids;
    }

    /** Cette fonction permet de déterminer si une colonne est lié aux status à ligne de commande.
     *  @return bool true si s'est le cas, false dans le cas contraire
     */
    function view_admin_ord_products_has_column_states(){
      global $ar_cols_ord_products_actived;

      $include_states = false;

      foreach( $ar_cols_ord_products_actived as $one_col ){
        if( in_array($one_col, ['state']) ){
          $include_states = true;
          break;
        }
      }

      return $include_states;
    }

    /** Cette fonction permet de déterminer si une colonne est lié aux dépôts.
     *  @return bool true si s'est le cas, false dans le cas contraire
     */
    function view_admin_ord_products_has_column_deposit(){
      global $ar_cols_ord_products_actived;

      $include_states = false;

      foreach( $ar_cols_ord_products_actived as $one_col ){
        if( in_array($one_col, ['dpsid']) ){
          $include_states = true;
          break;
        }
      }

      return $include_states;
    }
  }