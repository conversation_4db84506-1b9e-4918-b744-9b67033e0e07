<?php

require_once 'view/admin/JStorage.inc.php';

/**	\brief Cette classe gère les items de la popup "Créer" auquel l'utilisateur en cours à le droit d'accéder en les stockant dans JStorage
 *
 */
final class PopupCreate
{
	private $items = [];

	private static $instance = null;

	// Constructeur
	private function __construct() {
		$this->createItems();
	}

	/**
	 * Cette méthode permet d'instancier la popup create (Singleton)
	 *
	 * @return object L'objet en cours
	 */
	public static function getInstance()
	{
		if (!self::$instance) {
			self::$instance = new PopupCreate();
		}
		return self::$instance;
	}

	/**
	 * Cette fonction permet d'ajouter les éléments en fonction des droits de l'utilisateur
	 */
	private function createItems() {
		require_once('rights.inc.php');

		$items = [];

		if( gu_user_is_authorized('_RGH_ADMIN_FDV_REPORT_CALL_ADD') ){

			$items[] = array(
				'name' => _('Rapport d\'appel téléphonique'),
				'desc' => _('Rédiger le compte-rendu d\'un appel téléphonique avec un prospect ou un client.'),
				'url' => _('/admin/fdv/reports/calls/create.php'),
				'class' => _('icon-flux-incoming-call')
			);

		}

		if( gu_user_is_authorized('_RGH_ADMIN_FDV_REPORT_VISIT_ADD') ){

			$items[] = array(
				'name' => _('Rapport de visite'),
				'desc' => _('Rédiger le compte-rendu d\'un rendez-vous avec un prospect ou un client.'),
				'url' => _('/admin/fdv/reports/edit.php'),
				'class' => _('icon-flux-report')
			);

		}
		if( gu_user_is_authorized('_RGH_ADMIN_ORDER_CREATE') ){

			$items[] = array(
				'name' => _('Devis / Commande'),
				'desc' => _('Créer une nouvelle proposition commerciale.'),
				'url' => _('/admin/orders/order.php?ord=new'),
				'class' => _('icon-flux-sell')
			);
		}
		if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_RIGHT_ADD') ){

			$items[] = array(
				'name' => _('Produit'),
				'desc' => _('Intégrer un nouveau produit à votre catalogue.'),
				'url' => _('/admin/catalog/product.php'),
				'class' => _('icon-menu-catalog')
			);
		}
		if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD') ){

			$items[] = array(
				'name' => _('Compte client'),
				'desc' => _('Créer un nouveau compte client ou prospect.'),
				'url' => _('/admin/customers/new.php?prf='),
				'class' => _('icon-menu-account')
			);
		}
		if( gu_user_is_authorized('_RGH_ADMIN_PROMO_PRD_ADD') ){

			$items[] = array(
				'name' => _('Promotions sur les produits'),
				'desc' => _('Créer une réduction tarifaire sur un ou plusieurs produits pendant une période donnée.'),
				'url' => _('/admin/promotions/index.php'),
				'class' => _('icon-menu-marketing')
			);
		}
		if( gu_user_is_authorized('_RGH_ADMIN_TOOL_IMPORT_ADD') ){

			$items[] = array(
				'name' => _('Importer'),
				'desc' => _('Ajouter, actualiser ou supprimer des données par lot.'),
				'url' => _('/admin/tools/imports/index.php'),
				'class' => _('icon-menu-import')
			);
		}

		$this->items = $items;
		// Permet de passer les données de la popup create à JavaScript
		JStorage::getInstance()->add('createpopup', [
			'items'		=> $this->items,
		]);
	}
}
