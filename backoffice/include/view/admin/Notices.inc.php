<?php

/** Cette classe permet de récupérer des informations sur les abonnements Yuto et RiaShop
 * et permet également l'affichage de messages d'échéances, etc.
 */
class AdminNotices
{

	private static $instance = null; ///< L'instance

	private $access = null; ///< HTML de l'avertissement dans le cas où l'on peut accéder à plusieurs instances et que l'on en a choisi une

	private $yuto_end_5days = false; ///< Initialise une variable pour dire que l'abonnement Yuto (essai) se termine dans 5 jours au plus tard

	private $yuto = null; ///< HTML

	private $riashop_end_5days = false; ///< Initialise une variable pour dire que l'abonnement RiaShop (essai) se termine dans 5 jours au plus tard

	private $riashop = null; ///< HTML

	private $trial = false;

	private $expired = false;

	public function __construct()
	{
		if (gu_user_is_authorized('_RGH_ADMIN_OPTION_SUBSCRIPTION')) {
			$abo_yuto = dev_subscribtions_yuto_get(false, false);

			if (is_array($abo_yuto) && count($abo_yuto)) {
				// Si oui ou non il existe un abonnement payant
				$this->trial = isset($abo_yuto['in_testing']) && $abo_yuto['in_testing'];
			} else {
				$this->expired = true;
			}
		}

		$this->alertAccess()
			->yutoSubscription()
			->riashopSubscription();
	}

	/** Cette méthode permet d'instancier AdminNotices (Singleton)
	 * @return	object	L'objet en cours
	 */
	public static function getInstance()
	{

		if (!self::$instance) {
			self::$instance = new AdminNotices();
		}
		return self::$instance;
	}

	/**	Récupére la valeur de la variable riashop_end_5days
	 * @return	float|int|bool
	 */
	public function getRiashopEnd()
	{
		return $this->riashop_end_5days;
	}

	/**	Récupére la valeur de la variable yuto_end_5days
	 * @return	float|int|bool
	 */
	public function getYutoEnd()
	{
		return $this->yuto_end_5days;
	}

	/**	Affichage d'un avertissement dans le cas où l'on peut accéder à plusieurs instances et que l'on en a choisi une
	 * @return	string	HTML de l'avertissement
	 */
	public function renderAlertAccess()
	{
		return is_string($this->access) ? $this->access : '';
	}

	/**	Affichage d'une notice dans le cas où l'on est sur un abonnement Yuto qui se termine dans 5 jours ou moins
	 * @return	string	HTML de la notice
	 */
	public function renderAlertYutoSubscription()
	{
		return is_string($this->yuto) ? $this->yuto : '';
	}

	/**	Affichage d'une notice dans le cas où l'on est sur un abonnement BtoB qui se termine dans 5 jours ou moins
	 * @return	string	HTML de la notice
	 */
	public function renderAlertRiashopSubscription()
	{
		return is_string($this->riashop) ? $this->riashop : '';
	}

	/**	Si l'utilisateur n'a pas encore installé Yuto, affichage d'une popup d'incitation à l'installation
	 * @return	string	HTML de la notice
	 */
	public function renderQuickstart()
	{
		if (
			!$this->yuto_end_5days && !$this->riashop_end_5days // Ce message n'apparaît plus si les alertes de fin de période d'essai sont activées
			&& $this->trial && isset($_SESSION['usr_tnt_id']) // Il faut être en période d'essai et connecté
			&& $_SESSION['usr_tnt_id'] != 0 && !gu_user_have_yuto_installed() // Il ne faut pas être super-administrateur et ne pas avoir Yuto Installé
			&& $_SERVER['PHP_SELF'] != '/fdv/devices/setup-guide.php' // Et ne pas se trouver sur la page du guide d'installation qui est la cible du lien
		) {
			return '
				<div id="accesstenant" class="notice notice-header">
					C\'est parti avec Yuto ! Première étape : installer l\'application Yuto sur votre appareil mobile.
					<a href="/fdv/devices/setup-guide.php">Suivez le guide !</a>
				</div>
			';
		}
		return '';
	}

	/**	Initialise les variables et l'affichage d'un avertissement dans le cas où l'on peut accéder à plusieurs instances et que l'on en a choisi une
	 * @return	object	L'objet en cours
	 */
	private function alertAccess()
	{
		global $admin_account;

		if (getenv('oneriashop') === false) {
			return $this;
		}
		$tnt_access = $admin_account->getAccess();

		if (isset($_SESSION['change_tenant_is_closed']) && $_SESSION['change_tenant_is_closed']) {
			return $this;
		}

		if ($tnt_access !== 'all' && !(is_array($tnt_access) && count($tnt_access) > 1)) {
			return $this;
		}
		$current_tnt = $admin_account->getTenantSelected();

		if (!is_numeric($current_tnt) || !$current_tnt) {
			return $this;
		}

		// Récupère le dernier abonnement en date et détermine s'il est terminé
		$sub = dev_subscribtions_yuto_get(true);
		$date_end = new Datetime(dateparse($sub['date_end']));

		if ($date_end->getTimestamp() < strtotime(date('Y-m-d'))) {
			$this->expired = true;
		}

		// Affiche un warning si les crons sont désactivés
		$monitoring = new Monitoring();
		$crons_activated = $monitoring->getCronRules($current_tnt)['activated'];

		$this->access = '
			<div id="accesstenant" class="notice notice-header "' . ($sub && $this->expired ? ' style="background-color: #FC5A5A"' : (!$crons_activated ? ' style="background-color: #ee9b1b" title="Les tâches planifiées sont désactivées (!crons_activated dans le datastore)"' : '')) . '>'
				. _('Vous êtes actuellement sur l\'instance de')
				. ' <strong>' . tnt_tenants_get_name($current_tnt) . '</strong>. '
				. ($sub && $this->expired ? 'La période d\'essai ou d\'abonnement de ce locataire est terminée. ' : '') .
				'<br>
				<a href="#" onclick="return showPopupSelectTenant(false);">' . _('Changer d\'instance') . '</a>
				<input type="button" class="input-icon-del" id="close_accesstenant"/>
			</div>
		';

		return $this;
	}

	/**	Initialise les variables et l'affichage d'une notice dans le cas où l'on est sur un abonnement Yuto qui se termine dans 5 jours ou moins
	 * @return	object	L'objet en cours
	 */
	private function yutoSubscription()
	{
		global $config;

		if (!RegisterGCP::onGCloud() || !gu_user_is_authorized('_RGH_ADMIN_OPTION_SUBSCRIPTION')) {
			return $this;
		}
		$sub = dev_subscribtions_yuto_get();

		if (!is_array($sub) || !count($sub) || !isset($sub['in_testing']) || !$sub['in_testing']) {
			return $this;
		}
		// Recherche un abonnement future
		$tmp_sub_fut = dev_subscribtions_yuto_get(false, true);

		if (is_array($tmp_sub_fut) && count($tmp_sub_fut)) {
			return $this;
		}
		// Contrôle que la date de fin est bien dans 5 jours au maximum
		$date_end_before = new Datetime(dateparse($sub['date_end']));
		$date_end_before->modify('-5 days');

		if (time() > $date_end_before->getTimestamp()) {
			// Calcul le nombre de jours restant entre aujourd'hui et la date de fin de l'abonnement
			$d1 = new Datetime(dateparse($sub['date_end']) . ' 23:59:59');
			$d2 = new Datetime();
			$d2 = new Datetime($d2->format('Y-m-d') . ' 00:00:00');
			$interval = $d1->diff($d2);
			$modulo = (int) $interval->format('%a') + 1;

			// Récupère le type d'abonnement
			$package = ucfirst(RegisterGCP::getPackage($config['tnt_id']));

			if ($modulo > 1) {
				$alert = str_replace(array('#param[jours restants]#', '#param[package]#', '#param[date de fin]#'), array($modulo, $package, $d1->format('d/m/Y')), _('Votre période d\'essai Yuto #param[package]# se termine dans #param[jours restants]# jours. Après le #param[date de fin]#, votre compte sera fermé.'));
			} else {
				$alert = str_replace(array('#param[package]#'), array($package), _('Votre période d\'essai Yuto #param[package]# se termine aujourd\'hui. Demain, votre compte sera fermé.'));
			}

			$this->yuto = '
				<div id="accesstenant">
					' . $alert . '
					<a href="#" onclick="displayPopup(\'' . str_replace('#param[package]#', $package, _('Activation de mon abonnement Yuto #param[package]#')) . '\', \'\', \'/admin/options/popup-reactivate-subscription.php?activation=1\', \'\');return false;"><strong>' . str_replace('#param[package]#', $package, _('J\'active mon abonnement Yuto #param[package]#')) . '</strong></a>
				</div>
			';

			$this->yuto_end_5days = $modulo;
		}
		return $this;
	}

	/**	Initialise les variables et l'affichage d'une notice dans le cas où l'on est sur un abonnement BtoB qui se termine dans 5 jours ou moins
	 * @return	object	L'objet en cours
	 */
	private function riashopSubscription()
	{
		global $config;

		if (!RegisterGCP::onGCloud() || !gu_user_is_authorized('_RGH_ADMIN_OPTION_SUBSCRIPTION_BTOB')) {
			return $this;
		}
		// Charge l'abonnement actuel
		$sub = dev_subscribtions_btob_get();

		// Si cet abonnement est soumis à une période de test
		if (!is_array($sub) || !count($sub) || !isset($sub['in_testing']) || !$sub['in_testing']) {
			return $this;
		}
		// Recherche un abonnement future
		$tmp_sub_fut = dev_subscribtions_btob_get(false, true);

		// Si aucun abonnement future
		if (is_array($tmp_sub_fut) && count($tmp_sub_fut)) {
			return $this;
		}
		// Contrôle que la date de fin est bien dans 5 jours au maximum
		$date_end_before = new Datetime(dateparse($sub['date_end']));
		$date_end_before->modify('-5 days');

		if (time() > $date_end_before->getTimestamp()) {
			// Calcul le nombre de jours restant entre aujourd'hui et la date de fin de l'abonnement
			$d1 = new Datetime(dateparse($sub['date_end']) . ' 23:59:59');
			$d2 = new Datetime();
			$d2 = new Datetime($d2->format('Y-m-d') . ' 00:00:00');
			$interval = $d1->diff($d2);
			$modulo = (int) $interval->format('%a') + 1;

			// Récupère le type d'abonnement
			$package = ucfirst(RegisterGCP::getPackageBtoB($config['tnt_id']));

			if ($modulo > 1) {
				$alert = str_replace(array('#param[jours restants]#', '#param[package]#', '#param[date de fin]#'), array($modulo, $package, $d1->format('d/m/Y')), _('Votre période d\'essai RiaShop #param[package]# se termine dans #param[jours restants]# jours. Après le #param[date de fin]#, votre compte sera fermé.'));
			} else {
				$alert = str_replace(array('#param[package]#'), array($package), _('Votre période d\'essai RiaShop #param[package]# se termine aujourd\'hui. Demain, votre compte sera fermé.'));
			}

			$this->riashop = '
				<div id="accesstenant">
					' . $alert . '
					<a href="#" onclick="displayPopup(\'' . str_replace('#param[package]#', $package, _('Activation de mon abonnement RiaShop #param[package]#')) . '\', \'\', \'/admin/options/popup-riashop-subscriptions.php?activation=1\', \'\');return false;"><strong>' . str_replace('#param[package]#', $package, _('J\'active mon abonnement RiaShop #param[package]#')) . '</strong></a>
				</div>
			';

			$this->riashop_end_5days = $modulo;
		}
		return $this;
	}
}
