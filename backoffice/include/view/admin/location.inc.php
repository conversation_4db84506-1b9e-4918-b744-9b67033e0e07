<?php

/**	\defgroup breadcrumbs Fil d'ariane
 *
 * 	Ce module comprend les fonctions nécessaires à la gestion du fil d'Ariane permettant aux utilisateurs de se repérer dans le back-office.
 *  @{
 */

/** Affiche la position en cours de l'utilisateur dans l'interface d'administration
 *	Cette fonction ne retourne rien, elle se contente d'envoyer vers la sortie standard le code HTML correspondant au fil d'Ariane.
 *	C'est la seule fonction de ce module utilisable directement dans le back-office, les autres servent uniquement de soutien.
 *	@return void
 *	@deprecated Merci de ne plus mettre à jour cette fonction et d'utiliser la classe Breadcrumbs en remplacement
 */
function view_admin_location(){

 	// Rajout de "/admin" en début du PHP_SELF pour gérer l'admin mutualisée + l'admin normale
	$php_self = view_admin_current_page_url();

	if( $php_self != '/admin/index.php' ){
		print '<a href="/admin/index.php">'._('Accueil').'</a> &bull; ';
	}

	if( strstr($php_self,'/admin/catalog/') ){
		view_admin_location_catalog();
	}elseif( strstr($php_self, '/admin/orders/') ){
		view_admin_location_orders();
	}elseif( strstr($php_self, '/admin/comparators/') ){
		view_admin_location_comparators();
	}elseif( strstr($php_self, '/admin/fdv/') ){
		view_admin_location_yuto();
	}elseif( strstr($php_self,'/admin/tools/') ){ // Outils
		view_admin_location_tools();
	}elseif( strstr($php_self,'/admin/config/') ){ // Configuration
		view_admin_location_configuration();
	}

}

/**	Cette fonction gère la compatibilité entre l'ancien emplacement des back-offices
 * 	(domaine-client/admin/) et le nouveau système (app.riashop.fr).
 * 	Cette fonction ne sera plus nécessaire une fois tous les clients migrés sur app.riashop.fr
 * 	@return string l'url de la page en cours, dans l'ancien système d'url
 * 	\private
 */
function view_admin_current_page_url(){

	// Rajout de "/admin" en début du PHP_SELF pour gérer l'admin mutualisée + l'admin normale
	$php_self = $_SERVER['PHP_SELF'];
	if( substr($php_self, 0, 6) != '/admin' ){
		$php_self = '/admin'.$php_self;
	}

	return $php_self;
}

/**	Gère le fil d'ariane sur la page de modification de compte client
 *
 */
function view_admin_location_set_edit_customer_breadcrumbs(){

	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Comptes clients'), '/admin/customers/index.php' );

	if (isset($_GET['seg']) && is_numeric($_GET['seg']) && $_GET['seg']) {
		$r_seg = seg_segments_get($_GET['seg']);
		if ($r_seg && ria_mysql_num_rows($r_seg)) {
			$seg = ria_mysql_fetch_assoc($r_seg);

			Breadcrumbs::add( $seg['name'], '/admin/customers/index.php?seg='.$seg['id'] );
		}
	}

	if( isset($_GET['prf']) && $_GET['prf']>0 ){
		$prf_name = gu_profiles_get_name( $_GET['prf'] );
		Breadcrumbs::add( $prf_name , '/admin/customers/index.php?prf='.$_GET['prf'] );
	}

	if (isset($_GET['seller']) && is_numeric($_GET['seller']) && $_GET['seller']) {
		Breadcrumbs::add( _('Représentants'), '/admin/customers/index.php?prf='.PRF_SELLER );
		Breadcrumbs::add( gu_users_get_name($_GET['seller']), '/admin/customers/edit.php?usr='.$_GET['seller'].'&prf='.PRF_SELLER );
		Breadcrumbs::add( _('Liste de ses clients'), '/admin/customers/index.php?seller='.$_GET['seller'] );
	}

	if( !isset($_GET['usr']) || $_GET['usr']==0 ){
		Breadcrumbs::add( _('Créer un nouveau compte') );
	}else{
		$lusr = ria_mysql_fetch_array(gu_users_get($_GET['usr']));
		if( !is_numeric($lusr['adr_invoices']) ){
			Breadcrumbs::add( _('Non disponible') );
		}else{
			$name = gu_users_get_name($_GET['usr']);
			if( trim($name) == '' ){
				$name = gu_users_get_email($_GET['usr']);
			}

			if( is_numeric($lusr['adr_invoices']) ){
				Breadcrumbs::add( $name );
			}
		}
	}

}

/**	Cette fonction sert de soutien à view_admin_location et gère le fil d'Ariane des pages dédiées aux système de gestion des commandes
 *	\private
 */
function view_admin_location_orders(){

	// Rajout de "/admin" en début du PHP_SELF pour gérer l'admin mutualisée + l'admin normale
	$php_self = view_admin_current_page_url();

	// Commandes
	if( strstr($php_self,'/orders/index.php') || strstr($php_self,'/orders/orders.php') && $_GET['state'] === 0 ){
		print _('Commandes');
	}else{
		print '<a href="/admin/orders/orders.php">'._('Commandes').'</a> &bull; ';

		if( strstr($php_self,'/orders/returns/') ){
			if (strstr($php_self,'/orders/returns/returns.php')) {
				print '<a href="/admin/orders/returns/index.php">'._('Gestion des retours').'</a>';
				require_once('ord.returns.inc.php');
				$state = (isset($_GET['state']) && $_GET['state']) ? ria_mysql_fetch_assoc(ord_returns_states_get($_GET['state'])) : 0;
				print ' &bull; '.(($state) ? $state['name_plural'] : _('Tous les retours'));
			}elseif (strstr($php_self,'/orders/returns/return.php')) {
				print '<a href="/admin/orders/returns/index.php">'._('Gestion des retours').'</a>';
				print ' &bull; <a href="/admin/orders/returns/returns.php">'._('Tous les retours').'</a>';
				print ' &bull; '._('Retour n°').str_pad($_GET['ret'], 8, '0', STR_PAD_LEFT);
			}else{
				print _('Gestion des retours');
			}
		}
		elseif( strstr($php_self,'/orders/models.php') ){
			print _('Tous les modèles');
		}
		elseif( strstr($php_self,'/orders/bl/index.php') ){
			print _('Tous les bons de livraison');
		}
		elseif( strstr($php_self,'/orders/bl/bl.php') ){
			print '<a href="/admin/orders/bl/index.php">'._('Bons de livraison').'</a> &bull; ';
			printf( _('Pièce n°%d'), str_pad( $_GET['bl'], 8, '0', STR_PAD_LEFT ) );
		}
		elseif( strstr($php_self,'/orders/model.php') ){
			print '<a href="models.php">'._('Tous les modèles').'</a> &bull; ';
			print _('Modèle n°'). str_pad( $_GET['ord'], 8, '0', STR_PAD_LEFT );
		}
		elseif( strstr($php_self,'/orders/orders.php') ){
			if( !isset($_GET['state']) || $_GET['state']==0 ){
				print _('Toutes les commandes');
			}else{
				$state = ria_mysql_fetch_array(ord_states_get($_GET['state']));
				print $state['name_plural'];
			}
		}elseif( strstr($php_self,'/orders/invoice.php') ){
			print _('Facture n°'). str_pad( $_GET['inv'], 8, '0', STR_PAD_LEFT );
		}elseif( strstr($php_self,'/orders/order.php') ){
			if( !isset($_GET['state']) || $_GET['state']==0 ){
				print '<a href="orders.php">'._('Toutes les commandes').'</a> &bull; ';
			}else{
				$state = ria_mysql_fetch_array(ord_states_get($_GET['state']));
				print '<a href="orders.php?state='.$_GET['state'].(isset($_GET['date1']) ? '?date1='.$_GET['date1'].(isset($_GET['date2']) ? '&amp;date2='.$_GET['date2']: '') :'').'">'.$state['name_plural'].'</a> &bull; ';
			}

			print _('Commande n°'). str_pad( $_GET['ord'], 8, '0', STR_PAD_LEFT );
		}
	}

}

/**	Cette fonction sert de soutien à view_admin_location et gère le fil d'Ariane des pages dédiées à la configuration de la solution
 *	\private
 */
function view_admin_location_configuration(){
	global $config;

	$php_self = view_admin_current_page_url();

	// Configuration
	if( strstr($php_self,'/prices/') ){
		if( strstr($php_self,'/prices/index.php') ){
			print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Paramètres de tarification');
		}elseif( strstr($php_self,'/prices/edit.php') ){
			print '<a href="../index.php">'._('Configuration').'</a> &bull; <a href="index.php">'._('Paramètres de tarification').'</a> &bull; ';
			if( isset($_GET['prc']) ){
				$rprc = prd_prices_categories_get( $_GET['prc'] );
				if( ria_mysql_num_rows($rprc) ){
					$prc = ria_mysql_fetch_array($rprc);
					print htmlspecialchars( $prc['name'] );
				}
			}
		}
	}elseif( strstr($php_self,'/spam/') ){
		print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Filtre antispam');
	}elseif( strstr($php_self,'/errors/') ){
		print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="../index.php">'._('Redirections').'</a> &bull; '._('Erreurs 404');
	}elseif( strstr($php_self, '/permanent/') ){
		print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="../index.php">'._('Redirections').'</a> &bull; '._('Permanentes');
	}elseif( strstr($php_self,'/redirections/') ){
		print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Redirections');
	}elseif( strstr($php_self,'/owner/') ){
		print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Informations sur le propriétaire');
	}elseif( strstr($php_self,'/livraison/') ){
		if( strstr($php_self, '/livraison/zones/edit.php') ){
			// Édition zone
			if( isset($_GET['zone']) && $_GET['zone']>0 ){
				print '<a href="/admin/config/index.php">Configuration</a> &bull; <a href="/admin/config/livraison/index.php">Livraison des commandes</a> &bull; <a href="index.php">Zones de livraison</a> &bull; '.htmlspecialchars( dlv_zones_get_name($_GET['zone']) );
			}
			// Création zone
			else{
				print '<a href="/admin/config/index.php">Configuration</a> &bull; <a href="/admin/config/livraison/index.php">Livraison des commandes</a> &bull; <a href="index.php">Zones de livraison</a> &bull; Nouvelle zone de livraison';
			}

		}elseif( strstr($php_self,'/livraison/zones/') ){
			print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/livraison/index.php">'._('Livraison des commandes').'</a> &bull; '._('Zones de livraison');
		}elseif( strstr($php_self, '/livraison/services/edit.php') ){
			// Édition service
			if( isset($_GET['srv']) && $_GET['srv']>0 ){
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/livraison/index.php">'._('Livraison des commandes').'</a> &bull; <a href="index.php">'._('Services de livraison').'</a> &bull; '.htmlspecialchars( dlv_services_get_name($_GET['srv']) );
			}
			// Création service
			else{
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/livraison/index.php">'._('Livraison des commandes').'</a> &bull; <a href="index.php">'._('Services de livraison').'</a> &bull;'._(' Nouveau service de livraison');
			}

		}elseif( strstr($php_self,'/livraison/services/') ){
			print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/livraison/index.php">'._('Livraison des commandes').'</a> &bull; '._('Services de livraison');
		}elseif( strstr($php_self,'/livraison/stores/') ){
			if( isset($_GET['str']) && $_GET['str']>0 ){
				$str = ria_mysql_fetch_array(dlv_stores_get( $_GET['str'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null ));
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/livraison/index.php">'._('Livraison des commandes').'</a> &bull; <a href="index.php">'._('Magasins').'</a> &bull; '.htmlspecialchars($str['name']);
			}else{
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/livraison/index.php">'._('Livraison des commandes').'</a> &bull; '._('Magasins');
			}
		}elseif( strstr($php_self, '/livraison/options/') ){
			print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/livraison/index.php">'._('Livraison des commandes').'</a> &bull; '._('Options de livraison');
		}elseif( strstr($php_self, '/livraison/deposits/') ){
			if( isset($_GET['dps']) && $_GET['dps']>0 ){
				$dps = ria_mysql_fetch_array(prd_deposits_get($_GET['dps']));
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/livraison/index.php">'._('Livraison des commandes').'</a> &bull; <a href="index.php">'._('Dépôts').'</a> &bull; '.htmlspecialchars($dps['name']);
			}else{
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/livraison/index.php">'._('Livraison des commandes').'</a> &bull; '._('Dépôts');
			}
		}elseif( strstr($php_self,'/livraison/jobs/services/') ){
			if( strstr($php_self,'/livraison/jobs/services/edit.php') ){
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/livraison/index.php">'._('Livraison des commandes').'</a> &bull; <a href="/admin/config/livraison/stores/index.php">'._('Magasins').'</a> &bull; <a href="/admin/config/livraison/jobs/services/index.php">'._('Services').'</a> &bull; '._('Edition');
			}else{
				print '<a href="../index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/livraison/index.php">'._('Livraison des commandes').'</a> &bull; <a href="/admin/config/livraison/stores/index.php">'._('Magasins').'</a> &bull; '._('Services');
			}
		}elseif( strstr($php_self,'/livraison/jobs/') ){
			if( strstr($php_self,'/livraison/jobs/edit.php') ){
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/livraison/index.php">'._('Livraison des commandes').'</a> &bull; <a href="/admin/config/livraison/stores/index.php">'._('Magasins').'</a> &bull; <a href="/admin/config/livraison/jobs/index.php">'._('Postes').'</a> &bull; '._('Edition');
			}else{
				print '<a href="../index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/livraison/index.php">'._('Livraison des commandes').'</a> &bull; <a href="/admin/config/livraison/stores/index.php">'._('Magasins').'</a> &bull; '._('Postes');
			}
		}else{
			print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Livraison des commandes');
		}
	}elseif( strstr($php_self,'/returns/') ){
		print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Gestion des retours');
	}elseif( strstr($php_self, '/retractations/') ){
		print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Gestion des rétractations');
	}elseif( strstr($php_self,'/fields/') ){
		if( strstr($php_self, '/fields/classes') ){
			if( strstr($php_self, '/objects.php') ){
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="../index.php">'._('Structure des données').'</a>';
				if( isset($_GET['cls']) && is_numeric($_GET['cls']) ){
					$name = _('Nouvelle classe');
					if( $_GET['cls']>0 && ($rcls = fld_classes_get( $_GET['cls'] )) ){
						$name = ria_mysql_result( $rcls, 0, 'name' );
					}

					print ' &bull; <a href="/admin/config/fields/classes/index.php">'._('Classes').'</a> &bull; '._('Objets de la classe').' '.htmlspecialchars( $name );
				}else{
					print ' &bull; '._('Objets');
				}
			}elseif( strstr($php_self, '/object.php') ){
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="../index.php">'._('Structure des données').'</a>';
				if( isset($_GET['cls']) && is_numeric($_GET['cls']) ){
					$name = '';
					if( $_GET['cls']>0 && ($rcls = fld_classes_get( $_GET['cls'] )) ){
						$name = ria_mysql_result( $rcls, 0, 'name' );
					}

					print ' &bull; <a href="/admin/config/fields/classes/index.php">'._('Classes').'</a> &bull; <a href="/admin/config/fields/classes/objects.php?cls='.$_GET['cls'].'">'._('Objets de la classe').' '.htmlspecialchars( $name ).'</a>';

					$object = _('Nouvel objet');
					if( isset($_GET['obj']) && is_numeric($_GET['obj']) && $_GET['obj']>0 ){
						$robj = fld_objects_get( $_GET['obj'] );
						if( $robj && ria_mysql_num_rows($robj) ){
							$object = ria_mysql_result( $robj, 0, 'name' );
						}
					}

					print ' &bull; '.htmlspecialchars( $object );
				}
			}else{
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="../index.php">'._('Structure des données').'</a>';
				if( isset($_GET['cls']) && is_numeric($_GET['cls']) ){
					$name = _('Nouvelle classe');
					if( $_GET['cls']>0 && ($rcls = fld_classes_get( $_GET['cls'] )) ){
						$name = ria_mysql_result( $rcls, 0, 'name' );
					}

					print ' &bull; <a href="/admin/config/fields/classes/index.php">'._('Classes').'</a> &bull; '.htmlspecialchars( $name );
				}else{
					print ' &bull; '._('Classes');
				}
			}
		}elseif( strstr($php_self,'/fields/models/') ){
			if( isset($_GET['mdl']) && is_numeric($_GET['mdl']) ){
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="../index.php">'._('Structure des données').'</a> &bull; <a href="index.php">'._('Modèles de saisie').'</a> &bull; ';
				$mdl = ria_mysql_fetch_array(fld_models_get($_GET['mdl']));
				print htmlspecialchars($mdl['name']);
			}else{
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="../index.php">'._('Structure des données').'</a> &bull; '._('Modèles de saisie');
			}
		}elseif( strstr($php_self,'/fields/fields/') ){
			if( isset($_GET['fld']) && is_numeric($_GET['fld']) ){
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="../index.php">'._('Structure des données').'</a> &bull; <a href="index.php">'._('Champs personnalisés').'</a> &bull; ';
				if( $_GET['fld']>0 ){
					$fld = ria_mysql_fetch_array(fld_fields_get($_GET['fld']));
					print htmlspecialchars($fld['name']);
				}else{
					print _('Nouveau champ personnalisé');
				}
			}else{
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="../index.php">'._('Structure des données').'</a> &bull; '._('Champs personnalisés');
			}
		}elseif( strstr($php_self,'/fields/categories/') ){
			if( isset($_GET['cat']) && is_numeric($_GET['cat']) ){
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="../index.php">'._('Structure des données').'</a> &bull; <a href="index.php">'._('Catégories de champs').'</a> &bull; ';
				$cat = ria_mysql_fetch_array(fld_categories_get($_GET['cat']));
				print htmlspecialchars($cat['name']);
			}else{
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="../index.php">'._('Structure des données').'</a> &bull; '._('Catégories de champs');
			}
		}elseif( strstr($php_self,'/fields/units/') ){
			if( isset($_GET['unit']) && is_numeric($_GET['unit']) ){
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="../index.php">'._('Structure des données').'</a> &bull; <a href="index.php">'._('Unités de mesure').'</a> &bull; ';
				$unit = ria_mysql_fetch_array(fld_units_get($_GET['unit']));
				print htmlspecialchars($unit['name']);
			}else{
				print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="../index.php">'._('Structure des données').'</a> &bull; '._('Unités de mesure');
			}
		}elseif( strstr($php_self, '/fields/segments/') ){
			print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/fields/index.php">'._('Structure des données').'</a>';
			if( strstr($php_self, '/index.php') ){
				print ' &bull; '._('Segments');
			} elseif( strstr($php_self, '/segment.php') ){
				if( isset($_GET['id']) && is_numeric($_GET['id']) && $_GET['id']>0 ){
					$rseg = seg_segments_get( $_GET['id'] );
					if( $rseg && ria_mysql_num_rows($rseg) ){
						$seg = ria_mysql_fetch_array( $rseg );
						print ' &bull; <a href="/admin/config/fields/segments/index.php">Segments</a> &bull; '.htmlspecialchars($seg['name']);
					}else{
						print ' &bull; '._('Segments');
					}
				} else {
					print ' &bull; '._('Segments');
				}
			}
		}
		else{
			print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Structure des données');
		}
	}elseif( strstr($php_self,'/emails/') ){
		print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Adresses emails');
	}elseif( strstr($php_self,'/config/orders/') ){
		print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Archiver les commandes');
	}elseif( strstr($php_self,'/products/') ){
		print '<a href="../index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/catalogue/index.php">' . _('Catalogue') . '</a> &bull; '._('Listes de produits');
	}elseif( strstr($php_self, 'referencement/index.php') ){
		print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Référencement');
	}elseif( strstr($php_self, '/expeditions/') ){
		print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Horaires d\'expédition');
	}elseif( strstr($php_self, '/translate/') ){
		print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Traductions');
	}elseif( strstr($php_self, '/paiements/') ){
		if( strstr($php_self, '/paiements/cheque.php') ){
			print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/paiements/index.php">'._('Modes de règlement').'</a> &bull; '._('Règlement par chèque');
		}elseif( strstr($php_self, '/paiements/yesbycash.php') ){
			print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/paiements/index.php">'._('Modes de règlement').'</a> &bull; '._('YesByCash');
		} elseif( strstr($php_self, '/paiements/transfer.php') ) {
			print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/paiements/index.php">'._('Modes de règlement').'</a>';
			if( isset($_GET['transfer']) && is_numeric($_GET['transfer']) && $_GET['transfer']>=0 ){
				print ' &bull; <a href="/admin/config/paiements/transfer.php">'._('Règlement par virement').'</a>';
				if( $_GET['transfer'] ){
					print ' &bull; '._('Edition d\'informations bancaires');
				} else {
					print ' &bull; '._('Ajout d\'informations bancaires');
				}
			} else {
				print ' &bull; '._('Règlement par virement');
			}
		} else {
			print '<a href="../index.php">'._('Configuration').'</a> &bull; '._('Modes de règlement');
		}
	}elseif( strstr($php_self, '/gifts/index.php') ){
		print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; '._('Cartes cadeaux');
	}elseif( strstr($php_self, '/referencement/static.php') ){
		print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/referencement/index.php">'._('Référencement').'</a> &bull; '._('Pages statiques');
	}elseif( strstr($php_self, '/referencement/edit-tag.php') ){
		print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/referencement/index.php">'._('Référencement').'</a> &bull; <a href="/admin/config/referencement/static.php">'._('Pages statiques').'</a> &bull; '.( isset($_GET['tag']) && rew_tags_exists($_GET['tag']) ? _('Edition d\'une personnalisation') : _('Ajout d\'une personnalisation') ).' ';
	}elseif( strstr($php_self, '/referencement/dynamic-sentences.php') ){
		print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/referencement/index.php">'._('Référencement').'</a> &bull; '._('Phrases dynamiques');
	}elseif( strstr($php_self, '/config/img-default.php') ){
		print '<a href="/admin/config/index.php">'._('Configuration').'</a>  &bull; '._('Image par défaut');
	}elseif( strstr($php_self, '/config/instagram/index.php') ){
		print '<a href="/admin/config/index.php">'._('Configuration').'</a>  &bull; '._('Instagram');
	}elseif( strstr($php_self, '/pdf_generation/index.php') ){
		print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; '._('Génération PDF');
	}elseif( strstr($php_self, '/pdf_generation/invoice.php') ){
		print '<a href="/admin/config/index.php">'._('Configuration').'</a> &bull; <a href="/admin/config/pdf_generation/index.php">'._('Génération PDF') . '</a> &bull; ' . _('Facture');
	}elseif( strstr($php_self, '/pdf_generation/devis.php') ){
		print '<a href="/admin/config/index.php">' . _('Configuration') . '</a> &bull; <a href="/admin/config/pdf_generation/index.php">' . _('Génération PDF') . '</a> &bull; ' . _('Devis');
	}elseif( strstr($php_self, '/pdf_generation/reports.php') ){
		print '<a href="/admin/config/index.php">' . _('Configuration') . '</a> &bull; <a href="/admin/config/pdf_generation/index.php">' . _('Génération PDF') . '</a> &bull; ' . _('Rapports de visite');
	}else{
		print _('Configuration');
	}

}

/**	Cette fonction sert de soutien à view_admin_location et gère le fil d'Ariane des pages dédiées aux outils
 *	\private
 */
function view_admin_location_tools(){

	$php_self = view_admin_current_page_url();

	$link = '/admin/tools/index.php';
	$rubric_name = _('Outils');

	if( strstr($php_self,'/news/') ){ // Actualités
		if( strstr($php_self,'/news/categories/') ){
			if( strstr($php_self,'/news/categories/index.php') ){
				print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; '._('Catégories d\'actualités');
			}else{
				if( isset($_GET['cat']) && $_GET['cat'] ){
					$cat_data = ria_mysql_fetch_assoc(news_categories_get( $_GET['cat'] ));
					$p_url = '';
					if( $parents = news_categories_get_parents( $_GET['cat'] ) ){
						foreach( $parents as $pcat_id => $depth ){
							$pcat = ria_mysql_fetch_assoc(news_categories_get( $pcat_id ));
							$p_url .= ' &bull; <a href="/admin/tools/news/index.php?cat='.$pcat['id'].'">'.htmlspecialchars($pcat['name']).'</a>';
						}
					}
					print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; <a href="/admin/tools/news/categories/index.php">'._('Catégories d\'actualités').'</a>'.$p_url.' &bull; '.htmlspecialchars($cat_data['name']);
				}else{
					print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; <a href="/admin/tools/news/categories/index.php">'._('Catégories d\'actualités').'</a> &bull; '._('Nouvelle catégorie');
				}
			}
		}elseif( isset($_GET['archived']) && $_GET['archived'] ){ // Actualités > Archives
			if( strstr($php_self,'/news/edit.php') ){
				print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; <a href="/admin/tools/news/index.php">'._('Actualités').'</a> &bull; <a href="/admin/tools/news/index.php?archived=1">'._('Archives').'</a> &bull; ';

				if( isset($_GET['news']) ){
					$rnews = news_get($_GET['news']);
					if( ria_mysql_num_rows($rnews) ){
						$news = ria_mysql_fetch_array($rnews);
						print _('Actualité ').htmlspecialchars( $news['name'] );
					}
				}

			}else{
				print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; <a href="/admin/tools/news/index.php">'._('Actualités').'</a> &bull; '._('Archives');
			}
		}elseif( strstr($php_self,'/news/edit.php') ){
			print '<a href="../index.php">'.$rubric_name.'</a> &bull; <a href="index.php">'._('Actualités').'</a> &bull; ';
			if( isset($_GET['news']) && is_numeric($_GET['news']) && $_GET['news']>0 ){
				$rnews = news_get($_GET['news']);
				if( $rnews && ria_mysql_num_rows($rnews) ){
					$news = ria_mysql_fetch_array($rnews);
					print htmlspecialchars( $news['name'] );
				}else{
					print _('Nouvelle actualité');
				}
			}else{
				print _('Nouvelle actualité');
			}
		}elseif( isset($_GET['cat']) && $_GET['cat'] ){
			$cat_data = ria_mysql_fetch_assoc(news_categories_get( $_GET['cat'] ));
			$p_url = '';
			if( $parents = news_categories_get_parents( $_GET['cat'] ) ){
				foreach( $parents as $pcat_id => $depth ){
					$pcat = ria_mysql_fetch_assoc(news_categories_get( $pcat_id ));
					$p_url .= ' &bull; <a href="/admin/tools/news/index.php?cat='.$pcat['id'].'">'.htmlspecialchars($pcat['name']).'</a>';
				}
			}
			print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; <a href="/admin/tools/news/index.php">'._('Actualités').'</a>'.$p_url.' &bull; '.htmlspecialchars($cat_data['name']);
		}else{
			print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; '._('Actualités');
		}
	}elseif( strstr($php_self,'/livr-alerts/') ){
		print '<a href="../index.php">'.$rubric_name.'</a> &bull; '._('Alertes de disponibilité');
	}elseif( strstr($php_self,'/price-drop/') ){
		print '<a href="../index.php">Outils</a> &bull; Alertes de baisse de prix';
	}elseif( strstr($php_self,'/faq/') ){
		if( strstr($php_self, '/faq/index.php') ){
			print '<a href="../index.php">'.$rubric_name.'</a> &bull; '._('Foire Aux Questions');
		}elseif( strstr($php_self, '/faq/category.php') ) {
			if( isset($_GET['cat']) ) {
				$rcat = faq_categories_get( $_GET['cat'] );
				if( $rcat && ria_mysql_num_rows($rcat) ){
					print '<a href="../index.php">'.$rubric_name.'</a> &bull; <a href="/admin/tools/faq/index.php">'._('Foire Aux Questions').'</a> &bull; '.htmlspecialchars(ria_mysql_result( $rcat, 0, 'name' ));
				} else {
					print '<a href="../index.php">'.$rubric_name.'</a> &bull; '._('Foire Aux Questions');
				}
			} elseif( isset($_GET['name']) ) {
				$name = trim($_GET['name']) ? $_GET['name'] : _('Nouvelle catégorie');
				print '<a href="../index.php">'.$rubric_name.'</a> &bull; <a href="/admin/tools/faq/index.php">'._('Foire Aux Questions').'</a> &bull; '.htmlspecialchars($name);
			} else {
				print '<a href="../index.php">'.$rubric_name.'</a> &bull; '._('Foire Aux Questions');
			}
		} elseif( strstr($php_self, '/faq/question.php') ) {
			print '<a href="../index.php">'.$rubric_name.'</a> &bull; <a href="/admin/tools/faq/index.php">'._('Foire Aux Questions').'</a>';
			if( isset($_GET['cat']) ){
				$rcat = faq_categories_get( $_GET['cat'] );
				if( $rcat && ria_mysql_num_rows($rcat) ){
					$cat = ria_mysql_fetch_array( $rcat );
					print ' &bull; <a href="/admin/tools/faq/category.php?cat='.$cat['id'].'">'.htmlspecialchars($cat['name']).'</a>';
				}
			}
			if( isset($_GET['qst']) && is_numeric($_GET['qst']) && $_GET['qst']>0 ){
				$rqst = faq_questions_get( $_GET['qst'] );
				if( $rqst && ria_mysql_num_rows($rqst) ){
					print ' &bull; '.htmlspecialchars(ria_mysql_result( $rqst, 0, 'name' ));
				}
			} elseif( isset($_GET['name']) || (isset($_GET['qst']) && $_GET['qst']==0) ){
				$name = isset($_GET['name']) && trim($_GET['name']) ? $_GET['name'] : _('Nouvelle question');
				print ' &bull; '.htmlspecialchars($name);
			}
		}
	}elseif( strstr($php_self,'/newsletter/list.php') ){
		require_once( 'newsletter.inc.php' );
		print '<a href="../index.php">'.$rubric_name.'</a> &bull; <a href="index.php">'._('Newsletters').'</a> &bull; ';
		if( isset($_GET['oc']) ){
			$rcat = nlr_categorie_get( $_GET['oc'] );
			if( ria_mysql_num_rows($rcat) ){
				$cat = ria_mysql_fetch_array( $rcat );
				print htmlspecialchars( $cat['cat'] );
			}
		}
	}elseif( strstr($php_self,'/newsletter/edit-cat.php') || strstr($php_self,'/newsletter/list.php') ){
		require_once( 'newsletter.inc.php' );
		print '<a href="../index.php">'.$rubric_name.'</a> &bull; <a href="index.php">'._('Newsletters').'</a> &bull; ';
		if( isset($_GET['cat']) ){
			$rcat = nlr_categorie_get( $_GET['cat'] );
			if( ria_mysql_num_rows($rcat) ){
				$cat = ria_mysql_fetch_array( $rcat );
				print htmlspecialchars( $cat['cat'] );
			}
		}
	}elseif( strstr($php_self,'/newsletter/') ){
		print '<a href="../index.php">'.$rubric_name.'</a> &bull; '._('Newsletters');
	}elseif( strstr($php_self,'/banners/') ){
		if( strstr($php_self,'/banners/edit.php') ){
			if( !isset($_GET['id']) || $_GET['id']==0 ){
				print '<a href="../index.php">'.$rubric_name.'</a> &bull; <a href="index.php">'._('Bannières').'</a> &bull; '._('Créer une nouvelle bannière');
			}else{
				$rbanner = adv_banners_get( 0, $_GET['id'], false, false, false, false );
				if( $rbanner && ria_mysql_num_rows($rbanner) ){
					$banner = ria_mysql_fetch_array($rbanner);
					print '<a href="../index.php">'.$rubric_name.'</a> &bull; <a href="index.php">'._('Bannières').'</a> &bull; '._('Bannière ').htmlspecialchars( $banner['name'] );
				}
			}
		}else{
			print '<a href="../index.php">'.$rubric_name.'</a> &bull; '._('Bannières');
		}
	}elseif( strstr($php_self,'/tools/zones/') ){
		if( strstr($php_self,'/tools/zones/index.php') ){
			print '<a href="../index.php">'.$rubric_name.'</a> &bull; '._('Zones d\'actions');
		}else{
			print '<a href="../index.php">'.$rubric_name.'</a> &bull; <a href="index.php">'._('Zones d\'actions').'</a> &bull; Zone d\'action';
		}
	}elseif( strstr($php_self,'/reviews/') ){
		print '<a href="../index.php">'.$rubric_name.'</a> &bull; '._('Avis consommateurs');
	}elseif( strstr($php_self,'/cms/') ){
		$rchemin = array();
		if( isset($_GET['cat']) ){
			$rchemin = cms_hierarchy_get($_GET['cat']);
		}elseif( isset($_GET['parent']) ){
			$rchemin = cms_hierarchy_get($_GET['parent']);
		}

		print '<a href="../index.php">'.$rubric_name.'</a> &bull; <a href="index.php">'._('Gestion de contenu').'</a>';
		if( is_array($rchemin) && sizeof($rchemin) ){
			foreach($rchemin as $chem){
				if( $chem['id'] != $_GET['cat'] ){
					print ' &bull; <a href="edit.php?cat='.$chem['id'].'&amp;parent='.$chem['parent'].'">'.htmlspecialchars($chem['name']).'</a>';
				}else{
					print ' &bull; '.htmlspecialchars($chem['name']);
				}
			}
		}

	} elseif( strstr($php_self, '/erratums/') ){
		if( strstr($php_self, '/edit.php') && (!isset($_GET['id']) || $_GET['id']<=0) ){
			print'<a href="'.$link.'">'.$rubric_name.'</a> &bull; <a href="/admin/tools/erratums/index.php">'._('Erratums').'</a> &bull; '._('Nouvel erratum');
		}elseif( isset($_GET['id']) && ($erratum = ria_mysql_fetch_array(cat_erratums_get($_GET['id']))) ){
			print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; <a href="/admin/tools/erratums/index.php">'._('Erratums').'</a> &bull; '.htmlspecialchars($erratum['prd_name']);
		}else{
			print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; '._('Erratums');
		}
	} elseif( strstr($php_self,'/glossary/') ){
		print '<a href="../index.php">'.$rubric_name.'</a> &bull; '._('Glossaire');
	} elseif( strstr($php_self, '/rewards/') ){
		if( strstr($php_self, '/rewards/config/') ){
			print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; <a href="/admin/tools/rewards/index.php">'._('Parrainage / Points de fidélité').'</a> &bull; '._('Configuration');
		} elseif( strstr($php_self, '/rewards/stats.php') ){
			print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; <a href="/admin/tools/rewards/index.php">'._('Parrainage / Points de fidélité').'</a> &bull; '._('Statistiques');
		}else{
			print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; '._('Parrainage / Points de fidélité');
		}
	}elseif(strstr($php_self,'/marketing/')){
		if(strstr($php_self,'/marketing/sms/') ){ // Campagnes SMS Marketing
			if( strstr($php_self,'/sms/edit/edit.php') && $_GET['cpg'] == 0 ){
				print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; <a href="/admin/tools/marketing/sms/index.php">'._('Campagnes Marketing SMS').'</a> &bull; '._('Nouvelle campagne marketing');
			}
			elseif( strstr($php_self,'/sms/edit/edit.php') ){
				$title = CampaignsManager::getCampaignTitle($_GET['cpg']);
				print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; <a href="/admin/tools/marketing/sms/index.php">'._('Campagnes Marketing SMS').'</a> &bull; '.htmlspecialchars( $title );
			}else{
				print '<a href="'.$link.'">'.$rubric_name.'</a> &bull; '._('Campagnes Marketing SMS');
			}
		}
	}elseif(strstr($php_self,'/imports/')){ // Outils > Imports
		$breadcrumbs = '<a href="'.$link.'">'.$rubric_name.'</a> &bull; ';
		$breadcrumbs_import = _('Imports');

		if( strstr($php_self, '/reports.php') ){
			$breadcrumbs .= '<a href="/admin/tools/imports/index.php">'.$breadcrumbs_import.'</a> &bull; '._('Rapports');
		}elseif(strstr($php_self, '/mapping.php')){
			$breadcrumbs .= '<a href="/admin/tools/imports/index.php">'.$breadcrumbs_import.'</a> &bull; '._('Configuration de l\'import');
		}elseif( strstr($php_self, '/import.php') ){
			$breadcrumbs .= '<a href="/admin/tools/imports/index.php">'.$breadcrumbs_import.'</a> &bull; '._('Importer un nouveau fichier');
		}else{
			$breadcrumbs .= ''.$breadcrumbs_import;
		}

		print $breadcrumbs;
	}elseif(strstr($php_self,'/exports/')){ // Outils > Exports
		print '<a href="'.$link.'">'._('Outils').'</a> &bull; '._('Exports');
	}else{
		print $rubric_name;
	}

}

/**	Cette fonction sert de soutien à view_admin_location et gère le fil d'Ariane des pages dédiées à Yuto
 *	\private
 */
function view_admin_location_yuto(){

	$php_self = view_admin_current_page_url();

	$rubric_name = 'Yuto';

	// Racine
	if( strstr($php_self, '/admin/fdv/index.php') ){
		print _($rubric_name);
	}

	if( strstr($php_self, '/admin/fdv/devices') ){
		print '<a href="/admin/fdv/index.php">'._($rubric_name).'</a> &bull; ';
		if( strstr($php_self, '/index.php') ){
			print _('Gestion de parc / Flotte d\'appareils');
		}elseif( strstr($php_self, '/edit.php') ){
			print '<a href="/admin/fdv/devices/index.php">'._('Gestion de parc / Flotte d\'appareils').'</a> &bull; ';

			$device = false;
			if( isset($_GET['id']) && is_numeric($_GET['id']) && $_GET['id'] > 0 ){
				$rdevice = dev_devices_get( $_GET['id'] );
				if( $rdevice && ria_mysql_num_rows($rdevice) ){
					$device = ria_mysql_fetch_assoc( $rdevice );
				}
			}

			if( $device ){
				print _('Appareil').' '.htmlspecialchars($device['key']);
			}else{
				print _('Nouvel appareil');
			}
		}elseif ( strstr($php_self, '/devices-location.php') ) {
			print '<a href="/admin/fdv/devices/index.php">'._('Gestion de parc / Flotte d\'appareils').'</a> &bull; ';
			print _('Localisation des appareils');
		}elseif ( strstr($php_self, '/setup-guide.php') ) {
			print '<a href="/admin/fdv/devices/index.php">'._('Gestion de parc / Flotte d\'appareils').'</a> &bull; ';
			print _('Guide d\'installation');
		}elseif ( strstr($php_self, '/working-shift.php') ) {
			print '<a href="/admin/fdv/devices/index.php">'._('Gestion de parc / Flotte d\'appareils').'</a> &bull; ';
			print _('Horaires d\'activité');
		}
	}elseif( strstr($php_self, '/admin/fdv/notifications/edit.php') ){
		print '<a href="/admin/fdv/index.php">'._($rubric_name).'</a> &bull; <a href="/admin/fdv/notifications/index.php">'._('Notifications').'</a> &bull; ';

		if( isset($_GET['nt_id']) && $_GET['nt_id'] ){
			$not = nt_notifications_get($_GET['nt_id']);
			if( $not ){
				print htmlspecialchars( $not['nt_title'] );
			}
		}else{
			print _('Nouvelle notification');
		}

	}elseif( strstr($php_self, '/admin/fdv/notifications/') ){
		print '<a href="/admin/fdv/index.php">'._($rubric_name).'</a> &bull; '._('Notifications');
	}elseif( strstr($php_self, '/admin/fdv/maps/') ){
		print '<a href="/admin/fdv/index.php">'._($rubric_name).'</a> &bull; '._('Cartographie');
	}elseif( strstr($php_self, '/admin/fdv/prize/') ){
		print '<a href="/admin/fdv/index.php">'._($rubric_name).'</a> &bull; '._('Palmarès');
	}elseif( strstr($php_self, '/admin/fdv/reports') ){
		print '<a href="/admin/fdv/index.php">'._($rubric_name).'</a> &bull; ';
		if( strstr($php_self, '/types/') ){
			if( strstr($php_self, '/index.php') ){
				print _('Rapports de visite');
			}elseif( strstr($php_self, 'edit.php') ){
				print '<a href="/admin/fdv/reports/types/index.php">'._('Rapports').'</a> &bull; ';
				if( isset($_GET['type']) && is_numeric($_GET['type']) && $_GET['type'] > 0 ){
					print htmlspecialchars( rp_types_get_name( $_GET['type'] ) );
				}else{
					print _('Ajout d\'un type de rapport');
				}
			}
		}elseif( strstr($php_self, '/calls/' ) ){
			if( strstr($php_self, '/index.php') ){
				print _('Rapports d\'appel');
			}else{
				print '<a href="/admin/fdv/reports/calls/index.php">'._('Rapports d\'appels').'</a> &bull; ';
				if( isset($_GET['id']) ){
					$call = gcl_calls_get_by_view($_GET['id']);
					print _('Appel du ').date(_('d/m/Y à H:i'), strtotime($call['gcl_date_created']));
				}else{
					print _('Nouveau rapport d\'appel téléphonique');
				}
			}
		}else{
			print '<a href="/admin/fdv/reports/index.php">'._('Types de rapports').'</a> &bull; ';
			if( strstr($php_self, '/index.php') ){
				if( isset($_GET['type']) && is_numeric($_GET['type']) && $_GET['type'] > 0 ){
					print htmlspecialchars( rp_types_get_name( $_GET['type'] ) );
				}
			}elseif( strstr($php_self, '/reports/view.php') ){
				if( isset($_GET['type']) && is_numeric($_GET['type']) && $_GET['type'] > 0 ){
					print '<a href="/admin/fdv/reports/index.php?type='.$_GET['type'].'">'.htmlspecialchars( rp_types_get_name( $_GET['type'] ) ).'</a> &bull; ';
				}

				if( isset($_GET['rp']) && is_numeric($_GET['rp']) && $_GET['rp'] > 0 ){
					print _('Rapport n°').$_GET['rp'];
				}
			}elseif( strstr($_SERVER['PHP_SELF'], '/reports/edit.php') ){ // Fil d'ariane ajout de rapport de visite
				print _('Ajout d\'un rapport');
			}
		}
	}elseif( strstr($php_self, '/admin/fdv/config') ){
		print '<a href="/admin/fdv/index.php">'._($rubric_name).'</a> &bull; ';

		if( strstr($php_self, '/config/') ){
			if( strstr($php_self, '/index.php') ){
				print _('Configuration');
			}
		}else{
			print '<a href="/admin/fdv/reports/index.php">'._('Configurations').'</a> &bull; ';
		}
	}elseif( strstr($php_self, '/admin/fdv/stats') ){
		print '<a href="/admin/fdv/index.php">'._($rubric_name).'</a> &bull; ';

		if( strstr($php_self, '/index.php') ){
			print _('Utilisation');
		}else if( strstr($php_self, '/subscriptions.php') ){
			print '<a href="/admin/fdv/stats/index.php">'._('Statistiques').'</a> &bull; ';
			print _('Utilisation');
		}else if( strstr($php_self, '/pipeline.php') ){
			print '<a href="/admin/fdv/stats/index.php">'._('Statistiques').'</a> &bull; ';
			print _('Chiffre d\'affaires');
		}else if( strstr($php_self, '/report-time.php') ){
			print '<a href="/admin/fdv/stats/index.php">'._('Statistiques').'</a> &bull; ';
			print _('Rapport de visite');
		}else if( strstr($php_self, '/spent-time.php') ){
			print '<a href="/admin/fdv/stats/index.php">'._('Statistiques').'</a> &bull; ';
			print _('Temps de visite');
		}
	}

}

/**	Cette fonction sert de soutien à view_admin_location et gère le fil d'Ariane des pages dédiées à la gestion des comparateurs de prix et des
 * 	places de marché (RiaShopping)
 *	\private
 */
function view_admin_location_comparators(){

	$php_self = view_admin_current_page_url();

	if( strstr($php_self, '/admin/comparators/comparators.php') || strstr($php_self, '/admin/comparators/stats/index.php') ){
		print '<a href="/admin/comparators/index.php">'._('Comparateurs').'</a> &bull; ';

		if( isset($_GET['ctr']) && is_numeric($_GET['ctr']) && $_GET['ctr']>0 ){
			$rctr = ctr_comparators_get( $_GET['ctr'], false, false, null );
			if( $rctr && ria_mysql_num_rows($rctr)==1 ){
				$ctr = ria_mysql_fetch_array( $rctr );
				$ctr_menu_name = $ctr['marketplace'] ? _('Places de marché') : _('Comparateurs');
				print '<a href="/admin/comparators/comparators.php?marketplace='.$ctr['marketplace'].'">'.$ctr_menu_name.'</a> &bull; '.htmlspecialchars( $ctr['name'] );
			}
		} else {
			print isset($_GET['marketplace']) && $_GET['marketplace'] ? _('Places de marché') : _('Comparateurs');
		}
	} elseif( strstr($php_self, '/admin/comparators/search/index.php') ){
		print '<a href="/admin/comparators/index.php">'._('Comparateurs').'</a> &bull; ';
		if( isset($_GET['ctr']) && is_numeric($_GET['ctr']) && $_GET['ctr']>0 ){
			$rctr = ctr_comparators_get( $_GET['ctr'], false, false, null );
			if( $rctr && ria_mysql_num_rows($rctr)==1 ){
				$ctr = ria_mysql_fetch_array( $rctr );
				$ctr_menu_name = $ctr['marketplace'] ? _('Places de marché') : _('Comparateurs');

				print '<a href="/admin/comparators/comparators.php?marketplace='.$ctr['marketplace'].'">'.$ctr_menu_name.'</a> &bull; ';
				print '<a href="/admin/comparators/search/index.php?marketplace='.$ctr['marketplace'].'">'.htmlspecialchars( $ctr['name'] ).'</a> &bull; '._('Rechercher des produits');
			}
		} else {
			$marketplace = isset($_GET['marketplace']) && $_GET['marketplace'] ? 1 : null;
			$ctr_menu_name = $marketplace ? _('Places de marché') : _('Comparateurs');

			print '<a href="/admin/comparators/comparators.php?marketplace='.$marketplace.'">'.$ctr_menu_name.'</a> &bull; ';
			print _('Rechercher des produits');
		}
	} elseif( strstr($php_self, '/admin/comparators/link-categories.php') ){
		print '<a href="/admin/comparators/index.php">'._('Comparateurs').'</a> &bull; ';

		if( isset($_GET['ctr']) && is_numeric($_GET['ctr']) && $_GET['ctr']>0 ){
			$rctr = ctr_comparators_get( $_GET['ctr'], false, false, null );
			if( $rctr && ria_mysql_num_rows($rctr)==1 ){
				$ctr = ria_mysql_fetch_array( $rctr );
				$ctr_menu_name = $ctr['marketplace'] ? _('Places de marché') : _('Comparateurs');

				print '<a href="/admin/comparators/comparators.php?marketplace='.$ctr['marketplace'].'">'.$ctr_menu_name.'</a> &bull; ';
				print '<a href="/admin/comparators/stats/index.php?ctr='.$ctr['id'].'">'.htmlspecialchars( $ctr['name'] ).'</a> &bull; '._('Exporter les catégories');
			}
		} else {
			print isset($_GET['marketplace']) && $_GET['marketplace'] ? _('Places de marché') : _('Comparateurs');
		}
	} elseif( strstr($php_self, '/admin/comparators/categories.php') ){
		print '<a href="/admin/comparators/index.php">'._('Comparateurs').'</a> &bull; ';

		if( isset($_GET['ctr']) && is_numeric($_GET['ctr']) && $_GET['ctr']>0 ){
			$rctr = ctr_comparators_get( $_GET['ctr'], false, false, null );
			if( $rctr && ria_mysql_num_rows($rctr)==1 ){
				$ctr = ria_mysql_fetch_array( $rctr );
				$ctr_menu_name = $ctr['marketplace'] ? _('Places de marché') : _('Comparateurs');

				print '<a href="/admin/comparators/comparators.php?marketplace='.$ctr['marketplace'].'">'.$ctr_menu_name.'</a> &bull; ';
				print '<a href="/admin/comparators/stats/index.php?ctr='.$ctr['id'].'">'.htmlspecialchars( $ctr['name'] ).'</a> &bull; '._('Filtrer les familles');
			}
		} else {
			print isset($_GET['marketplace']) && $_GET['marketplace'] ? _('Places de marché') : _('Comparateurs');
		}
	} elseif( strstr($php_self, '/admin/comparators/params.php') ){
		print '<a href="/admin/comparators/index.php">'._('Comparateurs').'</a> &bull; ';

		if( isset($_GET['ctr']) && is_numeric($_GET['ctr']) && $_GET['ctr']>0 ){
			$rctr = ctr_comparators_get( $_GET['ctr'], false, false, null );
			if( $rctr && ria_mysql_num_rows($rctr)==1 ){
				$ctr = ria_mysql_fetch_array( $rctr );
				$ctr_menu_name = $ctr['marketplace'] ? _('Places de marché') : _('Comparateurs');

				print '<a href="/admin/comparators/comparators.php?marketplace='.$ctr['marketplace'].'">'.$ctr_menu_name.'</a> &bull; ';
				print '<a href="/admin/comparators/stats/index.php?ctr='.$ctr['id'].'">'.htmlspecialchars( $ctr['name'] ).'</a> &bull; '._('Configuration');
			}
		} else {
			print isset($_GET['marketplace']) && $_GET['marketplace'] ? _('Places de marché') : _('Comparateurs');
		}
	} elseif( strstr($php_self, '/admin/comparators/mapping-attributs.php') ){
		print '<a href="/admin/comparators/index.php">'._('Comparateurs').'</a> &bull; ';

		if( isset($_GET['ctr']) && is_numeric($_GET['ctr']) && $_GET['ctr']>0 ){
			$rctr = ctr_comparators_get( $_GET['ctr'], false, false, null );
			if( $rctr && ria_mysql_num_rows($rctr)==1 ){
				$ctr = ria_mysql_fetch_array( $rctr );
				$ctr_menu_name = $ctr['marketplace'] ? _('Places de marché') : _('Comparateurs');

				print '<a href="/admin/comparators/comparators.php?marketplace='.$ctr['marketplace'].'">'.$ctr_menu_name.'</a> &bull; ';
				print '<a href="/admin/comparators/stats/index.php?ctr='.$ctr['id'].'">'.htmlspecialchars( $ctr['name'] ).'</a> &bull; '._('Configuration');
			}
		} else {
			print isset($_GET['marketplace']) && $_GET['marketplace'] ? _('Places de marché') : _('Comparateurs');
		}
	} else {
		print _('Comparateurs');
	}

}

/**	Cette fonction sert de soutien à view_admin_location et gère le fil d'Ariane des pages dédiées au PIM (catalogue)
 *	\private
 */
function view_admin_location_catalog(){
	global $config;

	$php_self = view_admin_current_page_url();

	// Catalogue
	if( strstr($php_self, '/catalog/exports.php') ){
		print '<a href="index.php">'._('Catégories').'</a> &bull; '._('Exports catalogue');
	}elseif( strstr($php_self,'/brands/index.php') ){
		print '<a href="../index.php">'._('Catégories').'</a> &bull; '._('Marques');
	}elseif( strstr($php_self,'/brands/edit.php') ){
		print '<a href="../index.php">'._('Catégories').'</a> &bull; <a href="index.php">'._('Marques').'</a> ';
		if( !isset($_GET['brd']) || !prd_brands_exists($_GET['brd']) ){
			print ' &bull; '._('Nouvelle marque');
		}
	}elseif( strstr($php_self,'/index.php') && isset($_GET['cat']) && $_GET['cat']==0 ){
		if( isset($_GET['new']) && $_GET['new'] ){
			print '<a href="index.php">'._('Catégories').'</a> &bull; '._('Nouveautés');
		}elseif( isset($_GET['brd']) && $_GET['brd']!=0 ){
			print '<a href="index.php">'._('Catégories').'</a> ';
		}else{
			print _('Catégories');
		}
	}elseif( strstr($php_self, '/catalog/unclassified.php') ){
		print '<a href="/admin/catalog/index.php">'._('Catégories').'</a> &bull; '._('Non classés');
	}elseif( strstr($php_self, '/catalog/products.php') ){
		print '<a href="/admin/catalog/index.php">'._('Catégories').'</a> &bull; '._('Produits');
	}elseif( strstr($php_self, '/catalog/discount/index.php') ){
		print '<a href="/admin/catalog/index.php">'._('Catégories').'</a> &bull; '._('Tarification catalogue');
	}elseif( strstr($php_self, '/catalog/authorizations/index.php') ){
		print '<a href="/admin/catalog/index.php">'._('Catégories').'</a> &bull; '._('Droits d\'accès au catalogue');
	}elseif( strstr($php_self, '/catalog/authorizations/new.php') ){
		$name_user = '';
		if( isset($_GET['usr_fld_id'], $_GET['usr_value']) ){
			$name_user = view_conditions_name( $_GET['usr_fld_id'], $_GET['usr_value'] );
		}
		$wst_id = isset($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ? $_SESSION['websitepicker'] : $config['wst_id'];
		if( trim($name_user) != '' ){
			print '<a href="/admin/catalog/index.php">'._('Catégories').'</a> &bull; <a href="/admin/catalog/authorizations/index.php">'._('Droits d\'accès au catalogue').'</a>';
			print ' &bull; <a href="/admin/catalog/authorizations/edit.php?wst_id='.$wst_id.'&usr_fld_id='.$_GET['usr_fld_id'].'&usr_value='.$_GET['usr_value'].'">'.htmlspecialchars( $name_user ).'</a> &bull; '._('Créer un nouvel accès');
		}else{
			print '<a href="/admin/catalog/index.php">'._('Catégories').'</a> &bull; <a href="/admin/catalog/authorizations/index.php">'._('Droits d\'accès au catalogue').'</a> &bull;  '._('Créer un nouvel accès');
		}
	}elseif( strstr($php_self, '/catalog/authorizations/edit.php') ){
		print '<a href="/admin/catalog/index.php">'._('Catégories').'</a> &bull; <a href="/admin/catalog/authorizations/index.php">'._('Droits d\'accès au catalogue').'</a> &bull; '.htmlspecialchars(view_conditions_name( $_GET['usr_fld_id'], $_GET['usr_value'] ));
	}elseif( strstr($php_self, '/catalog/nomenclature.php') ){
		print '<a href="/admin/catalog/index.php">'._('Catégories').'</a> &bull; '._('Nomenclatures variables');
	}elseif( strstr($php_self, '/catalog/edit-nomenclature.php') ){
		if( isset($_GET['opt']) && $_GET['opt']>0 ){
			print '<a href="/admin/catalog/index.php">'._('Catégories').'</a> &bull; <a href="/admin/catalog/nomenclature.php">'._('Nomenclatures variables').'</a> &bull; ';
		}else{
			print '<a href="/admin/catalog/index.php">'._('Catégories').'</a> &bull; <a href="/admin/catalog/nomenclature.php">'._('Nomenclatures variables').'</a> &bull; '._('Nouvelle nomenclature');
		}
	}else{
		print '<a href="index.php">'._('Catégories').'</a> &bull; ';
	}

	// Options
	if( isset($_GET['opt']) && $_GET['opt'] != 0){
		$nomenclature = prd_options_get($_GET['opt']);
		$r = ria_mysql_fetch_array($nomenclature);
		print htmlspecialchars( $r['name'] );

	}

	// Déplacer des catégories ou des produits
	if( strstr($php_self, '/admin/catalog/move.php') ){

		// Parents
		if( isset($_GET['srccat']) || isset($_POST['srccat']) ){

			$srccat = isset($_GET['srccat']) ? $_GET['srccat'] : $_POST['srccat'];

			// Affiche les catégories parentes
			$parents = prd_categories_parents_get($srccat);
			if( $srccat ){
				while( $r = ria_mysql_fetch_array($parents) ){
				print '<a href="index.php?cat='.$r['id'].'">'.htmlspecialchars($r['title']).'</a> &bull; ';
				}
			}


			// Affiche la catégorie
			$rcat = prd_categories_get($srccat);
			$cat = ria_mysql_fetch_assoc($rcat);
			print '<a href="index.php?cat=' . $cat['id'] . '">' . htmlspecialchars($cat['title']) . '</a> &bull; ';

		}

		// Titre de la page
		print _('Déplacer des catégories ou des produits');

	}


	// Si on se trouve dans une sous-catégorie
	if( isset($_GET['cat']) ){
		if($_GET['cat']!=0){

			// Catégorie
			$parents = prd_categories_parents_get($_GET['cat']);
			while( $r = ria_mysql_fetch_array($parents) ){
				print '<a href="index.php?cat='.$r['id'].'">'.htmlspecialchars($r['title']).'</a> &bull; ';
			}

			$categ = prd_categories_get($_GET['cat']);
			if( $r = ria_mysql_fetch_array($categ) ){
				if( isset($_GET['prd']) || isset($_GET['prdname']) ){
					print '<a href="index.php?cat='.$r['id'].'">'.htmlspecialchars($r['title']).'</a> &bull; ';
				}elseif( strstr($php_self, '/catalog/edit.php') ){
					print '<a href="index.php?cat='.$r['id'].'">'.htmlspecialchars($r['title']).'</a> &bull; '._('Edition');
				}else{
					print htmlspecialchars($r['title']);
				}
			}

			// Produit
			if( isset($_GET['prd']) || isset($_GET['prdname']) ){
				if( isset($_GET['prdname']) ){
					print _('Nouveau produit');
				}else{
					$rlprd = prd_products_get_simple( $_GET['prd'] );
					if( $rlprd && ria_mysql_num_rows($rlprd) ){
						$lprd = ria_mysql_fetch_assoc( $rlprd );

						if( strstr($php_self,'/images.php') ){
							print '<a href="product.php?cat='.$_GET['cat'].'&amp;prd='.$_GET['prd'].'">'.$lprd['ref'].' - '.htmlspecialchars($lprd['title']).'</a> &bull; '._('Images');
						}else{
							print htmlspecialchars( $lprd['ref'] ).' - '.htmlspecialchars($lprd['title']);
						}
					}else{
						print _('Fiche produit');
					}
				}
			}
		}elseif( !isset($_GET['prd']) || !is_numeric($_GET['prd']) || $_GET['prd'] <= 0 ){
			print _('Nouveau produit');
		}else{
			// Produit dans catégorie non classée
			if( isset($_GET['prd']) ){
				print '<a href="/admin/catalog/unclassified.php">'._('Non classés').'</a> &bull; ';

				$rlprd = prd_products_get_simple( $_GET['prd'] );
				if( $rlprd && ria_mysql_num_rows($rlprd) ){
					$lprd = ria_mysql_fetch_assoc( $rlprd );
					print $lprd['ref'].' - '.htmlspecialchars($lprd['title']);
				}else{
					print _('Fiche produit');
				}
			}
		}
	}

	if( isset($_GET['brd']) ){
		if( $_GET['brd']==-1 ){
			print ' &bull; '._('Produits sans marque');
		}elseif( $_GET['brd']>0 ){
			$brd = ria_mysql_fetch_array(prd_brands_get($_GET['brd']));
			print ' &bull; '.htmlspecialchars($brd['title']);
		}
	}

	// Relevés de linéaires
	if (strstr($php_self, '/catalog/linear-raised/')) {
		if (strstr($php_self, '/catalog/linear-raised/edit.php') || strstr($php_self, '/catalog/linear-raised/section.php')) {
			print '<a href="/admin/catalog/linear-raised/">'._('Gestion des assortiments').'</a>  &bull; ';
			if (defined('ADMIN_PAGE_TITLE')) {
				print ADMIN_PAGE_TITLE;
			}else{
				print _('Nouvel assortiment de produits');
			}
		}else{
			print _('Gestion des assortiments');
		}
	}

}
/// @}