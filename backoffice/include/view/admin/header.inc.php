<?php

/** Cette fonction permet l'affichage du menu "mon compte"
 * @return	string	HTML du menu "mon commpte"
 */
function view_admin_header_account()
{
	global $config;

	$rmenu = adn_menu_get(false, false, false, '_MDL_OPTIONS');
	$first = $last = '?';

	if (isset($_SESSION['usr_firstname'], $_SESSION['usr_lastname'])) {
		$first = substr(trim($_SESSION['usr_firstname']), 0, 1);
		$last = substr(trim($_SESSION['usr_lastname']), 0, 1);
	}


	$html = '
		<div class="my-account">
			<button title="' . _('Mon compte') . '" class="my-account-btn btn">' . $first . $last . '</button>
			<nav class="my-account-dropdown"><button class="btn btn-close"></button>
	';

	if (ria_mysql_num_rows($rmenu)) {
		$html .= '<ul>';

		while ($menu = ria_mysql_fetch_assoc($rmenu)) {

			if (!gu_users_admin_rights_used($menu['code'])) {
				continue;
			}

			// Le menu Modération ne peut apparaître que s'il y a un ou plusieurs sites websites
			if ($menu['code'] == '_MENU_OPTIONS_MODERATION' && !tnt_tenants_have_websites()) {
				continue;
			}

			// Le menu "Désactiver le cache" ne peut apparaître que s'il y a un ou plusieurs sites websites
			if ($menu['code'] == '_MENU_OPTIONS_NOCACHE' && !tnt_tenants_have_websites()) {
				continue;
			}

			$rsubmenu = adn_menu_get(false, false, false, $menu['code']);
			$shtml = '';

			if (ria_mysql_num_rows($rsubmenu)) {
				$shtml = '<ul>';

				while ($submenu = ria_mysql_fetch_assoc($rsubmenu)) {
					if (!gu_users_admin_rights_used($submenu['code'])) {
						continue;
					}

					$shtml .= '<li><a href="' . $submenu['href'] . '">' . htmlspecialchars(_($submenu['name'])) . '</a></li>';
				}
				$shtml .= '</ul>';
			}
			$html .= '
				<li>
					<a href="' . $menu['href'] . '">' . htmlspecialchars(_($menu['name'])) . '</a>
					' . $shtml . '
				</li>
			';
		}


		$html .= '</ul>';
	}

	// Ajoute les options "Droits d'accès", "Changer d'instance" et "Partager l'url" réservées aux super-administrateurs
	if (isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id'] == 0) {
		$html .= '
			<a class="notice-super-admin my-account-rights" href="/options/rights.php" title="En tant que super-administrateur, vous pouvez gérer les droits de l\'instance">' . _('Gestion des droits d\'accès') . '</a>
			<a class="notice-super-admin my-account-maintenance" href="/maintenance/" title="Page de maintenance">' . _('Page de maintenance') . '</a>
			<a class="notice-super-admin my-account-tenant" href="#" onclick="return showPopupSelectTenant(false)" title="En tant que super-administrateur, vous pouvez changer d\'instance">' . _('Changer d\'instance') . '</a>
			<a class="notice-super-admin my-account-share" href="#" onclick="return shareTenantUrl('.$config['tnt_id'].')" title="En tant que super-administrateur, vous pouvez partager l\'URL de cette page">' . _('Partager l\'URL') . '</a>
		';
	}

	$html .= '
				<a href="/admin/exit.php" class="my-account-logout">' . _('Déconnexion') . '</a>
			</nav>
		</div>
	';

	return $html;
}