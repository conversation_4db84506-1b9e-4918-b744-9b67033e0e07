<?php
require_once 'view/admin/JStorage.inc.php';

/**
 * Cette classe permet de gérer le menu principal de l'interface d'administration de Riashop
 */
final class SidebarMenu
{

	private $menu = []; ///< Entrées du menu

	private $minor = []; ///< Menu secondaire

	private static $instance = null; ///< L'instance

	/** Constructeur
	 */
	private function __construct()
	{
		$this->addHelpToMenu();
		$this->menu();
	}

	/**
	 * Cette méthode permet d'instancier MainMenu (Singleton)
	 *
	 * @return	object	L'objet en cours
	 */
	public static function getInstance()
	{

		if (!self::$instance) {
			self::$instance = new SidebarMenu();
		}
		return self::$instance;
	}

	/**	Ajoute les liens d'aide en ligne au menu. La présence de certains liens dépend :
	 * 	- de l'activation ou non de Yuto
	 *  - de la présence d'un ou plusieurs sites web
	 */
	private function addHelpToMenu(){

		$submenu = [];

		if( tnt_tenants_have_yuto() ){

			$submenu[] = [
				'title'		=> _('Premiers pas sur l\'administration Yuto'),
				'name'		=> _('Premiers pas sur l\'administration Yuto'),
				'href'		=> 'https://support.riashop.fr/yuto-crm/aide-administration-yuto/premiers-pas-administration-yuto/',
				'target'	=> '_blank'
			];

			$submenu[] = [
				'title'		=> _('Premiers pas sur l\'application Yuto'),
				'name'		=> _('Premiers pas sur l\'application Yuto'),
				'href'		=> 'https://support.riashop.fr/yuto-crm/aide-application-yuto/premiers-pas-application-yuto/',
				'target'	=> '_blank'
			];

		}

		$submenu[] = [
			'title'		=> _('Accéder au site support'),
			'name'		=> _('Accéder au site support'),
			'href'		=> 'https://support.riashop.fr/',
			'target'	=> '_blank'
		];

		$submenu[] = [
			'title'		=> _('Contacter le service client'),
			'name'		=> _('Contacter le service client'),
			'href'		=> 'https://support.riashop.fr/contacter-service-client/',
			'target'	=> '_blank'
		];

		if( tnt_tenants_have_websites() ){

			$submenu[] = [
				'title'		=> _('Nos formations RiaShop'),
				'name'		=> _('Nos formations RiaShop'),
				'href'		=> 'https://www.riashop.fr/formations',
				'target'	=> '_blank'
			];

		}

		$submenu[] = [
			'title'		=> _('Informations légales'),
			'name'		=> _('Informations légales'),
			'href'		=> 'https://legal.riashop.fr/',
			'target'	=> '_blank'
		];

		$this->minor['_MDL_SUPPORT'] = [
			'code'			=> '_MDL_SUPPORT',
			'name'			=> _('Aide'),
			'title'			=> _('Aide et support'),
			'class'			=> '',
			'href'			=> 'https://support.riashop.fr/',
			'target'		=> '_blank',
			'submenu'		=> $submenu
		];

	}

	/** Cette méthode permet de récupérer le menu principal et le stocke dans $this->menu.
	 * 	Le menu est directement dépendant de l'utilisateur connecté et des droits d'accès dont il dispose.
	 * 	@return void
	 */
	private function menu()
	{
		global $config;

		$package = 'enterprise';

		// On récupère la formule d'inscription à Yuto
		if (gu_users_admin_rights_used('_MENU_OPTIONS_SUBSCRIPTION')) {
			$package = RegisterGCP::getPackage($config['tnt_id']);
		}

		// Charge le menu depuis la base de données
		$r_menu = adn_menu_get(false, false, null);
		if (!ria_mysql_num_rows($r_menu)) {
			return;
		}

		while ($menu = ria_mysql_fetch_assoc($r_menu)) {

			if (!gu_users_admin_rights_used($menu['code']) || in_array($menu['code'], ['_MDL_HOME', '_MDL_OPTIONS', '_MDL_SEARCH'])) {
				continue;
			}

			switch ($menu['code']) {

				case '_MDL_FDV':
					$menu['name'] = $menu['title'] = 'Yuto';

					if ($package == 'business') {
						$menu['name'] = $menu['title'] = 'Yuto Business';
					}
					break;

				case '_MDL_TOOLS':

					if (in_array($package, ['business'])) {
						$menu['name'] = $menu['title'] = _('Imports');
						$menu['href'] = '/admin/tools/imports/index.php';
					}
					break;

				case '_MDL_CUSTOMERS':

					if (in_array($package, ['business'])) {
						$menu['name'] = $menu['title'] = _('Comptes');
					}
					break;
			}
			$submenu = $this->getSubmenu($menu['code']);

			// Les menus Configuration et Synchronisation vont dans le menu "Mineur" et pas dans le menu principal
			if( in_array($menu['code'], ['_MDL_CONFIG']) || in_array($menu['code'], ['_MDL_SYNC']) ){
				$this->minor[$menu['code']] = $menu;
				$this->minor[$menu['code']]['submenu'] = $submenu;
				continue;
			}

			$this->menu[$menu['code']] = $menu;
			$this->menu[$menu['code']]['submenu'] = $submenu;
		}

		// Permet de passer les données du menu à JavaScript
		JStorage::getInstance()->add('menu', [
			'main'		=> $this->menu,
			'second'	=> $this->minor
		]);
	}

	/**	Cette méthode permet de récupérer un tableau contenant les entrées du sous-menu d'un menu (à partir de son code)
	 *
	 * @param	string	$code	Obligatoire, code du menu
	 * @return	array	Tableau contenant les entrées du sous-menu
	 */
	private function getSubmenu($code)
	{

		if (!is_string($code)) {
			return [];
		}
		$submenu = [];
		$code = trim($code);
		$rsubmenu = adn_menu_get(false, false, false, $code);

		if (!ria_mysql_num_rows($rsubmenu)) {
			return [];
		}

		while ($sub = ria_mysql_fetch_assoc($rsubmenu)) {

			if (!gu_users_admin_rights_used($sub['code']) || in_array($sub['code'], ['_MENU_ORDERS_SEARCH', '_MENU_ORDERS_RETURNS_SEARCH'])) {
				continue;
			}
			$submenu[$sub['code']] = $this->parseEntry($sub);
		}
		return $submenu;
	}

	/**	Cette méthode permet d'analyser une entrée de menu
	 *
	 * @param	array	$entry	Obligatoire, tableau contenant les informations de l'entrée
	 * @return	array	Tableau "amélioré" contenant les informations de l'entrée
	 */
	private function parseEntry($entry)
	{
		global $config;

		if (!is_array($entry) || !isset($entry['code']) || !is_string($entry['code'])) {
			return [];
		}
		$entry['code'] = trim($entry['code']);

		switch ($entry['code']) {
			case '_MENU_ORDERS_SEARCH':
				$entry['placeholder'] = _('Rechercher une commande');
				break;

			case '_MENU_ORDERS_RETURNS_SEARCH':
				$entry['placeholder'] = _('Rechercher un retour');
				break;

			case '_MENU_PROMO_SOLDES':
			case '_MENU_PROMO_REWARDS':
				$is_rewards = $entry['code'] == '_MENU_PROMO_REWARDS';
				$rtype = $is_rewards ? pmt_types_get(6, false) : pmt_types_get();

				if (!$rtype) {
					break;
				}
				$entry['submenu'] = [];

				while ($type = ria_mysql_fetch_array($rtype)) {
					if (!$is_rewards && $type['id'] != _PMT_TYPE_SOLDES) {
						continue;
					}
					$entry['submenu'][] = [
						'name'	=> _($type['name']),
						'href'	=> htmlspecialchars($entry['href'] . '?type=' . $type['id'])
					];
				}
				break;

			case '_MENU_TOOLS_NEWS_ARCHIVED':
				// Affiche les catégories de premier niveau
				$rcat = news_categories_get();

				if (!ria_mysql_num_rows($rcat)) {
					break;
				}
				$entry['submenu'] = [];

				while ($c = ria_mysql_fetch_assoc($rcat)) {

					$entry['submenu'][] = [
						'name'	=> htmlspecialchars($c['name']),
						'href'	=> htmlspecialchars('/admin/tools/news/index.php?cat=' . $c['id'])
					];
				}
				break;

			case '_MENU_COMPARATORS':
				$i = 0;
				$r_ctr = ctr_comparators_get(0, true, false, false);

				if (!ria_mysql_num_rows($r_ctr)) {
					break;
				}
				$entry['submenu'] = [];

				while ($ctr = ria_mysql_fetch_array($r_ctr)) {
					$i++;

					if ($i > 5) {
						$entry['submenu'][] = [
							'name'	=> _('Tous les comparateurs'),
							'href'	=> '/admin/comparators/comparators.php'
						];
						break;
					}
					$entry['submenu'][] = [
						'name'	=> htmlspecialchars($ctr['name']),
						'href'	=> htmlspecialchars('/admin/comparators/stats/index.php?ctr=' . $ctr['id'])
					];
				}
				break;

			case '_MENU_MARKETPLACE':
				$i = 0;
				$r_mtk = ctr_comparators_get(0, true, false, true);

				if (!ria_mysql_num_rows($r_mtk)) {
					break;
				}
				$entry['submenu'] = [];

				while ($ctr = ria_mysql_fetch_array($r_mtk)) {
					$i++;

					if ($i > 5) {
						$entry['submenu'][] = [
							'name'	=> _('Toutes les places de marché'),
							'href'	=> htmlspecialchars('/admin/comparators/comparators.php?marketplace=1')
						];
						break;
					}
					$entry['submenu'][] = [
						'name'	=> htmlspecialchars($ctr['name']),
						'href'	=> htmlspecialchars('/admin/comparators/stats/index.php?marketplace=1&ctr=' . $ctr['id'])
					];
				}
				break;

			case '_MENU_CATALOG_CATEG':

				$entry['submenu'] = [];

				$entry['submenu'][] = [
					'name'	=> _('Nouveautés'),
					'href'	=> _('/admin/catalog/index.php?new=1')
				];

				$rtop = prd_categories_get(0);

				if( !ria_mysql_num_rows($rtop)){
					break;
				}

				while( $r = ria_mysql_fetch_assoc($rtop) ){
					$entry['submenu'][] = [
						'name'	=> htmlspecialchars($r['title']),
						'href'	=> htmlspecialchars('/admin/catalog/index.php?cat='.$r['id'])
					];
				}
				break;

			case '_MENU_CUSTOMERS_ALL':
				require_once 'users.inc.php';
				$profiles = gu_profiles_get( false, false, false, false, null, true );

				if( !ria_mysql_num_rows($profiles) ){
					break;
				}
				$entry['submenu'] = [];

				while( $p = ria_mysql_fetch_assoc($profiles) ){
					$entry['submenu'][] = [
						'name'	=> htmlspecialchars($p['pl_name']),
						'count'	=> $p['users'],
						'href'	=> htmlspecialchars('/admin/customers/index.php?prf='.$p['id'])
					];
				}
				break;

			case '_MENU_CUSTOMERS_SEGMENTS':
				require_once 'segments.inc.php';
				// Charge la liste des segments et affiche les 12 premiers
				$segments = seg_segments_get( 0, CLS_USER );
				$count = ria_mysql_num_rows($segments);

				if( !$count ){
					break;
				}
				$entry['submenu'] = [];
				$i = 1;

				while( $s = ria_mysql_fetch_assoc($segments) ){
					if( $i > 12 ){
						break;
					}
					$entry['submenu'][] = [
						'name'	=> htmlspecialchars($s['name']),
						'href'	=> htmlspecialchars('/admin/customers/index.php?seg='.$s['id'])
					];
					$i++;
				}
				if( $count>12 ){
					$entry['submenu'][] = [
						'name'	=> _('Tous les segments'),
						'href'	=> htmlspecialchars('/admin/customers/segments/index.php')
					];
				}
				break;

			case '_MENU_ORDERS_RETURNS':
				require_once 'ord.returns.inc.php';
				$states = ord_returns_states_get();

				if( !ria_mysql_num_rows($states) ){
					break;
				}
				$entry['submenu'] = [];

				while ($state = ria_mysql_fetch_assoc($states)) {
					$entry['submenu'][] = [
						'name'	=> htmlspecialchars($state['name_plural']),
						'href'	=> htmlspecialchars('/admin/orders/returns/returns.php?state='.$state['id'])
					];
				}
				break;

			case '_MENU_PROMO_PRODUCTS':
				$rtype = pmt_types_get();

				if( !ria_mysql_num_rows($rtype) ){
					break;
				}
				$entry['submenu'] = [];

				while( $type = ria_mysql_fetch_assoc($rtype) ){
					if ($type['id'] == _PMT_TYPE_SOLDES) {
						continue;
					}
					$entry['submenu'][] = [
						'name'	=> htmlspecialchars($type['name']),
						'href'	=> htmlspecialchars('/admin/promotions/specials/index.php?type='.$type['id'])
					];
				}
				break;

			case '_MENU_DOCS_TYPES':
				$types = doc_types_get(0,true);

				if( $types === false || !ria_mysql_num_rows($types) ){
					break;
				}
				$entry['submenu'] = [];

				while( $t = ria_mysql_fetch_assoc($types) ){
					$entry['submenu'][] = [
						'name'	=> htmlspecialchars($t['name']),
						'href'	=> htmlspecialchars('/admin/documents/index.php?type='.$t['id'])
					];
				}
				break;

			case '_MENU_DOCS_HOSTS':
				$rhosts = doc_hosts_get();

				if( !ria_mysql_num_rows($rhosts) ){
					break;
				}
				$entry['submenu'] = [];

				while( $host = ria_mysql_fetch_assoc($rhosts) ){
					$entry['submenu'][] = [
						'name'	=> htmlspecialchars($host['name']),
						'href'	=> htmlspecialchars('/admin/documents/medias/index.php?hst='.$host['id'])
					];
				}
				break;

			case '_MENU_TOOLS_NEWSLETTER':
				require_once 'newsletter.inc.php';

				// Affiche les 5 premières catégories de newsletters
				$rcats = nlr_categorie_get();
				$count = ria_mysql_num_rows($rcats);

				if( !$count ){
					break;
				}
				$i = 0;
				$entry['submenu'] = [];

				while( $cat = ria_mysql_fetch_assoc($rcats) ){
					$i++;

					if( $i > 5 ){
						break;
					}
					$entry['submenu'][] = [
						'name'	=> htmlspecialchars($cat['cat']),
						'href'	=> htmlspecialchars('/admin/tools/newsletter/list.php?oc='.$cat['id'])
					];
				}

				if( $count > 5 ){
					$entry['submenu'][] = [
						'name'	=> _('Voir plus de newsletter'),
						'href'	=> '/admin/tools/newsletter/index.php'
					];
				}
				break;

			case '_MENU_MODERATION':
				$rtypes = gu_messages_types_get();

				if( !ria_mysql_num_rows($rtypes) ){
					break;
				}
				$entry['submenu'] = [];

				while( $type = ria_mysql_fetch_assoc($rtypes) ){

					$entry['submenu'][] = [
						'name'	=> htmlspecialchars($type['name-pl']),
						'href'	=> htmlspecialchars('/admin/moderation/moderation.php?type='.$type['code'])
					];
				}
				break;

			case '_MENU_MODERATION_PRD':
				$products = fld_update_products_get();

				if( $products ===false || !ria_mysql_num_rows($products) ){
					break;
				}
				$entry['submenu'] = [];

				while( $p = ria_mysql_fetch_assoc($products) ){

					$entry['submenu'][] = [
						'name'		=> htmlspecialchars($p['name']),
						'ref'		=> $p['ref'],
						'requests'	=> $p['requests'],
						'href'		=> htmlspecialchars('/admin/moderation/prd_moderation.php?prd='.$p['id'])
					];
				}
				break;

			case '_MENU_COMPARATORS_SEARCH':
				$rflt = ctr_filters_get(0, false);
				$count = ria_mysql_num_rows($rflt);

				if( !$count ){
					break;
				}
				$entry['submenu'] = [];
				$i = 0;

				while( $flt = ria_mysql_fetch_assoc($rflt) ){
					$i++;

					if( $i > 5 ){
						break;
					}
					$entry['submenu'][] = [
						'name'		=> htmlspecialchars($flt['name']),
						'href'		=> htmlspecialchars('/admin/comparators/search/index.php?flt='.$flt['id'])
					];
				}

				if( $count > 5 ){
					$entry['submenu'][] = [
						'name'		=> _('Toutes les recherches'),
						'href'		=> htmlspecialchars('/admin/comparators/search/index.php')
					];

				}
				break;

			case '_MENU_MARKETPLACE_SEARCH':
				$rflt = ctr_filters_get(0, true);
				$count = ria_mysql_num_rows($rflt);

				if( !$count ){
					break;
				}
				$entry['submenu'] = [];
				$i = 0;

				while( $flt = ria_mysql_fetch_assoc($rflt) ){
					$i++;

					if( $i > 5 ){
						break;
					}
					$entry['submenu'][] = [
						'name'		=> htmlspecialchars($flt['name']),
						'href'		=> htmlspecialchars('/admin/comparators/search/index.php?marketplace=1&flt='.$flt['id'])
					];
				}

				if( $count > 5 ){
					$entry['submenu'][] = [
						'name'		=> _('Toutes les recherches'),
						'href'		=> htmlspecialchars('/admin/comparators/search/index.php?marketplace=1')
					];

				}
				break;

			case '_MENU_FDV_REPORTS': // Menu Yuto
				$rtype = rp_types_get();

				if( !ria_mysql_num_rows($rtype) ){
					break;
				}
				$entry['submenu'] = [];

				while( $type = ria_mysql_fetch_assoc($rtype) ){
					$entry['submenu'][] = [
						'name'		=> htmlspecialchars($type['name']),
						'href'		=> htmlspecialchars('/admin/fdv/reports/index.php?type='.$type['id'])
					];
				}
				break;

			case '_MENU_ORDERS_ALL':
				if( in_array($config['tnt_id'], [977, 998, 1043]) ){
					$entry['submenu'] = [
						[
							'name'		=> '0 - Annulée',
							'href'		=> htmlspecialchars('/admin/orders/orders.php?state=10')
						],[
							'name'		=> '1 - En attente de traitement',
							'href'		=> htmlspecialchars('/admin/orders/orders.php?state=4')
						],[
							'name'		=> '2 - En cours de traitement',
							'href'		=> htmlspecialchars('/admin/orders/orders.php?state=5')
						],[
							'name'		=> '3 - En attente de confirmation Conforama',
							'href'		=> htmlspecialchars('/admin/orders/orders.php?state=3')
						],[
							'name'		=> '4 - En attente de validation franchisé',
							'href'		=> htmlspecialchars('/admin/orders/orders.php?state=42')
						],[
							'name'		=> '5 - À confirmer au fournisseur',
							'href'		=> htmlspecialchars('/admin/orders/orders.php?state=20')
						],[
							'name'		=> '6 - À annuler au fournisseur',
							'href'		=> htmlspecialchars('/admin/orders/orders.php?state=43')
						],[
							'name'		=> '7 - Confirmées',
							'href'		=> htmlspecialchars('/admin/orders/orders.php?state=37')
						],[
							'name'		=> '8 - En attente de livraison',
							'href'		=> htmlspecialchars('/admin/orders/orders.php?state=6')
						],[
							'name'		=> '9 - Partiellement expédiée',
							'href'		=> htmlspecialchars('/admin/orders/orders.php?state=32')
						],[
							'name'		=> '10 - Expédiée',
							'href'		=> htmlspecialchars('/admin/orders/orders.php?state=7')
						],[
							'name'		=> '99 - En erreur',
							'href'		=> htmlspecialchars('/admin/orders/orders.php?state=15')
						],
					];
				}else{
					$states = [
						0 => _STATE_BASKET,
						1 => _STATE_PAY_WAIT_CONFIRM,
						2 => _STATE_ORD_WAIT_CONFIRM,
						3 => _STATE_PAY_CONFIRM,
						4 => _STATE_IN_PROCESS,
						5 => _STATE_BL_READY,
						6 => _STATE_BL_EXP,
						7 => _STATE_BL_PARTIEL_EXP,
						8 => _STATE_BL_STORE,
						9 => _STATE_INV_STORE,
						10 => _STATE_INVOICE,
						11 => _STATE_WAIT_PAY,
						12 => _STATE_CANCEL_MERCHAND,
						13 => _STATE_CANCEL_USER,
						14 => _STATE_ARCHIVE,
						15 => _STATE_DEVIS,
						16 => _STATE_CLICK_N_COLLECT,
						17 => _STATE_WAIT_VALIDATION,
						18 => _STATE_REFUSED
					];

					$sort = false;

					$rstate = ord_states_get( $states, false, false, 0, $sort );
					$has_status = isset($config['admin_visible_order_statuses']) && is_array($config['admin_visible_order_statuses']) && !empty($config['admin_visible_order_statuses']);

					if (!ria_mysql_num_rows($rstate)) {
						break;
					}
					$entry['submenu'] = [];

					$pos = 0;
					while($state = ria_mysql_fetch_assoc($rstate)){
						if ( $has_status && !in_array($state['id'], $config['admin_visible_order_statuses']) ) {
							continue;
						}
						$entry['submenu'][$pos] = [
							'name'		=> htmlspecialchars($state['name_plural']),
							'href'		=> htmlspecialchars('/admin/orders/orders.php?state='.$state['id'])
						];
						$pos++;
					}
				}

				break;

			default:

				$submenu = $this->getThirdDepth($entry);

				if( $submenu ){
					$entry['submenu'] = $submenu;
				}

				if ($entry['code'] == '_MENU_CONFIG_ORDERS' && (!isset($config['allow_orders_update_state']) || !$config['allow_orders_update_state'])) {
					return false;
				}

				if ($entry['parent_id'] != 300) {
					break;
				}
				// Récupère l'id de l'état de la commande
				$result = preg_match('/(?<=state=)\d*$/', $entry['href'], $state_id);

				if( $entry['code'] != '_MENU_ORDERS_INTERVENTION' && is_array($state_id) && isset($state_id[0]) && is_numeric($state_id[0])){
					return false;
				}
				break;
		}

		if( isset($entry['submenu']) && is_array($entry['submenu']) && count($entry['submenu'])){
			foreach($entry['submenu'] as $k => $sub){

				if( isset($sub['href']) && riashop_submenu_entry_is_active($sub['href']) ){
					$entry['submenu'][$k]['active'] = true;
					$is_active = true;
					break;
				}

			}
		}

		if( !isset($is_active) && isset($entry['href']) && riashop_submenu_entry_is_active($entry['href']) ){
			$entry['active'] = true;
		}
		return $entry;

	}

	/** Cette méthode permet de récupérer le 3e niveau du menu
	 * @param	array			$parent	Obligatoire, Tableau contenant les informations du parent
	 *
	 * @return	array|bool	Array s'il y a un 3e niveau, false sinon
	 */
	private function getThirdDepth($parent){

		if( !is_array($parent) || !isset($parent['id'], $parent['code']) ){
			return false;
		}
		$rthird = adn_menu_get(false, false, $parent['id']);

		if( !ria_mysql_num_rows($rthird) ){
			return false;
		}
		$submenu = [];

		while( $one = ria_mysql_fetch_assoc($rthird) ){
			if( gu_users_admin_rights_used($one['code']) ){
				$submenu[] = $one;
			}

		}
		return $submenu;

	}

	/** Cette méthode permet de récupérer un tableau contenant les informations du menu principal
	 *
	 * @return	array	Tableau contenant le menu principal
	 */
	public function getMain()
	{
		// Génère le menu s'il ne l'a pas encore été
		if (!count($this->menu)) {
			$this->menu();
		}

		return $this->menu;
	}

	/** Cette méthode permet de récupérer un tableau contenant le menu secondaire
	 *
	 * @return	array	Tableau contenant le menu secondaire (minor)
	 */
	public function getSecond()
	{

		if (!count($this->minor)) {
			$this->menu();
		}
		return $this->minor;
	}
}
