<?php

/**	\ingroup breadcrumbs Fil d'ariane
 *
 *  @{
 */

/**	Cette classe gère le fil d'Ariane du back-office RiaShop. Elle produit un code conforme au standard
 * 	Bootstrap v4.4 tel qu'il est décrit ici : https://getbootstrap.com/docs/4.4/components/breadcrumb/.
 *
 * 	Elle n'est pas destinée à être utilisée dans des sites clients, notamment car sa sortie n'intègre pas
 * 	les microformats que l'on peut attendre pour le référencement naturel.
 *
 * 	Il s'agit d'une classe utilisant le pattern Singleton, qui permet d'éviter d'avoir à créer une variable
 * 	globale pour gérer le fil d'ariane. Elle est conçue de telle façon qu'il soit possible d'ajouter des pages
 * 	au fil d'Ariane depuis plusieurs endroits dans le code.
 *
 * 	Voici un exemple d'utilisation :
 * 	\code{.php}
 * 		print Breadcrumbs::root( 'Accueil', '/' )
 * 			->push( 'Catégories', '/catalog/' )
 * 			->push( 'Produit' );
 * 	\encode
 *
 */
class Breadcrumbs {

	// Contient les éléments du fil d'Ariane. Il s'agit d'un tableau associatif contenant les clés :
	// - [name] : Obligatoire, désignation de la page
	// - [url] : Facultatif, url de la page.
	private $_breadcrumbs;

	// Singleton
	private static $_instance;

	// Constructeur
	private function __construct(){
		$this->_breadcrumbs = array();
	}

	// Remet à zéro les données contenues dans l'instance.
	protected function reset(){
		$this->_breadcrumbs = array();
		return self::$_instance;
	}

	/**	Cette fonction permet l'ajout d'une page clé au fil d'ariane.
	 * 	@param $name Obligatoire, désignation de la page. Si cette chaîne est vide la méthode retourne false.
	 * 	@param $url Facultatif, url de la page. Si non précisé, la page en question ne sera pas cliquable dans le fil d'ariane.
	 * 	@return Breadcrumbs l'instance de la classe, pour permettre d'enchaîner les opérations d'ajout.
	 */
	function push( $name, $url='' ){

		if( !trim($name) ){
			return false;
		}

		if( trim($url) ){
			$this->_breadcrumbs[] = array(
				'name' => $name,
				'url' => $url
			);
		}else{
			$this->_breadcrumbs[] = array(
				'name' => $name
			);
		}

		return self::$_instance;

	}

	/**	Retourne le nombre d'éléments du fil d'ariane. Dans une phase de migration vers cette classe, cela permet de savoir
	 * 	par exemple si le système est renseigné avant de l'afficher, ou bien s'il faut utiliser l'ancien système :
	 * 	\code{.php}
	 * 	if( Breadcrumbs::count() ){
	 * 		print Breadcrumbs::toString();
	 *	}else{
	 *		view_admin_location();
	 *	}
	 *	\endcode
	 * 	@return int le nombre d'éléments
	 */
	public static function count(){

		return sizeof(
			self::getInstance()
			->_breadcrumbs
		);

	}

	/**	Cette fonction permet la définition de la racine du fil d'ariane (point de départ). Lorsqu'elle est utilisée, elle remet à 0
	 * 	toutes les données enregistrées précédemment dans le fil d'Ariane.
	 * 	\code{.php}
	 * 	Breadcrumbs::root( 'Accueil', '/' );
	 * 	if( Breadcrumbs::count() ){
	 * 		print Breadcrumbs::toString();
	 *	}else{
	 *		view_admin_location();
	 *	}
	 *	\endcode
	 * 	@param $name Obligatoire, désignation de la page. Si cette chaîne est vide la méthode retourne false.
	 * 	@param $url Facultatif, url de la page. Si non précisé, la page en question ne sera pas cliquable dans le fil d'ariane.
	 * 	@return Breadcrumbs l'instance de la classe, pour permettre d'enchaîner les opérations d'ajout.
	 */
	public static function root( $name, $url='' ){
		return self::getInstance()->reset()->push( $name, $url );
	}

	/** Permet l'ajout d'éléments au fil d'ariane sans pour autant modifier la racine ou remettre en cause les données déjà stockées
	 * 	@param $name Obligatoire, désignation de la page. Si cette chaîne est vide la méthode retourne false.
	 * 	@param $url Facultatif, url de la page. Si non précisé, la page en question ne sera pas cliquable dans le fil d'ariane.
	 * 	@return Breadcrumbs l'instance de la classe, pour permettre d'enchaîner les opérations d'ajout.
	 */
   public static function add( $name, $url='' ){
		return self::getInstance()->push( $name, $url );
	}

	// Converti le fil d'ariane en code HTML
	public function __toString(){

		$toString = '<nav aria-label="breadcrumb"><ol class="breadcrumb">';

		// Affiche les éléments menant à la page en cours, avec un lien permettant le retour
		for( $i=0; $i<sizeof($this->_breadcrumbs)-1; $i++ ){

			$b = $this->_breadcrumbs[ $i ];

			$toString .= '<li class="breadcrumb-item">';
			if( isset($b['url']) ){
				$toString .= '<a href="'. $b['url'] .'">';
			}
			$toString .= htmlspecialchars( $b['name'] );
			if( isset($b['url']) ){
				$toString .= '</a>';
			}
			$toString .= '</li>';
		}

		// Affiche la page en cours, sans lien
		$toString .=
			'<li class="breadcrumb-item active" aria-current="page">'.
				htmlspecialchars( $this->_breadcrumbs[ $i ]['name'] )
			.'</li>';

		$toString .= '</ol></nav>';

		return $toString;

	}

	// Singleton
	private static function getInstance(){

		if( !self::$_instance ){
			self::$_instance = new Breadcrumbs;
		}

		return self::$_instance;

	}

	/**	Converti le fil d'ariane stocké dans la classe en code HTML compatible Bootstrap
	 *
	 */
	public static function toString(){

		return self::getInstance()->__toString();

	}

}

/// @}