<?php
  require_once('goals.inc.php');

	/** Cette fonction retourne le html du tableau contenant les objectif d'un utilisateur pour un KPI (utilisé dans l'onglet objectif de la fiche client)
	 *  @param int $usr_id Obligatoire, identifiant d'un utilisateur
	 *  @param int $obj_id Obligatoire, identifiant d'un objectif
	 *  @param int $period_year Obligatoire, année pour laquelle on veut voir les objectifs
	 * 	@param array $objs Optionnel, tableau permettant de filtrer sur un objet (cls_id => type de d'objet, obj_id_0 => objet recherché)
   *  @param bool $in_popup Optionnel, permet de spécifier que l'appel est fait depuis la popup de gestion des objectifs
	 * 	@param null|int $seller_id Optionnel, identifiant d'un représentant (lorsque l'objectif porte sur un client en particulier)
	 * 	@param bool $show_period_perso Optionnel, par défaut la gestion de période personnalisée est activé, mettre false pour la désactiver
	 * 	@param bool $change_year Optionnel par défaut le changement d'année est possible, mettre false pour la désactiver
	 *
	 *  @return string l'html contenant les objectifs
	 */
	function view_goals( $usr_id, $obj_id, $period_year, $objs=[], $in_popup=false, $seller_id=null, $show_period_perso=true, $change_year=true ){
		if (!is_numeric($usr_id) || $usr_id <= 0) {
			return false;
		}

		if (!is_numeric($obj_id) || $obj_id <= 0) {
			return false;
		}

		if (!is_numeric($period_year) || $period_year <= 0) {
			return false;
		}

		$cls_id = 0; $obj_ids = 0;

		if( is_array($objs) && count($objs) ){
			if( !ria_array_key_exists(['cls_id', 'obj_id_0'], $objs) ){
				return false;
			}

			if( !in_array($objs['cls_id'], [CLS_PRODUCT, CLS_CATEGORY, CLS_BRAND]) ){
				return false;
			}

			if( !is_numeric($objs['obj_id_0']) || $objs['obj_id_0'] <= 0 ){
				return false;
			}

			$cls_id = $objs['cls_id'];
			$obj_ids = $objs['obj_id_0'];
		}elseif( $objs === false ){
			$cls_id = 0;
		}

		$r_obj = obj_objectifs_get_kpi( $obj_id, $objs );
		if (!$r_obj || !ria_mysql_num_rows($r_obj)) {
			return false;
		}

		$obj = ria_mysql_fetch_assoc($r_obj);

		ob_start();

		{ //Récupère les performances du représentant
			$r_periods = obj_periods_get( $usr_id, $obj_id, $period_year, null, null, $seller_id, $cls_id, $obj_ids );
			if( $r_periods && ria_mysql_num_rows($r_periods) ){
				while ($periods = ria_mysql_fetch_assoc($r_periods)) {
					if ($periods['value'] == 0) {
						continue;
					}

					$n_month = date( "m", strtotime($periods['date_start']) );

					if ($obj['type'] == 'int') {
						$periods['value'] = (int) $periods['value'];
					}

					//Récupère les objectifs mensuels
					if ($periods['date_type'] == 'month') {
						$month[(int) $n_month] = $periods['value'];
					}

					//Récupère les objectifs trimestriels
					if ($periods['date_type'] == 'trimester') {
						$trimester[(int) ($n_month + 2) / 3] = $periods['value'];
					}

					// Récupère les objectifs semestriels
					if( $periods['date_type'] == 'semester' ){
						$semester[ $n_month <= 6 ? 1 : 2 ] = $periods['value'];
					}

					//Récupère l'objectif annuel
					if ($periods['date_type'] == 'year') {
						$year = $periods['value'];
					}
				}
			}

			$perf = array();

			//Récupère les performances mensuelles
			$unite = 'euro';
			for ($i = 1; $i <= 12; $i++) {
				$perf['month_'.$i] = 0;

				$first_day_of_month = date("Y-m-d", gmmktime(0, 0, 0, $i, 1, $period_year));
				$last_day_of_month = date("Y-m-d", gmmktime(0, 0, 0, $i + 1, 0, $period_year));

				$res = stats_kpi_get($usr_id, $first_day_of_month, $last_day_of_month, array($obj_id), $objs, $seller_id);
				if ($res[$obj_id]) {
					$perf['month_'.$i] = $res[$obj_id];
				}
			}

			//Récupère les performances trimestrielles
			$perf['trimester_1'] = $perf['month_1']  + $perf['month_2']  + $perf['month_3'];
			$perf['trimester_2'] = $perf['month_4']  + $perf['month_5']  + $perf['month_6'];
			$perf['trimester_3'] = $perf['month_7']  + $perf['month_8']  + $perf['month_9'];
			$perf['trimester_4'] = $perf['month_10'] + $perf['month_11'] + $perf['month_12'];

			// Récupère les performances semestriels
			$perf['semester_1'] = $perf['trimester_1'] + $perf['trimester_2'];
			$perf['semester_2'] = $perf['trimester_3'] + $perf['trimester_4'];

			//Récupère la performance annuelle
			$perf['year'] = $perf['semester_1'] + $perf['semester_2'];

			foreach( $perf as &$one_perf ){
				if ($unite == 'second') {
					$one_perf = str_replace(array(' ', ','), array('', '.'), $one_perf);
					$one_perf = ria_seconds_to_time($one_perf, 6, '+ de ');

					if (trim($one_perf) == '') {
						$one_perf = 0;
					}
				} elseif ($unite == 'euro') {
					$one_perf = number_format($one_perf, 2, ',', ' ');
				}
			}
		}

		$pref_sufixe = $sufixe = '';
		switch ($obj['unite']) {
			case 'second' : $sufixe = ' s'; break;
			case 'euro' :   $pref_sufixe = $sufixe = ' € HT'; break;
		}

		?><form method="POST" action="admin/index.php">
			<input type="hidden" name="obj_id" id="obj_id" value="<?php print $_GET['goal']; ?>">
			<input type="hidden" name="goal-year" id="goal-year" value="<?php print $period_year; ?>">

			<h3><?php print _($obj['name']); ?></h3>

      <?php
        if( !$in_popup ){
          print '<div class="notice">'
            .'<p>'._('Vous pouvez paramétrer ci-dessous l\'objectif par période mensuelle, trimestrielle ou annuelle. Vous pouvez définir une période de date à date en utilisant la zone "Périodes personnalisées".').'</p>'

            .'<p>'.str_replace(
              '#param[url]#',
              '/admin/customers/edit.php?usr='.$_GET['usr'].'&tab=goals',
              _('Si vous souhaitez revenir à la liste des objectifs cliquez sur le lien suivant : <a href="#param[url]#">Revenir à la liste des objectifs</a>.')
            ).'</p>'
          .'</div><br />';
				}

				print '<table id="tb-goals" class="checklist">'
					.'<caption class="align-center">';

						if( $change_year ){
							print '<img id="goal-previous-year" class="hld-nav-year float-left" title="Année '.( $period_year - 1 ).'" alt="Année '.( $period_year - 1 ).'" src="/admin/images/expeditions/feries_active_left.svg" onclick="reload_goal('.$obj_id.', '.( $period_year - 1 ).')">';
						}

						print str_replace( '%d', $period_year, _('Année %d') );

						if( $change_year ){
							print '<img id="goal-next-year" class="hld-nav-year float-right" title="Année '.( $period_year + 1 ).'" alt="Année '.( $period_year + 1 ).'" src="/admin/images/expeditions/feries_active_right.svg" onclick="reload_goal('.$obj_id.', '.( $period_year + 1 ).')">';
						}

					print '</caption>'
					.'<thead>'
						.'<tr>'
							.'<th class="col300px">'._('Période').'</th>'
							.'<th class="col180px align-right">'._('Performance').'</th>'
							.'<th class="col180px align-center">'._('Objectif').'</th>'
						.'</tr>'
					.'</thead>'
					.'<tbody>'
						.'<tr>'
							.'<th colspan="3">'._('Objectifs mensuels').'</th>'
						.'</tr>';

						$ar_month = array(
							1 => _('Janvier'),
							2 => _('Février'),
							3 => _('Mars'),
							4 => _('Avril'),
							5 => _('Mai'),
							6 => _('Juin'),
							7 => _('Juillet'),
							8 => _('Août'),
							9 => _('Septembre'),
							10 => _('Octobre'),
							11 => _('Novembre'),
							12 => _('Décembre'),
						);

						foreach ($ar_month as $key => $n_month) {
							?><tr>
								<td data-label="<?php print _('Période :'); ?> "><?php print $n_month; ?></td>
								<td class="seller-perf" data-label="<?php print _('Performance :'); ?> "><?php print(isset($perf['month_'.$key]) ? $perf['month_'.$key] : '--').$pref_sufixe; ?></td>
								<td class="align-center" data-label="<?php print _('Objectif :'); ?> ">
									<span class="obj-value-input">
										<input data-type="<?php print $obj['type']; ?>" class="obj-value-month price" name="month[<?php print $key; ?>]" type='text' value="<?php print(isset($month[$key]) ? $month[$key] : ''); ?>" placeholder="--" /><?php print $sufixe; ?>
									</span>
								</td>
							</tr><?php

						}
					?>

					<tr>
						<th colspan="3"><?php print _('Objectifs trimestriels'); ?></th>
					</tr>

					<?php
						$ar_trimester = array(
							1 => _('1<sup>er</sup> trimestre'),
							2 => _('2<sup>eme</sup> trimestre'),
							3 => _('3<sup>eme</sup> trimestre'),
							4 => _('4<sup>eme</sup> trimestre'),
						);

						foreach ($ar_trimester as $key => $n_trimester) {
							?><tr>
								<td data-label="<?php print _('Période :'); ?> "><?php print $n_trimester; ?></td>
								<td class="seller-perf" data-label="<?php print _('Performance :'); ?> "><?php print(isset($perf['trimester_'.$key]) ? $perf['trimester_'.$key] : '--').$pref_sufixe; ?></td>
								<td class="align-center" data-label="<?php print _('Objectif :'); ?> ">
									<span class="obj-value-input">
										<input data-type="<?php print $obj['type']; ?>" class="obj-value-trimester price" name="trimester[<?php print $key; ?>]" type='text' value="<?php print(isset($trimester[$key]) ? $trimester[$key] : ''); ?>"placeholder="--" /><?php print $sufixe; ?>
									</span>
								</td>
							</tr><?php
						}
					?>

					<tr>
						<th colspan="3"><?php print _('Objectifs semestriels'); ?></th>
					</tr>

					<?php
						$ar_semester = [
							1 => _('1<sup>er</sup> semestre'),
							2 => _('2<sup>eme</sup> semestre'),
						];

						foreach( $ar_semester as $key => $n_semester) {
							?><tr>
								<td data-label="<?php print _('Période :'); ?> "><?php print $n_semester; ?></td>
								<td class="seller-perf" data-label="<?php print _('Performance :'); ?> "><?php
									print(isset($perf['semester_'.$key]) ? $perf['semester_'.$key] : '--').$pref_sufixe;
								?></td>
								<td class="align-center" data-label="<?php print _('Objectif :'); ?> ">
									<span class="obj-value-input">
										<input data-type="<?php print $obj['type']; ?>" class="obj-value-semester price" type="text"
											name="semester[<?php print $key; ?>]"
											value="<?php print(isset($semester[$key]) ? $semester[$key] : ''); ?>"
											placeholder="--"
										/><?php print $sufixe; ?>
									</span>
								</td>
							</tr><?php
						}
					?>

					<tr>
						<th colspan="3"><?php print _('Objectif annuel'); ?></th>
					</tr>

					<tr>
						<td data-label="<?php print _('Période :'); ?> "><?php printf(_('année %d'), $period_year); ?></td>
						<td class="seller-perf" data-label="<?php print _('Performance :'); ?> "><?php print(isset($perf['year']) ? $perf['year'] : '--').$pref_sufixe; ?></td>
						<td class="align-center" data-label="<?php print _('Objectif :'); ?> ">
							<span class="obj-value-input">
								<input data-type="<?php print $obj['type']; ?>" class="obj-value-year price" name="year" type='text' value="<?php print(isset($year) ? $year : ''); ?>" placeholder="--" /><?php print $sufixe; ?>
							</span>
						</td>
					</tr>

					<?php if( $show_period_perso ){ ?>
					<tr id="period-perso">
						<th colspan="3"><?php print _('Périodes personnalisées'); ?></th>
					</tr>
					<?php
						$r_periods_perso = obj_periods_get($usr_id, $obj_id, $period_year, 'perso');

						if( $r_periods_perso ){
							while( $periods_perso = ria_mysql_fetch_assoc($r_periods_perso) ){
								//Récupère la performance du représentant
								$perf = stats_kpi_get($usr_id, $periods_perso['date_start'], $periods_perso['date_end'], array($obj_id), $objs);
								$perf = $perf[$obj_id];

								// Affichage des temps sauvegardés en seconde
								if( $obj['unite'] == 'second' ){
									$perf = str_replace(array(' ', ','), array('', '.'), $perf);
									$perf = ria_seconds_to_time($perf, 6, '+ de ');

									if( trim($perf) == '' ){
										$perf = 0;
									}
								}elseif( $obj['unite'] == 'euro' && $perf > 0 ){
									$perf = number_format($perf, 2, ',', ' ');
								}

								?><tr id="<?php print $periods_perso['id']; ?>">
									<td data-label="<?php print _('Période :'); ?> ">
										<?php print htmlspecialchars($periods_perso['name']); ?> (du <?php print dateheureunparse($periods_perso['date_start'], false); ?> au <?php print dateheureunparse($periods_perso['date_end'], false); ?>)
										<a href="#" data-id="<?php print $periods_perso['id']; ?>" class="del obj-del">Supprimer</a>
									</td>
									<td class="seller-perf" data-label="<?php print _('Performance :'); ?> "><?php print($perf ? $perf : '--').$pref_sufixe; ?></td>
									<td class="align-center" data-label="<?php print _('Objectif :'); ?> ">
										<span class="obj-value-input">
											<input class="obj-value price" name="perso[<?php print $periods_perso['id']; ?>]" type='text' value="<?php print $periods_perso['value'] != 0 ? $periods_perso['value'] : ''; ?>" placeholder="--" /><?php print $sufixe; ?>
										</span>
									</td>
								</tr> <?php
							}
						}
					}
					?>
					<tr id="new-period" style="display : none">
						<td colspan="2" class="align-center">
							<input type="text" id="name-period" placeholder="<?php print _('Nom'); ?>"/>
							<br /><input class="datepicker" type="text" id="start-period" placeholder="<?php print _('Début'); ?>" autocomplete="off" />
							<input class="datepicker" type="text" id="end-period" placeholder="<?php print _('Fin'); ?>" autocomplete="off" />
						</td>
						<td class="align-center">
							<input type="button" id="save-new-period" value="<?php print _('Enregistrer'); ?>" />
							<br /><a class="del" id="cancel-new-period"><?php print _('Annuler'); ?></a>
						</td>
					</tr>
				</tbody>
				<tfoot>
					<?php if( $show_period_perso ){ ?>
						<tr>
							<td class="back-color-white text-center" colspan="3">
								<a href="#" id="add-period"><?php print _('Ajouter une période personnalisée'); ?></a>
							</td>
						</tr>
					<?php } ?>
					<tr>
						<td colspan="3" class="align-right"><input style="margin-right:3px" type="submit" value="<?php print _('Enregistrer'); ?>" name="save-goals"/><input type="button" value="<?php print _('Annuler'); ?>" id="cancel"/></td>
					</tr>
				</tfoot>
			</table>
		</form><?php

		return ob_get_clean();
	}

	/** Cette fonction permet d'afficher le palmarès
	 *  @param string $date_start Facultatif, date de début (jj/mm/aaaa) pour laquelle le palmarès est calculé,
	 *  @param string $date_end Facultatif, date de fin (jj/mm/aaaa) pour laquelle le palmarès est calculé,
	 *  @param int $usr_id Facultatif, identifiant du seller à afficher (par défaut seul les 3 meilleurs représentants de chaque KPI sont montrés)
	 */
	function view_palmares($date_start=null, $date_end=null, $usr_id=0){
		global $config;

		if (isdate($date_start) && isdate($date_end)) {
			$date_start = dateparse($date_start);
			$date_end = dateparse($date_end);
		} else {
			$date_start = null;
			$date_end = null;
		}

		$r_obj = obj_objectifs_get_kpi();
		if (!$r_obj || !ria_mysql_num_rows($r_obj)) {
			return false;
		}

		$r_user = gu_users_get(0, '', '', array(PRF_ADMIN, PRF_SELLER), '', 0, '', false, false, false, false, '', false, 0, '', 0, false, $config['tnt_id']);
		if (!$r_user || !ria_mysql_num_rows($r_user)) {
			return false;
		}

		ob_start();

		$ar_stats = array();

		$stats = goals_get_by_view(0, array('date1' => $date_start, 'date2' => $date_end));
		if (is_array($stats)) {
			foreach ($stats as $data) {
				// Exclusion des stats liés à un compte client
				// N'utilise que les stats représentant
				if( isset($data['cust_id']) && is_numeric($data['cust_id']) && $data['cust_id'] > 0 ){
					continue;
				}

				if (!array_key_exists($data['usr_id'], $ar_stats)) {
					$ar_stats[$data['usr_id']] = array();
				}

				foreach ($data as $key => $value) {
					if (!strstr($key, '_STATS_')) {
						continue;
					}

					if (!array_key_exists($key, $ar_stats[$data['usr_id']])) {
						$ar_stats[$data['usr_id']][$key] = 0;
					}

					if ($key == '_STATS_AVG_BASKET') {
						if ($ar_stats[$data['usr_id']][$key] > 0) {
							$ar_stats[$data['usr_id']][$key] = ($ar_stats[$data['usr_id']][$key] + $value) / 2;
						} else {
							$ar_stats[$data['usr_id']][$key] = $ar_stats[$data['usr_id']][$key] + $value;
						}
					} else {
						$ar_stats[$data['usr_id']][$key] = $ar_stats[$data['usr_id']][$key] + $value;
					}
				}
			}
		}

		while ($obj = ria_mysql_fetch_assoc($r_obj)) {
			ria_mysql_data_seek($r_user, 0);

			$stats = array();
			$sum_stats = 0;
			while ($user = ria_mysql_fetch_assoc($r_user)) {
				$stats[$user['id']] = array(
					'firstname' => $user['adr_firstname'],
					'lastname'  => $user['adr_lastname'],
					'is_sync'  => $user['is_sync'],
					'value'     => 0,
				);

				if (trim($user['adr_firstname'].$user['adr_lastname']) == '') {
					$stats[$user['id']]['firstname'] = $user['email'];
				}

				if (isset($ar_stats[$user['id']][$obj['code']])) {
					$sum_stats = $sum_stats + $ar_stats[$user['id']][$obj['code']];
					$stats[$user['id']]['value'] = $ar_stats[$user['id']][$obj['code']];
				}
			}

			if ($sum_stats <= 0) {
				continue;
			}

			$stats = array_msort($stats, array('value' => ($obj['sort'] == 'asc' ? SORT_ASC : SORT_DESC), 'firstname' => SORT_ASC, 'lastname' => SORT_ASC));

			$label_show_next = _('Afficher tous les représentants');
			if ((count($stats) - 3) > 10) {
				$label_show_next = _('Afficher les 10 représentants suivants');
			}

			?><table class="tb-kpi">
				<caption title="<?php print _($obj['name']); ?>"><?php print _($obj['name']); ?></caption>
				<tbody><?php
					$last_value = null;
					$i =  $rank = 0;

					foreach ($stats as $key => $data) {
						$class = '';

						if ($key == $usr_id) {
							if ($i >= 3) {
								?><tr class="separator">
									<td colspan="3" style="text-align:center"> ... </td>
								</tr><?php
							}

							$class = 'class="show-seller"';
						} elseif ($i >= 3) {
							$class = 'class="hidden-seller"';
						}

						if ($last_value === null || $last_value != $data['value']) {
							$rank++;
						}

						$last_value = $data['value'];

						$sufixe = '';
						switch ($obj['unite']) {
							case 'euro': $sufixe = ' € HT'; break;
						}

						if ($obj['unite'] == 'second') {
							if ($data['value'] > 0) {
								$data['value'] = ria_seconds_to_time($data['value'], 6, '+ de ');
							} else {

							}
						} else {
							$data['value'] = number_format($data['value'], ($obj['type'] == 'decimal' ? 2 : 0), ',', ' ');
						}

						?><tr <?php print $class; ?>>
							<td class="align-center"><?php print $rank <= 3 ? '<strong>'.$rank.'</strong>' : $rank; ?></td>
							<td><?php
								print view_usr_is_sync($data).' ';
								print '<a href="/admin/customers/edit.php?usr='.$key.'">'.htmlspecialchars(trim($data['firstname'].' '.$data['lastname'])).'</a>';
							?></td>
							<td class="align-right"><?php print $data['value'].$sufixe; ?></td>
						</tr><?php

						$i++;
					}
					$tmp = -1;
					$i = $rank = 0;
				?></tbody>
				<tfoot>
					<tr>
						<td colspan="3">
							<a class="toggle-seller show" href="#"><?php print $label_show_next; ?></a>
							<a style="display:none;" class="prize-only-first" href="#"><?php print _('Afficher les 3 premiers représentants'); ?></a>
						</td>
					</tr>
				</tfoot>
			</table><?php
		}

		return ob_get_clean();
	}