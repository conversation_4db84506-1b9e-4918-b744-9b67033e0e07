<?php
/**
 * Cette classe permet de construire un tableau contenant toutes les informations nécessaires à JavaScript.
 */
final class JStorage {

	private static $instance = null; ///< L'instance

	private $data = []; ///< Données de menu

	private $varName = '__appRiashop'; ///< Nom de la variable Javascript qui va stocker le menu

	/**
	 * Cette méthode permet d'instancier MainMenu (Singleton)
	 *
	 * @return	object	L'objet en cours
	 */
	public static function getInstance(){

		if( !self::$instance ){
			self::$instance = new JStorage();
		}
		return self::$instance;

	}

	/**
	 * Cette méthode permet d'ajouter une entrée dans le tableau
	 *
	 * @param	string	$key	Obligatoire, Chaine de caractères (clé) unique
	 * @param	mixed	$value	Obligatoire, valeur rattachée à la chaine de caractères (clé)
	 * @return	object	L'objet en cours, une exception sera levée en cas d'erreur
	 */
	public function add($key, $value){
		if( !$this->isKey($key) ){
			throw new Exception(_('La clé n\'est pas valide.'));

		}
		$this->data[$key] = $value;
		return $this;

	}

	/**
	 * Cette méthode permet de supprimer une entrée dans le tableau
	 *
	 * @param	string	$key	Obligatoire, Chaine de caractères (clé) à supprimer
	 * @return	object	L'objet en cours, une exception sera levée en cas d'erreur
	 */
	public function del($key){

		if( !$this->isKey($key) || !isset($this->data[$key]) ){
			throw new Exception(_('La clé n\'a pas été trouvée'));

		}
		unset($this->data[$key]);
		return $this;
	}

	/**
	 * Cette méthode permet de vérifier une clé de tableau
	 *
	 * @param	string	$key	Obligatoire, Chaine de caractères (clé) à vérifier
	 * @return	bool	True en cas de succès, false sinon
	 */
	private function isKey($key){

		return is_string($key) && preg_match('/[a-z0-9_]/i', $key) === 1;
	}

	/**
	 * Cette méthode permet de récupérer les données formatées
	 *
	 * @return	string	Balise script avec les données formatées
	 */
	public function getScript(){

		return '
			<script>
				var '.$this->varName.' = '.json_encode($this->data).';
			</script>
		';
	}

	/** Cette méthode permet de récupérer les données formatées
	 * 	@return string Balise script avec les données formatées
	 */
	public function getData(){
		return json_encode( $this->data );
	}

}