<?php

/// \cond onlyria

require_once('categories.inc.php');
require_once('delivery.inc.php');
require_once('products.inc.php');
require_once('documents.inc.php');
require_once('doc.images.inc.php');
require_once('fields.inc.php');
require_once('comparators.inc.php');
require_once('prices.admin.inc.php');
require_once('tnt.filters.inc.php');
require_once('Marketing/CampaignsManager.inc.php');
require_once('rights.inc.php');
require_once('admin/menu.inc.php');
require_once('ria.queue.inc.php');
require_once(__DIR__."/../vendor-downgraded/autoload.php");

// Sous-modules
require_once('view/admin/location.inc.php');
require_once('view/admin/Breadcrumbs.inc.php');
require_once('view/admin/goals.inc.php');
require_once('view.datepicker.inc.php');

/** \defgroup view_admin Interface d'administration
 *	Ce module comprend les fonctions nécessaires à l'interface d'administration
 * @{
 */

/**	Cette fonction ajoute automatiquement le paramètre get-tenant-admin à une uri. Le nom de cette fonction est volontairement au minimum
 * 	pour faciliter son intégration. D'un point de vue mémo-technique, le nom de cette fonction signifie "url wrapper". Elle n'a d'impact
 * 	que pour les super-administrateurs.
 * 	@param string $url Obligatoire, l'url à retraiter
 * 	@return string l'url à laquelle on a ajouté automatiquement le paramètre get-tenant-admin
 */
function uw( $url ){
	global $config;

	if( isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id']==0 ){
		$url .= ( strpos( $url, '?' )!==false ? '&' : '?' ) . 'get-tenant-admin=' . $config['tnt_id'];
	}

	return $url;
}

/** Cette fonction permet de vérifier qu'un module est accessible à un client. Elle s'appuie pour cela sur la variation de configuration
 * 	sys_module_not_auth, qui doit être renseignée. L'utilisateur doit donc être connecté.
 *	@param string $module Obligatoire, code du module
 *	@return bool True si le module est accessible, False dans le cas contraire
 */
function view_admin_menu_authorized( $module ){ // 4, 5, 6, 7, 8, 9, 10, 11
	global $config;

	if( !is_numeric($module) || $module<=0 ){
		return false;
	}

	// Recherche le module dans la liste des modules non autorisés (exclus)
	if( isset($config['sys_module_not_auth'])
		&& is_array($config['sys_module_not_auth'])
		&& sizeof($config['sys_module_not_auth'])
		&& in_array($module, $config['sys_module_not_auth']) ){
			return false;
	}

	return true;
}

/**	Retourne le sous-menu à activer en fonction de la page actuellement active.
 *	@return string le code du sous-menu à activer, parmi l'une des valeurs suivantes :
 *			- customers
 *			- tools
 *			- comparators
 *			- stats
 *			- config
 *			- orders
 *			- promotions
 *			- documents
 *			- options
 *			- moderation
 *			- search
 *			- fdv
 */
function view_admin_get_submenu(){
	// Rajout de "/admin" en début du PHP_SELF pour gérer l'admin mutualisé + l'admin normale
	$php_self = $_SERVER['PHP_SELF'];
	if( substr($php_self, 0, 6) != '/admin' ){
		$php_self = '/admin'.$php_self;
	}

	// Le sous-menu actif est détecté à partir de l'url
	$submenu = '';

	if( strstr($php_self,'admin/catalog/') ){
		$submenu = '_MDL_CATALOG';
	}elseif( strstr($php_self,'admin/fdv/') ){
		$submenu = '_MDL_FDV';
	}elseif( strstr($php_self,'admin/stats/') ){
		$submenu = '_MDL_STATS';
	}elseif( strstr($php_self,'admin/customers/') ){
		$submenu = '_MDL_CUSTOMERS';
	}elseif( strstr($php_self,'admin/tools/') ){
		$submenu = '_MDL_TOOLS';
	}elseif( strstr($php_self,'admin/comparators/') ){
		$submenu = '_MDL_COMPARATORS';
	}elseif( strstr($php_self,'admin/config/') ){
		$submenu = '_MDL_CONFIG';
	}elseif( strstr($php_self,'admin/orders/') ){
		$submenu = '_MDL_ORDERS';
	}elseif( strstr($php_self,'admin/promotions/') ){
		$submenu = '_MDL_PROMO';
	}elseif( strstr($php_self,'admin/documents/') ){
		$submenu = '_MDL_DOCS';
	}elseif( strstr($php_self,'admin/options/') ){
		$submenu = '_MDL_OPTIONS';
	}elseif( strstr($php_self,'admin/moderation/') ){
		$submenu = '_MDL_MOD';
	}elseif( strstr($php_self,'admin/search/') ){
		$submenu = '_MDL_SEARCH';
	}else{ // par défaut
		$submenu = '_MDL_CATALOG';
	}

	return $submenu;
}

/** Cette fonction détermine si le menu passé en argument est actuellement actif (au sens en cours de visualisation) ou non. Elle est utile pour identifier
 *	le menu de premier niveau du back-office RiaShop qui est actuellement en cours de consultation
 *	@param string $code Obligatoire, nom du dossier de 1er niveau permettant d'identifier le menu dans l'arborescence du back-office
 *	@return string la classe 'tab-menu-active' si l'élément de menu est en cours de consultation, ou chaîne vide dans le cas contraire
 */
function view_active_menu( $code ){
	$class = false;
	switch($code){
		case '_MDL_CATALOG':
			$menu = 'catalog';
			break;
		case '_MDL_CUSTOMERS':
			$menu = 'customers';
			break;
		case '_MDL_ORDERS':
			$menu = 'orders';
			break;
		case '_MDL_PROMO':
			$menu = 'promotions';
			break;
		case '_MDL_DOCS':
			$menu = 'documents';
			break;
		case '_MDL_TOOLS':
			$menu = 'tools';
			break;
		case '_MDL_CONFIG':
			$menu = 'config';
			break;
		case '_MDL_MOD':
			$menu = 'moderation';
			break;
		case '_MDL_STATS':
			$menu = 'stats';
			break;
		case '_MDL_COMPARATORS':
			$menu = 'comparators';
			break;
		case '_MDL_FDV':
			$menu = 'fdv';
			break;
		case '_MDL_OPTIONS':
			$menu = 'options';
			break;
		default:
			return false;
	}
	$menu = 'admin/'.$menu.'/';

	if( strstr($_SERVER['PHP_SELF'], $menu) ){
		$class = true;
	}

	return $class ? 'tab-menu-active' : '';
}

/** Affiche un sous menu de l'interface d'admininistration.
 *  Cette fonction ne retourne rien, elle envoie directement vers la sortie standard le code HTMl permettant d'afficher le sous menu.
 *  @param string $menu_code Obligatoire, code du menu pour lequel on veut afficher le sous menu
 *  @param bool $is_module Optionnel, définie si le menu passé en paramètre est un module principal
 *	@return string le code HTML du sous menu
 */
function view_admin_submenu( $menu_code, $is_module=false ){
	global $config;

	if(!$menu_code){
		return false;
	}
	ob_start();

	// Sous menu traité à part
	switch( $menu_code ){
		case '_MDL_SEARCH':{
			// Initialise un tableau qui va contenir les types et le nombre de résultats pour chaque type de contenu
			// retourné par le moteur de recherche
			$types = search_content_types_get();
			$ar_types = array();
			while( $res = ria_mysql_fetch_array($types) ){
				$ar_types[ $res['code'] ] = array(
					'code' => $res['code'],
					'name' => $res['name'],
					'count' => 0
				);
			}

			// Initialise un tableau qui va contenir les sections (catégories de premier niveau)
			$ar_sections = array(
				'' => array(
					'id' => '',
					'name' => _('Toutes sections'),
					'count' => 0
				)
			);

			// Charge les catégories de premier niveau
			$sections = prd_categories_get();
			while( $sec = ria_mysql_fetch_array($sections) ){
				$ar_sections[ $sec['id'] ] = array(
					'id' => $sec['id'],
					'name' => $sec['name'],
					'count' => 0
				);
			}

			// Conditions générales de Vente
			$ar_sections_cgv = array(
				'' => array(
					'id' => '',
					'name' => _('Toutes sections'),
					'count' => 0
				)
			);

			// Charge les sections des conditions générales de vente
			$sections = cgv_versions_get();
			while( $sec = ria_mysql_fetch_array($sections) ){
				$ar_sections_cgv[ $sec['id'] ] = array(
					'id' => $sec['id'],
					'name' => $sec['name'],
					'count' => 0
				);
			}

			// Calcule le nombre de résultats pour chaque type de résultats
			$total_types = $total_publish = $total_unpublish = 0;
			$tab = search_known_types();

			$q_search = str_replace( array('.net', '.fr', '.com', '.coop'), '', $_GET['q'] );
			switch( $config['tnt_id']){ // Traitement spécifiques LPO et Freevox
				case 5 :
					$q_search = str_replace('@lpo', '', $q_search);
					$q_search = str_replace('lpo', '', $q_search);
					break;
				case 39 :
					$q_search = str_replace('@freevox', '', $q_search);
					$q_search = str_replace('freevox', '', $q_search);
					break;
			}

			// Effectue la recherche sans tenir compte du statut de publication ni de la section
			$results = search( array('seg'=>1, 'keywords'=>$q_search, 'published'=>false, 'section'=>false, 'action'=>2, 'type'=>$tab), false, 0, -1, false, null, 0, true );
			if( is_array($results) && sizeof($results) ){
				foreach( $results as $one_res ){
					$res = $one_res['search'];

					$ar_types[ $res['type_code'] ]['count']++;
					$total_types++;
					if( $res['publish'] ){
						$total_publish++;
					}else{
						$total_unpublish++;
					}

					if( array_key_exists($res['section'],$ar_sections) ){
						$ar_sections[ $res['section'] ]['count']++;
					}

					if( $res['type_code'] == 'cgv-art' && array_key_exists($res['section'],$ar_sections_cgv) ){
						$ar_sections_cgv[ $res['section'] ]['count']++;
						$ar_sections_cgv[ '' ]['count']++; // Mis à jour du count global
					}
				}

				$ar_sections[ '' ]['count'] = sizeof($results);
			}

			//début sélecteur pour le types de résultats
			if ( $total_types>0 ){
				print '
				<div class="riapicker riapicker_search" id="ria_type_picker">
					<div class="selectorview">
						<div class="left">
							<span class="function_name">'._('Types de résultats').'</span>';

							$selectedcat = '<br /><span class="view">'.htmlspecialchars(_('Tous types de résultats')).'&nbsp;('.str_replace( ' ', '&nbsp;', number_format( $total_types, 0, ',', ' ' ) ).')'.'</span>';
							foreach( $ar_types as $t ){
								$ria_admin_ui_class = '';
								if( $t['code']==$_GET['type-result'] ) {
									$selectedcat= '<br /><span class="view">'.htmlspecialchars(_( $t['name'] )).'&nbsp;('.str_replace( ' ', '&nbsp;', number_format( $t['count'], 0, ',', ' ' ) ).')'.'</span>';
								}
							}
							print $selectedcat;

				print '</div><a name="btn" class="btn"><img class="fleche-stats" alt="" src="/admin/images/stats/fleche.gif" /></a><div class="clear"></div></div><div class="selector">';

				print '<a href="/admin/search/index.php?q='.urlencode($_GET['q']).'"'.$ria_admin_ui_class.'>';
				print htmlspecialchars(_('Tous types de résultats')).'&nbsp;('.str_replace( ' ', '&nbsp;', number_format( $total_types, 0, ',', ' ' ) ).')'.'</a>';

				foreach( $ar_types as $t ){
					if( $t['count']>0 || (isset($_GET['type-result']) && $_GET['type-result'] == $t['code']) ){
						$ria_admin_ui_class = '';
						print '<a href="/admin/search/index.php?q='.urlencode($_GET['q']).'&amp;type-result='.urlencode($t['code']).( isset($_GET['section']) ? '&amp;section='.$_GET['section'] : '' ).( isset($_GET['publish']) ? '&amp;publish='.$_GET['publish'] : '').'"'.$ria_admin_ui_class.'>';
						print htmlspecialchars(_( $t['name'] )).'&nbsp;('.str_replace( ' ', '&nbsp;', number_format( $t['count'], 0, ',', ' ' ) ).')';
						print '</a>';
					}
				}
				print '</div></div>';
			}

			//début sélecteur pour les états de publications
			if( $total_publish>0 && $total_unpublish>0 ){
				print '
				<div class="riapicker riapicker_search" id="riapicker">
					<div class="selectorview">
						<div class="left">
							<span class="function_name">'._('Publication').'</span>';

							if( isset($_GET['publish']) && $_GET['publish']=='0' ){
								print '<br /><span class="view">'.htmlspecialchars(_('Publiés et non publiés')).' ('.str_replace( ' ', '&nbsp;', number_format( $total_publish+$total_unpublish, 0, ',', ' ' ) ).')'.'</span>';
							} elseif ( isset($_GET['publish']) && $_GET['publish']=='1' ) {
								print '<br /><span class="view">'.htmlspecialchars(_('Publiés')).' ('.str_replace( ' ', '&nbsp;', number_format( $total_publish, 0, ',', ' ' ) ).')'.'</span>';
							} elseif ( isset($_GET['publish']) && $_GET['publish']=='-1' ) {
								print '<br /><span class="view">'.htmlspecialchars(_('Non publiés')).' ('.str_replace( ' ', '&nbsp;', number_format( $total_unpublish, 0, ',', ' ' ) ).')'.'</span>';
							}
				print '</div><a name="btn" class="btn"><img class="fleche-stats" alt="" src="/admin/images/stats/fleche.gif" /></a><div class="clear"></div></div><div class="selector">';

				print '<a href="/admin/search/index.php?q='.urlencode($_GET['q']).'&amp;type-result='.urlencode($_GET['type-result']).( isset($_GET['section']) ? '&amp;section='.$_GET['section'] : '' ).'"'.$ria_admin_ui_class.'>';
				print htmlspecialchars(_('Publiés et non publiés')).' ('.str_replace( ' ', '&nbsp;', number_format( $total_publish+$total_unpublish, 0, ',', ' ' ) ).')';
				print '</a>';
				print '<a href="/admin/search/index.php?q='.urlencode($_GET['q']).'&amp;type-result='.urlencode($_GET['type-result']).'&amp;publish=1'.( isset($_GET['section']) ? '&amp;section='.$_GET['section'] : '' ).'"'.$ria_admin_ui_class.'>';
				print htmlspecialchars(_('Publiés')).' ('.str_replace( ' ', '&nbsp;', number_format( $total_publish, 0, ',', ' ' ) ).')';
				print '</a>';
				print '<a href="/admin/search/index.php?q='.urlencode($_GET['q']).'&amp;type-result='.urlencode($_GET['type-result']).'&amp;publish=-1'.( isset($_GET['section']) ? '&amp;section='.$_GET['section'] : '' ).'"'.$ria_admin_ui_class.'>';
				print htmlspecialchars(_('Non publiés')).' ('.str_replace( ' ', '&nbsp;', number_format( $total_unpublish, 0, ',', ' ' ) ).')';
				print '</a>';
				print '</div></div>';
			}

			//permet de gérer les ouvertures/fermtures des selecteurs lié à une recherche
			?><script>
				$(document).ready(function() {
					if( typeof $('#ria_type_picker') != 'undefined' && $('#ria_type_picker').length ){
						$('#ria_type_picker .selectorview').click(function(){
							if($('#ria_type_picker .selector').css('display')=='none'){
								$('#ria_type_picker .selector').show();
							}else
								$('#ria_type_picker .selector').hide();
						});
					}
					if( typeof $('#riapicker') != 'undefined' && $('#riapicker').length ){
						$('#riapicker .selectorview').click(function(){
							if($('#riapicker .selector').css('display')=='none'){
								$('#riapicker .selector').show();
							}else
								$('#riapicker .selector').hide();
						});
					}
				});
			</script><?php

			return ob_get_clean();
		}
	}

	return ob_get_clean();
}

/**	Affiche le marqueur de synchronisation pour une marque donnée.
 *	@param array $brd Marque telle que retournée par ria_mysql_fetch_array(prd_brands_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour une marque donnée
 */
function view_brd_is_sync( $brd ){
	$title = $brd['is_sync'] ? _('Cette marque est synchronisée avec votre gestion commerciale') : _('Cette marque n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $brd['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'. $title .'" alt="'.$title.'" />';
}

/**	Affiche le marqueur de synchronisation pour un droit d'accès au catalogue donné.
 *	@param bool $issync Détermine si le droit d'accès catalogue est synchronisé ou non
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour un droit d'accès donné
 */
function view_rec_is_sync( $issync ){
	$title = $issync ? _('Ce droit d\'accès est synchronisé avec votre gestion commerciale') : _('Ce droit d\'accès n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $issync ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Affiche le marqueur de synchronisation pour une catégorie donnée.
 *	@param array $cat Catégorie telle que retournée par ria_mysql_fetch_array(prd_categories_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour une catégorie de produits donnée
 */
function view_cat_is_sync( $cat ){
	$title = $cat['is_sync'] ? _('Cette catégorie est synchronisée avec votre gestion commerciale') : _('Cette catégorie n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $cat['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Affiche le marqueur de synchronisation pour une instance de classe personnalisée donnée.
 *	@param array $obj Instance/Objet tel que retourné par ria_mysql_fetch_array(fld_objects_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour une instance de classe personnalisée donnée
 */
function view_obj_is_sync( $obj ){
	$title = $obj['is_sync'] ? _('Cet objet est synchronisé avec votre gestion commerciale') : _('Cet objet n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $obj['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Affiche le marqueur de synchronisation pour un colisage/conditionnement de produit donné.
 *	@param array $cls Catégorie telle que retournée par ria_mysql_fetch_array(prd_colisage_types_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour un colisage/conditionnement de produit donné
 */
function view_colisage_is_sync( $cls ){
	$title = $cls['is_sync'] ? _('Ce conditionnement est synchronisé avec votre gestion commerciale') : _('Ce conditionnement n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $cls['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Affiche le marqueur de synchronisation pour un produit donné.
 *	@param array $prd Produit tel que retourné par ria_mysql_fetch_array(prd_products_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour un produit donné
 */
function view_prd_is_sync( $prd ){
	$title = $prd['is_sync'] ? _('Ce produit est synchronisé avec votre gestion commerciale') : _('Ce produit n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $prd['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Affiche le marqueur de synchronisation pour une relation donnée.
 *	@param array $hry Obligatoire, hiérarchie telle que retournée par ria_mysql_fetch_array(prd_hierarchy_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour une relation donnée
 */
function view_hry_is_sync( $hry ){
	$title = $hry['is_sync'] ? _('Cette relation est synchronisée avec votre gestion commerciale') : _('Cette relation n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $hry['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Affiche le marqueur de synchronisation pour un compte client donné.
 *	@param array $usr Compte client tel que retourné par ria_mysql_fetch_array(gu_users_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour un compte client donné
 */
function view_usr_is_sync( $usr ){
	if( !isset($usr['is_sync']) ){
		$usr['is_sync'] = 0;
	}

	$title = $usr['is_sync'] ? _('Ce compte client est synchronisé avec votre gestion commerciale') : _('Ce compte client n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $usr['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Affiche le marqueur de synchronisation pour une catégorie tarifaire donnée.
 *	@param int $prc Catégorie tarifaire telle que retournée par ria_mysql_fetch_array(prd_prices_categories_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour une catégorie tarifaire donnée
 */
function view_prc_is_sync( $prc ){
	$title = $prc['is_sync'] ? _('Cette catégorie tarifaire est synchronisée avec votre gestion commerciale') : _('Cette catégorie tarifaire n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $prc['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Affiche le marqueur de synchronisation pour un classement de produit dans une catégorie
 *	@param array $cly Catégorie tarifaire telle que retournée par ria_mysql_fetch_array(prd_products_categories_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour un classement de produit donné
 */
function view_cly_is_sync( $cly ){
	$title = $cly['cly_is_sync'] ? _('Ce classement est synchronisé avec votre gestion commerciale') : _('Ce classement n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $cly['cly_is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Affiche le marqueur de synchronisation pour un magasin
 *	@param array $str Magasin tel que retourné par ria_mysql_fetch_array(dlv_stores_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour un magasin donné
 */
function view_str_is_sync( $str ){
	$title = $str['is_sync'] ? _('Ce magasin est synchronisé avec votre gestion commerciale') : _('Ce magasin n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $str['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/** Affiche le marqueur de synchronisation pour une commande
 *	@param array $ord Commande telle que retournée par ria_mysql_fetch_array(ord_orders_get_with_adresses())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour une commande donnée
 */
function view_ord_is_sync( $ord ){
	$title = $ord['piece'] ? _('Cette commande est synchronisée avec votre gestion commerciale') : _('Cette commande n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $ord['piece'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/** Affiche le marqueur de synchronisation pour un bon de livraison
 *	@param array $bl Bon de livraison tel que retourné par ria_mysql_fetch_array(ord_bl_get)
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour un bon de livraison donné
 */
function view_bl_is_sync( $bl ){
	$title = $bl['piece'] ? _('Ce bon de livraison est synchronisée avec votre gestion commerciale') : _('Ce bon de livraison n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $bl['piece'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/** Affiche le marqueur de synchronisation pour une facture
 *	@param array $inv Facture telle que retournée par ria_mysql_fetch_array(ord_invoices_get)
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour une facture donnée
 */
function view_inv_is_sync( $inv ){
	$title = $inv['piece'] ? _('Cette facture est synchronisée avec votre gestion commerciale') : _('Cette facture n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $inv['piece'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/** Affiche le marqueur de synchronisation pour un panier
 *	@param array $cart panier tel que retournée par ria_mysql_fetch_array(ord_orders_get_with_adresses())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour un panier donné
 */
function view_cart_is_sync( $cart ){
	$title = $cart['piece'] ? _('Ce panier est synchronisé avec votre gestion commerciale') : _('Ce panier n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $cart['piece'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/** Affiche le marqueur de synchronisation pour le tarif d'un produit
 *	@param array $prc identifiant d'un tarif tel que retourné par ria_mysql_fetch_array(prc_prices_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation
 */
function view_prc_prd_is_sync( $prc ){
	$title = $prc['is-sync'] ? _('Ce tarif est synchronisé avec votre gestion commerciale') : _('Ce tarif n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $prc['is-sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/** Affiche le marqueur de sunchronisation pour la tva d'un produit
 *	@param array $tva identifiant d'une tva tel que retourné par ria_mysql_fetch_array(prc_tvas_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation
 */
function view_tva_prd_is_sync( $tva ){
	$title = $tva['is-sync'] ? _('Cette TVA est synchronisée avec votre gestion commerciale') : _('Cette tva n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $tva['is-sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Affiche le marqueur de synchronisation pour une écotaxe d'un produit donné.
 *	@param array $prd Produit tel que retourné par ria_mysql_fetch_array(prd_products_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation
 */
function view_eco_prd_is_sync( $prd ){
	$title = $prd['is_sync'] ? _('Cette écotaxe est synchronisée avec votre gestion commerciale') : _('Cette écotaxe n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $prd['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Teste la variable $is_sync en paramètre, et affiche, si nécéssaire, le texte notifiant l'utilisateur que le champ du produit est affecté uniquement par la synchronisation de la gestion commerciale, selon le mode choisi
 *	@param bool $is_sync La variable $prd['is_sync']
 *	@param bool $in_title Définit si la sortie est dans l'attribut title, est par défaut à true
 *	@param string $disabled Définit si l'attribut disabled est activé/affiché seulement si l'affichage est dans un attribut title
 *	Est par défaut à null
 *	- null : n'ajoute rien
 *	- "disabled" : Set l'attribut disabled (disabled="disabled")
 *	- "readonly" : Set l'attribut readonly (readonly="readonly")
 *	@return string Retourne le texte de notification selon le mode choisi
 */
function view_data_if_is_sync( $is_sync , $in_title = true, $disabled = null){
	$data = "";
	$message = _("Cette information est synchronisée depuis votre gestion commerciale");
	if ( $is_sync ){
		if( $in_title === true ){
			$data = 'title="'.htmlspecialchars( $message ).'" ';

			// Affichage $disabled
			if( $disabled=='disabled' ){
				$data .= 'disabled="disabled" ';
			}elseif( $disabled=='readonly' ){
				$data .= 'readonly="readonly" ';
			}
		}
		else{
			$data = $message;
		}
	}
	return $data;
}

function view_usr_contact( $label='Contacter', $email='', $reply=0, $action='' ){
	return '
		<button onclick="return show_popup_contact(\''.$email.'\', '.$reply.', \''._('Envoyer une réponse').'\', \''.$action.'\');" value="'._('Répondre').'" name="answer" class="btn-send-mail" type="submit">
			<img width="16" border="0" height="16" alt="" src="/admin/images/mails/mail_new.gif?1" class="export-xls"> <span>'.htmlspecialchars( $label ).'</span>
		</button>
	';
}

/** Affiche le marqueur d'activation des apparareils où le logiciel FDV est installé.
 *	@param array $device Obligatoire, résultat ria_mysql_fetch_assoc( dev_devices_get() )
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour un device donné
 */
function view_devices_is_actived( $device ){
	$is_active = $device['is_active'] ? '1' : '0';
	$label_active = $device['is_active'] ? _('Cet appareil est activé') : _('Cet appareil n\'est pas activé');
	return '<img class="sync" src="/admin/images/connected/wifi_'.$is_active.'.svg" alt="'.( $is_active ? _('Oui') : _('Non') ).'" title="'.$label_active.'" />';
}

/**	Affiche le marqueur de synchronisation pour le PAMP d'un produit donné.
 *	@param array $prd Produit tel que retourné par ria_mysql_fetch_array(prd_products_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour un PAMP donné
 */
function view_purchase_avg_prd_is_sync( $prd ){
	$title = $prd['is_sync'] ? _('Ce prix d\'achat est synchronisé avec votre gestion commerciale') : _('Ce prix d\'achat n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $prd['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Affiche le marqueur de synchronisation pour l'image.
 *	@param array $img Image tel que retourné par ria_mysql_fetch_array(img_images_get())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour une image donnée
 */
function view_image_is_sync( $img ){
	$title = $img['is_sync'] ? _('Cette image est synchronisée avec votre gestion commerciale') : _('Cette image n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $img['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Affiche le marqueur de synchronisation pour un dépôt donnée.
 *	@param array $dps Dépôts telle que retournée par prd_deposits_get(p())
 *	@return string la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour un dépôt donné
 */
function view_dps_is_sync( $dps ){
	$title = $dps['is_sync'] ? _('Ce dépôt est synchronisé avec votre gestion commerciale') : _('Ce dépôt n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $dps['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Génère l'affichage du pictogramme et du texte indiquant si une note est synchronisée ou non
 *	@param array $note Note telle que retournée par fld_object_notes_get()
 *	@return string Une balise HTML contenant le pictogramme et le texte approprié
 */
function view_object_note_is_sync( $note ){
	$title = $note['is_sync'] ? _('Cette note est synchronisée avec votre gestion commerciale') : _('Cette note n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $note['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/**	Génère l'affichage du pictogramme et du texte indiquant si une règle d'unité de vente est synchronisée ou non
 *	@param array $rule Règle telle que retournée par gu_sell_units_rules_get()
 *	@return string Une balise HTML contenant le pictogramme et le texte approprié
 */
function view_object_sell_units_rules_is_sync( $rule ){
	$title = $rule['is_sync'] ? _('Cette règle est synchronisée avec votre gestion commerciale') : _('Cette règle n\'existe que dans votre boutique en ligne');
	return '<img class="sync" src="/admin/images/sync/'.( $rule['is_sync'] ? 1 : 0 ).'.svg?'.ADMIN_ASSET_IMGS.'" title="'.$title.'" alt="'.$title.'" />';
}

/** Cette fonction permet de charger le tableau de config constituant les colonnes du tableau des listing produits sur les pages
 * 	de catégorie dans l'espace d'administration.
 */
function load_list_cols_products(){
	global $config;

	$_SESSION['usr-admin-list-prds-cols'] = isset($_SESSION['usr-admin-list-prds-cols']) ? $_SESSION['usr-admin-list-prds-cols'] : $config['admin-list-prds-cols'];

	$config['ar_cols_prd'] = array(
		array(
			'code' 		=> 'ref',
			'name' 		=> 'Référence',
			'default' 	=> in_array('ref', $_SESSION['usr-admin-list-prds-cols']),
			'type'		=> 'text'
		),
		array(
			'code' 		=> 'barcode',
			'name' 		=> 'Code-barres',
			'default' 	=> in_array('barcode', $_SESSION['usr-admin-list-prds-cols']),
			'type'		=> 'text'
		),
		array(
			'code' 		=> 'name',
			'name' 		=> 'Désignation',
			'default'  	=> in_array('name', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'text'
		),
		array(
			'code' 		=> 'title',
			'name' 		=> 'Titre',
			'default' 	=> in_array('title', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'text'
		),
		array(
			'code' 		=> 'price_ht',
			'name' 		=> 'Prix HT',
			'default' 	=> in_array('price_ht', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'float'
		),
		array(
			'code' 		=> 'tva_rate',
			'name' 		=> 'TVA',
			'default' 	=> in_array('tva_rate', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'float'
		),
		array(
			'code' 		=> 'price_ttc',
			'name' 		=> 'Prix TTC',
			'default' 	=> in_array('price_ttc', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'float'
		),
		array(
			'code' 		=> 'promo',
			'name' 		=> 'Prix en promotion',
			'default' 	=> in_array('promo', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'float'
		),

		array(
			'code' 		=> 'publish',
			'name' 		=> 'Publié gescom ?',
			'default' 	=> in_array('publish', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'bool'
		),
		array(
			'code' 		=> 'publish-site',
			'name' 		=> 'Publié site ?',
			'default' 	=> in_array('publish-site', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'bool'
		),
		array(
			'code' 		=> 'stock',
			'name' 		=> 'Stock réel',
			'default' 	=> in_array('stock', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'float'
		),
		array(
			'code' 		=> 'weight',
			'name' 		=> 'Poids Brut',
			'default' 	=> in_array('weight', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'float'
		),
		array(
			'code' 		=> 'weight_net',
			'name' 		=> 'Poids Net',
			'default' 	=> in_array('weight_net', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'float'
		),
		array(
			'code' 		=> 'length',
			'name' 		=> 'Longueur',
			'default' 	=> in_array('length', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'float'
		),
		array(
			'code' 		=> 'width',
			'name' 		=> 'Largeur',
			'default' 	=> in_array('width', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'float'
		),
		array(
			'code' 		=> 'height',
			'name' 		=> 'Hauteur',
			'default' 	=> in_array('height', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'float'
		),
		array(
			'code' 		=> 'brd_title',
			'name' 		=> 'Marque',
			'default' 	=> in_array('brd_title', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'text'
		),
		array(
			'code' 		=> 'childonly',
			'name' 		=> 'Enfant seulement ?',
			'default' 	=> in_array('childonly', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'bool'
		),
		array(
			'code' 		=> 'follow_stock',
			'name' 		=> 'Suivi en stock ?',
			'default' 	=> in_array('follow_stock', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'bool'
		),
		array(
			'code' 		=> 'orderable',
			'name' 		=> 'Commandable ?',
			'default' 	=> in_array('orderable', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'bool'
		),
		array(
			'code' 		=> 'countermark',
			'name' 		=> 'Contremarque ?',
			'default' 	=> in_array('countermark', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'bool'
		),
		array(
			'code' 		=> 'new',
			'name' 		=> 'Nouveauté ?',
			'default' 	=> in_array('new', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'bool'
		),
		array(
			'code' 		=> 'sleep',
			'name' 		=> 'En sommeil ?',
			'default' 	=> in_array('sleep', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'bool'
		),
		array(
			'code' 		=> 'selled',
			'name' 		=> 'Nb ventes',
			'default' 	=> in_array('selled', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'float'
		),
		array(
			'code' 		=> 'date_created',
			'name' 		=> 'Date de création',
			'default' 	=> in_array('date_created', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'date'
		),
		array(
			'code' 		=> 'date_modified',
			'name' 		=> 'Date de dernière modification',
			'default' 	=> in_array('date_modified', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'date'
		),
		array(
			'code' 		=> 'date_published',
			'name' 		=> 'Date de première publication',
			'default' 	=> in_array('date_published', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'date'
		),
		array(
			'code' 		=> 'fields-missing',
			'name' 		=> 'Champs manquants',
			'default' 	=> in_array('fields-missing', $_SESSION['usr-admin-list-prds-cols']),
			'type' 		=> 'text'
		)
	);
}

/**	Affiche la liste des produits dans une catégorie de produits
 *	@param resource $products Obligatoire, Résultat SQL de la liste des produits à afficher
 *  @param array $sort Obligatoire, Tableau associatif des tris de la liste à afficher
 * 	@param int $limit_by_page Optionnel, appliquer un limite sur le nombre de produits affichés. Cet argument est utilisé sur la liste des produits non classés.
 * 	@param array $exclude_cols Optionnel, exclure certaines colonnes du résultat final
 * 	@param int $rowstart Optionnel, à n'utiliser que pour le Lazy Loading. Ligne de départ de l'affichage
 *	@return array le tableau des produits
 **/
function view_products_list( $products, $sort, $limit_by_page=0, $exclude_cols=array(), $rowstart=0 ){
	global $config;

	if (!isset($_GET['cat'])) {
		$_GET['cat'] = 0;
	}
	if (!isset($_GET['brd'])) {
		$_GET['brd'] = 0;
	}

	$html = '';

	if( !$products || !ria_mysql_num_rows($products)){
		$html .= '
			<tr>
				<td colspan="'.sizeof($config['ar_cols_prd']).'">'._('Aucun produit').'</td>
			</tr>
		';
	}else{

		$lazy_load = $_GET['cat']>0 && $limit_by_page==0 && ria_mysql_num_rows($products)>100;
		$limit_by_page = 100;

		$in_page = 1;
		$usr_view_product_right = gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW');
		while( $r = ria_mysql_fetch_array($products) ){

			if( $lazy_load && $in_page>100 ){
				break;
			}

			if ($limit_by_page > 0 && $in_page > $limit_by_page) {
				break;
			}

			$in_page++;

			// Vérifie si l'on a un tri perso pour la catégorie, et ajoute la classe ria-row-orderable
			$orderable_1 = isset($sort['type']) && $sort['type']==SORT_PERSO && $r['cat_id']==$_GET['cat'];
			// Vérifie si l'on a un tri perso pour la marque, et ajoute la classe ria-row-orderable
			$orderable_2 = $sort==array("brd_pos"=>"asc") && $r['brd_id']==$_GET['brd'];

			$orderable = $orderable_1 || $orderable_2;

			$html .=
				'<tr id="line-'.$r['id'].'" '.( $orderable ? 'class="ria-row-orderable"' : '' ).'>'.
					'<td headers="prd-sel" class="prd-col-nomove">'.
						'<input type="checkbox" class="checkbox" name="prd[]" value="'.$r['id'].'" />'.
					'</td>'.
					'<td headers="prd-is-sync" class="align-center prd-is-sync">'.
						view_prd_is_sync($r).
					'</td>';

			foreach( $config['ar_cols_prd'] as $c ){
				if (in_array($c['code'], $exclude_cols)) {
					continue;
				}

				$class = array();
				$class[] = $c['default'] ? 'th-col-show' : 'th-col-hide';

				switch( $c['code'] ){
					case 'ref' :
					case 'price_ht' :
					case 'price_ttc' :
					case 'tva_rate' :
					case 'weight' :
					case 'weight_net' :
						$class[] = 'td-nowrap';
						break;
					case 'desc' :
						$class[] = 'prd-desc';
						break;
				}

				switch( $c['type'] ){
					case 'float':
						$class[] = 'number';
						break;
					case 'bool':
						$class[] = 'pos-center';
						break;
				}

				$html .= '<td headers="prd-'.$c['code'].'" class="'.implode( ' ', $class ).'">';

				switch( $c['code'] ){
					case 'publish-site' :
						$html .= $r['publish'] && $r['publish_cat'] ? _('Oui') : _('Non');
						break;
					case 'promo' :
						$promo = prc_promotions_get( $r['id'] );
						if( is_array($promo) ){
							$html .= number_format( $promo['price_ht'], 2, ',', ' ' ).' € HT';
							$html .= ' - '.number_format( $promo['price_ttc'], 2, ',', ' ' ).' € TTC';
						}
						break;
					case 'fields-missing':
						$missing_fields = array();

						// Vérifie si la description n'est pas vide.
						if( !trim($r['desc']) ){
							$missing_fields[] = _('Description');
						}

						// Vérifie si une image principale est présente.
						if( !$r['img_id'] ){
							$missing_fields[] = _('Image principale');
						}

						// Vérifie si la marque à été saisie.
						if( !$r['brd_id'] ){
							$missing_fields[] = _('Marque');
						}

						// Vérifie les champs avancés.
						if( $r['completion'] < 100 ){
							$r_fields = fld_fields_get(0, 0, -2, 0, 0, $r['id'], true);

							if( ria_mysql_num_rows($r_fields) ){
								while( $field = ria_mysql_fetch_array($r_fields) ){
									$missing_fields[] = $field['name'];
								}
							}
						}

						// Si le nombre de champs est supérieur à 3, nous affichons seulement ceux-ci
						// suivis de "3 points" pour montrer qu'il y en plus.
						if( count($missing_fields) > 3 ){
							$missing_fields = array_slice($missing_fields, 0, 3);
							$missing_fields[] = '...';
						}

						$html .= implode(', ', $missing_fields);

						break;
					case 'ref' :
					case 'barcode' :
					case 'title' :
					case 'name' :
						if( $usr_view_product_right ){

							$cid = $_GET['cat'];
							if( !prd_classify_exists($_GET['cat'], $r['id']) ){
								$rcly = prd_classify_get( false, $r['id'] );
								if( $rcly && ria_mysql_num_rows($rcly) ){
									$cly = ria_mysql_fetch_array( $rcly );
									$cid = $cly['cat'];
								}
							}

							$html .= '<a href="product.php?cat='.$cid.'&amp;prd='.$r['id'].'" title="'._('Afficher la fiche de ce produit').'">'.htmlspecialchars($r[ $c['code'] ]).'</a>';
						}else{
							$html .= htmlspecialchars($r[ $c['code'] ]);
						}
						break;
					default :
						switch( $c['type'] ){
							case 'bool' :
								$html .= $r[$c['code']] ? _('Oui') : _('Non');
								break;
							case 'date' :
								$html .= ria_date_format( $r[$c['code']] );
								break;
							case 'float' :
								switch( $c['code'] ){
									case 'stock':
										$html .= ria_number_format($r[$c['code']], NumberFormatter::DECIMAL);
										break;
									case 'selled':
										$html .= ria_number_format($r[$c['code']], NumberFormatter::DECIMAL);
										break;
									case 'price_ht':
										$html .= '<span data-price-prd="'.$r['id'].'" data-price-type="price-ht">'
											.'<img class="loader" src="/admin/images/stats/loader.gif" alt="Chargement en cours..." width="16" height="16" />'
										.'</span>';
										break;
									case 'price_ttc':
										$html .= '<span data-price-prd="'.$r['id'].'" data-price-type="price-ttc">'
											.'<img class="loader" src="/admin/images/stats/loader.gif" alt="Chargement en cours..." width="16" height="16" />'
										.'</span>';
										break;
									case 'tva_rate':
										$html .= '<span data-price-prd="'.$r['id'].'" data-price-type="tva-rate">'
											.'<img class="loader" src="/admin/images/stats/loader.gif" alt="Chargement en cours..." width="16" height="16" />'
										.'</span>';
										break;
									case 'weight':
									case 'weight_net':
										$ratio = isset($config['weight_col_calc_lines']) && is_numeric($config['weight_col_calc_lines']) && $config['weight_col_calc_lines'] ? $config['weight_col_calc_lines'] : 1000;

										switch( $ratio ) {
											case 1000:
												$unit = 'Kg';
												break;
											case 100000:
												$unit = 'Qt';
												break;
											case 1000000:
												$unit = 'Tn';
												break;
											default:
												$unit = 'Gr';
										}

										$html .= str_replace(array('.000', ',000'), '', ria_number_format($r[$c['code']] / $ratio, NumberFormatter::DECIMAL, 3).' '.$unit);
										break;
									default:
										$html .= ria_number_format($r[$c['code']], NumberFormatter::DECIMAL, 2);
								}
								break;
							default :
								$html .= htmlspecialchars($r[$c['code']]);
						}
				}

				$html .= '</td>';
			}

			// Si on a un tri personnalisé pour la catégorie ou la marque - Partie bouton de déplacement de la ligne
			if( $orderable ){
				$html .= '<td headers="sort-perso" class="align-center ria-cell-move">';
				$html .= '<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
				$html .= '</td>';
			}

			$html .= '</tr>';

		}
	}

	return $html;
}

/** Affiche le marqueur de blocage pour une adresse IP
 *	@param int $ip Obligatoire, identifiant d'une adresse IP
 *	@param int $id Obligatoire, identifiant d'un message
 *
 *	@return Du code HTML, chaîne vide en cas d'erreur
 */
function view_ats_ip( $ip, $id ){
	if( trim($ip)=='' ){
		return '';
	}

	$rip = ats_ips_get( $ip, true );

	$unblock = '  <input type="button" class="show-form-replay show-form-replay-ip '.str_replace(array('.',':'), '', $ip).'" onclick="unblock_ats_ip(\''.$ip.'\', '.$id.')" id="atsip-'.$id.'" name="spam" value="'._('Retirer des spams').'" />';
	if( $rip && ria_mysql_num_rows($rip) ){
		$unblock = '';
	}

	return ats_ips_exists($ip) ? $unblock : ' <input type="button" class="show-form-replay show-form-replay-ip '.str_replace(array('.',':'), '', $ip).'" onclick="block_ats_ip(\''.$ip.'\', '.$id.')" id="atsip-'.$id.'" name="spam" value="'._('Signaler comme spam').'" />';
}

/**	Affiche la source d'un objet (commande, compte client, contact, etc...)
 *	@param $stat Obligatoire, Une ligne de stat récupérée avec la fonction stats_origins_get
 *	@param string $returntype Facultatif, si la chaine 'table' est passé alors la fonction retourne les informations de sources dans des lignes de tableaud HTML, sinon la fonction retourne du HTML en ligne
 *	@return string le code HTML généré
 */
function view_source_origin( $stat, $returntype = '' ){
	$source = $stat['source'];
	$term = $return = '';
	if( $stat['source'] === 'amazon' ){
		$source = 'Amazon';
	}elseif( $stat['source']=='priceminister' ){
		$source = 'PriceMinister';
	}elseif( $stat['source']=='(direct)' ){
		$source = 'Accès direct';
	}elseif( $stat['medium']=='organic' ){
		$source = 'Référencement naturel';
		$term = $stat['term'];
	}elseif( $stat['name']=='(referral)' && $stat['medium']=='referral' ){
		$source = 'Site Référent';
		$term = $stat['source'].$stat['content'];
	}elseif( ($stat['source']=='google' && $stat['medium']=='cpc') || $stat['source']=='google_shopping' ){
		$source = 'Google Adwords + Shopping';
		$term = $stat['term'];
		if( $returntype!='showsource' && trim($stat['term']) ){
			$rprd = prd_products_get_byref( $stat['term'] );
		}
	}elseif( in_array(strtolower($stat['source']), array('newsletter', 'newsletters', 'alerte', 'alerts', 'alertes', 'alert')) ){
		$source = "Newsletters";
	}elseif( $stat['medium']=='site' ){
		$source = 'Recommandations sur le site';
	}elseif( $stat['source']=='' ){
		$source = 'Origine inconnue';
	}else{
		$rctr = ctr_comparators_get_bysource( $stat['source'] );
		if( $rctr && ria_mysql_num_rows($rctr) ){
			$source = ria_mysql_result( $rctr, 0, 'name' );
		}

		if( $returntype!='showsource' && trim($stat['term']) ){
			$rprd = prd_products_get_byref( $stat['term'] );
		}
	}


	if( trim($source) ){
		if( $returntype=='showsource' ){
			return $source;
		}elseif( $returntype == 'table' ){
			$return .= '	<tr>';
			$return .= '		<td>Source :</td>';
			$return .= '		<td>'.htmlspecialchars( $source ).'</td>';
			$return .= '	</tr>';
			if( isset($rprd) && ria_mysql_num_rows($rprd) ) {
				$p = ria_mysql_fetch_array($rprd);
				$p['title'] = trim($p['title']) ? $p['title'] : $p['name'];

				$rcat = prd_products_categories_get( $p['id'] );
				$product_info = '';

				if( $rcat && ria_mysql_num_rows($rcat) ){
					$cat = ria_mysql_fetch_array($rcat);
					$product_info = view_prd_is_sync($p).' <a href="/admin/catalog/product.php?cat='.$cat['cat'].'&prd='.$p['id'].'" target="_blank">'.$p['ref'].' - '.$p['title'].'</a>';
				}else{
					$product_info = view_prd_is_sync($p).$p['ref'].' - '.$p['title'];
				}

				if( $product_info!='' ){
					if( trim($stat['medium']) != '' && $stat['medium'] == 'site' ){
						$return .= '		<td>Produit :</td>';
						$return .= '		<td>'.$product_info.'</td>';
					}else{
						$return .= '		<td>Produit exporté :</td>';
						$return .= '		<td>'.$product_info.'</td>';
					}
				}

				$return .= '	</tr>';
			}elseif( trim($term) ){
				$return .= '	<tr>';
				if( $stat['name']=='(referral)' ){
					$link = strstr($stat['content'], $stat['source']) ? $stat['content']  : 'http://'.$stat['source'].$stat['content'];
					$return .= '		<td>'._('Page d\'origine :').'</td>';
					$return .= '		<td><a target="_blank" href="'.htmlspecialchars($link).'">'.htmlspecialchars($link).'</a></td>';
				}else{
					$return .= '		<td>'._('Recherche effectuée :').'</td>';
					$return .= '		<td>'.$term.'</td>';
				}
				$return .= '	</tr>';
			}

			if( trim($stat['first_visit']) ){
				$first_visit = strtotime( $stat['first_visit'] );
				$return .= '	<tr>';
				$return .= '		<td>'._('Première visite :').'</td>';
				$return .= '		<td>'.ria_date_format($first_visit, true).'</td>';
				$return .= '	</tr>';
			}

			if( $stat['times_visited']!=0 ){
				$return .= '	<tr>';
				$return .= '		<td><span title="'._('Nombre de visites réalisées avant cet achat').'">'._('Nombre de visites :').'</span></td>';
				$return .= '		<td>'.number_format( $stat['times_visited'], 0, ',', ' ' ).'</td>';
				$return .= '	</tr>';
			}
			if( $stat['ip'] ){
				$return .= '	<tr>';
				$return .= '		<td>'._('Adresse ip du client :').'</td>';
				$return .= '		<td>'. $stat['ip'] .' - '. $stat['ip_host'] .'</td>';
				$return .= '	</tr>';
			}

		} else {
			$return .= '		<span class="bold">'._('Source :').'</span>';
			$return .= '		'.$source.'<br/>';
			if( isset($rprd) && ria_mysql_num_rows($rprd) ) {
				$p = ria_mysql_fetch_array($rprd);
				$p['title'] = trim($p['title']) ? $p['title'] : $p['name'];

				$rcat = prd_products_categories_get( $p['id'] );
				$product_info = '';

				if( $rcat && ria_mysql_num_rows($rcat) ){
					$cat = ria_mysql_fetch_array($rcat);
					$product_info = view_prd_is_sync($p).' <a href="/admin/catalog/product.php?cat='.$cat['cat'].'&prd='.$p['id'].'" target="_blank">'.htmlspecialchars( $p['ref'].' - '.$p['title'] ).'</a>';
				}else{
					$product_info = view_prd_is_sync($p).' '.htmlspecialchars( $p['ref'].' - '.$p['title'] );
				}

				if( $product_info!='' ){
					$return .= '		<span class="bold">'._('Produit exporté :').'</span>';
					$return .= '		'.$product_info.'<br/>';
				}
			}elseif( trim($term) ){
				if( $stat['name']=='(referral)' ){
					$link = strstr($stat['content'], $stat['source']) ? $stat['content']  : 'http://'.$stat['source'].$stat['content'];
					$return .= '		<span class="bold">'._('Page d\'origine :').'</span>';
					$return .= '		<a target="_blank" href="'.htmlspecialchars($link).'">'.htmlspecialchars($link).'</a><br/>';
				}else{
					$return .= '		<span class="bold">'._('Recherche effectuée :').'</span>';
					$return .= '		'.$term.'<br/>';
				}
			}

			if( trim($stat['first_visit']) ){
				$first_visit = strtotime( $stat['first_visit'] );
				$return .= '		<span class="bold">'._('Première visite :').'</span>';
				$return .= '		'.ria_date_format($first_visit, true).'<br/>';
			}

			if( $stat['times_visited']!=0 ){
				$return .= '		<span class="bold" title="'._('Nombre de visites réalisées').'">'._('Nombre de visites :').'</span>';
				$return .= '		'.number_format( $stat['times_visited'], 0, ',', ' ' ).'<br/>';
			}

			if( $stat['ip'] ){
				$return .= '		<span class="bold">'._('Adresse ip du client :').'</span>';
				$return .= '		'.$stat['ip'].' - '.$stat['ip_host'].'<br/>';
			}

		}

	}
	return $return;
}

/**	Affiche une liste d'image pour l'administration permettant le tri via drag and drop
 *
 *	@param int $cls_id Obligatoire, classe de l'objet pour lequel on affiche les images
 *	@param int $obj_id Obligatoire, Identifiant de l'objet
 *	@param array $ar_imgs Facultatif, tableau d'identifiants d'images à afficher. La valeur par défaut est false : les images seront directement chargées par la fonction.
 *	@param bool $in_popup Facultatif, détermine si l'affichage à lieu plein page (false, valeur par défaut) ou sous forme de popup (true)
 *	@param bool $publish Facultatif, Boolean indiquant si l'on veux afficher seulement les images publié ou non publié (prit en compte seulement pour les produits)
 *
 *	@return string un ul/li contenant les images.
 */
function view_admin_img_list( $cls_id, $obj_id, $ar_imgs=false, $in_popup=false, $publish=true ){
	if( in_array($cls_id, array(CLS_CTR_MKT)) ){
		if( !is_array($obj_id) || !sizeof($obj_id) ){
			return false;
		}
	}else{
		if( !is_numeric($obj_id) || $obj_id<0 ){
			return false;
		}
	}

	global $config;

	$html = '';
	$main_img = $secondary_img = false;

	$can_edit = true;

	if( is_array($ar_imgs) && sizeof($ar_imgs) ){
		// Transforme la liste des identifiants d'images en liste d'images
		$secondary_img = img_images_get( $ar_imgs, '', '', '', '', null, false, false, false, null, array('set'=>'asc') );
	}else{
		// Charge les images principales et secondaires en fonction du type de contenu. Les requêtes dépendent
		// du type de contenu
		switch( $cls_id ){
			case CLS_PRODUCT: // Produits
				if( !prd_products_exists($obj_id) ){
					return false;
				}
				$prd = ria_mysql_fetch_array( prd_products_get_simple( $obj_id ) );

				if($publish === false){
					$secondary_img = prd_images_get($obj_id, 0, false, false);
				}else{
					if( img_images_exists($prd['img_id']) ){
						$main_img = $prd['img_id'];
					}
					$secondary_img = prd_images_get($obj_id, 0, false, true );
				}
				if( !gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_PRD') ){
					$can_edit = false;
				}
				break;
			case CLS_NEWS: // Actualités
				if( !news_exists($obj_id) ){
					return false;
				}
				$secondary_img = news_images_get($obj_id);
				if( !gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_NEWS') ){
					$can_edit = false;
				}
				break;
			case CLS_CMS: // Pages de contenu
				if( !cms_categories_exists($obj_id) ){
					return false;
				}
				$secondary_img = cms_images_get($obj_id);
				if( !gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_CMS') ){
					$can_edit = false;
				}
				break;
			case CLS_CATEGORY: // Catégories de produits
				if( !prd_categories_exists($obj_id) ){
					return false;
				}
				$secondary_img = prd_cat_images_get($obj_id);
				if( !gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_CAT') ){
					$can_edit = false;
				}
				break;
			case CLS_CTR_MODELS:
				if( !ctr_models_exists($obj_id) ){
					return false;
				}
				$secondary_img = ctr_images_get( $obj_id );
				break;
			case CLS_CTR_MKT:
				if( !prd_products_exists($obj_id[0], false) ){
					return false;
				}

				if( !ctr_comparators_exists($obj_id[1], false) ){
					return false;
				}
				$secondary_img = ctr_catalogs_images_get( $obj_id[0], $obj_id[1] );
				break;
			case CLS_FAQ_CAT:
				if( !faq_categories_exists($obj_id) ){
					return false;
				}
				$secondary_img = faq_cat_images_get($obj_id);
				break;
			case CLS_FAQ_QST:
				if( !faq_questions_exists($obj_id) ){
					return false;
				}
				$secondary_img = faq_qst_images_get($obj_id);
				break;
			case CLS_STORE:
				$secondary_img = dlv_stores_images_get($obj_id);

				break;
			case CLS_DOCUMENT:
				if( !doc_documents_exists($obj_id) ){
					return false;
				}
				$secondary_img = doc_images_get($obj_id);

				break;
			case CLS_TYPE_DOCUMENT:
				if( !doc_types_exists($obj_id) ){
					return false;
				}
				$secondary_img = doc_types_images_get($obj_id);

				break;
			default:
				return false;
		}
	}


	$no_img_sec = false;

	$size = $config['img_sizes']['medium'];

	if( $main_img ){

		$html .= '
			<li class="main-img preview" id="img'.$main_img.'" onclick="'.( $in_popup ? 'parent.' : '' ).'previewClick(this);">
				<input type="checkbox" class="checkbox none" name="del-img-main" value="'.$main_img.'" />
				<img src="'.$config['img_url'].'/'.$size['dir'].'/'.$main_img.'.jpg" width="'.$size['width'].'" height="'.$size['height'].'" />
			</li>
			<label class="mb-only">'._('Images secondaires :').'</label>
		';
	}

	if (!$secondary_img || !ria_mysql_num_rows($secondary_img)) {
		$no_img_sec = true;
	} else {
		if ($publish === false) {
			$name = "imgs-unpublish";
		} else {
			$name = "imgs";
		}

		while ($r = ria_mysql_fetch_array($secondary_img)) {
			$types = array();

			$html .= '<li class="preview" id="img' . $r['id'] . '" onclick="' . ($in_popup ? 'parent.' : '') . ($cls_id === CLS_STORE ? 'prevClick(this, ' . $r['id'] . ')' : 'previewClick(this)') . ';">
			<input type="checkbox" ' . ($cls_id === CLS_STORE ? 'id="img-store-' . $r['id'] . '"' : '') . ' class="checkbox none" name="' . $name . '[]" value="' . $r['id'] . '" >
			<img src="'.$config['img_url'].'/'.$size['dir'].'/'.$r['id'].'.jpg">';

			// Exécute le code seulement si on récupère les photos de magasins.
			if ($cls_id === CLS_STORE) {
				$result = dlv_stores_img_types_get($r['id']);

				while($type = ria_mysql_fetch_array($result)) {
					$types[] = htmlspecialchars($type['name']);
				}

				$html .= '<p>' . implode(' / ', $types) . '</p>';
			}

			$html .= '</li>';
		}
	}

	if( !$main_img && $no_img_sec && $publish !== false ){
		$html .= '<li id="no_img" class="sortable-disabled">'._('Aucune image publiée').'</li>';
	}

	if( trim($html) ){

		$html =
			'<ul class="'.($can_edit ? 'sortable' : '').' sortable-img connectedSortable" id="sortable'.( $publish===false ? '2' : '1' ).'">'.
				$html .
			'</ul>';

	}


	return $html;
}

/** Cette fonction permet de charger les actions à effectuer lors de l'enregistrement de l'onglet "Documents".
 *	@param int $cls_id Obligatoire, identifiant d'une classe
 *	@return bool false en cas d'erreur
 */
function view_admin_tab_documents_actions( $cls_id ){
	global $config;

	if( !is_numeric($cls_id) || $cls_id<=0 ){
		return false;
	}

	$link = '';
	$objs = array();
	$compl_error = '';

	switch( $cls_id ){
		case CLS_CATEGORY :
			$compl_error = 'à cette catégorie';

			if( isset($_GET['cat']) ){
				$objs[] = $_GET['cat'];
				$link 	= '/admin/catalog/edit.php?cat='.$_GET['cat'].'&tab=documents';
			}
			break;
		case CLS_NEWS :
			$compl_error = 'à cette actualité';

			if( isset($_GET['news']) ){
				$objs[] = $_GET['news'];
				$link = '/admin/tools/news/edit.php?news='.$_GET['news'].'&archived='.$_GET['archived'].'&wst='.$_GET['wst'].( isset($_GET['lng']) ? '&lng='.$_GET['lng'] : '' ).'&tab=documents';
			}
			break;
		case CLS_PRODUCT :
			$compl_error = 'à ce produit';

			if( isset($_GET['prd']) ){
				$objs[] = $_GET['prd'];
				$link = '/admin/catalog/product.php?cat='.( isset($_GET['cat']) ? $_GET['cat'] : 0 ).'&prd='.$_GET['prd'].'&tab=documents';
			}
			break;
	}

	if( !sizeof($objs) ){
		return false;
	}

	$file_lng = isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ? $_GET['lng'] : false;

	// Mise à jour d'un ou plusieurs docuemnts
	if( isset($_POST['savedocs']) ){
		// Récupère les documents rattachés à l'objet
		$rdoc = doc_objects_get( 0, $cls_id, $objs );
		if( $rdoc && ria_mysql_num_rows($rdoc) ){
			while( $doc = ria_mysql_fetch_array($rdoc) ){
				if( isset($_FILES[ 'file-'.$doc['doc_id'] ]) && trim($_FILES[ 'file-'.$doc['doc_id'] ]['tmp_name'])!='' ){
					doc_documents_update_file( $doc['doc_id'], 'file-'.$doc['doc_id'], $file_lng );
				}
			}
		}

		header('Location: '.$link);
		exit;
	}

	// Suppression d'un ou plusieurs documents
	if( isset($_POST['deldocs']) ){
		if( isset($_POST['doc']) && is_array($_POST['doc']) && sizeof($_POST['doc']) ){
			foreach( $_POST['doc'] as $doc ){
				if( !doc_objects_del( $doc, $cls_id, $objs) ){
					$error = "Une erreur inattendue s'est produite lors de la suppression des documents liés ".$compl_error.". \n Veuillez réessayer ou prendre contact pour nous signaler le problème.";
				}
			}
		}

		if( !isset($error) ){
			header('Location: '.$link);
			exit;
		}
	}
}

/** Affiche le tableau contenant tous les documents rattachés à un contenu.
 *	@param int $cls_id Obligatoire, identifiant d'une classe
 *	@param $obj_id_0 Obligatoire, identifiant de l'objet en question
 *	@param $obj_id_1 Optionnel, identifiant de l'objet en question
 *	@param $obj_id_2 Optionnel, identifiant de l'objet en question
 *	@return string Le code HTML du tableau
 */
function view_admin_tab_documents_show( $cls_id, $obj_id_0, $obj_id_1=0, $obj_id_2=0 ){
	$html = '';

	$table_name = '';
	$table_no_doc = '';

	switch( $cls_id ){
		case CLS_CATEGORY :
			$table_name = _('Documents rattachés à cette catégorie');
			$table_summary = _('Liste des documents rattachés à cette catégorie.');
			$table_no_doc = _('Aucun document n\'est rattaché à cette catégorie.');
			break;
		case CLS_NEWS :
			$table_name = _('Documents rattachés à cette actualité');
			$table_summary = _('Liste des documents rattachés à cette actualité.');
			$table_no_doc = _('Aucun document n\'est rattaché à cette actualité.');
			break;
		case CLS_PRODUCT :
			$table_name = _('Documents rattachés à ce produit');
			$table_summary = _('Liste des documents rattachés à ce produit.');
			$table_no_doc = _('Aucun document n\'est rattaché à ce produit.');
			break;
	}

	$onclick_add = 'return newTabDocuments('.$cls_id.', '.$obj_id_0;
	$objs = array( $obj_id_0 );

	if( is_numeric($obj_id_1) && $obj_id_1>0 ){
		$objs[] = $obj_id_1;
		$onclick_add .= ', '.$obj_id_1;
	}
	if( is_numeric($obj_id_2) && $obj_id_2>0 ){
		$objs[] = $obj_id_2;
		$onclick_add .= ', '.$obj_id_2;
	}
	$onclick_add .= ');';

	if( isset($error) ){
		$html .= '
			<div class="error">'.nl2br( $error ).'</div>
		';
	}

	// Chargement des documents
	$rdoc = doc_objects_get( 0, $cls_id, $objs );

	$html .= '
		<table class="checklist list-docs-objects table-mediatheque" id="documents">
			<caption>'.htmlspecialchars( $table_name ).'</caption>
			<thead>
				<tr>
					<th id="doc-sel" data-label="'._('Tout cocher :').' ">
						<input type="checkbox" onclick="checkAllClick(this)" class="checkbox" />
					</th>
					<th id="doc-name" class="thead-none">'._('Désignation').'</th>
					<th id="doc-file" class="thead-none">'._('Fichier').'</th>
					<th id="doc-remp" class="thead-none">'._('Remplacement').'</th>
				</tr>
			</thead>
			<tbody>
	';

	if( !$rdoc || !ria_mysql_num_rows($rdoc) ){
		$html .= '
			<tr>
				<td colspan="4">'.htmlspecialchars( $table_no_doc ).'</td>
			</tr>
		';
	}else{
		while( $doc = ria_mysql_fetch_array($rdoc) ){
			$html .= '
				<tr class="ria-row-orderable" id="line-'.$doc['doc_id'].'">
					<td headers="doc-sel">
						<input type="checkbox" value="'.$doc['doc_id'].'" name="doc[]" class="checkbox" />
					</td>
					<td headers="doc-name" data-label="'._('Désignation :').' ">
						<a title="'._('Afficher la fiche de ce document').'" href="/admin/documents/edit.php?doc='.$doc['doc_id'].'&amp;type='.$doc['type_id'].'">'.htmlspecialchars( $doc['doc_name'] ).'</a>
					</td>
					<td headers="doc-file" data-label="'._('Fichier :').' ">
						<a href="/admin/documents/dl.php?doc='.$doc['doc_id'].'">'.htmlspecialchars( $doc['doc_filename'] ).'</a>
					</td>
					<td headers="doc-remp" data-label="'._('Remplacer :').' ">
						<input type="file" name="file-'.$doc['doc_id'].'" id="file-'.$doc['doc_id'].'" class="file" />
						<a onclick="resetFileInput(\'file-'.$doc['doc_id'].'\')">
							<img title="'._('Annuler').'" alt="'._('Annuler').'" src="/admin/images/del.svg" class="noborder" />
						</a>
					</td>
				</tr>
			';
		}
	}
	$html .= '
			</tbody>
			<tfoot>
				<tr>
					<td class="align-left" colspan="2">
	';
	// Le bouton "Supprimer" n'apparaît que si des documents sont présents
	if( ria_mysql_num_rows($rdoc) ){
		$html .= '<input type="submit" value="'._('Supprimer').'" name="deldocs" />';
	}
	$html .= '
					</td>
					<td class="align-right" colspan="2">
	';
	// Le bouton "Enregistrer" n'apparaît que si des documents sont présents
	if( ria_mysql_num_rows($rdoc) ){
		$html .= '<input type="submit" value="'._('Enregistrer').'" name="savedocs" />';
	}
	$html .= '
						<input type="submit" id="tab-add-doc" name="tab-add-doc" value="'._('Ajouter').'" title="'._('Ajouter des documents').'" onclick="'.$onclick_add.'" class="btn-main" />
					</td>
				</tr>
			</tfoot>
		</table>
		<script src="/admin/js/documents/index.js"></script>
		<script src="/admin/js/riaSelector.js"></script>
	';

	return $html;
}

/**	Affiche le tableau pour le trie des images
 * 	@param int $cls_id Obligatoire, identifiant d'une classe d'objet
 *	@param int $obj_id Obligatoire, identifiatn d'un objet de cette classe
 *	@param bool $print Facultatif, détermine si le résultat de cette fonction doit être retourné (false) ou envoyé directement vers la sortie standard (true, valeur par défaut)
 *	@param array $ar_imgs Optionnel, identifiant des images à afficher
 *	@param bool $in_popup Optionnel, tableau de gestion des images présent dans une popup
 *	@return string si l'argument $print est positionné à false, la fonction retournera le code HTML à afficher. Dans le cas contraire, elle ne retourne rien.
 */
function view_admin_img_table( $cls_id, $obj_id, $print=true, $ar_imgs=false, $in_popup=false ){
	if( !fld_classes_exists($cls_id) ){
		return false;
	}
	global $config;

	$in_table = false;
	$has_main_img = true;
	$table_name = 'Images';
	$consign = '';

	$ar_obj_id = control_array_integer( $obj_id );
	if( !$ar_obj_id ){
		return false;
	}

	switch( $cls_id ){
		case CLS_PRODUCT:
			if( !prd_products_exists($ar_obj_id[0]) ){
				return false;
			}

			$table_name = _('Images rattachées à ce produit');
			$this_cls = _('ce produit');
			break;
		case CLS_NEWS:
			if( !news_exists($ar_obj_id[0]) ){
				return false;
			}

			$table_name = _('Images rattachées à cette actualité');
			$this_cls = _('cette actualité');
			break;
		case CLS_CMS:
			if( !cms_categories_exists($ar_obj_id[0]) ){
				return false;
			}

			$table_name = _('Images rattachées à cette page de contenu');
			$this_cls = _('cette page de contenu');
			break;
		case CLS_CATEGORY:
			if( !prd_categories_exists($ar_obj_id[0]) ){
				return false;
			}

			$table_name = _('Images rattachées à cette catégorie');
			$this_cls = _('cette catégorie');
			break;
		case CLS_CTR_MODELS:
			if( !is_numeric($ar_obj_id[0]) || $ar_obj_id[0]<0 ){
				return false;
			}

			if( $ar_obj_id[0]>0 && !ctr_models_exists( $ar_obj_id[0] ) ){
				return false;
			}

			$in_table = true;
			$has_main_img = false;
			break;
		case CLS_CTR_MKT:
			if( !prd_products_exists($ar_obj_id[0]) ){
				return false;
			}

			if( !ctr_comparators_exists( $ar_obj_id[1], false ) ){
				return false;
			}

			$in_table = true;
			$has_main_img = false;
			break;
		case CLS_FAQ_CAT:
			if( !faq_categories_exists($ar_obj_id[0]) ){
				return false;
			}

			$cat = ria_mysql_fetch_array( faq_categories_get( $ar_obj_id[0] ) );
			$table_name = _('Images rattachées à cette catégorie');
			$this_cls = _('cette catégorie FAQ');
			break;
		case CLS_FAQ_QST:
			if( !faq_questions_exists($ar_obj_id[0]) ){
				return false;
			}

			$cat = ria_mysql_fetch_array( faq_questions_get( $ar_obj_id[0] ) );
			$table_name = _('Images rattachées à cette question');
			$this_cls = _('cette question');
			break;
		case CLS_DOCUMENT:
			if( !doc_documents_exists($ar_obj_id[0]) ){
				return false;
			}

			$table_name = _('Images rattachées à ce document');
			$this_cls = _('ce document');
			break;
		case CLS_TYPE_DOCUMENT:
			if( !doc_types_exists($ar_obj_id[0]) ){
				return false;
			}

			$table_name = _('Images rattachées à ce type de document');
			$this_cls = _('ce type de document');
			break;
		default:
			return false;
	}

	global $config;

	if( in_array($cls_id, array(CLS_CTR_MODELS, CLS_CTR_MKT)) ){
		$consign = '
			<sub>'._('Les images doivent impérativement respecter les règles ci-dessous (sous peine de quoi le produit peut être refusé et votre compte bloqué) :').'
				<ol>
					<li>'._('L\'image doit représenter le produit. N\'indiquez pas d\'espace réservé, tel que "Image non disponible", ou le logo de la marque ou de votre magasin.').'</li>
					<li>'._('Les images des produits ne doivent pas comporter de logos ou d\'autres formes de promotion.').'</sub></li>
				</ol>
			</sub>
		';
	}

	$html = '';

	if( !$in_table ){
		$html .= '
			<table class="table-mediatheque" id="table-images">
				<caption>'.htmlspecialchars( $table_name ).'</caption>
				<tbody>
		';
	}

	$html .= '
				<script><!--
					$(document).ready(function(){
						init_img('.$cls_id.', '.$ar_obj_id[0].( isset($ar_obj_id[1]) ? ','.$ar_obj_id[1] : '' ).');
					});
				--></script>
	';

	$ar_imgs = is_array($ar_imgs) ? $ar_imgs : false;
	if( $has_main_img ) {
		$html .= '
			<tr>
				<td class="imgs-primary-lbl"><label>'._('Image principale :').'</label></td>
				<td rowspan="2" ><div class="imgs-w has-main-img">
					'.view_admin_img_list( $cls_id, $obj_id, $ar_imgs, $in_popup ).'
				</div></td>
			</tr>
			<tr class="mb-hidden">
				<td><label>'._('Images secondaires :').'</label></td>
			</tr>
		';
	}else{
		$html .= '
			<tr>
				<td>
					<label>'._('Images associées :').'</label>
					'.$consign.'
				</td>
				<td rowspan="2" ><div class="imgs-w">
					'.view_admin_img_list( $cls_id, $obj_id, $ar_imgs, $in_popup ).'
				</div></td>
			</tr>
		';
	}

	if( $cls_id==CLS_PRODUCT ){
		$html_images = view_admin_img_list( $cls_id, $obj_id, $ar_imgs, $in_popup, false );
		if( trim($html_images) ){
			$html .='
				<tr>
					<td><label>'._('Images dé-publiées :').'</label></td>
					<td><div class="imgs-w2">
						'.$html_images.'
					</div></td>
				</tr>
			';
		}
	}

	if( !$in_table ){
		$html .= '
			</tbody>
			<tfoot>
				<tr>
					<td id="td-table-images" class="align-left" colspan="2">
						<input type="submit" class="delimg" name="delimg" value="'._('Supprimer').'" title="'._('Supprimer les images sélectionnées').'" style="display: none;">
						'.(isset($config['img_sizes']['very_high']) ? '<input type="button" class="edit-zones" name="zonimg" value="'._('Zones cliquables').'" title="'._('Editer les zones cliquables').'" style="display: none;">' : '').'
						<input type="button" class="edit-alt" name="altimg" value="'._('Personnaliser ALT').'" title="'._('Cliquez pour personnaliser l\'attribut alt sur ') . $this_cls .'" style="display: none;">
	';

	if( $cls_id==CLS_PRODUCT ){
		$childs = prd_childs_get( $_GET['prd'], false, false );
		if( ria_mysql_num_rows($childs) ){
			$html .= '
					<input type="submit" name="copy-images-to-childs" value="'._('Recopier sur les articles enfants').'" class="float-left" />
			';
		}
	}

	$html .= '
					<input type="submit" name="addimg" value="'._('Ajouter').'" title="'._('Ajouter des images').'" class="btn-main float-right" />
				</td></tr>
			</tfoot>
			</table>
		';
	}

	if( $print ){
		print $html;
	}else{
		return $html;
	}
}

/** Cette fonction détermine si pour un objet donné l'onglet Avancé doit être affiché ou non. Cette décision est prise en fonction des critères
 * 	suivants :
 *	- un ou plusieurs modèles de saisie existent pour cette classe d’objet
 *	- un ou plusieurs champs libres sont associés à cet objet
 *	@param int $cls_id Obligatoire, identifiant d'un champ avancé
 *	@param int $obj_id Obligatoire, identifiant de l'objet
 *	@return bool Un booléen indiquant si l'onglet Avancé doit être affiché ou non pour cet objet
 */
function view_admin_show_tab_fields( $cls_id, $obj_id ){

	// Compte le nombre de modèles de saisie existants pour cette classe
	$models = fld_models_get_count( $cls_id );
	if( $models ){
		return true;
	}

	// Charge la liste des champs rattachés à cet objet
	if( $obj_id>0 ){
		$fields = fld_fields_get( 0, 0, -2, 0, 0, $obj_id, null, array(), false, array(), null, $cls_id, null, false, null, null );
		if( ria_mysql_num_rows($fields) ){
			return true;
		}
	}

	return false;
}

/** Affiche les informations dans l'onglet "Avancés" d'un objet
 *	@param int $cls_id Obligatoire, identifiant d'un champ avancé
 *	@param int $obj_id Obligatoire, identifiant de l'objet
 *	@param string $lng_code Facultatif, code iso 3166 de la langue à utiliser
 *	@param string $url_widget_lng Facultatif, url du widget de langue
 *	@param int $id Facultatif, identifiant HTML du tableau généré par la fonction
 *	@param string $class Facultatif, valeur à renseigner dans l'attribut class de la balise HTML TABLE générée par la fonction
 *	@return string Le code HTML
 */
function view_admin_tab_fields( $cls_id, $obj_id, $lng_code, $url_widget_lng='', $id='', $class='' ){
	$html = '';

	if( !fld_classes_exists($cls_id) ){
		return $html;
	}

	if( in_array($cls_id, [CLS_ORD_PRODUCT]) ){
		$obj_id = control_array_integer( $obj_id, true, true, true );
		if( !$obj_id ){
			return '';
		}
	}else{
		$obj_id = control_array_integer( $obj_id, true, false, true );
		if( !$obj_id ){
			return '';
		}
	}

	$object = false;
	switch( $cls_id ){
		case CLS_PRODUCT: { // Onglet Avancé de la fiche Produit
			$rproduct = prd_products_get_simple( $obj_id[0] );
			if( !$rproduct || !ria_mysql_num_rows($rproduct) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rproduct );
			$url_supp 	= '/admin/catalog/product.php?cat='.$_GET['cat'].'&amp;prd='.$object['id'];

			break;
		}
		case CLS_CATEGORY: { // Onglet Avancé de la fiche Catégorie
			$rcat = prd_categories_get( $obj_id[0] );
			if( !$rcat || !ria_mysql_num_rows($rcat) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rcat );
			$url_supp 	= 'edit.php?cat='.$object['id'];

			break;
		}
		case CLS_PMT_CODE: { // Onglet Avancé de la fiche Code Promotion
			$rpmt = pmt_codes_get( $obj_id[0] );
			if( !$rpmt || !ria_mysql_num_rows($rpmt) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rpmt );
			$url_supp 	= 'edit.php?id='.$object['id'].'&amp;type='.$_GET['type'];

			break;
		}
		case CLS_BANNER: { // Onglet Avancé de la fiche Bannière
			$rbnr = adv_banners_get( 0, $obj_id[0], false, false, false, false );
			if( !$rbnr || !ria_mysql_num_rows($rbnr) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rbnr );
			$url_supp 	= 'edit.php?id='.$_GET['id'];

			break;
		}
		case CLS_USER: { // Onglet Avancé de la fiche Client
			$ruser = gu_users_get( $obj_id[0] );
			if( !$ruser || !ria_mysql_num_rows($ruser) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $ruser );
			$url_supp 	= 'edit.php?usr='.$_GET['usr'];

			break;
		}
		case CLS_NLR_SUBSCRIBERS: { // Onglet Avancé de la fiche Inscription Newsletter
			$radr = nlr_subscribers_get( NEWSLETTER_TYPE_ALL, $obj_id[0], '',  $_GET['cat'] );
			if( !$radr || !ria_mysql_num_rows($radr) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $radr );
			$url_supp 	= 'edit.php?cat='.$_GET['cat'].'&amp;id='.$_GET['id'];

			break;
		}
		case CLS_CMS: { // Onglet Avancé de la fiche Page de contenu
			$rcms = cms_categories_get( $obj_id[0] );
			if( !$rcms || !ria_mysql_num_rows($rcms) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rcms );
			$url_supp 	= 'edit.php?cat='.$object['id'];

			break;
		}
		case CLS_STORE: { // Onglet Avancé de la fiche Magasin
			$rstore = dlv_stores_get( $obj_id[0], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
			if( !$rstore || !ria_mysql_num_rows($rstore) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rstore );
			$url_supp 	= 'edit.php?str='.$object['id'];

			break;
		}
		case CLS_IMAGE: { // Onglet Avancé de la fiche Image
			$rimg = img_images_get( $obj_id[0] );
			if( !$rimg || !ria_mysql_num_rows($rimg) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rimg );
			$url_supp 	= 'edit.php?image='.$object['id'];

			break;
		}
		case CLS_TYPE_DOCUMENT: { // Onglet Avancé de la fiche Type de Document
			$rtype = doc_types_get( $obj_id[0] );
			if( !$rtype || !ria_mysql_num_rows($rtype) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rtype );
			$url_supp 	= 'edit.php?type='.$object['id'];

			break;
		}
		case CLS_DOCUMENT: { // Onglet Avancé de la fiche Type de Document
			$rtype = doc_documents_get( $obj_id[0], 0, null );
			if( !$rtype || !ria_mysql_num_rows($rtype) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rtype );
			$url_supp 	= 'edit.php?doc='.$object['id'].'&type='.$object['type_id'];

			break;
		}
		case CLS_NEWS: { // Onglet Avancé de la fiche Actualité
			$rnews = news_get( $obj_id[0] );
			if( !$rnews || !ria_mysql_num_rows($rnews) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rnews );
			$url_supp 	= 'edit.php?news='.$object['id'];

			break;
		}
		case CLS_DLV_SERVICE: { // Onglet Avancé de la fiche Service de livraison
			$rsrv = dlv_services_get( $obj_id );
			if( !$rsrv || !ria_mysql_num_rows($rsrv) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rsrv );
			$url_supp 	= '/admin/config/livraison/services/edit.php?srv='.$object['id'];

			break;
		}
		case CLS_REPORT: { // Onglet Avancé de la fiche Rapport de visite

			$rpr = rp_reports_get( $obj_id[0] );
			if( !$rpr || !ria_mysql_num_rows($rpr) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rpr );
			$url_supp 	= '/admin/fdv/reports/view.php?type='.$object['type_id'].'&rp='.$object['id'];

			break;
		}
		case CLS_DEPOSIT: { // Onglat Avancé de la fiche d'un dépôt de stockage
			$r_dps = prd_deposits_get( $obj_id[0] );
			if( !$r_dps || !ria_mysql_num_rows($r_dps) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $r_dps );
			$url_supp = '/admin/config/livraison/deposits/edit.php?dps='.$object['id'];
			break;
		}
		case CLS_ORD_PRODUCT: { // Onglat Avancé de la fiche d'un dépôt de stockage
			$r_line = ord_products_get( $obj_id[0], false, $obj_id[1], '', $obj_id[2] );
			if( !$r_line || !ria_mysql_num_rows($r_line) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $r_line );
			$object['is_sync'] = trim(ord_orders_get_piece($obj_id[0])) !== '';

			$url_supp = '/admin/orders/popup-data-line-order.php?ord='.$obj_id[0].'&prd='.$obj_id[1].'&line='.$obj_id[2];
			break;
		}
		default: {
			error_log('Classe d\'objets non prise en charge pour le moment : view_admin_tab_fields('.$cls_id.')' );
			return $html;
		}
	}

	global $config;

	if( !isset($object['is_sync']) ){
		$object['is_sync'] = false;
	}

	if( trim($url_widget_lng) != '' ){
		$html .= view_translate_menu( $url_widget_lng, $lng_code );
	}

	// Charge la liste des modèles de saisie
	$models = fld_models_get( 0, 0, $cls_id );

	// Construit la liste des modèles déjà appliqués à cet objet pour ne plus les proposer à l'ajout
	$exclude = array();
	$prd_models = fld_models_get( 0, $obj_id, $cls_id );
	while( $m = ria_mysql_fetch_assoc($prd_models) ){
		$exclude[] = $m['id'];
	}

	if (isset($_SESSION['error_save_fields'])) {
		$html .= '<div class="error">'.nl2br($_SESSION['error_save_fields']).'</div>';
		unset($_SESSION['error_save_fields']);
	}

	$html .= '
		<table '.( trim($id)!= '' ? 'id="'.htmlspecialchars( $id ).'"' : '' ).' class="tb-champs-avances'.( trim($class)!= '' ? ' '.htmlspecialchars( $class ).'' : '' ).'">
			<tbody>
	';

	// Affiche les champs avancés associés à cet objet, avec leur interface de saisie
	$ar_fields = array();
	$have_no_sync = true;
	$rmodel = fld_models_get( 0, $obj_id, $cls_id );
	if( $rmodel && ria_mysql_num_rows($rmodel) ){
		while( $m = ria_mysql_fetch_assoc($rmodel) ){
			$html .= '
				<tr class="tr-champs">
					<th colspan="2" class="left">
						<a class="del-link float-left" href="'.$url_supp.'&amp;delmdl='.$m['id'].( $lng_code != $config['i18n_lng'] ? '&amp;lng='.$lng_code : '' ).'" title="'._('Supprimer ce modèle et les champs associés').'"></a>
						<input class="btn-main float-right" type="submit" name="savefields" value="'._('Enregistrer').'" title="'._('Enregistrer les modifications').'" />
						<div class="modele-saisie">'._('Modèle de saisie :').' '.htmlspecialchars( $m['name'] ).'</div>
					</th>
				</tr>
			';

			$fields = fld_fields_get( 0, 0, $m['id'], 0, 0, $obj_id, null, array(), false, array(), null, $cls_id, null, false, null, null, $lng_code, null, true );

			$last_cat = '';

			while( $f = ria_mysql_fetch_assoc($fields) ){

				// Un même champ ne doit pas apparaître deux fois dans le même formulaire.
				// Ce qui peut arriver si le champ se trouve dans plusieurs modèles de saisie.
				if( array_search( $f['id'], $ar_fields, true)!==false ){
					continue;
				}

				if( $f['is-sync'] && $f['obj_value']=='' ){
					$f['obj_value'] = fld_object_values_get( $obj_id, $f['id'], '', true, true );
				}

				if( $f['cat_name']!=$last_cat && $f['cat_name']!=$m['name'] ){
					$html .= '
						<tr>
							<th colspan="2" class="bg-ghostwhite">'.htmlspecialchars($f['cat_name']).'</th>
						</tr>
					';

					$last_cat = $f['cat_name'];
				}

				$html .= '
					<tr>
						<td class="col230px">
							<label for="fld'.$f['id'].'" '.( $f['is-sync'] && $object['is_sync'] ? 'title="'._('Ce champ est synchronisé avec votre gestion commerciale.').'"' : '' ).' >'.($f['is_mandatory'] ? ' <span class="mandatory">*</span> ' : '').htmlspecialchars( $f['name'] ).' :</label>
				';

				if( !($f['is-sync'] && $object['is_sync']) ){
					$have_no_sync = false;
				}

				if( ($f['type_id']==5 || $f['type_id']==6) && ( !$f['is-sync'] || !$object['is_sync'] ) ){
					$html .= '
							<sub>
								<a class="edit" href="#" onclick="return fldFieldModifyValues('.$f['id'].')">'._('Editer la liste').'</a>
							</sub>
					';
				}

				$html .= '
						</td>
						<td>
							<div id="fields-'.$f['id'].'" class="list-fields" style="max-width: 1000px">
								'.fld_fields_edit( $f, '', $object['is_sync'] ? true: false, 0, (!is_array($obj_id) ? array($obj_id) : $obj_id) ).'
							</div>
						</td>
					</tr>
				';

				$ar_fields[] = $f['id'];
			}
		}
	}else{
		$html .= '
				<tr>
					<td colspan="2">
						<div class="notice">
						'._('Aucun modèle de saisie n\'a été associé à ce contenu.').' '.
						( ria_mysql_num_rows($models) ? _('Avant de pouvoir continuer, veuillez sélectionner un modèle ci-dessous.')
							: _('Vous avez la possibilité de <a href="/admin/config/fields/models/index.php">créer des modèles de saisie ici</a>.') ).'
						</div>
					</td>
				</tr>
		';
	}

	// Affiche la liste des champs orphelins (n'étant rattachés à aucun modèle affecté à l'objet)
	// Charge la liste complète des champs associés à l'objet
	$fields = fld_fields_get( 0, 0, -2, 0, 0, $obj_id, null, array(), false, array(), null, $cls_id, null, false, null, null, $lng_code );
	if( $fields && ria_mysql_num_rows($fields) ){
		$have_orphans = false;

		// Vérifie qu'un ou plusieurs champs sont orphelins
		while( $f = ria_mysql_fetch_assoc($fields) ){
			if( in_array($f['id'], $ar_fields) ){
				continue;
			}

			$have_orphans = true;
			break;
		}

		// Si des champs orphelins existent, les affiche ci-dessous
		if( $have_orphans ){
			ria_mysql_data_seek( $fields, 0 );

			$html .= '
				<tr class="tr-champs">
					<th colspan="2" class="left">'._('Champs orphelins').'</th>
				</tr>
			';

			$last_cat = '';
			while( $f = ria_mysql_fetch_assoc($fields) ){
				if( in_array($f['id'], $ar_fields) ){
					continue;
				}

				if( $f['cat_name']!=$last_cat ){
					$html .= '
						<tr>
							<th colspan="2" class="bg-ghostwhite">'.htmlspecialchars($f['cat_name']).'</th>
						</tr>
					';

					$last_cat = $f['cat_name'];
				}

				$html .= '
					<tr>
						<td>
							<label for="fld'.$f['id'].'" '.( $f['is-sync'] && $object['is_sync'] ? 'title="'._('Ce champ est synchronisé avec votre gestion commerciale.').'"' : '' ).'>'.($f['is_mandatory'] ? ' <span class="mandatory">*</span> ' : '').htmlspecialchars($f['name']).' :</label>
						</td>
						<td>
							'.fld_fields_edit( $f , '', $object['is_sync'] ? true: false ).'
						</td>
					</tr>
				';
			}
		}
	}

	$html .= '
			</tbody>
			<tfoot>
	';

	if(!$have_no_sync){
		$html .='
					<tr>
						<td colspan="2">
							<input type="submit" name="savefields" value="'._('Enregistrer').'" class="btn-main" />
							<input type="submit" name="cancel" value="'._('Annuler').'" class="btn-cancel" />
						</td>
					</tr>
		';
	}

	// Si le nombre de modèles disponibles pour cette classe est supérieur au nombre de modèles déjà appliqués,
	// affiche une interface permettant l'ajout de modèles de saisie supplémentaires
	if( ria_mysql_num_rows($models)>ria_mysql_num_rows($prd_models) ){
		$html .= '
				<tr class="tfoot-grey">
					<td colspan="2" class="page">
						<label for="model">'._('Modèle de saisie :').'</label>
						<select name="model" id="model">
		';

		while( $m = ria_mysql_fetch_assoc($models) ){
			if( array_search( $m['id'], $exclude )===false ){
				$html .= '<option value="'.$m['id'].'">'.htmlspecialchars( $m['name'] ).'</option>';
			}
		}

		$html .= '
						</select>
						<input type="submit" name="addmdl" class="button" value="'._('Ajouter').'" />
					</td>
				</tr>
		';
	}

	$html .= '
			</tfoot>
		</table>

		<script><!--
			$(document).ready(function() {
				if( typeof riaFieldRelated != "undefined" ){
					riaFieldRelated.init();
				}

				$(\'.check-all\').click(function() {
					$(this).parent().find(\'[type="checkbox"]\').attr(\'checked\', \'checked\');
					return false;
				});

				$(\'.uncheck-all\').click(function() {
					$(this).parent().find(\'[type="checkbox"]\').removeAttr(\'checked\');
					return false;
				});

			});
		--></script>
	';

	return $html;
}

/** Cette fonction gère les différentes actions sur l'onglet "Avancés" des objets
 *	@param int $cls_id Obligatoire, identifiant de la classe de l'objet
 *	@param int $obj_id Obligatoire, identifiant de l'objet ou tableau composant la clé de l'objet
 *	@param string $lng_code Obligatoire, code de la langue
 */
function view_admin_tab_fields_actions( $cls_id, $obj_id, $lng_code ){
	if( in_array($cls_id, [CLS_ORD_PRODUCT]) ){
		$obj_id = control_array_integer( $obj_id, true, true, true );
		if( !$obj_id ){
			return false;
		}
	}else{
		$obj_id = control_array_integer( $obj_id, true, false, true );
		if( !$obj_id ){
			return false;
		}
	}

	$url_header = '';

	$object = false;
	switch( $cls_id ){
		case CLS_PRODUCT: { // Onglet avancé de la fiche Produit
			$rproduct = prd_products_get_simple( $obj_id[0] );
			if( $rproduct && ria_mysql_num_rows($rproduct) ){
				$object = ria_mysql_fetch_assoc( $rproduct );
			}

			$url_header = '/admin/catalog/product.php?cat='.$_GET['cat'].'&prd='.$obj_id[0].'&tab=fields';

			break;
		}
		case CLS_CATEGORY: { // Onglet Avancé de la fiche Catégorie
			$rcat = prd_categories_get( $obj_id[0] );
			if( $rcat && ria_mysql_num_rows($rcat) ){
				$object = ria_mysql_fetch_assoc( $rcat );
			}

			$url_header = '/admin/catalog/edit.php?cat='.$obj_id[0].'&tab=fields';

			break;
		}
		case CLS_PMT_CODE: { // Onglet Avancé des Codes promotions
			$rpmt = pmt_codes_get( $obj_id[0] );
			if( $rpmt && ria_mysql_num_rows($rpmt) ){
				$object = ria_mysql_fetch_assoc( $rpmt );
			}

			$url_header = '/admin/promotions/specials/edit.php?id='.$obj_id[0].'&type='.$_GET['type'].'#tabFields';

			break;
		}
		case CLS_BANNER: { // Onglet Avancé des Bannières
			$rbnr = adv_banners_get( 0, $obj_id[0], false, false, false, false );
			if( $rbnr && ria_mysql_num_rows($rbnr) ){
				$object = ria_mysql_fetch_assoc( $rbnr );
			}

			if( isset($_GET['type']) && $_GET['type'] == 2 ){
				$url_header = '/admin/tools/zones/edit.php?id='.$obj_id[0].'&type=2&tab=fields';
				break;
			}
			$url_header = '/admin/tools/banners/edit.php?id='.$obj_id[0].'&tab=fields';

			break;
		}
		case CLS_USER: { // Onglet Avancé de la fiche Client
			$ruser = gu_users_get( $obj_id[0] );
			if( $ruser && ria_mysql_num_rows($ruser) ){
				$object = ria_mysql_fetch_assoc( $ruser );
			}

			$url_header = '/admin/customers/edit.php?usr='.$obj_id[0].'&tab=fields'.(isset($_GET['seg']) ? '&seg=' . $_GET['seg'] : '').(isset($_GET['seller']) ? '&seller=' . $_GET['seller'] : '');

			break;
		}
		case CLS_NLR_SUBSCRIBERS: { // Onglet Avancé de l'inscription à la Newsletter
			$radr = nlr_subscribers_get( NEWSLETTER_TYPE_ALL, $obj_id[0], '',  $_GET['cat'] );
			if( $radr && ria_mysql_num_rows($radr) ){
				$object = ria_mysql_fetch_assoc( $radr );
			}

			$url_header = '/admin/tools/newsletter/edit.php?id='.$obj_id[0].'&cat='.$_GET['cat'].'&tab=fields';

			break;
		}
		case CLS_CMS: { // Onglet Avancé des pages CMS
			$rcms = cms_categories_get( $obj_id[0], false, false, -1, false, false, true, null, false, null, false );
			if( $rcms && ria_mysql_num_rows($rcms) ){
				$object = ria_mysql_fetch_assoc( $rcms );
			}

			$url_header = '/admin/tools/cms/edit.php?cat='.$obj_id[0].'&tab=fields';

			break;
		}
		case CLS_STORE: { // Onglet Avancé de la fiche Magasin
			$rstore = dlv_stores_get( $obj_id[0], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
			if( $rstore && ria_mysql_num_rows($rstore) ){
				$object = ria_mysql_fetch_assoc( $rstore );
			}

			$url_header = '/admin/config/livraison/stores/edit.php?str='.$_GET['str'].'&tab=fields';

			break;
		}
		case CLS_IMAGE: { // Onglet Avancé de la fiche Image
			$rimg = img_images_get( $obj_id[0] );
			if( $rimg && ria_mysql_num_rows($rimg) ){
				$object = ria_mysql_fetch_assoc( $rimg );
			}

			$url_header = '/admin/documents/images/edit.php?image='.$obj_id[0].'&tab=fields';

			break;
		}
		case CLS_TYPE_DOCUMENT: { // Onglet Avancé des Types de Documents
			$rtype = doc_types_get( $obj_id[0] );
			if( $rtype && ria_mysql_num_rows($rtype) ){
				$object = ria_mysql_fetch_assoc( $rtype );
			}

			$url_header = '/admin/documents/types/edit.php?type='.$obj_id[0].'&tab=fields';

			break;
		}
		case CLS_DOCUMENT: { // Onglet Avancé des Types de Documents
			$rtype = doc_documents_get( $obj_id[0], 0, null, '', false );
			if( $rtype && ria_mysql_num_rows($rtype) ){
				$object = ria_mysql_fetch_assoc( $rtype );
			}

			$url_header = '/admin/documents/edit.php?type='.$object['type_id'].'&doc='.$obj_id[0].'&tab=fields';

			break;
		}
		case CLS_NEWS: { // Onglet Avancé de la fiche Actualité
			$rnews = news_get( $obj_id[0], false, null, 0, 0, false, false );
			if( $rnews && ria_mysql_num_rows($rnews) ){
				$object = ria_mysql_fetch_assoc( $rnews );
			}

			$url_header = '/admin/tools/news/edit.php?news='.$obj_id[0].'&tab=fields';

			break;
		}
		case CLS_DLV_SERVICE: { // Onglet Avancé des Services de Livraison
			$rsrv = dlv_services_get( $obj_id[0] );
			if( $rsrv && ria_mysql_num_rows($rsrv) ){
				$object = ria_mysql_fetch_assoc( $rsrv );
			}

			$url_header = '/admin/config/livraison/services/edit.php?srv='.$obj_id[0].'&tab=fields';

			break;
		}
		case CLS_REPORT: { // Onglet Avancé des Rapports
			$rsrv = rp_reports_get( $obj_id[0] );
			if( $rsrv && ria_mysql_num_rows($rsrv) ){
				$object = ria_mysql_fetch_assoc( $rsrv );
			}

			$url_header = '/admin/fdv/reports/view.php?rp='.$obj_id[0].'&tab=fields';

			break;
		}
		case CLS_DEPOSIT: { // Onglet Avancé des dépôts de stockage
			$r_dps = prd_deposits_get( $obj_id[0] );
			if( $r_dps && ria_mysql_num_rows($r_dps) ){
				$object = ria_mysql_fetch_assoc( $r_dps );
			}

			$url_header = '/admin/config/livraison/deposits/edit.php?dps='.$obj_id[0].'&tab=fields';

			break;
		}
		case CLS_ORD_PRODUCT: { // Onglat Avancé de la fiche d'un dépôt de stockage
			$r_line = ord_products_get( $obj_id[0], false, $obj_id[1], '', $obj_id[2] );
			if( $r_line && ria_mysql_num_rows($r_line) ){
				$object = ria_mysql_fetch_assoc( $r_line );
				$object['is_sync'] = trim(ord_orders_get_piece($obj_id[0])) !== '';
			}

			$url_header = '/admin/orders/popup-data-line-order.php?ord='.$obj_id[0].'&prd='.$obj_id[1].'&line='.$obj_id[2].'&tab=fields';
			break;
		}
		default: {
			error_log('Classe d\'objets non prise en charge pour le moment : view_admin_tab_fields_actions('.$cls_id.')' );
			return false;
		}
	}

	global $config;

	if( $object === false ){
		if( $lng_code != $config['i18n_lng'] ){
			$url_header .= '&lng='.$lng_code;
		}

		header('Location: '.$url_header);
		exit;
	}

	if( !isset($object['is_sync']) ){
		$object['is_sync'] = false;
	}

	// Ajout d'un modèle
	if( isset($_POST['addmdl']) && isset($_POST['model']) && is_numeric($_POST['model']) && $_POST['model']>0 ){
		if( $rmdl = fld_models_get($_POST['model']) ){
			$mdl = ria_mysql_fetch_array($rmdl);
			if( $mdl['cls_id']==$cls_id ){
				if( !fld_object_models_add( $obj_id,$_POST['model'] ) ){
					$error = _("L'ajout du modèle de saisie a échoué pour une raison inconnue.");
				}
			}else{
				$error = _("Le modèle de saisie sélectionné n'est pas valide.");
			}
		}else{
			$error = _("Un erreur d'origine inconnue est survenue pendant le chargement du modèle de saisie à ajouter.");
		}

		if( !isset($error) && trim($url_header) != '' ){
			if( $lng_code != $config['i18n_lng'] ){
				$url_header .= '&lng='.$lng_code;
			}

			header('Location: '.$url_header);
			exit;
		}
	}

	// Suppression d'un modèle
	if( isset($_GET['delmdl']) && is_numeric($_GET['delmdl']) && $_GET['delmdl']>0 ){
		if( !fld_object_models_del( $obj_id, $_GET['delmdl'], $cls_id ) ){
			$error = _("Le modèle de saisie n'a pas pu être détaché de la catégorie pour une raison inconnue.");
		}

		$tab = 'fields';
		if( !isset($error) && trim($url_header) != '' ){
			if( $lng_code != $config['i18n_lng'] ){
				$url_header .= '&lng='.$lng_code;
			}

			header('Location: '.$url_header);
			exit;
		}
	}

	// Enregistrement de la fiche avancée
	if( isset($_POST['savefields']) ){
		$field_to_save = array();

		// Champs orphelins
		$fields = fld_fields_get( 0, 0, -2, 0, 0, $obj_id, null, array(), false, array(), null, $cls_id );
		while( $f = ria_mysql_fetch_assoc($fields) ){
			// Les champs avancés ou les champs dont l'utilisateur n'a pas accès seront ignorés lors de l'enregistrement
			if( ($f['is-sync'] && $object['is_sync']) || !$f['used_access'] ){
				continue;
			}

			$field_to_save[$f['id']] = $f;
		}

		// Champs avec modèle de saisie
		$rmodel = fld_models_get( 0, $obj_id, $cls_id ); // Récupération des modèle lié à l'object
		if( $rmodel && ria_mysql_num_rows($rmodel) ){
			while( $m = ria_mysql_fetch_assoc($rmodel) ){
				$fields = fld_fields_get( 0, 0, $m['id'], 0, 0, $obj_id, null, array(), false, array(), null, $cls_id );

				while( $f = ria_mysql_fetch_assoc($fields) ){
					// Les champs avancés ou les champs dont l'utilisateur n'a pas accès seront ignorés lors de l'enregistrement
		            if((($f['is-sync'] && $object['is_sync']) || !$f['used_access']) && !(isset($_REQUEST['pointer-cls-id']) && $_REQUEST['pointer-cls-id'] === "3")){
						continue;
					}

					$field_to_save[$f['id']] = $f;
				}
			}
		}

		// Pour chaque champ, Enregistrement
		foreach( $field_to_save as $f ){
			if( !$f['is-sync'] || !$object['is_sync'] ){
				if( $f['type_id']==FLD_TYPE_IMAGE ){
					if( isset($_FILES['fld'.$f['id']]) && trim($_FILES['fld'.$f['id']]['tmp_name'])!='' ){
						$value = img_images_upload('fld'.$f['id']);
						fld_object_values_set( $obj_id, $f['id'], $value, $lng_code );
					}
					continue;
				}

				if( $f['type_id']==FLD_TYPE_SELECT_MULTIPLE && !isset($_POST['fld'.$f['id']]) ){
					$_POST['fld'.$f['id']] = '';
				}

				if ($f['is_mandatory'] && trim($_POST['fld'.$f['id']]) == '') {
					$error = htmlspecialchars( sprintf( _('L\'information "%s" est obligatoire.'), $f['name'] ) );
				}elseif( isset($_POST['fld'.$f['id']]) ){
					$value = $_POST['fld'.$f['id']];

					$value = ria_strip_pre($value);

					if( $f['type_id'] == FLD_TYPE_REFERENCES_ID ){
						$value = explode( ',', $value );
					}

					if( $f['type_id'] == FLD_TYPE_INT ){
						$value = is_numeric($value) ? (int)$value : '';
					}

					fld_object_values_set( $obj_id, $f['id'], $value, $lng_code );
				} else {
					fld_object_values_set( $obj_id, $f['id'], '', $lng_code );
				}
			}
		}

		if (isset($error)) {
			$_SESSION['error_save_fields'] = $error;
		}

		if( !isset($error) && trim($url_header) != '' ){
			if( $lng_code != $config['i18n_lng'] ){
				$url_header .= '&lng='.$lng_code;
			}
			$url_header .= '&upd-fields-success=1';

			// Dans le cas d'un code promotion, force l'actualisation du cache
			if( $cls_id == CLS_PMT_CODE ){
				require_once('promotions.inc.php');
				pmt_forced_cache_promo();
			}

			header('Location: '.$url_header);
			exit;
		}
	}
}

/** Cette fonction charge les informations sur un message de contact dans l'administration que ce soit dans la partie Modération ou bien l'onglet "Modération" des comptes client
 *	@param array $type Obligatoire, tableau contenant les informations de base d'un type de message (id, name)
 *	@param array $msg Obligatoire, tableau contenant les informations d'un message
 *	@param bool $show_type Optionnel, par défaut le type n'est pas affiché, mettre true pour que ce soit le cas
 */
function view_admin_msg_moderation( $type, $msg, $show_type=false ){
	global $config;

	$html = '';

	// Information sur le compte rattaché, s'il en existe un
	$usr['is_sync'] = 0;
	$usr['id'] = 0;
	if( $msg['usr_id']>0 ){
		$rusr = gu_users_get($msg['usr_id']);
		if( $rusr && ria_mysql_num_rows($rusr) ){
			$usr = ria_mysql_fetch_assoc($rusr);
		}
	}
	$html .= '	<tr id="message-'.$msg['id'].'">';
	$html .= '		<td headers="th-author" class="td-author">';

	// affiche les informations sur le produit concerné par le commentaire
	if( is_numeric($msg['prd_id']) && $msg['prd_id'] && prd_products_exists($msg['prd_id']) ){
		$prd = ria_mysql_fetch_assoc( prd_products_get_simple($msg['prd_id']) );

		$url = '#';
		$rcat = prd_products_categories_get($prd['id'], true);
		if( $rcat!=false && ria_mysql_num_rows($rcat)>0 ){
			$cat = ria_mysql_fetch_assoc($rcat);
			$url = '/admin/catalog/product.php?cat='.$cat['cat'].'&amp;prd='.$prd['id'];
		}

		$html .= '		<span class="bold">'._('Produit concerné :').' </span><br />'.view_prd_is_sync($prd).'&nbsp;';
		$html .= '		<a href="'.$url.'" target="_blank">'.htmlspecialchars( $prd['title'] ).'</a><br /><br />';
	}

	if( $show_type ){
		$html .= '
			<span class="bold">'._('Type :').' </span><br />'.htmlspecialchars( $type['name'] ).'<br /><br />
		';
	}

	// Afficher les informations sur l'expéditeur, destinataire, en copie
	$html .= '			<span class="bold">'._('Envoyé par :').'</span><br />'.view_usr_is_sync($usr).'&nbsp;';
	if( $msg['usr_id'] && isset($usr['adr_firstname'], $usr['adr_lastname']) ){
		$html .= '<a href="/admin/customers/edit.php?usr='.$msg['usr_id'].'" target="_blank">';
		$html .= htmlspecialchars( trim($usr['adr_firstname'].' '.$usr['adr_lastname'].' '.$usr['society']) );
		$html .= '</a>';
		$surnom = fld_object_values_get( $usr['id'], _FLD_PSEUDO );
		if( trim($surnom) ){
			$html .= '<br />(Surnom : '.htmlspecialchars( $surnom ).')';
		}
	} elseif( trim($msg['firstname'].' '.$msg['lastname'].' '.$msg['society'])!='' ){
		$html .= htmlspecialchars( trim($msg['firstname'].' '.$msg['lastname'].' '.$msg['society']).' <'.$msg['email'].'>' );
	}else{
		$html .= htmlspecialchars( $msg['email'] );
	}

	$html .= '			<br />'.sprintf( _('Le %s'), ria_date_format($msg['date_created']) ).'<br/>';

	// Récupère la date de dernière commande associée à ce compte client
	if( isset($usr['id']) ){
		$last_order = gu_users_get_last_order( $usr['id'] );
		if( $last_order ){
			$html .= '<br/><span class="bold">'._('Dernière commande :').'</span> ';
			$html .= '<a href="/orders/order.php?ord='.$last_order['id'].'" title="'._('Afficher la fiche de cette commande').'" target="_blank">';
			$html .= view_ord_is_sync($last_order).' '.( $last_order['piece'] ? htmlspecialchars($last_order['piece']) : $last_order['id'] );
			$html .= '</a><br/>';
		}
	}

	$to = explode(',', $msg['email_to']);
	if( sizeof($to)>0 && $to[0]!='' ){
		$html .= '<br/>			<span class="bold">'._('Destinataire :').'</span>';
		foreach( $to as $email ){
			$show_user = true;
			if( $config['tnt_id']==8 ){
				$rstr = dlv_stores_get( 0, null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, true, $email );
				if( $rstr && ria_mysql_num_rows($rstr) ){
					$str = ria_mysql_fetch_assoc( $rstr );
					$html .= '<br />'.view_str_is_sync($str).' <a href="/admin/config/livraison/stores/edit.php?str='.$str['id'].'">'.htmlspecialchars( $str['name'] ).'</a>';
					$show_user = false;
				}
			}

			if( $show_user ){
				$name = '';
				$usr_to = gu_users_get( 0, $email );

				if( $usr_to!=false && ria_mysql_num_rows($usr_to)>0 ){
					$usr_to = ria_mysql_fetch_assoc($usr_to);

					if($usr_to['type_id'] == 2){
						$name = $usr_to['society'];
					}else{
						$name = trim( $usr_to['adr_firstname'].' '.$usr_to['adr_lastname'].' '.$usr_to['society'] );
					}
				}

				if( trim($name) != '' ){
					$html .= '<br />'.view_usr_is_sync($usr_to).' '.
						( $usr_to['id'] ? '<a href="/admin/customers/edit.php?usr='.$usr_to['id'].'" target="_blank">'.htmlspecialchars( $name ).'</a>' : htmlspecialchars( $name ) );
				}else{
					$html .= '<br /><a href="mailto:'.htmlspecialchars( $email ).'">'.htmlspecialchars( $email ).'</a>';
				}
			}
		}
	}

	$cc = explode(',', $msg['email_cc']);
	if( sizeof($cc)>0 && $cc[0]!='' ){
		$html .= '			<br /><br/><span class="bold">'._('En copie :').'</span> ';
		foreach( $cc as $email ){

			$name = '';
			$usr_cc = gu_users_get( 0, $email );

			if( $usr_cc!=false && ria_mysql_num_rows($usr_cc)>0 ){
				$usr_cc = ria_mysql_fetch_assoc($usr_cc);

				if($usr_cc['type_id'] == 2){
					$name = $usr_cc['society'];
				}else{
					$name = $usr_cc['adr_firstname'].' '.$usr_cc['adr_lastname'].' '.$usr_cc['society'];
				}
			}

			if( trim($name) != '' ){
				$html .= '<br />'.view_usr_is_sync($usr_cc).' '.
					( $usr_cc['id'] ? '<a href="/admin/customers/edit.php?usr='.$usr_cc['id'].'" target="_blank">'.htmlspecialchars( $name ).'</a>' : htmlspecialchars( $name ) );
			}else{
				$html .= '<br /><a href="mailto:'.$email.'">'.htmlspecialchars( $email ).'</a>';
			}
		}
	}

	if( $type['moderate'] ){
		$date_publish ='';
		$html .= '			<div id="moderate-'.$msg['id'].'" class="moderate">';
		$html .= '<span class="bold">'._('Modération :').'</span>';

		// S'il s'agit d'un avis provenant de "Avis vérifiés"
		if( isset($msg['id_verif']) && trim($msg['id_verif']) != '' ){
			$html .= '<span class="notice">'._('Cet avis provient de la plateforme "Avis Vérifiés".').'</span>';
		}else{
			// Date de publication ou dépublication
			if( $msg['date_publish']!='' ){
				$date_publish = ria_date_format($msg['date_publish']);
				if( $msg['usr_publish']>0 && ($usrP = ria_mysql_fetch_assoc( gu_users_get($msg['usr_publish']) )) ){
					$date_publish .= ' <br />Par '.view_usr_is_sync( $usrP );
					$date_publish .= ' <a target="_blank" href="/admin/customers/edit.php?usr='.$usrP['id'].'">'.htmlspecialchars( trim($usrP['adr_firstname'].' '.$usrP['adr_lastname'].' '.$usrP['society']) ).'</a>';
				}
			}

			// Information sur le statut du message
			switch( $msg['publish'] ){
				case 1 : // Avis accepté
					$html .= '<span class="info-publish">'._('Approuvé le ');
					if( $date_publish!='' ){
						$html .= $date_publish;
					}
					$html .= '<br /><a onclick="moderateMessage('.$msg['id'].', false)" class="unchecked">'._('Ne plus approuver').'</a></span>';
					break;
				case 0 : // Avis refusé
					$html .= '<span class="info-publish">'._('Refusé le ');
					if( $date_publish!='' ){
						$html .= $date_publish;
					}else{
						$html .= '<br />Par ';
					}
					$html .= '<br /><a onclick="moderateMessage('.$msg['id'].', true)" class="checked">'._('Approuver').'</a></span>';
					if ($msg['comment'] != ""){
						$html .= '<br /><span class="bold">'._('Commentaire sur la désapprobation :').'</span>';
						$html .= '<br /><em>'.$msg['comment'].'</em>';
					}
					break;
				default : // Avis en attente de modération
					$html .= '<span class="info-publish">';
					$html .= 'En attente depuis le '.ria_date_format($msg['date']);
					$html .= '<br /><a onclick="moderateMessage( '.$msg['id'].', true)" class="checked">'._('Approuver').'</a> | <a onclick="moderateMessage( '.$msg['id'].', false)" class="unchecked">'._('Désapprouver').'</a>';
					$html .= '</span>';
					break;
			}
		}

		$html .= '			</div>';
	}

	$html .= '		</td>';
	$html .= '		<td headers="th-message" class="td-message">';
	$html .= '			<span class="bold">Sujet : '.htmlspecialchars($msg['subject']).'</span><br />';
	$html .= nl2br( htmlspecialchars( $msg['body'] ) ).'<br />';

	if( $msg['note']>0 ){
		$html .= '	 		<br /><span class="bold">'._('Note :').'</span> '.$msg['note'];
	}
	if( $msg['note_dlv']>0 ){
		$html .= '	 		<br /><span class="bold">'._('Note sur la livraison :').'</span> '.$msg['note_dlv'];
	}
	if( $msg['note_pkg']>0 ){
		$html .= '	 		<br /><span class="bold">'._('Note sur l\'emballage :').'</span> '.$msg['note_pkg'];
	}

	// Récupère la commande rattacher au message
	if( isset($msg['ord_id']) ){
		$ord_exists = ord_orders_exists( $msg['ord_id'], $usr['id'], 0, true);
		if( $ord_exists ){
			$html .= '<br/><span class="bold">'._('Numéro de commande attachée à ce message :').'</span> ';
			$html .= '<a href="/orders/order.php?ord='.$msg['ord_id'].'" title="'._('Afficher la fiche de cette commande').'" target="_blank">';
			$html .= $msg['ord_id'];
			$html .= '</a><br/>';
		}
	}

	// documents joints
	$docs = array();
	$rimg = gu_messages_images_get( $msg['id'] );
	if( $rimg && ria_mysql_num_rows($rimg) ){
		$sizeImg = $config['img_sizes']['medium'];
		$count = 1;
		while( $img = ria_mysql_fetch_assoc($rimg) ){
			$docs[] = '<a href="'.$config['img_url'].'/'.$sizeImg['width'].'x'.$sizeImg['height'].'/'.$img['id'].'.'.$sizeImg['format'].'" target="_blank">Image '.$count.'</a>';
			$count++;
		}
	}

	$rd = messages_files_get( 0, $msg['id'] );
	if( $rd && ria_mysql_num_rows($rd) ){
		while( $d = ria_mysql_fetch_assoc($rd) ){
			$docs[] = '<a href="/admin/customers/dl.php?file='.$d['id'].'">'.htmlspecialchars( $d['name'] ).'</a>';
		}
	}

	if( sizeof($docs) ){
		$label = sizeof($docs)>1 ? _('Pièces jointes') : _('Pièce jointe');
		$html .= '<br /><span class="bold">'.htmlspecialchars( $label ).'</span> : '.implode(', ', $docs);
	}

	$fields_libs = fld_fields_get( 0, 0, -2, 0, 0, $msg['id'], null, array(), false, array(), null, CLS_MESSAGE );
	if($fields_libs && ria_mysql_num_rows($fields_libs)) {
		$first = true; $div=false;
		while($fld = ria_mysql_fetch_assoc($fields_libs)){
			if( $fld['obj_value']!='' && $first ){
				$html .= '			<div class="infos-compl"><span class="bold">'._('Informations complémentaires :').'</span><br/>';
				$div=true; $first=false;
			}
			if( $fld['obj_value']!='' ){
				if( $fld['type_id']==FLD_TYPE_IMAGE ){
					$size = $config['img_sizes']['medium'];
					$html .= '<span class="bold info-value">'.htmlspecialchars( $fld['name'] ).'</span> : <br/><span class="bold info-value"><img src="'.$config['img_url'].'/'.$size['width'].'x'.$size['height'].'/'.$fld['obj_value'].'.'.$size['format'].'" alt="" width="'.$size['width'].'" height="'.$size['height'].'" /></span><br/>';
				} else {
					$html .= '<span class="bold info-value">'.htmlspecialchars( $fld['name'] ).'</span> : '.htmlspecialchars( $fld['obj_value'] ).'<br/>';
				}
			}
		}
		if( $div ){
			$html .= '</div>';
		}
	}

	$html .= '	<div id="reponse-'.$msg['id'].'">';
	if( $msg['nbrep']>0 ){
		$messages_reply = contacts_get_replies( $msg['id'] );
		if( $messages_reply!==false ){
			if( ria_mysql_num_rows($messages_reply)==1 ){
				$html .= '<br />
					<a id="show-lst-rep-'.$msg['id'].'" onclick="show_rep('.$msg['id'].')">'._('Afficher la réponse').'</a>
					<a id="hide-lst-rep-'.$msg['id'].'" onclick="hide_rep('.$msg['id'].')" class="none">'._('Masquer la réponse').'</a>
				';
			} elseif( ria_mysql_num_rows($messages_reply)>1 ){
				$html .= '<br />
					<a id="show-lst-rep-'.$msg['id'].'" onclick="show_rep('.$msg['id'].')">'._('Afficher les réponses').'</a>
					<a id="hide-lst-rep-'.$msg['id'].'" onclick="hide_rep('.$msg['id'].')" class="none">'._('Masquer les réponses').'</a>
				';
			}

			$html .= '<div id="lst-rep-'.$msg['id'].'" class="none">';

				while( $msg_reply = ria_mysql_fetch_assoc($messages_reply) )
				{

					$html .= '<div class="rep">';
						$html .= '<span class="bold">'._('Votre réponse :').'</span><br />
							Le '.$msg_reply['date_created'].'<br /><br />
						';
						$html .= nl2br(htmlspecialchars($msg_reply['body']));
						$r_file = messages_files_get(0,$msg_reply['id']);
						if( ria_mysql_num_rows($r_file)>0 ){

							$html .= '<div>
								<br />'._('Pièces jointes :').'<ul>';
								while( $file = ria_mysql_fetch_assoc($r_file) ){

									// Taille du fichier
									$size = round(($file['size']/1024),1);
									$size = $size>1024 ? $size = round(($size/1024),1).' Mo' : $size.' Ko';
									$html .= '<li><a href="/admin/customers/dl.php?file='.$file['id'].'">'.$file['name'].'</a> <span class="size-file">('.$size.')</span></li>';

								}
								$html .= '</ul>
							</div>';

						}
					$html .= '</div>';

				}
			$html .= '		<div class="clear"></div>';
			$html .= '</div>';
		}
	}
	$html .= '	</div>';
	if( $type['code'] != 'DIRECT_CONTACT' && (!isset($msg['id_verif']) || trim($msg['id_verif']) == '') ){
		$stats = stats_origins_get( $msg['id'], CLS_MESSAGE );

		if( $stats && ria_mysql_num_rows($stats) ){
			$html .= '<div class="infos-compl">';
			$stat = ria_mysql_fetch_assoc( $stats );
			$html .= view_source_origin($stat);
			$html .= '</div>';
		}

		$html .= '			<div id="action-'.$msg['id'].'" class="action">';

		$html .= '				<input type="button" id="button-rep-'.$msg['id'].'"  value="'._('Répondre').'" onclick="show_form_rep('.$msg['id'].')" class="show-form-replay btn-main'.( isset($_GET['cnt']) && $_GET['cnt']==$msg['id'] ? ' none' : '' ).'" />';

		if( $msg['spam_id']==0 ){
			$html .= '			<input type="button" id="button-spam-'.$msg['id'].'" class="show-form-spam button-del" value="Signaler comme spam" onclick="block_ats_ip('.$msg['id'].')" />';
		}else{
			$html .= '			<input type="button" id="button-spam-'.$msg['id'].'" class="show-form-spam" value="Retirer des spams" onclick="unblock_ats_ip('.$msg['id'].')" />';
		}

		$html .= '			</div>';
		$html .= '			<div class="clear"></div>';
		$html .= '			<div id="form-rep-'.$msg['id'].'" class="new-rep'.( isset($_GET['cnt']) && $_GET['cnt']==$msg['id'] ? ' block' : ' none' ).'">';
		$html .= '				<form action="moderation.php" id="form-contact-'.$msg['id'].'" method="post" enctype="multipart/form-data">';
		$html .= '					<input type="hidden" name="tab-file" id="tab-file-'.$msg['id'].'" value="" />';
		$html .= '					<input type="hidden" name="msg" value="'.$msg['id'].'" />';
		$html .= '					<span class="title-new-rep">'._('Réponse :').'</span>';
		$html .= '					<div class="clear"></div>';
		$html .= '					<div class="message">';
		$html .= '						<textarea name="reponce-message" cols="67" rows="10" placeholder="'._('Saisissez votre réponse').'"></textarea>';
		$html .= '					</div>';
		$html .= '					<div class="bottom-answer">';
		$html .= '						<a id="link-join-file-'.$msg['id'].'" onclick="show_join('.$msg['id'].')">'._('Attacher des fichiers à votre réponse').'</a>';
		$html .= '						<div id="div-file-'.$msg['id'].'" class="join-file none">';
		$html .= '							<span class="title-new-rep">'._('Pièces Jointes  :').'</span>';
		$html .= '							<div id="join-file-'.$msg['id'].'"></div>';
		$html .= '							';
		$html .= '						</div>';
		$html .= '						<div class="btn-group">';
		$html .= '							<input type="button" name="cancel-rep" value="Annuler" onclick="hide_form_rep(\''.$msg['id'].'\')" />';
		$html .= '							<input class="submit-rep btn-main" value="Envoyer" type="button" name="submit-rep" onclick="return sendReponse('.$msg['id'].');" />';
		$html .= '						</div>';
		$html .= '					</div>';
		$html .= '				</form>';
		$html .= '			</div>';
	}

	$html .= '			<div class="clear"></div>';
	$html .= '		</td>';
	$html .= '	</tr>';

	return $html;
}

/** Cette fonction permet d'afficher la sélection de sites et de langues pour un contenus
 *	@param int $class Obligatoire, identifiant d'un type de classe
 *	@param int $obj Obligatoire, identifiant d'un objet en particulier
 *	@param bool $mandatory facultatif, Détermine si il faut ajouter l'astérisque qui indique que le champs est obligatoire
 *	@param string $url facultatf, permet de récupérer l'url_alias d'une actualité pour l'afficher à côté du champ
 *
 *	@return string Le code HTML
 */
function view_admin_form_websites_languages( $class, $obj, $mandatory=false, $url='' ){
	global $config;

	$html = '';

	$wst = wst_websites_get( 0, false, null, true );
	if( ($wst && ria_mysql_num_rows($wst)>1) || (is_array($config['i18n_lng_used']) && sizeof($config['i18n_lng_used'])>1) ){
		$html .= '
			<tr class="websites-languages">
				<td><label>'.($mandatory ? '<span class="mandatory">*</span> ' : '')._('Sur les sites :').'</label></td>
				<td>
					<a href="#" onclick="return checkAll(\'.websites-languages\');">'._('Tous').'</a> | <a href="#" onclick="return unCheckAll(\'.websites-languages\');">'._('Aucun').'</a><br/>
		';

		while( $r = ria_mysql_fetch_array($wst) ){
			$checked = '';
			switch( $class ){
				case CLS_DOCUMENT: {
					$checked = doc_websites_exists( $r['id'], $obj ) ? ' checked="checked"' : '' ;
					break;
				}
				case CLS_NEWS: {
					$checked = news_websites_exists( $obj, $r['id'] ) ? ' checked="checked"' : '' ;
					break;
				}
				case CLS_BANNER: {
					$checked = adv_websites_exists( $obj, $r['id'] ) ? ' checked="checked"' : '';
					break;
				}
			}

			// permet de pas afficher la selection Yuto dans les actualitées
			if ($class == CLS_NEWS && $r['type_id'] == _WST_TYPE_FDV ){
				continue;
			}

			$rlng = wst_websites_languages_get( $r['id'] );
			$html .= '
				<div class="list-wst-lng">
					<input class="checkbox check-wst" type="checkbox" name="websites[]" id="website-'.$r['id'].'" value="'.$r['id'].'" '.$checked.' />
					<label for="website-'.$r['id'].'">'.htmlspecialchars( $r['name'] ).'</label><br/>
					<div class="language"'.((ria_mysql_num_rows($rlng) > 0)?' ':' hidden').'>
			';

			if (ria_mysql_num_rows($rlng) > 0) {
				while( $lng = ria_mysql_fetch_array($rlng) ){
					$checked = '';
					$link = '';
					switch( $class ){
						case CLS_DOCUMENT: {
							$checked = doc_websites_exists( $r['id'], $obj, $lng['lng_code'] ) ? ' checked="checked"' : '';
							break;
						}
						case CLS_NEWS: {
							$checked = news_websites_exists( $obj, $r['id'], $lng['lng_code'] ) ? ' checked="checked"' : '' ;
							$link = '<a href="'.wst_websites_languages_get_url($r['id'], $lng['lng_code']).$url.'" target="_blank" title="vers le site concerné" ></a>';
							break;
						}
						case CLS_BANNER: {
							$checked = adv_websites_exists( $obj, $r['id'], $lng['lng_code'] ) ? ' checked="checked"' : '';
							break;
						}
					}

					$html .= '
						<input class="checkbox" type="checkbox" name="language['.$r['id'].'][]" id="language-'.$r['id'].'-'.$lng['lng_code'].'" value="'.$lng['lng_code'].'" '.$checked.' />
						<label for="language-'.$r['id'].'-'.$lng['lng_code'].'"> '.i18n_languages_get_name($lng['lng_code']).'</label>'.$link.'<br />
					';
				}
			}
			else if (isset($config['i18n_lng']) ) {
				$checked = '';
				$link = '';
				switch( $class ){
					case CLS_DOCUMENT: {
						$checked = doc_websites_exists( $r['id'], $obj, $config['i18n_lng'] ) ? ' checked="checked"' : '';
						break;
					}
					case CLS_NEWS: {
						$checked = news_websites_exists( $obj, $r['id'], $config['i18n_lng'] ) ? ' checked="checked"' : '' ;
						$link = '<a href="'.wst_websites_languages_get_url($r['id'], $config['i18n_lng']).$url.'" target="_blank" title="vers le site concerné" ></a>';
						break;
					}
					case CLS_BANNER: {
						$checked = adv_websites_exists( $obj, $r['id'], $config['i18n_lng'] ) ? ' checked="checked"' : '';
						break;
					}
				}

				$html .= '
					<input diplay="none" class="checkbox" type="checkbox" name="language['.$r['id'].'][]" id="language-'.$r['id'].'-'.$config['i18n_lng'].'" value="'.$config['i18n_lng'].'" '.$checked.' />
					<label diplay = "none" for="language-'.$r['id'].'-'.$config['i18n_lng'].'"> '.i18n_languages_get_name($config['i18n_lng']).'</label>'.$link.'<br />
				';

			}
			$html .= '
					</div>
				</div>
			';
		}

		$html .= '
				</td>
			</tr>
		';
	}else{
		if( $wst ){
			while( $w = ria_mysql_fetch_assoc($wst) ){
				foreach( $config['i18n_lng_used'] as $one_lang ){
					print '<tr><td>
						<input type="hidden" name="language['.$w['id'].'][]" id="language-'.$w['id'].'-'.$one_lang.'" value="'.$one_lang.'" />
					</td></tr>';
				}
			}
		}
	}

	return $html;
}

/**	Permet la visualisation de l'arborescence des catégories enfants d'une catégorie de comparateur de prix donnée
 *	@param $nod Obligatoire, identifiant du noeud parent
 *	@param $type Facultatif, type d'élément d'interface de sélection : soit radio pour une sélection unique, soit checkbox pour une sélection multiple
 *	@param int $ctr_id Facultatif, identifiant de la catégorie du comparateur
 */
function view_cat_childs_arbo( $nod, $type='', $ctr_id=false ){
	global $config;

	$r = '';
	$children = $nod['children'];

	$r .= '
		<li>
	';

	$attr = $title = '';
	if( $ctr_id ){
		$havechilds = ctr_categories_have_childs( $ctr_id, $nod['id'] );
		if( in_array($ctr_id, $config['ctr_last_level']) && $havechilds ){
			$attr  = ' disabled="disabled"';
			$title = ' title="Vous ne pouvez choisir cette catégorie directement. Veuillez sélectionner une sous-catégorie."';
		}
	}

	if( trim($type)=='' ){
		$r .= '
			<span class="tree-nod-name name" onclick="show_card('.$nod['id'].', '.$_GET['ctr'].');" id="spanprd-'.$nod['id'].'">'.$nod['name'].'</span>
		';
	}elseif( $type=='radio' ){
		$r .= '
			<input '.$attr.' '.$title.' type="radio" name="cat" id="cat-'.$nod['id'].'" value="'.$nod['id'].'" />
			<label '.$title.' for="cat-'.$nod['id'].'" id="id-'.$nod['id'].'">'.$nod['name'].'</label>
		';
	}elseif( $type=='checkbox' ){
		$r .= '
			<input '.$attr.' '.$title.' type="checkbox" name="cat[]" id="cat-'.$nod['id'].'" value="'.$nod['id'].'" '.( !$nod['publish'] ? 'checked="checked"' : '' ).' />
			<label '.$title.' for="cat-'.$nod['id'].'" id="id-'.$nod['id'].'">'.$nod['name'].'</label>
		';
	}

	if (count($children) > 0) {
		ksort($children);
		$r .= '
			<ul>
		';

		foreach( $children as $child ){
			$r .= view_cat_childs_arbo( $child, $type, $ctr_id );
		}

		$r .= '
			</ul>
		';
	}

	$r .= '
		</li>
	';

	return $r;
}

/** Cette fonction charge tous les selecteurs de la partie Commandes et Stats
 *	@param int $wst_id Optionnel, identifiant du site déjà sélectionné
 *	@param $all_website Optionnel, par défaut on peut choisir "Tous les sites", mettre false pour ne pas avoir cette option
 *	@param $class Optionnel, classe particulière à appliquer à cette liste déroulante
 *	@param $in_menu Optionnel, par défaut à false, mettre true pour inclure la liste dans <div class="stats-menu"></div>
 *	@param $label Optionnel, texte à afficher quand aucun site n'est sélectionné, par défaut : 'Tous les sites'
 *	@param $not_website Facultatif, si true, l'option spéciale "Sur aucun site" sera disponible dans le menu. La valeur par défaut est false.
 *	@param $multi Facultatif. Si vrai, le menu autorise la sélection multiple. La valeur par défaut est false (sélection d'un seul élément)
 *
 *	@return string le code HTML des sélecteurs
 */
function view_all_selectors( $wst_id=0, $all_website=true, $class='', $in_menu=false, $label='Tous les sites', $not_website=false, $multi=false ){
	global $config;

	$html='';

	// Filtre sur les sites (seulement si un ou plusieurs sites web)
	if( tnt_tenants_have_websites() ){
		$html .= view_websites_selector( $wst_id, $all_website, $class, $in_menu, $label, $not_website, $multi);
	}

	// Filtre sur les origines (seulement si un ou plusieurs sites web)
	$html .= view_origins_selector();

	// Filtre sur le moyen de paiement utilisé
	$html .= view_pay_selector();

	// Filtre sur le représentant
	$html .= view_sellers_selector();

	// Filtre sur un magasin
	$html .= view_stores_origins();

	return $html;
}

/** Cette fonction charge le sélecteur de site
 *
 *	@param int $wst_id Optionnel, identifiant du site déjà sélectionné
 *	@param $all_website Optionnel, par défaut on peut choisir "Tous les sites", mettre false pour ne pas avoir cette option
 *	@param $class Optionnel, classe particulière à appliquer à cette liste déroulante
 *	@param $in_menu Optionnel, par défaut à false, mettre true pour inclure la liste dans <div class="stats-menu"></div>
 *	@param $label Optionnel, texte à afficher quand aucun site n'est sélectionné, par défaut : 'Tous les sites'
 *	@param $not_website Facultatif, si true, l'option spéciale "Sur aucun site" sera disponible dans le menu. La valeur par défaut est false.
 *	@param $multi Facultatif. Si vrai, le menu autorise la sélection multiple. La valeur par défaut est false (sélection d'un seul élément)
 *	@param $no_yuto Optionnel, par défaut ignoré, mettre true pour ne pas tenir compte du site lié à Yuto
 *
 *	@return string Le code HTML du sélecteur
 */
function view_websites_selector( $wst_id=0, $all_website=true, $class='', $in_menu=false, $label='Tous les sites', $not_website=false, $multi=false, $no_yuto=false ){
	global $config;

	$wty_id = false;
	if( $no_yuto ){
		$wty_id = [_WST_TYPE_SHOP, _WST_TYPE_EXTRANET, _WST_TYPE_SUPPLIER, _WST_TYPE_EXT_FDV, _WST_TYPE_PUBLIC];
	}

	$rwst = wst_websites_get( 0, false, null, false, $wty_id);

	$ar_websites = array();
	if( $rwst && ria_mysql_num_rows($rwst) ){
		while( $wst = ria_mysql_fetch_array($rwst) ){
			if( isset($config['selector_websites_excluded']) && in_array($wst['id'], $config['selector_websites_excluded']) ){
				continue;
			}

			$ar_websites[] = $wst;
		}
	}

	if( $multi ){
		$class .= ( trim($class) == '' ? '' : ' ' ).'smulti';
	}

	$html = '';
	if( sizeof($ar_websites)>1 ){
		if( !$in_menu ){
			$html .= '
				<div class="stats-menu">
			';
		}

		$view = '';
		if( is_array($wst_id) && sizeof($wst_id) ){
			if( sizeof($ar_websites) == sizeof($wst_id) ){
				$view = 'Tous les sites';
			}else{
				foreach( $wst_id as $one_wst ){
					$view .= ( trim($view) != '' ? ', ' : '' ).wst_websites_get_name( $one_wst );
				}

				$view = strcut( $view, 33 );
			}
		}elseif( is_numeric($wst_id) && $wst_id > 0 ){
			$view = wst_websites_get_name( $wst_id );
		}

		$html .= '
				<div id="riawebsitepicker" class="'.$class.'" data-defaultVal="w-0" data-selectorname="wst_id">';
		$html .=	'<div class="selectorview">
						<div class="left">
							<span class="function_name">'._('Sites web').'</span><br/>
							<span class="view">'.htmlspecialchars( trim($view) != '' ? $view : $label ).'</span>
						</div>
						<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif?1" alt=""/></a>
						<div class="clear"></div>
					</div>
					<div class="selector">
		';

		if( $all_website ){
			$html .= '
						<a name="w-0">'._('Tous les sites').'</a>
						<a class="selector-sep"></a>
			';
		}

		foreach( $ar_websites as $wst ){
			if( !$multi ){
				$html .= '
							<a name="w-'.$wst['id'].'">'.htmlspecialchars($wst['name']).'</a>
				';
			}else{
				$checked = '';
				if( isset($_SESSION['websitepicker']) && $_SESSION['websitepicker'] == $wst['id'] ){
					$checked = 'checked="checked"';
				}elseif( isset($_SESSION['ar_websitepicker']) && is_array($_SESSION['ar_websitepicker']) && in_array($wst['id'], $_SESSION['ar_websitepicker']) ){
					$checked = 'checked="checked"';
				}

				$html .= '
							<a name="w-'.$wst['id'].'">
								<input '.$checked.' type="checkbox" name="wst-id[]" id="s-wst-id-'.$wst['id'].'" value="'.$wst['id'].'" />
								<label for="s-wst-id-'.$wst['id'].'">'.htmlspecialchars($wst['name']).'</label>
							</a>
				';
			}
		}

		if( $not_website ){
			$html .= '
				<a class="selector-sep"></a>
				<a name="w--1">'._('Sur aucun site').'</a>
			';
		}

		$html .= '
					</div>
				</div>
		';

		if( !$in_menu ){
			$html .= '
					<div class="clear"></div>
				</div>
			';
		}
	}

	ob_start();
	?><script>
		$('#riawebsitepicker .selectorview').click(function(){
			if($('#riawebsitepicker .selector').css('display')=='none'){
				$('.selector').hide()
				$('#riawebsitepicker .selector').show();
			}else{
				$('#riawebsitepicker .selector').hide();
			}
			return false;
		});
	</script><?php
	$html .= ob_get_clean();

	return $html;
}

/** Cette fonction permet de charger le sélecteur de moyens de paiement
 */
function view_pay_selector(){

	global $ar_payments; // variable globale afin que le résultat puisse être réutilisé à l'endroit de l'appel
	$ar_payments = ord_payment_types_get_array();
	$ar_payments = array_msort( $ar_payments, array('pos'=>SORT_ASC, 'name'=>SORT_ASC) );

	ob_start();
	if( sizeof($ar_payments)>0 ){
		print '
			<div class="riapicker" id="selectpaytype" data-defaultVal="pay-0" data-selectorName="pay_id">
			<div class="selectorview">
				<div class="left">
					<span class="function_name">'._('Moyens de paiement').'</span>
		';

		$is_pay_check = false;
		foreach( $ar_payments as $pay ){
			if( isset($_SESSION['ord_pay_id']) && $pay['id']==$_SESSION['ord_pay_id'] ){
				print '<br /><span class="view">'.htmlspecialchars($pay['name']).'</span>';
				$is_pay_check = true;
				break;
			}
		}

		if( !$is_pay_check ){
			print '<br /><span class="view">'._('Tous les moyens').'</span>';
		}

		print '
					</div>
					<a name="btn" class="btn">
						<img class="fleche-stats" width="16" height="8" alt="" src="/admin/images/stats/fleche.gif?1" />
					</a>
					<div class="clear"></div>
				</div>
				<div class="selector">
					<a name="pay-0">'._('Tous les moyens').'</a>
		';

		foreach( $ar_payments as $pay ){
			print '<a name="pay-'.$pay['id'].'">'.htmlspecialchars( $pay['name'] ).'</a>';
		}

		print '
				</div>
			</div>
		';
	}
	$html = ob_get_contents();
	ob_end_clean();
	return $html;
}

/** Cette fonction permet de charger le sélecteur de dépôts
 *	@param int $tnt_id Obligatoire, identifiant du tenant
 */
function view_deposits_selector($tnt_id){
	$ar_deposits = prd_deposits_get_array( $tnt_id );

	ob_start();
	if( sizeof($ar_deposits)>0 ){
		print '
			<div class="riapicker hide-mobile" id="selectdeposit">
				<div class="selectorview">
					<div class="left">
						<span class="function_name">'._('Choix du dépôt').'</span>
		';

		$is_deposit_check = false;
		foreach( $ar_deposits as $deposit ){
			if( $deposit['id']==$_SESSION['ord_dps_id'] ){
				print '<br /><span class="view">'.htmlspecialchars($deposit['name']).'</span>';
				$is_deposit_check = true;
				break;
			}
		}

		if( !$is_deposit_check ){
			print '<br /><span class="view">'._('Tous les dépôts').'</span>';
		}

		print '
					</div>
					<a name="btn" class="btn">
						<img class="fleche-stats" alt="" src="/admin/images/stats/fleche.gif" />
					</a>
					<div class="clear"></div>
				</div>
				<div class="selector">
					<a name="dps-0">'._('Tous les dépôts').'</a>
		';

		foreach( $ar_deposits as $deposit ){
			print '<a name="dps-'.$deposit['id'].'">'.htmlspecialchars( $deposit['name'] ).'</a>';
		}

		print '
				</div>
			</div>
		';
	}
	$html = ob_get_contents();
	ob_end_clean();
	return $html;
}

/** Cette fonction permet de charger le sélecteur de commerciaux
 *	@param bool $use_user_id Optionnel, par défaut à False, mettre True pour utiliser l'identifiant client
 *	@param bool $include_admin Optionnel, par défaut à False, mettre True pour inclure les administrateur
 *	@param string $class_selector Optionnel par défaut à "selector", classe(s) appliquée(s) sur la liste déroulante
 *	@param string $id Optionnel par default "selectseller", identifiant du bloc contenant le sélecteur
 *	@return string Le code HTML du sélecteur, à envoyer au navigateur
 */
function view_sellers_selector( $use_user_id=false, $include_admin=false, $class_selector='selector', $id="selectseller" ){
	global $config;

	if( !isset($_SESSION['usr_prf_id']) ){
		return;
	}

	$profiles = array(PRF_SELLER);
	if ($include_admin) {
		$profiles[] = PRF_ADMIN;
	}

	if( $_SESSION['usr_prf_id'] == PRF_SELLER ){
		return;
	}

	$sellers = gu_users_get(0, '', '', $profiles, '', 0, '', 'name', false, 0, false, '', false, 0, '', 0, false, $config['tnt_id']);
	$html = '';
	if( $sellers && ria_mysql_num_rows($sellers) ){
		$html .= '
			<div class="riapicker" id="'.$id.'" data-defaultVal="seller-0" data-selectorName="seller_id">
				<div class="selectorview">
					<div class="left">
						<span class="function_name">'._('Représentants').'</span>
		';

		$is_seller_check = false;
		while( $seller = ria_mysql_fetch_assoc($sellers) ){
			$s_id = $use_user_id ? $seller['id'] : $seller['seller_id'];

			if( isset($_SESSION['ord_seller_id']) && $_SESSION['ord_seller_id'] > 0 && $s_id==$_SESSION['ord_seller_id'] ){
				$name = '';
				if( $seller['type_id']==1 ){
					$name = $seller['adr_lastname'].', '.$seller['adr_firstname'];
				}elseif( $seller['type_id']==2 ){
					$name = $seller['society'];
				}else{
					if( $seller['society']!='' ) {
						$name = $seller['society'].', ';
					}
					$name .= $seller['adr_lastname'].', '.$seller['adr_firstname'];
				}
				$html .= '<br /><span class="view">'.htmlspecialchars($name).'</span>';
				$is_seller_check = true;
				break;
			}
		}

		if( !$is_seller_check ){
			$html .= '<br /><span class="view">'._('Tous les représentants').'</span>';
		}

		$html .= '
					</div>
					<a href="javascript:;">
						<img width="16" height="8" alt="" src="/admin/images/stats/fleche.gif?1" />
					</a>
					<div class="clear"></div>
				</div>
				<div class="'.$class_selector.'" >
					<a name="seller-0">'._('Tous les représentants').'</a>
		';
		ria_mysql_data_seek($sellers, 0);
		while( $seller = ria_mysql_fetch_assoc($sellers) ){
			$s_id = $use_user_id ? $seller['id'] : $seller['seller_id'];

			if( $s_id ) {
				$name = '';
				if( $seller['type_id']==1 ){
					$name = $seller['adr_lastname'].', '.$seller['adr_firstname'];
				}elseif( $seller['type_id']==2 ){
					$name = $seller['society'];
				}else{
					if( $seller['society']!='' ) {
						$name = $seller['society'].', ';
					}
					$name .= $seller['adr_lastname'].', '.$seller['adr_firstname'];
				}
				$html .= '<a name="seller-'.$s_id.'">'.htmlspecialchars($name).'</a>';
			}
		}

		$html .= '
				</div>
			</div>
		';
	}

	return $html;
}

/** Cette fonction permet de générer le sélécteur de central, il retourne une liste déroulente des compte clients avec le champs avancé _FLD_USR_CENTRAL de renseigné
 * @return string Retourne le HTML du sélecteur de central, si aucun client correspond au critère le sélecteur n'est pas affiché
 */
function view_centrals_selector(){
	global $config;

	$r_centrals = gu_users_get_centrals();
	if (!$r_centrals || !ria_mysql_num_rows($r_centrals)) {
		return;
	}

	ob_start();
	while ($central = ria_mysql_fetch_assoc($r_centrals)) {
			$name = trim($central['central']);
			if( !trim($name) ){
				continue;
			}
			if( isset($_SESSION['central_id']) && $name == $_SESSION['central_id'] ){
				$view = $name;
			}
		?>
		<a name="central-<?php echo $name?>"><?php echo $name ?></a>
	<?php }
	$list = ob_get_clean();
	if (!isset($view)) {
		$view = _('Toutes les centrales');
	}

	ob_start();
	?>
		<div class="riapicker" id="select_central">
            <div class="selectorview">
                <div class="left">
                    <span class="function_name"><?php echo _('Centrale')?></span>
                    <br><span class="view"><?php echo $view ?></span>
                </div>
                <a name="btn" class="btn">
                    <img src="/admin/images/stats/fleche.gif?1" width="16" height="8">
                </a>
                <div class="clear"></div>
            </div>
            <div class="selector">
				<a name="central-0"><?php echo _('Toutes les centrales') ?></a>
				<?php echo $list ?>
            </div>
        </div>
	<?php
	return ob_get_clean();
}

/** Cette fonction permet d'afficher un sélecteur de département français en fonction des utilisateurs liés au tenant
 *
 * @param array|int $prf_id Facultatif, identifiant de profil permet de filtrer les clients. Valeur par défaut : null
 * @param array|int $user_id Facultatif, Identifiant d'un compte client ou tableau d'identifiants de comptes client
 *
 * @return string Retourne le code html du sélecteur
 */
function view_users_fr_depts_selector($prf_id=null, $user_id=null){
	global $config;

	$ar_depts = gu_users_get_fr_depts($prf_id, $user_id);
	if (!is_array($ar_depts) || empty($ar_depts)) {
		return '';
	}
	$view = $label = _('Tous les départements');
	ob_start();
	foreach ($ar_depts as $dept) {
			$name = $dept['dept'];
			if( isset($_SESSION['fr_depts']) && $_SESSION['fr_depts'] && $dept['dept']==$_SESSION['fr_depts'] ){
				$view = $name;
			}
		?>
		<a name="fr_dept-<?php echo htmlspecialchars($name); ?>"><?php echo htmlspecialchars($name); ?></a>
	<?php }
	$list = ob_get_clean();

	ob_start()
	?>
		<div class="riapicker" id="select_fr_depts">
            <div class="selectorview">
                <div class="left">
                    <span class="function_name"><?php echo _('Département')?></span>
                    <br><span class="view"><?php echo htmlspecialchars($view) ?></span>
                </div>
                <a name="btn" class="btn">
                    <img src="/admin/images/stats/fleche.gif?1" width="16" height="8">
                </a>
                <div class="clear"></div>
            </div>
            <div class="selector">
				<a name="fr_dept-0"><?php echo htmlspecialchars($label) ?></a>
				<?php echo $list ?>
            </div>
        </div>
	<?php
	return ob_get_clean();
}

/** Cette fonction permet de charges le sélecteur de type de CA.
 *	@param $selected Optionnel, valeur déjà sélectionné
 *	@return string Le code HTML du sélecteur
 */
function view_ca_selector($selected=''){
	$allowed_ca = array(
		'ord_ca_all' => 'Marge brute directes et indirectes',
		'ord_ca_direct' => 'Marge brute directes',
		'ord_ca_indirect' => 'Marge brute indirectes',
		'inv_ca' => 'Factures',
	);

	if( $selected == '' ){
		$selected = 'ord_ca_all';
	}

	ob_start();
	?>
	<div class="riapicker" id="selectCA" data-defaultVal="ord_ca_indirect" data-selectorName="CA">
		<div class="selectorview">
			<div class="left">
				<?php print '<span class="function_name">Chiffres d\'affaires</span>'; ?>
				<br /><span class="view"><?php echo htmlspecialchars(substr ( $allowed_ca[$selected], 0 , 30 ).'...') ?></span>
			</div>
			<a name="btn" class="btn">
				<img width="16" height="8" alt="" src="/admin/images/stats/fleche.gif?1" />
			</a>
			<div class="clear"></div>
		</div>
		<div class="selector">
			<?php foreach ($allowed_ca as $type => $text): ?>
				<a name="<?php echo $type?>" <?php echo $type!=$selected ?  : 'selected'; ?>><?php echo $text ?></a>
			<?php endforeach ?>
		</div>
	</div>
	<?php

	return ob_get_clean();
}

/** Cette fonction permet de charger le sélecteur de magasins d'origine
 */
function view_stores_origins(){
	// Récupère le site sélectionné
	$wst_id = isset( $_SESSION['websitepicker'] ) ? str_replace('w-', '', $_SESSION['websitepicker']) : 0;
	$rorders = ord_orders_get_with_adresses(0, 0, 0, '', false, false, false, false, null, false, false, false, false, 0, $wst_id);
	$html = $html_stores = '';
	$stores_id = array();
	$label = '';

	if( $rorders && ria_mysql_num_rows($rorders) ){
		while( $order = ria_mysql_fetch_assoc($rorders) ){
			if( isset($order['str_id']) && $order['str_id']) {
				$name = dlv_stores_get_name($order['str_id']);
				$name = $name ? $name : "";
				if(trim($name)!="" && !in_array($order['str_id'], $stores_id)){
					array_push($stores_id, $order['str_id']);
					$checked = '';
					if( isset($_SESSION['ord_store_id']) && is_array($_SESSION['ord_store_id']) && in_array($order['str_id'], $_SESSION['ord_store_id']) ){
						$checked = 'checked="checked"';
						$label .= trim($label)!="" ? ", ".$name:$name;
					}

					$html_stores .= '
								<a name="store-'.$order['str_id'].'">
									<input '.$checked.' type="checkbox" name="store-id[]" id="s-store-id-'.$order['str_id'].'" value="'.$order['str_id'].'" />
									<label for="s-store-id-'.$order['str_id'].'">'.htmlspecialchars($name).'</label>
								</a>
					';
				}
			}
		}
		if( trim($html_stores) == '' ){
		    return '';
        }
		$html .= '
			<div class="riapicker" id="selectstore" data-defaultVal="all" data-selectorName="stores">
				<div class="selectorview">
					<div class="left">
						<span class="function_name">Magasins d\'origine</span>';
				$html .='<br /><span class="view">'.htmlspecialchars( trim($label) != '' ? strcut( $label, 33 ) : 'Tous les magasins').'</span>';

		$html .= '
					</div>
					<a name="btn" class="btn">
						<img width="16" height="8" alt="" src="/admin/images/stats/fleche.gif?1" />
					</a>
					<div class="clear"></div>
				</div>
				<div class="selector">
					<a name="all">Tous les magasins</a>
					<a class="selector-sep"></a>
		';

		$html .= $html_stores;


		$html .= '
				</div>
			</div>
		';
	}

	return $html;
}

/** Cette fonction permet de charger le sélecteur d'origine
 *	@param $content Facultatif, si faux (valeur par défaut), le sélecteur sera inclus dans un \c div d'id \c selectorigins. Si faux, ce div ne sera pas ajouté.
 */
function view_origins_selector( $content=false ){
	global $config;
	// Récupère la période sélectionnée
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1)  ? $date1 : $date2;

	$html = '';

	if( !$content ){
		$html .= '
			<div id="selectorigins" class="riapicker items-list-filters" data-defaultVal="all" data-selectorName="origin">
		';
	}

	$html .= '
			<div class="selectorview">
				<div class="left">
					<span class="function_name">'._('Origine de la commande').'</span>
					<br/><span class="view">
	';

	$origin = array();
	if( isset($_SESSION['origin']) ){
		$origin = is_array($_SESSION['origin']) ? $_SESSION['origin'] : array( $_SESSION['origin'] );
	}

	$is_yuto = !tnt_tenants_have_websites();

	$device = 'Web';
	if ($is_yuto) {
		$device = 'Yuto';
	}

	if( is_array($origin) && sizeof($origin) ){
		$sub_html = array();
		foreach( $origin as $o ){
			if( $o=='all' ){
				$html .= _('Tout le trafic');
			}elseif( $o=='noorigin' ){
				$html .= _('Origine inconnue');
			}else{
				if( mb_strstr($o, 'ctr-') ){
					// comparateur de prix
					$ctr_id = mb_ereg_replace( 'ctr-', '', $o );
					if( trim($ctr_id) ){
						$sub_html[] .= ctr_comparators_get_name($ctr_id);
					}
				} else {
					switch( $o ){
						case 'noorigin' :
							$sub_html[] .= _('Origine inconnue'); break;
						case 'direct' :
							$sub_html[] .= _('Accès direct'); break;
						case 'referal' :
							$sub_html[] .= _('Sites référents'); break;
						case 'natural' :
							$sub_html[] .= _('Référencement naturel'); break;
						case 'adwords' :
							$sub_html[] .= _('Google AdWords'); break;
						case 'gescom' :
							$sub_html[] .= _('Gestion commerciale'); break;
						case 'web' :
							$sub_html[] .= $device; break;
						case 'newsletters' :
							$sub_html[] .= _('Newsletters'); break;
						case 'recommendation' :
							$sub_html[] .= _('Recommandations'); break;
						case 'all-web' :
						case 'all-ctr' :
						case 'all-mkt' :
							break;
						default :
							$sub_html[] .= _('Tout le trafic'); break;
					}
				}
			}
		}

		if( sizeof($sub_html) ){
			$html .= strcut( implode( ', ', $sub_html ), 33 );
		}
	}else{
		$html .= 'Tout le trafic';
	}

	$html .= '
				</span></div>
				<a class="btn" name="btn">
					<img src="/admin/images/stats/fleche.gif?1" height="8" width="16" alt="" />
				</a>
				<div class="clear"></div>
			</div>';
	ob_start();
	?>
			<div class="selector">
				<a name="all"><?php print _('Tout le trafic'); ?></a>
				<a name="noorigin"><?php print _('Origine inconnue'); ?></a>
				<?php if (!$is_yuto) { ?>
					<a class="selector-sep"></a>
					<div>
						<a class="parent" name="o-all-web">
							<input type="checkbox" name="origin[]" id="o-all-web" value="all-web" />
							<label for="o-all-web"><?php print _('Site(s) web'); ?></label>
						</a>
						<a class="child" name="o-direct">
							<input <?php echo in_array('direct', $origin) ? 'checked="checked"' : ''; ?> type="checkbox" name="origin[]" id="o-direct" value="direct" />
							<label for="o-direct"><?php print _('Accès direct'); ?></label>
						</a>
						<a class="child" name="o-referal">
							<input <?php echo in_array('referal', $origin) ? 'checked="checked"' : ''; ?> type="checkbox" name="origin[]" id="o-referal" value="referal" />
							<label for="o-referal"><?php print _('Sites référents'); ?></label>
						</a>
						<a class="child" name="o-natural">
							<input <?php echo in_array('natural', $origin) ? 'checked="checked"' : ''; ?> type="checkbox" name="origin[]" id="o-natural" value="natural" />
							<label for="o-natural"><?php print _('Référencement naturel'); ?></label>
						</a>
						<a class="child" name="o-adwords">
							<input <?php echo in_array('adwords', $origin) ? 'checked="checked"' : '';?> type="checkbox" name="origin[]" id="o-adwords" value="adwords" />
							<label for="o-adwords"><?php print _('Google AdWords'); ?></label>
						</a>
						<a class="child" name="o-ctr-4">
							<input <?php echo in_array('ctr-4', $origin) ? 'checked="checked"' : ''; ?> type="checkbox" name="origin[]" id="o-ctr-4" value="ctr-4" />
							<label for="o-ctr-4"><?php print _('Google Shopping'); ?></label>
						</a>
						<a class="child" name="o-newsletters">
							<input <?php echo in_array('newsletters', $origin) ? 'checked="checked"' : ''; ?> type="checkbox" name="origin[]" id="o-newsletters" value="newsletters" />
							<label for="o-newsletters"><?php print _('Newsletters'); ?></label>
						</a>
						<a class="child" name="o-recommendation">
							<input <?php echo in_array('recommendation', $origin) ? 'checked="checked"' : ''; ?> type="checkbox" name="origin[]" id="o-recommendation" value="recommendation" />
							<label for="o-recommendation"><?php print _('Recommandations sur le site'); ?></label>
						</a>
					</div>
				<?php }?>
	<?php
	$html .= ob_get_clean();

	// récupère les comparateurs actifs
	$rctr = ctr_comparators_get( 0, true, true );
	if( $rctr && ria_mysql_num_rows($rctr) ){
		$tmp = '
				<div>
					<a class="selector-sep" ></a>
					<a class="parent" name="o-all-ctr">
						<input type="checkbox" name="origin[]" id="o-all-ctr" value="all-ctr" />
						<label for="o-all-ctr">'._('Comparateurs de prix').'</label>
					</a>
		';

		$have_ctr = false;
		while( $ctr = ria_mysql_fetch_array($rctr) ){
			if( $ctr['id'] == CTR_GOOGLE ){
				continue;
			}

			$have_ctr = true;

			$checked = '';
			if( isset($_SESSION['origin']) && is_array($_SESSION['origin']) && in_array('ctr-'.$ctr['id'], $_SESSION['origin']) ){
				$checked = 'checked="checked"';
			}

			$tmp .= '
					<a class="child" name="o-ctr-'.$ctr['id'].'">
						<input '.$checked.' type="checkbox" name="origin[]" id="o-ctr-'.$ctr['id'].'" value="ctr-'.$ctr['id'].'" />
						<label for="o-ctr-'.$ctr['id'].'">'.htmlspecialchars($ctr['name']).'</label>
					</a>
			';
		}

		$tmp .= '
			</div>
		';

		if( $have_ctr ){
			$html .= $tmp;
		}
	}

	// récupère les places de marchés actives
	$rctr = ctr_comparators_get( 0, true, true, true );
	if( $rctr && ria_mysql_num_rows($rctr) ){
		$html .= '
				<div>
					<a class="selector-sep" ></a>
					<a class="parent" name="o-all-mkt">
						<input type="checkbox" name="origin[]" id="o-all-mkt" value="all-mkt" />
						<label for="o-all-mkt">'._('Places de marché').'</label>
					</a>
		';

		while( $ctr = ria_mysql_fetch_array($rctr) ){
			$checked = '';
			if( isset($_SESSION['origin']) && is_array($_SESSION['origin']) && in_array('ctr-'.$ctr['id'], $_SESSION['origin']) ){
				$checked = 'checked="checked"';
			}

			if( $ctr['id'] == CTR_BEEZUP){
				require_once('comparators/BeezUP.class.php');
				$cfg_flow_ord = BeezUP::config();

				$BeezUP = new BeezUp( $cfg_flow_ord['account']['user_id'], $cfg_flow_ord['account']['token'] );
				$use_business_code = in_array( $config['tnt_id'], [171] );

				if( $use_business_code ){
					$list_marketplace = $BeezUP->getMarketplaceListByBusiness();
				}else{
					$list_marketplace = $BeezUP->getMarketplaceList();
				}

				foreach( $list_marketplace as $market ){
					if( !isset($cfg_flow_ord['user'][$market['code']]) ){
						continue;
					}

					$html .= '
							<a class="child" name="o-beezup-'.$market['code'].'">
								<input '.$checked.' type="checkbox" name="origin[]" id="o-beezup-'.$market['code'].'" value="'.strtolower2( $market['code'] ).'" />
								<label for="o-beezup-'.$market['code'].'">'.htmlspecialchars($market['name']).'</label>
							</a>
					';
				}
			}else{
				$html .= '
						<a class="child" name="o-ctr-'.$ctr['id'].'">
							<input '.$checked.' type="checkbox" name="origin[]" id="o-ctr-'.$ctr['id'].'" value="ctr-'.$ctr['id'].'" />
							<label for="o-ctr-'.$ctr['id'].'">'.htmlspecialchars($ctr['name']).'</label>
						</a>
				';
			}
		}

		$html .= '
			</div>
		';
	}

	$html .= '
				<a class="selector-sep" ></a>
	';

	// vérifier s'il existe au moins une commande venant de la gestion commerciale
	$tmpord = ord_orders_get_simple(array(), array('start' => $date1, 'end' => $date2, array('is_web' => false)));
	if( $tmpord && ria_mysql_num_rows($tmpord) ){
		$html .= '
				<a name="gescom">'._('Gestion Commerciale').'</a>
		';
	}

	$html .= '
				<a name="web">'.htmlspecialchars( $device ).'</a>
			</div>
	';

	if( !$content ){
		$html .= '
			</div>
		';
	}

	return $html;
}

/** Cette fonction charge un tableau contenant les paramètres d'origine(s) sélectionnée(s).
 *	@param $origin Optionnel, origine pré-sélectionnée, par défaut on récupère le contenu de la variable $_SESSION['origin']
 *	@return array Un tableau conteant :
 *				- origin : origine(s) sélectionnée(s)
 *				- gescom : provenant de la gestion
 *				- is_web : uniquement web
 */
function view_origins_get_params( $origin=false ){
	$p = array(
		'origin' => false,
		'gescom' => null,
		'is_web' => null
	);

	if( $origin===false ){
		$origin = isset($_SESSION['origin']) ? $_SESSION['origin'] : false;
	}

	if( $origin!==false ){
		if( !is_array($origin) ){
			$origin = array( $origin );
		}

		foreach( $origin as $o ){
			if( mb_strstr($o, 'ctr-') ){
				// Comparateur de prix
				$ctr_id = mb_ereg_replace( 'ctr-', '', $o );
				$source = ctr_comparators_get_source( $ctr_id );
				if( trim($source) ){
					$p['origin'][] = array('source'=>$source);
				}
			} else {
				switch( $o ){
					case 'noorigin' :{
						$p['gescom'] = false;
						$p['is_web'] = true;
						$p['origin'] = -1;
						break;
					}
					case 'direct' :{
						$p['origin'][] = array('source'=>'(direct)');
						break;
					}
					case 'referal' :{
						$p['origin'][] = array('name'=>'(referral)', 'medium'=>'referral');
						break;
					}
					case 'natural' :{
						$p['origin'][] = array('medium'=>'organic');
						break;
					}
					case 'adwords' :{
						$p['origin'][] = array('source'=>'google', 'medium'=>'cpc');
						break;
					}
					case 'gescom' :{
						$p['gescom'] = true;
						$p['is_web'] = false;
						break;
					}
					case 'web' :{
						$p['gescom'] = false;
						$p['is_web'] = true;
						break;
					}
					case 'newsletters' :{
						$p['origin'][] = array('source'=>'newsletters');
						break;
					}
					case 'recommendation' :{
						$p['origin'][] = array('medium'=>'site');
						break;
					}
					default:{
						if( $o != 'all' ){
							$p['origin'][] = ['source' => $o];
						}
						break;
					}
				}
			}
		}
	}

	return $p;
}

/** Cette fonction permet d'afficher les statistiques globales liées aux points de fidélités
 *	@param int $usr_id Optionnel, filtrer selon l'identifiant d'un compte client
 *
 */
function view_rewards_get_global_stats( $usr_id=0 ){
	if( !is_numeric($usr_id) || $usr_id<0 ){
		return false;
	}

	print '
		<div id="tb-synthese-order" class="synthese-rewards">
			<table id="table-synthese-order" class="orders">
				<caption></caption>
				<thead>
					<tr>
						<th id="hd-rewards-total">'._('Solde disponible').'</th>
						<th id="hd-rewards-ttc">Solde en <abbr title="Toutes Taxes Comprises">TTC</abbr></th>
						<th id="hd-rewards-used">'._('Utilisés').'</th>
						<th id="hd-rewards-used-ttc">Utilisés en <abbr title="Toutes Taxes Comprises">TTC</abbr></th>
						<th id="hd-rewards-no-used">'._('Non utilisés').'</th>
						<th id="hd-rewards-no-used-ttc">Non utilisés en <abbr title="Toutes Taxes Comprises">TTC</abbr></th>
					</tr>
				</thead>
				<tbody>
	';

	$solde 	= gu_users_get_rewards_balance( ($usr_id > 0 ? $usr_id : 'all'), 'all' );
	$stats = stats_rewards_get_average( $usr_id );

	print '	<tr>';
	print '		<td headers="hd-rewards-total">'.ria_number_format($solde['solde'], NumberFormatter::DECIMAL).' point'.($solde['solde'] == 1 ? '' : 's').'</td>';
	print '		<td headers="hd-rewards-ttc">'.ria_number_format($solde['amount'], NumberFormatter::CURRENCY, 2).'</td>';
	print '		<td headers="hd-rewards-used">'.ria_number_format($stats['used_pts'], NumberFormatter::DECIMAL).' point'.($stats['used_pts'] == 1 ? '' : 's').'</td>';
	print '		<td headers="hd-rewards-used-ttc">'.ria_number_format($stats['used_amount'], NumberFormatter::CURRENCY, 2).'</td>';
	print '		<td headers="hd-rewards-no-used">'.ria_number_format($stats['neg_pts'], NumberFormatter::DECIMAL).' point'.($stats['neg_pts'] == 1 ? '' : 's').'</td>';
	print '		<td headers="hd-rewards-no-used-ttc">'.ria_number_format($stats['neg_amount'], NumberFormatter::CURRENCY, 2).'</td>';
	print '	</tr>';

	print '
				</tbody>
			</table>
		</div>
		<div class="clear"></div>
	';
}

/** Cette fonction permet d'afficher le tableau des statistiques liées aux points de fidélité
 *	@param int $usr Facultatif, filtrer selon l'identifiant d'un compte client
 *	@param false|int $rewards Facultatif, nombre de points fidélité de l'utilisateur. Si non renseigné, cette valeur sera chargée directement par la fonction.
 *	@return string Le code HTML du tableau des statistiques
 */
function view_rewards_get_stats( $usr=0, $rewards=false ){
	if( !is_numeric($usr) || $usr<0 ){
		return false;
	}

	if( !$rewards ){
		$rewards = stats_rewards_get( $usr );
	}

	// Permet de recupérer les statistiques de fidélité globales
	$solde 	= gu_users_get_rewards_balance( ($usr > 0 ? $usr : 'all'), 'all' );
	$soldeTotal = $solde['solde'];

	ob_start();
	if( $rewards && ria_mysql_num_rows($rewards) ){
		while( $reward = ria_mysql_fetch_assoc($rewards)){
			if($reward['pts'] > 0){ // on affcihe la ligne seulement si c'est des points positif
				$date = ria_date_format( $reward['date_en'], false, true );
				$date_limit = trim($reward['date_limit_en']) != '' ? ria_date_format( $reward['date_limit_en'], false, true ) : false;
				$amount = 0;

				// si le solde est plus petit que les points de la ligne, on affiche seulement les point restants et non les points totaux
				$soldeTotal = $soldeTotal - $reward['pts'];
				if( $soldeTotal > 0 ){
					$rewards_line = $reward['pts'];
				}else{
					$rewards_line = $reward['pts'] + $soldeTotal;
				}

				if( trim($reward['convert'])!='' ){
					$ar_convert = preg_split('/\//', $reward['convert']);
					$convert = $ar_convert[0] / $ar_convert[1];
					$amount = $rewards_line * $convert;
				}

				?><tr class="positive">
					<td headers="hdatecurrent" align="center"><?php print $date; ?></td>
					<td headers="hrwacurrent"><?php print htmlspecialchars($reward['name']); ?></td>
					<td headers="hptscurrent" align="right"><?php print ria_number_format($rewards_line, NumberFormatter::DECIMAL); ?></td>
					<td headers="hconvertcurrent" align="right"><?php print ria_number_format($amount, NumberFormatter::CURRENCY, 2); ?></td>
					<td headers="hlimitcurrent" align="center"><?php print $date_limit; ?></td>
				</tr><?php
			}

			if($soldeTotal <= 0){
				break;
			}
		}

		// on remet le curseur à 0 pour l'affcihage du tableau suivant
		ria_mysql_data_seek($rewards, 0);
	}

	$content = ob_get_clean();

	// Historique des points en cours
	if( trim($content) != '' ){
		print '
			<table id="stats-rewards-current" class="checklist">
				<caption>'._('Historique des points en cours').'</caption>
				<col width="100" /><col width="290" /><col width="115" /><col width="70" /><col width="100" />
				<thead>
					<tr>
						<th id="hdatecurrent">'._('Date').'</th>
						<th id="hrwacurrent">'._('Action réalisée').'</th>
						<th id="hptscurrent">'._('Points').'</th>
						<th id="hconvertcurrent">'._('Valeur<br />en €').'</th>
						<th id="hlimitcurrent">'._('Date limite d\'utilisation').'</th>
					</tr>
				</thead>
				<tbody>
		';

		print $content;

		print '
				</tbody>
				<tfoot>
				<tr><td colspan="5">
					<input name="export" id="export-rewards" data-usr="'.$_GET['usr'].'" value="'._('Exporter').'" title="'._('Exporter l\'historique des points en cours').'" type="submit" />
				</td></tr>
				</tfoot>
			</table>
		';
	}

	// Historique des attributions de points de fidélité
	print '
		<table id="stats-rewards" class="checklist">
			<caption>'._('Historique des attributions de points de fidélité').'</caption>
			<thead>
				<tr>
					<th id="del-rewards" data-label="'._('Cocher tout :').'"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
					<th id="hdate" class="thead-none">'._('Date').'</th>
					'.( !$usr ? '<th id="husr" class="thead-none">'._('Client').'</th>' : '' ).'
					<th id="hrwa" class="thead-none">'._('Action réalisée').'</th>
					<th id="hpts" class="thead-none">'._('Points<br />gagnés / perdus').'</th>
					<th id="hconvert" class="thead-none">'._('Valeur<br />en €').'</th>
					<th id="hlimit" class="thead-none">'._('Date limite d\'utilisation').'</th>
					'.( !$usr ? '' : '<th id="husr" class="thead-none">'._('Parrainage de').'</th>' ).'
					<th id="edit" class="thead-none"></th>
				</tr>
			</thead>
			<tbody>
	';

	if( !$rewards || !ria_mysql_num_rows($rewards) ){
		print '
			<tr>
				<td colspan="8">'._('Aucune historique n\'est disponible pour le moment.').'</td>
			</tr>
		';
	} else {
		$ar_users = array(); $limit = 40;
		while( $reward = ria_mysql_fetch_array($rewards) ){
			if( $limit<=0 ){
				break;
			}

			$amount = 0;
			if( trim($reward['convert'])!='' ){
				$ar_convert = preg_split('/\//', $reward['convert']);
				$convert = $ar_convert[0] / $ar_convert[1];
				$amount = $reward['pts']*$convert;
			}

			// Récupère le compte client
			if( $usr<=0 && !isset($ar_users[$reward['usr']]) ){
				$rusr = gu_users_get( $reward['usr'] );
				if( $rusr && ria_mysql_num_rows($rusr) ){
					$ar_users[ $reward['usr'] ] = ria_mysql_fetch_array( $rusr );
				}
			}

			// Gestion des dates
			$date = ria_date_format( $reward['date_en'], false, true );
			$date_limit = trim($reward['date_limit_en']) != '' ? ria_date_format( $reward['date_limit_en'], false, true ) : false;

			$class = '';
			if( !($amount<0 || $reward['pts']<0) && trim($reward['date_limit_en']) != '' && strtotime($reward['date_limit_en'])<time() ){
			}else{
				$class .= $amount<0 || $reward['pts']<0 ? ' negative' : ' positive';
			}

			print '
				<tr class="'.$class.'">
					<td headers="del-rewards" class="align-center">
						<input type="checkbox" name="rwd-id[]" value="'.$reward['stats_id'].'" />
					</td>
					<td headers="hdate" class="align-center">'.$date.'</td>
			';

			if( !$usr ){
				print '
					<td headers="husr">
				';

				if( isset($ar_users[ $reward['usr'] ]) ){
					print '
							'.view_usr_is_sync($ar_users[ $reward['usr'] ]).'
							<a target="_blank" href="/admin/customers/edit.php?usr='.$reward['usr'].'">'.htmlspecialchars( $reward['title_name'].' '.$reward['lastname'].' '.$reward['firstname'].' '.$reward['society'] ).'</a>
					';
				} else {
					print htmlspecialchars( '
							'.$reward['title_name'].' '.$reward['lastname'].' '.$reward['firstname'].' '.$reward['society'].'
					' );
				}

				if( $reward['godchild'] ){
					print '
							<br />(<span class="bold">'._('Parrainage de ').'</span>
					';

					if( !isset($ar_users[$reward['godchild']]) ){
						$rusr = gu_users_get( $reward['godchild'] );
						if( $rusr && ria_mysql_num_rows($rusr) ){
							$ar_users[ $reward['godchild'] ] = ria_mysql_fetch_array( $rusr );
						}
					}

					if( isset($ar_users[$reward['godchild']]) ){
						$godchild = $ar_users[ $reward['godchild'] ];

						print '
							'.view_usr_is_sync($godchild).'
							<a target="_blank" href="/admin/customers/edit.php?usr='.$godchild['id'].'">
								'.htmlspecialchars( $godchild['title_name'].' '.$godchild['adr_lastname'].' '.$godchild['adr_firstname'] ).'
							</a>
						';
					}

					print '
						)
					';
				}

				print '
					</td>
				';
			}

			print '
					<td headers="hrwa">
						'.htmlspecialchars($reward['rwa']>0 ? ($amount<0 ? $reward['contrary'] : $reward['name']) : $reward['name'] ).'
			';

			switch( $reward['cls_id'] ){
				case CLS_ORDER :
					$rord = ord_orders_get( 0, $reward['obj_id_0'] );
					if( $rord && ria_mysql_num_rows($rord) ){
						$ord = ria_mysql_fetch_array( $rord );
						print '
							<br />'._('Commande :').' '.view_ord_is_sync( $ord ).'
							<a href="/admin/orders/order.php?ord='.$ord['id'].'" title="'._('Afficher la fiche de cette commande').'">
								'.ord_orders_name( '', $ord['piece'], $ord['id'] ).'
							</a>
						';
					}
					break;
				case CLS_PMT_CODE :
					$rcod = pmt_codes_get( $reward['obj_id_0'] );
					if( $rcod && ria_mysql_num_rows($rcod) ){
						$cod = ria_mysql_fetch_array( $rcod );
						print '
							<br />Promotion :
							<a href="/admin/promotions/specials/edit.php?id='.$cod['id'].'&amp;type='.$cod['type'].'" title="'._('Afficher les informations sur cette promotion').'">
								'.htmlspecialchars( $cod['code'] ).'
							</a>
						';

						if( trim($cod['date_stop']) ){
							print '
								<br />(valable jusqu\'au '.$cod['date_stop'].' inclus)
							';
						}

					}
					break;
				case CLS_INVOICE :
					$rinv = ord_invoices_get( $reward['obj_id_0'] );
					if( $rinv && ria_mysql_num_rows($rinv) ){
						$inv = ria_mysql_fetch_array( $rinv );
						$name = trim($inv['piece']) ? htmlspecialchars( $inv['piece'] ) : $inv['id'];
						$ref = trim($inv['ref']) ? htmlspecialchars( ', référence '.$inv['ref'] ) : '';

						print '
							(Pièce n°<a href="/admin/orders/invoice.php?inv='.$inv['id'].'">'.htmlspecialchars( $name ).'</a>'.$ref.')
						';

						$riord = ord_inv_orders_get( $inv['id'] );
						if( $riord && ria_mysql_num_rows($riord) ){
							$multi = ria_mysql_num_rows($riord)>1;

							if( $multi ){
								print '
									<br />'._('Liée aux commandes :').'
								';
							}else{
								print '
									<br />'._('Liée à la commande :').'
								';
							}

							$first = true;
							while( $iord = ria_mysql_fetch_array($riord) ){
								$ord = ria_mysql_fetch_array( ord_orders_get(0, $iord['id']) );

								print ( !$first ? ', ' : '' ).view_ord_is_sync( $ord ).'
									<a href="/admin/orders/order.php?ord='.$ord['id'].'" title="'._('Afficher la fiche de cette commande').'">
										'.ord_orders_name( '', $ord['piece'], $ord['id'] ).'
									</a>
								';

								$first = false;
							}
						}
					}
			}

			print '
					</td>
					<td headers="hpts" class="align-right">'.ria_number_format($reward['pts'], NumberFormatter::DECIMAL).'</td>
					<td headers="hconvert" class="align-right">'.ria_number_format($amount, NumberFormatter::CURRENCY, 2).'</td>
					<td headers="hlimit" class="align-center">'.( $amount<0 || $reward['pts']<0 ? '' : $date_limit ).'</td>
			';

			if( $usr ){
				print '
					<td>
				';

				if( $reward['godchild'] ){
					if( !isset($ar_users[$reward['godchild']]) ){
						$rusr = gu_users_get( $reward['godchild'] );
						if( $rusr && ria_mysql_num_rows($rusr) ){
							$ar_users[ $reward['godchild'] ] = ria_mysql_fetch_array( $rusr );
						}
					}

					if( isset($ar_users[$reward['godchild']]) ){
						$godchild = $ar_users[ $reward['godchild'] ];

						print '
							'.view_usr_is_sync($godchild).'
							<a target="_blank" href="/admin/customers/edit.php?usr='.$godchild['id'].'">
								'.htmlspecialchars( $godchild['title_name'].' '.$godchild['adr_lastname'].' '.$godchild['adr_firstname'] ).'
							</a>
						';
					}
				}

				print '
					</td>
				';
			}

			print '
					<td headers="edit" class="align-center"><a class="edit-rewards" data-usr="'.$usr.'" data-id="'. $reward['stats_id'].'">'._('Modifier').'</a></td>
				</tr>
			';

			$limit--;
		}

	}

	print '
			</tbody>
			<tfoot>
				<tr>
	';

	if( $usr ){
		print '
					<td class="left" colspan="2">
		';
		if( $rewards && ria_mysql_num_rows($rewards)>0 ){
			print '<input type="submit" name="del-rewards" value="'._('Supprimer').'" /> ';
		}
		print '
					</td>
					<th class="align-right">
						'._('Solde :').'
					</th>
					<td>'.ria_number_format(gu_users_get_rewards_balance($usr), NumberFormatter::DECIMAL).'</td>
					<td>'.ria_number_format(gu_users_get_rewards_balance($usr, true), NumberFormatter::CURRENCY, 2).'</td>
					<td colspan="2"></td>
					<td>
						<input type="button" class="edit-rewards" data-id="0" data-usr="'.$usr.'" value="'._('Ajouter').'" />
					</td>
		';
	}else{
		print '
					<td colspan="8"></td>
		';
	}
	print '
				</tr>
			</tfoot>
		</table>

		<span class="rwd-legend">
			<span class="color active"></span>
			'._('Points de fidélité actifs').'
		</span>
		<span class="rwd-legend">
			<span class="color unactive"></span>
			'._('Points de fidélité utilisés').'
		</span>
		<div class="clear"></div>
	';
}

/** Cette fonction permet d'afficher les propositions d'association automatique pour une image
 *	@param $img_id Obligatoire, identifiant d'une image
 *	@param string $src_name Obligatoire, nom d'origine de l'image
 *	@param $products Optionnel, tableau des produits ayant un lien avec l'image (par défaut une recherche sur le $src_name permet de les récupérer)
 *	@return string Le code HTML de cette zone, ou chaine vide si l'un des paramètres obligatoires est faux
 */
function view_images_get_autolinks( $img_id, $src_name, $products=false ){
	if( !is_numeric($img_id) || $img_id <= 0 ){
		return '';
	}

	global $config, $memcached;

	$not_child = false;
	if( isset($config['img_sync_with_prd_secondary']) && $config['img_sync_with_prd_secondary'] ){
		$separator_pos = strpos( $src_name, $config['img_sync_code_separator'] );

		if( $separator_pos !== false ){
			$src_name = substr($src_name, 0, $separator_pos);
			$not_child = true;
		}
	}

	if( trim($src_name) == '' ){
		return '';
	}

	$key_memcached = 'funct:view_images_get_autolinks:v8:'.$config['tnt_id'].':'.$img_id;
	if( $get = $memcached->get($key_memcached) ){
		return $get;
	}

	$size = $config['img_sizes']['high'];

	$ar_product = array();
	if( is_array($products) && sizeof($products) ){
		foreach( $products as $one_prd ){
			$ar_product[ $one_prd['id'] ] = $one_prd;
		}
	}

	if( !sizeof($ar_product) ){
		if( isset($config['search_general_rule']) ){
			$tmp = $config['search_general_rule'];
		}

		$config['search_general_rule'] = 'start';

		// Recherche des correspondances à l'aide du moteur de recherche
		$res = search(
			array('seg'=>1, 'keywords'=>$src_name, 'published'=>false, 'section'=>false, 'action'=>2, 'type'=>'prd'),
			false, 0, 200, false, null, 0, true
		);
		if( is_array($res) && sizeof($res) ){
			$i = 0;
			foreach( $res as $one_res ){
				if( !isset($config['img_sync_no_childonly']) || $config['img_sync_no_childonly'] ){
					if( isset($one_res['get']['childonly']) && $one_res['get']['childonly'] ){
						continue;
					}
				}

				if( $i >= 10 ){
					break;
				}

				if( !$not_child || !$one_res['get']['childonly'] ){
					$ar_product[ $one_res['get']['id'] ] = $one_res['get'];
					$i++;
				}
			}
		}

		if( isset($tmp) ){
			$config['search_general_rule'] = $tmp;
		}
	}

	$html = '
		<div class="card-auto-link">
			<div class="media">
				<a href="/admin/documents/images/edit.php?image='.$img_id.'" target="_blank">
					<img src="'.$config['img_url'].'/'.$size['width'].'x'.$size['height'].'/'.$img_id.'.'.$size['format'].'" alt="" height="260" width="260" />
				</a>
			</div>

			<div id="links-'.$img_id.'" class="links">
				<h3>'._('Produits').' <span class="checking-all'.( sizeof($ar_product) ? '' : ' none' ).'"><a onclick="checkPrdImage('.$img_id.', true);">'._('Cocher tout').'</a> | <a onclick="checkPrdImage('.$img_id.', false);">'._('Décocher tout').'</a></span></h3>
	';

	if( sizeof($ar_product) ){
		foreach( $ar_product as $product ){
			// Construit l'url du produit
			$cat = false;
			$url = '/admin/catalog/product.php?prd='.$product['id'];

			// Tente de charge la catégorie principale du produit, si possible publiée
			$categories = prd_classify_get( false, $product['id'] );
			if( $categories!==false && ria_mysql_num_rows($categories) ){
				$cat = ria_mysql_fetch_array( $categories );
				$url = '/admin/catalog/product.php?cat='.$cat['cat'].'&amp;prd='.$product['id'];
			}

			// Construit le code HTML qui sera intégré à la page
			$html .= '
					<div id="cnt-img-'.$img_id.'-'.$product['id'].'" class="cnt-infos">
						<div class="check-link">
							<input checked="checked" name="add-links['.$img_id.'][prd][]" value="'.$product['id'].'" type="checkbox" />
						</div>
						<div class="cnt-name">
							<a href="'.$url.'" target="_blank">
								'.view_prd_is_sync( $product ).' '.htmlspecialchars( $product['ref'].' '.$product['name'] ).'
							</a>
						</div>
						<div class="cnt-desc"></div>
						<div class="cnt-url">
							<a href="'.$url.'" target="_blank">
			';

			// Génère le fil d'Ariane associé au produit proposé
			$html .= _('Catalogue').' &raquo; ';
			if( isset($cat['cat']) && is_numeric($cat['cat']) && $cat['cat'] > 0 ){
				$rcp = prd_categories_parents_get( $cat['cat'] );
				if( $rcp ){
					while( $cp = ria_mysql_fetch_array($rcp) ){
						$html .= htmlspecialchars( $cp['title'] ).' &raquo; ';
					}
				}
				$html .= htmlspecialchars( prd_categories_get_name($cat['cat'],true) ).' &raquo; ';
			}else{
				$html .= _('Non classés').' &raquo; ';
			}
			$html .= htmlspecialchars( $product['name'] );

			$html .= '
							</a>
						</div>
						<div class="cnt-place">
							<label for="position-links-'.$product['id'].'-prd-'.$img_id.'-0">Associer en tant que : </label>
							<select name="position-links['.$img_id.'][prd]['.$product['id'].']" id="position-links-'.$product['id'].'-prd-'.$img_id.'-0" class="valign-center">
								<option value="main">'._('Image principale').'</option>
								<option value="second"'.( $not_child ? ' selected="selected"' : '' ).'>'._('Image secondaire').'</option>
							</select>
						</div>
					</div>
			';
		}
	}else{
		$html .= '<div class="no-product">'._("Aucun article n'a été trouvé pour cette image.").'</div>';
	}

	$html .= '
				<div class="actions-links">
					<hr />
					<input class="save'.( sizeof($ar_product) ? '' : ' none' ).'" name="save" value="'._('Enregistrer').'" data-img="'.$img_id.'" type="button" title="'._('Enregistrer la ou les associations pour cette image.').'" 	/>
					<input class="search" name="search" value="'._('Rechercher').'" data-img="'.$img_id.'" type="button" title="'._('Rechercher un ou plusieurs autres articles.').'" />
					<input class="masked" name="masked" value="'._('Masquer').'" data-img="'.$img_id.'" type="button" title="'._('Retirer cette image des associations automatique.').'" />
					<input class="delete" name="delete" value="'._('Supprimer').'" data-img="'.$img_id.'" type="button" title="'._('Supprime cette image définitivement.').'" />
				</div>
			</div>
		</div>
	';

	$memcached->set( $key_memcached, $html, 3600 );
	return $html;
}

/**	Cette fonction est chargée d'inclure la librairie HighCharts ainsi que ses modules
 *
 */
function view_import_highcharts(){
	//<script src="http://code.highcharts.com/modules/exporting.js"></script>
	print '
		<script src="https://code.highcharts.com/highcharts.js"></script>
		<script src="https://code.highcharts.com/highcharts-more.js"></script>
		<script src="https://code.highcharts.com/modules/exporting.js"></script>
		<script src="https://code.highcharts.com/modules/no-data-to-display.js"></script>
        <script src="https://highcharts.github.io/export-csv/export-csv.js"></script>
        <script src="https://code.highcharts.com/modules/accessibility.js"></script>
		<script>
			Highcharts.setOptions({
				chart: {
					style: {
						fontFamily: \'"Montserrat", sans-serif\'
					}
				},
				lang: {
					months: ["'._('Janvier').'", "'._('Février').'", "'._('Mars').'", "'._('Avril').'", "'._('Mai').'", "'._('Juin').'",  "'._('Juillet').'", "'._('Août').'", "'._('Septembre').'", "'._('Octobre').'", "'._('Novembre').'", "'._('Décembre').'"],
					weekdays: ["'._('Dimanche').'", "'._('Lundi').'", "'._('Mardi').'", "'._('Mercredi').'", "'._('Jeudi').'", "'._('Vendredi').'", "'._('Samedi').'"],
					viewFullscreen: "'._('Afficher le graphique en plein écran').'",
					downloadJPEG: "'._('Télécharger au format JPG').'",
					downloadPDF: "'._('Télécharger au format PDF').'",
					downloadPNG: "'._('Télécharger au format PNG').'",
					downloadSVG: "'._('Télécharger au format SVG').'",
					downloadCSV: "'._('Télécharger au format CSV').'",
					downloadXLS: "'._('Télécharger au format XLS').'",
					noData: "'._('Aucune donnée à afficher pour la période sélectionnée').'",
					viewData: "'._('Afficher la source de données').'",
					printChart: "'._('Imprimer le graphique').'",
					decimalPoint: ",",
					thousandsSep: " ",
					contextButtonTitle: "'._('Imprimer ou exporter le graphique"').'
				}
			});
		</script>
	';
}

/** Affiche le menu déroulant de filtre de période
 *	@param $state un int représentant l'option de filtre choisie, elle sera par défaut à 1 (promos en cours)
 *	@param $page_type un string représentant l'attribut id de la div de classe "riapicker" (doit être changé pour correspondre au .js utilisé sur les différentes pages)
 *	@param $max_state un int représentant le maximum d'états disponibles (l'état 6 peut ne pas être disponible)
 *	@param string $title Optionnel, titre à afficher pour le sélecteur (par défaut - Période)
 *	@return void
 */
function view_state_periodpicker( $state=1, $page_type=0, $max_state=5, $title='Période' ){
	if( !$page_type ){
		$page_type = "periodpicker";
	}

	if( in_array($page_type, array('riawebsitepicker', "periodpicker", "riaselectorperiod")) ){
		$prefix = "p-";
	}else{
		$prefix = "";
	}

	?>
	<div id="<?php print $page_type; ?>" class="riapicker larger_picker">
			<div class="selectorview">
				<div class="left">
					<span class="function_name"><?php print $title; ?></span><br/>
					<?php
						switch( $state ){
							case 0:
								print '<span class="view">'._('Toutes').'</span>';
								break;
							case 1:
								print '<span class="view">'._('Actuelles').'</span>';
								break;
							case 2:
								print '<span class="view">'._('Passées').'</span>';
								break;
							case 3:
								print '<span class="view">'._('Futures').'</span>';
								break;
							case 4:
								print '<span class="view">'._('Passées ou actuelles').'</span>';
								break;
							case 5:
								print '<span class="view">'._('Actuelles ou futures').'</span>';
								break;
							case 6:
								if($max_state >= 6){
									print '<span class="view">'.strcut( _('Terminées récemment ou actuelles ou futures'), 33).'</span>';
								}
								break;
							case 7:
								if($max_state >= 7){
									print '<span class="view">'._('Terminées récemment').'</span>';
								}
								break;
						}
					?>
				</div>
				<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif?1" alt=""/></a>
				<div class="clear"></div>
			</div>
			<div class="selector">
				<a name="<?php print $prefix; ?>0"><?php print _('Toutes'); ?></a>
				<a name="<?php print $prefix; ?>1"><?php print _('Actuelles'); ?></a>
				<a name="<?php print $prefix; ?>2"><?php print _('Passées'); ?></a>
				<a name="<?php print $prefix; ?>3"><?php print _('Futures'); ?></a>
				<a name="<?php print $prefix; ?>4"><?php print _('Passées ou actuelles'); ?></a>
				<a name="<?php print $prefix; ?>5"><?php print _('Actuelles ou futures'); ?></a>
				<?php if($max_state >=6){ ?><a name="<?php print $prefix; ?>6"><?php print _('Terminées récemment ou actuelles ou futures'); ?></a><?php } ?>
				<?php if($max_state >=7){ ?><a name="<?php print $prefix; ?>7"><?php print _('Terminées récemment'); ?></a><?php } ?>
			</div>
		</div>
	<?php
}

/** Stocke le statut de sélection du periodpicker en session
 *	@param $_value la valeur du periodpicker choisie
 */
function session_set_periodpicker_state( $_value ){
	if(is_numeric($_value)){
		$_SESSION['periodpicker_state'] = intval($_value);
	}
}

/** Récupère le statut de sélection du periodpicker en session
 *	@return la valeur enregistrée en session ou false
 */
function session_get_periodpicker_state(){
	if(isset($_SESSION['periodpicker_state'])){
		return $_SESSION['periodpicker_state'];
	}
	return false;
}

/** Sur la page admin/catalog/product.php, onglet tarification le contenu du tableau de "conditions tarifaires" est chargé par cette fonction
 *	@param $_prd_ref la référence du produit à afficher
 *	@param $_prd_id l'id du produit à afficher
 *	@param $_periode un int représentant la période d'affichage choisie
 */
function product_price_table_get( $_prd_ref, $_prd_id, $_periode=6 ){
	?>
	<thead class="thead-none">
		<tr>
			<th id="sync"></th>
			<th id="information"><?php print _('Informations'); ?></th>
			<th id="conditions-prc"><?php print _('Conditions'); ?></th>
			<th id="action-prc"></th>
		</tr>
	</thead>
	<tbody>
		<?php print prc_prices_list_view($_prd_id, 0, $_periode); ?>
		<tr id="price-new" style="display: none">
			<td colspan="4"></td>
		</tr>
	</tbody>
	<tfoot>
	<tr>
		<td colspan="4" class="align-right">
			<input type="button" name="prices-add" id="prices-add" value="<?php print _('Nouveau Tarif'); ?>"
				   onclick="newForm('price', 'product', '0', '<?php print $_prd_id; ?>');"/>
		</td>
	</tr>
	</tfoot>

	<?php
}

/** Cette fonction permet de charger l'interface pour l'administation des liens catégories / promotions
 *	@param $cat_id Obligatoire, identifiant d'une catégorie
 *	@return string Le code HTML
 */
function view_admin_categories_codes( $cat_id ){
	if( !is_numeric($cat_id) || $cat_id<=0 ){
		return '';
	}

	ob_start();

	$ar_types 	= pmt_types_get_array();
	$ar_codes 	= array();
	$ar_cod_cat = array();

	$r_cod = prd_categories_codes_get( $cat_id );
	if( $r_cod && ria_mysql_num_rows($r_cod) ){
		while( $cod = ria_mysql_fetch_assoc($r_cod) ){
			$ar_cod_cat[ $cod['cod_id'] ] = $cod;
		}
	}

	$r_cod = pmt_codes_get( null, null, 5, false, true );
	if( $r_cod ){
		while( $cod = ria_mysql_fetch_assoc($r_cod) ){
			if (array_key_exists($cod['id'], $ar_cod_cat)) {
				continue;
			}

			if (!array_key_exists($cod['type'], $ar_codes)) {
				$ar_codes[ $cod['type'] ] = array();
			}

			$ar_codes[ $cod['type'] ][] = $cod;
		}
	}

	if( count($ar_cod_cat) || count($ar_codes) ){
	?>
	<tr class="cat-cod-link">
		<th colspan="2"><?php print _('Promotion(s) liée(s)'); ?></th>
		<input type="hidden" name="obj-pmt-id" value="<?php print $cat_id; ?>" />
	</tr>
	<tr class="cat-cod-link">
		<td><?php print _('Promotions :'); ?></td>
		<td>
			<?php
				if (count($ar_cod_cat)) {
					foreach ($ar_cod_cat as $cod) {
			?>
				<input class="del-obj-pmt" type="image" name="del-pmt-<?php print $cod['cod_id']; ?>" src="/admin/images/del-cat.png" title="<?php print _('Retirer cette promotion'); ?>" alt="<?php print _('Supprimer'); ?>" value="<?php print $cod['cod_id']; ?>" />&nbsp;
				<?php print htmlspecialchars( _($cod['cod_name']) ); ?><br />
			<?php
					}
				}else{
			?>
				<?php print _('Aucun lien avec les promotions'); ?>
			<?php } ?>
		</td>
	</tr>
	<?php if (count($ar_codes)) { ?>
	<tr class="cat-cod-link">
		<td><?php print _('Ajouter :'); ?></td>
		<td>
			<select class="select-obj-pmt" name="promotion" id="promotion">
				<option><?php print _('Choisir une promotion'); ?></option>
				<?php
					foreach ($ar_codes as $type => $codes) {
						if (!array_key_exists($type, $ar_types)) {
							continue;
						}
				?>
				<optgroup label="<?php print htmlspecialchars( _($ar_types[ $type ]['name']) ); ?>">
					<?php foreach ($codes as $cod) { ?>
					<option value="<?php print $cod['id']; ?>"><?php print htmlspecialchars( _($cod['name']) ); ?></option>
					<?php } ?>
				</optgroup>
				<?php
					}
				?>
			</select>
		</td>
	</tr>
	<?php
		}
	}

	$html = ob_get_contents();
	ob_end_clean();
	return $html;
}

/** Cette fonction permet de charger le tableau d'image associé à un objet
 *	@param int $cls_id Obligatoire Identifiant d'une classe
 *	@param $obj_id_0 Obligatoire Identifiant d'un objet id_0
 *	@param $obj_id_1 Identifiant d'un objet id_1
 *	@param $obj_id_2 Identifiant d'un objet id_2
 *	@param $type_id Identifiant d'un type
 *	@param $only_rows Boolean permet de récupérer uniquement le body ou les lignes du tableau
 *	@return string Le code HTML
 */
function view_admin_img_objects($cls_id, $obj_id_0, $obj_id_1=0, $obj_id_2=0, $type_id = 0, $only_rows = false){
	if (!is_numeric($cls_id) || $cls_id < 0) {
		return false;
	}
	if (!is_numeric($obj_id_0) || $obj_id_0 < 0) {
		return false;
	}
	if (!is_numeric($obj_id_1) || !is_numeric($obj_id_2) || !is_numeric($type_id) ) {
		return false;
	}
	if( !fld_classes_exists($cls_id) ){
		return false;
	}

	$table_head = '
		<script><!--
			$(document).ready(function(){
				init_img('.$cls_id.', '.$obj_id_0.', '.$obj_id_1.', '.$obj_id_2.', '.CLS_IMAGES_OBJECT.');
			});
		--></script>

		<table class="table-mediatheque" id="table-images-liees-mediatheque">
			<caption>'._('Images liées').'</caption>
			<tbody id="body_type_img">
		';

	$rtypes = img_images_types_get($cls_id, $type_id);
	$table_rows = '';
	$has_data = false;
	if ($rtypes && ria_mysql_num_rows($rtypes) > 0) {
		$has_data = true;
		while ($type = ria_mysql_fetch_array($rtypes)) {
			$table_rows .=
				'<tr>
					<td class="col200px">
						<label class="type_name'.$type["id"].'">'.htmlspecialchars( $type["name"] ).' : </label>';

		 	if ($type["tnt_id"] != 0) {
			 	$table_rows .='<br /> <span> (<a href="#" class="editType" onclick="return editType(this, '.$type["id"].');">'._('Editer').'</a> | <a href="#" class="delType" onclick="return delType(this, '.$type["id"].');">'._('Supprimer l\'album').'</a> )</span>';
		 	}

		 	$table_rows .='
					</td>
					<td>
						<div class="imgs-w" id="imgs-w'.$type["id"].'">';

			$table_rows .= view_admin_img_objects_list( $cls_id, $obj_id_0, $obj_id_1, $obj_id_2, $type["id"] );

			$table_rows .= '
				 		</div>
				 	</td>
			 	</tr>';
		}
	}else{
		$table_rows .= '<tr class="no_type"><td colspan="2">'._("Aucun type n'a été défini.").'</td></tr>';
	}

	if( $has_data ){
		$table_foot = '
			</tbody>
			<tfoot>
				<tr>
				<td class="left">
					<input disabled="disabled" id="imo-del-list-select" type="submit" onclick="return delImages('.$cls_id.', '.$obj_id_0.', '.$obj_id_1.', '.$obj_id_2.')" value="'._('Supprimer').'" title="'._('Supprimer les images sélectionnées').'" />
				</td>
				<td>
					<label for="typename">'._("Ajouter un type d'image :").'</label>
					<input id="typename" type="text" name="input_type_name" maxlength="75" />
					<input type="button" name="addtype" value="'._('Ajouter').'" title="'._('Ajouter un type d\'image').'" onclick="addType('.$cls_id.','.$obj_id_0.','.$obj_id_1.','.$obj_id_2.')" />
				</td></tr>
			</tfoot>
			</table>
		';
	}else{
		$table_foot = '
			</tbody>
			<tfoot>
				<tr>
					<td colspan="2">
						<label for="typename">'._("Ajouter un type d'image :").'</label>
						<input id="typename" type="text" name="input_type_name" maxlength="75" />
						<input type="button" name="addtype" value="'._('Ajouter').'" title="'._('Ajouter un type d\'image').'" onclick="addType('.$cls_id.','.$obj_id_0.','.$obj_id_1.','.$obj_id_2.')" />
					</td>
				</tr>
			</tfoot>
			</table>
		';
	}


	if ($only_rows) {
		return $table_rows;
	}else{
		return $table_head.$table_rows.$table_foot;
	}
}

/**	Affiche une liste d'image pour l'administration permettant le tri via drag and drop
 *	@param int $cls_id Obligatoire, classe de l'objet pour lequel on affiche les images
 *	@param $obj_id_0 Obligatoire, Identifiant de l'objet id_0
 *	@param $obj_id_1 Facultatif, Identifiant de l'objet id_1
 *	@param $obj_id_2 Facultatif, Identifiant de l'objet id_2
 *	@param $type_id Obligatoire, Identifiant du type d'image
 *	@param $in_popup Facultatif, détermine si l'affichage à lieu plein page (false, valeur par défaut) ou sous forme de popup (true)
 *
 *	@return un ul/li contenant les images
 */
function view_admin_img_objects_list( $cls_id, $obj_id_0, $obj_id_1, $obj_id_2, $type_id, $in_popup=false ){
	global $config;

	if (!is_numeric($cls_id) || $cls_id < 0) {
		return false;
	}
	if (!is_numeric($obj_id_0) || $obj_id_0 < 0) {
		return false;
	}
	if (!is_numeric($obj_id_1) || $obj_id_1 < 0) {
		return false;
	}
	if (!is_numeric($obj_id_2) || $obj_id_2 < 0) {
		return false;
	}
	if (!is_numeric($type_id) || $type_id < 0) {
		return false;
	}
	if( !fld_classes_exists($cls_id) ){
		return false;
	}

	$html = '';
	$rimo = img_images_objects_get($type_id, $obj_id_0, $obj_id_1, $obj_id_2);
	$images = false;
	$size = $config['img_sizes']['medium'];

	$html .= '
		<ul class="sortable sortable-img" data-typeid="'.$type_id.'">
	';
	if ($rimo && ria_mysql_num_rows($rimo)>0) {
		while( $r = ria_mysql_fetch_array($rimo) ){
			$images = img_images_get( $r["img_id"], '', '', '', '', null, false, false, false, null, array('set'=>'asc') );
			if ($images && ria_mysql_num_rows($images) > 0) {
				$image = ria_mysql_fetch_array($images);
				// Renommage de la function dans tab-media.js
				// On passe l'identifiant de l'objet image
				$html .= '
					<li class="preview" id="img'.$image['id'].'" onclick="'.( $in_popup ? 'parent.' : '' ).'prevClick(this, '.$r['imo_id'].');">
						<img src="'.$config['img_url'].'/'.$size['dir'].'/'.$image['id'].'.jpg" />
					</li>
				';
			}
		}
	}

	$html .= ' <li class="preview" onclick="return addImg('.$type_id.')"><input type="image" src="/admin/images/media/ajout_image.svg" value="Ajouter" name="imgadd"/></li>
		</ul>
	';

	return $html;
}

/** Cette fonction permet de vérifié si un tenant a bien accès a une fonctionnalité ou non
 *	@param $functionnality_code Code de la fonctionnalité à donner accès
 *
 *	@return bool true si le tenant a accès, false dans le cas contraire
 *	@todo Cette fonction pourra évolué pour voir si un utilisateur en particulié a accès a une fonctionnalité
 */
function view_functionnality_access($functionnality_code){
	global $config;
	return isset($config['sys_admin_functionnality_access']) && in_array($functionnality_code, $config['sys_admin_functionnality_access']);
}

/** Affiche le menu permettant de sélectionner un classement d'un produit
 *	@param int $prd_id Identifiant du produit
 *	@param int $cat_id Identifiant de la catégorie
 *	@param int $opt_cat_id Optionnel, identifiant de la catégorie en compte d'optimisation
 *	@param bool $is_optimisation Optionnel (par défaut à false), si oui ou non on est en mode optimisation
 *	@return string Le code HTML du sélecteur
 */
function view_product_classement_menu( $prd_id, $cat_id, $opt_cat_id=0, $is_optimisation=false){

	if( !is_numeric($prd_id) || !is_numeric($cat_id) ){
		throw new InvalidArgumentException('Les arguments doivent être des numériques');
	}

	if( $prd_id == 0 ){
		return '';
	}

	// ajouter controle droit affichage
	if( $is_optimisation && !view_functionnality_access('VIEW_PRODUCT_OPTIMISATION')){
		return '';
	}

	$rcat = prd_products_categories_get($prd_id);
	if ( !$rcat || !ria_mysql_num_rows($rcat) ) {
		return '';
	}
	$classements = array();

	$params = array(
		'cat' => $is_optimisation ? $cat_id : 0,
		'prd' => $prd_id,
		'tab' => $is_optimisation ? 'general' : 'ref'
	);
	if( $is_optimisation ){
		$params['opt'] = 0;
		$url = view_admin_construct_url('/admin/catalog/product.php', $params);
		$classements[0] = array('text' => 'Par défaut', 'url' => $url);
	}
	while ($cat = ria_mysql_fetch_array($rcat)) {
		$rparent = prd_categories_parents_get($cat['cat']);

		// récupération de la hiérarchie pour l'affichage
		$hierarchy = array();
		if ($rparent && ria_mysql_num_rows($rparent)) {
			while ($parent = ria_mysql_fetch_array($rparent)){
				$hierarchy[] = $parent['title'];
			}
		}
		$hierarchy[] = $cat['title'];

		$text = implode(' » ', $hierarchy );

		// configuration des paramètres de l'url
		$params = array(
			'cat' => $is_optimisation ? $cat_id : $cat['cat'],
			'prd' => $prd_id,
			'tab' => $is_optimisation ? 'general' : 'ref'
		);

		$params['cat'] = $is_optimisation ? $cat_id : $cat['cat'];

		if( $is_optimisation ){
			$params['opt'] = $cat['cat'];
		}
		$url = view_admin_construct_url('/admin/catalog/product.php', $params);

		$classements[$cat['cat']] = array(
			'text' => $text,
			'url' => $url
		);
	}
	$index = $is_optimisation ? $opt_cat_id : $cat_id;
	if( $index == 0 && !$is_optimisation){
		$view = 'Aucune classification sélectionné';
	}else{
		$view = $classements[$index]['text'];
	}
	if(strlen($view) > 35){
		$view = strcut( $view, 33 );
	}
	// Affiche le menu
	ob_start();?>
	<div class="riapicker">
		<div class="selectorview" onclick="">
			<div class="left">
				<span class="function_name">Classements</span><br/>
				<span class="view"><?php echo $view?></span>
			</div>
			<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif?1" height="8" width="16" alt=""/></a>
			<div class="clear"></div>
		</div>
		<div class="selector">
		<?php foreach( $classements as $id => $cly ){ ?>
			<a name="p-<?php echo $id?>" href="<?php echo $cly['url']?>"><?php echo htmlspecialchars($cly['text']); ?></a>
		<?php } ?>
		</div>
	</div>
	<?php
	$content = ob_get_contents();
	ob_end_clean();

	return $content;
}

/** Cette fonction permet de généré une url en fonction d'une URI de base et des paramètres GET
 * @param   $base_uri     Uri de base du lien
 * @param   $query_params Tableau contenant les paramètres GET avec "clé" => "valeur" (aussi "clé" => ["valeur", "valeur1"]))
 * @return                retourne le lien avec les paramètres GET
 */
function view_admin_construct_url($base_uri,array $query_params=array()){
	if(!is_string($base_uri)){
		throw new InvalidArgumentException ("$base_uri doit être un string");
	}
	$query = '';
	if( !empty($query_params)){
		$query = '?' . http_build_query($query_params);
	}
	$uri = $base_uri . $query;
	return $uri;
}

/** Affiche les informations dans l'onglet "Référencement" d'un object
 *	@param int $cls_id Obligatoire, identifiant d'un champ avancé
 *	@param int $obj_id Obligatoire, identifiant de l'objet
 *	@param string $lng_code Obligatoire, code iso 3166 de la langue à utiliser
 *	@param $url_widget_lng Facultatif, url du widget de langue
 *	@param int $id Facultatif, identifiant HTML du tableau généré par la fonction
 *	@param $class Facultatif, valeur à renseigner dans l'attribut class de la balise HTML TABLE générée par la fonction
 *	@return string Le code HTML
 */
function view_admin_tab_referencement( $cls_id, $obj_id, $lng_code, $url_widget_lng='', $id='', $class='' ){
	global $config;

	$html = '';

	if( !fld_classes_exists($cls_id) ){
		return $html;
	}

	$obj_id = control_array_integer( $obj_id, true, false, true );
	if( !$obj_id ){
		return '';
	}

	$object = false;

	$r_title_sentences = seo_templates_get(0, 'title', $cls_id, $lng_code);
	$title_sentences = array();

	while($title_sentence = ria_mysql_fetch_assoc($r_title_sentences)){
		$title_sentences[$title_sentence['id']] = $title_sentence;
	}

	$r_desc_sentences = seo_templates_get(0, 'desc', $cls_id, $lng_code);
	$desc_sentences = array();
	while($desc_sentence = ria_mysql_fetch_assoc($r_desc_sentences)){
		$desc_sentences[$desc_sentence['id']] = $desc_sentence;
	}

	$variables = array();
	$seo_objects = array();

	i18n::setLang($lng_code);

	switch( $cls_id ){
		case CLS_PRODUCT: { // Onglet Référencement de la fiche Produit
			$rproduct = prd_products_get_simple( $obj_id[0], '', false, $_GET['cat']);
			if( !$rproduct || !ria_mysql_num_rows($rproduct) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rproduct );

			$link = 'index.php?cat='.$_GET['cat'];
			$obj_ids = array($object['id'], $_GET['cat']);

			if ($object['id'] != 0 && $lng_code != $config['i18n_lng']) {
				$tsk_prd = fld_translates_get(CLS_PRODUCT, $object['id'], $lng_code, $object, array(_FLD_PRD_TITLE => 'title', _FLD_PRD_NAME => 'name', _FLD_PRD_DESC => 'desc', _FLD_PRD_DESC_LG => 'desc-long', _FLD_PRD_TAG_TITLE => 'tag_title', _FLD_PRD_TAG_DESC => 'tag_desc', _FLD_PRD_TAG_KEYWORD => 'keywords'), true);
				$object['name'] = $object['is_sync'] ? $object['name'] : $tsk_prd['name'];
				$object['title'] = $tsk_prd['title'];
				$object['desc'] = $tsk_prd['desc'];
				$object['desc-long'] = $tsk_prd['desc-long'];
				$object['tag_title'] = $tsk_prd['tag_title'];
				$object['tag_desc'] = $tsk_prd['tag_desc'];
				$object['keywords'] = $tsk_prd['keywords'];

				$tsk_url = fld_object_values_get(array($_GET['cat'], $object['id']), _FLD_PRD_URL, $lng_code, false, true);

				$object['cly_url_alias'] = trim($tsk_url) != '' ? $tsk_url : (isset($object['cly_url_alias']) ? $object['cly_url_alias'] : '');
				$object['url_perso'] = fld_object_values_get(array($_GET['cat'], $object['id']), _FLD_CLY_URL_PERSO, $lng_code, false, true);

				$object['url_alias'] = trim($object['url_perso']) != '' ? $object['url_perso'] : $object['cly_url_alias'];
			}
			$keys = array('url_perso', 'cly_url_alias');


			$pers_title = page_obj_title($cls_id, $obj_ids, true, true );
			$auto_title = htmlspecialchars(page_obj_title($cls_id, $obj_ids, false, true ));
			$pers_desc = htmlspecialchars(page_obj_desc($cls_id, $obj_ids, true, true));
			$auto_desc = htmlspecialchars(page_obj_desc($cls_id, $obj_ids, false, true));

			$variables = array(
				$object['id'],
				$_GET['cat'],
				$object['brd_id'] !== null ? $object['brd_id'] : false
			);

			$seo_objects = array($_GET['cat'], $object['id']);

			break;
		}
		case CLS_CATEGORY: { // Onglet Référencement de la fiche Catégorie
			$rcat = prd_categories_get( $obj_id[0] );
			if( !$rcat || !ria_mysql_num_rows($rcat) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rcat );

			$link = 'index.php?cat='.$object['id'];
			$obj_ids = $object['id'];

			if( $lng_code!=$config['i18n_lng'] ){
				$tsk_cat = fld_translates_get( CLS_CATEGORY, $object['id'], $lng_code, $object, array(_FLD_CAT_NAME=>'name', _FLD_CAT_TITLE=>'title', _FLD_CAT_DESC=>'desc', _FLD_CAT_TAG_TITLE=>'tag_title', _FLD_CAT_TAG_DESC=>'tag_desc'), true );
				$object['name'] = $tsk_cat['name'];
				$object['title'] = $tsk_cat['title'];
				$object['desc'] = $tsk_cat['desc'];
				$object['tag_title'] = $tsk_cat['tag_title'];
				$object['tag_desc'] =  $tsk_cat['tag_desc'];

				$tsk_url = fld_object_values_get( array($_GET['cat']), _FLD_CAT_URL, $lng_code, false, true );
				$object['cat_url_alias'] = trim($tsk_url)!='' ? $tsk_url : $object['url_alias'];
				$object['url_perso'] = fld_object_values_get( array($_GET['cat']), _FLD_CAT_URL_PERSO, $lng_code, false, true );
				$object['url_alias'] = trim($object['url_perso'])!='' ? $object['url_perso'] : $object['cat_url_alias'];
			}
			$keys = array('url_perso', 'cat_url_alias');

			$pers_title = page_obj_title($cls_id, $obj_ids, true, true );
			$auto_title = page_obj_title($cls_id, $obj_ids, false, true );
			$pers_desc = page_obj_desc($cls_id, $obj_ids, true, true);
			$auto_desc = page_obj_desc($cls_id, $obj_ids, false, true);
			$keywords = page_obj_key( CLS_CATEGORY, $object['id'], true );

			$variables = array(
				$object['id'],
				$object['products']
			);

			$seo_objects = array($object['id']);

			break;
		}
		case CLS_BRAND: { // Onglet Référencement de la fiche Marques
			$r_brd = prd_brands_get( $obj_id[0] );
			if( !$r_brd || !ria_mysql_num_rows($r_brd) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $r_brd );

			$link = 'index.php';
			$obj_ids = $object['id'];

			if( $lng_code!=$config['i18n_lng'] ){
				$tsk_brd = fld_translates_get( CLS_BRAND, $object['id'], $lng_code, $object, array(
					_FLD_BRD_NAME=>'name',
					_FLD_BRD_TAG_TITLE=>'tag_title',
					_FLD_BRD_TAG_DESC=>'tag_desc',
					_FLD_BRD_KEYWORDS=>'keywords',
					_FLD_BRD_TITLE=>'title',
					_FLD_BRD_DESC=>'desc',
					_FLD_BRD_WEB=>'url'), true );

				$object['name'] = $tsk_brd['name'];
				$object['title'] = $tsk_brd['title'];
				$object['desc'] = $tsk_brd['desc'];
				$object['url'] = $tsk_brd['url'];
				$object['tag_title'] = $tsk_brd['tag_title'];
				$object['tag_desc'] = $tsk_brd['tag_desc'];
				$object['keywords'] = $tsk_brd['keywords'];
			}

			$pers_title = $object['tag_title'];
			$auto_title = page_obj_title($cls_id, $obj_ids, false, true );
			$pers_desc = $object['tag_desc'];
			$auto_desc = page_obj_desc($cls_id, $obj_ids, false, true);
			$keywords = $object['keywords'];

			$variables = array(
				$object['id'],
				$object['products']
			);

			$seo_objects = array($object['id']);

			break;
		}
		case CLS_CMS: { // Onglet Référencement de la fiche Page de contenu
			$rcms = cms_categories_get( $obj_id[0] );
			if( !$rcms || !ria_mysql_num_rows($rcms) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rcms );

			$link = 'index.php?cat='.$object['id'];
			$obj_ids = $object['id'];

			if( $lng_code!=$config['i18n_lng'] ){
				$tsk_cat = fld_translates_get( CLS_CMS, $object['id'], $lng_code, $object, array(_FLD_CMS_NAME=>'name', _FLD_CMS_SHORT_DESC=>'short_desc', _FLD_CMS_DESC=>'desc', _FLD_CMS_TAG_TITLE=>'tag_title', _FLD_CMS_TAG_DESC=>'tag_desc', _FLD_CMS_TAG_KEYWORDS=>'keywords', _FLD_CMS_URL=>'url' ), true );
				$object['name'] = $tsk_cat['name'];
				$object['short_desc'] = $tsk_cat['short_desc'];
				$object['desc'] = $tsk_cat['desc'];
				$object['tag_title'] = $tsk_cat['tag_title'];
				$object['tag_desc'] = $tsk_cat['tag_desc'];
				$object['keywords'] = $tsk_cat['keywords'];

				$tsk_url = fld_object_values_get( array($_GET['cat']), _FLD_CMS_URL, $lng_code, false, true );
				$object['cat_url'] = trim($tsk_url)!='' ? $tsk_url : $object['url'];
				$object['url_perso'] = fld_object_values_get( array($_GET['cat']), _FLD_CMS_URL_PERSO, $lng_code, false, true );
				$object['url'] = trim($object['url_perso'])!='' ? $object['url_perso'] : $object['cat_url'];
			}
			$keys = array('url_perso', 'cat_url');

			$pers_title = $object['tag_title'];
			$auto_title = page_obj_title($cls_id, $obj_ids, false, true );
			$pers_desc = $object['tag_desc'];
			$auto_desc = page_obj_desc($cls_id, $obj_ids, false, true);
			$keywords = $object['keywords'];

			$seo_objects = array($object['id']);

			break;
		}
		case CLS_STORE: { // Onglet Référencement de la fiche Magasin
			$rstore = dlv_stores_get( $obj_id[0], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
			if( !$rstore || !ria_mysql_num_rows($rstore) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rstore );

			$website = ria_mysql_fetch_array( wst_websites_get($config['wst_id']) );
			$tsk_website = fld_translates_get( CLS_WEBSITE, $website['id'], $lng_code, $website, array(_FLD_WST_TAG_TITLE=>'site_title',_FLD_WST_TAG_DESC=>'meta_desc',_FLD_WST_TAG_KEYWORDS=>'meta_kwd'), true );
			// Récupère les informations traduite
			if( $lng_code!=$config['i18n_lng'] ){
				$tsk_str = fld_translates_get( CLS_STORE, $object['id'], $lng_code, $object, array(_FLD_STR_NAME=>'name',_FLD_STR_DESC=>'desc'), true );
				$object['name'] = $tsk_str['name']; $object['desc'] = $tsk_str['desc'];
				$object['tag_title'] = fld_object_values_get($object['id'], _FLD_STR_TAG_TITLE, $lng_code, false, true);
				$object['tag_desc'] = fld_object_values_get($object['id'], _FLD_STR_TAG_DESC, $lng_code, false, true);
				$object['keywords'] = fld_object_values_get($object['id'], _FLD_STR_KEYWORDS, $lng_code, false, true);
			}

			// Charge la liste des départements français
			$depts = sys_france_counties_get();
			$object_dept_code = substr($object['zipcode'], 0, 2);

			// Construit une proposition de titre optimisé pour le référencement naturel pour ce magasin
			$titleDef = $object['name'];
			$titleDef .= $object['city']!='' && $object['name']!=$object['city'] ? ' - '.$object['city'] : '';
			$titleDef .= $object['zipcode']!='' && isset($depts[ $object_dept_code ]) ? ' - '.$depts[ $object_dept_code ] : '';
			if( $config['tnt_id']==8 ){ // Attention spécifique Chazelles
				$titleDef .= ' - Revendeur Cheminées de Chazelles';
			}else{
				$titleDef = htmlspecialchars(page_obj_title( CLS_STORE, $object['id'], false ));
			}
			$descDef = html_strip_tags($object['desc'])!='' ? str_replace("[\s].", ' ', html_strip_tags( $object['desc'] )) : htmlspecialchars( $tsk_website['meta_desc'] );
			if( $config['tnt_id']==8 ){ // Attention spécifique Chazelles
				$descDef = $object['name'];
				$descDef .= $object['city']!='' && $object['name']!=$object['city'] ? ', revendeur Chazelles à '.$object['city'] : '';
				$descDef .= ', vous propose une gamme de cheminées design, contemporaines, d\'inserts à bois, à pellets et de poêles à bois ou à granulés';
			}else{
				$descDef = htmlspecialchars(page_obj_desc( CLS_STORE, $object['id'], false ));
			}

			$link = 'index.php';
			$obj_ids = $object['id'];

			$pers_title = $object['tag_title'];
			$auto_title = trim($titleDef);
			$pers_desc = $object['tag_desc'];
			$auto_desc = trim($descDef);
			$keywords = $object['keywords'];

			$seo_objects = array($object['id']);

			break;
		}
		case CLS_NEWS: { // Onglet Référencement de la fiche Actualité
			$rnews = news_get( $obj_id[0] );
			if( !$rnews || !ria_mysql_num_rows($rnews) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $rnews );

			$link = 'index.php?cat='.$object['id'];
			$obj_ids = $object['id'];

			$pers_title = $object['tag_title'];
			$auto_title = page_obj_title($cls_id, $obj_ids, false, true );
			$pers_desc = $object['tag_desc'];
			$auto_desc = page_obj_desc($cls_id, $obj_ids, false, true);
			$keywords = $object['keywords'];

			$seo_objects = array($object['id']);

			break;
		}
		case CLS_FAQ_CAT: { // Onglet Référencement d'une fiche Catégorie de la FAQ
			$r_cat = faq_categories_get( $obj_id[0] );
			if( !$r_cat || !ria_mysql_num_rows($r_cat) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $r_cat );

			$link = 'index.php?cat='.$object['id'];
			$obj_ids = $object['id'];

			$pers_title = page_obj_title($cls_id, $obj_ids, true, true );
			$auto_title = page_obj_title($cls_id, $obj_ids, false, true );
			$pers_desc = page_obj_desc($cls_id, $obj_ids, true, true);
			$auto_desc = page_obj_desc($cls_id, $obj_ids, false, true);
			$keywords = $object['keywords'];

			$seo_objects = array($object['id']);

			break;
		}
		case CLS_FAQ_QST: { // Onglet Référencement d'une fiche Question d'une catégorie de la FAQ
			$r_qst = faq_questions_get( $obj_id[0] );
			if( !$r_qst || !ria_mysql_num_rows($r_qst) ){
				return $html;
			}

			$object = ria_mysql_fetch_assoc( $r_qst );

			$link = 'index.php?cat='.$_GET['cat'];
			$obj_ids = $object['id'];

			$pers_title = page_obj_title($cls_id, $obj_ids, true, true );
			$auto_title = page_obj_title($cls_id, $obj_ids, false, true );
			$pers_desc = page_obj_desc($cls_id, $obj_ids, true, true);
			$auto_desc = page_obj_desc($cls_id, $obj_ids, false, true);
			$keywords = $object['keywords'];

			$seo_objects = array($object['id']);

			break;
		}
		default: {
			error_log('Classe d\'objets non prise en charge pour le moment : view_admin_tab_referecement('.$cls_id.')' );
			return $html;
		}
	}

	foreach ($title_sentences as $key => $title_sentence) {
		$title_sentences[$key]['content'] = seo_templates_get_translation($title_sentence['id'], $variables, $lng_code);
	}
	foreach ($desc_sentences as $key => $desc_sentence) {
		$desc_sentences[$key]['content'] = seo_templates_get_translation($desc_sentence['id'], $variables, $lng_code);
	}

	if( !isset($object['is_sync']) ){
		$object['is_sync'] = false;
	}

	if ($cls_id != CLS_BRAND){
		$html .= '
		<table'.($id != '' ? ' id="'.$id.'"' : ' id="table-referencement"' ).($class != '' ? ' class="'.$class.'"' : '' ).' >
			<thead class="thead-none">
				<tr>
					<th class="col200px">&nbsp;</th>
					<th>'._('Référencement personnalisé').'</th>
					<th>'._('Référencement automatique').'</th>
				</tr>
			</thead>
			<tbody>
		';
	}
	$html .= '

	';

	$r_objects = seo_objects_get($seo_objects, 0, 0);
	$seo_ids = array();
	if (ria_mysql_num_rows($r_objects)){
		while ($seo_object = ria_mysql_fetch_assoc($r_objects)){
			$r_template = seo_templates_get($seo_object['seo_id'], '', 0, $lng_code);
			if ($r_template && $template = ria_mysql_fetch_assoc($r_template)){
				$seo_ids[$template['type']] = $seo_object['seo_id'];
			}
		}
	}

	if (sizeof($title_sentences)){
		$html .= '<tr>
					<td rowspan="2">
						<label for="tag_title">'._('Titre de la page').' :</label><br/><sub>('._('Balise title').')</sub>
					</td>
					<td colspan="2">
						'.($object['tag_title'] != '' && isset($seo_ids['title']) && $seo_ids['title'] ? '<input type="hidden" id="original_title_text" name="original_title_text" value="'.$object['tag_title'].'" />' : '').'
						<select name="perso_title" id="select_title">
							<option id="no_title" value="'.($object['tag_title'] != '' && isset($seo_ids['title']) && $seo_ids['title'] ? "-1" : "0").'">'.($object['tag_title'] != '' && isset($seo_ids['title']) && $seo_ids['title'] ? _('Récupérer le précédent titre personnalisé') : _('Sélectionner un titre de référencement personnalisé')).'</option>';
		foreach($title_sentences as $key => $title_sentence) {
			$html .= '		<option id="title-'.$title_sentence['id'].'" value="'.$title_sentence['id'].'" '.(isset($seo_ids['title']) && $seo_ids['title'] == $title_sentence['id'] ? 'selected="selected"' : '').'>'.$title_sentence['content'].'</option>';
		}
		$html .= '		</select>
					</td>
				</tr>';
	}

	$html .= '
				<tr>';
	if (!sizeof($title_sentences)){
		$html .= '
					<td class="col200px">
						<label for="tag_title">'._('Titre de la page').' :</label><br/><sub>('._('Balise title').')</sub>
					</td>';
	}
	$html .= '
					<td data-label="'._('Référencement personnalisé :').' ">
						<input type="text" class="referencing" name="tag_title" id="tag_title" value="'.htmlspecialchars($pers_title).'" maxlength="70"/>
					</td>
					<td data-label="'._('Référencement automatique :').' ">
						<input type="text" id="tag_title_ref" class="referencing" readonly="readonly"  id="tag_title" value="'.htmlspecialchars($auto_title).'" maxlength="70"/>
					</td>
				</tr>';

	if (sizeof($desc_sentences)){
		$html .= '<tr>
					<td rowspan="2">
						<label for="tag_desc">'._('Description').' :<br/><sub>('._('Balise méta-description').', <br/>'.sprintf(_('%d caractères maximum'), META_DESCRIPTION_LIMIT).' <br/> <br/> '._('Cette description sera utilisée en priorité pour le référencement').')</sub></label></td>
					<td colspan="2">
						'.($object['tag_desc'] != '' && isset($seo_ids['desc']) && $seo_ids['desc'] ? '<input type="hidden" id="original_desc_text" name="original_desc_text" value="'.$object['tag_desc'].'" />' : '').'
						<select name="perso_desc" id="select_desc">
							<option id="no_desc" value="'.($object['tag_desc'] != '' && isset($seo_ids['desc']) && $seo_ids['desc'] ? "-1" : "0").'">'.($object['tag_desc'] != '' && isset($seo_ids['desc']) && $seo_ids['desc'] ? _('Récupérer la précédente description personnalisée') : _('Sélectionner une description de référencement personnalisé')).'</option>';


		foreach($desc_sentences as $key => $desc_sentence) {
			$html .= '		<option id="desc-'.$desc_sentence['id'].'" value="'.$desc_sentence['id'].'" '.(isset($seo_ids['desc']) && $seo_ids['desc'] == $desc_sentence['id'] ? 'selected="selected"' : '').'>'.$desc_sentence['content'].'</option>';
		}
		$html .= '		</select>
					</td>
				</tr>';
	}
	$html .= '	<tr>';

	if (!sizeof($desc_sentences)){
		$html .= '	<td>
						<label for="tag_desc">'._('Description').' :<br/><sub>('._('Balise méta-description').', <br/>'.sprintf(_('%d caractères maximum'), META_DESCRIPTION_LIMIT).' <br/> <br/> '._('Cette description sera utilisée en priorité pour le référencement').')</sub></label>
					</td>';
	}
	$html .= '		<td data-label="'._('Référencement personnalisé').'">
						<textarea class="ref-desc" name="tag_desc" id="tag_desc" cols="40" rows="10">'.htmlspecialchars( $pers_desc ).'</textarea>
					</td>
					<td data-label="'._('Référencement automatique').'">
						<textarea id="tag_desc_ref" class="readonly ref-desc" readonly="readonly" cols="40" rows="10">'.htmlspecialchars( $auto_desc ).'</textarea>
					</td>
				</tr>
	';

	if ( $cls_id == CLS_PRODUCT ){
		$html .= '
				<tr>
					<td>
						<label for="keywords">'._('Mots-clés').' :<br/><sub>('._('Séparez les mots-clés par une virgule.').'<br/><br/>'._('L\'utilisation de ce champ est limitée au moteur de recherche.').')</sub></label></td>
					<td data-label="'._('Référencement personnalisé').'">
						<textarea name="keywords" id="keywords" cols="40" rows="5">'.htmlspecialchars(page_obj_key($cls_id, $obj_ids, true, true)).'</textarea>
					</td>
					<td data-label="'._('Référencement automatique').'">
						<textarea id="tag_keywords_ref" class="readonly keywords" readonly="readonly" cols="40" rows="5">'.htmlspecialchars(page_obj_key($cls_id, $obj_ids, false, true)).'</textarea>
					</td>
				</tr>
		';
	}

	if ($cls_id == CLS_PRODUCT || $cls_id == CLS_CATEGORY ){
		i18n::setLang($config['i18n_lng']);
	}

	if (($cls_id == CLS_PRODUCT && isset($config['prd_pages'][''])) || $cls_id == CLS_CATEGORY || $cls_id == CLS_CMS) {
		if (ria_array_key_exists($keys, $object)) {
			$is_ok = true;
			if ($cls_id == CLS_CMS){
				$hierarchy = cms_hierarchy_get( $object['id'] );

				if( is_array($hierarchy) && sizeof($hierarchy) ){
					$cmshierarchy = array_shift( $hierarchy );
					$rparent = cms_categories_get( $cmshierarchy['id'], false, false, -1, false, false, true, null, false, null, false );
					if( !$rparent || !ria_mysql_num_rows($rparent) ){
						$is_ok = false;
					}
				} else {
					$is_ok = false;
				}
			}
			if ($is_ok){
				$html .= '
					<tr>
						<td>
							<label for="url_perso"> '._('URL personnalisée').' :<br/>
								<sub>('._('Une URL ne peut être composée que de caractères alphanumériques (sauf les accents) ou bien des caractères \'-\' et \'/\'.').')</sub>
							</label><br/>
						</td>
						<td data-label="'._('Référencement personnalisé').'">
							<input type="hidden" name="old_url" id="old_url" value="'.(trim($object[$keys[0]]) != '' ? $object[$keys[0]] : $object[$keys[1]]).'"/>
							<textarea class="area-url" cols="200" rows="5" name="url_perso" id="url_perso">'.$object[$keys[0]].'</textarea>
						</td>
						<td data-label="'._('Référencement automatique').'">
							<input type="hidden" name="default_url" id="default_url" value="'.(isset($object[$keys[1]]) ? $object[$keys[1]] : '').'"/>
							<textarea class="readonly area-url" cols="200" rows="5" name="url_default" id="url_default" readonly="readonly">'.(isset($object[$keys[1]]) ? $object[$keys[1]] : '').'</textarea>
						</td>
					</tr>
				';
			}
		}
	}

	if ($cls_id != CLS_BRAND){
		$html .= '
			</tbody>
			<tfoot>
				<tr>
					<td colspan="3">
		';
		if ($cls_id == CLS_CMS){
			$html .= '
						<div class="rev-form">
							<table class="float-right">
								<tr>
									<td>'._('Modification').' :</td>
									<td>
										<input type="radio" value="0" name="rev-ref-major" id="rev-ref-major-0" checked="checked" /> <label for="rev-ref-major-0">'._('Mineure').'</label>
										<input type="radio" value="1" name="rev-ref-major" id="rev-ref-major-1" /> <label for="rev-ref-major-1">'._('Majeure').'</label>
									</td>
								</tr>
								<tr>
									<td>
										<label for="rev-ref-comment" style="vertical-align: top">'._('Commentaire sur cette révision :').'</label>
									</td>
									<td>
										<textarea name="rev-ref-comment" id="rev-ref-comment" cols="50" rows="2"></textarea>
									</td>
								</tr>
							</table>
							<div class="clear-right"></div>
						</div>
			';
		}
		$html .= '
						<input type="submit" name="save-ref" class="btn-main" value="'._('Enregistrer').'"/>
						<input type="submit" name="cancel" value="'._('Annuler').'" onclick="window.location.href=\''.$link.'\'"/>
					</td>
				</tr>
			</tfoot>
		</table>
		';
	}

	if ($cls_id != CLS_PRODUCT){
		$html .= '<input type="hidden" name="keywords" value="'.htmlspecialchars( $keywords ).'" />';
	}

	return $html;
}

/** Cette fonction gère les différentes actions sur l'onglet "Référencement" des objets
 *	@param int $cls_id Obligatoire, identifiant de la classe de l'objet
 *	@param int $obj_id Obligatoire, identifiant de l'objet ou tableau composant la clé de l'objet
 *	@param string $lng_code Obligatoire, code de la langue
 */
function view_admin_tab_referencement_actions( $cls_id, $obj_id, $lng_code ){
	$obj_id = control_array_integer( $obj_id, true, false, true );
	if( !$obj_id ){
		return false;
	}

	$url_header = '';

	$object = false;
	$fields = array();

	global $config;

	switch( $cls_id ){
		case CLS_PRODUCT: { // Onglet Référencement de la fiche Produit
			$rproduct = prd_products_get_simple( $obj_id[0], '', false, $_GET['cat'], false, false, false, false, array('childs'=>true) );
			if( $rproduct && ria_mysql_num_rows($rproduct) ){
				$object = ria_mysql_fetch_assoc( $rproduct );
			}
			$rewrite_object = array($_GET['cat'], $object['id']);
			$url_header = '/admin/catalog/product.php?cat='.$_GET['cat'].'&prd='.$obj_id[0].'&tab=ref';

			$fields['tag_title'] = _FLD_CLY_TAG_TITLE;
			$fields['tag_desc'] = _FLD_CLY_TAG_DESC;
			$fields['keywords'] = _FLD_CLY_KEYWORDS;
			$fields['url'] = _FLD_PRD_URL;
			$fields['url_perso'] = _FLD_CLY_URL_PERSO;

			$keys = array('url_perso', 'url_alias');
			$prd_page = isset($config['prd_pages']['']) ? $config['prd_pages'][''] : '';
			$url_p = '';
			if ($prd_page != ''){
				$url_p = $prd_page . '?cat=' . $_GET['cat'] . '&prd=' . $object['id'];
			}
			$seo_objects = array($_GET['cat'], $object['id']);

			break;
		}
		case CLS_CATEGORY: { // Onglet Référencement de la fiche Catégorie
			$rcat = prd_categories_get( $obj_id[0] );
			if( $rcat && ria_mysql_num_rows($rcat) ){
				$object = ria_mysql_fetch_assoc( $rcat );
			}
			$rewrite_object = array($object['id']);
			$url_header = '/admin/catalog/edit.php?cat='.$obj_id[0].'&tab=ref';

			$fields['tag_title'] = _FLD_CAT_TAG_TITLE;
			$fields['tag_desc'] = _FLD_CAT_TAG_DESC;
			$fields['keywords'] = _FLD_CAT_TAG_KEYWORD;
			$fields['url'] = _FLD_CAT_URL;
			$fields['url_perso'] = _FLD_CAT_URL_PERSO;

			$keys = array('url_perso', 'cat_url_alias');
			$url_p = ria_mysql_fetch_assoc(cfg_urls_get($config['wst_id'], $cls_id));
			$url_p = $url_p['url']. '?cat=' . $object['id'];


			$seo_objects = array($object['id']);


			break;
		}
		case CLS_CMS: { // Onglet Référencement des pages CMS
			$rcms = cms_categories_get( $obj_id[0], false, false, -1, false, false, true, null, false, null, false );
			if( $rcms && ria_mysql_num_rows($rcms) ){
				$object = ria_mysql_fetch_assoc( $rcms );
			}
			$rewrite_object = array($object['id']);
			$url_header = '/admin/tools/cms/edit.php?cat='.$obj_id[0].'&tab=ref';

			$fields['tag_title'] = _FLD_CMS_TAG_TITLE;
			$fields['tag_desc'] = _FLD_CMS_TAG_DESC;
			$fields['keywords'] = _FLD_CMS_TAG_KEYWORDS;
			$fields['url'] = _FLD_CMS_URL;
			$fields['url_perso'] = _FLD_CMS_URL_PERSO;

			$keys = array('url_perso', 'cat_url');

			$hierarchy = cms_hierarchy_get( $object['id'] );
			if (is_array($hierarchy) && sizeof($hierarchy)) {
				$cmshierarchy = array_shift( $hierarchy );
				$rparent = cms_categories_get( $cmshierarchy['id'], false, false, -1, false, false, true, null, false, null, false );
				if( $rparent && ria_mysql_num_rows($rparent) ){
					$url_p = ria_mysql_result( $rparent, 0, 'url' ).'?cat='.$object['id'];
				}
			}

			$seo_objects = array($object['id']);

			break;
		}
		case CLS_STORE: { // Onglet Référencement de la fiche Magasin
			$rstore = dlv_stores_get( $obj_id[0], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
			if( $rstore && ria_mysql_num_rows($rstore) ){
				$object = ria_mysql_fetch_assoc( $rstore );
			}
			$rewrite_object = array($object['id']);

			$url_header = '/admin/config/livraison/stores/edit.php?str='.$_GET['str'].'&tab=ref';

			$fields['tag_title'] = _FLD_STR_TAG_TITLE;
			$fields['tag_desc'] = _FLD_STR_TAG_DESC;
			$fields['keywords'] = _FLD_STR_KEYWORDS;

			$seo_objects = array($object['id']);

			break;
		}
		case CLS_NEWS: { // Onglet Référencement de la fiche Actualité
			$rnews = news_get( $obj_id[0], false, null, 0, 0, false, false );
			if( $rnews && ria_mysql_num_rows($rnews) ){
				$object = ria_mysql_fetch_assoc( $rnews );
			}
			$rewrite_object = array($object['id']);

			$url_header = '/admin/tools/news/edit.php?news='.$obj_id[0].'&tab=ref';

			$seo_objects = array($object['id']);

			break;
		}
		case CLS_BRAND: { // Onglet Référencement de la fiche Marque
			$r_brd = prd_brands_get( $obj_id[0] );
			if( $r_brd && ria_mysql_num_rows($r_brd) ){
				$object = ria_mysql_fetch_assoc( $r_brd );
			}
			$rewrite_object = array($object['id']);

			$fields['tag_title'] = _FLD_BRD_TAG_TITLE;
			$fields['tag_desc'] = _FLD_BRD_TAG_DESC;
			$fields['keywords'] = _FLD_BRD_KEYWORDS;

			$url_header = '/admin/catalog/brands/edit.php?brd='.$obj_id[0].'&tab=ref';

			$seo_objects = array($object['id']);

			break;
		}
		case CLS_FAQ_CAT: { // Onglet Référencement de la fiche Catégorie de la FAQ
			$r_cat = faq_categories_get( $obj_id[0] );
			if( $r_cat && ria_mysql_num_rows($r_cat) ){
				$object = ria_mysql_fetch_assoc( $r_cat );
			}
			$rewrite_object = array($object['id']);

			$url_header = '/admin/tools/faq/category.php?cat='.$obj_id[0].'&tab=ref';

			$seo_objects = array($object['id']);

			break;
		}
		case CLS_FAQ_QST: { // Onglet Référencement de la fiche Question d'une catégorie de la FAQ
			$r_qst = faq_questions_get( $obj_id[0] );
			if( $r_qst && ria_mysql_num_rows($r_qst) ){
				$object = ria_mysql_fetch_assoc( $r_qst );
			}
			$rewrite_object = array($object['id']);

			$url_header = '/admin/tools/faq/question.php?qst='.$obj_id[0].'&cat='.$_GET['cat'].'&tab=ref';

			$seo_objects = array($object['id']);

			break;
		}
		default: {
			error_log('Classe d\'objets non prise en charge pour le moment : view_admin_tab_referencement_actions('.$cls_id.')' );
			return false;
		}
	}

	if( $object === false ){
		if( $lng_code != $config['i18n_lng'] ){
			$url_header .= '&lng='.$lng_code;
		}

		header('Location: '.$url_header);
		exit;
	}

	if( !isset($object['is_sync']) ){
		$object['is_sync'] = false;
	}

	//Bouton Enregistrer, onglet référencement
	if( ($cls_id != CLS_BRAND && isset($_POST['save-ref'])) || ($cls_id == CLS_BRAND && (isset($_POST['save']) || isset($_POST['save_stay'])))){
		//Dans tout les cas on doit avoir ces trois variables post
		if( !isset($_POST['keywords'],$_POST['tag_title'],$_POST['tag_desc']) ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes ou invalides.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
		}else{
			if( $cls_id==CLS_CMS && !cms_categories_revision_add($object['id'], $_POST['rev-ref-major'], $_POST['rev-ref-comment'])) {
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du référencement de la page de contenu.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
			}
			$seo_title_id = isset($_POST['perso_title']) ? $_POST['perso_title'] : 0;
			$seo_desc_id = isset($_POST['perso_desc']) ? $_POST['perso_desc'] : 0;
			$seo_filter = isset($_POST['previous_title_text']) || isset($_POST['previous_desc_text']);
			if (ria_mysql_num_rows($r_objects = seo_objects_get($seo_objects, 0, 0))){
				$to_delete_sob_ids = array();
				while ($seo_object = ria_mysql_fetch_assoc($r_objects)){
					$r_template = seo_templates_get($seo_object['seo_id'], '', 0, $lng_code);
					if ($r_template && ria_mysql_num_rows($r_template)){
						$template = ria_mysql_fetch_assoc($r_template);
						if ($seo_title_id <= 0){
							if ($template['type'] == 'title'){
								$to_delete_sob_ids[] = $seo_object['id'];
							}
						} else {
							if ($seo_filter && $seo_title_id != $seo_object['seo_id']){
								if ($template['type'] == 'title'){
									$to_delete_sob_ids[] = $seo_object['id'];
								}
							}
						}
						if ($seo_desc_id <= 0){
							if ($template['type'] == 'desc'){
								$to_delete_sob_ids[] = $seo_object['id'];
							}
						} else {
							if ($seo_filter && $seo_desc_id != $seo_object['seo_id']){
								if ($template['type'] == 'desc'){
									$to_delete_sob_ids[] = $seo_object['id'];
								}
							}
						}
					}
				}
				foreach ($to_delete_sob_ids as $id) {
					if (!seo_objects_del($id)){
						$error = _("Une erreur inattendue s'est produite lors de la suppression du précédent contenu.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
					}
				}
			}
			if( (!isset($lng_code) || strtolower($lng_code)==$config['i18n_lng']) ){
				switch($cls_id){
					case CLS_PRODUCT: { // Onglet Référencement de la fiche Produit
						if (!prd_classify_update_referencing($_GET['cat'], $object['id'], !isset($_POST['perso_title']) || $_POST['perso_title'] <= 0 ? $_POST['tag_title'] : $object['tag_title'], !isset($_POST['perso_desc']) || $_POST['perso_desc'] <= 0 ? $_POST['tag_desc'] : $object['tag_desc'], $_POST['keywords'])){
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du référencement du produit.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur");
						}
						break;
					}
					case CLS_CATEGORY: { // Onglet Référencement de la fiche Catégorie
						if( prd_categories_update_referencing($object['id'], !isset($_POST['perso_title']) || $_POST['perso_title'] <= 0 ? $_POST['tag_title'] : $object['tag_title'], !isset($_POST['perso_desc']) || $_POST['perso_desc'] <= 0 ? $_POST['tag_desc'] : $object['tag_desc']) ){
							if( !prd_categories_update_keywords( $object['id'], $_POST['keywords'] ) ){
								$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des mots clés.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
							}
						}else{
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du référencement de la catégorie.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
						}
						break;
					}
					case CLS_CMS: { // Onglet Référencement des pages CMS
						if ( !cms_categories_update_referencing($object['id'], !isset($_POST['perso_title']) || $_POST['perso_title'] <= 0 ? $_POST['tag_title'] : $object['tag_title'], !isset($_POST['perso_desc']) || $_POST['perso_desc'] <= 0 ? $_POST['tag_desc'] : $object['tag_desc'], $_POST['keywords']) ) {
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du référencement de la page de contenu.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
						}
						break;
					}
					case CLS_STORE: { // Onglet Référencement de la fiche Magasin
						if( !dlv_stores_update_referencing( $object['id'], !isset($_POST['perso_title']) || $_POST['perso_title'] <= 0 ? $_POST['tag_title'] : $object['tag_title'], !isset($_POST['perso_desc']) || $_POST['perso_desc'] <= 0 ? $_POST['tag_desc'] : $object['tag_desc'], $_POST['keywords']) ){
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du référencement du magasin.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
						}
						break;
					}
					case CLS_NEWS: { // Onglet Référencement de la fiche Actualité
						if( !news_update_referencing($object['id'], !isset($_POST['perso_title']) || $_POST['perso_title'] <= 0 ? $_POST['tag_title'] : $object['tag_title'], !isset($_POST['perso_desc']) || $_POST['perso_desc'] <= 0 ? $_POST['tag_desc'] : $object['tag_desc'], $_POST['keywords']) ){
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du référencement de l'actualité.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
						}
						break;
					}
					case CLS_FAQ_CAT: { // Onglet Référencement de la fiche Catégorie de la FAQ
						if( !faq_categories_update_referencing($object['id'], !isset($_POST['perso_title']) || $_POST['perso_title'] <= 0 ? $_POST['tag_title'] : $object['tag_title'], !isset($_POST['perso_desc']) || $_POST['perso_desc'] <= 0 ? $_POST['tag_desc'] : $object['tag_desc'], $_POST['keywords']) ){
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du référencement de la catégorie.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
						}
						break;
					}
					case CLS_FAQ_QST: { // Onglet Référencement de la fiche Question d'une catégorie de la FAQ
						if( !faq_questions_update_referencing($object['id'], !isset($_POST['perso_title']) || $_POST['perso_title'] <= 0 ? $_POST['tag_title'] : $object['tag_title'], !isset($_POST['perso_desc']) || $_POST['perso_desc'] <= 0 ? $_POST['tag_desc'] : $object['tag_desc'], $_POST['keywords']) ){
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du référencement de la question.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
						}
						break;
					}
					case CLS_BRAND: {
						if( !prd_brands_update_referencing($object['id'], !isset($_POST['perso_title']) || $_POST['perso_title'] <= 0 ? $_POST['tag_title'] : $object['tag_title'], !isset($_POST['perso_desc']) || $_POST['perso_desc'] <= 0 ? $_POST['tag_desc'] : $object['tag_desc'], $_POST['keywords']) ){
							$error = 1;
						}
						break;
					}
				}
			} elseif( isset($lng_code) && strtolower($lng_code)!=$config['i18n_lng'] && sizeof($fields)){
				$tsk_obj = fld_translates_get( $cls_id, $object['id'], $lng_code, $object, array($fields['tag_title']=>'tag_title', $fields['tag_desc']=>'tag_desc'), true );
				$values = array(
					$fields['tag_title']=>!isset($_POST['perso_title']) || $_POST['perso_title'] <= 0 ? $_POST['tag_title'] : $tsk_obj['tag_title'],
					$fields['tag_desc']=>!isset($_POST['perso_desc']) || $_POST['perso_desc'] <= 0 ? $_POST['tag_desc'] : $tsk_obj['tag_desc'],
					$fields['keywords']=>$_POST['keywords']
				);
				if( !fld_translates_add($seo_objects, $lng_code, $values) ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des informations traduites du produit.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
				}
			}
			if( isset($_POST['previous_title_text']) || isset($_POST['previous_desc_text']) ){
				$seo_title_exists = $seo_title_id ? seo_objects_exists($seo_objects, 0, $seo_title_id) : false;
				$seo_desc_exists = $seo_desc_id ? seo_objects_exists($seo_objects, 0, $seo_desc_id) : false;
				if ($seo_title_id && !$seo_title_exists){
					if (!seo_objects_add($seo_objects, $cls_id, $seo_title_id)){
						$error = _("Une erreur inattendue s'est produite lors de l'ajout du nouveau contenu (title).")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
					}
				}
				if ($seo_desc_id && !$seo_desc_exists){
					if (!seo_objects_add($seo_objects, $cls_id, $seo_desc_id)){
						$error = _("Une erreur inattendue s'est produite lors de l'ajout du nouveau contenu (meta-description).")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
					}
				}
			}
			$key_fld_fields = $object['id'];
			if ($cls_id == CLS_PRODUCT) {
				$key_fld_fields = array($_GET['cat'],$object['id']);
			}
			// sauvegarde de la personnalisation de l'url - Uniquement dans les cas de CLS_PRODUCT / CLS_CATEGORY et CLS_CMS
			if( isset($_POST['url_perso'], $_POST['old_url'])){
				if (trim($_POST['url_perso'])!='' ) {
					$save_lng = isset($lng_code) && in_array($lng_code, $config['i18n_lng_used']) ? $lng_code : $config['i18n_lng'];

					$ar_control_url = array($object[$keys[0]], $object[$keys[1]]);
					if ($save_lng != $config['i18n_lng']) {
						$temp = fld_object_values_get($key_fld_fields, $fields['url'], $save_lng, false, true);
						if (trim($temp) != '') {
							$ar_control_url = array($temp);
						}

						$temp = fld_object_values_get($key_fld_fields, $fields['url_perso'], $save_lng, false, true);
						if (trim($temp) != '') {
							array_push($ar_control_url, $temp);
						}
					}

					$can_save_url_perso = true;
					if (in_array($_POST['url_perso'], $ar_control_url)) {
						$can_save_url_perso = -1;
					}else{
						// Vérifier que l'url personnalisée n'est pas déjà utilisée sur un autre contenu
						$r_url = rew_rewritemap_get('', $_POST['url_perso'], 0, null, false);
						if ($r_url && ria_mysql_num_rows($r_url)) {
							$can_save_url_perso = false;
						}
					}

					if ($can_save_url_perso !== -1) {
						if ($can_save_url_perso === false) {
							$error = sprintf(_("L'url \"%s\" est déjà utilisée."), htmlspecialchars($_POST['url_perso']));
						} else {
							$new_url = urlalias2($_POST['url_perso']);

							if ($url_p != "" && !rew_rewritemap_customized($_POST['old_url'], $new_url, $url_p, $save_lng, $cls_id, $rewrite_object)) {
								$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la version personnalisée de l'URL du contenu.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
							} else {
								// Permet de supprimer les traces d'une URL personalisée quand elle est remplacée par une autre, sinon il y a des restes
								if ($_POST['old_url'] != $_POST['default_url']) {
									rew_rewritemap_del($_POST['old_url'], '', $save_lng);
									prd_products_change_url_unreachable($_POST['old_url'], $new_url);
								}
								if ($save_lng != $config['i18n_lng']) {
									if (!fld_object_values_set($key_fld_fields, $fields['url_perso'], $new_url, $save_lng)) {
										$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la version personnalisée de l'url du contenu.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
									}
								} else {
									switch($cls_id){
										case CLS_PRODUCT: { // Onglet Référencement de la fiche Produit
											if (!prd_classify_set_url_perso($_GET['cat'], $object['id'], $new_url)) {
												$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la version personnalisée de l'URL du produit.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur.");
											}
											break;
										}
										case CLS_CATEGORY: { // Onglet Référencement de la fiche Catégorie
											if (!prd_categories_set_url_perso($object['id'], $new_url)) {
												$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la version personnalisée de l'URL de la catégorie.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
											}
											break;
										}
										case CLS_CMS: { // Onglet Référencement des pages CMS
											if( !cms_categories_set_url_perso($object['id'], $new_url) ){
												$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la version personnalisée de l'URL du contenu.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
											}
											break;
										}
									}

								}
							}
						}
					}
				}
				// Dans le cas où on supprime la valeur de l'url personnalisée, on fait un RAZ avec l'url de base
				else if (trim($_POST['url_perso'])=='' && $_POST['old_url'] != $_POST['default_url']){

					$save_lng = isset($lng_code) && in_array($lng_code, $config['i18n_lng_used']) ? $lng_code : $config['i18n_lng'];
					$base_url = urlalias2($_POST['default_url']);

					if ($url_p != "" && !rew_rewritemap_clean_redirection_del_then_update_with_base_url($_POST['old_url'], $base_url, $save_lng)) {
						$error = _("Une erreur inattendue s'est produite lors de la suppression de la version personnalisée de l'URL du contenu.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
					} else {
						if ($save_lng != $config['i18n_lng']) {
							if (!fld_object_values_set($key_fld_fields, $fields['url_perso'], '', $save_lng)) {
								$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la version personnalisée de l'url du contenu.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
							}
						} else {
							switch($cls_id){
								case CLS_PRODUCT: { // Onglet Référencement de la fiche Produit
									if (!prd_classify_del_and_set_base_url_perso($_GET['cat'], $object['id'], $base_url)) {
										$error = _("Une erreur inattendue s'est produite lors de la suppression de la version personnalisée de l'URL du produit.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur.");
									}
									break;
								}
								case CLS_CATEGORY: { // Onglet Référencement de la fiche Catégorie
									if (!prd_categories_del_and_set_base_url_perso($object['id'], $base_url)) {
										$error = _("Une erreur inattendue s'est produite lors de la suppression de la version personnalisée de l'URL de la catégorie.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
									}
									break;
								}
								case CLS_CMS: { // Onglet Référencement des pages CMS
									if( !cms_categories_del_and_set_base_url_perso($object['id'], $base_url) ){
										$error = _("Une erreur inattendue s'est produite lors de la suppression de la version personnalisée de l'URL du contenu.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
									}
									break;
								}
							}

						}
					}
				}
				else if (trim($_POST['url_perso'])=='') {
					$save_lng = isset($lng_code) && in_array($lng_code, $config['i18n_lng_used']) ? $lng_code : $config['i18n_lng'];
					$base_url = urlalias2($_POST['default_url']);
					if ($save_lng != $config['i18n_lng']) {
						if (!fld_object_values_set($key_fld_fields, $fields['url_perso'], '', $save_lng)) {
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la version personnalisée de l'url du contenu.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
						}
					} else {
						switch($cls_id){
							case CLS_PRODUCT: { // Onglet Référencement de la fiche Produit
								if (!prd_classify_del_and_set_base_url_perso($_GET['cat'], $object['id'], $base_url)) {
									$error = _("Une erreur inattendue s'est produite lors de la suppression de la version personnalisée de l'URL du produit.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur.");
								}
								break;
							}
							case CLS_CATEGORY: { // Onglet Référencement de la fiche Catégorie
								if (!prd_categories_del_and_set_base_url_perso($object['id'], $base_url)) {
									$error = _("Une erreur inattendue s'est produite lors de la suppression de la version personnalisée de l'URL de la catégorie.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
								}
								break;
							}
							case CLS_CMS: { // Onglet Référencement des pages CMS
								if( !cms_categories_del_and_set_base_url_perso($object['id'], $base_url) ){
									$error = _("Une erreur inattendue s'est produite lors de la suppression de la version personnalisée de l'URL du contenu.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
								}
								break;
							}
						}

					}
				}
			}
		}

		if( !isset($error) ){
			if ($cls_id == CLS_CATEGORY){
				try{
					// Index la commande dans le moteur de recherche.
					RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
						'cls_id' => CLS_CATEGORY,
						'obj_id_0' => $object['id'],
					));
				}catch(Exception $e){
					error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
				}
			}

			$_SESSION['referencement_edit_success'] = _('Le référencement a été mis à jour avec succès.');
			$url_lng = isset($lng_code) && in_array($lng_code, $config['i18n_lng_used']) ? '&lng=' . $lng_code : '';

			if ($cls_id != CLS_BRAND){
				header('Location: '.$url_header . $url_lng);
				exit;
			}
		} else {
			$_SESSION['referencement_edit_error'] = $error;
		}
	}
}

/** Cette fonction permet de charger l'url de l'image par défaut dans l'administration.
 *  Celle configurée par le client sera utilisé en priorité.
 *  @param string $size Optionnel, dimensions de l'image, trois valeur possibles : "small", "medium" ou "high". Par défaut "small"
 * 	@return string l'url de l'image
 */
function view_admin_get_img_default( $size='small' ){
	global $config;

	if( !in_array($size, array('small', 'medium', 'high')) ){
		return false;
	}

	$img_default_url = false;
	if( isset($config['default_image']) && is_numeric($config['default_image']) && $config['default_image'] >= 0 ){
		// Si l'image par défaut est configurée alors elle sera utilisé
		$img_default_url = $config['img_url'].'/'.$config['img_sizes'][$size]['dir'].'/'.$config['default_image'].'.'.$config['img_sizes'][$size]['format'];
	}else{
		// Si aucune image par défaut n'est configurée, alors on utilise celle prévue par RiaShop
		$img_default_url = '/admin/images/default.jpg';
	}

	return $img_default_url;
}

/** Cette fonction permet l'affichage de la barre de recherche (notamment dans le header)
 *
 * @return	string	HTML de la barre de rechercher
 */
function view_admin_searchbar(){
	global $config;

	$placeholder = _('Rechercher');

	if( gu_users_admin_rights_used('_MDL_CATALOG') ){
		$placeholder .= _(' un produit,');
	}

	if( gu_users_admin_rights_used('_MDL_ORDERS') ){
		$placeholder .= _(' une commande,');
	}

	if( gu_users_admin_rights_used('_MDL_CUSTOMERS') ){
		$placeholder .= _(' un client,');
	}
	$placeholder = trim($placeholder, ',');
	$placeholder .= '...';

	return '
		<form class="app-searchbar" method="get" action="/admin/search/index.php">
			<span class="app-searchbar-icon"></span>
			<input type="text" class="app-searchbar-input" name="q" placeholder="'.$placeholder.'" />
			<button class="app-searchbar-cmd">&#8617; <span>'._('pour chercher').'</span></button>
		</form>
	';

}

/**	Cette fonction permet l'affichage de la sidebar
 *
 * @return	string	HTML de la sidebar
 */
function view_admin_sidebar(){
	require_once 'view/admin/Menu.inc.php';

	$Menu = SidebarMenu::getInstance();
	$ar_main = $Menu->getMain();
	$ar_second = $Menu->getSecond();

	$html = '
		<nav class="app-menubar">
			'.view_admin_searchbar().'
			<div class="app-menubar-inner">
				<button class="app-menubar-btn-top">↑</button>

	';

	// Menu principal (Yuto, Catalogue, Clients, etc...)
	if( count($ar_main) ){
		$html .= '<nav class="app-menubar-major">';

		foreach($ar_main as $code => $menu){
			$class = 'app-menu-item';

			if( isset($menu['href']) && riashop_menu_entry_is_active($menu['href']) ){
				$class .= ' is-active';

			}
			$html .= '
				<div class="'.$class.'" data-code="'.$code.'">
					<span class="btn"></span>
					<label>'.htmlspecialchars($menu['name']).'</label>
				</div>
			';

		}
		$html .= '</nav>';

	}

	// Menu secondaire (Aide, Configuration, Synchronisation)
	if( count($ar_second) ){
		$html .= '<nav class="app-menubar-minor">';

		foreach($ar_second as $code => $menu){
			$class = 'app-menu-item';

			if( isset($menu['href']) && riashop_menu_entry_is_active($menu['href']) ){
				$class .= ' is-active';

			}
			$html .= '
				<div class="'.$class.'" data-code="'.$code.'">
					<span class="btn"></span>
					<label>'.htmlspecialchars($menu['name']).'</label>
				</div>
			';

		}
		$html .= '</nav>';

	}

	$html .= '
				<button class="app-menubar-btn-bottom">↓</button>
			</div>
			<nav class="app-menubar-submenu">
				<button class="btn btn-close" title="'._('Menu principal').'"></button>
				<ul class="app-submenu-items">
				</ul>
			</nav>
		</nav>
	';

	return $html;

}

/** Cette fonction permet de déterminer la page en cours
 *
 * @param	string	$path	Obligatoire, path de la page, exemple: 'catalog/index.php'
 * @return	bool	True en cas de succes, false sinon
 */
function riashop_is_page($path){

	if( !is_string($path) || ($path = trim($path)) == '' ){
		return false;
	}

	if( isset($_SERVER['SCRIPT_URL']) && trim($_SERVER['SCRIPT_URL']) != '' ){
		$filename = $_SERVER['SCRIPT_URL'];

	}elseif( isset($_SERVER['PHP_SELF']) && trim($_SERVER['PHP_SELF']) != '' ){
		$filename = $_SERVER['PHP_SELF'];

	}elseif( isset($_SERVER['REQUEST_URI']) && trim($_SERVER['REQUEST_URI']) != ''){
		$filename = str_replace('/admin', '', $_SERVER['REQUEST_URI']);

	}elseif( isset($_SERVER['SCRIPT_FILENAME']) && trim($_SERVER['SCRIPT_FILENAME']) != '' ){
		if( isset($_SERVER['DOCUMENT_ROOT']) && preg_match('/^[a-z\/\_\-]*\/htdocs/i', $_SERVER['DOCUMENT_ROOT'], $m) && is_array($m) && isset($m[0]) ){
			$filename = str_replace($m[0], '', $_SERVER['SCRIPT_FILENAME'] );
		}

	}

	if( !isset($filename) ){
		return false;
	}

	$filename = trim($filename);
	$filename = preg_match('/^\//', $filename) ? substr($filename, 1) : $filename;
	$path = preg_match('/^\//', $path) ? substr($path, 1) : $path;

	return htmlspecialchars_decode($path) == htmlspecialchars_decode($filename);
}

/** Cette fonction permet de déterminer la page en cours est la page d'accueil de l'admin riashop
 *
 * @return	bool	True en cas de succes, false sinon
 */
function riashop_is_home(){

	return riashop_is_page('/');

}

/** Cette fonction permet de déterminer la page en cours est le compte de l'utilisateur dans l'admin riashop
 *
 * @return	bool	True en cas de succes, false sinon
 */
function riashop_is_account(){

	return riashop_is_page('options/my-account.php');

}

/** Cette fonction permet de vérifier que la page en cours est active par rapport à un path
 *
 * @param	string	$path	Obligatoire, path à tester
 * @return	bool	True en cas de succès, false sinon
 */
function riashop_menu_entry_is_active($path){

	if( !is_string($path) || ($path = trim($path)) == ''){
		return false;
	}

	if( isset($_SERVER['SCRIPT_URL']) && trim($_SERVER['SCRIPT_URL']) != '' ){
		$filename = $_SERVER['SCRIPT_URL'];

	}elseif( isset($_SERVER['PHP_SELF']) && trim($_SERVER['PHP_SELF']) != '' ){
		$filename = $_SERVER['PHP_SELF'];

	}elseif( isset($_SERVER['REQUEST_URI']) && trim($_SERVER['REQUEST_URI']) != ''){
		$filename = strtolower($_SERVER['REQUEST_URI']);

		if( $filename == '/' || $filename == '/admin/' ){
			$filename = '/admin/index.php';
		}

	}elseif( isset($_SERVER['SCRIPT_FILENAME']) && trim($_SERVER['SCRIPT_FILENAME']) != '' ){
		if( isset($_SERVER['DOCUMENT_ROOT']) && preg_match('/^[a-z\/\_\-]*\/htdocs/i', $_SERVER['DOCUMENT_ROOT'], $m) && is_array($m) && isset($m[0]) ){
			$filename = str_replace($m[0], '', $_SERVER['SCRIPT_FILENAME'] );
		}
	}

	if( !isset($filename) ){
		return false;
	}

	$filename = trim($filename);
	$test = preg_match('/^\/?admin/', $filename);

	if( $test == false || $test < 0 ){
		$filename = substr($filename, 0, 1) !== '/' ? '/'.$filename : $filename;
		$filename = '/admin'.$filename;
	}

	$path = substr($path, 0, 1) !== '/' ? '/'.$path : $path;
	$dpath = dirname($path);
	$dfile = dirname($filename);
	$dfile = substr($dfile, 0, strlen($dpath));

	return $dpath == $dfile;

}

/** Cette fonction permet de vérifier que la page en cours est active par rapport à un path
 *
 * @param	string	$path	Obligatoire, path à tester
 * @return	bool	True en cas de succès, false sinon
 */
function riashop_submenu_entry_is_active($path){

	if( !is_string($path) || ($path = trim($path)) == ''){
		return false;
	}
	$path = preg_replace('/^\/?admin\//i', '', $path);

	if( isset($_SERVER['REQUEST_URI']) && trim($_SERVER['REQUEST_URI']) != ''){
		$filename = str_replace('/admin', '', $_SERVER['REQUEST_URI']);

		if( $filename == '/'){
			$filename = 'index.php';
		}

	}

	if( !isset($filename) ){
		return riashop_is_page($path);
	}
	$filename = trim($filename);
	$filename = preg_match('/^\//', $filename) ? substr($filename, 1) : $filename;
	$path = preg_match('/^\//', $path) ? substr($path, 1) : $path;

	return htmlspecialchars_decode($path) == htmlspecialchars_decode($filename);

}
/// @}

/// \endcond
//
//

/** Cette function génère une vue tableau des droits activés pour export, uniquement en vue superadmin
 *
 */

function view_admin_rights_export_table($tnt, $wst, $usr=false, $is_yuto=false){
	global $config;
	// Vérifie les paramètres d'entrée
	if(
		!is_numeric($tnt) || $tnt<=0
		|| !is_numeric($wst) || $wst<=0
	){
		return false;
	}

	$return="";


	 if ($is_yuto) {
                                $r_module = gu_categories_rights_get(0, null, true, false);
                        } else {
                                $r_module = gu_categories_rights_get(0, true, true, true);
         }

        while( $module = ria_mysql_fetch_assoc($r_module) ){


			if ($is_yuto) {
				$nb_rights = gu_categories_rights_count_rights( $module['id'], null, $usr, true, false);
			} else {
				$nb_rights = gu_categories_rights_count_rights( $module['id'], true, $usr, false, true);
			}

			if( $nb_rights == 0 ){
				continue;
			}


				if ($is_yuto) {
					$r_right = gu_rights_get( 0, '', $module['id'], array(), null, null, true, $usr, true, false);
				} else {
					$r_right = gu_rights_get( 0, '', $module['id'], array(), true, null, true, $usr, false, true);
				}


						while( $right = ria_mysql_fetch_assoc($r_right) ){
							if( $right['id'] == 1000029 ){
								// La gestion d'abonnement Yuto n'est accessible que pour les Yuto Business
								if( RegisterGCP::onGcloud() && !in_array(RegisterGCP::getPackage($config['tnt_id']), ['business']) ){
									continue;
								}
							}
							if( $right['id'] == 1000068 ){
								// La gestion d'abonnement RiaShop n'est accessible que pour les RiaShop Essentiel ou Business
								if( RegisterGCP::onGcloud() && !in_array(RegisterGCP::getPackageBtoB($config['tnt_id']), ['essentiel', 'business']) ){
									continue;
								}
							}
							$has_child = gu_rights_count_childs($right['id']);



							$return .= view_admin_subright_export_table( $module['name'], $right['id'], 1, $usr, $is_yuto );

						}

				}


	return $return;

}

function view_admin_subright_export_table($module, $parent, $depth, $usr=false, $is_yuto=false){


    $return="";


        if ($is_yuto) {
                $r_right = gu_rights_get( 0, '', $module, array(), null, $parent, true, $usr, true, false);
        } else {
                $r_right = gu_rights_get( 0, '', $module, array(), true, $parent, true, $usr, false, true);
        }
        $rgh_name = $is_yuto ? 'rgh-yuto' : 'rgh';
        	if( !$r_right || !ria_mysql_num_rows($r_right) ){
			return "" ;
    }else{

	while( $right = ria_mysql_fetch_assoc($r_right) ){
        	$has_child = gu_rights_count_childs($right['id']);



		if (gu_user_is_authorized($right['code'])) {
			$is_activated="on";
		} else {
			$is_activated="off";
		}
		$return.=  $is_activated."|" .$module."|". $right['code']."|". $right['desc']. "|". $right['name']." <br />";

		$return.=view_admin_subright_export_table($module, $right['id'], $depth+1, $usr, $is_yuto);


	}

    }

    return $return;
}
