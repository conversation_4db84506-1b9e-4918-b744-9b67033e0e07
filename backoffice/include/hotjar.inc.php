<?php

/** Retourne la liste des identifiants de tenants exclus de tout suivi hotjar
 *	@return array la liste des identifiants de tenants sous forme de tableau
 */
function hotjar_exclude_list(){

	return array(
		0,52,121,189,194,196,197,201,203,208,231,232,233,234,235,236,237,238,239,241,242,246,247,248,249,250,251,261,285,306,315,324,325,326,327,328,
		334,335,337,342,343,344,345,346,347,407,408,409,419,425,426,434,435,438,442,443,449,451,457,458,473,474,507,508,509,510,511,512,513,514,
	);

}

/**	Retourne un booléen indiquant si oui ou non l'installation fait l'objet d'un suivi hotjar
 * 	@return bool true si le tenant doit
 */
function hotjar_include_tenant(){
	global $config;

	$exclude_list = hotjar_exclude_list();

	// Vérifie que l'instance n'est pas exclue du tracking
	if( isset($config['tnt_id']) && in_array( $config['tnt_id'], $exclude_list, false ) ){
		return false;
	}

	// Pour la suite, l'identifiant du tenant est obligatoire
	if( !isset($_SESSION['usr_tnt_id']) ){
		return true;
	}

	// Les super-users sont exclus du tracking
	if( $_SESSION['usr_tnt_id']==0 ){
		return false;
	}

	// Si $config['tnt_id'] n'était pas disponible on peut arriver jusqu'à vérifier le usr_tnt_id
	return !in_array( $_SESSION['usr_tnt_id'], $exclude_list, false );

}
