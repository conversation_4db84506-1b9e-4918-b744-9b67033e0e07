<?php

	/**`\file graph-dev-subscriptions.php
	 *	Ce fichier est chargé de l'affichage des données d'utilisation de Yuto (licences actives) sur une période choisie
	 */

	if( !isset($highcharts_show_title) ){
		$highcharts_show_title = true;
	}

	require_once('categories.inc.php');
	require_once('stats.inc.php');

	if( $highcharts_show_title ){
		print '<h3>'._('Nombre de licences actives').'</h3>';
	}

	print '<div id="graph-dev-subscriptions"></div>';

	// Récupération des statistiques
	// Nombre de devices/licences actifs hors administrateurs
	$filtre1 = array("usr_prf_id" => -PRF_ADMIN);
	$dev_users_subscriptions = stats_graphs_get_datas( 'dev-subscriptions', $date1, $date2, $filtre1, -1, $wst_id );
	// Nombre de devices/licences actifs associés à un compte administrateur
	$filtre2 = array("usr_prf_id" => PRF_ADMIN);
	$dev_admin_subscriptions = stats_graphs_get_datas( 'dev-subscriptions', $date1, $date2, $filtre2, -1, $wst_id );

?>
<script><!--
	$(function () {
		$('#graph-dev-subscriptions').highcharts({
			chart: {
				type: "spline",
				plotBorderWidth: 0,
				animation: false,
				events: {
					load: function (event) {
						var extremes = this.yAxis[0].getExtremes();
						if (extremes.dataMax == 0) {
							this.yAxis[0].setExtremes(0, 5);
						}
					}
				}
			},
			credits: {
				enabled: false
			},
			exporting: {
				filename: 'statistiques-nombre-tablettes'
			},
			title: {
				text: '',
				x: -20
			},
			xAxis: {
				categories: [<?php print '\''.implode('\', \'', array_keys($dev_users_subscriptions)).'\''; ?>]
			},
			<?php /*yAxis: {
				allowDecimals: false,
				title: {
					text: ''
				},
				min: 0,
				plotLines: [{
				    color: '#FF0000',
				    width: 2,
				    value: <?php print max(array_values($dev_users_subscriptions)); ?>,
				    label: {
				   		text: "<?php print _('Nombre de licences utilisées au maximum sur la période')?>"
				    }
			   }]
			},*/ ?>
			legend: {
				layout: 'horizontal',
				align: 'center',
				verticalAlign: 'top',
				borderWidth: 0
			},
			tooltip: {
				shared: true,
				crosshairs: true,
				followTouchMove: true,
				style: {
					fontSize: "13px"
				},
				formatter: function() {
					var str = '<span style="font-size: 12px;">'+ this.x +'</span>';

					$.each(this.points, function(i, point) {
						str += '<br/><span style="color:'+point.series.color+'">'+ point.series.name +'</span>: <b>'+
						point.y+'</b>';
					});

					return str;
				},
			},
			series: [
				{
					name: '<?php print _('Administrateurs'); ?>',
					data: [<?php print implode( ', ', array_values($dev_admin_subscriptions) ); ?>],
					color: '#8AA54E'
				},
				{
					name: '<?php print _('Utilisateurs'); ?>',
					data: [<?php print implode( ', ', array_values($dev_users_subscriptions) ); ?>],
					color: '#4572A7'
				},
			]
		});
	});
//--></script>