<?php
	require_once('categories.inc.php');
	require_once('stats.inc.php');

	// Par défaut, on arrive sur le jour en cours
	if( isset($_GET['date1']) && isdate($_GET['date1']) ){ $_SESSION['datepicker_date1'] = $_GET['date1']; }
	if( isset($_GET['date2']) && isdate($_GET['date2']) ){ $_SESSION['datepicker_date2'] = $_GET['date2']; }

	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : date('Y-m-d');
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	
	print '
		<h3>Provenance des ventes</h3>
		<div id="graph-origins"></div>
	';

	// Paramètre de recherche
	$prd_id = isset($_GET['prd']) ? $_GET['prd'] : 0;
	$cat_id = isset($_GET['cat']) ? $_GET['cat'] : 0;
	
	// Récupération des statistiques
	$origins = stats_graphs_get_datas( 'origins', $date1, $date2, array('cat'=>$cat_id) );
	
	$data = '';
	foreach( $origins as $key=>$val ){
		if( trim($data)!='' ){
			$data .= ', ';
		}

		$data .= '[\''.$key.'\', '.$val.']';
	}
?>
<script>
	$(function(){
		$('#graph-origins').highcharts({
			chart: {
				plotBackgroundColor: null,
				plotBorderWidth: null,
				plotShadow: false
			},
			credits: {
				enabled: false
			},
			exporting: {
				filename: 'stats-contacts'
			},
			title: {
				text: 'Provenance des ventes'
			},
			tooltip: {
				pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
			},
			plotOptions: {
				pie: {
					allowPointSelect: true,
					cursor: 'pointer',
					dataLabels: {
						enabled: true,
						color: '#000000',
						connectorColor: '#000000',
						format: '<b>{point.name}</b>: {point.percentage:.1f} %'
					}
				}
			},
			series: [{
				type: 'pie',
				name: 'Browser share',
				data: [
					<?php print $data; ?>
				]
			}]
		});
	});
</script>