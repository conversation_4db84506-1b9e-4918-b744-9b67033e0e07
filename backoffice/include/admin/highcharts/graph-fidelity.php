<?php

	/**	\file graph-fidelity.php
	 *	Ce fichier est utilisé en include par le fichier fidelity.php pour afficher un graphique sur la fidélité des clients.
	 */

    require_once("users.inc.php");

    $tenant = ria_mysql_fetch_array(tnt_tenants_get($config['tnt_id']));

    if(!isset($_GET['date1']) || !isset($_GET['date2'])){
        $_GET['date1'] = '01/01/'.date('Y');
        $_GET['date2'] = '31/12/'.date('Y');
        $period = true;
    }else if(strtotime(dateparse($_GET['date1']))==strtotime($tenant['date-created']) && strtotime(dateparse($_GET['date2']))==strtotime(date("Y-m-d"))){
        // Si la période sélectionnée correspond à "toute la période" elle est ignoré.
        $period = false;
    }else{
        $period = true;
    }

	// Calcule la date de début de la période précédente à partir de la date de début de la période -1 an
    $date1LastYear = date_create_from_format( 'd/m/Y', $_GET['date1'] );
    date_modify( $date1LastYear, '-1 year' );
    $date1LastYear = date_format( $date1LastYear, 'd/m/Y' );
	
	// Calcule la date de fin de la période précédente à partir de la date de fin de la période -1 an
    $date2LastYear = date_create_from_format( 'd/m/Y', $_GET['date2'] );
    date_modify( $date2LastYear, '-1 year' );
    $date2LastYear = date_format( $date2LastYear, 'd/m/Y' );

	// Calcule les statistiques pour la période en cours
    $nb_users_total = gu_users_get_nb( $_GET['date1'], $_GET['date2'] );
    $nb_users_1_orders = gu_users_get_nb_users_by_nb_orders( 1, $_GET['date1'], $_GET['date2'] );
    $nb_users_2_orders = gu_users_get_nb_users_by_nb_orders( 2, $_GET['date1'], $_GET['date2'] );
    $nb_users_3_orders = gu_users_get_nb_users_by_nb_orders( 3, $_GET['date1'], $_GET['date2'] );
    $nb_users_4_orders = gu_users_get_nb_users_by_nb_orders( 4, $_GET['date1'], $_GET['date2'] );
    $nb_users_5_orders = gu_users_get_nb_users_by_nb_orders( 5, $_GET['date1'], $_GET['date2'] );

	// Calcule les statistiques pour la période précédente
    $nb_users_last_year_total = gu_users_get_nb( $date1LastYear, $date2LastYear );
    $nb_users_last_year_1_orders = gu_users_get_nb_users_by_nb_orders( 1, $date1LastYear, $date2LastYear );
    $nb_users_last_year_2_orders = gu_users_get_nb_users_by_nb_orders( 2, $date1LastYear, $date2LastYear );
    $nb_users_last_year_3_orders = gu_users_get_nb_users_by_nb_orders( 3, $date1LastYear, $date2LastYear );
    $nb_users_last_year_4_orders = gu_users_get_nb_users_by_nb_orders( 4, $date1LastYear, $date2LastYear );
    $nb_users_last_year_5_orders = gu_users_get_nb_users_by_nb_orders( 5, $date1LastYear, $date2LastYear );

	print '
		<div id="graph-fidelity"></div>
	';

?>
<script><!--
	$(function () {
		$('#graph-fidelity').highcharts({
            credits: {
                  enabled: false
            },
            chart: {
                type: 'column'
            },
            title: {
                text: '<?php print _('Nombre de comptes clients par nombre de commandes'); ?>'
            },
            xAxis: {
                type: 'category',
                title: {
                    text: '<?php print _('Nombre de commandes'); ?>'
                }
            },
            yAxis: {
                title: {
                    text: '<?php print _('Nombre de clients'); ?>'
                }
            },
            legend: {
                enabled: true
            },
            plotOptions: {
                series: {
                    borderWidth: 0,
                    dataLabels: {
                        enabled: true,
                        format: '{point.yWithDelimiter}'
                    }
                }
            },

            tooltip: {
                enabled: true,
                headerFormat: '',
                pointFormat: '{point.tooltip}</b>',
                style: {
                    width:'300px'
                }
            },

            "series": [
                {
                    "name": "<?php print _('Clients année précédente'); ?>",
                    "colorByPoint": false,
                    "data": [
                        {
                            "name": "0+",
                            "y": <?php print intval($nb_users_last_year_total); ?>,
                            "yWithDelimiter" : <?php print "'".number_format($nb_users_last_year_total, 0, ',', ' ')."'" ?>,
                            "tooltip":"<?php  print ($period)? _('Nombre de nouveaux comptes durant l\'année précedente de la période sélectionnée '): _('Nombre de comptes total') ?>",
                        },
                        {
                            "name": "1+",
                            "y": <?php print intval($nb_users_last_year_1_orders); ?>,
                            "yWithDelimiter" : <?php print "'".number_format($nb_users_last_year_1_orders, 0, ',', ' ')."'" ?>,
                            "tooltip":"<?php print sprintf( _('%d %% des comptes '.(($period)? 'créé durant l\'année précedente de la période sélectionnée ': '').'ont réellement passé une commande'), ($nb_users_last_year_total>0)? intval($nb_users_last_year_1_orders/$nb_users_last_year_total*100): 0 ) ?>",
                        },
                        {
                            "name": "2+",
                            "y": <?php print intval($nb_users_last_year_2_orders); ?>,
                            "yWithDelimiter" : <?php print "'".number_format($nb_users_last_year_2_orders, 0, ',', ' ')."'" ?>,
                            "tooltip":"<?php print sprintf( _('%d %% des comptes '.(($period)? 'créé durant l\'année précedente de la période sélectionnée et ': '').'ayant commandé une fois ont recommencé au moins une seconde fois <br>(soit %d %% de tout les clients)'), 
                            ($nb_users_last_year_1_orders>0)? intval($nb_users_last_year_2_orders/$nb_users_last_year_1_orders*100):0,
                            ($nb_users_last_year_total>0)? intval($nb_users_last_year_2_orders/$nb_users_last_year_total*100):0 )?>",
                        },
                        {
                            "name": "3+",
                            "y": <?php print intval($nb_users_last_year_3_orders); ?>,
                            "yWithDelimiter" : <?php print "'".number_format($nb_users_last_year_3_orders, 0, ',', ' ')."'" ?>,
                            "tooltip":"<?php print sprintf( _('%d %% des comptes '.(($period)? 'créé durant l\'année précedente de la période sélectionnée et ': '').'ayant commandé une fois ont recommencé au moins une troisième fois <br>(soit %d %% de tout les clients)'), 
                            ($nb_users_last_year_2_orders>0)? intval($nb_users_last_year_3_orders/$nb_users_last_year_2_orders*100):0,
                            ($nb_users_last_year_total>0)? intval($nb_users_last_year_3_orders/$nb_users_last_year_total*100):0 )?>",
                        },
                        {
                            "name": "4+",
                            "y": <?php print intval($nb_users_last_year_4_orders); ?>,
                            "yWithDelimiter" : <?php print "'".number_format($nb_users_last_year_4_orders, 0, ',', ' ')."'" ?>,
                            "tooltip":"<?php print sprintf( _('%d %% des comptes '.(($period)? 'créé durant l\'année précedente de la période sélectionnée et ': '').'ayant commandé une fois ont recommencé au moins une quatrième fois <br>(soit %d %% de tout les clients)'), 
                            ($nb_users_last_year_3_orders>0)? intval($nb_users_last_year_4_orders/$nb_users_last_year_3_orders*100):0,
                            ($nb_users_last_year_total>0)? intval($nb_users_last_year_4_orders/$nb_users_last_year_total*100):0 )?>",
                        },
                        {
                            "name": "5+",
                            "y": <?php print intval($nb_users_last_year_5_orders); ?>,
                            "yWithDelimiter" : <?php print "'".number_format($nb_users_last_year_5_orders, 0, ',', ' ')."'" ?>,
                            "tooltip":"<?php print sprintf( _('%d %% des comptes '.(($period)? 'créé durant l\'année précedente de la période sélectionnée et ': '').'ayant commandé une fois ont recommencé au moins une cinquième fois <br>(soit %d %% de tout les clients)'), 
                            ($nb_users_last_year_4_orders>0)? intval($nb_users_last_year_5_orders/$nb_users_last_year_4_orders*100):0,
                            ($nb_users_last_year_total>0)? intval($nb_users_last_year_5_orders/$nb_users_last_year_total*100):0 )?>",
                        }
                        
                    ]
                },
                {
                    "name": "<?php print _('Clients'); ?>",
                    "colorByPoint": false,
                    "data": [

                        {
                            "name": "0+",
                            "y": <?php print intval($nb_users_total); ?>,
                            "yWithDelimiter" : <?php print "'".number_format($nb_users_total, 0, ',', ' ')."'" ?>,
                            "tooltip":"<?php  print ($period)? _('Nombre de nouveaux comptes durant la période sélectionnée'): _('Nombre de clients total')?>",
                        },
                        {
                            "name": "1+",
                            "y": <?php print intval($nb_users_1_orders); ?>,
                            "yWithDelimiter" : <?php print "'".number_format($nb_users_1_orders, 0, ',', ' ')."'" ?>,
                            "tooltip":"<?php print sprintf( _('%d %% des comptes '.(($period)? 'créé durant la période sélectionnée ': '').'ont réellement passé une commande'), ($nb_users_total>0)? intval($nb_users_1_orders/$nb_users_total*100): 0) ?>",
                        },
                        {
                            "name": "2+",
                            "y": <?php print intval($nb_users_2_orders); ?>,
                            "yWithDelimiter" : <?php print "'".number_format($nb_users_2_orders, 0, ',', ' ')."'" ?>,
                            "tooltip":"<?php print sprintf( _('%d %% des comptes '.(($period)? 'créé durant la période sélectionnée et ': '').'ayant commandé une fois ont recommencé au moins une seconde fois <br>(soit %d %% de tout les clients)'), 
                            ($nb_users_1_orders>0)? intval($nb_users_2_orders/$nb_users_1_orders*100):0,
                            ($nb_users_total>0)? intval($nb_users_2_orders/$nb_users_total*100):0 )?>",
                        },
                        {
                            "name": "3+",
                            "y": <?php print intval($nb_users_3_orders); ?>,
                            "yWithDelimiter" : <?php print "'".number_format($nb_users_3_orders, 0, ',', ' ')."'" ?>,
                            "tooltip":"<?php print sprintf( _('%d %% des comptes '.(($period)? 'créé durant la période sélectionnée et ': '').'ayant commandé une fois ont recommencé au moins une troisième fois <br>(soit %d %% de tout les clients)'), 
                            ($nb_users_2_orders>0)? intval($nb_users_3_orders/$nb_users_2_orders*100):0,
                            ($nb_users_total>0)? intval($nb_users_3_orders/$nb_users_total*100):0)?>",
                        },
                        {
                            "name": "4+",
                            "y": <?php print intval($nb_users_4_orders); ?>,
                            "yWithDelimiter" : <?php print "'".number_format($nb_users_4_orders, 0, ',', ' ')."'" ?>,
                            "tooltip":"<?php print sprintf( _('%d %% des comptes '.(($period)? 'créé durant la période sélectionnée et ': '').'ayant commandé une fois ont recommencé au moins une quatrième fois <br>(soit %d %% de tout les clients)'), 
                            ($nb_users_3_orders>0)? intval($nb_users_4_orders/$nb_users_3_orders*100):0,
                            ($nb_users_total>0)? intval($nb_users_4_orders/$nb_users_total*100):0)?>",
                        },
                        {
                            "name": "5+",
                            "y": <?php print intval($nb_users_5_orders); ?>,
                            "yWithDelimiter" : <?php print "'".number_format($nb_users_5_orders, 0, ',', ' ')."'" ?>,
                            "tooltip":"<?php print sprintf( _('%d %% des comptes '.(($period)? 'créé durant la période sélectionnée et ': '').'ayant commandé une fois ont recommencé au moins une cinquième fois <br>(soit %d %% de tout les clients)'), 
                            ($nb_users_4_orders>0)? intval($nb_users_5_orders/$nb_users_4_orders*100):0,
                            ($nb_users_total>0)? intval($nb_users_5_orders/$nb_users_total*100):0)?>",
                        }
                    ]
                }
            ],
		});
    });
//--></script>