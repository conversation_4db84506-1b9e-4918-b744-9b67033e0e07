<?php
	require_once('categories.inc.php');
	require_once('stats.inc.php');

	print '
		<div id="graph-report-time"></div>
	';
	// Récupération des statistiques
	$filtre = array("usr_seller_id" => 0);
	if (isset($_SESSION["ord_seller_id"])) {
		$filtre["usr_seller_id"] = $_SESSION["ord_seller_id"];
	}
	
	$report_group = stats_graphs_get_datas( 'report-time', $date1, $date2, $filtre, -1, $wst_id, false);
	
	$colors[1] = "#ff8172";
	$colors[2] = "#ffc862";
	$colors[3] = "#7883b7";
	$colors[4] = "#3398bf";
	$colors[5] = "#39b09f";

	$total_rpr = 0;

	if ($report_group && count($report_group) > 0) {
		foreach ($report_group as $value) {
			$total_rpr += $value["rpr_total"];
		}
	}else{
		$report_group = array();
	}

	?>

	<script>
		$(function () {
			$('#graph-report-time').highcharts({
				chart: {
					type: "pie",
					plotBorderWidth: 0,
					animation: false,
					events: {
						load: function (event) {
							var extremes = this.yAxis[0].getExtremes();
							if (extremes.dataMax == 0) {
								this.yAxis[0].setExtremes(0, 5);
							}
						}
					}
				},
				credits: {
					enabled: false
				},
				exporting: {
					filename: 'statistiques-pipeline'
				},
				title: {
					text: '',
					x: -20
				},
				legend: {
					layout: 'horizontal',
					align: 'center',
					verticalAlign: 'bottom',
					borderWidth: 0
				},
	            plotOptions: {
	                pie: {
	                    allowPointSelect: true,
	                    cursor: 'pointer',
	                    dataLabels: {
	                        enabled: true
	                    },
	                    showInLegend: true
	                }
	            },
				tooltip: {
					shared: true,
					crosshairs: true,
					valueDecimals: 1,
					followTouchMove: true,
					valueSuffix: ' %',
					style: {
						fontSize: "13px"
					},
				},
		        series: [{
		            name: "<?php print _('Rapports de visite') ?>",
		            colorByPoint: true,
		            dataLabels: {
		                formatter: function () {
		                    return Highcharts.numberFormat( this.point.y, 1 ) + ' %' ;
		                },
		                color: '#000',
		                style:{
                            fontSize: 20
                        }
		            },
		            data: [<?php 
			            foreach ($report_group as $val) {
			            	print '
			            	{
			            		name:\''.addslashes($val['group_name']).'\',
			            		y:'.round( $val['rpr_total'] / $total_rpr * 100, 1).',
			            		color: \''.$colors[$val['group_id']].'\'
				            },';
		            } ?>]
		            
		        }]
			});
		});
	</script>

	<?php if( $report_group && count($report_group)>0 ){ ?>
	<table class="checklist">
		<caption><?php print _('Rapports de visite')?></caption>
		<colgroup>
			<col width="250"/><col width="250"/><col width="250"/>
		</colgroup>
		<thead>
			<th><?php print _('Règles')?></th>
			<th><?php print _('Nombres de rapports')?></th>
			<th><?php print _('Temps de visite')?></th>
		</thead>
		<tbody>
			<?php foreach( $report_group as $rp ){ ?>
				<tr>
					<td><?php print htmlspecialchars( $rp["group_name"] ); ?></td>
					<td align="center"><?php print $rp["rpr_total"] ?></td>
					<td align="right"><?php print convert_second_to_readable_delay($rp["total_time"]) ?></td>
				</tr>
			<?php } ?>
		</tbody>
	</table>
	<?php } ?>


	

