<?php
	require_once('calls.inc.php');
	require_once('stats.inc.php');

	print '
		<div id="graph-calls-history"></div>
    ';
    
    $author = 0;
    if( isset($_GET['author']) && is_numeric($_GET['author']) && $_GET['author'] ){
        $author = $_GET['author'];
    }
	// Récupération des données
    $filtre = array("author" => $author);

    $date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	if( isset($_GET["date1"], $_GET["date2"]) ){
		$date1 = ria_mysql_escape_string( dateheureparse($_GET["date1"] ) );
        $date2 = ria_mysql_escape_string( dateheureparse( $_GET["date2"] ) );
	} 
    
	$calls_history = stats_graphs_get_datas( 'calls-history', $date1, $date2, $filtre);
	$all 	= array(); // Appels totaux
    $answered = array(); // Appels réussis
    if(max(array_values($calls_history)) > 0){
		if( $calls_history && sizeof($calls_history) > 0 ){
			foreach( $calls_history as $xVal => $yVals ){
				$all[] = isset($yVals["all"]) ? $yVals["all"] == "n" ? "null" : $yVals["all"] : 0 ;
	            $answered[] = isset($yVals["answered"]) ? $yVals["answered"] == "n" ? "null" : $yVals["answered"] : 0;
			}
		?>

		<script><!--
			$(function () {
				$('#graph-calls-history').highcharts({
					chart: {
						type: "column",
						plotBorderWidth: 0,
						animation: false,
						events: {
							load: function (event) {
								var extremes = this.yAxis[0].getExtremes();
								if (extremes.dataMax == 0) {
									this.yAxis[0].setExtremes(0, 5);
								}
							}
						}
					},
					credits: {
						enabled: false
					},
					exporting: {
						filename: 'calls-history'
					},
					title: {
	                    text: "<?php 
	                            if (strtotime($date1) != strtotime($date2)) {
	                                printf(_('Evolution du nombre d\'appels du %s au %s'), dateformatfull(date("d/m/Y", strtotime($date1))), dateformatfull(date("d/m/Y", strtotime($date2))));
	                            } else {
	                                printf(_('Evolution du nombre d\'appels le %s'), dateformatfull(date("d/m/Y", strtotime($date1))));
	                            }
	                        ?>"
	                },
					xAxis: {
						categories: [<?php print '\''.implode('\', \'', array_keys($calls_history)).'\''; ?>],
					},
					yAxis: {
						allowDecimals: false,
						title: {
							text: ''
						},
			            stackLabels: {
			                enabled: true,
			                style: {
			                    fontWeight: 'bold',
			                    color: (Highcharts.theme && Highcharts.theme.textColor) || 'gray'
			                },
						    formatter: function() {
						    	if (this.total > 0) {
							        return Highcharts.numberFormat(this.total, 0, ',');
						    	}
						    	return '';
						    }
			            }
					},
					legend: {
						layout: 'horizontal',
						align: 'center',
						verticalAlign: 'top',
						borderWidth: 0
					},
					plotOptions: {
						column: {
							pointPadding: 0.2,
							borderWidth: 0
						}
					},
					tooltip: {
						shared: true,
						crosshairs: true,
						valueDecimals: 0,
						followTouchMove: true,
						style: {
							fontSize: "13px"
						},
						formatter: function() {
							var str = '<span style="font-size: 12px;">'+ this.x +'</span>';

						$.each(this.points, function(i, point) {
							str += '<br/><span>'+ point.series.name +'</span>: <b>'+
							number_format( point.y, 0, ',', ' ' )+'</b>';
						});

							return str;
						}
					},
					series: [{
						type: 'column',
						name: "<?php print _('Nombre total d\'appels')?>",
						data: [<?php print implode(',', array_values($all)); ?>]
					},{
						type: 'column',
						name: "<?php print _('Nombre d\'appels réussis')?>",
						data: [<?php print implode(',', array_values($answered)); ?>],
							color: '#33cc33'
					}]
				});
			});
		//--></script>

<?php 
		}
	}else{
		echo '<div class="notice">' . _("Aucun rapport d'appel n'a été enregistré selon les critères définis.") . '</div>';
	}
 ?>