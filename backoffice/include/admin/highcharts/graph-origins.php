<?php

	require_once('categories.inc.php');
	require_once('stats.inc.php');

	// Paramètres de recherche.
	$filters = array();

	if( isset($_GET['prd']) ){
		$filters['prd'] = $_GET['prd'];
	}elseif( isset($_GET['cat']) ){
		$filters['cat'] = $_GET['cat'];
	}

	// Récupération des statistiques.
	$origins = stats_graphs_get_datas('origins', $date1, $date2, $filters, -1);

	$data = '';

	foreach( $origins as $key=>$val ){
		if( trim($data) ){
			$data .= ', ';
		}

		$data .= '[\''.$key.'\', '.$val['count'].']';
	}
?>
<script>
	$(function(){
		$('#graph-origins').highcharts({
			chart: {
				plotBackgroundColor: null,
				plotBorderWidth: null,
				plotShadow: false
			},
			credits: {
				enabled: false
			},
			exporting: {
				filename: 'origines-des-commandes'
			},
			title: {
				text: ''
			},
			tooltip: {
				pointFormat: '<b>{point.percentage:.1f}%</b>'
			},
			plotOptions: {
				pie: {
					allowPointSelect: true,
					cursor: 'pointer',
					dataLabels: {
						enabled: true,
						color: '#000000',
						connectorColor: '#000000',
						format: '<b>{point.name}</b>: {point.percentage:.1f} %'
					}
				}
			},
			series: [
				{
					type: 'pie',
					name: '<?php print _('Origine des ventes'); ?>',
					data: [
						<?php print $data; ?>
					]
				}
			]
		});
	});
</script>

<?php
	// Affichage des statistiques sous forme de tableau de données.
	$total_count = $total_ht = 0;

	$tfoot = array(
		'count' => 0,
		'total_ht' => 0,
		'total_ttc' => 0,
	);

	foreach( $origins as $k => $v ){
		$total_count += $v['count'];
		$total_ht += $v['total_ht'];

		$origins[$k]['cart_average_ht'] = $v['total_ht'] / $v['count'];
		$origins[$k]['cart_average_ttc'] = $v['total_ttc'] / $v['count'];

		$tfoot['count'] += $v['count'];
		$tfoot['total_ht'] += $v['total_ht'];
		$tfoot['total_ttc'] += $v['total_ttc'];
	}

	// Le tableau n'apparaît pas si il n'y a pas de données.
	if( !$total_count ){
		return;
	}

	$tfoot['cart_average_ht'] = $tfoot['total_ht'] / $tfoot['count'];
	$tfoot['cart_average_ttc'] = $tfoot['total_ttc'] / $tfoot['count'];
?>
<h3><?php  print _('Provenances des ventes') ;?></h3>
<div id="graph-origins"></div>
<table class="checklist ctr-stats" style="margin: 20px auto auto;">
	<caption><?php  print _('Origine des commandes') ;?></caption>
	<thead class="thead-none">
		<tr>
			<th><?php  print _('Source de trafic') ;?></th>
			<th class="align-right"><abbr title="<?php  print _('Nombre') ;?>"><?php  print _('Nbr');?></abbr> <?php  print _('de commandes') ;?></th>
			<th class="align-right"><abbr title="<?php  print _('Pourcentage') ;?>">%</abbr> <?php  print _('des commandes') ;?></th>
			<th class="align-right"><?php  print _('Chiffre d\'affaires') ;?> <abbr title="<?php  print _('Hors Taxes');?>"><?php  print _('HT');?></abbr></th>
			<th class="align-right"><?php  print _('Chiffre d\'affaires') ;?> <abbr title="<?php  print _('Toutes Taxes Comprises');?>Toutes Taxes Comprises"><?php  print _('TTC');?></abbr></th>
			<th class="align-right"><abbr title="<?php  print _('Pourcentage') ;?>">%</abbr> <?php  print _('du Chiffre d\'affaires');?></th>
			<th class="align-right"><?php  print _('Panier moyen') ;?> <abbr title="<?php  print _('Hors Taxes');?>"><?php  print _('HT');?></abbr></th>
			<th class="align-right"><?php  print _('Panier moyen') ;?> <abbr title="<?php  print _('Toutes Taxes Comprises');?>"><?php  print _('TTC');?></abbr></th>
		</tr>
	</thead>
	<tbody>
		<?php foreach( $origins as $k => $v ){ ?>
			<tr>
				<td data-label="<?php print _('Source de trafic :'); ?> "><?php print htmlspecialchars($k); ?></td>
				<td class="right" data-label="<?php print _('Nombre de commandes :'); ?> "><?php print ria_number_format($v['count']); ?></td>
				<td class="right" data-label="<?php print _('% des commandes :'); ?> "><?php print ria_number_format($v['count'] / $total_count, NumberFormatter::PERCENT, 2); ?></td>
				<td class="right" data-label="<?php print _('Chiffre d\'affaires HT :'); ?> "><?php print ria_number_format($v['total_ht'], NumberFormatter::CURRENCY, 2); ?></td>
				<td class="right" data-label="<?php print _('Chiffre d\'affaires TTC :'); ?> "><?php print ria_number_format($v['total_ttc'], NumberFormatter::CURRENCY, 2); ?></td>
				<td class="right" data-label="<?php print _('% du Chiffre d\'affaires :'); ?> "><?php print ria_number_format($v['total_ht'] / $total_ht, NumberFormatter::PERCENT, 2); ?></td>
				<td class="right <?php print $v['cart_average_ht'] > $tfoot['cart_average_ht'] ? 'positive' : ''; ?>" data-label="<?php print _('Panier moyen HT :'); ?> "><?php print ria_number_format($v['cart_average_ht'], NumberFormatter::CURRENCY, 2); ?></td>
				<td class="right <?php print $v['cart_average_ttc'] > $tfoot['cart_average_ttc'] ? 'positive' : ''; ?>" data-label="<?php print _('Panier moyen TTC :'); ?> "><?php print ria_number_format($v['cart_average_ttc'], NumberFormatter::CURRENCY, 2); ?></td>
			</tr>
		<?php } ?>
	</tbody>
	<tfoot>
		<tr class="bold">
			<td><?php  print _('Total') ;?></td>
			<td class="right"><?php print ria_number_format($tfoot['count']); ?></td>
			<td></td>
			<td class="right"><?php print ria_number_format($tfoot['total_ht'], NumberFormatter::CURRENCY, 2); ?></td>
			<td class="right"><?php print ria_number_format($tfoot['total_ttc'], NumberFormatter::CURRENCY, 2); ?></td>
			<td></td>
			<td class="right"><?php print ria_number_format($tfoot['cart_average_ht'], NumberFormatter::CURRENCY, 2); ?></td>
			<td class="right"><?php print ria_number_format($tfoot['cart_average_ttc'], NumberFormatter::CURRENCY, 2); ?></td>
		</tr>
	</tfoot>
</table>