	<div id="app-flow" class="app-flow" :class="{ 'app-flow--opened': isOpen }" v-if="isOpen">
		<button class="btn btn-close" @click="toggle"></button>
		<div class="app-flow-wrapper">
			<div class="app-flow-header">
				<h2><?php print _('Flux d\'activité'); ?></h2>
				<button class="btn btn-filter" @click="toggle_filters" :class="{ 'actived': filterIsActived }">
					<span class="icon-filter"></span>
				</button>
			</div>

			<section class="app-flow-content" @scroll="scrollHandler">
				<div>
					<FlowItem v-for="item in items" :key="item.id" :item="item"></FlowItem>
				</div>

				<div v-if="isFetching">
					<Loader></Loader>
				</div>

				<!-- Message d'indication s'il n'y a aucun retour dans le flux (vierge)-->
				<div v-if="noData">
					<div class="app-flow-empty">
						<img src="/src/icons/flow/onboarding.svg" width="34" height="34" alt="Icône" />

						<h3><?php print _('C\'est plutôt calme par ici...'); ?></h3>

						<p><?php print _('Il n\'y a pas encore d\'activité dans votre flux. Pour y remédier, vous pourriez :'); ?></p>

						<a href="/admin/customers/new.php?prf=" class="create"><?php print _('Ajouter un compte'); ?></a>
						<a href="/admin/fdv/reports/edit.php" class="create"><?php print _('Rédiger un rapport'); ?></a>
						<a href="/admin/orders/order.php?ord=new" class="create"><?php print _('Créer un devis'); ?></a>

						<!--p><?php //printf(_('Ou appuyer sur la touche %s de votre clavier pour découvrir plus d\'éléments à créer dans RiaShop.'), '<span class="app-shortcut">c</span>'); ?></p-->
					</div>
				</div>

				<!-- Message d'indication de la fin du flux-->
				<div class="app-flow-end" v-if="thatsAll">
					<img alt="👀" width="32" height="32" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAABmUExURUdwTJSUk4uLi39/f4+PjoiIiJqammxtb4SEg6Kiont7e4hqSmJKO////ycQAPn5+fHx8ejn59XV1F0rAeDe3MzLyrm5uLGurMLAv2w/CEQhAqZ1BsyZAIlXB3RLLJp+YaqWhem6AAuZMzoAAAANdFJOUwDUnDK4e+oNVPwe/f74vUwMAAAD9ElEQVRYw+2Xx3rzKhCGo94igySKkKzi+7/JfwDJEs3JWZ1N8JNFnDffFGCY+fr6W3/rWkVR/Ir64e9lnEZpnNU/Q+kHqMiiijPGeBWFqaLUEKvyOAAlacVoLxdlPE38UB1XbOx7AtDIo9KHlDnIdB3GuOsIZbkXSiJOe3JBmVenJxihFj4Id71XKYkYJVhSGhorR6mOWN8pom1PJSe6OpU6mgGDUsk2V6RKpz2XUorsZMacGhACJQvKqsuUXrijPLb2qxodqDehOrcR8ImMZnAFRO+BqtLwue+QybTgNzOsZRCYA2HIwHXKE9chZY3eXSoiH4Q6enMp8zikXLolADIEDrUOBC6lxSdbMpX3LMG++iFw6YT8tqRL9DpwZR6Cep5dqcZe5u62Ct8Dgd9nuutAZKAE6a4/h39PdygyHVt5njRKQtAZW9BpFVv29ZM1mQDFwDXDbYAhB/OVMX8e9eaqCyejR6jft23nvXUDEDAqkUXMACJ8a7Z97JwEqFOSwL722zANA/xs1GHqI9ddp6FhWDnyZbLMezaIbVUMSHmzDbmm67pvQlFDgz3ZzqpxXQkijWam3WS0UAIQ/DfaJQJq610J69sd82YY4dfq8VCeT8zYEb21SdUIAl+Mk6RAaTe3LS4gjfuwyvTtj4eWWjtXqNyHRiaGT5KSStS+AEUKMfG27b8fSkkYweGjAJTbtIJHnYYeQtxzKQ+JFII0f+/VgUhrzZ05hMBaM7JG2ZLmhMD3g3QITY9rgdAVG3oLCQN6LEJQR8hkpkXIIOzQhgM6ULEI7oRmG1suoSvZgwmJeeF2tYktj+blHtopZFubl9He/szK0bw0yD2Q38NwF5rnhRhC0tgqxB15LpvnijTDHRLP2bCm3E4asVzWxOt5Of2+2HBph5u56fmauWOtiNblrSRer3kzS42uxymYE5fOs+lca+kuIGQxTZOYXyYiT8jZHIhlnt/QTN3CBqV2W+anXJJoen+p5bvQ1EtSzFdqoRyDkoLmeSNmOeLl+apTvh725oa2ngqhqh/dG9DadmpVWuM5Ivu2LACNKPDUxLKwd4QQ+w3A/fh+IOUrCukgpHMeiHNDPjzZ12schvDVagSeWvSfm4jAE2l1Wr4+y+oPQo2W0UP52kOn0dKtn8eh1Gn92o+tn88l2flVidmKj24zajqkrRkQcttjt4dGyuviU8Pe+hr2wtuwW/NBkt9GCI24w8gxZ9x1qtI31EgltbAXuczJwScw1JxjFpYrNEEBJMcsNYspqMoCs9gIwxgh4ZlOTYcXlJeh6TDnbBxHxoNTpp5XTygJTrU1zL0pDMfJp+H4VxA8T/Uv5vVfQX/rb/2P6x9ihanXUWg+GAAAAABJRU5ErkJggg==" />
					<p><?php print _('Vous avez tout vu !'); ?></p>
				</div>

				<!-- Message d'indication d'erreur -->
				<div class="app-flow-error" v-if="hasError">
					<img alt="🤔" width="32" height="32" src="data:image/png;base64,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" />
					<h3><?php print _('Quelque chose s\'est mal passé'); ?></h3>
					<p><?php print _('Une erreur s\'est produite au chargement du flux d\'activité.'); ?></p>
					<button class="create" @click="retryLoad"><?php print _('Réessayer'); ?></button>
				</div>
			</section>
		</div>
	</div>

	<!-- Affichage des filtres du flow -->
	<div id="app-flow-filters" class="app-flow-filters" :class="{ 'app-flow-filters--opened': isOpen }">
		<div id="flow-filters-box">
			<button class="btn btn-close" @click="close"></button>
			<div class="flow-filters-header">
				<h2><?php print _('Filtrer le flux d\'activité'); ?></h2>
			</div>
			<!-- Champ de recherche d'un client -->
			<form class="flow-searchbar">
				<span class="flow-searchbar-icon"></span>
				<input type="text" name="flow-searchbar-input" class="flow-searchbar-input" placeholder="Nom du client">
				<input type="hidden" name="flow-searchbar-input-id" class="flow-searchbar-input-id" value="" />
				<span class="reset" :class="{ 'active': userSelected }">X</span>
			</form>
			<!-- Filtre type selecteurs -->

			<!-- Filtre type d'activité -->
			<div id="flow-selector-activity" class="flow-filter-selector riapicker" style="width: 100%;">
				<div class="selectorview">
					<div class="left">
						<span class="function_name"><?php print _("Type d'activités") ?></span>
						<br/><span class="view"><?php print  _("Toutes les activités") ?></span>
					</div>
					<a class="btn" name="btn">
						<img src="/admin/images/stats/fleche.gif" height="8" width="16" alt="" />
					</a>
					<div class="clear"></div>
				</div>
				<div class="flowselector"><?php
					// IMPORTANT : L'ajout d'une nouvelle activé doit faire l'objet d'un ajout dans /js/core/api.js
					// Rechercher "flow-selector-activity" pour trouver où sont envoyés les choix via l'API
					print '<a name="a-0">'._("Toutes les activités").'</a>'
					.'<span name="a-reports">'
						.'<input type="checkbox" name="origin[]" id="a-reports" value="reports" /> '
						.'<label for="a-reports">'._("Rapports de visite").'</label>'
					.'</span>'
					.'<div>'
						.'<span class="parent" name="a-all-call">'
							.'<input type="checkbox" name="origin[]" id="a-all-call" value="all-call" /> '
							.'<label for="a-all-call">'._("Appels").'</label>'
						.'</span>'
						.'<span class="child" name="a-callin">'
							.'<input type="checkbox" name="origin[]" id="a-callin" value="callin" /> '
							.'<label for="a-callin">'._("Appels entrants").'</label>'
						.'</span>'
						.'<span class="child" name="a-callout">'
							.'<input type="checkbox" name="origin[]" id="a-callout" value="callout" /> '
							.'<label for="a-callout">'._("Appels sortants").'</label>'
						.'</span>'
						.'<span class="child" name="a-missed-call">'
							.'<input type="checkbox" name="origin[]" id="a-missed-call" value="missed-call" /> '
							.'<label for="a-missed-call">'._("Appels manqués").'</label>'
						.'</span>'
					.'</div>'
					.'<span name="a-cart">'
						.'<input type="checkbox" name="origin[]" id="a-cart" value="cart" /> '
						.'<label for="a-cart">'._("Panier").'</label>'
					.'</span>'
					.'<span name="a-devis">'
						.'<input type="checkbox" name="origin[]" id="a-devis" value="devis" /> '
						.'<label for="a-devis">'._("Devis").'</label>'
					.'</span>'
					.'<span name="a-order">'
						.'<input type="checkbox" name="origin[]" id="a-order" value="order" /> '
						.'<label for="a-order">'._("Commandes").'</label>'
					.'</span>'
					.'<span name="a-bl">'
						.'<input type="checkbox" name="origin[]" id="a-bl" value="bl" /> '
						.'<label for="a-bl">'._("Bons de livraison").'</label>'
					.'</span>'
					.'<div>'
						.'<span class="parent" name="a-all-notif">'
							.'<input type="checkbox" name="origin[]" id="a-all-notif" value="all-notif" /> '
							.'<label for="a-all-notif">'._("Notifications").'</label>'
						.'</span>'
						.'<span class="child" name="a-notif-addcpt">'
							.'<input type="checkbox" name="origin[]" id="a-notif-addcpt" value="notif-addcpt" /> '
							.'<label for="a-notif-addcpt">'._("Ajout d'un contact").'</label>'
						.'</span>'
						.'<span class="child" name="a-notif-updcpt">'
							.'<input type="checkbox" name="origin[]" id="a-notif-updcpt" value="notif-updcpt" /> '
							.'<label for="a-notif-updcpt">'._("Mise à jour d'un contact").'</label>'
						.'</span>'
					.'</div>';
				?></div>
			</div>

			<!-- Filtre période -->
			<div id="flow-selector-dates" class="flow-filter-selector riapicker" style="width: 100%;">
				<div class="riadatepicker"></div>
			</div>

			<!-- Filtre représentant -->
			<div class="flow-filter-selector riapicker" style="width: 100%;">
				<?php print view_sellers_selector( false, false, 'flowselector', 'flow-selector-seller' ); ?>
			</div>

			<div class="flow-filters-footer">
				<button class="btn" @click="reinit"  :class="{ 'hide': !canReinit }"><?php print _('Réinitialiser'); ?></button>
				<button class="btn" @click="cancel"><?php print _('Annuler'); ?></button>
				<input type="button" name="flow-filters-apply" class="btn btn-main disabled" @click="apply" value="<?php print _('Appliquer'); ?>"></input>
			</div>
		</div>
	</div>


	<script>
		<?php
			ob_start();
			view_date_initialized();
			$init = ob_get_clean();

			print str_replace( array('websites','var ','flow_flow_websites'), array('flow_websites','var flow_','flow_websites'), $init );
		?>
		function flow_select_user( id, email, name ){
			$('[name="flow-searchbar-input"]').val( name + '<' + email + '>' );
			$('[name="flow-searchbar-input-id"]').val( id );
			$('.flow-searchbar .reset').addClass('active');
			hidePopup( false );
		}
	</script>