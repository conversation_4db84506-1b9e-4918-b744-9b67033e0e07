<?php
/** Permet de charger les templates vuejs
 *
 */

namespace App;

use Exception;

final class Templates {

	// Objet
	private static $instance = null;

	// Tableau des fichiers existants
	private $files = [];

	// Path par défaut
	private $path = '';

	private function __construct(){

		$this->path = dirname(__FILE__).'/';

	}

	/** Singleton permet d'instancier la classe
	 *
	 * @return void
	 */
	public static function getInstance(){

		if( !self::$instance ){
			self::$instance = new Templates;
		}

		return self::$instance;

	}

	/** Permet d'ajouter un template
	 *
	 * @param	string	$filename	Nom du fichier (ne pas inclure le path ni .vue.php)
	 * @return	object	L'objet en cours, une exception sera levée en cas d'erreur
	 */
	public function addQueue($filename){

		if( !is_string($filename) || !trim($filename)){
			throw new Exception('Le nom du fichier n\'est pas valide.');
		}
		$filename = trim($filename);

		if( !is_file($this->path.$filename.'.vue.php') ){
			throw new Exception('Le nom du fichier ne correspond à aucun fichier.');

		}

		if( array_key_exists($filename, $this->files) ){
			return $this;
		}
		$this->files[$filename] = [
			'name'		=> $filename,
			'type'		=> 'php',
			'filename'	=> $filename.'.vue.php'
		];

		return $this;

	}

	/** Cette méthode permet de charger les fichiers
	 *
	 * @return void	Charge les fichiers
	 */
	public function load(){

		if( !count($this->files) ){
			return;
		}

		foreach($this->files as $file){
			require_once $this->path.$file['filename'];
		}

	}

 }