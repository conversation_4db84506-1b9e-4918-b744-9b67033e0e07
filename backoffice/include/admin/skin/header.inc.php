<?php
	require_once 'view/admin/header.inc.php';
	require_once 'view/admin/Notices.inc.php';
	require_once 'hotjar.inc.php';

	// Initialise une variable pour dire que l'abonnement Yuto (essai) se termine dans 5 jours au plus tard
	$yuto_end_5days = false;

	// Initialise une variable pour dire que l'abonnement RiaShop (essai) se termine dans 5 jours au plus tard
	$riashop_end_5days = false;

	// Initialise la constante IS_HTTP_ERROR pour simplifier le code qui suit
	if( !defined('IS_HTTP_ERROR') ){
		define( 'IS_HTTP_ERROR', false );
	}
	if( !defined('ADMIN_HEAD_POPUP') ){
		define( 'ADMIN_HEAD_POPUP', false );
	}
	if( !defined('ADMIN_HEAD_LOGIN') ){
		define( 'ADMIN_HEAD_LOGIN', false );
	}

	// Contrôle qu'un administrateur est connecté sauf sur la page de connexion
	if( !ADMIN_HEAD_LOGIN ){
		require_once('users.inc.php');

		if( getenv('oneriashop') !== false ){
			if(!isset($admin_account) || !$admin_account->isConnected() ){
				header('Location: /admin/login.php');
				exit;
			}
		}else{
			if (!gu_users_is_connected(false)) {
				header('Location: /admin/login.php');
				exit;
			}
		}
	}

	//Redirige vers la page d'accueil si l'utilisateur tente d'accéder à un module auquel il n'a pas accès
	if( !IS_HTTP_ERROR ){
		gu_rights_admin_accessibility();
	}
	$start_time_page = microtime( true );
	$adminNotices = AdminNotices::getInstance();
	$yuto_end_5days = $adminNotices->getYutoEnd();
	$riashop_end_5days = $adminNotices->getRiashopEnd();

	$attr_body = '';
	if( defined('ADMIN_ID_BODY') ){
		$attr_body .= ' id="'.ADMIN_ID_BODY.'"';
	}

	if( defined('ADMIN_CLASS_BODY') ){
		$attr_body .= ' class="'.ADMIN_CLASS_BODY.'"';
	}

	// Détermine s'il s'agit d'une formule avec abonnement ou sans abonnement
	// Par défaut à null car il ne s'agit pas d'un compte avec gestion d'abo
	// $trial = $expired = $abo_yuto = null;

	// // S'il existe une gestion d'abonnement
	// if( gu_user_is_authorized('_RGH_ADMIN_OPTION_SUBSCRIPTION') ){
	// 	$abo_yuto = dev_subscribtions_yuto_get( false, false );

	// 	if( !is_array($abo_yuto) || !count($abo_yuto) ){
	// 		$expired = true;
	// 	}else{
	// 		// Si oui ou non il existe un abonnement payant
	// 		$trial = isset($abo_yuto['in_testing']) && $abo_yuto['in_testing'] ? true : false;
	// 	}
	// }

	// Défini l'attribut lang de la page
	if( isset($_SESSION['lang']) && trim($_SESSION['lang'])!='' ){
		$lang = str_replace('_', '-', htmlspecialchars($_SESSION['lang']));
	}else{
		$lang = 'fr-FR';
	}

?>
<!doctype html>
<html lang="<?php print $lang; ?>">
	<head>
		<title><?php print (defined('ADMIN_PAGE_TITLE') ? ADMIN_PAGE_TITLE . ' - ' : '') . 'RiaShop - ' . _('Interface d\'administration'); ?></title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;800&family=Rubik&display=swap" rel="stylesheet">
		<?php include_once 'header.comp.php'; ?>
		<?php if( !ADMIN_HEAD_LOGIN ){ ?>
		<link href="/src/app.css?<?php print ADMIN_ASSET; ?>" rel="stylesheet">
        <style>
			.my-account-dropdown > a.my-account-maintenance {
				background: transparent url("/src/icons/header/websites/maintenance.svg") 16px center no-repeat !important;
			}
		</style>
		<?php } ?>
	</head>
	<body <?php print $attr_body; ?>>

	<?php if( !ADMIN_HEAD_POPUP && !ADMIN_HEAD_LOGIN ){ ?>
		<!-- header -->
		<div class="app-header">
			<!-- menu -->
			<button class="app-header-burger btn"></button>
			<!-- logo -->
			<a href="<?php print riashop_is_home() ? '#' : '/';?>" class="app-branding <?php print riashop_is_home() ? 'nuxt-link-exact-active' : 'nuxt-link-active'; ?>">
				<div title="RiaShop" class="app-logo app-logo-riashop btn"></div>
				<div title="Yuto" class="app-logo app-logo-yuto btn"></div>
				<div class="app-title">
					<h1 >RiaShop &amp; Yuto</h1>
					<span class="baseline"><?php print _('Powerful solutions for successful stories');?></span>
				</div>
			</a>

			<?php
				// search bar
				print view_admin_searchbar();

				// my account
				print view_admin_header_account();

				// flow
				print '<button id="app-flow-btn" title="'._('Flux d\'activité').'"
					class="icon-header-flux app-flow-toggle-btn btn" :class="{ \'app-flow-is-open\': isOpen }" @click="toggle"></button>
				';

				// add popup
				print '<button title="'._('Ajouter').'" class="app-add-btn btn">+</button>';
			?>
		</div>
		<!-- sidebar -->
		<?php print view_admin_sidebar(); ?>

		<!-- start content -->
		<div class="app-content">
			<?php
				// Affichage d'un avertissement dans le cas où l'on peut accéder à plusieurs instances et que l'on en a choisi une
				print $adminNotices->renderAlertAccess();
				print $adminNotices->renderAlertYutoSubscription();
				print $adminNotices->renderAlertRiashopSubscription();
				print $adminNotices->renderQuickstart();
			?>
			<nav aria-label="breadcrumb" class="breadcrumbs">
				<?php
				if( Breadcrumbs::count() ){
					print Breadcrumbs::toString();
				}else{
					view_admin_location();
				}
				?>
			</nav>
			<!-- start content holder -->
			<div id="site-content" class="app-content-holder">

	<?php } ?>