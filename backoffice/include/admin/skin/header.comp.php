	<?php if( isset($config) ){?>
		<script>
			window.ui_config = {
				img_url: "<?php echo ria_array_get($config, 'img_url', '')?>"
			}
		</script>
	<?php }

		if( getenv('oneriashop') !== false ){
			print '<link rel="icon" href="/images/favicon.ico" />';
		}

		$filename = '';
		if( isset($_SERVER['SCRIPT_URL']) && trim($_SERVER['SCRIPT_URL']) != '' ){
			$filename = $_SERVER['SCRIPT_URL'];
		}elseif( isset($_SERVER['SCRIPT_FILENAME']) && trim($_SERVER['SCRIPT_FILENAME']) != '' ){
			$filename = str_replace( $config['site_dir'].'/admin', '', $_SERVER['SCRIPT_FILENAME'] );
		}

		$file_admin = trim($filename) != '' ? str_replace( '/admin', '', $filename ) : 'none';

		// Déclaration de la variable $file_css
		$file_css = array();
		$popup_ria = 'popup_ria_v1.css';

		// Gestion des fichiers CSS spécifique à l'environnement de maquettes
		// Si on n'est pas sur la page de Login
		// Il est possible de forcer l'environnement de production en passant en paramètre "forced_css_prod"
		if( !ADMIN_HEAD_LOGIN ) {
			$file_css[] = 'layout-v1.css';
		}

		// Importe les fichiers gérant le datepicker et les tris de tableaux
		if( !IS_HTTP_ERROR ){
			switch( $file_admin ){
				case '/':
					$file_css = array_merge( $file_css, array('ria-sortable.css', 'jquery.datepicker.css', 'riadatepicker.css') );
					break;
				case '/popup-select-tenant.php':
				case '/customers/popup-export-customers.php':
				case '/documents/images/import/index.php':
				case '/tools/imports/index.php':
				case '/sync/index.php':
				case '/sync/':
				case '/search/index.php':
					break;
				case '/orders/order.php':
					array_push( $file_css, 'ria-sortable.css' );
					break;
				case '/orders/order-beta.php':
					array_push( $file_css, 'datatables.min.css' );
					break;
				case '/orders/models.php': // Modèles de commandes
					array_push( $file_css, 'ria-sortable.css' );
					break;
				case '/orders/orders.php': // Liste des commandes
					array_push( $file_css, 'jquery.datepicker.css' );
					array_push( $file_css, 'riadatepicker.css' );
					break;
				case '/orders/orders-beta.php': // Liste des commandes (Version 2 - En beta-test)
					array_push( $file_css, 'jquery.datepicker.css' );
					array_push( $file_css, 'riadatepicker.css' );
					array_push( $file_css, 'datatables.min.css' );
					break;
				case '/config/scripts/': // Gestion des scripts additionnels
				case '/config/scripts/index.php': // Gestion des scripts additionnels
					array_push( $file_css, 'codemirror.css' );
					break;
				default:
					$file_css = array_merge( $file_css, array('ria-sortable.css', 'jquery.datepicker.css', 'riadatepicker.css') );
					break;
			}
		}

		if( !ADMIN_HEAD_POPUP ){
			$file_css = array_merge_recursive( $file_css, array($popup_ria) );
		}

		if( ADMIN_HEAD_LOGIN ){
			$file_css = array('login-opti.min.css');
		}

		// CSS spécifiques aux erreurs 403 et 404
		if( IS_HTTP_ERROR ){
			array_push($file_css, 'errors.css');
		}

		switch( $file_admin ){
			case '/config/paiements/transfer.php' :
				array_push($file_css, 'paiements.css');
				break;
			case '/config/returns/index.php' :
				array_push($file_css, 'treeview.css');
				break;
			case '/comparators/categories.php' :
			case '/comparators/link-categories.php' :
				array_push($file_css, 'treeview.css', 'comparators.css');
				break;
			case '/tools/banners/edit.php' :
			case '/tools/news/edit.php' :
			case '/customers/segments/segment.php' :
			case '/config/fields/segments/segment.php' :
			case '/documents/edit.php' :
				array_push($file_css, 'segments.css');
				break;
			case '/documents/images/zones.php':
				array_push($file_css, $popup_ria, 'popup-img-zones.css');
				break;
			case '/documents/images/alt.php':
				array_push($file_css, $popup_ria);
				break;
			case '/promotions/specials/edit.php' :
				array_push($file_css, 'segments.css', 'promotions.css');
				break;
			case '/tools/cms/edit.php' :
				array_push($file_css, 'segments.css', 'jquery.fancybox-1.3.4.css', array('file' => 'skin.css', 'dir' => '/admin/js/skin/classic'));
				break;
			case '/documents/popup-add-document.php' :
				array_push($file_css, 'segments.css', 'order-create.css', 'popup-img.css');
				break;
			case '/tools/glossary/edit.php' :
			case '/tools/glossary/index.php' :
				array_push($file_css, 'glossary.css');
				break;
			case '/tools/cms/popup-new-image.php' :
			case '/tools/cms/popup-revision.php' :
			case '/tools/cms/popup_image.php' :
				array_push($file_css, 'riawysiwyg.css');
				break;
			case '/orders/returns/return.php' :
			case '/orders/returns/update.php' :
				array_push($file_css, 'jquery.fancybox-1.3.4.css');
				break;
			case '/stats/prd-no-image.php' :
				array_push($file_css, 'jquery.fancybox-1.3.4.css');
				break;
			case '/stats/popup-image.php' :
				array_push($file_css, 'popup-stats-img.css');
				break;
			case '/tools/rewards/config/index.php' :
				array_push($file_css, 'rewards.css');
				break;
			case '/ajax/orders/ncmd-delivery-edit.php' :
			case '/ajax/orders/ncmd-customers-edit.php' :
			case '/ajax/orders/ncmd-customers-change.php' :
			case '/ajax/orders/ncmd-add-products.php' :
			case '/ajax/orders/ncmd-rights.php' :
				array_push($file_css, 'order-create.css');
				break;
			case '/ajax/media/img_popup.php' :
			case '/catalog/popup-image.php' :
				array_push($file_css, 'order-create.css', 'popup-img.css');
				break;
			case '/customers/ajax-usr-passwd.php' :
				array_push($file_css, 'order-create.css' );
				break;
			case '/comparators/search/index.php' :
			case '/comparators/stats/index.php' :
				array_push($file_css, 'comparators.css');
				break;
			case '/documents/images/import/index.php' :
				array_push($file_css, 'images.css');
				break;
			case '/stats/prd-conversion.php' :
				array_push($file_css, 'jquery.tooltip.css');
				break;
			case '/customers/edit.php' :
				array_push($file_css, 'jquery.fancybox-1.3.4.css', 'jquery.tooltip.css', 'contact.css');
				break;
			case '/customers/join-file.php' :
			case '/moderation/spam/index.php' :
			case '/moderation/moderation.php' :
			case '/catalog/product.php' :
				array_push($file_css, 'contact.css');
				break;
			case '/comparators/search/popup-multiexport.php' :
			case '/catalog/popup-export-products.php' :
			case '/catalog/popup-export-categories.php' :
			case '/fdv/reports/calls/popup-export-calls.php' :
			case '/catalog/popup-duplicate.php' :
			case '/catalog/popup-duplicate-parent-to-child.php' :
			case '/tools/exports/index.php' :
				array_push($file_css, 'export.css');
				break;
			case '/comparators/search/popup-save-search.php' :
			case '/comparators/popup-choose-family.php' :
				array_push($file_css, 'popup-ctr.css');
				break;
			case '/comparators/comparators.php' :
			case '/comparators/params.php' :
			case '/comparators/index.php' :
			case '/comparators/mapping-attributs.php' :
				array_push($file_css, 'comparators.css');
				break;
			case '/config/cycles/edit.php' :
				array_push($file_css, 'cycles.css', array('file' => 'skin.css', 'dir' => '/admin/js/skin/classic'));
				break;
			case '/fdv/devices/index.php' :
			case '/fdv/devices/edit.php' :
			case '/fdv/devices/devices-location.php' :
				array_push($file_css, 'fdv.css');
				break;
			case '/config/livraison/stores/edit.php' :
			case '/config/livraison/stores/popup-plage-horaire.php' :
			case '/fdv/reports/edit.php' :
				array_push($file_css, 'jquery-clockpicker.css');
				break;
			case '/config/livraison/zones/edit.php' :
				array_push($file_css, 'delivery.css');
				break;
			case '/config/cross_selling/cross-selling.php' :
				array_push($file_css, 'cross-selling.css');
				break;
			case '/tools/imports/':
			case '/tools/imports/index.php':
			case '/tools/imports/mapping.php':
			case '/sync/':
			case '/sync/index.php':
				array_push($file_css, 'imports-v1.css');
				break;
		}

		// Les utilisateurs de riastudio bénéficient de messages Flash
		if( isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id']==0 && $file_admin!='/login.php' && !ADMIN_HEAD_POPUP ){
			print '<link rel="stylesheet" type="text/css" href="/js/flash/flash.min.css?'.ADMIN_ASSET.'" />';
			print '<link rel="stylesheet" type="text/css" href="/css/font-awesome.min.css" media="screen">';
		}

		foreach( $file_css as $one_file ){
			if (!is_array($one_file)) {
				$one_file = array('file' => $one_file, 'dir' => '/admin/css');
			}

			print '<link rel="stylesheet" type="text/css" href="'.$one_file['dir'].'/'.$one_file['file'].'?'.ADMIN_ASSET.'" />';
		}
		if( !ADMIN_HEAD_LOGIN && ( !defined('ADMIN_NO_MOBILE_STYLE') || !ADMIN_NO_MOBILE_STYLE ) ){
			print '<link rel="stylesheet" type="text/css" media="screen and (max-width: 1023px)" href="/admin/css/mobile.css?'.ADMIN_ASSET.'" />';
		}

		// Import des fichiers JS nécessaires à la page en cours de consultation
		if( $file_admin=='/popup-select-tenant.php' ){
			$file_js = array( 'jquery.min.js' );
		}else{
			$file_js = array(
				'jquery.min.js',
				'jquery-ui-1.8.2.custom.min.js',
				'default.js',
				'jquery.datepicker.js'
			);
		}

		if( !IS_HTTP_ERROR ){
			switch( $file_admin ){
				// Les fichiers listés ici n'utilisent pas de datepicker, ni de tableau de données, ni d'onglet Médias pouvant être triées
				case '/':
				case '/popup-select-tenant.php':
				case '/customers/popup-export-customers.php':
				case '/documents/images/import/index.php':
				case '/search/index.php':
				case '/orders/orders.php': // Liste des commandes
					break;
				case '/orders/orders-beta.php': // Liste des commandes
				case '/orders/order-beta.php': // Page commande (Version 2 - En beta-test)
					array_push( $file_js, 'datatables/datatables.min.js');
					break;
				case '/orders/order.php':
					array_push( $file_js, 'ria-sortable.js');
					break;
				case '/catalog/move.php': // Déplacer un produit ou une catégorie
					array_push( $file_js, 'json.js', 'catalog/move.js' );
					break;
				case '/fdv/config/index.php': // Configuration Yuto
					break;
				case '/catalog/edit.php': // Catégorie
				case '/catalog/popup-edit-ctrmarket.php': // Popup Place de marché / comparateur de prix
				case '/catalog/product.php': // Produit
				case '/documents/edit.php': // Document
				case '/documents/types/edit.php': // Type de document
				case '/tools/cms/edit.php': // Page de contenu
				case '/tools/faq/category.php': // Catégorie
				case '/tools/faq/question.php': // Question
				case '/tools/news/edit.php': // Actualité
					array_push( $file_js, 'tab-medias.js' );
				default:
					array_push( $file_js, 'ria-sortable.js' );
					break;
			}
		}

		if( !ADMIN_HEAD_POPUP ){
			array_push($file_js, 'popup_ria.js');
		}

		if( ADMIN_HEAD_LOGIN ){
			$file_js = array('jquery.min.js');
		}

		if( !IS_HTTP_ERROR ){
			switch( $file_admin ){
				case '/':
					array_push($file_js, 'riadatepicker.js');
					break;
				case '/popup-select-tenant.php':
				case '/login.php':
				case '/orders/orders.php':
				case '/customers/segments/segment.php':
				case '/config/fields/segments/segment.php':
				case '/catalog/product.php':
				case '/documents/images/import/index.php':
				case '/orders/models.php':
				case '/search/index.php':
					break;
				case '/options/moderation.php' :
					array_push($file_js, 'options-tree.js');
					break;
				case '/customers/negotiations/edit.php' :
				case '/customers/negotiations/new.php' :
					array_push($file_js, 'customers/negotiations.js');
					break;
				case '/documents/images/zones.php':
					array_push($file_js, 'interact/interact.min.js', 'documents/images/zones.js');
					break;
				case '/tools/exports/index.php':
					array_push($file_js, 'tools/exports.js');
					break;
					break;
				case '/config/avis_verifie/index.php':
					array_push($file_js, 'config/avis-verifie.js');
					break;
				case '/config/extranet/index.php':
					array_push($file_js, 'config/extranet.js');
					break;
				default:
					array_push($file_js, 'riadatepicker.js');
					break;
			}
		}

		// Fichier de traductions. Il est utile partout hors popup de sélection de tenant pour RiaStudio
		if( !isset($no_translates) ){
			print '<script src="/admin/js/translates.php?lng='.( isset($_SESSION['lang']) ? $_SESSION['lang'] : '' ).'&'.ADMIN_ASSET.'"></script>';
		}

		foreach ($file_js as $one_file) {
			if (!is_array($one_file)) {
				$one_file = array('file' => $one_file, 'dir' => '/admin/js', 'extern' => false);
			}

			$dir_file_js = $one_file['dir'];
			if (trim($dir_file_js) != '') {
				$dir_file_js .= '/';
			}

			print '
				<script src="'.$dir_file_js.$one_file['file'].( $one_file['extern'] ? '' : '?'.ADMIN_ASSET).'"></script>
			';
		}
	?>

	<?php if( hotjar_include_tenant() && ( !defined('ADMIN_HEAD_POPUP') || !ADMIN_HEAD_POPUP ) ){ ?>
		<script>
			(function(h,o,t,j,a,r){
				h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
				h._hjSettings={hjid:1245476,hjsv:6};
				a=o.getElementsByTagName('head')[0];
				r=o.createElement('script');r.async=1;
				r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
				a.appendChild(r);
			})(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
		</script>
	<?php } ?>
