<?php

/** Ce module comprend les fonctions nécessaire à la gestion du menu admin
 *
 */

 // \cond onlyria
/** Retourne l'ensemble des élément du menu administrateur, filtrés en fonction des paramètres optionnels.
 * @param int $menu_id Facultatif, identifiant du menu. Valeur par défaut : false
 * @param string $menu_code Facultatif, code du menu. Valeur par défaut : false
 * @param int $parent_id Facultatif, identifiant du menu parent. Valeur par défaut : false
 * @param int $parent_code Facultatif, code du menu parent. Valeur par défaut : false
 * @return resource un résultat de requête mysql comprenant les colonnes suivantes :
 *      - id : identifiant du sous menu
 *      - code : code du sous menu
 *		- name : libellé du menu
 *      - full_name : libellé complet du menu
 *		- title : titre du menu
 *		- href : href du menu
 *      - desc : description du menu
 *		- html_id : identifiant html utilisé pour l'affichage du menu
 *		- class : classe html utilisé pour l'affichage du menu
 *		- rights : droit nécessaire pour avoir accès au menu
 *      - one_true : boolean indiquant si un seul des droits suffit à avoir accès au menu
 *		- parent_id : identifiant du menu parent
 */
function adn_menu_get( $menu_id=false, $menu_code=false, $parent_id=false, $parent_code=false ){

    if( ($menu_id !== false && !is_numeric($menu_id))||$menu_id < 0 ){
        return false;
    }

    if( $parent_id !== false && $parent_code !== false ){
        return false;
    }

    $sql = '
        select
			amn_id as id, amn_code as code, amn_name as name, amn_full_name as full_name, amn_tag_title as title, amn_href as href, amn_desc as "desc",
			amn_html_id as html_id, amn_html_class as class, amn_rights as rights, amn_one_true as one_true, amn_parent_id as parent_id
        from adn_menu
        where 1
    ';

    if( $menu_id ){
        $sql .= ' and amn_id = '.$menu_id;
    }
    if( $menu_code ){
        $sql .= ' and amn_code = "'.$menu_code.'"';
    }
    if( $parent_id !==false ){
        if( $parent_id === null ){
            $sql .= ' and amn_parent_id is null';
        }else{
            $sql .= ' and amn_parent_id = "'.$parent_id.'"';
        }
    }
    if( $parent_code!==false ){
        $sql .= ' and amn_parent_id = (select amn_id from adn_menu where amn_code = "'.$parent_code.'")';
    }
    // Affiche la rubrique Yuto en première pour les Riashop ayant une gestion d'abonnement Yuto
	if( gu_users_admin_rights_used('_MENU_OPTIONS_SUBSCRIPTION') ){
        $sql .= ' order by (amn_name = "Yuto") DESC, amn_pos';
    }else{
        $sql .= ' order by amn_pos asc';
    }

    return ria_mysql_query($sql);
}
 // \endcond


// \cond onlyria
/** Cette fonction permet de retourner les droits associés à chaque élément du menu admin
 *	@return array un tableau code menu => tableau de d'identifiant de droits, false en cas d'erreur
 */
function adn_menu_get_rights(){
    global $memcached;

    $key_memcached = 'adn_menu_get_rights';
    if( $get = $memcached->get($key_memcached) ) {
		return $get;
    }

    // Récupère le menu de l'administration avec la gestion des droits appliqués dessus
    $r_menu = ria_mysql_query('
		select
			amn_code as code, amn_rights as rights, amn_one_true as one_true
		from adn_menu
    ');

    if( !$r_menu || !ria_mysql_num_rows($r_menu) ){
		return false;
    }

    // Prépare un tableau de ce menu pour le stocker en cache
    $menu_rights = array();
    while( $menu = ria_mysql_fetch_assoc($r_menu) ){
		$menu_rights[$menu['code']]['rights'] = json_decode($menu['rights']);
		$menu_rights[$menu['code']]['one_true'] = $menu['one_true'];
    }

    // Mise en place du cache pour 3 heures
    $memcached->set($key_memcached, $menu_rights, 60 * 60 * 3);

    return $menu_rights;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'afficher la page d'accueil d'un module/menu
 *	@param int $menu_id Optionnel, Identifiant du menu (null pour afficher la page d'accueil de riashop)
 *	@return string retourne le code HTML correspondant à la page d'accueil du module/menu passé en paramètre
 */
function view_index_menu( $menu_code=null ){
    global $config;
    ob_start();

    if( $menu_code ){
		$r_menu = adn_menu_get(false, false, false, $menu_code);
    }else{
		$r_menu = adn_menu_get(false, false, null);
    }

	if( $r_menu ){
		// Défini la formule de RiaShop par défaut
		$package = 'enterprise';
		$package_btob = 'entreprise';

		// On récupère la formule d'inscription à Yuto
		if( gu_users_admin_rights_used('_MENU_OPTIONS_SUBSCRIPTION') ){
			$package = RegisterGCP::getPackage($config['tnt_id']);
		}

		// On récupère la formule d'inscription à Yuto
		if( gu_users_admin_rights_used('_MENU_OPTIONS_RIASHOP_SUBSCRIPTION') ){
			$package_btob = RegisterGCP::getPackageBtoB($config['tnt_id']);
		}

		while( $menu = ria_mysql_fetch_assoc($r_menu) ){
			if( gu_users_admin_rights_used($menu['code']) ){
				switch($menu['code']){
					case '_MDL_HOME':
					case '_MDL_SEARCH':
					case '_MENU_COMPARATORS_SEARCH':
					case '_MENU_MARKETPLACE_SEARCH':{
						break;
					}
					case '_MDL_MOD':
					case '_MDL_COMPARATORS':
						// Les menus Comparateurs et Modération ne sont pas accessibles pour les Yuto
						if( $package=='business' ){
							continue;
						}
						break;
					case '_MENU_PROMO_SOLDES':{
						$rtype = pmt_types_get();
						if( $rtype ){
							while( $type = ria_mysql_fetch_array($rtype) ){
								if ($type['id'] != _PMT_TYPE_SOLDES) {
									continue;
								}
								?><dl>
									<dt><a href="<?php print htmlspecialchars($menu['href'].'?type='.$type['id']); ?>"><?php print _($type['name']); ?></a></dt>
									<dd><?php print trim($type['desc']) != '' ? _($type['desc']) : '&nbsp;'; ?></dd>
								</dl><?php
							}
						}
						break;
					}
					case '_MENU_PROMO_REWARDS':{
						$rtype = pmt_types_get(6, false);
						if( $rtype ){
							while( $type = ria_mysql_fetch_array($rtype) ){
								?><dl>
									<dt><a href="<?php print htmlspecialchars($menu['href'].'?type='.$type['id']); ?>"><?php print _($type['name']); ?></a></dt>
									<dd><?php print trim($type['desc']) != '' ? _($type['desc']) : '&nbsp;'; ?></dd>
								</dl><?php
							}
						}
						break;
					}
					case '_MENU_PROMO_PRODUCTS':{
						?><dl>
							<dt><a href="<?php print htmlspecialchars($menu['href']); ?>"><?php print _($menu['full_name']); ?></a></dt>
							<dd><?php print _($menu['desc']); ?></dd>
						</dl><?php
						$rtype = pmt_types_get();
						if( $rtype ){
							while( $type = ria_mysql_fetch_array($rtype) ){
								if ($type['id'] == _PMT_TYPE_SOLDES) {
									continue;
								}

								?><dl>
									<dt><a href='/admin/promotions/specials/index.php?type=<?php print $type['id']; ?>'><?php print _($type['name']); ?></a></dt>
									<dd><?php print trim($type['desc']) != '' ? _($type['desc']) : '&nbsp;'; ?></dd>
								</dl><?php
							}
						}
						break;
					}
					case '_MENU_TOOLS_FAQ':{
							?><dl>
								<dt><a href="<?php print htmlspecialchars($menu['href']); ?>"><?php print htmlspecialchars( _($menu['full_name']) ); ?> (<abbr title="Foire Aux Questions"><?php print _('FAQ'); ?></abbr>)</a></dt>
								<dd><?php print _($menu['desc']); ?></dd>
							</dl><?php
							break;
					}
					case '_MENU_CONFIG_DLV_STORES':{
						?><dl>
							<dt><a href="<?php print htmlspecialchars($menu['href']); ?>"><?php print htmlspecialchars( _($menu['full_name']) ); ?></a></dt>
							<dd><?php print _($menu['desc']); ?></dd>
						</dl><?php
						print view_index_menu($menu['code']);
						break;
					}
					case '_MENU_CONFIG_EXTRANET':{
						$rdatas = RegisterGCP::getDatas($config['tnt_id']);
						if( isset($rdatas['package_btob']) && $rdatas['package_btob'] ){
							print '
								<dl>
									<dt><a href="'.htmlspecialchars($menu['href']).'">'.htmlspecialchars( _($menu['full_name']) ).'</a></dt>
									<dd>'.htmlspecialchars( _($menu['desc']) ).'</dd>
								</dl>
							';
						}
						break;
					}
					case '_MENU_MODERATION':{
						$rtypes = gu_messages_types_get();
						if( $rtypes && ria_mysql_num_rows($rtypes) ){
								while( $type = ria_mysql_fetch_array($rtypes) ){
										?><dl>
											<dt><a href="moderation.php?type=<?php print $type['code']; ?>"><?php print htmlspecialchars($type['name-pl']); ?></a></dt>
											<dd><?php print _($type['desc']); ?></dd>
										</dl><?php
								}
						}
						break;
					}
					case '_MENU_FDV_STATS_SUBSCRIPTIONS':{
						?><dl>
							<dt><a href="subscriptions.php"><?php print _('Utilisation')?></a></dt>
							<dd><?php print _('Cet écran permet de suivre l\'utilisation des tablettes Yuto.')?></dd>
						</dl><?php
						break;
					}
					case '_MDL_OPTIONS':{
						if( in_array($package, array('business')) ){
							$menu['desc'] = 'Gérer vos informations personnelles, vos licences…';
							$sub_info = dev_subscribtions_yuto_get();
						}

						if( in_array($package_btob, array('essentiel', 'business')) ){
							$menu['desc'] = 'Gérer vos informations personnelles, votre abonnement…';
							$sub_info_btob = dev_subscribtions_btob_get();
						}

						print '
							<dl>
								<dt><a href="'.htmlspecialchars($menu['href']).'">'.htmlspecialchars(_($menu['full_name'])).'</a></dt>
								<dd>'._($menu['desc']).'</dd>
							</dl>
						';

						if( gu_users_admin_rights_used('_MENU_OPTIONS_SUBSCRIPTION') ){
							print '<dl>';

							if( in_array($package, array('business')) && $sub_info && $sub_info['in_testing'] ){
									print '
											<dd>
												<strong>
									';

									if( RegisterGCP::getPackage($config['tnt_id']) == 'business' ){
										print _("Tout Yuto Business gratuit pendant 14 jours !");
									}

									print '
												</strong>
											</dd>
									';

									print '<dd>&nbsp;</dd>';
							}

							// Affiche la partie "Support" pour les Riashop ayant une gestion d'abonnement actif pour un Yuto Business
							if( $menu_code == null && dev_subscribtions_yuto_get() ){
								print '
										<dt>'._('Support').'</dt>
										<dd class="notice">'._('Activer l\'application : ').'<a href="https://support.riashop.fr/aide/installer-yuto/?utm_source=riashop&amp;utm_medium=homepage&amp;utm_campaign=support&amp;utm_term=lien_activation" target="_blank">'._('Voir le tutoriel').'</a></dd>
										<dd class="notice">'._('Créer des représentants : ').'<a href="https://support.riashop.fr/aide/ajouter-un-compte-utilisateur/?utm_source=riashop&amp;utm_medium=homepage&amp;utm_campaign=support&amp;utm_term=lien_representants" target="_blank">'._('Voir le tutoriel').'</a></dd>
										<dd class="notice">'._('Importer des contacts : ').'<a href="https://support.riashop.fr/aide/importer-contacts-dans-yuto/?utm_source=riashop&amp;utm_medium=homepage&amp;utm_campaign=support&amp;utm_term=lien_contacts" target="_blank">'._('Voir le tutoriel').'</a></dd>
										<dd><input type="button" value="'.htmlspecialchars(_('Guide "Premiers pas"')).'" class="btn-main" onclick="window.open(\'https://support.riashop.fr/yuto-crm/aide-administration-yuto/premiers-pas-administration-yuto/?utm_source=riashop&amp;utm_medium=homepage&amp;utm_campaign=support&amp;utm_term=lien_premiers_pas\', \'_blank\');"></dd>
								';
							}

							print '</dl>';
						}

						if( gu_users_admin_rights_used('_MENU_OPTIONS_RIASHOP_SUBSCRIPTION') ){
							print '<dl>';

							if( in_array($package_btob, array('essentiel', 'business')) && $sub_info_btob && $sub_info_btob['in_testing'] ){
									print '
											<dd>
												<strong>
									';

									if( $package_btob == 'business' ){
										print _("Tout RiaShop Business gratuit pendant 14 jours !");
									}else{
										print _("Tout RiaShop Essentiel gratuit pendant 14 jours !");
									}

									print '
												</strong>
											</dd>
									';

									print '<dd>&nbsp;</dd>';
							}

							// Affiche la partie "Support" pour les Riashop ayant une gestion d'abonnement actif pour un BtoB Essentiel ou Business
							if( $menu_code == null && dev_subscribtions_btob_get() ){
								print '
										<dt>'._('Support').'</dt>
										<dd class="notice">
											'._('Consulter l’aide RiaShop :').'
											<a href="https://support.riashop.fr/riashop-e-commerce/aide-riashop-e-commerce/
											?utm_source=riashop&amp;utm_medium=homepage&amp;utm_campaign=support&amp;utm_term=lien_activation" target="_blank">
												'._('Accéder au site support').'
											</a>
										</dd>
										<dd class="notice">
											'._('Être formé :').'
											<a href="https://www.riashop.fr/formations
											?utm_source=riashop&amp;utm_medium=homepage&amp;utm_campaign=support&amp;utm_term=lien_activation" target="_blank">
												'._('Découvrir les formations').'
											</a>
										</dd>
										<dd>
											<a class="button" href="/config/extranet/index.php">'._('Personnaliser ma boutique').'</a>
										</dd>
								';
							}

							print '</dl>';
						}

						break;
					}
					default:{
						if( $menu['code'] == '_MENU_CONFIG_ORDERS' && (!isset($config['allow_orders_update_state']) || !$config['allow_orders_update_state']) ){
							continue;
						}

						// Traitement spécifique du menu selon sur la version de Yuto activée
						switch( $package ){
							case 'business' : // Yuto Business
								switch( $menu['code'] ){
									case '_MDL_FDV' :
										$menu['full_name'] = _('Yuto Business');
										$menu['desc'] = _('Gérer et configurer votre application mobile, accéder aux rapports, statistiques...');
										break;
									case '_MDL_TOOLS' :
										$menu['full_name'] = _('Imports');
										$menu['desc'] = _('Importer vos données clients à partir de fichiers Excel et CSV.');
										$menu['href'] = '/admin/tools/imports/index.php';
										break;
									case '_MDL_CUSTOMERS' :
										$menu['full_name'] = _('Comptes');
										$menu['desc'] = _('Accéder à la liste et aux fiches de vos clients.');
										break;
									case '_MDL_STATS' :
										$menu['desc'] = _('Vous trouverez ici des statistiques détaillées sur l\'utilisation de l’application.');
										break;
									case '_MDL_MOD':
									case '_MDL_COMPARATORS':
										continue;
										break;
								}
								break;
						}

						print '
							<dl>
								<dt><a href="'.htmlspecialchars($menu['href']).'">'.htmlspecialchars( _($menu['full_name']) ).'</a></dt>
								<dd>'.htmlspecialchars( _($menu['desc']) ).'</dd>
							</dl>
						';

						break;
					}
				}
			}
		}
	}

	// Ajoute un accès à "Mon abonnement" à partir du module Yuto pour tous les RiaShop ayant une gestion d'abonnement
	if( $menu_code == '_MDL_FDV' && gu_users_admin_rights_used('_MENU_OPTIONS_SUBSCRIPTION') ){
			$r_menu = adn_menu_get(1210);
			if( $r_menu && ria_mysql_num_rows($r_menu) ){
					$menu = ria_mysql_fetch_assoc($r_menu);
					?><dl>
							<dt><a href="<?php print htmlspecialchars($menu['href']); ?>"><?php print htmlspecialchars( _($menu['full_name']) ); ?></a></dt>
							<dd><?php print _($menu['desc']); ?></dd>
					</dl><?php
			}
	}

	return ob_get_clean();
}

// \endcond

// \cond onlyria
/** Cette fonction permet de déterminer si un menu à des sous menu
 *	@param $menu_id Obligatoire, Identifiant du menu
 *	@return bool retourne true si le menu à un sous menu, false dans le cas contraire
 */
function adn_menu_has_child( $menu_id ){

    if( !$menu_id || $menu_id < 0 ){
        return false;
    }
    $res = ria_mysql_query('select count(*) from adn_menu where amn_parent_id ='.$menu_id);

    return ria_mysql_result($res, 0, 0);
}
// \endcond
?>
