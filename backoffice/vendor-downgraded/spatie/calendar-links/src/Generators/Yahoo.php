<?php

namespace Spatie\CalendarLinks\Generators;

use Spa<PERSON>\CalendarLinks\Link;
use <PERSON><PERSON>\CalendarLinks\Generator;

class Yahoo implements Generator
{
    public function generate(Link $link)
    {
        $url = 'https://calendar.yahoo.com/?v=60&view=d&type=20';

        $url .= '&title='.urlencode($link->title);
        $url .= '&st='.$link->from->format('Ymd\THis');
        $url .= '&et='.$link->to->format('Ymd\THis');

        if ($link->description) {
            $url .= '&desc='.urlencode($link->description);
        }

        if ($link->address) {
            $url .= '&in_loc='.urlencode($link->address);
        }

        return $url;
    }
}
