<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit11ea9ff9fbf5d5d8d49779cc49784bd5
{
    public static $prefixLengthsPsr4 = array (
        'S' => 
        array (
            '<PERSON><PERSON>\\CalendarLinks\\' => 21,
        ),
    );

    public static $prefixDirsPsr4 = array (
        '<PERSON><PERSON>\\CalendarLinks\\' => 
        array (
            0 => __DIR__ . '/..' . '/spatie/calendar-links/src',
        ),
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit11ea9ff9fbf5d5d8d49779cc49784bd5::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit11ea9ff9fbf5d5d8d49779cc49784bd5::$prefixDirsPsr4;

        }, null, ClassLoader::class);
    }
}
