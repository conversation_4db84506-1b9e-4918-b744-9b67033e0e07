{"name": "riashop-admin-transition", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.11.1", "@babel/preset-env": "^7.11.0", "autoprefixer": "^9.8.6", "babel-core": "^6.26.3", "babel-loader": "^8.1.0", "babel-preset-env": "^1.7.0", "css-loader": "^3.5.3", "extract-loader": "^5.1.0", "extract-text-webpack-plugin": "^4.0.0-beta.0", "file-loader": "^6.0.0", "node-sass": "^4.14.1", "postcss-loader": "^3.0.0", "sass-loader": "^8.0.2", "style-loader": "^1.2.1", "vue": "^2.6.11", "vue-loader": "^15.9.2", "vue-template-compiler": "^2.6.11", "webpack": "^4.46.0", "webpack-cli": "^3.3.11"}, "dependencies": {"axios": "^0.19.2", "es6-promise": "^4.2.8", "vue": "^2.6.11", "vue-infinite-loading": "^2.4.5"}}