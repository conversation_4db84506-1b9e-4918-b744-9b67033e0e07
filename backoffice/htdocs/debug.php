<?php
	/**	\file debug.php
	 *	Cette page permet la gestion de la barre de debug
	 *	Elle permet l'enregistrement des requêtes SQL exécutées par une page donnée,
	 *	et en affiche le détail.
	 */

	// Seuls les super-admin peuvent accéder à la barre de débug
	if (gu_users_is_tenant_linked($_SESSION['usr_id'])) {
		header('Location: /admin/');
		exit;
	}

	if (isset($_GET['active'])) {
		ria_debug_active();
		header('Location: /admin/debug.php');
		exit;
	}

	if (isset($_GET['unactive'])) {
		ria_debug_unactive();
		header('Location: /admin/debug.php');
		exit;
	}

	if (isset($_GET['unactive_memcached'])) {
		ria_debug_memcached_unactived();
		header('Location: /admin/debug.php');
		exit;
	}

	if (isset($_GET['active_memcached'])) {
		ria_debug_memcached_actived();
		header('Location: /admin/debug.php');
		exit;
	}

	if (isset($_GET['unactive_admin'])) {
		ria_debug_admin_unactived();
		header('Location: /admin/debug.php');
		exit;
	}

	if (isset($_GET['active_admin'])) {
		ria_debug_admin_actived();
		header('Location: /admin/debug.php');
		exit;
	}

	if (isset($_GET['clean'])) {
		ria_debug_reinit();
		$_SESSION['debug_clean_is_ok'] = true;
		header('Location: /admin/debug.php');
		exit;
	}

	require_once('admin/skin/header.inc.php');
?>
<style type="text/css">
	#debug .all-sql {
		display: none
	}
	#debug tbody td.sql a {
		display: none;
	}
	#debug tbody td.sql.minify a {
		display: inline-block;
	}
	#debug tbody pre {
		display: block;
		max-width: 900px;
		overflow: auto;
		border: 1px solid #A3A3A3;
		margin: 3px;
		padding: 5px;
		background-color: white;
	}
	thead th {
		cursor: pointer;
	}
	tr.too-long {
		color: red;
	}
</style>

<h2>Débogueur RiaShop</h2>

<?php
	print '<pre>';
	print 'session_id : '.session_id();
	print '</pre><br />';
?>

<div id="debug">
	<form action="/admin/debug.php" method="get">
		<?php
			if (isset($_SESSION['debug_clean_is_ok'])) {
				print '<div class="success">La barre de débug a été vidée.</div>';
				unset($_SESSION['debug_clean_is_ok']);
			}
		?>

		<?php if (ria_debug_is_actived()){ ?>
			<input type="submit" name="unactive" value="Désactiver la barre de debug" />
			<?php if (ria_debug_memcached_is_actived()){ ?>
				<input type="submit" name="unactive_memcached" value="Désactiver les caches" />
			<?php }else{ ?>
				<input type="submit" name="active_memcached" value="Activer les caches" />
			<?php } ?>
			
			<?php if (ria_debug_admin_is_actived()){ ?>
				<input type="submit" name="unactive_admin" value="Désactiver dans l'administration" />
			<?php }else{ ?>
				<input type="submit" name="active_admin" value="Activer dans l'administration" />
			<?php } ?>
		<?php }else{ ?>
			<input type="submit" name="active" value="Activer la barre de debug" />
		<?php } ?>
		<input type="submit" name="clean" value="Vider" />
	</form>

	<?php
		if (isset($_SESSION['ria_debug']) && is_array($_SESSION['ria_debug'])) {
			foreach ($_SESSION['ria_debug'] as $method => $data) {
				if (!in_array($method, array('get', 'post', 'request'))) {
					continue;
				}

				$i = 0;
				foreach ($data as $http=>$list_timer) {
					$ar_request = array();

					foreach( $list_timer as $timer=>$list_request ){
						foreach( $list_request as $request ){
							$md5_sql  = md5( preg_replace('/[^a-zA-Z0-9]/', '', $request['sql']) );
							// $md5_sql .= md5( is_array($request['backtrace']) ? json_encode($request['backtrace']) : 'none' );

							if( !array_key_exists($md5_sql, $ar_request) ){
								$ar_request[ $md5_sql ] = array(
									'min' 	=> null,
									'max' 	=> null,
									'avg' 	=> null,
									'count' => 0,
									'tot' 	=> 0,
									'sql' 	=> $request['sql'],
									'backtrace' 	=> $request['backtrace'],
								);
							}

							$temp_time = round( $request['time'], 6 );

							if ($ar_request[ $md5_sql ]['min'] === null || $ar_request[ $md5_sql ]['min'] > $temp_time) {
								$ar_request[ $md5_sql ]['min'] = $temp_time;
							}

							if ($ar_request[ $md5_sql ]['max'] === null || $ar_request[ $md5_sql ]['max'] < $temp_time) {
								$ar_request[ $md5_sql ]['max'] = $temp_time;
							}

							if ($ar_request[ $md5_sql ]['avg'] === null) {
								$ar_request[ $md5_sql ]['avg'] = $temp_time;
							}else{
								$ar_request[ $md5_sql ]['avg'] = ($ar_request[ $md5_sql ]['avg'] + $temp_time) / 2;
							}

							$ar_request[ $md5_sql ]['count']++;
							$ar_request[ $md5_sql ]['tot'] = $ar_request[ $md5_sql ]['tot'] + $temp_time;
						}
					}

					$ar_request = array_msort( $ar_request, array('count'=>SORT_DESC) );

					ob_start();
					$t_count = $t_tot = $t_avg = 0;
					$t_min = $t_max = null;
					foreach ($ar_request as $request) { 
						$request['sql'] = trim( $request['sql'] );

						$t_count += $request['count'];
						$t_tot   += $request['tot'];
						$t_avg   += $request['avg'];

						$t_min    = $t_min === null || $t_min > $request['min'] ? $request['min'] : $t_min;
						$t_max    = $t_max === null || $t_max < $request['min'] ? $request['min'] : $t_max;
						
						$class = 'minify';
						?>
						<tr <?php print $request['tot']>1 ? ' class="too-long"' : '';?>>
							<td class="sql <?php print $class; ?>" headers="h-sql-<?php print $i; ?>">
								<div class="simple-sql">
									<?php
										print strcut( $request['sql'], 100 );
									?>
									<a href="#">Tout voir</a>
								</div>

								<div class="all-sql">
									<pre><?php
										print htmlspecialchars( $request['sql'] );
									?></pre>
									<table class="checklist">
									<caption>Backtrace</caption>
										<thead>
											<tr>
												<th>Ordre</th>
												<th>function</th>
												<th>fichier</th>
											</tr>
										</thead>
										<?php foreach($request['backtrace'] as $pos => $function) {?>
											<tr>
												<td><?php echo $pos ?> </td>
												<td style="color:green;font-weight:bold;"><?php echo $function['function']?></td>
												<td style="color:blue;">
													<?php echo $function['file']?>:
													<span style="color:red;"><?php echo $function['line']?></span>
												</td>
											</tr>
										<?php }?>
									</table>
									<a href="#">Réduire</a>
								</div>
							</td>
							<td headers="h-nbr-<?php print $i; ?>"><?php print $request['count']; ?></td>
							<td headers="h-min-<?php print $i; ?>"><?php print number_format($request['min'], 6).'s'; ?></td>
							<td headers="h-max-<?php print $i; ?>"><?php print number_format($request['max'], 6).'s'; ?></td>
							<td headers="h-avg-<?php print $i; ?>"><?php print number_format($request['avg'], 6).'s'; ?></td>
							<td headers="h-tot-<?php print $i; ?>"><?php print number_format($request['tot'], 6).'s'; ?></td>
						</tr>
						<?php 
					}

					$body = ob_get_contents();
					ob_end_clean();
					?>

					<dl>
						<dt><?php print htmlspecialchars( strtoupper($method).' - '.$http ); ?></dt>
						<dd>
							<table class="tb-debug checklist ui-sortable tablesorter" style="width: 100%">
								<caption>Requête(s)</caption>
								<col width="*" /><col width="90" /><col width="90" /><col width="90" /><col width="90" /><col width="90" />
								<thead>
									<tr>
										<th></th>
										<th></th>
										<th colspan="4">Temps d'exécution</th>
									</tr>
									<tr>
										<th id="h-sql-<?php print $i; ?>">Requête</th>
										<!-- th id="h-backtrace-<?php print $i; ?>">Backtrace</th -->
										<th id="h-nbr-<?php print $i; ?>">Exécution(s)</th>
										<th id="h-min-<?php print $i; ?>">(min)</th>
										<th id="h-max-<?php print $i; ?>">(max)</th>
										<th id="h-avg-<?php print $i; ?>">(avg)</th>
										<th id="h-tot-<?php print $i; ?>">(total)</th>
									</tr>
								</thead>
								<tfoot>
									<tr>
										<td></td>
										<td><?php print $t_count; ?></td>
										<td><?php print number_format( $t_min, 6 ); ?></td>
										<td><?php print number_format( $t_max, 6 ); ?></td>
										<td><?php print number_format( ($t_avg / $t_count), 6 ); ?></td>
										<td><?php print number_format( $t_tot, 6 ); ?></td>
									</tr>
									<!--tr class="pagination">
										<td style="text-align: left;">Page <input type="text" class="pagedisplay" readonly="readonly"></td>
										<td colspan="10">
											<a class="first" href="#">«« </a>
											<a class="prev" href="#">« Page précédente</a>
											<a class="next" href="#">Page suivante »</a>
											<a class="last" href="#"> »»</a>
											<select class="pagesize">
												<option value="10">10</option>
												<option value="20">20</option>
												<option value="30" selected="selected">30</option>
												<option value="40">40</option>
												<option value="50">50</option>
												<option value="100">100</option>
											</select>
										</td>
									</tr-->
								</tfoot>
								<tbody>
									<?php print $body; ?>
								</tbody>
							</table>
						</dd>
					</dl>

					<?php
					$i++;
				}
			}
		}
	?>
</div>

<script>
	$(document).ready(function(){
		$('.tb-debug').tablesorter({});
	});

	$(document).on('click', '.simple-sql a', function(){
		var parent = $(this).parents('td');
		
		parent.find('.simple-sql').hide();
		parent.find('.all-sql').show();

		return false;
	});

	$(document).on('click', '.all-sql a', function(){
		var parent = $(this).parents('td');
		
		parent.find('.simple-sql').show();
		parent.find('.all-sql').hide();

		return false;
	});
</script>

<?php
	require_once('admin/skin/footer.inc.php');
?>