<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS');

	require_once('documents.inc.php');

	// Cette page permet le téléchargement d'un document
	if( !isset($_GET['doc']) || !doc_documents_exists($_GET['doc']) ){
		exit;
	}

	$doc = ria_mysql_fetch_array(doc_documents_get($_GET['doc'],0,null,'',false));

	header('Cache-Control: private, store');
	header('Pragma: cache');

	header('Content-Type: application/x-force-download');
	header('Content-Disposition: attachment; filename="'.str_replace(' ','_',$doc['filename']).'"');
 	header('Content-Length: '.$doc['size']);

	readfile( $config['doc_dir'].'/'.$doc['id'] );
