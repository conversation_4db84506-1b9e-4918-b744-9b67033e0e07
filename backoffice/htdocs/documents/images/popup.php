<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE');

	require_once('images.inc.php');

	if( isset($closed_popup) ){
		unset( $closed_popup );
	}

	// Enregistre le choix des associations
	if( isset($_GET['linked']) ){
		if( isset($_GET['cnt-img']) && is_array($_GET['cnt-img']) ){
			foreach( $_GET['cnt-img'] as $idCnt ){
				$res = false;
				switch( $_GET['class'] ){
					case CLS_PRODUCT : {
						if( isset($_GET['cnt-place'][$idCnt]) && $_GET['cnt-place'][$idCnt]=='main' ){
							$res = prd_images_main_add_existing( $idCnt, $_GET['image'] );
						}else{
							$res = prd_images_add_existing( $idCnt, $_GET['image'] );
						}

						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du lien entre l'image et le ou les article(s) sélectionné(s).");
						break;
					}
					case CLS_CATEGORY : {
						$res = prd_cat_images_add_existing( $idCnt, $_GET['image'] );
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du lien entre l'image et la ou les catégorie(s) sélectionnée(s).");
						break;
					}
					case CLS_CMS : {
						$res = cms_images_add_existing( $idCnt, $_GET['image'] );
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du lien entre l'image et la ou les page(s) de contenu sélectionnée(s).");
						break;
					}
					case CLS_BRAND : {
						$res = prd_brands_image_add_existing( $idCnt, $_GET['image'] );
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du lien entre l'image et la ou les marque(s) sélectionnée(s).");
						break;
					}
					case CLS_NEWS :{
						$res = news_image_add_existing( $idCnt, $_GET['image'] );
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du lien entre l'image et la ou les actualité(s) sélectionnée(s).");
						break;
					}
					case CLS_STORE : {
						if( isset($_GET['cnt-place'][ $idCnt ]) && is_numeric($_GET['cnt-place'][ $idCnt] ) ){
							$res = dlv_stores_images_add_existing( $idCnt, $_GET['image'], array($_GET['cnt-place'][ $idCnt ]) );
						}

						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du lien entre l'image et le ou les magasin(s) sélectionné(s).");
						break;
					}
					case CLS_USER : {
						if( !isset($_GET['cnt-place'][$idCnt]) ){
							if( !isset($img_type) ){
								$img_type = img_images_types_add( CLS_USER, 'Par défaut');
							}
							$_GET['cnt-place'][$idCnt] = $img_type;
						}
						$res = img_images_objects_add( CLS_USER, $_GET['image'], $_GET['cnt-place'][$idCnt], $idCnt );
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du lien entre l'image et le ou les utilisateur(s) sélectioné(s).");
					}
				}

			}
		}

		if( !isset($error) ){
			$closed_popup = true;
		}
	}

	$rimg = img_images_get( $_GET['image'] );
	switch( $_GET['class'] ){
		case CLS_PRODUCT :
			$type = array('prd'); break;
		case CLS_CATEGORY :
			$type = array('prd-cat'); break;
		case CLS_BRAND :
			$type = array('brd'); break;
		case CLS_STORE :
			$type = array('dlv-str'); break;
		case CLS_CMS :
			$type = array('cms'); break;
		case CLS_NEWS :
			$type = array('news'); break;
		case CLS_USER :
			$type = array('usr'); break;
		default :
			$type = array(); break;
	}

	$count_r	= 0;
	$limit_r 	= 15;
	$ar_results = array();
	$ar_tag[] 	= array();
	$ar_ids 	= array();

	$ar_ids = img_images_get_by_class( $_GET['image'], $_GET['class'] );
	if( !$ar_ids ){
		$ar_ids = array();
	}

	if( !isset($closed_popup) ){
		if( !isset($_GET['autolink']) ){
			if( $rimg && ria_mysql_num_rows($rimg) ){
				$img = ria_mysql_fetch_array( $rimg );

				if( $img['src_name']!='' ){
					// nom d'origine de l'image
					$src_name = strtoupper( str_replace( array('-', '_'), ' ', $img['src_name'] ) );

					// recherche les produits pouvant être rattachés à l'image
					$res = search( array('seg'=>1, 'keywords'=>$src_name, 'published'=>false, 'section'=>false, 'action'=>2, 'type'=>$type), false, 0, 15, false, null, 0, true );
					if( is_array($res) && sizeof($res) ){
						foreach( $res as $one_res ){
							$g = $one_res['get'];
							$s = $one_res['search'];

							if( !in_array($s['tag'], $ar_ids) ){
								$obj_ids = img_images_get_by_class($_GET['image'], $_GET['class'], $s['tag']);
								if (is_array($obj_ids) && count($obj_ids)) {
									continue;
								}

								$ref = $s['type_code'] == 'prd' ? $g['ref'] : '';
								if( !isset($config['img_sync_no_childonly']) || $config['img_sync_no_childonly'] ){
									if( prd_products_get_childonly($s['tag']) ) continue;
								}

								$ar_results[ $s['tag'] ] = array( 'ref' => $ref, 'name'=>$s['name'], 'desc'=>$s['desc'], 'url'=>$s['alt_url'], 'img'=>$s['img_id'] );

								switch( $_GET['class'] ){
									case CLS_PRODUCT :
										$sync = view_prd_is_sync( $g ); break;
									case CLS_CATEGORY :
										$sync = view_cat_is_sync( $g ); break;
									case CLS_BRAND :
										$sync = view_brd_is_sync( $g ); break;
									case CLS_STORE :
										$sync = view_str_is_sync( $g ); break;
									default :
										$sync = ''; break;
								}

								$ar_tag[] = $s['tag'];
								$ar_results[ $s['tag'] ]['is_sync'] = $sync;
							}
						}
					}
				}
			}
		}

		if( isset($_GET['search']) && trim($_GET['search']) ){

			$rsearch = search( array('seg'=>1, 'keywords'=>$_GET['search'], 'published'=>false, 'section'=>false, 'action'=>2, 'type'=>$type), false, 0, 0, false, null );

			$ar_tmp = array();
			if( is_array($rsearch) ){
				$i = 0;

				foreach( $rsearch as $one_res ){
					if( $i >= $limit_r ){
						break;
					}

					$r = $one_res['search'];
					if( in_array($r['tag'], $ar_tag) )
						continue;

					if( (isset($_GET['image']) && $_GET['image']!=$r['img_id']) || !isset($_GET['image']) ){
						$ar_tmp[] = $r['id'];
						$i++;
					}

					if( (isset($_GET['image']) && $_GET['image']==$r['img_id']) || !isset($_GET['image']) ){
						$ar_tag[] = $r['tag'];
					}
				}
			}

			$count_r = $rsearch ? sizeof( $ar_tmp ) : 0;
		}
	}

	$title = array(
		CLS_PRODUCT => _('Associer à un produit'),
		CLS_CATEGORY => _('Associer à une catégorie'),
		CLS_BRAND => _('Associer à une marque'),
		CLS_STORE => _('Associer à un magasin'),
		CLS_CMS => _('Associer à un contenu'),
		CLS_NEWS => _('Associer à une actualité'),
		CLS_USER => _('Associer à un client')
	);

	define('ADMIN_PAGE_TITLE', $title[ $_GET['class'] ].' - ' ._('Images') . ' - ' . _('Médiathèque'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-links-img');
	define('ADMIN_CLASS_BODY', 'popup_img popup-iframe');
	require_once('admin/skin/header.inc.php');
?>
<div class="popup-content">
	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}
	?>

	<form id="search-link" action="popup.php" method="get">
		<input type="hidden" name="class" value="<?php print $_GET['class']; ?>" />
		<input type="hidden" name="image" value="<?php print $_GET['image']; ?>" />
		<?php if( isset($_GET['autolink']) ){ ?>
			<input type="hidden" name="autolink" value="1" />
		<?php } ?>
		<input type="text" name="search" id="search" value="<?php print isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>" />
		<input type="submit" name="btn-search" id="btn-search" value="<?php print _('Rechercher'); ?>" onclick="return $.trim( $('#search').val() ) != ''" />
	</form>
</div>

<form action="popup.php" method="get">
	<div class="popup-content">
		<input type="hidden" name="class" value="<?php print $_GET['class']; ?>" />
		<input type="hidden" name="image" value="<?php print $_GET['image']; ?>" />
		<input type="hidden" name="search" value="<?php print isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>" />
		<?php if( isset($_GET['autolink']) ){ ?>
			<input type="hidden" name="autolink" value="1" />
		<?php } ?>

		<?php
			if( isset($_GET['search']) && trim($_GET['search']) ){
				$h3 = '<h3>Résultat'.( $count_r > 1 ? 's' : '' ).' de recherche :</h3>';
				print _($h3);

				if( $count_r > 0 ){
					$count = 0; $ar_tmp = array();
					foreach( $rsearch as $one_res ){
						$r = $one_res['search'];
						$g = $one_res['get'];

						$obj_ids = img_images_get_by_class($_GET['image'], $_GET['class'], $r['tag']);
						if (is_array($obj_ids) && count($obj_ids)) {
							continue;
						}

						switch( $_GET['class'] ){
							case CLS_PRODUCT :
								$sync = view_prd_is_sync( $g ); break;
							case CLS_CATEGORY :
								$sync = view_cat_is_sync( $g ); break;
							case CLS_BRAND :
								$sync = view_brd_is_sync( $g ); break;
							case CLS_STORE :
								$sync = view_str_is_sync( $g ); break;
							case CLS_USER :
								$sync = view_usr_is_sync( $g ); break;
							default :
								$sync = ''; break;
						}

						if( in_array($r['tag'], $ar_tag) ){
							continue;
						}

						if( (isset($_GET['image']) && $_GET['image']==$r['img_id']) || !isset($_GET['image']) ){
							continue;
						}

						if( $count>=$limit_r ){
							break;
						}

						if( $r['type_code']=='prd' ){
							if( !isset($config['img_sync_no_childonly']) || $config['img_sync_no_childonly'] ){
								if( prd_products_get_childonly($r['tag']) ) continue;
							}
						}

						$ref = '';
						if( $_GET['class'] == CLS_PRODUCT ){
							$ref = prd_products_get_ref( $r['tag'] );
						}

						print '	<div class="cnt-infos">
									<div class="cnt-checkbox"><input type="checkbox" name="cnt-img[]" id="cnt-img-'.$r['tag'].'" value="'.htmlspecialchars($r['tag']).'" /></div>
									<div class="cnt-name"><a href="'.$r['alt_url'].'" target="_blank">'.( trim($sync) != '' ? $sync.' ' : '' ).( trim($ref) != '' ? htmlspecialchars($ref).' - ' : '' ).htmlspecialchars($r['name']).'</a></div>
									<div class="cnt-desc">'.htmlspecialchars($r['desc']).'</div>
									<div class="cnt-url"><a href="'.htmlspecialchars($r['alt_url']).'" target="_blank">
						';
						switch ($_GET['class']) {
							case CLS_PRODUCT : {
								print _('Catalogue').' &raquo; ';

								$categories = prd_classify_get( false, $r['tag'] );
								if( $categories!==false && ria_mysql_num_rows($categories) ){

									$cat = ria_mysql_fetch_array( $categories );

									$rcp = prd_categories_parents_get( $cat['cat'] );
									while( $cp = ria_mysql_fetch_array($rcp) ){
										print htmlspecialchars($cp['title']).' &raquo; ';
									}

									$rc = prd_categories_get( $cat['cat'] );
									if( ria_mysql_num_rows($rc) ){
										$c = ria_mysql_fetch_array($rc);
										print htmlspecialchars($c['title']).' &raquo; ';
									}

								}
								break;
							}
							case CLS_CATEGORY :
							case CLS_BRAND : {
								print _('Catalogue').' &raquo; ';
								break;
							}
							case CLS_STORE : {
								print _('Configuration').' &raquo; '._('Livraison des commandes').' &raquo; '._('Magasins').' &raquo; ';
								break;
							}
							case CLS_CMS : {
								print _('Outils').' &raquo; '._('Gestion de contenu').' &raquo; ';
								break;
							}
							case CLS_NEWS : {
								print _('Outils').' &raquo; '._('Actualités').' &raquo; ';
								break;
							}
						}
						print htmlspecialchars($r['name']);
						print '		</a></div>';
						if( $_GET['class']==CLS_PRODUCT ){
							print '
									<div class="cnt-place">
										<label for="cnt-place-'.$r['tag'].'">' . _("Associer en tant que :") . '</label>
										<select name="cnt-place['.$r['tag'].']" for="cnt-place-'.$r['tag'].'">
											<option value="main">' . _("Image principale") . '</option>
											<option value="second">' . _("Image secondaire") . '</option>
										</select>
									</div>
							';
						} elseif( $_GET['class']==CLS_STORE ){
							print '	<div class="cnt-place">
										<label for="cnt-place-'.$r['tag'].'">' . _("Associer en tant que :") .'</label>
										<select name="cnt-place['.$r['tag'].']" for="cnt-place-'.$r['tag'].'">';
							$rplc = dlv_stores_img_types_get();
							if( $rplc ){
								while( $plc = ria_mysql_fetch_array($rplc) )
									print '	<option value="'.$plc['id'].'">'.htmlspecialchars($plc['name']).'</option>';
							}
							print '		</select>
									</div>';
						}elseif( $_GET['class'] == CLS_USER ){
							$r_type = img_images_types_get( CLS_USER );
							if( $r_type && ria_mysql_num_rows($r_type) ){
								print '
										<div class="cnt-place">
											<label for="cnt-place-'.$r['tag'].'">' . _("Associer en tant que :") .'</label>
											<select name="cnt-place['.$r['tag'].']" for="cnt-place-'.$r['tag'].'">
								';
								while( $type = ria_mysql_fetch_array($r_type) ){
									print '	<option value="'.$type['id'].'">'.htmlspecialchars($type['name']).'</option>';
								}
								print '		</select>
										</div>';
							}
						}
						print '	</div>';

						$count++;
					}

					if ($count <= 0) {
						print _('Aucun résultat pour votre recherche "').htmlspecialchars($_GET['search']).'"';
					}
				}else{
					print _('Aucun résultat pour votre recherche "').htmlspecialchars($_GET['search']).'"';
				}
			}

			if( sizeof($ar_results) ){
		?>

		<div>
			<a class="suggestion" href="#" onclick="$('#auto-links').prev().hide(); $('#auto-links').show(); return false;"><?php echo _("Voir nos suggestions"); ?></a>
			<div id="auto-links">
				<div class="auto-link">
					<h3><?php echo _("Nos suggestions :"); ?></h3>

					<?php
						foreach( $ar_results as $tag=>$info ){
							if( $tag<=0 ){
								break;
							}

							$fil = '';
							switch ($_GET['class']) {
								case CLS_PRODUCT : {
									$fil .= _('Catalogue &raquo; ');

									preg_match( '/cat=([0-9]+)/', $info['url'], $matches );
									if (is_array($matches) && count($matches)) {
										$rcp = prd_categories_parents_get( $matches[1] );
										while( $cp = ria_mysql_fetch_array($rcp) ){
											$fil .= htmlspecialchars($cp['title']).' &raquo; ';
										}

										$rc = prd_categories_get( $matches[1] );
										if( ria_mysql_num_rows($rc) ){
											$c = ria_mysql_fetch_array($rc);
											$fil .= htmlspecialchars($c['title']).' &raquo; ';
										}
									}
									break;
								}
								case CLS_CATEGORY :
								case CLS_BRAND : {
									$fil .= _('Catalogue &raquo; ');
									break;
								}
								case CLS_STORE : {
									$fil .= _('Configuration &raquo; Livraison des commandes &raquo; Magasins &raquo; ');
									break;
								}
								case CLS_CMS : {
									$fil .= _('Outils &raquo; Gestion de contenu &raquo; ');
									break;
								}
								case CLS_NEWS : {
									$fil .= _('Outils &raquo; Actualités &raquo; ');
									break;
								}
							}

							?><div class="cnt-infos">
								<div class="cnt-checkbox"><input name="cnt-img[]" id="cnt-img-<?php print $tag; ?>" value="<?php print $tag; ?>" type="checkbox"></div>
								<div class="cnt-name"><a href="<?php print $info['url']; ?>" target="_blank"><?php print htmlspecialchars($info['name']); ?></a></div>
								<div class="cnt-desc"></div>
								<div class="cnt-url">
									<a href="<?php print $info['url']; ?>" target="_blank"><?php print $fil.htmlspecialchars($info['name']); ?></a>
								</div>
							</div><?php

							if ($_GET['class'] == CLS_PRODUCT) {
								?><div class="cnt-place">
									<label for="cnt-pos-<?php print $tag; ?>-prd"><?php print _('Associer en tant que :'); ?> </label>
									<select id="cnt-pos-<?php print $tag; ?>-prd" name="cnt-pos[<?php print $tag; ?>]">
										<option value="main"><?php print _('Image principale'); ?></option>
										<option value="second"><?php print _('Image secondaire'); ?></option>
									</select>
								</div><?php
							}elseif( $_GET['class']==CLS_STORE ){
								?><div class="cnt-place">
									<label for="cnt-place-<?php echo $tag;?>"><?php print _('Associer en tant que :');?> </label>
									<select name="cnt-place[<?php echo $tag; ?>]" for="cnt-place-<?php echo $tag; ?>">
									<?php
										$rplc = dlv_stores_img_types_get();
										if( $rplc ){
											while( $plc = ria_mysql_fetch_array($rplc) )
												print '	<option value="'.$plc['id'].'">'.htmlspecialchars($plc['name']).'</option>';
										}
									?>
									</select>
								</div><?php
							}elseif( $_GET['class'] == CLS_USER ){
								$r_type = img_images_types_get( CLS_USER );
								if( $r_type && ria_mysql_num_rows($r_type) ){ ?>
									<div class="cnt-place">
										<label for="cnt-place-<?php echo $tag?>"><?php print _('Associer en tant que :'); ?> </label>
										<select name="cnt-place[<?php echo $tag?>]" for="cnt-place-<?php echo $tag?>">
									<?php while( $type = ria_mysql_fetch_array($r_type) ){
										print '	<option value="'.$type['id'].'">'.htmlspecialchars($type['name']).'</option>';
									} ?>
											</select>
									</div><?php
								}
							}
						}
					?>
				</div>
				<div class="clear"></div>
			</div>
		</div>
		<?php } ?>
	</div>

	<div id="pagination" class="popup-content">
		<div class="linked"><input type="submit" name="linked" id="linked" value="<?php print _('Associer'); ?>" />&nbsp;<input type="button" name="cancel" value="<?php echo _("Annuler"); ?>" onclick="parent.hidePopup();" /></div>
	</div>
</form>
<script><!--
<?php if( isset($_GET['autolink']) ){ ?>
	var imageID = <?php print $_GET['image']; ?>;

	$('#linked').click(function(){
		var html = '';
		$('.cnt-infos').each(function(){
			var checkbox = $(this).find('[type=checkbox]');

			if( checkbox.is(':checked') ){
				var prdID  = checkbox.val();
				var posImg = $(this).find('option:selected').val()

				html += '<div id="cnt-img-' + imageID + '-' + prdID + '" class="cnt-infos">';
				html += '	<div class="check-link">';
				html += '		<input name="add-links[' + imageID + '][prd][]" value="' + prdID + '" type="checkbox" checked="checked" />';
				html += '	</div>';
				html += '	<div class="cnt-name">' + $(this).find('.cnt-name').html() + '</div>';
				html += '	<div class="cnt-desc"></div>';
				html += '	<div class="cnt-url">';
				html += '		<a href="" target="_blank">' + $(this).find('.cnt-url').html() + '</a>';
				html += '	</div>';
				html += '	<div class="cnt-place">';
				html += '		<label for="position-links-' + prdID + '-prd-' + imageID + '-0">Associer en tant que : </label>';
				html += '		<select name="position-links[' + imageID + '][prd][' + prdID + ']" id="position-links-' + prdID + '-prd-' + imageID + '-0" class="valign-center">';
				html += '			<option ' + ( posImg == 'main' ? 'selected="selected"' : '' ) + 'value="main">Image principale</option>';
				html += '			<option ' + ( posImg == 'second' ? 'selected="selected"' : '' ) + 'value="second">Image secondaire</option>';
				html += '		</select>';
				html += '	</div>';
				html += '</div>';
			}
		});

		parent.autoLink_reloadAutoLinkImage( imageID, html );
		return false;
	});
<?php }elseif( isset($closed_popup) && $closed_popup ===true ){ ?>
	parent.hidePopupImage();
<?php } ?>
$('#pagination').scrollToFixed({
	bottom: 0,
	offsets: true
});
--></script>

<?php
	require_once('admin/skin/footer.inc.php');
?>
