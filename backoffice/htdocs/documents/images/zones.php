<?php
/**	\file zones.php
 *
 * 	Cette page apparaît en popup et permet la gestion de zones cliquables à l'intérieur d'une image. L'utilisation type
 * 	est un visuel contenant plusieurs produits et la création de zones cliquables qui permettent d'accéder à chaque fiche
 * 	produit individuelle.
 *
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE');

// Le format d'image "very_high" est indispensable pour que cette fonctionnalité soit disponible
if (!isset($config['img_sizes']['very_high'])) {
    exit;
}

require_once('images.inc.php');
require_once('img.zones.inc.php');

$CLS_IDS = array(
    'prd' => CLS_PRODUCT,
    'prd-cat' => CLS_CATEGORY,
    // 'brd' => CLS_BRAND,
    'dlv-str' => CLS_STORE,
    'cms' => CLS_CMS,
    //'news' => CLS_NEWS
);

$SEARCH_CONTENT_TYPES = array_flip($CLS_IDS);

// Sélection un site à partir de 3 infos dont voici la priorité :
//  * identifiant passé en paramètre
//  * sinon identifiant stocké en session
//  * sinon identifiant du site par défaut
$wst_id = $config['wst_id'];
if( isset($_GET['wst_id']) && is_numeric($_GET['wst_id']) && $_GET['wst_id'] > 0 ){
    $_SESSION['websitepicker'] = $_GET['wst_id'];
    $wst_id = $_GET['wst_id'];
}elseif( isset($_SESSION['websitepicker']) && is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker'] > 0 ){
    $wst_id = str_replace('w-', '', $_SESSION['websitepicker']);
}

$cls_id = null;
$object_ids = array();
$cols = array();
if (isset($_GET['cls_id']) && fld_classes_exists(intval($_GET['cls_id']))) {
    $cls_id = intval($_GET['cls_id']);
    if (isset($_GET['obj_id_0'])) {
        $object_ids[] = intval($_GET['obj_id_0']);
    }
    if (isset($_GET['obj_id_1'])) {
        $object_ids[] = intval($_GET['obj_id_1']);
    }
    if (isset($_GET['obj_id_2'])) {
        $object_ids[] = intval($_GET['obj_id_2']);
    }

}

// Si le paramètre image est fourni, chargement de l'image
$image = null;
if (isset($_GET['image'])) {
    $r_image = img_images_get($_GET['image']);
    if ($r_image && ria_mysql_num_rows($r_image)) {
        $image = ria_mysql_fetch_assoc($r_image);
    }
}

$size = $config['img_sizes']['very_high'];
$sizeLength = sqrt($size['width'] * $size['width'] + $size['height'] * $size['height']);

$sourceSize = img_images_source_infos($image['id']);

// Bouton "Enregistrer les zones"
if (!empty($_POST) && isset($_POST['zone']) && is_array($_POST['zone'])) {
    header('Content-Type: application/json');

	// Bouton "Supprimer la zone"
    if (isset($_POST['delete-zone']) && isset($_POST['zone']['id'])) {
        if (!img_zones_delete($_POST['zone']['id'], false, $wst_id)) {
            $json = array('error' => _('Erreur lors de la suppression d\'une zone cliquable'));
        } else {
            $json = array('success' => _('Zone cliquable supprimée'));
        }

        print json_encode($json);
        exit;
    }

    $zonesData = array();
    $multiple = false;
    if (isset($_POST['zone'][0])) {
        $zonesData = $_POST['zone'];
        $multiple = true;
    } else {
        $zonesData = array($_POST['zone']);
    }

    $error = null;
    $json = array('data' => array());

    foreach ($zonesData as $zoneData) {
        if (!isset($zoneData['x'],
            $zoneData['y'],
            $zoneData['width'],
            $zoneData['height'],
            $zoneData['title'],
            $zoneData['desc'],
            $zoneData['href_type'],
            $zoneData['href'],
            $zoneData['href_target'])
        ) {
            $error = _('La zone cliquable n\'est pas valide et ne peut pas être enregistrée.');
            break;
        }

		// Mise à jour d'une zone existante
        if (isset($zoneData['id']) && !empty($zoneData['id'])) {
            if (!img_zones_update(
                $zoneData['id'],
                false,
                $image['id'],
                isset($zoneData['cls_id']) ? $zoneData['cls_id'] : (is_null($cls_id) ? false : $cls_id),
                isset($object_ids[0]) ? $object_ids[0] : false,
                isset($object_ids[1]) ? $object_ids[1] : false,
                isset($object_ids[2]) ? $object_ids[2] : false,
                floatval($zoneData['x']) / $size['width'],
                floatval($zoneData['y']) / $size['height'],
                floatval($zoneData['width']) / $size['width'],
                floatval($zoneData['height']) / $size['height'],
                $zoneData['title'],
                $zoneData['desc'],
                intval($zoneData['href_type']),
                $zoneData['href'],
                $zoneData['href_target'],
                $wst_id
            )) {
                $error = _("Erreur lors de la modification d'une zone.");
                break;
            }

            $r_zone = img_zones_get( $zoneData['id'], false, false, false, false, false, false, false, $wst_id );
            if (!$r_zone || !ria_mysql_num_rows($r_zone)) {
                continue;
            }

            $zone = ria_mysql_fetch_assoc($r_zone);


            if (preg_match('/^[^:]+\:(?P<href>\d+)/', $zone['href'], $matches)) {
                $cols = array();
                $r_obj = fld_classes_get_objects($zone['href_type'], array($matches['href']), $cols);
                if ($r_obj && ria_mysql_num_rows($r_obj) && $obj = ria_mysql_fetch_assoc($r_obj)) {
                    $zone['obj_name'] = isset($obj['title']) ? $obj['title'] : $obj['name'];
                }
            }
            $zone['x'] *= $size['width'];
            $zone['y'] *= $size['height'];
            $zone['width'] *= $size['width'];
            $zone['height'] *= $size['height'];

            if ($multiple) {
                $json['data'][] = $zone;
            } else {
                $json['data'] = $zone;
            }

            continue;
        }

		// Création d'une nouvelle zone
        if (!$zone_id = img_zones_add(
            $image['id'],
            floatval($zoneData['x']) / $size['width'],
            floatval($zoneData['y']) / $size['height'],
            floatval($zoneData['width']) / $size['width'],
            floatval($zoneData['height']) / $size['height'],
            isset($zoneData['cls_id']) ? $zoneData['cls_id'] : $cls_id,
            isset($object_ids[0]) ? $object_ids[0] : 0,
            isset($object_ids[1]) ? $object_ids[1] : 0,
            isset($object_ids[2]) ? $object_ids[2] : 0,
            $zoneData['title'],
            $zoneData['desc'],
            empty($zoneData['href_type']) ? null : $zoneData['href_type'],
            empty($zoneData['href']) ? null : $zoneData['href'],
            empty($zoneData['href_target']) ? null : $zoneData['href_target'],
            null,
            $wst_id
        )) {
            $error = _("Erreur lors de l'ajout d'une zone cliquable sur l'image.");
            break;
        }

        $r_zone = img_zones_get( $zone_id, false, false, false, false, false, false, false, $wst_id );
        if (!$r_zone || !ria_mysql_num_rows($r_zone)) {
            $error = _("Erreur lors de l'ajout d'une zone. Données non sauvegardées.");
            break;
        }

        $zone = ria_mysql_fetch_assoc($r_zone);

		// Charge la cible du lien
        if( trim($zone['href']) && preg_match('/^[^:]+\:(?P<href>\d+)/', $zone['href'], $matches) ){
            $cols = array();
            $r_obj = fld_classes_get_objects($zone['href_type'], array($matches['href']), $cols);
            if ($r_obj && ria_mysql_num_rows($r_obj) && $obj = ria_mysql_fetch_assoc($r_obj)) {
                $zone['obj_name'] = isset($obj['title']) ? $obj['title'] : $obj['name'];
            }
		}

        $zone['x'] *= $size['width'];
        $zone['y'] *= $size['height'];
        $zone['width'] *= $size['width'];
        $zone['height'] *= $size['height'];

        if ($multiple) {
            $json['data'][] = $zone;
        } else {
            $json['data'] = $zone;
        }

        continue;
    }

    if (!is_null($error)) {
        $json['error'] = $error;
    } else {
        $json['success'] = _('La configuration des zones cliquables a été sauvegardée.');
    }

    print json_encode($json);
    exit;
}

$imageUrl = $config['img_url']
    . '/' . $config['img_sizes']['very_high']['dir']
    . '/' . $image['id'] . '.' . $config['img_sizes']['very_high']['format'];

$imageZones = array();
$zones = img_zones_get(
    false,
    i18n::getLang(),
    $image['id'],
    !$cls_id ? 0 : intval($cls_id),
    isset($object_ids[0]) ? $object_ids[0] : false,
    isset($object_ids[1]) ? $object_ids[1] : false,
    isset($object_ids[2]) ? $object_ids[2] : false,
    false,
    $wst_id
);

if ($zones && ria_mysql_num_rows($zones)) {
    while ($zones && $zone = ria_mysql_fetch_assoc($zones)) {
        if (preg_match('/^[^:]+\:(?P<href>\d+)/', $zone['href'], $matches)) {
            $cols = array();
            $r_obj = fld_classes_get_objects($zone['href_type'], array($matches['href']), $cols);
            if ($r_obj && ria_mysql_num_rows($r_obj) && $obj = ria_mysql_fetch_assoc($r_obj)) {
                $zone['obj_name'] = isset($obj['title']) ? $obj['title'] : $obj['name'];
            }
        }
        $zone['x'] *= $size['width'];
        $zone['y'] *= $size['height'];
        $zone['width'] *= $size['width'];
        $zone['height'] *= $size['height'];

        $imageZones[] = $zone;
    }
}

define('ADMIN_PAGE_TITLE', _('Zones cliquables d\'image') );
define('ADMIN_HEAD_POPUP', true);
define('ADMIN_ID_BODY', 'popup-img-zones');
define('ADMIN_CLASS_BODY', 'popup_img popup_img_zones popup-iframe' );
require_once('admin/skin/header.inc.php');
?>
<div id="popup-content">
    <?php print view_websites_selector( $wst_id, false, 'riapicker', false, wst_websites_get_name($wst_id), false, false ); ?>

    <div class="resize-container" style="background-image: url(<?php print htmlspecialchars($imageUrl) ?>);
				width: <?php print $config['img_sizes']['very_high']['width'] ?>px; max-width: 770px;
				height: <?php print $config['img_sizes']['very_high']['height'] ?>px;
                background-size: auto <?php print $config['img_sizes']['very_high']['height'] ?>px;
                float: none; margin: 0 auto;">
    </div>
    <div id="popup_ria" class="form-container" style="display:none;">
        <div class="popup_ria_drag">
            <div class="drag"></div>
            <div class="text"><?php print _('Modifier la zone cliquable'); ?></div>
            <a>
                <div class="close close-zone"></div>
            </a>
        </div>
        <div class="row content">
            <form action="#" class="zone-details" enctype="multipart/form-data">
                <input type="hidden" name="zone[index]" id="index" value="" />
                <input type="hidden" name="zone[id]" id="id" value="" />
                <input type="hidden" name="zone[x]" id="x" value="" />
                <input type="hidden" name="zone[y]" id="y" value="" />
                <input type="hidden" name="zone[width]" id="width" value="" />
                <input type="hidden" name="zone[height]" id="height" value="" />

                <label for="title">
                    <span><?php echo _('Titre :'); ?></span>
                    <input type="text" name="zone[title]" id="title" value="" />
                </label>
                <label for="desc">
                    <span><?php echo _('Description :'); ?></span>
                    <textarea name="zone[desc]" id="desc" cols="50" rows="5"></textarea>
                </label>
                <label for="href_type">
                    <span><?php echo _("Lien :"); ?></span>
                    <select name="zone[href_type]" id="href_type">
                        <?php
                        $classes = fld_classes_get(array_values($CLS_IDS));
                        if ($classes && ria_mysql_num_rows($classes)) {
                            while ($classes && $class = ria_mysql_fetch_assoc($classes)) {
                                print '<option value="' . htmlspecialchars($class['id']). '">' . htmlspecialchars($class['name']) . '</option>';
                            }
                        }
                        ?>
                        <option value=""><?php echo _("URL externe"); ?></option>
                    </select>
                </label>

                <label for="href" class="href-label" style="display:none;">
                    <span><?php echo _("URL du lien :"); ?></span>
                    <input type="text" name="zone[href]" id="href"/>
                </label>
                <label for="zone[search]" class="search-label">
                    <span><?php echo _("Recherche :"); ?></span>
                    <input type="text" id="search" aria-required="true" onkeyup="return false;" class="mceFocus" name="zone[search]" />
                    <input onclick="return openSelector($('#href_type').val());" class="btn-main" type="button" name="files" id="files" value="<?php echo _("Parcourir"); ?>" />
                </label>
                <label for="href_target">
                    <span><?php echo _("Cible du lien :"); ?></span>
                    <select class="href-target" name="zone[href_target]" id="href_target">
                        <option value="_self"><?php echo _("Ouvrir dans cette fenêtre / dans ce cadre"); ?></option>
                        <option value="_blank"><?php echo _("Ouvrir dans une nouvelle fenêtre (_blank)"); ?></option>
                    </select>
                </label>

                <div style="padding: 0 10px">
					<input type="button" name="cancel-zone" value="<?php echo _("Annuler"); ?>" class="btn-cancel float-right close-zone" />
					<input type="button" name="save-zone" value="<?php echo _("Enregistrer"); ?>" class="btn-main float-right" />
					<input type="button" name="delete-zone" class="delete-zone" value="<?php echo _("Supprimer"); ?>" />
                </div>
            </form>
        </div>
        <div id="choose-elem" style="position: absolute;top: 0px;left: 0px;width: 0px; height: 0px"></div>
    </div>

    <div class="clear"></div>

    <br /><p>
        <span class="error" style="display:none;"></span>
        <span class="success" style="display:none;"></span>
        <?php
            if( isset($_GET['success']) ){
                print '<span class="success">'._('La configuration des zones cliquables a été sauvegardée.').'</span>';
            }
        ?>
    </p>

    <div>
        <div class="right">
            <input type="button" name="add-new-zone" value="<?php echo _("Ajouter une zone"); ?>" />
            <input type="button" name="save-all-zone" value="<?php echo _("Enregistrer les zones"); ?>" class="btn-main float-right" />
        </div>
    </div>
    <div class="clear"></div>
</div>
<div class="clear"></div>
<div class="echo-save"></div>
<script>
    window.zonesClick = <?php print json_encode($imageZones) ?>;
    window.zoneWidth = <?php print intval($size['width']) ?>;
    window.zoneHeight = <?php print intval($size['height']) ?>;
</script>
