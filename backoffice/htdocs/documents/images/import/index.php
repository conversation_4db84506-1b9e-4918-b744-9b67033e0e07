<?php

	/** \file index.php
	 *	Cette page fait des suggestions d'association automatique entre des images importées qui ne sont associées à aucun contenu
	 *	et des contenus de l'instance.
	 *	Par défaut, seules les images n'ayant jamais été associées sont affichées.
	 *	Le but est de permettre à nos clients de gagner du temps lors de la mise en route de l'instance.
	 */

	require_once('images.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE_IMPORT');

	if(isset($_GET['is_associated']) && $_GET['is_associated'] == '0') { $_SESSION['img_import_is_associated'] = 'NULL'; }
	if(isset($_GET['is_associated']) && $_GET['is_associated'] == '1') { $_SESSION['img_import_is_associated'] = false; }

	$is_associated = isset($_SESSION['img_import_is_associated']) ? $_SESSION['img_import_is_associated'] : ($_SESSION['img_import_is_associated'] = false);
	if($is_associated == 'NULL'){ $is_associated = null; }

	$img_like = isset($_GET['like']) && in_array($_GET['like'], array('start', 'in', 'end', 'equal')) ? $_GET['like'] : 'start';
	$img_search = isset($_GET['search']) && trim($_GET['search']) != '' ? trim($_GET['search']) : '';

	$limit_by_page = 20;

	// Récupère les images liées non associées avec la liste des produits liés (via la référence)
	$ar_images = img_images_get_automatic_association($is_associated, $img_search, $img_like, $with_engine=false);

	$page 		= isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] > 0 ? $_GET['page'] : 1;
	$pages 		= ceil( sizeof($ar_images) / $limit_by_page );

	if( $page > $pages ){
		$page = $pages;
	}

	$page_start = $page - 2 > 0 	 ? $page - 2 : 1;
	$page_stop 	= $page + 2 < $pages ? $page + 2 : $pages;

	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Médiathèque'), '/admin/documents/index.php' )
		->push( _('Images'), '/documents/images/index.php' )
		->push( _('Association automatique') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Association automatique') . ' - ' . _('Images') . ' - ' . _('Médiathèque'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Association automatique'); ?> (<span class="count_img"><?php print ria_number_format(count($ar_images)); ?></span>)</h2>
	<?php
		// Affichage des messages d'erreur
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}
	?>
	<div class="notice">
		<p><?php echo _("Vous trouverez ci-dessous toutes les images qui ne sont associées à aucun contenu. Pour chacune d’entres elles, deux actions sont possibles :"); ?></p>
		<ul>
			<li><?php echo _("Enregistrer : vous permet d’enregistrer l’association cochée pour cette image."); ?></li>
			<li><?php echo _("Recherche : si le produit qui correspond à l’image n’est pas dans la liste proposée, ce bouton vous permettra d’effectuer manuellement une recherche."); ?></li>
			<li><?php echo _("Masquer : permet de retirer l'image des associations automatiques"); ?></li>
			<li><?php echo _("Supprimer : permet de supprimer définitivement l'image"); ?></li>
		</ul>
		<p><?php echo _("Vous pouvez enregistrer une page entière en cliquant sur \"Enregistrer\" tout en bas de page."); ?></p>
	</div>

	<div class="autoassociated-image">
		<form action="index.php?page=<?php print $page; ?>" method="get">
			<div class="filter">
				<input type="checkbox" name="is_associated" id="is_associated" value="1" <?php print $is_associated!==null ? 'checked="checked"' : ''; ?> />
				<label for="is_associated"><?php echo _("N'afficher que les images n'ayant jamais été associées"); ?></label>

				<div id="img-search">
					<div class="label"><?php echo _("Rechercher :"); ?></div>
					<div>
						<label for="search"><?php echo _("Le nom de l'image"); ?>&nbsp;</label>
						<select name="like" id="like" class="width-auto">
							<option <?php print $img_like == 'start' ? 'selected="selected"' : ''; ?> value="start"><?php echo _("Commence par"); ?></option>
							<option <?php print $img_like == 'in' ? 'selected="selected"' : ''; ?> value="in"><?php echo _("Contient"); ?></option>
							<option <?php print $img_like == 'end' ? 'selected="selected"' : ''; ?> value="end"><?php echo _("Se termine par"); ?></option>
							<option <?php print $img_like == 'equal' ? 'selected="selected"' : ''; ?> value="equal"><?php echo _("Est égal à"); ?></option>
						</select>
						<input type="text" name="search" id="search" value="<?php print $img_search; ?>" />
						<input type="submit" name="search-img" value="<?php echo _("Rechercher"); ?>" />
					</div>
				</div>
			</div>
		</form>

		<div class="clear"></div>

		<div id="auto-links" data-current-page="<?php print $page; ?>">
			<?php
				if( !sizeof($ar_images) ){
					print '<div class="notice">';

					if( $img_search != '' ){
						print _('Aucune image ne correspond à vos critères de recherche.');
					}else{
						print _('Toutes les images sont associées à au moins un contenu, vous pouvez vous rendre sur <a href="/admin/documents/images/index.php">la médiathèque</a> pour gérer leurs associations.');
					}

					print '</div>';
				}

				$index = 0;
				foreach( $ar_images as $data ){
			?>
				<div class="auto-link<?php print $index < (($page -1) * $limit_by_page) || $index >= ($page * $limit_by_page) ? ' hidden' : ' not-min-height'; ?>" data-img-id="<?php print $data['img']['id']; ?>" data-index="<?php print $index; ?>">
					<h2>
						<?php print view_image_is_sync( $data['img'] ); ?>
						<span class="src-name"><?php print htmlspecialchars( $data['img']['src_name'] ); ?></span>
					</h2>

					<?php
						if( $index >= (($page -1) * $limit_by_page) && $index < ($page * $limit_by_page) && sizeof($data['products']) ){
							print view_images_get_autolinks( $data['img']['id'], $data['img']['src_name'], $data['products'] );
						}else{ ?>
							<div class="wait">
								<img src="/admin/images/json-load.gif" with="48" height="48" />
								<div><?php echo _("Chargement en cours, merci de patientez..."); ?></div>
							</div>
					<?php } ?>
					<div class="clear"></div>
				</div>
			<?php $index++; } ?>
		</div>

		<?php if( sizeof($ar_images) ){ ?>
		<div class="actions">
			<hr />
			<input type="button" name="save-all" value="<?php echo _("Enregistrer"); ?>" />

			<div class="pagination"><?php
				print ( $page>1 ? '<a data-page="'.($page - 1).'" href="#">&laquo; ' . _("Page précédente") .'</a> | ' : '&nbsp;' );

				for( $i=$page_start ; $i<=$page_stop ; $i++ ){
					if( $i==$page ){
						print '<b>'.$i.'</b>'.( $i==$page_stop ? '' : ' | ' );
					}else{
						print '<a data-page="'.($i).'" href="#">'.$i.'</a>'.( $i==$page_stop ? '' : ' | ' );
					}
				}

				print ( $page<$pages ? ' | <a data-page="'.($page + 1).'" href="#">' . _("Page suivante") .' &raquo;</a>' : '&nbsp;' );
			?></div>
		</div>
		<?php } ?>
	</div>
<?php
	require_once('admin/skin/footer.inc.php');
?>