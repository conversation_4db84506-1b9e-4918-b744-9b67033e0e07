<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS');

	require_once( 'products.inc.php' );
	require_once( 'strings.inc.php' );

	$result = array();
	if( isset($_GET['q']) && $_GET['q']!='' ){
		$search = search3( 1, $_GET['q'], 1, 30, false, false, 6, array('prd') );
		if( $search && ria_mysql_num_rows($search) ){
			while( $r_prd = ria_mysql_fetch_array($search) ){
				$prd = ria_mysql_fetch_array( prd_products_get_simple($r_prd['tag']) );
				$name = $prd['title']!='' ? $prd['title'] :$prd['name'];
				if( $name != '' ){
					$result[] = $prd['ref'].' - '.htmlspecialchars($name).'|'.$prd['ref'].' - '.$name;
				}
			}
		}
	}
	print json_encode($result);
