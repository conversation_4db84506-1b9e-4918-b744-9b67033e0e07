<?php

	/**	\file popup-add-object.php
	 *	Cette popup permet la sélection d'un objet pour l'associer à un document
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS_EDIT');

	require_once('documents.inc.php');
	require_once('advertising.inc.php');
	require_once('erratums.inc.php');

	if( !isset($_GET['doc']) || !doc_documents_exists($_GET['doc']) ){
		$g_error = _("Une erreur s'est produite lors du chargement. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
	}else{
		$limit_for_page = 30;

		$page = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] ? $_GET['page'] : 1;

		if( isset($_GET['sel']) ){
			if( isset($_GET['cls']) && is_numeric($_GET['cls']) ){
				foreach( $_GET['sel'] as $o ){
					if( !doc_objects_add($_GET['doc'], $_GET['cls'], $o) ){
						$error = _("Une erreur inattendue s'est produite lors de l'ajout des liens entres ce document et les objets sélectionnés. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
						break;
					}
				}
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Lier un objet à ce document') . ' - ' . _('Document') . ' - ' . _('Médiathèque'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'popup-iframe');

	require_once('admin/skin/header.inc.php');

	if( isset($g_error) ){
		print '<div class="error">'.nl2br( $g_error ).'</div>';
	}else{
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}

		// Liste des classes auxquelles un document peut être associé
		$ar_classes_ok = array();
		if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_RIGHT') ){
			$ar_classes_ok[] = CLS_PRODUCT;
			$ar_classes_ok[] = CLS_CATEGORY;
			$ar_classes_ok[] = CLS_BRAND;
		}
		if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER') ){
			$ar_classes_ok[] = CLS_USER;
		}
		if( gu_user_is_authorized('_RGH_ADMIN_ORDER') ){
			$ar_classes_ok[] = CLS_ORDER;
		}
		if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE') ){
			$ar_classes_ok[] = CLS_STORE;
		}
		if( gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS') ){
			$ar_classes_ok[] = CLS_CMS;
		}
		if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS') ){
			$ar_classes_ok[] = CLS_NEWS;
		}
		if( gu_user_is_authorized('_RGH_ADMIN_TOOL_BANNER') ){
			$ar_classes_ok[] = CLS_BANNER;
		}
		if( gu_user_is_authorized('_RGH_ADMIN_TOOL_ERRATUM') ){
			$ar_classes_ok[] = CLS_ERRATUM;
		}

?>

	<form action="/admin/documents/popup-add-object.php" method="get">
		<input type="hidden" name="page" value="<?php print $page; ?>" />
		<input type="hidden" name="doc" value="<?php print $_GET['doc']; ?>" />
		<p>
			<?php echo _("À partir d'ici, vous pouvez rattacher le document à un ou plusieurs objets, pour cela commencez par choisir la classe :"); ?>
		</p>
		<p>
			<label for="cls"><?php echo _("Choisissez une classe :"); ?></label><br/>
			<select name="cls" id="cls" onchange="$('#q').val(''); form.submit();"><?php
				print '
					<option value=""></option>
				';

				$rcls = fld_classes_get();
				if( $rcls && ria_mysql_num_rows($rcls) ){
					while( $cls = ria_mysql_fetch_array($rcls) ){
						if( $cls['tenant'] || in_array($cls['id'], $ar_classes_ok) ){
							$selected = isset($_GET['cls']) && $_GET['cls']==$cls['id'] ? 'selected="selected"' : '';
							print '<option '.$selected.' value="'.$cls['id'].'">'.htmlspecialchars( $cls['name'] ).'</option>';
						}
					}
				}

			?></select>
		</p>

		<?php
			if( isset($_GET['cls']) && is_numeric($_GET['cls']) && $_GET['cls'] ){
				$is_cls_linked = fld_classes_is_tenant_linked( $_GET['cls'] );

				if( !$is_cls_linked && !in_array($_GET['cls'], array(CLS_BANNER, CLS_ERRATUM)) ){
					print '
						<p>
							<label for="q">' . _("Rechercher") .' :</label>
							<input type="text" name="q" id="q" value="'.( isset($_GET['q']) ? $_GET['q'] : '' ).'" />
							<input type="submit" name="search" value="' . _("Rechercher"). '" />
						</p>
					';
				}

				if( isset($_GET['q']) && trim($_GET['q'])!='' || $is_cls_linked || in_array($_GET['cls'], array(CLS_BANNER, CLS_ERRATUM)) ){
					// On peut réaliser une recherche
					$ar_results = array();
					switch( $_GET['cls'] ){
						case CLS_PRODUCT :
							$type = 'prd';
							break;
						case CLS_USER :
							$type = 'usr';
							break;
						case CLS_CATEGORY :
							$type = 'prd-cat';
							break;
						case CLS_ORDER :
							$type = 'ord';
							break;
						case CLS_BRAND :
							$type = 'brd';
							break;
						case CLS_STORE :
							$type = 'dlv-str';
							break;
						case CLS_CMS :
							$type = 'cms';
							break;
						case CLS_NEWS :
							$type = 'news';
							break;
					}

					if( isset($type) && trim($type)!='' ){
						$search = search(
							array('seg'=>1, 'keywords'=>$_GET['q'], 'page'=>1, 'limit'=>0, 'published'=>false, 'section'=>false, 'action'=>6, 'type'=>array($type)),
							false, 0, -1, true, false, 0, true
						);

						if( is_array($search) && sizeof($search) ){
							foreach( $search as $s ){
								if( $_GET['cls']==CLS_PRODUCT ){
									$ar_results[] = array(
										'id' => $s['search']['tag'],
										'name' => $s['get']['ref'].' - '.$s['search']['name']
									);
								}else{
									$ar_results[] = array(
										'id' => $s['search']['tag'],
										'name' => $s['search']['name']
									);
								}
							}
						}
					}else{
						switch( $_GET['cls'] ){
							case CLS_BANNER :
								$rb = adv_banners_get();
								if( $rb && ria_mysql_num_rows($rb) ){
									while( $b = ria_mysql_fetch_array($rb) ){
										$ar_results[] = array(
											'id' => $b['id'],
											'name' => $b['name']
										);
									}
								}
								break;
							case CLS_ERRATUM :
								$re = cat_erratums_get();
								if( $re && ria_mysql_num_rows($re) ){
									while( $e = ria_mysql_fetch_array($re) ){
										$ar_results[] = array(
											'id' => $e['id'],
											'name' => $e['prd_name']
										);
									}
								}
							default :
								if( $is_cls_linked ){
									$ro = fld_objects_get( 0, $_GET['cls'] );
									if( $ro && ria_mysql_num_rows($ro) ){
										while( $o = ria_mysql_fetch_array($ro) ){
											$ar_results[] = array(
												'id' => $o['id'],
												'name' => $o['name']
											);
										}
									}
								}
								break;
						}
					}

					// print '<pre>';
					// print_r( $ar_results );
					// print '</pre>';
					// print 'Page : '.$page;

					$pages = sizeof( $ar_results ) ? ceil(sizeof( $ar_results )/$limit_for_page) : 0;
					$page = $page>$pages ? $pages : $page;

					$pmin = $page-5;
					$pmax = $pmin+9;
					$pmin = $pmin<1 ? 1 : $pmin;
					$pmax = $pmax>$pages ? $pages : $pmax;

					print '
						<table class="checklist" id="table-create-link-object">
							<caption>'.( sizeof($ar_results)._(' résultat').( sizeof($ar_results)>1 ? 's' : '') ).'</caption>
							<thead>
								<tr>
									<th id="res-check">
										<input type="checkbox" class="checkbox" onclick="checkAllClick(this);" />
									</th>
									<th id="res-name">' . _("Désignation") .'</th>
								</tr>
							</thead>
							<tbody>
					';

					if( !sizeof($ar_results) ){
						print '
							<tr>
								<td colspan="2">' . _("Aucun résultat") . '</td>
							</tr>
						';
					}else{
						$i = 1;

						$results = array_slice( $ar_results, $limit_for_page*($page-1), $limit_for_page );
						foreach( $results as $id=>$r ){
							if( $i>$limit_for_page ){
								break;
							}

							print '
								<tr>
									<td headers="res-check">
										<input type="checkbox" class="obj-sel-checkbox" name="sel[]" id="sel-'.$r['id'].'" value="'.$r['id'].'" />
									</td>
									<td headers="res-name">
										<label for="sel-'.$r['id'].'">'.htmlspecialchars( $r['name'] ).'</label>
									</td>
								</tr>
							';

							$i++;
						}
					}

					print '
							</tbody>
							<tfoot>
								<tr>
									<td colspan="2">
										<div class="float-left">
											<input type="submit" name="select" value="' . _("Sélectionner") .'" onclick="return !$(\'input.obj-sel-checkbox:checked\').length ? false : true;" />
										</div>
										<div class="float-right">
					';

					if( $pages>1 ){
						$query = array(
							'doc' => $_GET['doc'],
							'cls' => $_GET['cls'],
						);

						if( isset($_GET['q']) ){
							$query['q'] = $_GET['q'];
						}

						$plink = '/admin/documents/popup-add-object.php?'.http_build_query($query);

						if( $page>1 ){
							print '<a href="'.$plink.'&amp;page='.($page-1).'">&laquo; ' . _("Page précédente") . '</a> | ';
						}
						for( $i=$pmin; $i<=$pmax; $i++ ){
							if( $i==$page ){
								print '<b>'.$page.'</b>';
							}else{
								print '<a href="'.$plink.'&amp;page='.($i).'">'.$i.'</a>';
							}

							if( $i<$pmax ){
								print ' | ';
							}
						}
						if( $page<$pages ){
							print ' | <a href="'.$plink.'&amp;page='.($page+1).'">' . _("Page suivante") . ' &raquo;</a>';
						}
					}

					print '
										</div>
									</td>
								</tr>
							</tfoot>
						</table>
					';
				}
			}
		}
		?>
	</form>
<?php
	if( isset($_GET['sel']) && !isset($error) ){
		print '
			<script><!--
				parent.reloadObjects();
			--></script>
		';
	}

	require_once('admin/skin/footer.inc.php');
?>