<?php
	require_once('documents.inc.php');
	require_once('doc.images.inc.php');
	require_once('advertising.inc.php');
	require_once('websites.inc.php');
	require_once('erratums.inc.php');
	unset( $error );

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	if( $_GET['doc']==0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_DOCS_ADD');
	}else{ // $_GET['doc'] != 0
		gu_if_authorized_else_403('_RGH_ADMIN_DOCS_EDIT');
	}

	// Vérifie que le paramètre type est fourni
	if( !isset($_REQUEST['type']) || !doc_types_exists($_REQUEST['type']) ){
		header('Location: types/index.php');
		exit;
	}

	// Validation du paramètre doc (identifiant du document)
	if( isset($_GET['doc']) && $_GET['doc']!=0 ){
		if( !doc_documents_exists($_GET['doc']) ){
			header('Location: index.php?type='.$_REQUEST['type']);
			exit;
		}
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php?type='.$_REQUEST['type']);
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['delete']) ){
		doc_documents_del($_GET['doc']);
		header('Location: index.php?type='.$_REQUEST['type']);
		exit;
	}

	$wst = wst_websites_get( 0, false, null, true );
	// Récupère la langue
	require_once('view.translate.inc.php');
	$lng = view_selected_language();

	// Enregistrement
	if( isset($_POST['save']) ){
		if( !(($wst && ria_mysql_num_rows($wst)>1) || (is_array($config['i18n_lng_used']) && sizeof($config['i18n_lng_used'])>1)) ){
			$_POST['language'] = array();

			while( $r = ria_mysql_fetch_assoc($wst) ){
				$_POST['language'][ $r['id'] ] = array();

				foreach( $config['i18n_lng_used'] as $one_lng ){
					$_POST['language'][ $r['id'] ][] = $one_lng;
				}
			}
		}

		if( !isset($_POST['type']) || !isset($_POST['name']) || !isset($_POST['desc']) || !isset($_POST['language']) ){
			$error = _('Une ou plusieurs informations obligatoires ne sont pas renseignées.');
		}elseif( !isset($_GET['doc']) || $_GET['doc']==0 ){
			// Ajout
			$_GET['doc'] = doc_documents_upload( $_POST['type'], 'file', $_POST['name'], $_POST['desc'] );
			if( !$_GET['doc'] ){
				$error = _("La création du document a échoué pour une raison inconnue.");
			}
		}elseif( isset($_POST['type'],$_POST['name'],$_POST['desc']) ){
			if( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] ){
				// Mise à jour
				$_POST['filename'] = isset($_POST['filename']) ? $_POST['filename'] : '';
				$res = doc_documents_update( $_GET['doc'], $_POST['type'], $_POST['filename'], $_POST['name'], $_POST['desc'] );
				if( !$res ){
					$error = _("La mise à jour du document a échoué pour une raison inconnue.");
				}
			} elseif( in_array($_GET['lng'], $config['i18n_lng_used']) ){
				$values = array(
					_FLD_DOC_NAME=>$_POST['name'],
					_FLD_DOC_DESC=>$_POST['desc'],
					_FLD_DOC_FILENAME=>isset($_POST['filename']) ? $_POST['filename'] : ''
				);

				if( !fld_translates_add($_GET['doc'], $_GET['lng'], $values) ){
					$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
				}
			}

			if( !isset($error) ){
				if( sizeof($_FILES)>0 ){
					doc_documents_update_file( $_GET['doc'], 'file', $_GET['lng'] );
				}
			}
		}

		if( !isset($error) ){
			// Gestion des liens entres documents / sites / langues
			doc_websites_del( $_GET['doc'] );
			if( isset($_POST['language']) && is_array($_POST['language']) && sizeof($_POST['language']) ){
				foreach( $_POST['language'] as $wst=>$langs ){
					foreach( $langs as $one_lng ){
						doc_websites_add( $_GET['doc'], $wst, $one_lng );
					}
				}
			}
		}

		if( !isset($error) && !isset($_POST['tabProducts']) && !isset($_POST['tabCategories']) && !isset($_POST['tabErratums']) ){
			if( !isset($_GET['doc']) || $_GET['doc']==0 ){
				header('Location: index.php?type='.$_POST['type']);
			} else {
				header('Location: edit.php?doc='.$_GET['doc'].'&type='.$_POST['type'].'&lng='.$_GET['lng'].'');
			}
			exit;
		}
	}

	if( isset($_GET['doc']) && is_numeric($_GET['doc']) && $_GET['doc'] > 0 ){
		view_admin_tab_fields_actions( CLS_DOCUMENT, $_GET['doc'], $lng );
	}

	// Bouton supprimer un ou plusieurs objet
	if( isset($_POST['del-obj']) ){
		if( !isset($_POST['del']) ){
			$_POST['del'] = array();
		}

		if( sizeof($_POST['del']) ){
			foreach( $_POST['del'] as $cls=>$objs ){
				if( is_array($objs) && sizeof($objs) ){
					foreach( $objs as $obj ){
						if( !doc_objects_del( $_GET['doc'], $cls, $obj) ){
							$tab = 'objects';
							$error = _("Une erreur inattendue s'est produite lors de la suppression des objets sélectionnés. \nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
						}
					}
				}
			}
		}

		if( !isset($error) ){
			header('Location: /admin/documents/edit.php?doc='.$_GET['doc'].'&type='.$_GET['type'].'&lng='.$_GET['lng'].'&tab=objects');
			exit;
		}
	}

	if( !isset($_GET['tab']) ){
		$_GET['tab'] = 'general';
	}

	$tab = $_GET['tab'];
	if( isset($_POST['tabGeneral']) ){
		$tab = 'general';
	}elseif( isset($_POST['tabProducts']) ){
		$tab = 'products';
	}elseif( isset($_POST['tabCategories']) ){
		$tab = 'categories';
	}elseif( isset($_POST['tabErratums']) ){
		$tab = 'erratums';
	}elseif( isset($_POST['tabObjects']) ){
		$tab = 'objects';
	}elseif( isset($_POST['tabFields']) ){
		$tab = 'fields';
	}elseif( isset($_POST['tabDownloads']) ){
		$tab = 'downloads';
	}elseif( isset($_POST['tabImages']) ){
		$tab = 'images';
	}

	$filterType = isset($_REQUEST['download_type']) ? $_REQUEST['download_type'] : 'all';
	$filterLng = isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng'];

	$page = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] ? $_GET['page'] : 1;

	$doc = array('id'=>0,'name'=>'','filename'=>'','desc'=>'','type_id'=>$_REQUEST['type']);
	if( isset($_GET['doc']) && $_GET['doc']>0 ){
		$doc = ria_mysql_fetch_array(doc_documents_get($_GET['doc'],0,null,'',false));
		// Récupère les informations traduite
		if( $lng!=$config['i18n_lng'] ){
			$tsk_doc = fld_translates_get( CLS_PRODUCT, $doc['id'], $lng, $doc, array(_FLD_DOC_NAME=>'name', _FLD_DOC_DESC=>'desc', _FLD_DOC_FILENAME=>'filename', _FLD_DOC_SIZE=>'size' ), true );
			$doc['name'] = $tsk_doc['name'];
			$doc['desc'] = $tsk_doc['desc'];
			$doc['filename'] = $tsk_doc['filename'];
		}
	}

	$page_title = $doc['name'];

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Médiathèque'), '/admin/documents/index.php?type=0' );

	$parent = doc_types_get_parents_array( $_GET['type'], 'desc' );
	if( is_array($parent) && sizeof($parent) ){
		foreach( $parent as $one_t ){
			$t = ria_mysql_fetch_array( doc_types_get($one_t) );
			Breadcrumbs::add( $t['name'], '/admin/documents/index.php?type='.$t['id'] );
		}
	}

	$type = ria_mysql_fetch_array(doc_types_get($_GET['type']));
	Breadcrumbs::add( $type['name'], '/admin/documents/index.php?type='.$type['id'] )
		->push( $page_title );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', $page_title . ' - ' . _('Document') . ' - ' . _('Médiathèque'));
	require_once('admin/skin/header.inc.php');

?>
<h2><?php print htmlspecialchars( $page_title ); ?></h2>

<?php

	if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}

	$lngs = wst_websites_languages_get_array($config['wst_id']);

	// Affiche le menu de langue
	if( isset($_GET['doc']) && $_GET['doc']>0 ) {
		//print view_translate_menu( 'edit.php?doc='.$doc['id'].'&amp;type='.$doc['type_id'], $lng, true );
		print '<div class="stats-menu">';
		if(sizeof($lngs)>1){
			print view_translate_menu( 'edit.php?type='.$_GET['type'].'&doc=' . $_GET['doc'], $lng );
		}
		if ($tab == 'downloads'){
			print '
					<div id="div-filter-type">
						<select id="filter-type" name="filter-type">
							<option value="all"' . ($filterType === 'all' ? ' selected="selected"' : '') . '>' . _("Tous les téléchargements") . '</option>
							<option value="anonymous"' . ($filterType === 'anonymous' ? ' selected="selected"' : '') . '>' . _("Téléchargements anonymes") . '</option>
							<option value="named"' . ($filterType === 'named' ? ' selected="selected"' : '') . '>' . _("Téléchargements non anonymes") . '</option>
						</select>
					</div>
				';
		}
		print '</div>';

	}
	?>

<form action="edit.php?doc=<?php print $doc['id']; ?>&amp;type=<?php print $doc['type_id']; ?>&amp;lng=<?php print $lng; ?>" method="post" enctype="multipart/form-data" <?php if($tab=='general') print 'onsubmit="return validForm(this)"'; ?>>
<input type="hidden" name="doc-id" id="doc-id" value="<?php print $doc['id']; ?>" />
<input type="hidden" name="is-default-lng" id="is-default-lng" value="<?php print $lng == $config['i18n_lng'] ? '1' : '0'; ?>" />


<ul class="tabstrip">
	<li><input type="submit" name="tabGeneral" value="<?php echo _("Général"); ?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
	<li><input type="submit" name="tabObjects" value="<?php echo _("Objets liés"); ?>" <?php if( $tab=='objects' ) print 'class="selected"'; ?> /></li>
	<?php if( view_admin_show_tab_fields( CLS_DOCUMENT, $_GET['doc'] ) ){ ?>
		<li><input type="submit" name="tabFields" value="<?php echo _("Avancé"); ?>" <?php if( $tab=='fields' ) print 'class="selected"'; ?> /></li>
	<?php } ?>
	<li><input type="submit" name="tabImages" value="<?php echo _("Images"); ?>" <?php if( $tab=='images' ) print 'class="selected"'; ?> /></li>
	<?php if( tnt_tenants_have_websites() ){ ?>
	<li><input type="submit" name="tabDownloads" value="<?php echo _("Téléchargements"); ?>" <?php if( $tab=='downloads' ) print 'class="selected"'; ?> /></li>
	<?php } ?>
</ul>
<div id="tabpanel">
	<?php if( $tab=='general' ){ ?>
		<table>
			<caption><?php echo _("Propriétés du document"); ?></caption>
			<tbody>
				<tr>
					<td><label for="name"><span class="mandatory">*</span> <?php echo _('Désignation :'); ?></label></td>
					<td><input type="text" name="name" id="name" value="<?php print htmlspecialchars($doc['name']); ?>" maxlength="45" /></td>
				</tr>
				<?php
					if( $doc['filename']=='' ){
						print '
							<tr>
								<td><label for="file"><span class="mandatory">*</span> ' . _("Fichier :") . '</label></td>
								<td>
									<input data-size-max="'.return_bytes(ini_get('upload_max_filesize')).'" type="file" name="file" id="file" /> <span class="color-red">('._('Taille max. autorisée :').' '.ini_get("upload_max_filesize").')</span>
								</td>
							</tr>
						';
					}else{
						print '
							<tr>
								<td><label for="filename"><span class="mandatory">*</span> ' . _("Fichier :") . '</label></td>
								<td><input type="text" name="filename" id="filename" value="'.htmlspecialchars($doc['filename']).'" /></td>
							</tr>
							<tr>
								<td><label for="file">' . _("Remplacer par :") . '</label></td>
								<td>
									<input data-size-max="'.return_bytes(ini_get('upload_max_filesize')).'" type="file" name="file" id="file" /> <span class="color-red">('._('Taille max. autorisée :').' '.ini_get("upload_max_filesize").')</span>
								</td>
							</tr>
						';
					}
				?>

				<tr>
					<td><label for="type"><span class="mandatory">*</span> <?php echo _('Type :'); ?></label></td>
					<td>
						<select name="type" id="type">
							<?php
								$selecttype = is_numeric($doc['type_id']) && $doc['type_id'] ? $doc['type_id'] : isset($_REQUEST['type']) ? $_REQUEST['type'] : 0;

								// chargement de tous les niveaux de types de documents
								$type_ar = array();
								$rtype = doc_types_get( 0, false, true, false, false, false, -1 );
								while( $type = ria_mysql_fetch_array($rtype) ){
									$p_id = $type['parent_id'];
									$name_ar = array($type['name']);

									// remonte jusqu'au premier niveau pour chaque type de documents
									// gestion d'une sortie de boucle (sécurité si références circulaires entre types de documents)
									$MAX_LOOP = 10;
									$i = 0;
									while( $p_id && $i < $MAX_LOOP ){
										$rptype = doc_types_get( $p_id );
										if( $rptype && ria_mysql_num_rows($rptype) ){
											// charge le nom du parent
											$ptype = ria_mysql_fetch_assoc($rptype);
											$name_ar[] = $ptype['name'];
											$p_id = $ptype['parent_id'];
										}else{
											$p_id = 0;
										}
										$i++;
									}
									$type_ar[] = array( 'name' => implode( ' >> ', array_reverse($name_ar) ), 'data' => $type );
								}

								// tri par nom
								$type_ar = array_msort( $type_ar, array('name' => SORT_ASC) );

								foreach( $type_ar as $type ){
									print '<option value="'.$type['data']['id'].'" '.( $type['data']['id']==$selecttype ? ' selected="selected"' : '' ).'>'.htmlspecialchars( $type['name'] ).'</option>';
								}
							?>
						</select>
					</td>
				</tr>

				<tr>
					<td><label for="desc"><?php echo _('Description :'); ?></label></td>
					<td><textarea name="desc" id="desc" cols="50" rows="5"><?php print htmlspecialchars($doc['desc']); ?></textarea></td>
				</tr>

				<?php
					print view_admin_form_websites_languages( CLS_DOCUMENT, $doc['id'], true );

					// Le bloc "Segmentation" n'apparaît que si le document est enregistré et que l'utilisateur à accès aux segments
					if( $doc['id']>0 && gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_SEGMENT') ){
				?>
				<tr>
					<th colspan="2"><?php echo _("Segmentation"); ?></th>
				</tr>
				<tr>
					<td><?php echo _("Segments"); ?></td>
					<td><?php
						$ar_ex_seg = array();

						require_once('segments.inc.php');
						print '
							<input type="hidden" name="seg-obj-cls" id="seg-obj-cls" value="'.CLS_DOCUMENT.'" />
							<input type="hidden" name="seg-obj-id-0" id="seg-obj-id-0" value="'.$doc['id'].'" />

							<div class="seg-obj-infos">
						';
						$robject = seg_objects_get_segments( CLS_DOCUMENT, array($doc['id']) );
						if( $robject &&  ria_mysql_num_rows($robject) ){
							while( $obj = ria_mysql_fetch_array($robject) ){
								$ar_ex_seg[] = $obj['id'];
								print '	<input class="del-obj-seg" type="image" name="del-seg-'.$obj['id'].'" src="/admin/images/del-cat.png" title="' . _("Retirer ce segment") . '" alt="Supprimer" />&nbsp;';
								print htmlspecialchars( $obj['name'] ).'<br />';
							}
						} else { print _('Aucune restriction liée aux segments.'); }
						print '	</div>';
					?></td>
				</tr>
				<tr>
					<td><label><?php echo _("Ajouter :"); ?></label></td>
					<td>
					<?php
						$rseg = seg_segments_get( 0, CLS_USER );
						if( $rseg && ria_mysql_num_rows($rseg) ){ ?>
						<select class="select-obj-seg" name="segment" id="segment">
							<option value="-1"><?php echo _("Choisir un segment"); ?></option>
							<?php
								$rseg = seg_segments_get( 0, CLS_USER );
								if( $rseg && ria_mysql_num_rows($rseg) ){
									while( $seg = ria_mysql_fetch_array($rseg) ){
										if( in_array($seg['id'], $ar_ex_seg) ) continue;
										print '<option value="'.$seg['id'].'">'.htmlspecialchars( $seg['name'] ).'</option>';
									}
								}
							?>
						</select>
						<?php
						}else{
							print _('Aucun segment enregistré'); ?>
							<br/>
							<a href="/admin/config/fields/segments/segment.php?cls=2&id=0" class="button" target="_BLANK"><?php print _('Créer un segment'); ?></a> <?php
						} ?>
					</td>
				</tr>
				<?php } ?>
			</tbody>
			<tfoot>
				<tr><td colspan="2">
					<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" />
					<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" onclick="return cancelEdit()" />
					<?php if( $doc['id'] && gu_user_is_authorized('_RGH_ADMIN_DOCS_DEL') ){ ?>
					<input type="submit" name="delete" value="<?php echo _("Supprimer"); ?>" onclick="return confirmDel();" />
					<?php } ?>
				</td></tr>
			</tfoot>
		</table>
	<?php }elseif( $tab=='images' ){ ?>
		<?php print view_admin_img_table( CLS_DOCUMENT, $doc['id']); ?>
	<?php }elseif( $tab=='objects' ){ ?>
		<p><?php echo _("Vous trouverez ci-dessous tous les objets liés à ce document."); ?></p>
		<?php
			// Récupère tous les objects rattachés au document
			$robj = doc_objects_get( $doc['id'] );

			$ar_cls = array();
			if( $robj && ria_mysql_num_rows($robj) ){
				while($obj = ria_mysql_fetch_array($robj) ){
					// Classes supportées (à compléter au besoin)
					if( $obj['cls_tnt_id']==0 && !in_array($obj['cls_id'], array(CLS_PRODUCT, CLS_USER, CLS_CATEGORY, CLS_ORDER, CLS_BRAND, CLS_STORE, CLS_BANNER, CLS_ERRATUM, CLS_CMS, CLS_NEWS)) ){
						continue;
					}

					if( !isset($ar_cls[ $obj['cls_id'] ]) ){
						$ar_cls[ $obj['cls_id'] ] = array(
							'name' => $obj['cls_name'],
							'objects' => array()
						);
					}

					switch( $obj['cls_id'] ){
						case CLS_PRODUCT :
							$rp = prd_products_get_simple( $obj['obj_id_0'] );
							if( $rp && ria_mysql_num_rows($rp) ){
								$p = ria_mysql_fetch_array( $rp );

								$ar_cls[ $obj['cls_id'] ]['objects'][] = array(
									'id' => $p['id'],
									'name' => $p['ref'].' - '.$p['name'],
									'is_sync' => view_prd_is_sync( $p ),
									'link' => '/admin/catalog/product.php?cat=0&amp;prd='.$p['id']
								);
							}
							break;
						case CLS_CATEGORY :
							$rc = prd_categories_get( $obj['obj_id_0'] );
							if( $rc && ria_mysql_num_rows($rc) ){
								$c = ria_mysql_fetch_array( $rc );

								$ar_cls[ $obj['cls_id'] ]['objects'][] = array(
									'id' => $c['id'],
									'name' => $c['name'],
									'is_sync' => view_cat_is_sync( $c ),
									'link' => '/admin/catalog/edit.php?cat='.$c['id']
								);
							}
							break;
						case CLS_USER :
							$ru = gu_users_get( $obj['obj_id_0'] );
							if( $ru && ria_mysql_num_rows($ru) ){
								$u = ria_mysql_fetch_array( $ru );

								$ar_cls[ $obj['cls_id'] ]['objects'][] = array(
									'id' => $u['id'],
									'name' => trim( $u['title_name'].' '.$u['adr_firstname'].' '.$u['adr_lastname'].' '.$u['society'] ),
									'is_sync' => view_usr_is_sync( $u ),
									'link' => '/admin/customers/edit.php?usr='.$u['id']
								);
							}
							break;
						case CLS_ORDER :
							$ro = ord_orders_get( 0, $obj['obj_id_0'] );
							if( $ro && ria_mysql_num_rows($ro) ){
								$o = ria_mysql_fetch_array( $ro );

								$ar_cls[ $obj['cls_id'] ]['objects'][] = array(
									'id' => $o['id'],
									'name' => ord_orders_name( '', $o['piece'], $o['id'] ),
									'is_sync' => view_ord_is_sync( $o ),
									'link' => '/admin/orders/order.php?ord='.$o['id'].'&amp;state=0'
								);
							}
							break;
						case CLS_BANNER :
							$rb = adv_banners_get( 0, $obj['obj_id_0'] );
							if( $rb && ria_mysql_num_rows($rb) ){
								$b = ria_mysql_fetch_array( $rb );

								$ar_cls[ $obj['cls_id'] ]['objects'][] = array(
									'id' => $b['id'],
									'name' => $b['name'],
									'is_sync' => null,
									'link' => '/admin/tools/banners/edit.php?id='.$b['id']
								);
							}
							break;
						case CLS_STORE :
							$rs = dlv_stores_get( $obj['obj_id_0'] );
							if( $rs && ria_mysql_num_rows($rs) ){
								$s = ria_mysql_fetch_array( $rs );

								$ar_cls[ $obj['cls_id'] ]['objects'][] = array(
									'id' => $s['id'],
									'name' => $s['name'],
									'is_sync' => view_str_is_sync( $s ),
									'link' => '/admin/config/livraison/stores/edit.php?str='.$s['id']
								);
							}
							break;
						case CLS_BRAND :
							$rb = prd_brands_get( $obj['obj_id_0'] );
							if( $rb && ria_mysql_num_rows($rb) ){
								$b = ria_mysql_fetch_array( $rb );

								$ar_cls[ $obj['cls_id'] ]['objects'][] = array(
									'id' => $b['id'],
									'name' => $b['name'],
									'is_sync' => view_brd_is_sync( $b ),
									'link' => '/admin/catalog/brands/edit.php?brd='.$b['id']
								);
							}
							break;
						case CLS_ERRATUM :
							$re = cat_erratums_get( $obj['obj_id_0'] );
							if( $re && ria_mysql_num_rows($re) ){
								$e = ria_mysql_fetch_array( $re );

								$ar_cls[ $obj['cls_id'] ]['objects'][] = array(
									'id' => $e['id'],
									'name' => $e['prd_name'],
									'is_sync' => null,
									'link' => '/admin/tools/erratums/edit.php?id='.$e['id']
								);
							}
							break;
						case CLS_CMS :
							$rc = cms_categories_get( $obj['obj_id_0'] );
							if( $rc && ria_mysql_num_rows($rc) ){
								$c = ria_mysql_fetch_array( $rc );

								$ar_cls[ $obj['cls_id'] ]['objects'][] = array(
									'id' => $c['id'],
									'name' => $c['name'],
									'is_sync' => null,
									'link' => '/admin/tools/cms/edit.php?cat='.$c['id']
								);
							}
							break;
						case CLS_NEWS :
							$rn = news_get( $obj['obj_id_0'] );
							if( $rn && ria_mysql_num_rows($rn) ){
								$n = ria_mysql_fetch_array( $rn );

								$ar_cls[ $obj['cls_id'] ]['objects'][] = array(
									'id' => $n['id'],
									'name' => $n['name'],
									'is_sync' => null,
									'link' => '/admin/tools/news/edit.php?news='.$n['id']
								);
							}
							break;
						default :
							if( $obj['cls_tnt_id']!=0 ){
								$ro = fld_objects_get( $obj['obj_id_0'], $obj['cls_id'] );
								if( $ro && ria_mysql_num_rows($ro) ){
									$o = ria_mysql_fetch_array( $ro );

									$ar_cls[ $obj['cls_id'] ]['objects'][] = array(
										'id' => $o['id'],
										'name' => $o['name'],
										'is_sync' => null,
										'link' => '/admin/config/fields/classes/object.php?cls='.$obj['cls_id'].'&obj='.$o['id']

									);
								}
							}
							break;
					}
				}
			}

			print '
				<table id="table-objets-associes" class="checklist">
					<caption>' . _('Objets liés') . '</caption>
					<tbody>
			';

			if( !sizeof($ar_cls) ){
				print '
					<tr>
						<td colspan="2">' . _("Aucun objet n'est rattaché à ce document.") . '</td>
					</tr>
				';
			}else{
				foreach( $ar_cls as $cls_id=>$cls_infos ){
					if( !is_array($cls_infos['objects']) || !sizeof($cls_infos['objects']) ){
						continue;
					}

					print '
							<tr>
								<th data-label="'._('Tout cocher :').'" id="obj-sel"><input type="checkbox" class="checkbox check-all-objects" id="check-obj-'.$cls_id.'" /></th>
								<th class="thead-none" id="obj-name">'.htmlspecialchars( $cls_infos['name'] ).'</th>
							</tr>
					';

					foreach( $cls_infos['objects'] as $obj ){
						print '
							<tr>
								<td headers="obj-sel">
									<input class="check-obj-'.$cls_id.'" type="checkbox" name="del['.$cls_id.'][]" value="'.$obj['id'].'" />
								</td>
								<td headers="obj-name-'.$cls_id.'">
									<a href="'.$obj['link'].'">'.$obj['is_sync'].'&nbsp;'.htmlspecialchars( $obj['name'] ).'</a>
								</td>
							</tr>
						';
					}
				}
			}

			print '
					</tbody>
					<tfoot>
						<tr>
							<td colspan="2" class="align-left">
								<input type="submit" name="del-obj" value="' . _("Supprimer") . '" />
								<div class="float-right">
									<input type="button" name="add-obj" id="add-obj" value="' . _("Ajouter") . '" class="btn-main" />
								</div>
							</td>
						</tr>
					</tfoot>
				</table>
			';
	} elseif ($tab == 'fields') {
		print view_admin_tab_fields( CLS_DOCUMENT, $_GET['doc'], $lng );
	} elseif ($tab == 'downloads') {
			$user_filter = "all";
			if (isset($_GET['download_type'])){
				$user_filter = $_GET['download_type'];
			}
			$limit = 25;

			$total_rows = doc_downloads_couchdb_get_count($_GET['doc'], $user_filter, $lng);

			//Calcule le nombre de pages
			$pages = ceil($total_rows / $limit);

			// Détermine la page en cours de consultation
			$page = 1;
			if( isset($_GET['page']) && is_numeric($_GET['page']) ){
				if( $_GET['page']>0 && $_GET['page']<=$pages )
					$page = $_GET['page'];
			}

			$start = $limit * ($page - 1);

			$ar_couchDb_dl = doc_downloads_couchdb_get($_GET['doc'], $start, $limit, $user_filter, $lng);

			$ar_downloads = array();
			foreach ($ar_couchDb_dl as $download) {
				$user_information = '';
				if($download['user_id'] != ''){
					$r_user = gu_users_get($download['user_id']);

					if ($r_user && $user = ria_mysql_fetch_assoc($r_user)){
						$user_information = view_usr_is_sync($user).' <a href="../customers/edit.php?usr='.$user['id'].'" title="' . _("Afficher la fiche de cet utilisateur") . '">';
						if( $user['adr_invoices'] ){
							if( $user['type_id']==1 ){ // Particulier
								$user_information .= htmlspecialchars($download['user_lastname']).', '.htmlspecialchars($download['user_firstname']);
							}elseif( $user['type_id']==2 ){ // Société
								$user_information .= htmlspecialchars($download['society']);
							}else{ // Professionnel ou autre
								if( $download['user_society']!='' ) {
									$user_information .= htmlspecialchars($download['user_society']).', ';
								}
								$user_information .= htmlspecialchars($download['user_lastname']).', '.htmlspecialchars($download['user_firstname']);
							}
						}else{
							$user_information .= $user['user_email'];
						}
						$user_information .= '</a>';
					}

				} else {
					$user_information = $download['user_firstname'];
				}


				$ar_downloads[] = array(
					'date' => $download['date_download'],
					'user_id' => $download['user_id'],
					'user_information' => $user_information,
					'doc_lng' => $download['doc_lng'],
					'doc_wst' => $download['doc_website'],
					'md5_default' => $download['doc_md5_default'],
				);
			}
			// usort($ar_downloads, "ria_date_sort_desc");

			//Détermine les limites inférieures et supérieures pour l'affichage des pages
			$pmin = $page-5;
			if( $pmin<1 )
				$pmin = 1;
			$pmax = $pmin+9;
			if( $pmax>$pages )
				$pmax = $pages;

		?>
		<table class="checklist" id="lst-downloads">
			<caption><?php echo _("Historique des téléchargements"); ?> <?php print '('.$total_rows.' '.( $total_rows > 1 ? _('téléchargements') : _('téléchargement')).')' ?></caption>
			<?php if (isset($ar_downloads) && $total_rows) { ?>
			<thead>
				<tr>
					<th class="th-lst-dl-150"><?php echo _("Date"); ?></th>
					<th class="th-lst-dl-150"><?php echo _("Compte"); ?></th>
					<th class="th-lst-dl-250"><?php echo _("Site"); ?></th>
					<th class="th-lst-dl-100"><?php echo _("Langue"); ?></th>
					<th class="th-lst-dl-150"><?php echo _("Version"); ?></th>
				</tr>
			</thead>
			<tbody>
			<?php
				foreach ($ar_downloads as $key => $download) {
					$version = $download['md5_default'] != $doc['md5_content'] ? _('Ancienne version') : 'Actuelle';
					$date = ria_date_format($download['date']);
					print '
						<tr>
							<td>'.$date.'</td>
							<td>'.$download['user_information'].'</td>
							<td>'.wst_websites_get_name($download['doc_wst']).'</td>
							<td>'.i18n_languages_get_name($download['doc_lng']).'</td>
							<td>'.$version.'</td>
						</tr>
					';
				}

			?>
			</tbody>
			<?php } else { ?>
			<tbody>
				<tr><td colspan="3"><?php echo _("Il n'y a aucun téléchargement pour ce document"); ?></td></tr>
			</tbody>
			<?php }
			if( $pages ){ ?>
				<tfoot>
					<tr id="pagination">
						<td class="page"><?php print _('Page')?> <?php print $page ?>/<?php print $pages; ?></td>
						<td colspan="4" class="pages">
							<?php
								if( $pages>1 ){
									if( $page>1 )
										print '<a href="edit.php?doc='.$_GET['doc'].'&type='.$_GET['type'].'&page='.($page-1).($user_filter != 'all' ? '&download_type='.$user_filter : '').(isset($_GET['lng']) ? '&lng='.$_GET['lng'] : '').'&tab=downloads">&laquo; '._('Page précédente').'</a> | ';
									for( $i=$pmin; $i<=$pmax; $i++ ){
										if( $i==$page )
											print '<b>'.$page.'</b>';
										else
											print '<a href="edit.php?doc='.$_GET['doc'].'&type='.$_GET['type'].'&page='.$i.($user_filter != 'all' ? '&download_type='.$user_filter : '').(isset($_GET['lng']) ? '&lng='.$_GET['lng'] : '').'&tab=downloads">'.$i.'</a>';
										if( $i<$pmax )
											print ' | ';
									}
									if( $page<$pages )
										print ' | <a href="edit.php?doc='.$_GET['doc'].'&type='.$_GET['type'].'&page='.($page+1).($user_filter != 'all' ? '&download_type='.$user_filter : '').(isset($_GET['lng']) ? '&lng='.$_GET['lng'] : '').'&tab=downloads">'._('Page suivante').' &raquo;</a>';
								}
							?>
						</td>
					</tr>
				</tfoot>
			<?php } ?>
		</table>
	<?php } ?>
</div>
</form>
<script>
	var segClsID = <?php print CLS_DOCUMENT; ?>;
	var passe = false;
	var inp = '';
	var tab = "<?php print $tab; ?>";
</script>
<?php
	require_once('admin/skin/footer.inc.php');
