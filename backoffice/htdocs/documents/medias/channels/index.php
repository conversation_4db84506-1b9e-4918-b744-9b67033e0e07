<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS');

	require_once('medias.inc.php');

	// Charge l'hébergement externe (obligatoire)
	if( !isset($_GET['hst']) || !doc_hosts_exists($_GET['hst']) ){
		header('Location: /admin/documents/medias/hosts.php');
		exit;
	}
	$host = ria_mysql_fetch_assoc( doc_hosts_get($_GET['hst']) );

	// Charge la chaîne (obligatoire)
	if( !isset($_GET['chl']) || !doc_channels_exists($_GET['chl']) ){
		header('Location: /admin/documents/medias/index.php?hst='.$host['id']);
		exit;
	}
	$channel = ria_mysql_fetch_assoc( doc_channels_get(0, $_GET['chl']) );

	// Bouton Ajouter une playlist
	if( isset($_POST['addpls']) ){
		header('Location: /admin/documents/medias/playlists/edit.php?hst='.$host['id'].'&chl='.$channel['id'].'&pls=0');
		exit;
	}

	// Bouton Ajouter un média
	if( isset($_POST['addmed']) ){
		header('Location: /admin/documents/medias/medias/edit.php?hst='.$host['id'].'&chl='.$channel['id'].'&med=0');
		exit;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Médiathèque'), '/admin/documents/index.php' )
		->push( _('Hébergeurs externes'), '/admin/documents/medias/hosts.php' )
		->push( $host['name'], '/admin/documents/medias/index.php?hst='.$host['id'] )
		->push( $channel['name'] );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', htmlspecialchars( $channel['name'] ).' - '._('Hébergeurs'));
	require_once('admin/skin/header.inc.php');

	print '
		<h2>'.htmlspecialchars( $channel['name'] ).'<a class="edit-cat" href="/admin/documents/medias/channels/edit.php?hst='.$host['id'].'&amp;chl='.$channel['id'].'">' . _("Modifier cette chaine") . '</a></h2>

		<form action="/admin/documents/medias/channels/index.php?hst='.$host['id'].'&amp;chl='.$channel['id'].'" method="post">
			<table class="checklist">
				<caption>' . _("Liste des playlists") . '</caption>
				<col width="30" /><col width="350" />
				<thead>
					<tr>
						<th id="pls-sel">
							<input type="checkbox" onclick="checkAllClick(this)" class="checkbox" />
						</th>
						<th id="pls-name">' . _("Désignation") . '</th>

					</tr>
				</thead>
				<tfoot>
					<tr>
						<td colspan="2">
							<input type="submit" value="' . _("Supprimer") . '" name="delpls"  class="float-left"/>
							<input type="submit" value="' . _("Ajouter") . '" name="addpls" />
						</td>
					</tr>
				</tfoot>
				<tbody>
	';

	$rplaylist = doc_playlists_get( $host['id'], $channel['id'] );
	if( !$rplaylist || !ria_mysql_num_rows($rplaylist) ){
		print '
					<tr>
						<td colspan="2">
							' . _("Aucune playlist n'est rattachée à cet hébergeur") . '
						</td>
					</tr>
		';
	}else{
		while( $playlist = ria_mysql_fetch_assoc($rplaylist) ){
			print '
					<tr>
						<td headers="pls-sel" class="td-checkbox">
							<input type="checkbox" value="'.$playlist['id'].'" name="pls[]" class="checkbox" />
						</td>
						<td headers="pls-name">
							<a title="' . _("Afficher les informations sur cette playlist") . '" href="/admin/documents/medias/playlists/index.php?hst='.$host['id'].'&amp;chl='.$channel['id'].'&amp;pls='.$playlist['id'].'">'.htmlspecialchars( $playlist['name'] ).'</a>
						</td>
					</tr>
			';
		}
	}

	print '
				</tbody>
			</table>
	';

	print '
			<table class="checklist">
				<caption>' . _("Liste des médias (videos, audios, images...)") . '</caption>
				<col width="30" /><col width="350" />
				<thead>
					<tr>
						<th id="med-sel">
							<input type="checkbox" onclick="checkAllClick(this)" class="checkbox" />
						</th>
						<th id="med-name">' . _("Désignation") . '</th>

					</tr>
				</thead>
				<tfoot>
					<tr>
						<td colspan="2">

							<input type="submit" value="' . _("Supprimer") . '" name="delmed" class="float-left"/>
							<input type="submit" value="' . _("Ajouter") . '" name="addmed" />
						</td>
					</tr>
				</tfoot>
				<tbody>
	';

	$rmedia = doc_medias_get( $host['id'], 0, $channel['id'], 0, null );
	if( !$rmedia || !ria_mysql_num_rows($rmedia) ){
		print '
					<tr>
						<td colspan="2">
							' . _("Aucun médias n'est rattaché à cet hébergeur") . '
						</td>
					</tr>
		';
	}else{
		while( $media = ria_mysql_fetch_assoc($rmedia) ){
			print '
					<tr>
						<td headers="med-sel" class="td-checkbox">
							<input type="checkbox" value="'.$media['id'].'" name="med[]" class="checkbox" />
						</td>
						<td headers="med-name">
							<a title="' . _("Afficher les informations sur ce média") . '" href="/admin/documents/medias/medias/index.php?chl='.$channel['id'].'&amp;med='.$media['id'].'">'.htmlspecialchars( $media['name'] ).'</a>
						</td>
					</tr>
			';
		}
	}

	print '
				</tbody>
			</table>
		</form>
	';

	require_once('admin/skin/footer.inc.php');
