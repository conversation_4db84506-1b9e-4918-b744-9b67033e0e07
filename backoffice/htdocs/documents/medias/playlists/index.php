<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS');

	require_once('medias.inc.php');

	if( !isset($_GET['hst']) || !doc_hosts_exists($_GET['hst']) ){
		header('Location: /admin/documents/medias/hosts.php');
		exit;
	}

	$host = ria_mysql_fetch_assoc( doc_hosts_get($_GET['hst']) );
	$chl_id = isset($_GET['chl']) && is_numeric($_GET['chl']) && $_GET['chl']>0 ? $_GET['chl'] : 0;

	if( !isset($_GET['pls']) || !doc_playlists_exists($_GET['pls']) ){
		header('Location: /admin/documents/medias/channels/index.php?hst='.$host['id'].'&chl='.$chl_id);
		exit;
	}

	$playlist = ria_mysql_fetch_assoc( doc_playlists_get($host['id'], 0, $_GET['pls']) );

	if( isset($_POST['addmed']) ){
		header('Location: /admin/documents/medias/medias/edit.php?hst='.$host['id'].'&chl='.$chl_id.'&pls='.$playlist['id'].'&med=0');
		exit;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Médiathèque'), '/admin/documents/index.php' )
		->push( _('Hébergeurs externes'), '/admin/documents/medias/hosts.php' )
		->push( $host['name'], '/admin/documents/medias/index.php?hst='.$host['id'] )
		->push( $playlist['name'] );

	define('ADMIN_PAGE_TITLE', htmlspecialchars( $playlist['name'] ).' - ' . _('Hébergeurs'));
	require_once('admin/skin/header.inc.php');

	print '
		<h2>'.htmlspecialchars( $playlist['name'] ).'<a class="edit-cat" href="/admin/documents/medias/playlists/edit.php?hst='.$host['id'].'&amp;chl='.$chl_id.'&amp;pls='.$playlist['id'].'">' . _("Modifier cette playlist") . '</a></h2>

		<form action="/admin/documents/medias/playlists/index.php?hst='.$host['id'].'&amp;chl='.$chl_id.'&amp;pls='.$playlist['id'].'" method="post">
			<table class="checklist">
				<caption>' . _("Liste des médias (videos, audios, images...)") . '</caption>
				<col width="30" /><col width="350" />
				<thead>
					<tr>
						<th id="med-sel">
							<input type="checkbox" onclick="checkAllClick(this)" class="checkbox" />
						</th>
						<th id="med-name">'. _("Désignation") . '</th>

					</tr>
				</thead>
				<tfoot>
					<tr>
						<td colspan="2">
							<div class="left">
								<input type="submit" value="' . _("Supprimer") . '" name="delmed" />
							</div>
							<input type="submit" value="' . _("Ajouter") . '" name="addmed" />
						</td>
					</tr>
				</tfoot>
				<tbody>
	';

	$rmedia = doc_medias_get( $host['id'], $playlist['id'], 0, 0, null );
	if( !$rmedia || !ria_mysql_num_rows($rmedia) ){
		print '
					<tr>
						<td colspan="2">
							' . _("Aucun médias n'est rattaché à cet hébergeur"). '
						</td>
					</tr>
		';
	}else{
		while( $media = ria_mysql_fetch_assoc($rmedia) ){
			print '
					<tr>
						<td headers="med-sel" class="td-checkbox">
							<input type="checkbox" value="'.$media['id'].'" name="med[]" class="checkbox" />
						</td>
						<td headers="med-name">
							<a title="' . _("Afficher les informations sur ce média") . '" href="/admin/documents/medias/medias/edit.php?hst='.$host['id'].'&amp;chl='.$chl_id.'&amp;pls='.$playlist['id'].'&amp;med='.$media['id'].'">'.htmlspecialchars( $media['name'] ).'</a>
						</td>
					</tr>
			';
		}
	}

	print '
				</tbody>
			</table>
		</form>
	';

	require_once('admin/skin/footer.inc.php');
