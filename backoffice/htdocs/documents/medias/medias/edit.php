<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS');

	require_once('medias.inc.php');

	if( !isset($_GET['hst']) || !doc_hosts_exists($_GET['hst']) ){
		header('Location: /admin/documents/medias/hosts.php');
		exit;
	}

	$host = ria_mysql_fetch_assoc( doc_hosts_get($_GET['hst']) );
	$chl_id = isset($_GET['chl']) && is_numeric($_GET['chl']) && $_GET['chl'] ? $_GET['chl'] : 0;
	$pls_id = isset($_GET['pls']) && is_numeric($_GET['pls']) && $_GET['pls'] ? $_GET['pls'] : 0;
	$med_id = isset($_GET['med']) && is_numeric($_GET['med']) && $_GET['med'] ? $_GET['med'] : 0;

	$title = _('Nouveau média');
	$media = array(
		'id' => 0, 'name' => '', 'desc' => '', 'import_id' => '', 'url' => '', 'date_publish' => ''
	);

	if( doc_medias_exists($med_id) ){
		$media = ria_mysql_fetch_assoc( doc_medias_get($host['id'], 0, 0, $med_id, null) );
		$title = 'Média '.$media['name'];
	}elseif( $med_id>0 ){
		$_POST['cancel'] = true;
	}

	if( isset($_POST['save']) ){
		if( !isset($_POST['name'], $_POST['desc'], $_POST['import_id'], $_POST['url'], $_POST['publish']) ){
			$error = _("Une ou plusieurs informations sont manquantes");
		}elseif( trim($_POST['name'])=='' ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes. \nMerci de renseigner tous les champs notés avec un <span class=\"mandatory\">*</span>");
		}else{
			if( $med_id>0 ){
				if( !doc_medias_update($med_id, $_POST['name'], $_POST['desc'], $_POST['import_id'], $_POST['url'], null, ($_POST['publish'] ? true : false)) ){
					$error = _("Une erreur inattendue s'est produite lors de la mise à jour du média. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
				}else{
					$_SESSION['save-media'] = true;
				}
			}else{
				$med_id = doc_medias_add( $host['id'], $_POST['name'], $_POST['desc'], $_POST['import_id'], $_POST['url'], 0, ($_POST['publish'] ? true : false) );
				if( !$med_id ){
					$error = _("Une erreur inattendue s'est produite lors de l'ajout du média. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
				}
			}

			if( $med_id ){
				if( !doc_channels_medias_del(0, $med_id) ){
					$error = _("Une erreur inattendue s'est produite lors du rattachement de ce média à la ou les chaines choisies. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
				}elseif( isset($_POST['channels']) && is_array($_POST['channels']) ){
					foreach( $_POST['channels'] as $one_channel){
						if( !doc_channels_medias_add($one_channel, $med_id) ){
							$error = _("Une erreur inattendue s'est produite lors du rattachement de média à la ou les chaines choisies. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
						}
					}
				}

				if( !doc_playlists_medias_del(0, $med_id) ){
					$error = _("Une erreur inattendue s'est produite lors du rattachement de ce média à la ou les playlists choisies. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
				}elseif( isset($_POST['playlists']) && is_array($_POST['playlists']) ){
					foreach( $_POST['playlists'] as $one_channel){
						if( !doc_playlists_medias_add($one_channel, $med_id) ){
							$error = _("Une erreur inattendue s'est produite lors du rattachement de média à la ou les playlists choisies. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
						}
					}
				}
			}
		}

		if( !isset($error) ){
			$_SESSION['save-media'] = true;
			header('Location: /admin/documents/medias/medias/edit.php?hst='.$host['id'].'&chl='.$chl_id.'&pls='.$pls_id.'&med='.$med_id);
			exit;
		}
	}

	if( isset($_POST['delete']) ){
		if( !doc_playlists_del($pls_id) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression de la playlist. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
		}

		if( !isset($error) ){
			if( $chl_id ){
				header('Location: /admin/documents/medias/channels/index.php?hst='.$host['id'].'&chl='.$chl_id);
			}else{
				header('Location: /admin/documents/medias/index.php?hst='.$host['id']);
			}
			exit;
		}
	}

	if( isset($_POST['cancel']) ){
		header('Location: /admin/documents/medias/playlists/index.php?hst='.$host['id'].'&chl='.$chl_id.'&pls='.$pls_id);
		exit;
	}

	if( sizeof($_POST) ){
		$media = array(
			'id' 			=> isset($_POST['id']) ? $_POST['id'] : 0,
			'name' 			=> isset($_POST['name']) ? $_POST['name'] : '',
			'desc' 			=> isset($_POST['desc']) ? $_POST['desc'] : '',
			'import_id' 	=> isset($_POST['import_id']) ? $_POST['import_id'] : '',
			'url' 			=> isset($_POST['url']) ? $_POST['url'] : '',
			'date_publish' 	=> isset($_POST['publish']) && $_POST['publish'] ? date('Y-m-d H:i:s') : false
		);
	}

	// Récupère les chaines et les playlists dans lesquels se trouve une vidéo
	$ar_channels = array(); $ar_playlists = array();

	if( $media['id']>0 ){
		$rchannel = doc_channels_medias_get( 0, $media['id'] );
		if( $rchannel ){
			while( $channel = ria_mysql_fetch_assoc($rchannel) ){
				$ar_channels[ $channel['chl_id'] ] = $channel;
			}
		}

		$rplaylist = doc_playlists_medias_get( $media['id'] );
		if( $rplaylist ){
			while( $playlist = ria_mysql_fetch_assoc($rplaylist) ){
				$ar_playlists[ $playlist['pls_id'] ] = $playlist;
			}
		}
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Médiathèque'), '/admin/documents/index.php' )
		->push( _('Hébergeurs externes'), '/admin/documents/medias/hosts.php' )
		->push( $host['name'], '/admin/documents/medias/index.php?hst='.$host['id'] )
		->push( $title );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', $title.' - '.$host['name'].' - ' . _('Hébergeurs'));
	require_once('admin/skin/header.inc.php');

	print '
		<h2>'.htmlspecialchars( $title ).'</h2>
	';

	if( isset($error) ){
		print '
			<div class="error">'.nl2br($error).'</div>
		';
	}elseif( isset($_SESSION['save-media']) ){
		unset( $_SESSION['save-media'] );
		print '
			<div class="success">' . _("Les informations ont bien été mises à jour.") . '</div>
		';
	}

	print '
		<form action="/admin/documents/medias/medias/edit.php?hst='.$host['id'].'&amp;chl='.$chl_id.'&amp;pls='.$pls_id.'&amp;med='.$media['id'].'" method="post">
			<table id="table-edit-hosts">
				<thead>
					<tr>
						<th colspan="2">' . _("Général") . '</th>
					</tr>
				</thead>
				<tfoot>
					<tr>
						<td colspan="2">
							<input type="submit" value="' . _("Enregistrer") . '" name="save" />
							<input type="submit" value="' . _("Annuler") . '" name="cancel" />
							'.( $med_id>0 ? '<input type="submit" value="' . _("Supprimer") . '" name="delete" onclick="return confirmDeleteMedia();" />' : '' ).'
						</td>
					</tr>
				</tfoot>
				<tbody>
					<tr>
						<td id="td-edit-hosts-150">
							<span class="mandatory">*</span>
							<label for="name">' . _('Désignation :') . '</label>
						</td>
						<td id="td-edit-hosts-350">
							<input type="text" name="name" value="'.htmlspecialchars($media['name']).'" />
						</td>
					</tr>
					<tr>
						<td><label for="desc">' . _('Description :') . '</label></td>
						<td>
							<textarea class="tinymce" name="desc" id="desc" cols="40" rows="5">'.htmlspecialchars( $media['desc'] ).'</textarea>
						</td>
					</tr>
					<tr>
						<td><label for="import_id">' . _("Identifiant d'import :") . '</label></td>
						<td>
							<input type="text" name="import_id" value="'.htmlspecialchars($media['import_id']).'" />
						</td>
					</tr>
					<tr>
						<td><label for="url">' . _("URL :") . '</label></td>
						<td>
							<input type="text" name="url" value="'.htmlspecialchars($media['url']).'" />
						</td>
					</tr>
					<tr>
						<td><label for="name">' . _("Publiée :") . '</label></td>
						<td>
							<input type="radio" '.( trim($media['date_publish'])!='' ? 'checked="checked"' : '' ).' value="1" id="publish-true" name="publish" class="publish-cat" /><label for="publish-true">' . _("Oui") . '</label>
							<input type="radio" '.( trim($media['date_publish'])=='' ? 'checked="checked"' : '' ).' value="0" id="publish-false" name="publish" class="publish-cat" /><label for="publish-false">' . _("Non") . '</label>
						</td>
					</tr>
					<tr>
						<td><label for="name">' . _("Chaine(s) :") . '</label></td>
						<td>
							<div id="list-channels">
								<div class="seg-obj-infos">
	';

	if( sizeof($ar_channels) ){
		foreach( $ar_channels as $one_channel ){
			print '
									<div>
										<input type="hidden" name="channels[]" value="'.$one_channel['chl_id'].'" />
										<input type="image" title="' . _("Retirer cette chaine") . '" src="/admin/images/del-cat.png" name="del-chl" class="del-obj-seg" />&nbsp;<span>'.htmlspecialchars( $one_channel['chl_name'] ).'</span>
									</div>
			';
		}
	}else{
		print _('Présent dans aucune chaine pour l\'instant');
	}

	$rchannel = doc_channels_get( $host['id'] );

	print '
								</div>
							</div>

							<select name="choose-channel" id="choose-channel" class="'.( $rchannel && ria_mysql_num_rows($rchannel)==sizeof($ar_channels) ? 'none' : 'block' ).'">
								<option value="-1">' . _("Inclure ce média dans une chaine") . '</option>
	';

	if( $rchannel ){
		while( $channel = ria_mysql_fetch_assoc($rchannel) ){
			if( array_key_exists($channel['id'], $ar_channels) ){
				continue;
			}

			print '
								<option value="'.$channel['id'].'">'.htmlspecialchars( $channel['name'] ).'</option>
			';
		}
	}

	print '
							</select>
						</td>
					</tr>
					<tr>
						<td><label for="name">' . _("Playlist(s) :") . '</label></td>
						<td>
							<div id="list-playlists">
								<div class="seg-obj-infos">
	';

	if( sizeof($ar_playlists) ){
		foreach( $ar_playlists as $one_playlist ){
			print '
									<div>
										<input type="hidden" name="playlists[]" value="'.$one_playlist['pls_id'].'" />
										<input type="image" title="' . _("Retirer cette playlist") . '" src="/admin/images/del-cat.png" name="del-pls" class="del-obj-seg" />&nbsp;<span>'.htmlspecialchars( $one_playlist['pls_name'] ).'</span>
									</div>
			';
		}
	}else{
		print _('Présent dans aucune playlist pour l\'instant');
	}

	$rplaylist = doc_playlists_get( $host['id'] );

	print '
								</div>
							</div>

							<select name="choose-playlist" id="choose-playlist" class="'.( $rplaylist && ria_mysql_num_rows($rplaylist)==sizeof($ar_channels) ? 'none' : 'block' ).'">
								<option value="-1">' . _("Inclure ce média dans une playlist") . '</option>
	';

	if( $rplaylist ){
		while( $playlist = ria_mysql_fetch_assoc($rplaylist) ){
			if( array_key_exists($playlist['id'], $ar_playlists) ){
				continue;
			}

			print '
								<option value="'.$playlist['id'].'">'.htmlspecialchars( $playlist['name'] ).'</option>
			';
		}
	}

	print '
							</select>
						</td>
					</tr>
				</tbody>
			</table>
		</form>
	';
?>

	<style>
	#table-edit-hosts #td-edit-hosts-150 {
		width: 150px;
	}
	#table-edit-hosts #td-edit-hosts-350 {
		width: 350px;
	}
	#table-edit-hosts #list-channels .del-obj-seg {
		width: 16px;
		height: 13px;
	}
	</style>

<?php
	require_once('admin/skin/footer.inc.php');
