<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403(['_RGH_ADMIN_COMPARATOR', '_RGH_ADMIN_MARKET_PLACE'], false);

	require_once('categories.inc.php');
	require_once('comparators.inc.php');
	require_once('view.ctr.inc.php');

	if (!isset($_POST['ctr'])){
		print _('Il manque des paramètres');
		exit;
	}
	$rcat = ctr_categories_get( $_POST['ctr'], 0, 0 );
	$have_categories = !$rcat || !ria_mysql_num_rows($rcat);

	if( isset($_POST['cat'], $_POST['ctr']) ){
		$cat = ria_mysql_fetch_array( prd_categories_get($_POST['cat']) );
		$cat_ctr = ria_mysql_fetch_array( ctr_prd_categories_get( $_POST['ctr'], $cat['id'] ) );
		$childs = sizeof( prd_categories_childs_get_array($cat['id']) );
		$is_exported = ctr_prd_categories_is_publish( $_POST['ctr'],$cat['id'] );

		$txt = '
			<div id="card-title">
				'.$cat['id'].' - '.htmlspecialchars( $cat['name'] ).
				( $cat_ctr['name']!="" ? ' <span class="card-title-ctr">(rattachée à '.htmlspecialchars( $cat_ctr['name'] ).')</span>' : '' ).'
				<div class="close"></div>
			</div>
			<div id="card-content">
				<div id="desc">
					<span class="underline">'._('Description :').'</span><br /><br />
					<p class="desc">'.($cat['desc']=="" ? _('Aucune, vous pouvez la compléter ici').' : <a href="/admin/catalog/edit.php?cat='.$cat['id'].'">'._('Compléter').'</a>' : str_replace(array('','_'),array('oe',' '),$cat['desc'])).'</p>
					<p class="desc">
						<span class="underline">'._('Nombre de produits publiés').' :</span> '.ria_mysql_num_rows( prd_products_get_simple(0, '', false, $cat['id'], true, false, false, false, array('orderable'=>true)) ).'
					</p>
				</div>
				<div id="choose">
					<span class="underline">'._('Rattachement possible').' :</span>
					<div class="not_actived_prds">
						<input type="checkbox" name="not_actived_prds" id="not_actived_prds" value="1" '.( $is_exported ? 'checked="checked"' : '' ).' />
						<label for="not_actived_prds" title="'._('Cette option permet de réaliser un simple mapping entre votre catégorie et une famille sans modifier l\'information d\'activation des produits contenus dans cette catégorie.').'">'._('Ne pas modifier l\'activation des produits').'</label>
					</div>
					<br /><br />
		';

		if( !$is_exported ){
			if( $have_categories ){
				$txt .= '
					<div>
						<input type="radio" name="choose-exp" id="exp-new" value="1" onclick="show_treeview_card();" />
						<label for="exp-new">'._('Exporter').'</label><br />
					</div>
				';
			}else{
				if( $rctr = ctr_prd_categories_get($_POST['ctr'], $cat['parent_id']) ){
					$cat_ctr = ria_mysql_fetch_array( $rctr );

					if( $cat_ctr['name']!="" ){
						$txt .= '
							<input type="radio" name="choose-exp" id="exp-parent-familly" value="'.$cat_ctr['id'].'" onclick="hide_treeview_card();" />
							<label for="exp-parent-familly">'.sprintf(_('Classer dans %s (hérite de sa catégorie mère)'), htmlspecialchars($cat_ctr['name'])).'</label>
							<br />
						';
					}
				}

				if( ctr_has_categories($_POST['ctr']) ){
					$txt .= '
						<input type="radio" name="choose-exp" id="exp-new-2" value="1" onclick="show_treeview_card();" />
						<label for="exp-new-2">'._('Classer dans une famille').'</label>
						<br />
					';
				}
			}

		}else{
			$rcat_ctr_p = ctr_prd_categories_get( $_POST['ctr'], $cat['parent_id'] );
			$cat_ctr_c = ria_mysql_fetch_array( ctr_prd_categories_get( $_POST['ctr'], $cat['id'] ) );

			$txt .= '
					<div>
						<input type="radio" name="choose-exp" id="no-exp" value="0" onclick="hide_treeview_card();" /><label for="no-exp">'._('Ne plus exporter').'</label>
						<br />
					</div>
			';

			if( $childs ){
				$txt .= '
					<div>
						<input type="radio" name="choose-exp" id="no-exp-child" value="-1" onclick="hide_treeview_card();" /><label for="no-exp-child">'._('Ne plus exporter (enfants y compris)').'</label>
						<br />
					</div>
				';
			}

			if( $have_categories ){
				$txt .= '
					<div' . (! ctr_prd_categories_is_publish($_POST['ctr'],$cat['parent_id']) ? ' style="display: none"' : '') . '>
						<input type="radio" name="choose-exp" id="exp-new" value="1" onclick="show_treeview_card();" />
						<label for="exp-new">'._('Exporter').'</label>
						<br />
					</div>
				';
			}else{
				if( trim($cat_ctr_c['name'])!='' ){
					$txt .= '
						<input type="radio" name="choose-exp" id="exp-familly" value="'.$cat_ctr['id'].'" checked="checked" onclick="hide_treeview_card();" />
						<label for="exp-familly">'.sprintf(_('Classer dans %s'), htmlspecialchars($cat_ctr['name'])).'</label>
						<br />
					';
				}

				if( $rcat_ctr_p && ria_mysql_num_rows($rcat_ctr_p) ){
					$cat_ctr_p = ria_mysql_fetch_array($rcat_ctr_p);

					if( $cat_ctr_p['name']!="" && $cat_ctr_p['name']!=$cat_ctr_c['name'] ){
						$txt .= '
							<input type="radio" name="choose-exp" id="exp-parent-familly" value="'.$cat_ctr_p['id'].'" onclick="hide_treeview_card();" />
							<label for="exp-parent-familly">'.sprintf(_('Classer dans %s (hérite de sa catégorie mère)'), htmlspecialchars($cat_ctr_p['name'])).'</label>
							<br />
						';
					}
				}

				$txt .= '
					<input type="radio" name="choose-exp" id="exp-new-2" value="1" onclick="show_treeview_card();" />
					<label for="exp-new-2">'._('Classer dans une autre famille').'</label>
					<br />
				';
			}
		}

		$txt .= '
				</div>
			</div>
		';

		print $txt;

	}