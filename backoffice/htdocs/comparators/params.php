<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403(['_RGH_ADMIN_COMPARATOR', '_RGH_ADMIN_MARKET_PLACE'], false);

	require_once('comparators.inc.php');
	require_once('view.ctr.inc.php');
	require_once('http.inc.php');

	$marketplace = isset($_GET['marketplace']) ? $_GET['marketplace'] : false;

	// Vérifier si le client à accès aux comparateurs
	if( !$config['ctr_active'] ){
		http_403();
		exit;
	}

	// Vérifie l'existance du comparateur de prix
	if( isset($_GET['ctr']) && !ctr_comparators_exists($_GET['ctr']) ){
		header('Location: index.php');
		exit;
	}

	// Charge une instance d'eBay
	if( $_GET['ctr']==CTR_EBAY ){
		require_once('comparators/ctr.ebay.inc.php');
		$eBay = new eBay();
	}

	//Récupère le nom du comparateur
	if( isset($_GET['ctr']) ) {
		$rctr = ctr_comparators_get($_GET['ctr'], true, false, null);
		if ($rctr && ria_mysql_num_rows($rctr)) {
			$ctr = ria_mysql_fetch_array($rctr);
			$marketplace = $ctr['marketplace'];
			$ctr = $ctr['name'];
		}
	}

	// Valeur par défaut pour la configuration du flux BeezUp
	$default_cfg_flow_beezup = [
		'prd_parent' => 0,
		'prd_stock' => 0,
		'prd_links' => 0,
		'prd_docs' => 0,
		'declinaison' => 0,
		'carrier_default' => 0,
		'dps_id' => 0,
		'zone_default' => '',
		'dlvdelay' => '',
		'dlvcost' => '',
		'cfg_img' => '',
		'include_img_second' => 0,
		'fields' => []
	];

	// Enregistrement de la configuration de la gestion des commande BeezUP
	if( isset($_POST['save-beezup-cfg-order']) ){
		require_once('comparators/BeezUP.class.php');

		if( !isset($_POST['cfg_beezup_ord']) || !is_array($_POST['cfg_beezup_ord']) ){
			$error = _('Un ou plusieurs paramètres obligatoires sont manquants.');
		}elseif( !BeezUP::setConfig($_POST['cfg_beezup_ord']) ){
			$error = _('Une erreur inattendue s\'est produte lors de l\'enregistrement de la configuration de la gestion des commandes.');
		}

		if( !isset($error) ){
			$_SESSION['ctr_prams_edit_save'] = _('La configuration de la gestion des commandes a bien été enregistrée.');
			header('Location: params.php?ctr='.CTR_BEEZUP.'&tab=order');
			exit;
		}
	}

	// Enregistrement de la configuration du flux BeezUP
	if( isset($_POST['save-beezup-flux']) ){
		if( !isset($_POST['fldbeezup']) || !is_array($_POST['fldbeezup']) ){
			$error = _('Un ou plusieurs paramètres obligatoires sont manquants.');
		}else{
			// Contrôle certains paramètres
			foreach( $_POST['fldbeezup'] as $key=>$val ){
				switch( $key ){
					case 'dlvdelay':
						if( trim($val) == '' || preg_match('/[^0-9-]/', $val) > 0 ){
							$error = _('La valeur saisie pour le délai de livraison n\'est pas valide.');
						}
						break;
					case 'dlvcost':
						if( !is_numeric($val) || $val < 0 ){
							$error = _('La valeur saisie pour le prix de la livraison n\'est pas valide.');
						}
						break;
				}
			}

			if( !isset($error) ){
				$cfg = array_replace( $default_cfg_flow_beezup, $_POST['fldbeezup'] );

				// Contrôle des clés
				foreach( array_keys($cfg) as $one_key ){
					if( !array_key_exists($one_key, $default_cfg_flow_beezup) ){
						unset( $cfg[$one_key] );
					}
				}

				if( !ctr_param_fields_add(CTR_BEEZUP, 'cfg_flow_beezup', json_encode($cfg)) ){
					$error = _('Une erreur inattendue s\'est produite lors de l\'enregistrement de la configuration du flux.');
				}
			}
		}

		if( !isset($error) ){
			$_SESSION['ctr_prams_edit_save'] = _('La configuration du flux a bien été enregistrée.');
			header('Location: params.php?ctr='.CTR_BEEZUP);
			exit;
		}
	}

	// Enregistrement des paramètres
	if( isset($_POST['save-params'], $_GET['ctr']) ){
		foreach( $_POST['fld'] as $code=>$fld ){
			if( $fld!='' && $fld>=0 ){
				if( !ctr_param_fields_add($_GET['ctr'], $code, $fld) )
					$error = sprintf(_("Une erreur inattendue s'est produite lors de la configuration de %s."), $ctr)."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			} elseif( !ctr_param_fields_delete($_GET['ctr'], $code) )
				$error_delete[] = $code;
		}

		if( isset($error_delete) && sizeof($error_delete)>0 ){
			$rparam = ctr_params_get( $_GET['ctr'], $error_delete );
			if( $rparam!=false ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression du lien vers le champ avancé pour ");
				$error .= sizeof($error_delete)>1 ? _("les paramètres suivants")." : " : _("le paramètre suivant")." : ";
				while( $param = ria_mysql_fetch_array($rparam) ){
					$error .= "<br />- ".$param['name'];
				}
				$error .= "<br />"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			} else
				$error = _("Une erreur inattendue s'est produite lors de la suppression des liens entre les paramètres et leur champ avancé.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");

		}

		if( !isset($error) ){
			// Force la mise à jour des tarifs et des quantités dès lors que la configuration est modifié
			// Permet de prendre en compte les nouveaux paramètres
			ctr_catalogs_update_quantity($_GET['ctr']);
			ctr_catalogs_update_price($_GET['ctr']);

			header('Location: params.php?ctr='.$_GET['ctr']);
			exit;
		}
	}

	// Enregistre les liens entre les services de livraison et les transporteurs de la place de marché
	if( isset($_POST['carrier'], $_POST['carrier']) ){
		if( is_array($_POST['carrier']) && sizeof($_POST['carrier']) ){
			foreach( $_POST['carrier'] as $srv=>$ccr ){
				if( !is_numeric($ccr) || $ccr<=0 ){
					$errorCarrier = _("Veuillez sélectionner un transporteur pour chaqu'un de vos services de livraison.");
				} elseif( !ctr_carriers_services_add($_GET['ctr'], $srv, $ccr) ){
					$errorCarrier = _("Une erreur inattendue s'est produite lors de l'enregistrement des liens entre vos services de livraison et les transporteurs.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}
		}

		if( !isset($errorCarrier) ){
			header('Location: /admin/comparators/params.php?ctr='.$_GET['ctr']);
			exit;
		}
	}

	// Enregistrement du budget max
	if( isset($_POST['save-budget']) ){
		if( !isset($_POST['budget']) ){
			$error = _("L'information de bugdet n'a pas été transmise.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}else{
			$budget = is_numeric($_POST['budget']) && $_POST['budget']>0  ? $_POST['budget'] : 0;

			if( !ctr_comparator_tenants_update_budget($_GET['ctr'], $budget) ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du budget.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}

		if( !isset($error) ){
			header('Location: /admin/comparators/params.php?ctr='.$_GET['ctr']);
			exit;
		}
	}

	$reload = false;
	if( $_GET['ctr']==CTR_EBAY ){
		$token = cfg_overrides_get_value( 'ebay_api_token' );
		$reload = trim($token)!='' ? false : true;
	}

	if( !isset($_SESSION['websitepicker']) || !is_numeric($_SESSION['websitepicker']) || !$_SESSION['websitepicker'] ){
		$_SESSION['websitepicker'] = $config['wst_id'];
	}

	if( isset($_GET['wst']) && is_numeric($_GET['wst']) && $_GET['wst'] ){
		$_SESSION['websitepicker'] = $_GET['wst'];
	}

	define('ADMIN_PAGE_TITLE', ($marketplace ? _('Places de marché') : _('Comparateurs de prix')).' - '._('Configuration'));
	require_once('admin/skin/header.inc.php');

	print '<div id="comparators">'
		.view_ctr_header( $_GET['ctr'] )
	.'</div>';

	if( isset($error) ){
		print '<div class="error">'.$error.'</div>';
	}

	// Zone spécial pour eBay, permettant de donner le droit à RiaShopping d'accéder et de modifier les informations du compte eBay
	if( $_GET['ctr']==CTR_EBAY ){
		$userID = '';
		$ruserID = ctr_params_get( CTR_EBAY, 'user_id' );
		if( $ruserID && ria_mysql_num_rows($ruserID) ){
			$userID = ria_mysql_result( $ruserID, 0, 'fld' );
		}

		if( trim($userID)=='' ){
			print '<div class="notice">'
				.'<p>'._('L\'identifiant eBay est requis pour l\'activation du module. Vous devez posséder un compte eBay professionnel, <a target="_blank" href="https://scgi.ebay.fr/ws/eBayISAPI.dll?RegisterEnterInfo&bizflow=2">créez un compte professionnel</a>.').'</p>'
			.'</div>';
		}else{
			if( !$eBay->fetchToken() ){
				print '<div class="notice">'
					.'<p>'._('Afin d\'utiliser le module eBay, vous devez rattacher votre compte eBay à RiaShopping. Pour cela vous devez cliquer sur le lien suivant').' : <a id="link-ebay" target="_blank" href="'.$eBay->getUrlLogin().'">'._('Rattacher le module à mon compte eBay').'</a>.</p>'
					.'<p>'._('Vous devez posséder un compte eBay professionnel, <a target="_blank" href="https://scgi.ebay.fr/ws/eBayISAPI.dll?RegisterEnterInfo&bizflow=2">créez un compte professionnel</a>.').'</p>'
				.'</div>';
			}else{
				print '<div class="notice">'
					.'<p>'._('Votre compte eBay est bien rattaché au module RiaShopping. Cette connexion peut automatiquement être coupée au bout d\'un certain moment, vous recevrez alors un email vous demandant une reconnexion.').'</p>'
				.'</div>';
			}
		}
	}

	print view_websites_selector( $_SESSION['websitepicker'], false );
	print '<form action="params.php?ctr='.$_GET['ctr'].'" method="post" id="frm-params">';

	switch( $_GET['ctr'] ){
		case CTR_BEEZUP:
			include( dirname(__FILE__).'/../views/ctr/params/beezup.php');
		break;

		default:
			if( !$marketplace && !in_array($_GET['ctr'], array(CTR_GOOGLE, CTR_PRIXAN)) ){
				print '<h2>'._('Budget mensuel maximum').'</h2>'
				.'<div class="block-conf-ctr">'
					.'<label for="budget">'._('Vous pouvez définir un budget mensuel maximum pour ce comparateur de prix :').'</label>'
					.'<input type="text" name="budget" id="budget" value="'.ctr_comparator_tenants_get_budget( $_GET['ctr'] ).'" /> €&nbsp;'
					.'<input type="submit" name="save-budget" value="'._('Enregistrer').'" />'
				.'</div>';
			}

			print '<h2>'._('Configurer').' '.htmlspecialchars( $ctr ).'</h2>';

			$texte = '';
			if($marketplace){
				$texte = sprintf( _('Afin d\'optimiser l\'exportation de vos produits vers la place de marché %s, nous vous proposons de rattacher certaines informations demandées par la place de marché à des <a href="/admin/config/fields/fields/index.php">champs libres</a> déjà associés à vos produits ou bien des informations complémentaires :'), htmlspecialchars($ctr) );
			}else{
				$texte = sprintf( _('Afin d\'optimiser l\'exportation de vos produits vers le comparateur de prix %s, nous vous proposons de rattacher certaines informations demandées par le comparateur de prix à des <a href="/admin/config/fields/fields/index.php">champs libres</a> déjà associés à vos produits ou bien des informations complémentaires :'), htmlspecialchars($ctr) );
			}

			print '<p>'.$texte.'</p>'

			.'<table id="ctr-params">'
				.'<caption>'.str_replace( '%comparator%', $ctr, _('Paramètres pour %comparator%') ).'</caption>'
				.'<col width="180" /><col width="*" /><col width="195"/>'
				.'<tfoot>'
					.'<tr>'
						.'<td colspan="3" style="text-align:right"><input type="submit" name="save-params" id="save-params" value="'._('Enregistrer').'" /></td>'
					.'</tr>'
				.'</tfoot>'
				.'<tbody>';

					// Champs avancés
					$rfld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array('fld_name'), true, array(), null, 1 );

					// Paramètres
					$rparams = ctr_params_get($_GET['ctr']);

					if( $rparams!=false && ria_mysql_num_rows($rparams)>0 ){
						$param_obligatory = array(); $other_param = array();
						while( $param = ria_mysql_fetch_array($rparams) ){
							if( $param['is_obligatory'] )
								$param_obligatory[] = $param;
							else
								$other_param[] = $param;
						}

						print '	<tr>';
						print '		<th colspan="3">'._('Paramètres obligatoires').'</th>';
						print '	</tr>';
						if( sizeof($param_obligatory)>0 ){
							foreach( $param_obligatory as $key=>$param ){
								print '	<tr>';
								print '		<td>'.htmlspecialchars( $param['name'] ).'</td>';
								print '		<td>'.htmlspecialchars( $param['desc'] ).'</td>';

								$val = trim($param['fld']) ? $param['fld'] : ( isset($param['params']) && trim($param['params']) != '' ? $param['params'] : '' );
								switch( $param['type'] ){
									case 8 :
										print '	<td>';
										print '		<input'.($val=='Oui' ? ' checked="checked"' : '').' type="radio" name="fld['.$param['code'].']" id="fld-'.$param['code'].'-y" value="'._('Oui').'" />';
										print '		<label for="fld-'.$param['code'].'-y">'._('Oui').'</label>';
										print '		<input'.($val=='Non' ? ' checked="checked"' : '').' type="radio" name="fld['.$param['code'].']" id="fld-'.$param['code'].'-n" value="'._('Non').'" />';
										print '		<label for="fld-'.$param['code'].'-n">'._('Non').'</label>';
										print '		<input'.($val=='' ? ' checked="checked"' : '').' type="radio" name="fld['.$param['code'].']" id="fld-'.$param['code'].'-nd" value="" />';
										print '		<label for="fld-'.$param['code'].'-nd">'._('Non défini').'</label>';
										print '	</td>';
										break;
									case 4 :
									case 1 :
										$type = $param['code']=='password' ? 'password' : 'text';
										print '	<td>';
										print '		<input class="text-small" type="'.$type.'" name="fld['.$param['code'].']" id="fld-'.$param['code'].'" value="'.$val.'" />';
										print '	</td>';
										break;
									case 2 :
										print '	<td>';
										print '		<textarea cols="50" rows="5" name="fld['.$param['code'].']" id="fld-'.$param['code'].'">'.$val.'</textarea>';
										print '	</td>';
										break;
									case 5 :
										print '<td>';
											print '<select name="fld['.$param['code'].']" id="fld-'.$param['code'].'">';
											if ($val == '') print '<option value=""></option>';
											foreach (explode(',', $param['params']) as $option) {
												print '<option value="'.htmlspecialchars($option).'"'.($val === $option ? ' selected="selected"' : '').'>'.htmlspecialchars($option).'</option>';
											}
											print '</select>';
										print '</td>';
										break;
									default :
										print '	<td><select name="fld['.$param['code'].']" id="fld-'.$param['code'].'"><option value="-1"></option>';
										if( $rfld!=false ){
											while( $fld = ria_mysql_fetch_array($rfld) )
												print '	<option value="'.$fld['id'].'" '.( $param['fld']==$fld['id'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars($fld['name']).'</option>';
											ria_mysql_data_seek( $rfld, 0 );
										}
										print '	</select></td>';
										break;
								}

								print '	</tr>';
							}
						} else {
							print '<tr><td colspan="3">'._('Aucun paramètre obligatoire').'</td></tr>';
						}

						if( sizeof($other_param)>0 ){
							print '	<tr>';
							print '		<th colspan="3">'._('Paramètres optionnels').'</th>';
							print '	</tr>';

							foreach( $other_param as $key=>$param ){
								print '	<tr>';
								print '		<td>'.$param['name'].'</td>';
								print '		<td>'.$param['desc'].'</td>';

								$val = trim($param['fld']) ? $param['fld'] : ( isset($param['params']) && trim($param['params']) != '' ? $param['params'] : '' );
								switch( $param['type'] ){
									case 8 :
										print '	<td>';
										print '		<input'.($val=='Oui' ? ' checked="checked"' : '').' type="radio" name="fld['.$param['code'].']" id="fld-'.$param['code'].'-y" value="'._('Oui').'" />';
										print '		<label for="fld-'.$param['code'].'-y">'._('Oui').'</label>';
										print '		<input'.($val=='Non' ? ' checked="checked"' : '').' type="radio" name="fld['.$param['code'].']" id="fld-'.$param['code'].'-n" value="'._('Non').'" />';
										print '		<label for="fld-'.$param['code'].'-n">'._('Non').'</label>';
										print '		<input'.($val=='' ? ' checked="checked"' : '').' type="radio" name="fld['.$param['code'].']" id="fld-'.$param['code'].'-nd" value="" />';
										print '		<label for="fld-'.$param['code'].'-nd">'._('Non défini').'</label>';
										print '	</td>';
										break;
									case 1 :
										print '	<td>';
										print '		<input type="text" class="text-small" name="fld['.$param['code'].']" id="fld-'.$param['code'].'" value="'.$val.'" />';
										print '	</td>';
										break;
									case 2 :
										print '	<td>';
										print '		<textarea cols="50" rows="5" name="fld['.$param['code'].']" id="fld-'.$param['code'].'">'.$val.'</textarea>';
										print '	</td>';
										break;
									case 5 :
										print '<td>';
											print '<select name="fld['.$param['code'].']" id="fld-'.$param['code'].'">';
											if ($val == '') print '<option value=""></option>';
											foreach (explode(',', $param['params']) as $option) {
												print '<option value="'.htmlspecialchars($option).'"'.($val === $option ? ' selected="selected"' : '').'>'.htmlspecialchars($option).'</option>';
											}
											print '</select>';
										print '</td>';
										break;
									default :
										print '		<td><select name="fld['.$param['code'].']" id="fld-'.$param['code'].'"><option value="-1"></option>';
										if( $rfld!=false ){
											while( $fld = ria_mysql_fetch_array($rfld) )
												print '<option value="'.$fld['id'].'" '.( $param['fld']==$fld['id'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars($fld['name']).'</option>';
											ria_mysql_data_seek( $rfld, 0 );
										}
										print '		</select></td>';
										break;
								}
								print '	</tr>';
							}
						}
					} else {
						print '<tr><td colspan="3">'.str_replace( '%comparator%', $ctr, _('Aucun paramètre pour %comparator%') ).'</td></tr>';
					}

				print '</tbody>'
			.'</table>';

			if( $_GET['ctr']==CTR_EBAY ){
				print '<h2>'._('Services de livraison').'</h2>'
				.'<table id="ebay-shipping" class="checklist">'
					.'<caption>'._('Services de livraison activés').'</caption>'
					.'<thead>'
						.'<th id="ebay-checklist"><input type="checkbox" class="checkboxall" /></th>'
						.'<th id="ebay-carrier">'._('Transporteur eBay').'</th>'
						.'<th id="ebay-shipping">'._('Service de livraison associé').'</th>'
						.'<th id="ebbay-cost-supp">'._('Frais par produit supplémentaire').'</th>'
					.'</thead>'
					.'<tfoot>'
						.'<tr>'
							.'<td colspan="2">'
								.'<input type="submit" name="ebay-del-srv" id="ebay-del-srv" value="'._('Supprimer').'" style="float:left;" />'
							.'<td>'
							.'<td colspan="2">'
								.'<input type="submit" name="ebay-add-srv" id="ebay-add-srv" value="'._('Ajouter').'" />'
							.'</td>'
						.'</tr>'
					.'</tfoot>'
					.'<tbody>';

					$carrier = cfg_overrides_get_value( 'ebay_carriers' );
					$carrier = json_decode($carrier, true);

					$has_srv = false;
					if( trim($token)!='' && is_array($carrier) && sizeof($carrier) ){
						foreach( $carrier as $key=>$c ){
							$srv = dlv_services_get_name( $c['service'] );

							$crr = $eBay->getShippingServices( $c['carrier'] );
							if( !isset($crr['Description']) ){
								continue;
							}

							print '
								<tr>
									<td><input type="checkbox" name="ebay-del[]" value="'.$key.'" /></td>
									<td>'.htmlspecialchars( $crr['Description'] ).'</td>
									<td>'.htmlspecialchars( $srv ).'</td>
									<td>'.( $c['cost-supp']>0 ? number_format( $c['cost-supp'], 2, ',', ' ' ) : '0,00' ).' €</td>
								</tr>
							';

							$has_srv = true;
						}
					}

					if( !$has_srv ){
						print '
							<tr>
								<td colspan="4">'._('Aucun service de livraison n\'est configuré, l\'export des produits ne pourra pas avoir lieu.').'</td>
							</tr>
						';
					}

					print '</tbody>'
				.'</table>';
			}else{
				if( isset($errorCarrier) ){
					print '<div class="error errorCarrier">'.$errorCarrier.'</div>';
				}

				$rsrv = dlv_services_get();
				$rcarrier = ctr_carriers_get( $_GET['ctr'] );

				if( $rsrv && ria_mysql_num_rows($rsrv) && $rcarrier && ria_mysql_num_rows($rcarrier) ){
					print '<h2>'._('Services de livraison').'</h2>'
					.'<div class="notice">'
						._('Afin de permettre la confirmation d\'expédition d\'une commande, vous devez rattacher vos services de livraison à un transporteur disponible chez FNAC.')
						.'<br />'
						._('Si aucun transporteur ne correspond à votre service de livraison, n\'hésitez pas à prendre contact avec nous afin que nous puissions compléter la liste des transporteurs.')
					.'</div>'
					.'<table id="carriers" class="checklist">'
						.'<caption>'._('Liste des services de livraison').'</caption>'
						.'<col width="325" /><col width="75" align="center" />'
						.'<thead>'
							.'<tr>'
								.'<th id="name">'._('Service de livraison').'</th>'
								.'<th id="is-active">'._('Transporteur').'</th>'
							.'</tr>'
							.'</thead>'
							.'<tfoot>'
							.'<tr><td colspan="3">'
								.'<input type="submit" title="'._('Enregistrer').'" value="'._('Enregistrer').'" class="btn-save" name="save-srv" />'
							.'</td></tr>'
						.'</tfoot>'
						.'<tbody>';

							while( $srv = ria_mysql_fetch_array($rsrv) ){
								print '	<tr>';
								print '		<td headers="name">';
								print '			<a target="_blank" href="/admin/config/livraison/services/edit.php?srv='.$srv['id'].'">'.htmlspecialchars( $srv['name'] ).'</a>';
								print '		</td>';
								print '		<td>';
								print '			<select name="carrier['.$srv['id'].']" id="carrier-'.$srv['id'].'">';
								print '				<option value="-1">'.str_replace( '%comparator%', $ctr, _('Sélectionnez un transporteur %comparator%') ).'</option>';

								ria_mysql_data_seek( $rcarrier, 0 );
								while( $carrier = ria_mysql_fetch_array($rcarrier) ){
									$selected = !isset($_POST['carrier'][$srv['id']]) && ctr_carriers_services_get_carrier($_GET['ctr'], $srv['id'])==$carrier['id'] ? 'selected="selected"' : '';
									$selected = isset($_POST['carrier'][$srv['id']]) && $_POST['carrier'][$srv['id']]==$carrier['id'] ? 'selected="selected"' : $selected;

									print '			<option '.$selected.' value="'.$carrier['id'].'">'.htmlspecialchars( $carrier['name'] ).'</option>';
								}

								print '			</select>';
								print '		</td>';
								print '	</tr>';
							}

						print '</tbody>'
					.'</table>';
				}
			}
		break;
	}

	print '</form>'

	.'<script>'
		.'var ctrID = '.$_GET['ctr'].';'
		.'var reloadEBay = '.( $reload ? 'true' : 'false' ).';'
	.'</script>';

	require_once('admin/skin/footer.inc.php');
