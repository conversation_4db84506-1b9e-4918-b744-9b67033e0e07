<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403(['_RGH_ADMIN_COMPARATOR', '_RGH_ADMIN_MARKET_PLACE'], false);

	require_once('comparators.inc.php');

	$marketplace = isset($_POST['marketplace']) ? $_POST['marketplace'] : false;

	// if ($marketplace) $_POST['catCtr'] = 0;

	header("Content-Type: application/xml");
	$error = false;
	$xml = '<?xml version="1.0" encoding="utf-8"?>';

	if( isset($_POST['savelink']) ){

		if( !isset($_POST['catCtr'], $_POST['catPrd'], $_POST['choose']) ){

			$error = _('Certaines informations obligatoires sont manquantes et ont empêché l\'enregistrement.').'<br />'._('<PERSON><PERSON><PERSON><PERSON> réessayer ou prendre contact avec l\'administrateur.');

		} else {

			$cat_name = ria_mysql_result( prd_categories_get($_POST['catPrd']), 0, 'title' );
			if( in_array($_POST['choose'], array(0, -1)) ){

				if( $_POST['choose']==0 && !ctr_prd_categories_del($_POST['ctr'], $_POST['catPrd']) ){
					$error = _("Une erreur inattendue s'est produite lors de la suppression de l\'export.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				} elseif( $_POST['choose']==-1 && !ctr_prd_categories_del($_POST['ctr'], $_POST['catPrd'], true) ){
					$error = _("Une erreur inattendue s'est produite lors de la suppression de l\'export (enfants y compris).")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}

			}elseif( $_POST['choose']>=1 ){
				$_POST['catCtr'] = $_POST['choose']>1 ? $_POST['choose'] : $_POST['catCtr'];

				$_POST['catCtr'] = $_POST['catCtr']!='undefined' ? $_POST['catCtr'] : 0;
				if( $_POST['catCtr'] ){
					$rcat = ctr_categories_get( $_POST['ctr'], $_POST['catCtr'] );
					if( $rcat && ria_mysql_num_rows($rcat) ){
						$ctr_cat_name = ria_mysql_result( $rcat, 0, 'title');
					}else{
						$ctr_cat_name = '';
					}
				}

				$not_actived_prds = isset($_POST['not_actived_prds']) && $_POST['not_actived_prds'] ? true : false;
				if( !ctr_prd_categories_add($_POST['ctr'], $_POST['catPrd'], $_POST['catCtr'], 0, $not_actived_prds) ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l\'export.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}

			}

		}
	}

	if( !$error ){
		$xml .= '<result type="1">';
		$xml .= '<success catid="'.( isset($_POST['catPrd']) ? $_POST['catPrd'] : '' ).'" catprd="'.( isset($cat_name) ? $cat_name : '' ).'" catctr="'.( isset($ctr_cat_name) ? $ctr_cat_name : '' ).'"></success>';
		$xml .= '</result>';
	} else {
		$xml .= '<result type="0"><error>';
		$xml .= '<![CDATA['.$error.']]>';
		$xml .= '</error>\n</result>';
	}

	print $xml;
