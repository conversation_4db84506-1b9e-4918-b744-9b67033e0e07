<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403(['_RGH_ADMIN_COMPARATOR', '_RGH_ADMIN_MARKET_PLACE'], false);

	require_once('comparators.inc.php');

	if( isset($_POST['save']) ){
		if( !isset($_POST['sh-title']) || !trim($_POST['sh-title']) ){
			$error = _('Veuillez renseigner le titre de votre recherche.');
		}

		if( !isset($error) ){
			$_POST['sh-desc'] = isset($_POST['sh_desc']) ? $_POST['sh-desc'] : '';

			$ar_conditions = array();
			foreach( $_POST['cdts'] as $cdt=> $val ){
				$end = strpos($cdt, '-');
				$code = $end ? substr( $cdt, 0, $end ) : $cdt;
				$id = ctr_conditions_get_id_bycode( $code );
				$sbl = isset($_POST['sbl'][$cdt]) ? $_POST['sbl'][$cdt] : '=';

				$ar_conditions[] = array(
					'id' => $id,
					'symbol' => $sbl,
					'value' => $val
				);
			}

			if( !ctr_filters_add( $_POST['sh-title'], $_POST['sh-desc'], $ar_conditions, $_GET['marketplace']) ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre recherche.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Sauvegarder la recherche') . ' - ' . _('Recherche') . ' - ' . _('Comparateurs'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'popup-iframe');

	require_once('admin/skin/header.inc.php');
?>
	<div id="popupctr">
		<form action="popup-save-search.php?marketplace=<?php echo $_GET['marketplace']; ?>" method="post">
			<?php
				if( isset($error) ){
					print '<div class="error">'.nl2br( $error ).'</div>';
				}

				$_GET['sbl'] = isset($_GET['sbl']) ? $_GET['sbl'] : (isset($_POST['sbl']) ? $_POST['sbl'] : array());
				$_GET['cdts'] = isset($_GET['cdts']) ? $_GET['cdts'] : (isset($_POST['cdts']) ? $_POST['cdts'] : array());

				if( isset($_GET) && is_array($_GET) && sizeof($_GET) ){
					$count = 0;
					foreach( $_GET as $key=>$val ){
						if( $key=='sbl' ){
							foreach( $val as $k=>$v ){
								print '<input type="hidden" name="sbl['.$k.']" id="get-'.($count++).'" value="'.$v.'" />';
							}
						} elseif( $key=='cdts' ) {
							foreach( $val as $k=>$v ){
								print '<input type="hidden" name="cdts['.$k.']" id="get-'.($count++).'" value="'.$v.'" />';
							}
						}
					}
				}
			?>
			<div id="infosearch">
				<div>
					<label for="sh-title"><?php print _('Titre :'); ?> *</label>
					<input type="text" name="sh-title" id="sh-title" value="<?php print isset($_POST['sh-title']) ? $_POST['sh-title'] : ''; ?>" />
				</div><div>
					<label for="sh-desc"><?php print _('Commentaires :'); ?></label>
					<textarea cols="100" rows="5" name="sh-desc" id="sh-desc"><?php print isset($_POST['sh-desc']) ? $_POST['sh-desc'] : ''; ?></textarea>
				</div>
			</div>
			<div class="actions">
				<input class="btn-main" type="submit" name="save" id="save" value="<?php print _('Sauvegarder'); ?>" title="<?php print _('Sauvegarder la recherche pour une execution ultérieure'); ?>" />
			</div>
		</form>
	</div>
<?php
	require_once('admin/skin/footer.inc.php');
