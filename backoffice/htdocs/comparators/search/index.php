<?php
	/**	\file index.php
	 * 	Cette page permet d'effectuer des recherches sur les produits, pour les envoyer ou les retirer des comparateurs de prix.
	 *
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403(['_RGH_ADMIN_COMPARATOR', '_RGH_ADMIN_MARKET_PLACE'], false);

	require_once('comparators.inc.php');
	require_once('view.ctr.inc.php');
	require_once('http.inc.php');

	$marketplace = isset($_GET['marketplace']) ? $_GET['marketplace'] : false;

	// Vérifier si le client à accès aux comparateurs
	if( !$config['ctr_active'] ){
		http_403();
		exit;
	}

	// Bouton Exporter (Télécharger le fichier .csv)
	if( isset($_GET['downloadexport']) ){
		// Contrôle que le fichier est bien disponible
		if (file_exists( $config['doc_dir'].'/recherche-stats-'.( $marketplace ? 'marketplaces' : 'comparateurs' ).'.csv')) {
			header('Content-disposition: attachment; filename="recherche-stats-'.( $marketplace ? 'marketplaces' : 'comparateurs' ).'.csv"');
			header('Content-type: application/octetstream; charset=utf-8');
			header('Pragma: no-cache');
			header('Expires: 0');

			echo "\xEF\xBB\xBF"; // BOM UTF-8
			readfile ( $config['doc_dir'].'/recherche-stats-'.( $marketplace ? 'marketplaces' : 'comparateurs' ).'.csv');
			exit;
		}
	}

	// Récupère le nom du comparateur
	$ctr = '';
	if( isset($_GET['ctr']) && $_GET['ctr'] ){
		$ctr = ria_mysql_result(ctr_comparators_get($_GET['ctr'], true, false, null),0,'name');
	}

	$_GET['ctr'] = isset($_GET['ctr']) ? $_GET['ctr'] : 0;
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;

	if( !isset($_SESSION['websitepicker']) || !is_numeric($_SESSION['websitepicker']) || !$_SESSION['websitepicker'] ){
		$_SESSION['websitepicker'] = $config['wst_id'];
	}

	if( isset($_GET['wst']) && is_numeric($_GET['wst']) && $_GET['wst'] ){
		$_SESSION['websitepicker'] = $_GET['wst'];
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Rechercher des produits').' - '.($marketplace ? _('Places de marché') : _('Comparateurs')));
	require_once('admin/skin/header.inc.php');

?>
			<form id="formctrsearch" action="/admin/comparators/search/index.php?ctr=<?php print $_GET['ctr']; print isset($_GET['flt']) ? '&amp;flt='.$_GET['flt'] : ''; ?>" method="post">

				<input type="hidden" name="ctrid" id="ctrid" value="<?php print $_GET['ctr']; ?>" />
				<input type="hidden" name="ctrname" id="ctrname" value="<?php print $ctr; ?>" />

				<h2><?php print _('Rechercher des produits'); ?></h2>
				<?php
					$phrase = _('Utilisez le moteur de recherche avec conditions ci-dessous, afin d\'accéder rapidement aux produits pouvant être exportés ou au contraire retirés de l\'export.').'<br />';
					if ($marketplace){
						$phrase .= _('Vous avez la possibilité de préciser une période, ainsi qu\'une place de marché, qui servira de référence à la sélection des statistiques.').'<br />'._('Si aucune place de marché n\'est sélectionnée, alors l\'utilisation des options "Export" ou "Ne plus exporter" se fera sur toutes les places de marché pour chaque produit sélectionné.');
					} else {
						$phrase .= _('Vous avez la possibilité de préciser une période, ainsi qu\'un comparateur, qui servira de référence à la sélection des statistiques.').'<br />'._('Si aucun comparateur n\'est sélectionné, alors l\'utilisation des options "Export" ou "Ne plus exporter" se fera sur tous les comparateurs pour chaque produit sélectionné.');
					}
				?>
				<p><?php print $phrase; ?>
				</p>
				<div class="stats-menu">
					<div id="riadatepicker"></div>
					<div id="selectctr" class="riapicker">
						<div class="selectorview">
							<div class="left">
								<span class="function_name"><?php echo $marketplace ? _('Places de marché') : _('Comparateurs de prix'); ?></span>
								<br/><span class="view"><?php
									if( $_GET['ctr']>0 ){
										print ctr_comparators_get_name($_GET['ctr']);
									} else {
										print $marketplace ? _('Toutes les places de marché') : _('Tous les comparateurs');
									}
								?></span>
							</div>
							<a class="btn" name="btn">
								<img src="/admin/images/stats/fleche.gif" height="8" width="16" alt="" />
							</a>
							<div class="clear"></div>
						</div>
						<div class="selector">
							<a name="ctr-0"><?php print $marketplace ? _('Toutes les places de marché') : _('Tous les comparateurs'); ?></a>
							<?php
								// récupère les comparateurs actifs
								$rctr = ctr_comparators_get( 0, true, false, $marketplace );
								if( $rctr && ria_mysql_num_rows($rctr) ){
									print '<a class="selector-sep" ></a>';
									while( $ctr = ria_mysql_fetch_array($rctr) ){
										print '<a name="ctr-'.$ctr['id'].'">'.htmlspecialchars($ctr['name']).'</a>';
									}
								}
							?>
						</div>
					</div>
					<?php
						if( !$marketplace ){
							print view_websites_selector( $_SESSION['websitepicker'], false, 'ctr-selector-search', true );
						}
					?>
					<div class="clear"></div>
				</div>
				<div id="ctr-search">
					<div id="zone-filters">
						<label for="filters"><?php print _('Charger une recherche :'); ?></label>
						<?php
							$flt = isset($_GET['flt']) && is_numeric($_GET['flt']) && $_GET['flt']>0 ? $_GET['flt'] : 0;
							$filters = ctr_filters_get(0, $marketplace);
							if( !$filters || !ria_mysql_num_rows($filters) ){
								print '	<span id="filters">'._('Aucune recherche sauvegardée n\'est disponible.').'</span>';
							} else {
								print '	<select name="filters" id="filters" onchange="return loadFilter();">';
								print '		<option value="-1">&nbsp;</option>';
								while( $filter = ria_mysql_fetch_array($filters) ){
									$selected = $flt==$filter['id'] ? 'selected="selected"' : '';
									print '	<option '.$selected.' value="'.$filter['id'].'" title="'.htmlspecialchars( $filter['comment'] ).'">'.htmlspecialchars( $filter['name'] ).'</option>';
								}
								print '	</select>';
							}
						?>
					</div>
					<div id="zone-conditions"><?php

						// charge par défaut trois conditions avec Oui comme valeur
						$cdts = array( 'CTR_SEARCH_PUBLISH'=>'1', 'CTR_SEARCH_STOCK'=>'1', 'CTR_SEARCH_EXPORT'=>'0' );

						$count = 1;
						foreach( $cdts as $cdt=>$val ){
							print view_ctr_conditions( $count, $cdt, $val );
							$count++;
						}
						?>
						<div class="clear"></div>
						<a onclick="return showAddConditions();" id="new-cdt" class="button" href="#">+ <?php print _('Ajouter une condition'); ?></a>
						<div class="clear"></div>
					</div>
					<div id="filter" class="actions" style="display: none;">
						<div class="flt">
							<div id="cdt-select">
								<label for="conditions"><?php print _('Ajouter une condition sur :'); ?></label>
								<select name="conditions" id="conditions" onchange="return loadNewConditions();"><?php
									print '	<option value="-1"></option>';
									$rcdts = ctr_conditions_get( 0, '', $cdts );
									if( $rcdts && ria_mysql_num_rows($rcdts) ){
										while( $cdt = ria_mysql_fetch_array($rcdts) ){
											$cdt['desc'] = str_replace( '%CTRNAME%', $ctr, $cdt['desc'] );
											print '<option value="'.$cdt['code'].'" title="'.htmlspecialchars( $cdt['desc'] ).'">'.htmlspecialchars( $cdt['name'] ).'</option>';
										}
									}
								?></select>
							</div>
							<div id="cdt-actions">
								<input onclick="return selectedConditions();" class="btn-main" type="button" name="addcdt" id="addcdt" value="<?php print _('Ajouter'); ?>" />
								<a class="del button btn-cancel" href="#" onclick="return hideAddConditions();"><?php print _('Annuler'); ?></a>
							</div>
							<div class="clear"></div>
						</div>
					</div>
					<div class="actions">
						<input onclick="return saveCtrSearch(false);" class="btn-action" type="button" name="save" id="save" value="<?php print _('Sauvegarder'); ?>" title="<?php print _('Sauvegarder la rechercher pour une execution ultérieur'); ?>" />
						<input onclick="return searchProducts();" class="btn-action btn-main" type="submit" name="ctrsearch" id="ctrsearch" value="<?php print _('Rechercher'); ?>" title="<?php print _('Lancer la recherche'); ?>" />
					</div>
				</div>
				<div id="ctr-sh-results">

				</div>
				<input type="hidden" name="sort" id="sort" value="" />
				<input type="hidden" name="dir" id="dir" value="" />
				<input type="hidden" name="ctr-id" id="ctr-id" value="0" />
				<input type="hidden" name="wst-id" id="wst-id" value="<?php print $_SESSION['websitepicker']; ?>" />
			</form>
		</div>

		<input id="marketplace" type="hidden" value="<?php print ($marketplace * 1); ?>" />

		<script><!--
			<?php view_date_initialized( 0, '', false, array() ); ?>
		</script>


<?php
	require_once('admin/skin/footer.inc.php');
?>