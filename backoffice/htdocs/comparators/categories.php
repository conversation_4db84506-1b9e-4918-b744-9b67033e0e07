<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403(['_RGH_ADMIN_COMPARATOR', '_RGH_ADMIN_MARKET_PLACE'], false);

	require_once('comparators.inc.php');
	require_once('http.inc.php');

	// Vérifier si le client à accès aux comparateurs
	if( !$config['ctr_active'] ){
		http_403();
		exit;
	}

	// Vérifie l'existance du comparateur de prix
	if( isset($_GET['ctr']) && !ctr_comparators_exists($_GET['ctr']) ){
		header('Location: categories.php');
		exit;
	}

	$marketplace = isset($_GET['marketplace']) ? $_GET['marketplace'] : false;
	$rfamily = ctr_categories_get( $_GET['ctr'] );
	if( !$rfamily || !ria_mysql_num_rows($rfamily) ){
		header('Location: /admin/comparators/stats/index.php?ctr='.$_GET['ctr']);
		exit;
	}

	//Récupère le nom du comparateur
	$ctr = '';
	if( isset($_GET['ctr']) ){
		$rctr = ctr_comparators_get($_GET['ctr'], false, false, null);
		if ($rctr && ria_mysql_num_rows($rctr)) {
			$ctr = ria_mysql_fetch_array($rctr);
			$marketplace = $ctr['marketplace'];
			$ctr = $ctr['name'];
		}
	}
	// print_r( $_POST );
	// exit;
	//Sauvegarde du filtre apporté aux catégories
	if( isset($_GET['ctr'], $_POST['save'], $_POST['cat-visit'], $_POST['cat']) ){
		strlen($_POST['cat-visit'])>0 ? $cat_visit = explode(',',$_POST['cat-visit']) : $cat_visit = '';
		if( !ctr_categories_unpublish($_GET['ctr'],$_POST['cat'], $cat_visit) ){
			$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
		} else {
			$succes =  sprintf($marketplace ? _("L'enregistrement de vos modifications concernant le filtre sur les catégories de la place de marché %s a bien été effectué") : _('L\'enregistrement de vos modifications concernant le filtre sur les catégories du comparateur de prix %s a bien été effectué'), $ctr);
		}
	} elseif( isset($_GET['ctr'], $_POST['save']) ){
		if( !ctr_categories_unpublish($_GET['ctr']) ){
			$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
		} else {
			$succes =  sprintf($marketplace ? _("L'enregistrement de vos modifications concernant le filtre sur les catégories de la place de marché %s a bien été effectué") : _('L\'enregistrement de vos modifications concernant le filtre sur les catégories du comparateur de prix %s a bien été effectué'), $ctr);
		}
	}

	define('ADMIN_PAGE_TITLE', sprintf($marketplace ? _('Catégories de la place de marché %s') : _('Catégories du comparateur de prix %s'),$ctr));
	require_once('admin/skin/header.inc.php');

	if( !isset($_GET['ctr']) ){ // Aucun comparateur de prix est sélectionné
?>
			<h2><?php print $marketplace ? _('Familles des places de marché') : _('Familles des comparateurs de prix'); ?></h2>
			<?php
				$texte = '';
				if ($marketplace) {
					$texte = _('Afin de faciliter la mise en correspondance de votre catalogue et de celui de la place de marché, vous pouvez masquer les catégories de la place de marché dont vous n\'avez pas besoin. Lors de la recherche de la famille de destination, vous aurez donc moins de catégories à parcourir. Choisissez l\'une des places de marché ci-dessous :');
				} else {
					$texte = _('Afin de faciliter la mise en correspondance de votre catalogue et de celui du comparateur vous pouvez masquer les catégories du comparateur dont vous n\'avez pas besoin. Lors de la recherche de la famille de destination, vous aurez donc moins de catégories à parcourir. Choisissez l\'un des comparateurs ci-dessous :');
				}
			?>
			<p id="exp" class="justify"><?php print $texte; ?><br /></p>
			<ul id="diff-cat">
				<?php
					$r_ctr = ctr_comparators_get();
					while( $ctr = ria_mysql_fetch_array($r_ctr) ){
						print '<li><a href="categories.php?ctr='.$ctr['id'].'">'.$ctr['name'].'</a></li>';
					}
				?>
			</ul>
		<?php } else { // Un comparateur de prix est sélectionné ?>
			<div id="comparators">
				<?php
					require_once('view.ctr.inc.php');
					print view_ctr_header($_GET['ctr']);
				?>
			</div>
			<?php
				// Affichage des messages de succès ou d'échec de la sauvegarde
				if( isset($error) )
					print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
				elseif( isset($succes) )
					print '<div class="error-success ">'.nl2br(htmlspecialchars($succes)).'</div>';
			?>
			<form action="categories.php?ctr=<?php print $_GET['ctr']; ?>" method="post">
				<input type="hidden" name="ctr_id" id="ctr_id" value="<?php print $_GET['ctr']; ?>" />
				<input type="hidden" name="cat-visit" id="cat-visit" value="" />
				<h2><?php printf($marketplace ? _('Familles de la place de marché %s :') : _('Familles du comparateur %s :'), $ctr); ?></h2>
				<div id="family-tree" style="float:none">
					<?php
						$treeFamily = array();
						while( $family = ria_mysql_fetch_array($rfamily) ){
							$nod =& $treeFamily[ $family['id'] ];
							if( !$nod ){
								$nod = array( 'children' => array() );
							}

							$nod['id'] = $family['id'];
							$nod['name'] = htmlspecialchars( $family['title'] );
							$nod['publish'] = $family['publish'];

							if( !$family['parent'] ){
								$family['parent'] = 0;
							}

							$parent =& $treeFamily[ $family['parent'] ];
							if( !$parent ){
								$parent = array( 'children' => array() );
							}
							$parent['children'][ $family['id'] ] =& $treeFamily[ $family['id'] ];
						}

						unset($nod, $parent);

						print '
							<ul class="treeview" id="treeview-cat2">
						';

						foreach( $treeFamily[0]['children'] as $child ){
							print view_cat_childs_arbo( $child, 'checkbox' );
						}

						print '
							</ul>
						';
					?>
				</div>
				<div id="cat-exp">
					<?php
						$texte = '';
						if ($marketplace) {
							$texte = sprintf(_('Afin de faciliter la mise en correspondance de votre catalogue et de celui de la place de marché %s, vous pouvez masquer les catégories de la place de marché dont vous n\'avez pas besoin. Lors de la recherche de la famille de destination, vous aurez donc moins de catégories à parcourir.<br /><br />Pour masquer une famille de la place de marché, il suffit de la cocher ci-contre puis de cliquer sur Enregistrer ci-dessous :'), $ctr);
						} else {
							$texte = sprintf(_('Afin de faciliter la mise en correspondance de votre catalogue et de celui du comparateur %s, vous pouvez masquer les catégories du comparateur dont vous n\'avez pas besoin. Lors de la recherche de la famille de destination, vous aurez donc moins de catégories à parcourir.<br /><br />Pour masquer une famille du comparateur, il suffit de la cocher ci-contre puis de cliquer sur Enregistrer ci-dessous :'), $ctr);
						}
					?>
					<p class="justify"><?php print $texte; ?></p>
					<input class="action" type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
				</div>

			</form>
		<?php
	}

	require_once('admin/skin/footer.inc.php');
?>