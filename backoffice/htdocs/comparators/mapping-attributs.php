<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403(['_RGH_ADMIN_COMPARATOR', '_RGH_ADMIN_MARKET_PLACE'], false);

	require_once('comparators.inc.php');
	require_once('categories.inc.php');
	require_once('http.inc.php');

	// Vérifier si le client à accès aux comparateurs
	if( !$config['ctr_active'] ){
		http_403();
		exit;
	}

	// Vérifie l'existance du comparateur de prix
	if( !isset($_GET['ctr']) || !ctr_comparators_exists($_GET['ctr']) ){
		header('Location: index.php');
		exit;
	}

	//Récupère le nom du comparateur
	$ctr = ria_mysql_fetch_array(ctr_comparators_get($_GET['ctr'], true, false, null));
	$marketplace = $ctr['marketplace'];
	$ctr = $ctr['name'];

	$fld_type_accept = array( FLD_TYPE_TEXT, FLD_TYPE_TEXTAREA, FLD_TYPE_INT, FLD_TYPE_FLOAT, FLD_TYPE_BOOLEAN_YES_NO );

	$only_used = true;
	$mandatory = null;
	$fields = $mapping = array();

	if( isset($_GET['only-used']) || isset($_GET['mandatory']) ){
		$only_used = isset($_GET['only-used']) ? true : false;

		if( isset($_GET['mandatory']) && $_GET['mandatory'] != 'all' ){
			$mandatory = $_GET['mandatory'] === '1' ? true : false;
		}
	}

	$rattr = ctr_cat_fields_get( $_GET['ctr'], 0, '', true, ($only_used ? true : false), $mandatory, ($_GET['ctr'] == CTR_CDISCOUNT ? false : true) );
	$nb_attr = $rattr ? ria_mysql_num_rows($rattr) : 0;


	if( $nb_attr > 0 ){
		$rfield = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), false, array(), null, CLS_PRODUCT, null, false );
		if( $rfield ){
			while( $field = ria_mysql_fetch_assoc($rfield) ){
				if( !isset($fields[ $field['type_id'] ]) ){
					$fields[ $field['type_id'] ] = array();
				}

				$fields[ $field['type_id'] ][] = $field;
			}
		}

		$rattr_field = ctr_cat_field_mapping_get( $_GET['ctr'] );
		if( $rattr_field ){
			while( $attr_field = ria_mysql_fetch_assoc($rattr_field) ){
				$mapping[ $attr_field['ccf_code'] ] = $attr_field['fld_id'];
			}
		}
	}
	$url = '/admin/comparators/mapping-attributs.php?ctr='.$_GET['ctr'].'&marketplace='.($marketplace ? '1' : '0');
	if( $mandatory === null ){
		$url .= '&mandatory=all';
	}else{
		$url .= '&mandatory='.( $mandatory ? '1' : '0' );
	}
	if( $only_used ){
		$url .= '&only-used=1';
	}

	if( isset($_POST['save-mapping']) ){
		if( isset($_POST['attr']) && is_array($_POST['attr']) ){
			foreach( $_POST['attr'] as $id=>$fld ){
				// Suppression des valeurs déjà mapping avec ce code
				if(!ctr_cat_field_mapping_del($_GET['ctr'], $id) ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre mapping.");
				}elseif( trim($fld) != '' && !ctr_cat_field_mapping_add( $_GET['ctr'], $id, $fld ) ){ // ajout du mapping
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre mapping.");
				}
			}
		}

		if( !isset($error) ){
			header('Location: '.$url);
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Mapping des attributs').' - '.htmlspecialchars($ctr));
	require_once('admin/skin/header.inc.php');
?>
	<div id="comparators">
		<?php
			require_once('view.ctr.inc.php');
			print view_ctr_header($_GET['ctr']);
		?>
	</div>

	<h2><?php printf( $marketplace ? _('Mapping des attributs de la place de marché %s') : _('Mapping des attributs du comparateur %s'), $ctr ); ?></h2>
	<div class="form-notice-container">
		<div class="notice"><?php printf( $marketplace ? _('Vous pouvez ici réaliser un mapping des attributs de la place de marché %s vers les informations de vos fiches produits. Cela vous permettra de pré-remplir les informations nécessaire à l\'exportation de vos produits à renseigner directement depuis leur fiche.') : _('Vous pouvez ici réaliser un mapping des attributs du comparateur %s vers les informations de vos fiches produits. Cela vous permettra de pré-remplir les informations nécessaire à l\'exportation de vos produits à renseigner directement depuis leur fiche.'), $ctr); ?></div>

		<form action="/admin/comparators/mapping-attributs.php" method="get">
			<input type="hidden" name="ctr" value="<?php print $_GET['ctr']; ?>" />
			<input type="hidden" name="marketplace" value="<?php print $marketplace ? '1' : '0'; ?>" />

			<div class="list-filters">
				<div class="filter-label"><?php print _('Filtrer'); ?></div>
				<div class="filter">
					<label for="mandatory-yes"><?php print _('Afficher les attributs :'); ?></label>
					<input type="radio" name="mandatory" id="mandatory-yes" value="1" <?php print $mandatory === true ? 'checked="checked"' : ''; ?> />
					<label for="mandatory-yes"><?php print _('Obligatoires'); ?></label>
					<input type="radio" name="mandatory" id="mandatory-no" value="0" <?php print $mandatory === false ? 'checked="checked"' : ''; ?> />
					<label for="mandatory-no"><?php print _('Optionnels'); ?></label>
					<input type="radio" name="mandatory" id="mandatory-all" value="all" <?php print $mandatory === null ? 'checked="checked"' : ''; ?> />
					<label for="mandatory-all"><?php print _('Tous'); ?></label>
				</div>
				<div class="filter">
					<input type="checkbox" name="only-used" id="only-used" value="1" <?php print $only_used ? 'checked="checked"' : ''; ?> />
					<label for="only-used"><?php print _('N\'afficher que les attributs utilisés'); ?></label>
				</div>

				<input class="btn-action" type="submit" name="filter" value="<?php print _('Filtrer'); ?>" />
			</div>
		</form>

		<form action="<?php print $url; ?>" method="post" />
			<table class="checklist" cellpadding="0" cellspacing="0">
				<caption><?php print _('Liste des attributs'); ?></caption>
				<col width="300" /><col width="400" />
				<thead>
					<tr>
						<th id="attr-name" class="thead-none"><?php print _('Attribut'); ?></th>
						<th id="attr-field" class="thead-none"><?php print _('Lié à'); ?></th>
					</tr>
				</thead>
				<tfoot>
					<tr>
						<td colspan="2" style="align-right">
							<input type="submit" name="save-mapping" value="<?php print _('Enregistrer'); ?>" />
						</td>
					</tr>
				</tfoot>
				<tbody><?php
					$ar_attr_not_mapping = array();
					$have_attr = false; $unique_code = array();
					if( $rattr && ria_mysql_num_rows($rattr) ){
						while( $attr = ria_mysql_fetch_assoc($rattr) ){
							$ar_prds = ctr_cat_fields_get_products( $_GET['ctr'], $attr['id'] );

							if( $only_used ){
								if( !is_array($ar_prds) || !sizeof($ar_prds) ){
									continue;
								}
							}

							if( !in_array($attr['type_id'], $fld_type_accept) ){
								$ar_attr_not_mapping[] = array_merge( $attr, array('products'=>$ar_prds) );
								continue;
							}

							if( in_array($attr['code'], ctr_cat_fields_mapping_exclude_codes()) /*|| in_array($attr['code'], $unique_code)*/ ){
								continue;
							}

							if( !array_key_exists( $attr['type_id'], $fields) ){
								continue;
							}
				?>
					<tr>
						<td headers="attr-name" <?php print 'data-label="'._('Attribut :').'" '?>><?php
							print htmlspecialchars( $attr['name'] );
							print '<sub>'.htmlspecialchars( $attr['type_name'] ).'<sub>';
							print ( trim($attr['unite']) != '' ? '<sub>'._('Unité').' : '.htmlspecialchars( $attr['unite'] ).'</sub>' : '' );
						?></td>

						<td headers="attr-field" <?php print 'data-label="'._('lié à :').'" '?>>
							<select name="attr[<?php print $attr['id']; ?>]">
								<option value=""><?php print _('Sélectionnez un champ'); ?></option>
								<?php
									foreach( $fields as $type=>$ar_fields ){
										if( in_array($attr['type_id'], array(FLD_TYPE_FLOAT)) ){
											if( !in_array($type, array(FLD_TYPE_INT, FLD_TYPE_FLOAT)) ){
												continue;
											}
										}else{
											if( $attr['type_id'] != $type ){
												continue;
											}
										}

										foreach( $ar_fields as $one_field ){
											$selected = '';
											if( isset($mapping[ $attr['code'] ]) && $mapping[ $attr['code'] ] == $one_field['id'] ){
												$selected = ' selected="selected"';
											}

											print '<option value="'.$one_field['id'].'"'.$selected.'>'.htmlspecialchars( $one_field['name'] ).'</option>';
										}
									}
								?>
							</select>
							<br /><a href="#" onclick="return showPopupEditAttr(<?php print $_GET['ctr']; ?>, '<?php print $attr['id']; ?>');"><?php printf(sizeof($ar_prds)>1 ? _('Saisir l\'information pour %d produits utilisant ce champ') : _('Saisir l\'information pour %d produit utilisant ce champ'), sizeof($ar_prds)); ?></a>
						</td>

				<?php
							$unique_code[] = $attr['code'];
							$have_attr = true;
						}
					}

					if( !$have_attr ){
						print '<tr><td colspan="2">'._('Aucun mapping d\'attribut n\'est à réaliser.').'</td></tr>';
					}
				?></tbody>
			</table>
		</form>

		<p class="notice"><?php print $marketplace ? _('Vous trouverez ci-dessous la liste des attributs ne pouvant être reliés à une information produit présente dans RiaShop car les valeurs acceptées sont restreintes par la place de marché. Vous pouvez toutefois choisir la valeur qui sera exportée pour chaque produit.') : _('Vous trouverez ci-dessous la liste des attributs ne pouvant être reliés à une information produit présente dans RiaShop car les valeurs acceptées sont restreintes par le comparateur. Vous pouvez toutefois choisir la valeur qui sera exportée pour chaque produit.'); ?></p><br />

		<form action="<?php print $url; ?>" method="post" />
			<table class="checklist" cellpadding="0" cellspacing="0">
				<caption><?php print _('Liste des attributs'); ?></caption>
				<col width="300" /><col width="400" />
				<thead>
					<tr>
						<th id="attr-name" class="thead-none"><?php print _('Attribut'); ?></th>
						<th id="attr-field" class="thead-none"><?php print _('Lié à'); ?></th>
					</tr>
				</thead>
				<tfoot>
					<tr>
						<td colspan="2" style="align-right">
							<input type="submit" name="save-mapping" value="<?php print _('Enregistrer'); ?>" />
						</td>
					</tr>
				</tfoot>
				<tbody><?php
					$have_attr = false; $unique_code = array();
					if( $rattr && ria_mysql_num_rows($rattr) ){
						foreach( $ar_attr_not_mapping as $attr ){
							if( in_array($attr['code'], ctr_cat_fields_mapping_exclude_codes()) || in_array($attr['code'], $unique_code) ){
								continue;
							}

							$ar_prds = $attr['products'];
				?>
					<tr>
						<td headers="attr-name" <?php print 'data-label="'._('Attribut :').'" '?>><?php
							print htmlspecialchars( $attr['name'] );
							print '<sub>'.htmlspecialchars( $attr['type_name'] ).'<sub>';
						?></td>

						<td headers="attr-field"  <?php print 'data-label="'._('Lié à :').'" '?>>
							<a href="#" onclick="return showPopupEditAttr(<?php print $_GET['ctr']; ?>, '<?php print $attr['id']; ?>');"><?php printf(sizeof($ar_prds)>1 ? _('Saisir l\'information pour %d produits utilisant ce champ') : _('Saisir l\'information pour %d produit utilisant ce champ'), sizeof($ar_prds)); ?></a>
						</td>

				<?php
							$unique_code[] = $attr['code'];
							$have_attr = true;
						}
					}

					if( !$have_attr ){
						print '<tr><td colspan="2">'._('Aucun mapping d\'attribut n\'est à réaliser.').'</td></tr>';
					}
				?></tbody>
			</table>
		</form>
	</div>
	<input id="marketplace" type="hidden" value="<?php print ($marketplace * 1); ?>" />
<?php
	require_once('admin/skin/footer.inc.php');
?>