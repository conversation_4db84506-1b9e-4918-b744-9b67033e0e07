<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403(['_RGH_ADMIN_COMPARATOR', '_RGH_ADMIN_MARKET_PLACE'], false);

	require_once('comparators.inc.php');
	require_once('categories.inc.php');
	require_once('http.inc.php');

	// Vérifier si le client à accès aux comparateurs
	if( !$config['ctr_active'] ){
		http_403();
		exit;
	}

	// Vérifie l'existance du comparateur de prix
	if( !isset($_GET['ctr']) || !ctr_comparators_exists($_GET['ctr']) ){
		header('Location: index.php');
		exit;
	}

	//Récupère le nom du comparateur
	$ctr = ria_mysql_fetch_array(ctr_comparators_get($_GET['ctr'], true, false, null));
	$marketplace = $ctr['marketplace'];
	$ctr = $ctr['name'];

	// Enregistre les redirections entre catégories
	if( isset($_POST['save-new-id']) ){
		// Array ( [ctr_id] => 4 [save-new-id] => Enregistrer [new-cat] => Array ( [8556] => 8563 ) )
		if( isset($_POST['new-cat']) && is_array($_POST['new-cat']) && sizeof($_POST['new-cat']) ){
			foreach( $_POST['new-cat'] as $cat=>$redirection ){
				if( !is_numeric($redirection) || $redirection<=0 ){
					continue;
				}

				if( !ctr_cat_redirections_update($_GET['ctr'], $cat, $redirection) ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des redirections entre famille.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
		}

		if( !isset($error) ){
			$_SESSION['save-family-redir'] = true;
			header('Location: /admin/comparators/link-categories.php?ctr='.$_GET['ctr']);
			exit;
		}
	}

	if( !isset($_SESSION['websitepicker']) || !is_numeric($_SESSION['websitepicker']) || !$_SESSION['websitepicker'] ){
		$_SESSION['websitepicker'] = $config['wst_id'];
	}

	if( isset($_GET['wst']) && is_numeric($_GET['wst']) && $_GET['wst'] ){
		$_SESSION['websitepicker'] = $_GET['wst'];
	}

	define('ADMIN_PAGE_TITLE', sprintf(_('Rattacher vos catégories produits à %s'),htmlspecialchars($ctr) ) );
	require_once('admin/skin/header.inc.php');
?>
			<div id="comparators">
				<?php
					require_once('view.ctr.inc.php');
					print view_ctr_header($_GET['ctr']);
				?>
			</div>
			<?php
				// Affichage des messages de succès ou d'échec de la sauvegarde
				if( isset($error) ){
					print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
				}else{
					if( isset($_SESSION['save-family-redir']) ){
						print '<div class="success">'._('L\'enregistrement des redirections s\'est correctement déroulé.').'</div>';
						unset( $_SESSION['save-family-redir'] );
					}elseif( isset($_SESSION['del-family-redir']) ){
						print '<div class="success">'._('La suppression des redirections s\'est correctement déroulée.').'</div>';
						unset( $_SESSION['del-family-redir'] );
					}
				}
			?>

			<?php
				$rcat = ctr_categories_get( $_GET['ctr'], 0, '', 0, false, '', true, false, true );
				if( $rcat && ria_mysql_num_rows($rcat) ){
			?>

			<h2><?php print _('Familles désactivées'); ?></h2>
			<form action="/admin/comparators/link-categories.php?ctr=<?php print $_GET['ctr']; ?>" method="post">
				<input type="hidden" name="ctr_id" id="ctr_id" value="<?php print $_GET['ctr']; ?>" />
				<p><?php printf($marketplace ? _('Vous trouverez ci-dessous la liste des familles de la place de marché %s actuellement utilisées, mais qui ne sont plus disponibles') : _('Vous trouverez ci-dessous la liste des familles du comparateur de prix %s actuellement utilisées, mais qui ne sont plus disponibles') , htmlspecialchars( $ctr ) ); ?> </p>
				<table id="choose-new-cat" class="checklist">
					<caption><?php print _('Liste des familles désactivées'); ?></caption>
					<col width="300" /><col width="300" /><col width="100" />
					<thead>
						<tr>
							<th id="desactived-name"><?php print _('Famille'); ?></th>
							<th id="desactived-choose"><?php print _('Redirigée vers'); ?></th>
							<th id="desactived-action"></th>
						</tr>
					</thead>
					<tfoot>
						<tr>
							<td colspan="3">
								<input type="submit" name="save-new-id" value="<?php print _('Enregistrer'); ?>" />
							</td>
						</tr>
					</tfoot>
					<tbody><?php
						if( !$rcat || !ria_mysql_num_rows($rcat) ){
							print '
								<tr>
									<td colspan="3">'._('Aucune famille utilisée n\'est actuellement désactivée').'</td>
								</tr>
							';
						}else{
							while( $cat = ria_mysql_fetch_array($rcat) ){
								$c = ctr_categories_get_count_used( $_GET['ctr'], $cat['id'] );

								$new_cat = array( 'id' => 0, 'name' => '' );
								if( isset($_POST['new-cat'][$cat['id']]) ){
									if( is_numeric($_POST['new-cat'][$cat['id']]) && $_POST['new-cat'][$cat['id']]>0 ){
										$rnew = ctr_categories_get( $_GET['ctr'], $_POST['new-cat'][$cat['id']] );
										if( $rnew && ria_mysql_num_rows($rnew) ){
											$new_cat = ria_mysql_fetch_array( $rnew );
										}
									}
								}

								$new_sub_text = '(Utilisée par ';
								if( $c['categories'] > 0 ){
									$new_sub_text .= '<a onclick="showDisabledFamily('.$_GET['ctr'].', '.$cat['id'].', '.CLS_CATEGORY.');">'.$c['categories'].' catégorie'.( $c['categories']>1 ? 's' : '' ).'</a>';

									if( $c['products'] > 0 ){
										$new_sub_text .= ' et ';
									}
								}

								if( $c['products'] > 0 ){
									$new_sub_text .= '<a onclick="showDisabledFamily('.$_GET['ctr'].', '.$cat['id'].', '.CLS_PRODUCT.');">'.$c['products'].' produit'.( $c['products']>1 ? 's' : '' ).'</a>';
								}

								$new_sub_text .= ')';

								print '
									<tr id="fml-disabled-'.$cat['id'].'">
										<td headers="desactived-name">
											'.htmlspecialchars( $cat['title'] ).'
											<sub>'.$new_sub_text.'</sub>
										</td>
										<td headers="desactived-choose">
											<input type="hidden" name="new-cat['.$cat['id'].']" id="new-cat-'.$cat['id'].'" value="'.$new_cat['id'].'" />
											<span class="name-cat">'.( trim($new_cat['name'])!='' ? htmlspecialchars( $new_cat['name'] ) : _('Aucune redirection') ).'</span>
										</td>
										<td headers="desactived-action">
											<input class="choose-new-cat" type="button" name="choose-new-cat" value="'._('Choisir une famille').'" title="'._('Sélection une nouvelle famille').'" />
										</td>
									</tr>
								';
							}
						}
					?></tbody>
				</table>
			</form>
			<?php } ?>

			<div class="clear"></div>

			<h2><?php printf($marketplace ? _('Exportation de vos catégories produits vers la place de marché %s') : _('Exportation de vos catégories produits vers le comparateur %s'), $ctr); ?></h2>
			<?php
				if( !$marketplace ){
					print view_websites_selector( $_SESSION['websitepicker'], false );
				}
			?>
			<form id="form-cat-prd" action="link-categories.php?ctr=<?php print $_GET['ctr']; ?>" method="post">
				<input type="hidden" name="cat-visit" id="cat-visit" value="" />
				<input type="hidden" name="cat-link" id="cat-link" value="" />
				<p><?php printf($marketplace ? _('Vous pouvez rattacher vos catégories publiées avec une des familles de la place de marché %s :') : _('Vous pouvez rattacher vos catégories publiées avec une des familles du comparateur de prix %s :' ), htmlspecialchars( $ctr ) ); ?></p>
				<div id="categories-tree">
				<?php
					$treeCats = array();

					$exclude = false;
					$cat_root = cfg_overrides_get_value('cat_root', $_SESSION['websitepicker']);

					if( $cat_root ){
						$ar_cat_root = array();

						$rroot = cfg_overrides_get(false, array(), 'cat_root');
						if( $rroot && ria_mysql_num_rows($rroot) ){
							while( $root = ria_mysql_fetch_array($rroot) ){
								if( $root['value'] != $cat_root ){
									$ar_cat_root[] = $root['value'];
								}
							}
						}

						foreach( array_unique(array_filter($ar_cat_root)) as $once_cat ){
							$exclude[] = $once_cat;
							$rcat = prd_categories_get(0, false, $once_cat, '', false, false, null, false, array(), true);
							if( $rcat && ria_mysql_num_rows($rcat) ){
								while( $cat = ria_mysql_fetch_array($rcat) ){
									$exclude[] = $cat['id'];
								}
							}
						}
					}

					$ar_categs = $ar_ids = array();

					$rcat = prd_categories_get(0, false, -1, '', false, false, null, false, array(), false, false, false, false, false, $exclude);
					if( $rcat && ria_mysql_num_rows($rcat) ){
						while( $cat = ria_mysql_fetch_array($rcat) ){
							if( !in_array($cat['id'], $ar_ids) ){
								$ar_categs[] = $cat;
								$ar_ids[] = $cat['id'];
							}
						}
					}

					foreach( $ar_categs as $cat ){
						// Rattachement
						$link = '';
						if( ctr_prd_categories_is_publish($_GET['ctr'], $cat['id']) ){
							$cat_name = '';

							$rcatctr = ctr_prd_categories_get( $_GET['ctr'], $cat['id'] );
							if( $rcatctr && ria_mysql_num_rows($rcatctr) ){
								$cat_name = ria_mysql_result( $rcatctr, 0, 'name' );
							}

							$link = '<span class="card-title-ctr">('.(trim($cat_name)!='' ? sprintf(_('rattachée à %s'), htmlspecialchars( $cat_name ) ) : _('rattachée')).')</span>';
						}

						$nod =& $treeCats[ $cat['id'] ];
						if( !$nod ){
							$nod = array( 'children' => array() );
						}

						$nod['id'] = $cat['id'];
						$nod['name'] = htmlspecialchars( $cat['title'] ).' '.$link;

						if( !$cat['parent_id'] ){
							$cat['parent_id'] = 0;
						}

						$parent =& $treeCats[ $cat['parent_id'] ];
						if( !$parent ){
							$parent = array( 'children' => array() );
						}
						$parent['children'][ $cat['id'] ] =& $treeCats[ $cat['id'] ];
					}

					unset($nod, $parent);

					print '
						<ul class="treeview">
					';

					foreach( $treeCats[0]['children'] as $child ){
						print view_cat_childs_arbo( $child );
					}

					print '
							</ul>
						<div class="clear"></div>
					';
				?>
				</div>
				<div id="block-card">
					<div id="card" style="display:none">
						<div id="card2"></div>
						<?php
							$rfamily = ctr_categories_get($_GET['ctr'], 0, '', true);
							if( $rfamily && ria_mysql_num_rows($rfamily) ){
						?>
							<script>
								var has_ctr_categories = true;
							</script>
							<div id="treeview-card" style="display:none">
								<?php print $marketplace ? _('Choisissez une des familles de la place de marché ci-dessous :') : _('Choisissez une des familles du comparateur ci-dessous :'); ?>
								<div id="treeview-cat-card">
									<?php
										$treeFamily = array();
										while( $family = ria_mysql_fetch_array($rfamily) ){
											$nod =& $treeFamily[ $family['id'] ];
											if( !$nod ){
												$nod = array( 'children' => array() );
											}

											$nod['id'] = $family['id'];
											$nod['name'] = $family['title'];

											if( !$family['parent'] ){
												$family['parent'] = 0;
											}

											$parent =& $treeFamily[ $family['parent'] ];
											if( !$parent ){
												$parent = array( 'children' => array() );
											}
											$parent['children'][ $family['id'] ] =& $treeFamily[ $family['id'] ];
										}
										unset($nod, $parent);
										print '
											<ul class="treeview" id="treeview-cat2">
										';

										foreach( $treeFamily[0]['children'] as $child ){
											print view_cat_childs_arbo( $child, 'radio', $_GET['ctr'] );
										}

										print '
											</ul>
										';
									?>
								</div>
							</div>
						<?php } else { ?>
							<script>
								var has_ctr_categories = false;
							</script>
						<?php } ?>
						<div id="card-action">
							<input type="submit" name="save_card" id="save_card" value="<?php print _('Enregistrer'); ?>" onclick="return saveLinkCategories(<?php print $_GET['ctr']; ?>);" />
						</div>
					</div>
				</div>
			</form>

			<input id="marketplace" type="hidden" value="<?php print ($marketplace * 1); ?>" />
<?php
	require_once('admin/skin/footer.inc.php');
?>