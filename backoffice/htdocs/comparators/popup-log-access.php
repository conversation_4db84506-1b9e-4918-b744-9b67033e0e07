<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403(['_RGH_ADMIN_COMPARATOR', '_RGH_ADMIN_MARKET_PLACE'], false);

	require_once('comparators.inc.php');
	if( !isset($_GET['ctr_id']) || !ctr_comparators_exists($_GET['ctr_id']) ){
		$error_load = _("Impossible de charger l'historique des accès au flux du catalogue.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
	}

	define('ADMIN_PAGE_TITLE', _('Historique des accès - Comparateurs'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($error_load) ){
		print '<div class="error">'.nl2br( $error_load ).'</div>';
	}else{
		$ctr = ria_mysql_fetch_array( ctr_comparators_get($_GET['ctr_id']) );
?>

	<p><?php printf(_('Vous trouverez ci-dessous l\'historique des accès au flux catalogue pour le comparateur de prix %s :'), $ctr['name'] ); ?></p>
	<table cellpadding="0" cellspacing="0">
		<caption><?php print _('Historique'); ?></caption>
		<tbody style="text-align:center;"><?php
			$rlog = ctr_comparator_logs_get( $_GET['ctr_id'], false, false, 5 );
			if( !$rlog || !ria_mysql_num_rows($rlog) ){
				print '
					<tr>
						<td>'._('Aucun accès au flux n\'est enregistré pour le moment.').'</td>
					</tr>
				';
			}else{
				while( $log = ria_mysql_fetch_array($rlog) ){
					print '
						<tr>
							<td>Le '.$log['date'].'</td>
						</tr>
					';
				}
			}
		?></tbody>
	</table>
	<?php } ?>
	<script><!--
		<?php if( isset($_POST['save']) && !isset($error) ){ ?>
			parent.reloadEBayShipping(<?php print json_encode($carrier); ?>);
		<?php } ?>
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>