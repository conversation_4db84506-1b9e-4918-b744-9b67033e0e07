<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_MARKET_PLACE');

	require_once('comparators/ctr.ebay.inc.php');

	$ebay = new EBay();
	$shipping = $ebay->getShippingServices();
	if( !is_array($shipping) || !sizeof($shipping) ){
		$error_load = _("Une erreur inattendue s'est produite lors de la récupération des transporteur d'eBay.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
	}

	// Enregistre la nouvelle assocciation
	if( isset($_POST['save']) ){
		if( !isset($_POST['ebay-carrier']) || !$_POST['ebay-carrier'] ){
			$error = _("Veuillez sélectionner un transporteur eBay.");
		}elseif( !isset($_POST['ebay-service']) || !$_POST['ebay-service'] ){
			$error = _("Veuillez sélectionner l'un de vos services de livraison.");
		}elseif( trim($_POST['ebay-cost'])!='' && !is_numeric($_POST['ebay-cost']) ){
			$error = _("La valeur saisie pour les frais supplémentaires n'est pas reconnue comme étant un numérique.");
		}else{
			$carrier = cfg_overrides_get_value( 'ebay_carriers' );
			if( trim($carrier)!='' ){
				$carrier = json_decode( $carrier, true );
				$carrier[ $_POST['ebay-service'] ] = array( 'service' => $_POST['ebay-service'], 'carrier'=>$_POST['ebay-carrier'], 'cost-supp' => $_POST['ebay-cost'] );
			}else{
				$carrier = array(
					$_POST['ebay-service'] => array( 'service' => $_POST['ebay-service'], 'carrier'=>$_POST['ebay-carrier'], 'cost-supp' => $_POST['ebay-cost'] )
				);
			}

			if( !cfg_overrides_set_value('ebay_carriers', json_encode($carrier)) ){
				$error = _("Une erreur inattendue s'est produite lors de l'ajout de la nouvelle association transporteur - service de livrasion.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Ajouter un service de livraison') . ' - ' . _('eBay') . ' - ' . _('Places de marché'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($error_load) ){
		print '<div class="error">'.nl2br( $error_load ).'</div>';
	}else{
?>

	<form action="/admin/comparators/eBay/popup-add-shipping.php" method="post">
		<p><?php print _('Vous pouvez associer un transporteur eBay à l\'un de vos services de livraison, RiaShopping tiendra compte des services disponibles pour chaque produit lors de son exportation.'); ?></p>
		<p><?php print _('Les frais supplémentaires sont optionnels et sont appliqués à chaque produit supplémentaire contenu dans une même commande eBay. Si un internaute commande 2 fois le même produit dans une même commande, les frais supplémentaires seront aussi appliqués sur le second.'); ?></p>

		<?php
			if( isset($error) ){
				print '<div class="error">'.nl2br( $error ).'</div>';
			}
		?>

		<table>
			<caption><?php print _('Ajouter un service de livraison'); ?></caption>
			<col width="175" /><col width="*" />
			<tfoot>
				<tr>
					<td colspan="2">
						<input type="button" name="cancel" value="<?php print _('Annuler'); ?>" onclick="parent.hidePopup();" />
						<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
					</td>
				</tr>
			</tfoot>
			<tbody>
				<tr>
					<th><label for="evay-carrier"><span class="mandatory">*</span> <?php print _('Transporteur eBay :'); ?></label></th>
					<td><?php
						print '
							<select name="ebay-carrier" id="ebay-carrier">
								<option value=""></option>
						';

						foreach( $shipping as $code=>$s ){
							if( $s['InternationalService'] ){
								continue;
							}

							$selected = isset($_POST['ebay-carrier']) && $_POST['ebay-carrier']==$code ? 'selected="selected"' : '';

							print '
								<option '.$selected.' value="'.$code.'">'.htmlspecialchars( $s['Description'] ).'</option>
							';
						}

						print '
							</select>
						';
					?></td>
				</tr>
				<tr>
					<th><label for="ebay-service"><span class="mandatory">*</span> <?php print _('Service de livraison :'); ?></label></th>
					<td><?php
						$rsrv = dlv_services_get();
						if( $rsrv && ria_mysql_num_rows($rsrv) ){
							print '
								<select name="ebay-service" id="ebay-service">
									<option value=""></option>
							';

							while( $srv = ria_mysql_fetch_array($rsrv) ){
								$selected = isset($_POST['ebay-service']) && $_POST['ebay-service']==$srv['id'] ? 'selected="selected"' : '';

								print '
									<option '.$selected.' value="'.$srv['id'].'">'.htmlspecialchars( $srv['name'] ).'</option>
								';
							}

							print '
								</select>
							';
						}
					?></td>
				</tr>
				<tr>
					<th><label for="ebay-cost"><?php print _('Frais supplémentaire'); ?></label> :</th>
					<td>
						<input class="number" type="text" name="ebay-cost" id="ebay-cost" value="<?php print isset($_POST['ebay-cost']) ? $_POST['ebay-cost'] : ''; ?>" />&nbsp;€
					</td>
				</tr>
			</tbody>
	</form>

<?php
	}

	require_once('admin/skin/header.inc.php');
?>