<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_MARKET_PLACE');

	require_once('comparators/ctr.ebay.inc.php');

	$eBay = new EBay();

	if( isset($_GET['delShipping'], $_GET['del']) ){
		if( is_array($_GET['del']) ){
			$carrier = cfg_overrides_get_value( 'ebay_carriers' );
			$carrier = json_decode($carrier, true);

			foreach( $_GET['del'] as $del ){
				if( isset($carrier[$del]) ){
					unset( $carrier[$del] );
				}
			}

			if( cfg_overrides_set_value('ebay_carriers', json_encode($carrier)) ){
				$_GET['reloadShipping'] = true;
			}
		}
	}

	if( isset($_GET['reloadShipping']) ){
		$html = '';

		$carrier = cfg_overrides_get_value( 'ebay_carriers' );
		$carrier = json_decode($carrier, true);

		$has_srv = false;
		if( is_array($carrier) && sizeof($carrier) ){
			foreach( $carrier as $key=>$c ){
				$srv = dlv_services_get_name( $c['service'] );

				$crr = $eBay->getShippingServices( $c['carrier'] );
				if( !isset($crr['Description']) ){
					continue;
				}

				$html .= '
					<tr>
						<td><input type="checkbox" name="del[]" value="'.$key.'" /></td>
						<td>'.htmlspecialchars( $srv ).'</td>
						<td>'.htmlspecialchars( $crr['Description'] ).'</td>
						<td>'.( $c['cost-supp']>0 ? number_format( $c['cost-supp'], 2, ',', ' ' ) : '0,00' ).' €</td>
					</tr>
				';

				$has_srv = true;
			}
		}

		if( !$has_srv ){
			$html .= '
				<tr>
					<td colspan="4">'._('Aucun service de livraison n\'est configuré, l\'export des produits ne pourra pas avoir lieu.').'</td>
				</tr>
			';
		}

		print $html;
		return;
	}
