<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403(['_RGH_ADMIN_COMPARATOR', '_RGH_ADMIN_MARKET_PLACE'], false);

	require_once('products.inc.php');
	require_once('comparators.inc.php');
	require_once('stats.inc.php');
	require_once('view.admin.inc.php');

	// chargement du comparateur
	if( !isset($_GET['ctr']) || !($ctr = ria_mysql_fetch_array( ctr_comparators_get( $_GET['ctr'], true, false, null) )) ){
		$error_g = true;
	}

	if( !isset($_GET['oldcat']) || !is_numeric($_GET['oldcat']) || !$_GET['oldcat'] ){
		$error_g = true;
	}else{
		$rfamily = ctr_categories_get( $_GET['ctr'], $_GET['oldcat'], '', 0, false, '', true );
		if( !$rfamily ){
			$error_g = true;
		}
	}

	if( !isset($_GET['class']) || !fld_classes_exists($_GET['class']) ){
		$error_g = true;
	}

	if( !isset($error_g) ){
		if( isset($_GET['save-new-id']) ){
			foreach( $_GET['new-cat'] as $key_one_new=>$one_new ){
				if( $_GET['class'] == CLS_CATEGORY ){
					$res = ctr_prd_categories_set_family( $_GET['ctr'], $one_new, $key_one_new );
				}elseif( $_GET['class'] == CLS_PRODUCT ){
					$res = ctr_catalogs_set_family( $_GET['ctr'], $one_new, $key_one_new );
				}
			}

			if( !isset($error) ){
				$c = ctr_categories_get_count_used( $_GET['ctr'], $_GET['oldcat'] );

				if( $c['categories'] <= 0 && $c['products'] <= 0 ){
					$new_sub_text = '';
				}else{
					$new_sub_text = '(Utilisée par ';
					if( $c['categories'] > 0 ){
						$new_sub_text .= '<a onclick="showDisabledFamily('.$_GET['ctr'].', '.$_GET['oldcat'].', '.CLS_CATEGORY.');">'.$c['categories'].' catégorie'.( $c['categories']>1 ? 's' : '' ).'</a>';

						if( $c['products'] > 0 ){
							$new_sub_text .= ' et ';
						}
					}

					if( $c['products'] > 0 ){
						$new_sub_text .= '<a onclick="showDisabledFamily('.$_GET['ctr'].', '.$_GET['oldcat'].', '.CLS_PRODUCT.');">'.$c['products'].' produit'.( $c['products']>1 ? 's' : '' ).'</a>';
					}

					$new_sub_text .= ')';
				}
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Choix d\'une famille'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	print '
		<div id="popupctr" class="details-family-disabled">
			<form action="popup-details-family.php" method="get">
				<div id="tabpanel">
	';

	if( isset($error_g) ){
		print '
					<div class="error">'._('Un ou plusieurs paramètres obligatoires sont manquants empêchant le formulaire de se charger correctement.').' '._('Veuillez réessayer ou prendre contact pour nous signaler le problème.').'</div>
		';
	}else{
		print '
			<input type="hidden" name="ctr" value="'.$_GET['ctr'].'" />
			<input type="hidden" name="oldcat" value="'.$_GET['oldcat'].'" />
			<input type="hidden" name="class" value="'.$_GET['class'].'" />
		';

		$family = ria_mysql_fetch_assoc( $rfamily );

		$th_head = $title = '';
		switch( $_GET['class'] ){
			case CLS_CATEGORY: {
				$title = _('Liste des catégories');
				$desc = _('Vous trouverez ci-dessous la liste de toutes les catégories rattachées à cette famille désactivée et la possibilité de choisir une nouvelle famille pour chaque catégorie.');
				$th_head = _('Catégorie');
				$no_object = _('Aucune catégorie rattachée à cette famille désactivée.');
				break;
			}
			case CLS_PRODUCT: {
				$title = _('Liste des produits');
				$desc = _('Vous trouverez ci-dessous la liste de tous les produits rattachés à cette famille désactivée et la possibilité de choisir une nouvelle famille pour chaque produit.');
				$th_head = _('Produit');
				$no_object = _('Aucun produit rattaché à cette famille désactivée.');
				break;
			}
		}

		print '
					<h2>'._('Utilisation de la famille désactivée').' : "'.htmlspecialchars( $family['name'] ).'"</h2>
					<p>'.htmlspecialchars( $desc ).'</p>

					<table id="choose-new-cat" class="checklist">
						<caption>'.htmlspecialchars( $title ).'</caption>
						<col width="300" /><col width="300" /><col width="100" />
						<thead>
							<tr>
								<th id="desactived-name">'._('Famille').'</th>
								<th id="desactived-choose">'._('Redirigée vers').'</th>
								<th id="desactived-action"></th>
							</tr>
						</thead>
						<tfoot>
							<tr>
								<td colspan="3">
									<input name="save-new-id" value="'._('Enregistrer').'" type="submit" />
								</td>
							</tr>
						</tfoot>
						<tbody>
		';

		$objects = ctr_categories_get_objects( $_GET['ctr'], $family['id'], $_GET['class'] );
		if( !is_array($objects) || !sizeof($objects) ){
			print '
							<tr>
								<td colspan="3">'.htmlspecialchars( $no_object ).'</td>
							</tr>
			';
		}else{
			foreach( $objects as $one_obj ){
				$new_cat = 0;
				$txt_line = 'Aucune redirection';

				if( isset($_GET['new-cat'][ $one_obj['id'] ]) && is_numeric($_GET['new-cat'][ $one_obj['id'] ]) && $_GET['new-cat'][ $one_obj['id'] ] ){
					$txt_line = ctr_categories_get_name( $_GET['ctr'], $_GET['new-cat'][ $one_obj['id'] ] );

					if( trim($txt_line) != '' ){
						$new_cat = $_GET['new-cat'][ $one_obj['id'] ];
					}
				}

				print '
							<tr>
								<td headers="desactived-name">'.htmlspecialchars( $one_obj['name'] ).'</td>
								<td headers="desactived-choose">
									<input name="new-cat['.$one_obj['id'].']" id="new-cat-'.$one_obj['id'].'" value="'.$new_cat.'" type="hidden" />
									<span class="name-cat">'.htmlspecialchars( $txt_line ).'</span>
								</td>
								<td headers="desactived-action">
									<input onclick="return chooseNewFamilyDetails('.$_GET['ctr'].', '.$_GET['oldcat'].', '.$one_obj['id'].', '.$_GET['class'].')" name="choose-new-cat" value="'._('Choisir une famille').'" title="'._('Sélection une nouvelle famille').'" type="button" />
								</td>
							</tr>
				';
			}
		}

		print '

						</tbody>
					</table>
		';
	}

	print '
				</div>
			</form>
		</div>
	';

	if( isset($new_sub_text) ){
		print '
			<script><!--
				parent.saveNewFamily('.$_GET['oldcat'].', \''.$new_sub_text.'\');
			--></script>
		';
	}

	require_once('admin/skin/footer.inc.php');

