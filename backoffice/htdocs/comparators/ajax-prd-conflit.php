<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403(['_RGH_ADMIN_COMPARATOR', '_RGH_ADMIN_MARKET_PLACE'], false);

	require_once('comparators.inc.php');
	require_once('products.inc.php');
	require_once('categories.inc.php');

	if (!isset($_POST['prd'])){
		print _('Il manque des paramètres');
		exit;
	}

	$prd = ria_mysql_fetch_array(prd_products_get($_POST['prd']));
	$txt = '<span id="title-conflit">'._('Gestion des conflits de rattachement').' :</span><br /><br />';
	$txt .= '<div id="cft-content">'.sprintf(_('Le produit <span class="underline">%s</span> possède plusieurs catégories qui sont rattachées à une famille du comparateur.'), $prd['name']).'<br />'._('Pour une meilleure exportation dans le catalogue, nous vous conseillons de résoudre le conflit.');
	$txt .= '<br /><br />'._('Voici les catégories').' :<br /><ul>';

	// Liste des familles de comparateur de prix
	$r_cat = prd_products_categories_get($_POST['prd'], true);
	while( $c = ria_mysql_fetch_array($r_cat) ){
		if( ctr_prd_categories_is_publish($_POST['ctr'],$c['cat']) ){
			$ctr_cat = ria_mysql_fetch_array( ctr_prd_categories_get($_POST['ctr'],$c['cat']) );
			$txt .= '<li>
				<input id="cft-'.$ctr_cat['id'].'" type="radio" name="cat" value="'.$ctr_cat['id'].'" />
				<label for="cft-'.$ctr_cat['id'].'">'.$ctr_cat['name'].' ('.$c['name'].')</label>
			</li>';
		}
	}

	// Famille par défault
	$c = ria_mysql_fetch_array(prd_products_categories_get($_POST['prd'], true));
	$ctr_cat = ria_mysql_fetch_array( ctr_prd_categories_get($_POST['ctr'],$c['cat']) );
	$txt .= 'Par défault : '.$ctr_cat['name'].' ('.$c['name'].')</div>';

	print $txt;
