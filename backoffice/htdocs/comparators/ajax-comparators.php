<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403(['_RGH_ADMIN_COMPARATOR', '_RGH_ADMIN_MARKET_PLACE'], false);

	require_once('view.ctr.inc.php');
	require_once('comparators.inc.php');

	if( isset($_POST['saveallfilter'], $_POST['delete'], $_POST['ctr']) ){
		ctr_categories_unpublish( $_POST['ctr'] );
	}elseif( isset($_POST['ctr'], $_POST['saveallfilter'], $_POST['cat-visit'], $_POST['cat']) ){
		strlen($_POST['cat-visit'])>0 ? $cat_visit = explode(',',$_POST['cat-visit']) : $cat_visit = '';
		if( !ctr_categories_publish($_POST['ctr'], $_POST['cat']) ){
			print 'ko';
		} else {
			print 'ok';
		}
		exit;
	}elseif( isset($_POST['ctr'], $_POST['saveallfilter']) ){
		if( !ctr_categories_unpublish($_POST['ctr']) ){
			print 'ko';
		} else {
			print 'ok';
		}
		exit;
	}

	header("Content-Type: application/xml");
	$xml = '<?xml version="1.0" encoding="utf-8"?>';
	$error = false;
	$ctr = ria_mysql_result(ctr_comparators_get($_POST['ctr'], true, false, null),0,'name');

	if( isset($_POST['ctr'], $_POST['active']) ){
		$res = ctr_comparators_activation( $_POST['ctr'] );
		if( !$res ){
			$xml .= "<result type=\"0\">";
			$xml .= "<error>".sprintf($_POST['active'] ? _("Une erreur inattendue est survenue lors de l'activation du comparateur de prix %s.") : _("Une erreur inattendue est survenue lors de la désactivation du comparateur de prix %s"), $ctr)."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur")."</error>";
			$xml .= "</result>";
		} else {
			if( $res === '-1' ){
				if( in_array($_POST['ctr'], ctr_cdiscount_partners_get_ria_id()) ){
					$xml .= "<result type=\"0\">";
					$xml .= "<error>"._("Ce partenaire CDiscount n'a pas encore été activé dans votre espace vendeur CDiscount")." > "._("Vos données personnelles et bancaires")." > "._("Votre abonnement sur les sites de diffusion.")."</error>";
					$xml .= "</result>";
				}
			}else{
				$xml .= "
					<result type=\"1\">
						<code_html><![CDATA[".view_ctr_header( $_POST['ctr'] )."]]></code_html>
					</result>
				";
			}
		}
	}

	print $xml;
    exit;