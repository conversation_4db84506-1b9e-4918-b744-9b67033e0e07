<?php
// ======================================================================
// CONFIGURATION REQUISE
// ======================================================================
define('DEBUG_USER', 'votre_login_ici');      // !! CHANGEZ CECI !!
define('DEBUG_PASS', 'votre_mot_de_passe_ici'); // !! CHANGEZ CECI !!
define('TOOL_TITLE', 'Outil Maintenance V6'); // Titre plus neutre
define('DB_CHARSET_DUMP', 'utf8'); // Pour PHP 5.3, utf8 est plus sûr. Si MySQL >= 5.5.3, utf8mb4 peut être utilisé.
define('DEFAULT_EMAIL_FROM', 'debug@' . (isset($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'localhost'));

// ======================================================================
// INITIALISATION & SÉCURITÉ (Phase 1: Avant tout output)
// ======================================================================

error_reporting(E_ALL);
ini_set('display_errors', 1);
@set_time_limit(0);
@ini_set('max_execution_time', 0);
@ini_set('memory_limit', '512M');

if (session_id() === '') {
    $cookieParams = session_get_cookie_params();
    // PHP 5.3: session_set_cookie_params n'accepte pas 'samesite'.
    session_set_cookie_params(
        $cookieParams['lifetime'],
        $cookieParams['path'],
        $cookieParams['domain'],
        isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on', // secure
        true // httponly
    );
    @session_start();
}

if (!function_exists('hash_equals')) {
    function hash_equals($str1, $str2) { if (strlen($str1) != strlen($str2)) { return false; } else { $res = $str1 ^ $str2; $ret = 0; for ($i = strlen($res) - 1; $i >= 0; $i--) { $ret |= ord($res[$i]); } return !$ret; } }
}

if (empty($_SESSION['csrf_token'])) {
    if (function_exists('openssl_random_pseudo_bytes')) {
        $_SESSION['csrf_token'] = bin2hex(openssl_random_pseudo_bytes(32));
    } else {
        // Fallback moins sécurisé si openssl n'est pas disponible
        $strong_fallback = '';
        for ($i = 0; $i < 32; $i++) { $strong_fallback .= chr(mt_rand(0, 255)); }
        $_SESSION['csrf_token'] = bin2hex($strong_fallback);
    }
}
$csrf_token = $_SESSION['csrf_token'];

$csrf_valid = true;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $submitted_token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';
    if (!isset($_SESSION['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $submitted_token)) { $csrf_valid = false; }
} elseif (isset($_GET['download']) || isset($_GET['view_file'])) {
    $submitted_token_get = isset($_GET['csrf_token']) ? $_GET['csrf_token'] : '';
    if (!isset($_SESSION['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $submitted_token_get)) { $csrf_valid = false; }
}

$is_login_attempt = ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'], $_POST['password']));

if (!$csrf_valid && !$is_login_attempt) {
    @session_destroy();
    die('<!DOCTYPE html><html lang="fr"><head><meta charset="UTF-8"><title>Erreur Sécurité</title><style>body{font-family: sans-serif; background:#f8d7da; color:#721c24; padding:20px; text-align:center;} h1{border-bottom:1px solid #f5c6cb; padding-bottom:10px;} a{color:#007bff;}</style></head><body><h1>Erreur de Sécurité</h1><p>Le jeton de sécurité (CSRF) est invalide ou a expiré. Cela peut se produire si la page est restée inactive trop longtemps.</p><p><a href="' . htmlspecialchars($_SERVER['PHP_SELF'], ENT_QUOTES, 'UTF-8') . '">Veuillez rafraîchir la page de connexion</a> et réessayer.</p></body></html>');
}

// ======================================================================
// AUTHENTIFICATION
// ======================================================================

$is_authenticated = (isset($_SESSION['auth']) && $_SESSION['auth'] === true);

if (!$is_authenticated) {
    if ($is_login_attempt) {
        $submitted_token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';
        if (!isset($_SESSION['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $submitted_token)) {
            die('<!DOCTYPE html><html><head><title>Erreur</title><style>body{font-family: sans-serif; background: #f8d7da; color:#721c24; padding: 20px;}</style></head><body><h1>Erreur de sécurité (Login CSRF).</h1><p>Veuillez rafraîchir.</p></body></html>');
        }
        if (defined('DEBUG_USER') && defined('DEBUG_PASS') && $_POST['login'] === DEBUG_USER && $_POST['password'] === DEBUG_PASS) {
            $_SESSION['auth'] = true;
            session_regenerate_id(true);
            if (function_exists('openssl_random_pseudo_bytes')) {
                $_SESSION['csrf_token'] = bin2hex(openssl_random_pseudo_bytes(32));
            } else {
                $strong_fallback = ''; for ($i = 0; $i < 32; $i++) { $strong_fallback .= chr(mt_rand(0, 255)); } $_SESSION['csrf_token'] = bin2hex($strong_fallback);
            }
            header('Location: ' . $_SERVER['PHP_SELF']);
            exit;
        } else {
            $_SESSION['login_error'] = 'Identifiants incorrects.';
        }
    }
} else {
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['logout'])) {
        $_SESSION = array();
        if (ini_get("session.use_cookies")) { $params = session_get_cookie_params(); setcookie(session_name(), '', time() - 42000, $params["path"], $params["domain"], $params["secure"], $params["httponly"]); }
        @session_destroy();
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit;
    }
}

// ======================================================================
// FONCTIONS UTILITAIRES
// ======================================================================

function h($s) { return htmlspecialchars((string)$s, ENT_QUOTES, 'UTF-8'); }
function csrf_input() { global $csrf_token; return '<input type="hidden" name="csrf_token" value="' . h($csrf_token) . '">'; }
function display_message($message, $type = 'info') {
    $icon_map = array('success' => '✅', 'error' => '❌', 'warning' => '⚠️', 'info' => 'ℹ️');
    $icon = isset($icon_map[$type]) ? $icon_map[$type] . ' ' : '';
    $class = 'message message-' . h(strtolower($type));
    return '<div class="' . $class . '">' . $icon . h($message) . '</div>';
}
function format_bytes($bytes, $precision = 2) { $units = array('B', 'KB', 'MB', 'GB', 'TB'); $bytes = max($bytes, 0); $pow = floor(($bytes ? log($bytes) : 0) / log(1024)); $pow = min($pow, count($units) - 1); if ($pow > 0) { $bytes /= pow(1024, $pow); } return round($bytes, $precision) . ' ' . $units[$pow]; }
function flush_buffers_dump() { while (ob_get_level() > 0) { @ob_end_flush(); } @flush(); }

// ======================================================================
// FONCTION DE DUMP BDD
// ======================================================================
function perform_database_dump($host, $user, $pass, $dbname, $include_data_for) {
    @set_time_limit(0); @ini_set('max_execution_time', 0); @ini_set('memory_limit', '512M');
    $conn = @mysqli_connect($host, $user, $pass, $dbname);
    if (mysqli_connect_errno()) { if (ob_get_level() > 0) { while(@ob_end_clean()); } die("<!DOCTYPE html><html><head><title>Erreur Dump</title></head><body><h1>Erreur Fatale</h1><p>Connexion BDD échouée: " . htmlspecialchars(mysqli_connect_error()) . "</p></body></html>"); }
    $charset_to_use = defined('DB_CHARSET_DUMP') ? DB_CHARSET_DUMP : 'utf8';
    if (!mysqli_set_charset($conn, $charset_to_use)) { $error_msg = "Erreur charset " . $charset_to_use . ": " . htmlspecialchars(mysqli_error($conn)); mysqli_close($conn); if (ob_get_level() > 0) { while(@ob_end_clean()); } die("<!DOCTYPE html><html><head><title>Erreur Dump</title></head><body><h1>Erreur Fatale</h1><p>" . $error_msg . "</p></body></html>"); }
    $filename = 'backup_' . preg_replace('/[^a-z0-9_]/i', '', $dbname) . '_' . date('Y-m-d_H-i-s') . '.sql';
    $use_gzip = false; if (extension_loaded('zlib')) { $filename .= '.gz'; $use_gzip = true; }
    if (ob_get_level() > 0) { while (@ob_end_clean()); }
    header('Content-Type: application/octet-stream'); header('Content-Disposition: attachment; filename="' . $filename . '"'); if ($use_gzip) { header('Content-Encoding: gzip'); } header('Content-Transfer-Encoding: binary'); header('Pragma: no-cache'); header('Expires: 0');
    ob_start( $use_gzip ? 'ob_gzhandler' : null );
    echo "-- Sauvegarde BDD '" . h($dbname) . "' - " . date('Y-m-d H:i:s') . "\n"; echo "-- Hôte: " . h($host) . " / Serveur: " . mysqli_get_server_info($conn) . " / PHP: " . phpversion() . "\n\n"; echo "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n"; echo "SET time_zone = \"+00:00\";\n"; echo "/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;\n"; echo "/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;\n"; echo "/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;\n"; echo "/*!40101 SET NAMES " . $charset_to_use . " */;\n"; echo "SET FOREIGN_KEY_CHECKS=0;\n\n"; flush_buffers_dump();
    $tables = array(); $result = mysqli_query($conn, "SHOW TABLES"); if (!$result) { echo "-- ERREUR FATALE: Impossible de lister les tables : " . h(mysqli_error($conn)) . "\n"; mysqli_close($conn); ob_end_flush(); exit; } while ($row = mysqli_fetch_row($result)) { $tables[] = $row[0]; } mysqli_free_result($result);
    foreach ($tables as $table) { echo "\n-- --------------------------------------------------------\n"; echo "-- Structure table `" . $table . "`\n--\n"; $result_create = mysqli_query($conn, "SHOW CREATE TABLE `" . mysqli_real_escape_string($conn, $table) . "`"); if (!$result_create) { echo "-- ERREUR structure `" . h($table) . "`: " . h(mysqli_error($conn)) . "\n\n"; continue; } $row_create = mysqli_fetch_row($result_create); mysqli_free_result($result_create); if (isset($row_create[1])) { $create_table_sql = preg_replace('/ AUTO_INCREMENT=[0-9]+\s?/', ' ', $row_create[1]); echo $create_table_sql . ";\n\n"; } else { echo "-- ERREUR définition CREATE TABLE `" . h($table) . "`.\n\n"; } flush_buffers_dump();
        if (in_array($table, $include_data_for)) { echo "-- Dump données `" . $table . "`\n--\n"; $result_fields = mysqli_query($conn, "SELECT * FROM `" . mysqli_real_escape_string($conn, $table) . "` LIMIT 0"); if(!$result_fields) { echo "-- ERREUR infos champs `" . h($table) . "`: " . h(mysqli_error($conn)) . "\n\n"; continue; } $fields_info = mysqli_fetch_fields($result_fields); $field_names = array(); foreach ($fields_info as $field) { $field_names[] = '`' . $field->name . '`'; } $fields_str = implode(', ', $field_names); $num_fields = count($field_names); mysqli_free_result($result_fields); $result_data = mysqli_query($conn, "SELECT * FROM `" . mysqli_real_escape_string($conn, $table) . "`", MYSQLI_USE_RESULT); if (!$result_data) { echo "-- ERREUR données `" . h($table) . "`: " . h(mysqli_error($conn)) . "\n\n"; continue; }
            $insert_limit = 100; $flush_interval = 5; $row_count_total = 0; $insert_count = 0; $current_insert_rows = 0; $is_first_insert_block = true;
            while ($row = mysqli_fetch_row($result_data)) { if ($current_insert_rows === 0) { if (!$is_first_insert_block) echo ";\n"; echo "INSERT INTO `" . h($table) . "` (" . $fields_str . ") VALUES \n"; $is_first_insert_block = false; } else { echo ",\n"; } echo "("; $values = array(); for ($i = 0; $i < $num_fields; $i++) { if ($row[$i] === null) { $values[] = "NULL"; } else { $values[] = "'" . mysqli_real_escape_string($conn, $row[$i]) . "'"; } } echo implode(", ", $values) . ")"; $row_count_total++; $current_insert_rows++; if ($current_insert_rows >= $insert_limit) { $current_insert_rows = 0; $insert_count++; if ($insert_count % $flush_interval === 0) { flush_buffers_dump(); } } } mysqli_free_result($result_data);
            if ($row_count_total > 0 && $current_insert_rows > 0) { echo ";\n"; } elseif ($row_count_total == 0) { echo "-- Table `" . h($table) . "` vide.\n"; }
        } else { echo "-- Données `" . h($table) . "` exclues.\n"; } echo "\n"; flush_buffers_dump(); }
    echo "\n-- --------------------------------------------------------\n"; echo "-- Dumping routines\n"; flush_buffers_dump();
    $routine_types = array('PROCEDURE', 'FUNCTION'); foreach ($routine_types as $routine_type) { $sql_routines = "SELECT ROUTINE_NAME FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = '" . mysqli_real_escape_string($conn, $dbname) . "' AND ROUTINE_TYPE = '" . $routine_type . "'"; $result_routines = mysqli_query($conn, $sql_routines); if ($result_routines && mysqli_num_rows($result_routines) > 0) { echo "\n-- " . ucfirst(strtolower($routine_type)) . "s --\n"; echo "DELIMITER //\n"; while ($row_routine = mysqli_fetch_assoc($result_routines)) { $routine_name = $row_routine['ROUTINE_NAME']; echo "\nDROP " . $routine_type . " IF EXISTS `" . h($routine_name) . "`//\n"; $sql_create_routine = "SHOW CREATE " . $routine_type . " `" . mysqli_real_escape_string($conn, $routine_name) . "`"; $result_create_routine = mysqli_query($conn, $sql_create_routine); if ($result_create_routine) { $row_create_routine = mysqli_fetch_assoc($result_create_routine); $create_sql = ''; if (isset($row_create_routine['Create ' . ucfirst(strtolower($routine_type))])) { $create_sql = $row_create_routine['Create ' . ucfirst(strtolower($routine_type))]; } elseif (isset($row_create_routine['Create Statement'])) { $create_sql = $row_create_routine['Create Statement']; } if (!empty($create_sql)) { echo $create_sql . "//\n"; } else { echo "-- ERREUR structure vide " . $routine_type . " `" . h($routine_name) . "`//\n"; } mysqli_free_result($result_create_routine); } else { echo "-- ERREUR SHOW CREATE " . $routine_type . " `" . h($routine_name) . "`: " . h(mysqli_error($conn)) . "//\n"; } flush_buffers_dump(); } echo "DELIMITER ;\n"; } elseif (!$result_routines) { echo "-- ERREUR récupération " . strtolower($routine_type) . "s : " . h(mysqli_error($conn)) . "\n"; } if($result_routines) mysqli_free_result($result_routines); }
    echo "\n-- Réactiver vérifications\n"; echo "SET FOREIGN_KEY_CHECKS=1;\n"; echo "/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;\n"; echo "/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;\n"; echo "/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;\n"; echo "\n-- Dump terminé " . date('Y-m-d H:i:s') . "\n";
    mysqli_close($conn); ob_end_flush(); exit;
}

// ======================================================================
// TRAITEMENT DES ACTIONS (Si authentifié)
// ======================================================================
$page_message = null;
$tool_output = null;

if ($is_authenticated) {
    $current_tool = isset($_REQUEST['tool']) ? $_REQUEST['tool'] : 'dashboard';
    $current_path_request = isset($_REQUEST['path']) ? $_REQUEST['path'] : getcwd();
    $current_path = realpath($current_path_request);

    if (!$current_path || !is_dir($current_path)) {
        $parent_dir = dirname($current_path_request);
        $current_path = realpath($parent_dir ? $parent_dir : getcwd());
        $_SESSION['message'] = array('text' => 'Chemin initial invalide. Affichage de: ' . h($current_path ? $current_path : $current_path_request), 'type' => 'warning');
    }

    $action = isset($_POST['action']) ? $_POST['action'] : (isset($_GET['action']) ? $_GET['action'] : null);

    // --- ACTION DE TÉLÉCHARGEMENT (NOUVEAU) ---
    if (isset($_GET['download']) && $csrf_valid) { // $csrf_valid a déjà été vérifié pour $_GET['download']
        $file_to_download_path = $_GET['download'];
        $real_file_path = realpath($file_to_download_path);

        if ($real_file_path && is_file($real_file_path) && is_readable($real_file_path)) {
            if (ob_get_level()) { @ob_end_clean(); } // Nettoyer les buffers de sortie

            header('Content-Description: File Transfer');
            if (function_exists('mime_content_type')) {
                $mime = mime_content_type($real_file_path);
                header('Content-Type: ' . ($mime ? $mime : 'application/octet-stream'));
            } else {
                header('Content-Type: application/octet-stream');
            }
            header('Content-Disposition: attachment; filename="'.basename($real_file_path).'"');
            header('Expires: 0');
            header('Cache-Control: must-revalidate');
            header('Pragma: public');
            header('Content-Length: ' . filesize($real_file_path));

            @flush(); // S'assurer que les headers sont envoyés

            $file_handle = @fopen($real_file_path, 'rb');
            if ($file_handle) {
                while (!feof($file_handle) && !connection_aborted()) { // Vérifier aussi si la connexion est coupée
                    echo fread($file_handle, 8192 * 2); // Lire par morceaux (ex: 16KB)
                    @flush();
                }
                fclose($file_handle);
            } else {
                // Fallback si fopen échoue (ne devrait pas si is_readable est true)
                @readfile($real_file_path);
            }
            exit;
        } else {
            $_SESSION['message'] = array('text' => 'Fichier non trouvé ou non lisible pour téléchargement : ' . h($file_to_download_path), 'type' => 'error');
            $redirect_path = isset($_GET['path']) ? $_GET['path'] : '.'; // Récupère le path pour la redirection
            header('Location: ' . $_SERVER['PHP_SELF'] . '?tool=files&path=' . urlencode($redirect_path) . '&csrf_token=' . $csrf_token);
            exit;
        }
    }


    // Action: Modifier Permissions (files)
    if ($current_tool === 'files' && $action === 'change_permissions') {
        $change_perm_target = isset($_POST['target']) ? $_POST['target'] : null;
        $new_perms = isset($_POST['perms']) ? $_POST['perms'] : null;
        if ($change_perm_target && $new_perms !== null && $current_path) {
            $target_abs = realpath($current_path . DIRECTORY_SEPARATOR . $change_perm_target);
            if ($target_abs && file_exists($target_abs)) {
                if (preg_match('/^[0]?[0-7]{3}$/', $new_perms)) {
                    $octal_perms = octdec(ltrim($new_perms,'0'));
                    if (@chmod($target_abs, $octal_perms)) {
                        $_SESSION['message'] = array('text' => 'Permissions de "' . h(basename($target_abs)) . '" modifiées en ' . h(sprintf('%04o',$octal_perms)) . '.', 'type' => 'success');
                    } else {
                        $error_details_arr = error_get_last();
                        $error_details = ($error_details_arr && isset($error_details_arr['message'])) ? $error_details_arr['message'] : 'Cause inconnue';
                        $_SESSION['message'] = array('text' => 'ERREUR CHMOD sur "' . h(basename($target_abs)) . '". Droits serveur? Erreur: ' . h($error_details), 'type' => 'error');
                    }
                } else { $_SESSION['message'] = array('text' => 'Format permission invalide (ex: 0755).', 'type' => 'error'); }
            } else { $_SESSION['message'] = array('text' => 'Cible invalide pour chmod: ' . h($change_perm_target), 'type' => 'error'); }
        } else { $_SESSION['message'] = array('text' => 'Données manquantes pour chmod.', 'type' => 'error'); }
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tool=files&path=' . urlencode($current_path ? $current_path : '.'));
        exit;
    }

    // Action: Envoyer Email de Test (email_test)
    elseif ($current_tool === 'email_test' && $action === 'send_email') {
        $to = filter_input(INPUT_POST, 'email_to', FILTER_VALIDATE_EMAIL);
        $from = filter_input(INPUT_POST, 'email_from', FILTER_VALIDATE_EMAIL);
        $subject = trim(isset($_POST['email_subject']) ? $_POST['email_subject'] : 'Test Email from Debug Tool');
        $message = trim(isset($_POST['email_message']) ? $_POST['email_message'] : "Test email sent at " . date('Y-m-d H:i:s'));
        $headers_extra = trim(isset($_POST['email_headers']) ? $_POST['email_headers'] : '');

        if (!$to) {
            $_SESSION['message'] = array('text' => 'Adresse email destinataire invalide.', 'type' => 'error');
        } elseif (!$from) {
            $_SESSION['message'] = array('text' => 'Adresse email expéditeur invalide.', 'type' => 'error');
        } else {
            $headers = "From: " . $from . "\r\n";
            $headers .= "Reply-To: " . $from . "\r\n";
            $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
            $headers .= "Content-Type: text/plain; charset=utf-8\r\n";
            if (!empty($headers_extra)) { $headers .= $headers_extra . "\r\n"; }

            $mail_sent = false;
            if (function_exists('mb_send_mail')) {
                mb_language('uni'); mb_internal_encoding('UTF-8');
                $mail_sent = @mb_send_mail($to, $subject, $message, $headers);
            } else {
                $mail_sent = @mail($to, $subject, $message, $headers);
            }

            if ($mail_sent) {
                $_SESSION['message'] = array('text' => 'Email de test envoyé avec succès à ' . h($to) . '.', 'type' => 'success');
            } else {
                $error_details_arr = error_get_last();
                $error_details = ($error_details_arr && isset($error_details_arr['message'])) ? $error_details_arr['message'] : 'Vérifiez la configuration mail() du serveur PHP.';
                $_SESSION['message'] = array('text' => 'ERREUR lors de l\'envoi de l\'email. ' . h($error_details), 'type' => 'error');
            }
        }
        header('Location: ' . $_SERVER['PHP_SELF'] . '?tool=email_test' . '&csrf_token=' . $csrf_token); // Ajout CSRF token pour redirection
        exit;
    }

    // Action: Lancer le Dump BDD (db_dump - Étape 2)
    elseif ($current_tool === 'db_dump' && $action === 'confirm_dump') {
        $db_host = trim(isset($_POST['db_host']) ? $_POST['db_host'] : '');
        $db_user = trim(isset($_POST['db_user']) ? $_POST['db_user'] : '');
        $db_pass_submitted = isset($_POST['db_pass']) ? $_POST['db_pass'] : '';
        $db_name = trim(isset($_POST['db_name']) ? $_POST['db_name'] : '');
        $include_data_for = isset($_POST['include_data']) && is_array($_POST['include_data']) ? $_POST['include_data'] : array();

        if (empty($db_host) || empty($db_user) || empty($db_name)) {
            $_SESSION['message'] = array('text' => "Erreur : Informations BDD manquantes.", 'type' => 'error');
            header('Location: ' . $_SERVER['PHP_SELF'] . '?tool=db_dump'. '&csrf_token=' . $csrf_token);
            exit;
        } else {
            perform_database_dump($db_host, $db_user, $db_pass_submitted, $db_name, $include_data_for);
        }
    }

    // Action: Tester Connexion BDD & Lister Tables (db_dump - Étape 1)
    elseif ($current_tool === 'db_dump' && $action === 'select_tables') {
        $db_host = trim(isset($_POST['db_host']) ? $_POST['db_host'] : 'localhost');
        $db_user = trim(isset($_POST['db_user']) ? $_POST['db_user'] : '');
        $db_pass_submitted = isset($_POST['db_pass']) ? $_POST['db_pass'] : '';
        $db_name = trim(isset($_POST['db_name']) ? $_POST['db_name'] : '');

        $tool_output = array(
            'dump_step' => 'db_form', 'db_host' => $db_host, 'db_user' => $db_user,
            'db_name' => $db_name, 'db_pass_submitted' => $db_pass_submitted,
            'tables_info' => array(), 'form_data_hidden' => '', 'error_message' => '', 'info_message' => ''
        );

        if (empty($db_host) || empty($db_user) || empty($db_name)) {
            $tool_output['error_message'] = 'Veuillez remplir tous les champs obligatoires.';
        } else {
            $conn_check = @mysqli_connect($db_host, $db_user, $db_pass_submitted);
            if (!$conn_check) {
                $tool_output['error_message'] = "Échec connexion MySQL: " . h(mysqli_connect_error());
            } else {
                if (!@mysqli_select_db($conn_check, $db_name)) {
                    $tool_output['error_message'] = "Échec sélection BDD '" . h($db_name) . "': " . h(mysqli_error($conn_check));
                } else {
                    @mysqli_set_charset($conn_check, DB_CHARSET_DUMP);
                    $sql = "SELECT TABLE_NAME, TABLE_ROWS, data_length, index_length FROM information_schema.TABLES WHERE TABLE_SCHEMA = '" . mysqli_real_escape_string($conn_check, $db_name) . "' ORDER BY (data_length + index_length) DESC";
                    $result = @mysqli_query($conn_check, $sql);
                    if (!$result) {
                        $tool_output['error_message'] = "Erreur récupération tables: " . h(mysqli_error($conn_check));
                    } else {
                        while ($row = mysqli_fetch_assoc($result)) {
                            $data_length = isset($row['data_length']) ? $row['data_length'] : 0;
                            $index_length = isset($row['index_length']) ? $row['index_length'] : 0;
                            $row['table_size'] = $data_length + $index_length;
                            $tool_output['tables_info'][] = $row;
                        }
                        mysqli_free_result($result);
                        $tool_output['dump_step'] = 'table_select';
                        $tool_output['form_data_hidden'] = sprintf(
                            '<input type="hidden" name="db_host" value="%s"><input type="hidden" name="db_user" value="%s"><input type="hidden" name="db_pass" value="%s"><input type="hidden" name="db_name" value="%s">',
                            h($db_host), h($db_user), h($db_pass_submitted), h($db_name)
                        );
                        if (empty($tool_output['tables_info'])) { $tool_output['info_message'] = "Aucune table trouvée dans '" . h($db_name) . "'."; }
                    }
                }
                mysqli_close($conn_check);
            }
        }
    }

    if (isset($_SESSION['message'])) {
        $page_message = $_SESSION['message'];
        unset($_SESSION['message']);
    }
}

// ======================================================================
// FONCTION AFFICHAGE LOGIN
// ======================================================================

function render_login_form() {
    global $csrf_token;
    $login_error = isset($_SESSION['login_error']) ? $_SESSION['login_error'] : null;
    if ($login_error) { unset($_SESSION['login_error']); }
    ?>
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Connexion - <?php echo h(TOOL_TITLE); ?></title>
        <style>
            :root { --primary-color: #3498db; --danger-color: #e74c3c; --light-grey: #ecf0f1; --dark-grey: #7f8c8d; --white: #fff; --black: #333; }
            body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; margin: 0; background-color: var(--light-grey); display: flex; justify-content: center; align-items: center; min-height: 100vh; padding: 20px; }
            .login-container { background-color: var(--white); padding: 30px 40px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); width: 100%; max-width: 400px; text-align: center; }
            .login-container h1 { margin-top: 0; margin-bottom: 10px; color: var(--black); font-size: 1.8em; font-weight: 600; }
            .login-container p { color: var(--dark-grey); margin-bottom: 25px; }
            .form-group { margin-bottom: 20px; text-align: left; }
            label { display: block; margin-bottom: 8px; font-weight: 500; color: var(--black); font-size: 0.95em;}
            input[type="text"], input[type="password"] { width: 100%; padding: 12px 15px; border: 1px solid #bdc3c7; border-radius: 5px; font-size: 1em; box-sizing: border-box; transition: border-color 0.2s; }
            input[type="text"]:focus, input[type="password"]:focus { border-color: var(--primary-color); outline: none; box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2); }
            button[type="submit"] { width: 100%; padding: 12px 20px; background-color: var(--primary-color); color: var(--white); border: none; border-radius: 5px; cursor: pointer; font-size: 1.1em; font-weight: bold; transition: background-color 0.3s ease; text-transform: uppercase; letter-spacing: 0.5px; }
            button[type="submit"]:hover { background-color: #2980b9; }
            .message.message-error { background-color: #fdeded; border: 1px solid var(--danger-color); color: var(--danger-color); padding: 10px 15px; margin-bottom: 20px; border-radius: 5px; font-size: 0.9em; text-align: left; }
            .message.message-error::before { content: '❌ '; margin-right: 5px;}
        </style>
    </head>
    <body>
    <div class="login-container">
        <h1><?php echo h(TOOL_TITLE); ?></h1>
        <p>Veuillez vous connecter pour accéder aux outils.</p>
        <?php if ($login_error): ?>
            <div class="message message-error"><?php echo h($login_error); ?></div>
        <?php endif; ?>
        <form action="<?php echo h($_SERVER['PHP_SELF']); ?>" method="post" novalidate>
            <?php echo csrf_input(); ?>
            <div class="form-group">
                <label for="login">Identifiant</label>
                <input type="text" id="login" name="login" required autofocus>
            </div>
            <div class="form-group">
                <label for="password">Mot de passe</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit">Connexion</button>
        </form>
    </div>
    </body>
    </html>
    <?php
}

if (!$is_authenticated) {
    render_login_form();
    exit;
}

// ======================================================================
// AFFICHAGE PRINCIPAL
// ======================================================================
function display_dashboard() {
    echo "<p>Tableau de bord principal. Sélectionnez un outil dans la barre latérale.</p>";
    echo display_message("<strong>ATTENTION :</strong> Cet outil donne accès à des informations et actions sensibles. Utilisez-le avec précaution et supprimez-le du serveur immédiatement après utilisation.", 'warning');
    echo "<h2>Informations Serveur</h2>";
    echo "<div class='info-grid'>";
    $server_vars = array(
        'PHP Version' => phpversion(),
        'System' => php_uname(),
        'Server Software' => isset($_SERVER['SERVER_SOFTWARE']) ? $_SERVER['SERVER_SOFTWARE'] : 'N/A',
        'Server Name' => isset($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'N/A',
        'Server IP' => isset($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : 'N/A',
        'Document Root' => isset($_SERVER['DOCUMENT_ROOT']) ? $_SERVER['DOCUMENT_ROOT'] : 'N/A',
        'Script Filename' => isset($_SERVER['SCRIPT_FILENAME']) ? $_SERVER['SCRIPT_FILENAME'] : 'N/A',
        'Current Directory' => getcwd(),
        'PHP User' => (function_exists('get_current_user') ? get_current_user() : 'N/A') . (function_exists('posix_getuid') ? ' (UID: '.posix_getuid().')' : ''),
        'Memory Limit' => ini_get('memory_limit'),
        'Max Execution Time' => ini_get('max_execution_time'),
        'Upload Max Filesize' => ini_get('upload_max_filesize'),
        'Post Max Size' => ini_get('post_max_size'),
        'Display Errors' => ini_get('display_errors'),
        'Error Reporting' => error_reporting(),
        'Session Save Path' => session_save_path() ? session_save_path() : 'N/A',
        'Open Basedir' => ini_get('open_basedir') ? ini_get('open_basedir') : 'N/A',
    );
    foreach ($server_vars as $key => $value) {
        echo "<div><strong>" . h($key) . "</strong></div><div>" . h($value) . "</div>";
    }
    echo "</div>";
}

function display_files($base_path, $csrf_token) {
    $view_file = isset($_GET['view_file']) ? $_GET['view_file'] : null;
    $posix_available = function_exists('posix_getpwuid') && function_exists('posix_getgrgid');

    echo '<nav aria-label="breadcrumb" class="breadcrumbs">';
    $real_base_path_bc = $base_path ? $base_path : '.';
    $path_parts = explode(DIRECTORY_SEPARATOR, trim($real_base_path_bc, DIRECTORY_SEPARATOR));

    $root_path_link = ($real_base_path_bc !== DIRECTORY_SEPARATOR) ? h($_SERVER['PHP_SELF']) . '?tool=files&path=' . urlencode(DIRECTORY_SEPARATOR) . '&csrf_token=' . h($csrf_token) : null;
    echo '<a href="' . ($root_path_link ? $root_path_link : '#') . '" class="' . ($root_path_link ? '' : 'disabled') . '">Racine</a>';

    $current_built_path = DIRECTORY_SEPARATOR;
    foreach ($path_parts as $part) {
        if (empty($part) && $current_built_path !== DIRECTORY_SEPARATOR) continue; // Sauf si c'est la racine elle-même
        if ($current_built_path === DIRECTORY_SEPARATOR && empty($part)) {
            // Cas où $real_base_path_bc est la racine, $path_parts peut être array('')
        } else {
            $current_built_path .= $part . DIRECTORY_SEPARATOR;
        }

        $real_segment_path = realpath($current_built_path);
        if (!$real_segment_path) continue;

        echo '<span class="separator">/</span>';
        $is_last = (rtrim($real_segment_path, DIRECTORY_SEPARATOR) === rtrim($real_base_path_bc, DIRECTORY_SEPARATOR)); // Comparaison plus robuste
        $link = h($_SERVER['PHP_SELF']) . '?tool=files&path=' . urlencode($real_segment_path) . '&csrf_token=' . h($csrf_token);
        if ($is_last) {
            echo '<span class="active" aria-current="page">' . h(empty($part) && $real_base_path_bc === DIRECTORY_SEPARATOR ? 'Racine' : $part) . '</span>';
        } else {
            echo '<a href="' . $link . '">' . h($part) . '</a>';
        }
    }
    echo '<span class="current-path-display">Chemin actuel : <code>' . h($real_base_path_bc) . '</code></span>';
    echo '</nav>';

    if (is_dir($base_path) && is_readable($base_path)) {
        echo '<div class="file-explorer">';
        echo '<div class="fe-header"><span>Type</span><span>Nom</span><span>Taille</span><span>Proprio</span><span>Perms</span><span>Modifié</span><span>Actions</span></div>';
        echo '<ul>';

        $parent_path = dirname($real_base_path_bc);
        if ($parent_path !== $real_base_path_bc && $parent_path !== '.' && $parent_path !== DIRECTORY_SEPARATOR && is_readable($parent_path)) {
            echo '<li class="folder-up"><span class="item-icon">⤴️</span><div class="item-name"><a href="' . h($_SERVER['PHP_SELF']) . '?tool=files&path=' . urlencode($parent_path) . '&csrf_token=' . h($csrf_token) . '" class="folder-link">[ Dossier Parent ]</a></div><span class="item-size">-</span><span class="item-owner">-</span><span class="item-perms">-</span><span class="item-date">-</span><div class="item-actions"></div></li>';
        } elseif ($real_base_path_bc !== DIRECTORY_SEPARATOR && $parent_path === DIRECTORY_SEPARATOR && is_readable($parent_path)) { // Cas où parent est la racine
            echo '<li class="folder-up"><span class="item-icon">⤴️</span><div class="item-name"><a href="' . h($_SERVER['PHP_SELF']) . '?tool=files&path=' . urlencode($parent_path) . '&csrf_token=' . h($csrf_token) . '" class="folder-link">[ Dossier Parent ]</a></div><span class="item-size">-</span><span class="item-owner">-</span><span class="item-perms">-</span><span class="item-date">-</span><div class="item-actions"></div></li>';
        }


        $items = @scandir($base_path);
        if ($items === false) {
            echo '<li>'.display_message('Impossible de lister le contenu.', 'error').'</li>';
        } else {
            $folders = array(); $files = array();
            foreach ($items as $item) { if ($item === '.' || $item === '..') continue; $item_path = $base_path . DIRECTORY_SEPARATOR . $item; if (@is_dir($item_path)) { $folders[] = $item; } elseif(@is_file($item_path)) { $files[] = $item; } }
            natcasesort($folders); natcasesort($files);

            foreach ($folders as $folder) {
                $folder_path = $base_path . DIRECTORY_SEPARATOR . $folder;
                $perms = @fileperms($folder_path); $perms_str = ($perms !== false) ? substr(sprintf('%o', $perms), -4) : '?';
                $mtime = @filemtime($folder_path); $date_str = ($mtime !== false) ? date('Y-m-d H:i', $mtime) : '?';
                $owner_str = '?'; $uid = @fileowner($folder_path); $gid = @filegroup($folder_path);
                if ($uid !== false && $gid !== false) {
                    if ($posix_available) { $owner_info = @posix_getpwuid($uid); $owner_name = ($owner_info && isset($owner_info['name'])) ? $owner_info['name'] : $uid; $group_info = @posix_getgrgid($gid); $group_name = ($group_info && isset($group_info['name'])) ? $group_info['name'] : $gid; $owner_str = h($owner_name) . ':' . h($group_name); } else { $owner_str = $uid . ':' . $gid; }
                }
                echo '<li><span class="item-icon">📁</span><div class="item-name"><a href="' . h($_SERVER['PHP_SELF']) . '?tool=files&path=' . urlencode($folder_path) . '&csrf_token=' . h($csrf_token) . '" class="folder-link">'.h($folder).'</a></div><span class="item-size">-</span><span class="item-owner" title="' . h($owner_str) . '">' . $owner_str . '</span><span class="item-perms">'.h($perms_str).'</span><span class="item-date">' . $date_str . '</span><div class="item-actions"><form method="POST" action="'.h($_SERVER['PHP_SELF']).'" class="chmod-form">'.csrf_input().'<input type="hidden" name="tool" value="files"><input type="hidden" name="path" value="'.h($base_path).'"><input type="hidden" name="target" value="'.h($folder).'"><input type="hidden" name="action" value="change_permissions"><input type="text" name="perms" size="4" value="'.h($perms_str).'" pattern="[0]?[0-7]{3}" title="Nouvelles Permissions (ex: 0755)"><button type="submit" class="btn btn-small" title="Modifier Permissions">💾</button></form></div></li>';
            }
            foreach ($files as $file) {
                $file_path = $base_path . DIRECTORY_SEPARATOR . $file;
                $real_file_path = realpath($file_path);
                $perms = @fileperms($file_path); $perms_str = ($perms !== false) ? substr(sprintf('%o', $perms), -4) : '?';
                $size = @filesize($file_path); $size_str = ($size !== false) ? format_bytes($size) : '?';
                $mtime = @filemtime($file_path); $date_str = ($mtime !== false) ? date('Y-m-d H:i', $mtime) : '?';
                $owner_str = '?'; $uid = @fileowner($file_path); $gid = @filegroup($file_path);
                if ($uid !== false && $gid !== false) {
                    if ($posix_available) { $owner_info = @posix_getpwuid($uid); $owner_name = ($owner_info && isset($owner_info['name'])) ? $owner_info['name'] : $uid; $group_info = @posix_getgrgid($gid); $group_name = ($group_info && isset($group_info['name'])) ? $group_info['name'] : $gid; $owner_str = h($owner_name) . ':' . h($group_name); } else { $owner_str = $uid . ':' . $gid; }
                }
                $is_readable_file = $real_file_path && is_file($real_file_path) && is_readable($real_file_path); // Assurer is_file
                $file_icon = $is_readable_file ? '📄' : '🔒';
                echo '<li><span class="item-icon">'.$file_icon.'</span><div class="item-name">';
                if ($is_readable_file) { echo '<a href="' . h($_SERVER['PHP_SELF']) . '?tool=files&path=' . urlencode($base_path) . '&view_file=' . urlencode($file) . '&csrf_token='.h($csrf_token).'" title="Voir Contenu">' . h($file) . '</a>'; } else { echo h($file) . ' <small>(Non lisible/accessible)</small>'; }
                echo '</div><span class="item-size">' . $size_str . '</span><span class="item-owner" title="' . h($owner_str) . '">' . $owner_str . '</span><span class="item-perms">'.h($perms_str).'</span><span class="item-date">' . $date_str . '</span><div class="item-actions">';
                if ($is_readable_file) { echo '<a href="' . h($_SERVER['PHP_SELF']) . '?tool=files&path=' . urlencode($base_path) . '&view_file=' . urlencode($file) . '&csrf_token='.h($csrf_token).'" title="Voir Contenu"><button type="button" class="btn btn-small">👁️</button></a> '; } else { echo '<button type="button" class="btn btn-small" disabled>👁️</button> '; }
                if ($is_readable_file && $real_file_path) { echo '<a href="' . h($_SERVER['PHP_SELF']) . '?download='.urlencode($real_file_path).'&csrf_token='.h($csrf_token).'&path='.urlencode($base_path).'" title="Télécharger"><button type="button" class="btn btn-small">⬇️</button></a> '; } else { echo '<button type="button" class="btn btn-small" disabled>⬇️</button> '; }
                echo '<form method="POST" action="'.h($_SERVER['PHP_SELF']).'" class="chmod-form">'.csrf_input().'<input type="hidden" name="tool" value="files"><input type="hidden" name="path" value="'.h($base_path).'"><input type="hidden" name="target" value="'.h($file).'"><input type="hidden" name="action" value="change_permissions"><input type="text" name="perms" size="4" value="'.h($perms_str).'" pattern="[0]?[0-7]{3}" title="Nouvelles Permissions (ex: 0644)"><button type="submit" class="btn btn-small" title="Modifier Permissions">💾</button></form></div></li>';
            }
        }
        echo '</ul></div>';
    } else {
        echo display_message('Accès impossible au répertoire : ' . h($base_path), 'error');
        $parent_path_err = dirname($real_base_path_bc);
        if ($parent_path_err !== $real_base_path_bc && is_readable($parent_path_err)) {
            echo '<p><a href="' . h($_SERVER['PHP_SELF']) . '?tool=files&path=' . urlencode($parent_path_err) . '&csrf_token=' . h($csrf_token) . '" class="btn"><span class="icon">⤴️</span> Remonter au dossier parent</a></p>';
        }
    }

    if ($view_file) {
        $file_to_view_abs = rtrim($base_path, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . $view_file; // Assurer un seul /
        $real_file_to_view = realpath($file_to_view_abs);
        if ($real_file_to_view && is_file($real_file_to_view) && is_readable($real_file_to_view)) {
            echo '<hr><h3>Visualisation : ' . h($view_file) . '</h3>';
            echo '<p class="filepath-display"><code>' . h($real_file_to_view) . '</code></p>';
            $file_ext = strtolower(pathinfo($real_file_to_view, PATHINFO_EXTENSION)); $image_extensions = array('jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'ico'); $is_image = in_array($file_ext, $image_extensions); $file_size = filesize($real_file_to_view); $max_view_size = 2 * 1024 * 1024;
            $is_likely_text = !$is_image && ($file_size < $max_view_size);
            if ($is_likely_text && $file_size > 0) {
                $content_sample = file_get_contents($real_file_to_view, false, null, 0, 1024);
                if (function_exists('mb_detect_encoding') && mb_detect_encoding($content_sample, 'UTF-8', true) === false && strpos($content_sample, "\0") !== false) {
                    $is_likely_text = false;
                } elseif (strpos($content_sample, "\0") !== false && !function_exists('mb_detect_encoding')) { // Si mb_detect_encoding absent, vérifier les nuls bytes
                    $is_likely_text = false;
                }
            } elseif ($file_size == 0) { $is_likely_text = true; } elseif ($file_size >= $max_view_size) { $is_likely_text = false; }

            if ($is_image) {
                echo '<div class="image-preview">';
                $img_data = @file_get_contents($real_file_to_view);
                if ($img_data !== false) {
                    $mime_type = function_exists('mime_content_type') ? (mime_content_type($real_file_to_view) ? mime_content_type($real_file_to_view) : 'image/jpeg') : 'image/jpeg';
                    echo '<img src="data:' . h($mime_type) . ';base64,' . base64_encode($img_data) . '" alt="'.h($view_file).'">';
                } else { echo display_message('Impossible de lire l\'image.', 'error'); }
                echo '</div>';
                echo '<div class="view-actions"><a href="' . h($_SERVER['PHP_SELF']) . '?download='.urlencode($real_file_to_view).'&csrf_token='.h($csrf_token).'&path='.urlencode($base_path).'" class="btn btn-secondary"><span class="icon">⬇️</span> Télécharger</a> <a href="'.h($_SERVER['PHP_SELF']).'?tool=files&path='.urlencode($base_path).'&csrf_token='.h($csrf_token).'" class="btn btn-secondary"><span class="icon">🔙</span> Retour Liste</a></div>';
            } elseif ($is_likely_text) {
                $content = file_get_contents($real_file_to_view);
                echo '<label for="content_view">Contenu (Lecture Seule):</label>';
                echo '<textarea name="content_view" id="content_view" rows="20" readonly class="code-view">' . h($content) . '</textarea>';
                echo '<div class="view-actions"><a href="' . h($_SERVER['PHP_SELF']) . '?download='.urlencode($real_file_to_view).'&csrf_token='.h($csrf_token).'&path='.urlencode($base_path).'" class="btn btn-secondary"><span class="icon">⬇️</span> Télécharger</a> <a href="'.h($_SERVER['PHP_SELF']).'?tool=files&path='.urlencode($base_path).'&csrf_token='.h($csrf_token).'" class="btn btn-secondary"><span class="icon">🔙</span> Retour Liste</a></div>';
            } else {
                echo display_message('Fichier non visualisable (type ' . h($file_ext) . ' ou taille > '.format_bytes($max_view_size).').', 'info');
                echo '<div class="view-actions"><a href="' . h($_SERVER['PHP_SELF']) . '?download='.urlencode($real_file_to_view).'&csrf_token='.h($csrf_token).'&path='.urlencode($base_path).'" class="btn btn-secondary"><span class="icon">⬇️</span> Télécharger</a> <a href="'.h($_SERVER['PHP_SELF']).'?tool=files&path='.urlencode($base_path).'&csrf_token='.h($csrf_token).'" class="btn btn-secondary"><span class="icon">🔙</span> Retour Liste</a></div>';
            }
        } else { echo display_message('Fichier à visualiser non trouvé ou non lisible : ' . h($view_file), 'error'); }
    }
}

function display_db_dump($csrf_token, $output_data) {
    global $page_message;
    if ($page_message) { echo display_message($page_message['text'], $page_message['type']); }

    $dump_step = isset($output_data['dump_step']) ? $output_data['dump_step'] : 'db_form';
    $db_host = h(isset($output_data['db_host']) ? $output_data['db_host'] : 'localhost');
    $db_user = h(isset($output_data['db_user']) ? $output_data['db_user'] : '');
    $db_name = h(isset($output_data['db_name']) ? $output_data['db_name'] : '');
    $db_pass_submitted = h(isset($output_data['db_pass_submitted']) ? $output_data['db_pass_submitted'] : '');
    $tables_info = isset($output_data['tables_info']) ? $output_data['tables_info'] : array();
    $form_data_hidden = isset($output_data['form_data_hidden']) ? $output_data['form_data_hidden'] : '';
    $error_message = isset($output_data['error_message']) ? $output_data['error_message'] : '';
    $info_message = isset($output_data['info_message']) ? $output_data['info_message'] : '';

    echo display_message("<strong>ATTENTION :</strong> Le mot de passe BDD transite via le formulaire. Assurez la sécurité de cette page et supprimez le script après usage.", 'warning');
    if ($error_message) { echo display_message($error_message, 'error'); }
    if ($info_message) { echo display_message($info_message, 'info'); }

    if ($dump_step === 'db_form') {
        ?>
        <form action="<?php echo h($_SERVER['PHP_SELF']); ?>" method="post" class="form-card">
            <?php echo csrf_input(); ?>
            <input type="hidden" name="tool" value="db_dump">
            <input type="hidden" name="action" value="select_tables">
            <div class="form-group"><label for="db_host_dump">Hôte BDD <span class="required">*</span>:</label><input type="text" id="db_host_dump" name="db_host" value="<?php echo $db_host; ?>" required></div>
            <div class="form-group"><label for="db_user_dump">Utilisateur BDD <span class="required">*</span>:</label><input type="text" id="db_user_dump" name="db_user" value="<?php echo $db_user; ?>" required></div>
            <div class="form-group"><label for="db_pass_dump">Mot de passe BDD :</label><input type="password" id="db_pass_dump" name="db_pass"><small>Laissez vide si aucun.</small></div>
            <div class="form-group"><label for="db_name_dump">Nom BDD <span class="required">*</span>:</label><input type="text" id="db_name_dump" name="db_name" value="<?php echo $db_name; ?>" required></div>
            <button type="submit" class="btn btn-primary">Valider & Sélectionner Tables</button>
        </form>
        <?php
    } elseif ($dump_step === 'table_select') {
        ?>
        <form action="<?php echo h($_SERVER['PHP_SELF']); ?>" method="post" class="form-card">
            <?php echo csrf_input(); ?>
            <input type="hidden" name="tool" value="db_dump">
            <input type="hidden" name="action" value="confirm_dump">
            <?php echo $form_data_hidden; ?>
            <h3>Sélectionner les tables à inclure (données)</h3>
            <p>La structure de toutes les tables et les routines seront toujours incluses.</p>
            <?php if (!empty($tables_info)): ?>
                <div class="table-select-actions">
                    <button type="button" onclick="selectAllDumpCheckboxes(true)" class="btn btn-small btn-secondary">Tout Cocher</button>
                    <button type="button" onclick="selectAllDumpCheckboxes(false)" class="btn btn-small btn-secondary">Tout Décocher</button>
                </div>
                <ul class="table-list" id="tableListDump">
                    <?php foreach ($tables_info as $table):
                        $table_name = h($table['TABLE_NAME']);
                        $table_size = isset($table['table_size']) ? $table['table_size'] : 0;
                        $table_rows = h(isset($table['TABLE_ROWS']) ? $table['TABLE_ROWS'] : '?');
                        $checkbox_id = 'dump_table_' . preg_replace('/[^a-zA-Z0-9_-]/', '_', $table_name);
                        ?>
                        <li>
                            <input type="checkbox" name="include_data[]" value="<?php echo $table_name; ?>" id="<?php echo $checkbox_id; ?>" checked>
                            <label for="<?php echo $checkbox_id; ?>">
                                <?php echo $table_name; ?> <small>(<?php echo $table_rows; ?> lignes)</small>
                            </label>
                            <span class="size"><?php echo format_bytes($table_size); ?></span>
                        </li>
                    <?php endforeach; ?>
                </ul>
                <button type="submit" class="btn btn-primary btn-danger"><span class="icon">🚀</span> Lancer la Sauvegarde</button>
            <?php else: ?>
                <p>Aucune table trouvée dans la base de données.</p>
                <button type="submit" class="btn btn-primary"><span class="icon">🚀</span> Lancer Sauvegarde (Structure & Routines)</button>
            <?php endif; ?>
        </form>
        <script> function selectAllDumpCheckboxes(checkedState){ var checkboxes = document.getElementById('tableListDump').getElementsByTagName('input'); for(var i=0; i < checkboxes.length; i++) { if (checkboxes[i].type == 'checkbox') { checkboxes[i].checked = checkedState; } } } </script>
        <?php
    }
}

function display_email_test($csrf_token) {
    global $page_message;
    if ($page_message) { echo display_message($page_message['text'], $page_message['type']); }
    $default_from = h(DEFAULT_EMAIL_FROM);
    echo "<p>Cet outil utilise la fonction <code>mail()</code> de PHP pour envoyer un email. Assurez-vous que votre serveur est correctement configuré.</p>";
    ?>
    <form action="<?php echo h($_SERVER['PHP_SELF']); ?>" method="post" class="form-card">
        <?php echo csrf_input(); ?>
        <input type="hidden" name="tool" value="email_test">
        <input type="hidden" name="action" value="send_email">
        <div class="form-group">
            <label for="email_to">Destinataire <span class="required">*</span>:</label>
            <input type="email" id="email_to" name="email_to" required placeholder="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="email_from">Expéditeur <span class="required">*</span>:</label>
            <input type="email" id="email_from" name="email_from" required value="<?php echo $default_from; ?>">
        </div>
        <div class="form-group">
            <label for="email_subject">Sujet:</label>
            <input type="text" id="email_subject" name="email_subject" value="Email de Test - <?php echo h(TOOL_TITLE); ?>">
        </div>
        <div class="form-group">
            <label for="email_message">Message:</label>
            <textarea id="email_message" name="email_message" rows="6"><?php echo h("Ceci est un message de test envoyé depuis " . TOOL_TITLE . ".\n\nDate et Heure : " . date('Y-m-d H:i:s')); ?></textarea>
        </div>
        <div class="form-group">
            <label for="email_headers">Headers Supplémentaires (Optionnel):</label>
            <textarea id="email_headers" name="email_headers" rows="3" placeholder="Exemple : Cc: <EMAIL>\r\nReply-To: <EMAIL>"></textarea>
            <small>Un header par ligne (Nom: Valeur), séparé par CRLF (<code>\r\n</code>).</small>
        </div>
        <button type="submit" class="btn btn-primary"><span class="icon">✉️</span> Envoyer Email Test</button>
    </form>
    <?php
}

function display_phpinfo() {
    echo '<div class="phpinfo-container">';
    ob_start();
    phpinfo();
    $phpinfo_content = ob_get_clean();
    $phpinfo_content = preg_replace('%^.*<body[^>]*>(.*)</body>.*$%ms', '$1', $phpinfo_content);
    $phpinfo_content = preg_replace('/<table(.*?)>/', '<table class="phpinfo-table"$1>', $phpinfo_content);
    $phpinfo_content = preg_replace('/<td class="e">(.*?)<\/td>/', '<td class="phpinfo-key">$1</td>', $phpinfo_content);
    $phpinfo_content = preg_replace('/<td class="v">(.*?)<\/td>/', '<td class="phpinfo-value">$1</td>', $phpinfo_content);
    $phpinfo_content = preg_replace('/<h[12](.*?)>(.*?)<\/h[12]>/', '<h3 class="phpinfo-heading"$1>$2</h3>', $phpinfo_content);
    $phpinfo_content = preg_replace('/<hr>/', '<hr class="phpinfo-hr">', $phpinfo_content);
    $phpinfo_content = preg_replace('/<img (.*?)>/', '', $phpinfo_content);
    $phpinfo_content = preg_replace('/<a href="http:\/\/www.php.net\/">(.*?)<\/a>/', '$1', $phpinfo_content);
    $phpinfo_content = preg_replace('/<a href="http:\/\/www.zend.com\/">(.*?)<\/a>/', '$1', $phpinfo_content);
    echo $phpinfo_content;
    echo '</div>';
}

$tools = array(
    'dashboard' => array('icon' => '🏠', 'label' => 'Dashboard', 'handler' => 'display_dashboard'),
    'files'     => array('icon' => '📁', 'label' => 'Fichiers', 'handler' => 'display_files'),
    'db_dump'   => array('icon' => '💾', 'label' => 'Sauvegarde BDD', 'handler' => 'display_db_dump'),
    'email_test'=> array('icon' => '✉️', 'label' => 'Test Email', 'handler' => 'display_email_test'),
    'phpinfo'   => array('icon' => '⚙️', 'label' => 'PHP Info', 'handler' => 'display_phpinfo')
);

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo h(isset($tools[$current_tool]['label']) ? $tools[$current_tool]['label'] : 'Outil') . ' - ' . h(TOOL_TITLE); ?></title>
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #ecf0f1;
            --text-color: #34495e;
            --heading-color: #2c3e50;
            --border-color: #dde3e6;
            --sidebar-bg: #2c3e50;
            --sidebar-link-color: #bdc3c7;
            --sidebar-link-hover-bg: #34495e;
            --sidebar-link-active-bg: var(--primary-color);
            --sidebar-link-active-color: #ffffff;
            --content-bg: #ffffff;
            --success-bg: #d4edda; --success-border: #c3e6cb; --success-text: #155724;
            --error-bg: #f8d7da; --error-border: #f5c6cb; --error-text: #721c24;
            --warning-bg: #fff3cd; --warning-border: #ffeeba; --warning-text: #856404;
            --info-bg: #d1ecf1; --info-border: #bee5eb; --info-text: #0c5460;
            --code-bg: #f8f9fa;
            --code-text: #e83e8c;
            --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            --monospace-font: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
        }
        *, *::before, *::after { box-sizing: border-box; }
        html { font-size: 16px; }
        body {
            font-family: var(--font-family);
            margin: 0;
            background-color: var(--secondary-color);
            color: var(--text-color);
            display: flex;
            min-height: 100vh;
            font-size: 0.95rem;
            line-height: 1.6;
        }
        .sidebar {
            width: 240px;
            background-color: var(--sidebar-bg);
            color: var(--sidebar-link-color);
            padding: 20px 0;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            position: fixed;
            height: 100%;
            overflow-y: auto;
        }
        .sidebar-header { padding: 0 20px 20px 20px; border-bottom: 1px solid var(--sidebar-link-hover-bg); text-align: center; }
        .sidebar-header h2 { color: #fff; font-size: 1.3em; margin: 0; font-weight: 600; }
        .sidebar nav ul { list-style: none; padding: 0; margin: 15px 0; }
        .sidebar nav li form { margin: 0; }
        .sidebar nav button {
            display: flex; align-items: center;
            width: 100%; padding: 12px 20px;
            background: none; border: none;
            color: var(--sidebar-link-color); font-size: 1em; font-family: inherit;
            text-align: left; cursor: pointer;
            transition: background-color 0.2s, color 0.2s;
        }
        .sidebar nav button .icon { margin-right: 12px; font-size: 1.1em; width: 20px; display: inline-block; text-align: center; }
        .sidebar nav button:hover { background-color: var(--sidebar-link-hover-bg); color: #fff; }
        .sidebar nav li.active button { background-color: var(--sidebar-link-active-bg); color: var(--sidebar-link-active-color); font-weight: 500; }
        .sidebar-footer { margin-top: auto; padding: 20px; border-top: 1px solid var(--sidebar-link-hover-bg); }
        .sidebar-footer .logout-form button {
            background-color: var(--sidebar-link-hover-bg); color: var(--sidebar-link-color); width: 100%; text-align: center; border-radius: 4px;
            padding: 10px; font-weight: 500;
        }
        .sidebar-footer .logout-form button:hover { background-color: #e74c3c; color: #fff; }
        .main-content {
            flex-grow: 1;
            padding: 30px 40px;
            background-color: var(--content-bg);
            margin-left: 240px;
            overflow-y: auto;
        }
        h1, h2, h3 { color: var(--heading-color); margin-top: 1.8em; margin-bottom: 0.8em; font-weight: 600; border-bottom: 1px solid var(--border-color); padding-bottom: 0.5em; }
        h1:first-child, h2:first-child, h3:first-child { margin-top: 0; }
        h1 { font-size: 1.9em; } h2 { font-size: 1.6em; } h3 { font-size: 1.3em; }
        p { margin-bottom: 1em; }
        a { color: var(--primary-color); text-decoration: none; } a:hover { text-decoration: underline; }
        hr { border: none; border-top: 1px solid var(--border-color); margin: 30px 0; }
        code { font-family: var(--monospace-font); background-color: var(--code-bg); color: var(--code-text); padding: 0.2em 0.4em; border-radius: 3px; font-size: 0.9em; }
        pre { font-family: var(--monospace-font); background-color: var(--code-bg); border: 1px solid var(--border-color); padding: 15px; border-radius: 4px; overflow: auto; font-size: 0.9em; line-height: 1.5; }
        small { font-size: 0.85em; color: #7f8c8d; }
        .required { color: #e74c3c; font-weight: bold; margin-left: 2px; }
        .form-card { background: #fdfdfd; border: 1px solid var(--border-color); padding: 25px; border-radius: 5px; margin-bottom: 25px; box-shadow: 0 2px 5px rgba(0,0,0,0.05); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 8px; font-weight: 500; color: var(--heading-color); font-size: 0.95em; }
        input[type="text"], input[type="password"], input[type="email"], textarea, select {
            width: 100%; padding: 10px 12px; border: 1px solid #bdc3c7; border-radius: 4px; box-sizing: border-box; font-size: 1em; background-color: #fff; color: var(--text-color); transition: border-color 0.2s, box-shadow 0.2s; font-family: inherit;
        }
        input[type="text"]:focus, input[type="password"]:focus, input[type="email"]:focus, textarea:focus, select:focus { border-color: var(--primary-color); outline: none; box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2); }
        textarea { min-height: 120px; line-height: 1.5; }
        textarea.code-view { font-family: var(--monospace-font); background-color: var(--code-bg); }
        .btn { display: inline-flex; align-items: center; justify-content: center; padding: 9px 18px; border: 1px solid transparent; border-radius: 4px; cursor: pointer; font-size: 0.95em; font-weight: 500; line-height: 1.5; transition: all 0.2s ease; text-align: center; vertical-align: middle; white-space: nowrap; margin-right: 8px; margin-bottom: 5px; font-family: inherit; }
        .btn .icon { margin-right: 6px; font-size: 1.1em; line-height: 1; vertical-align: middle;}
        .btn-primary { background-color: var(--primary-color); color: #fff; border-color: var(--primary-color); } .btn-primary:hover { background-color: #2980b9; border-color: #2980b9; }
        .btn-secondary { background-color: var(--secondary-color); color: var(--text-color); border-color: #bdc3c7; } .btn-secondary:hover { background-color: #e2e6ea; border-color: #adb5bd; }
        .btn-danger { background-color: #e74c3c; color: #fff; border-color: #e74c3c; } .btn-danger:hover { background-color: #c0392b; border-color: #c0392b; }
        .btn-small { padding: 5px 10px; font-size: 0.85em; }
        .message { padding: 12px 18px; margin-bottom: 20px; border: 1px solid transparent; border-radius: 4px; font-weight: 400; display: flex; align-items: center; }
        .message-success { background-color: var(--success-bg); border-color: var(--success-border); color: var(--success-text); }
        .message-error { background-color: var(--error-bg); border-color: var(--error-border); color: var(--error-text); }
        .message-warning { background-color: var(--warning-bg); border-color: var(--warning-border); color: var(--warning-text); }
        .message-info { background-color: var(--info-bg); border-color: var(--info-border); color: var(--info-text); }
        .info-grid { display: grid; grid-template-columns: auto 1fr; gap: 10px 20px; background-color: #fdfdfd; border: 1px solid var(--border-color); padding: 20px; border-radius: 4px; margin-top: 15px; }
        .info-grid div { padding: 5px 0; word-break: break-word; }
        .info-grid div:nth-child(odd) { font-weight: 500; color: var(--heading-color); }
        .info-grid div:nth-child(even) { color: var(--text-color); }
        .breadcrumbs { background-color: var(--secondary-color); padding: 10px 15px; border-radius: 4px; margin-bottom: 20px; font-size: 0.9em; border: 1px solid var(--border-color); display: flex; align-items: center; flex-wrap: wrap; }
        .breadcrumbs a, .breadcrumbs span { margin-right: 5px; color: var(--text-color);}
        .breadcrumbs a:hover { color: var(--primary-color); }
        .breadcrumbs .separator { color: #7f8c8d; margin: 0 5px; } /* dark-grey from login */
        .breadcrumbs .active { font-weight: 500; color: var(--heading-color); }
        .breadcrumbs .disabled { color: #7f8c8d; pointer-events: none; }
        .breadcrumbs .current-path-display { margin-left: auto; font-size: 0.9em; color: #7f8c8d; padding-left: 15px; }
        .file-explorer { border: 1px solid var(--border-color); border-radius: 4px; overflow: hidden; margin-top: 15px; }
        .file-explorer ul { list-style: none; padding: 0; margin: 0; }
        .file-explorer .fe-header, .file-explorer li { display: grid; grid-template-columns: 30px 2fr 100px 120px 70px 130px 1fr; gap: 10px; align-items: center; padding: 10px 15px; border-bottom: 1px solid var(--border-color); font-size: 0.9em; }
        .file-explorer .fe-header { background-color: var(--secondary-color); font-weight: 500; color: var(--heading-color); padding: 12px 15px; }
        .file-explorer li:last-child { border-bottom: none; }
        .file-explorer li:hover { background-color: #f8f9fa; }
        .file-explorer .item-icon { font-size: 1.2em; text-align: center; }
        .file-explorer .item-name { word-break: break-all; font-weight: 500; }
        .file-explorer .item-name a { font-weight: 500; }
        .file-explorer .item-name a.folder-link { color: #2980b9; font-weight: bold; }
        .file-explorer .item-size, .file-explorer .item-perms, .file-explorer .item-date { text-align: right; color: #555; }
        .file-explorer .item-owner { text-align: left; color: #555; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        .file-explorer .item-perms { font-family: var(--monospace-font); }
        .file-explorer .item-actions { display: flex; gap: 6px; justify-content: flex-end; }
        .file-explorer .item-actions .chmod-form { display: inline-flex; gap: 4px; align-items: center; }
        .file-explorer .item-actions .chmod-form input[type="text"] { width: 60px; padding: 4px 6px; font-size: 0.85em; text-align: center; font-family: var(--monospace-font); margin:0;}
        .file-explorer .folder-up { background-color: #f1f5f7; font-weight: bold;}
        .filepath-display { font-size: 0.9em; color: #7f8c8d; margin-bottom: 15px; }
        .image-preview { margin: 20px 0; text-align: center; }
        .image-preview img { max-width: 100%; height: auto; border: 1px solid var(--border-color); background: #f8f9fa; padding: 5px; border-radius: 4px; }
        .view-actions { margin-top: 20px; }
        .table-list { list-style: none; padding: 0; max-height: 450px; overflow-y: auto; border: 1px solid var(--border-color); margin-bottom: 20px; background-color: #fff; border-radius: 4px; }
        .table-list li { padding: 10px 15px; border-bottom: 1px solid var(--secondary-color); display: flex; justify-content: space-between; align-items: center; transition: background-color 0.2s; }
        .table-list li:hover { background-color: #f8f9fa; }
        .table-list li:last-child { border-bottom: none; }
        .table-list label { margin-bottom: 0; font-weight: normal; flex-grow: 1; margin-left: 10px; cursor: pointer; }
        .table-list label small { color: #7f8c8d; margin-left: 8px; font-size: 0.9em; }
        .table-list .size { font-style: normal; color: #7f8c8d; margin-left: 20px; white-space: nowrap; font-size: 0.9em; text-align: right; width: 100px; }
        .table-list input[type="checkbox"] { margin-right: 10px; vertical-align: middle; height: 16px; width: 16px; cursor: pointer; }
        .table-select-actions { margin-bottom: 10px; text-align: right; }
        .phpinfo-container { border: 1px solid var(--border-color); border-radius: 4px; overflow: hidden; margin-top: 20px; background-color: #fff; padding: 0; }
        .phpinfo-container table.phpinfo-table { width: 100%; border-collapse: collapse; margin: 0; border: none; box-shadow: none; font-size: 0.95em; }
        .phpinfo-container .phpinfo-table td, .phpinfo-container .phpinfo-table th { border: 1px solid var(--border-color); padding: 8px 10px; vertical-align: baseline; font-size: inherit; }
        .phpinfo-container .phpinfo-key { background-color: var(--secondary-color) !important; color: var(--heading-color) !important; font-weight: 500; width: 30%; }
        .phpinfo-container .phpinfo-value { background-color: #fff !important; color: var(--text-color) !important; word-break: break-all; }
        .phpinfo-container .phpinfo-heading { background-color: var(--primary-color) !important; color: #fff !important; font-weight: bold; padding: 10px 15px; font-size: 1.2em; margin: 0; border: none; }
        .phpinfo-container .phpinfo-hr { border: none; border-top: 1px solid var(--primary-color); margin: 1em 0; }
        .phpinfo-container a:link, .phpinfo-container a:visited { color: var(--primary-color) !important; text-decoration: none !important; }
        .phpinfo-container a:hover { text-decoration: underline !important; }
        @media (max-width: 992px) {
            .sidebar { width: 200px; }
            .main-content { margin-left: 200px; padding: 20px; }
            h1 { font-size: 1.7em; } h2 { font-size: 1.4em; } h3 { font-size: 1.2em; }
            .file-explorer .fe-header, .file-explorer li { grid-template-columns: 30px 3fr 1fr 1fr; gap: 8px; font-size: 0.85em; }
            .file-explorer .item-owner, .file-explorer .item-perms, .file-explorer .item-date { display: none; }
            .file-explorer .item-actions { grid-column: 4; }
            .file-explorer .item-actions .chmod-form input[type="text"] { display: none; }
            .file-explorer .item-actions .chmod-form button { display: none; }
        }
        @media (max-width: 768px) {
            body { flex-direction: column; }
            .sidebar { width: 100%; height: auto; position: static; padding: 0; }
            .sidebar-header { padding: 15px; text-align: left; }
            .sidebar-header h2 { font-size: 1.2em; }
            .sidebar nav ul { display: flex; flex-wrap: wrap; margin: 0; border-bottom: 1px solid var(--sidebar-link-hover-bg); }
            .sidebar nav li { flex-grow: 1; border-right: 1px solid var(--sidebar-link-hover-bg); }
            .sidebar nav li:last-child { border-right: none; }
            .sidebar nav button { padding: 10px; font-size: 0.9em; justify-content: center; }
            .sidebar nav button .icon { margin-right: 5px; }
            .sidebar-footer { display: none; }
            .main-content { margin-left: 0; padding: 15px; }
            h1 { font-size: 1.5em; } h2 { font-size: 1.3em; } h3 { font-size: 1.1em; }
            .breadcrumbs { font-size: 0.8em; }
            .breadcrumbs .current-path-display { display: none; }
            .file-explorer .fe-header, .file-explorer li { grid-template-columns: 25px auto 70px; font-size: 0.8em; }
            .file-explorer .item-size { grid-column: 3; }
            .file-explorer .item-actions { grid-column: 3; }
            .file-explorer .item-actions a, .file-explorer .item-actions form { margin-left: 5px; }
            .info-grid { grid-template-columns: 1fr; }
            .info-grid div:nth-child(odd) { border-bottom: 1px dashed var(--border-color); margin-bottom: 5px; padding-bottom: 5px; }
        }
    </style>
</head>
<body>
<aside class="sidebar">
    <div class="sidebar-header">
        <h2><?php echo h(TOOL_TITLE); ?></h2>
    </div>
    <nav>
        <ul>
            <?php foreach ($tools as $tool_id => $tool_info): ?>
                <li class="<?php echo ($current_tool === $tool_id) ? 'active' : '' ?>">
                    <form method="GET" action="<?php echo h($_SERVER['PHP_SELF']); ?>">
                        <input type="hidden" name="tool" value="<?php echo h($tool_id); ?>">
                        <?php // Pour les outils "files", "download", "view_file", le token CSRF est important.
                        // Pour les changements d'outils simples, il est moins critique mais peut être ajouté.
                        // Les actions GET sensibles (download, view_file) sont déjà protégées.
                        // Le passage du token dans l'URL pour chaque changement d'outil via GET est ici omis pour la simplicité de la sidebar.
                        // Les actions POST sont protégées par csrf_input() dans leurs formulaires.
                        ?>
                        <?php if (($tool_id === 'files' || $current_tool === 'files') && isset($current_path)): ?>
                            <input type="hidden" name="path" value="<?php echo h($current_path); ?>">
                        <?php endif; ?>
                        <input type="hidden" name="csrf_token" value="<?php echo h($csrf_token); ?>"> <!-- Ajout du token pour cohérence GET -->
                        <button type="submit">
                            <span class="icon"><?php echo h($tool_info['icon']); ?></span> <?php echo h($tool_info['label']); ?>
                        </button>
                    </form>
                </li>
            <?php endforeach; ?>
        </ul>
    </nav>
    <div class="sidebar-footer">
        <form method="POST" action="<?php echo h($_SERVER['PHP_SELF']); ?>" class="logout-form">
            <?php echo csrf_input(); ?>
            <input type="hidden" name="logout" value="1">
            <button type="submit" class="btn"><span class="icon">🚪</span> Déconnexion</button>
        </form>
    </div>
</aside>

<main class="main-content">
    <h1><?php echo h(isset($tools[$current_tool]['label']) ? $tools[$current_tool]['label'] : 'Outil Inconnu'); ?></h1>
    <?php
    if (isset($page_message) && $page_message) {
        echo display_message($page_message['text'], $page_message['type']);
    }
    if (isset($tools[$current_tool]) && function_exists($tools[$current_tool]['handler'])) {
        $handler_func = $tools[$current_tool]['handler'];
        if ($current_tool === 'files') {
            $handler_func($current_path, $csrf_token);
        } elseif ($current_tool === 'db_dump') {
            $handler_func($csrf_token, $tool_output);
        } elseif ($current_tool === 'email_test') {
            $handler_func($csrf_token);
        } else {
            $handler_func();
        }
    } else {
        if ($current_tool !== 'dashboard') {
            echo display_message('Outil non valide : ' . h($current_tool) . '. Affichage du Dashboard.', 'warning');
        }
        display_dashboard();
    }
    ?>
</main>
</body>
</html>