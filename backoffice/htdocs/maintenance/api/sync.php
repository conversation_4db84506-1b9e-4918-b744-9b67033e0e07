<?php
/**
 * Sync API Endpoint
 * 
 * Provides API access to synchronization data
 * 
 * @package Maintenance\API
 * <AUTHOR> Team
 */

// Define access constant
define('MAINTENANCE_ACCESS', true);

// Include required files
require_once('../config/constants.php');
require_once('../../http.inc.php');
require_once('../../tenants.inc.php');
require_once('../../users.inc.php');
require_once('../../orders.inc.php');
require_once('../../products.inc.php');
require_once('../../fields.inc.php');
require_once('../../tasks.inc.php');
require_once('../../cfg.variables.inc.php');

// Include our utilities and models
require_once('../utils/Authentication.php');
require_once('../utils/Response.php');
require_once('../utils/Validator.php');
require_once('../utils/Database.php');
require_once('../models/BaseModel.php');
require_once('../models/SyncModel.php');

try {
    // Initialize session
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check authentication
    Maintenance_Utils_Authentication::requireAuth();

    // Handle CORS
    Maintenance_Utils_Response::handleOptionsRequest();

    // Get request method and action
    $method = isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['REQUEST_METHOD'] : 'GET';
    $action = isset($_GET['action']) ? $_GET['action'] : 'info';

    // Load configuration
    global $config;
    if (function_exists('cfg_variables_load')) {
        cfg_variables_load($config);
    }

    // Initialize sync model
    $syncModel = new Maintenance_Models_SyncModel($config);

    // Route the request
    switch ($method) {
        case 'GET':
            handleGetRequest($action, $syncModel);
            break;

        case 'POST':
            handlePostRequest($action, $syncModel);
            break;

        default:
            Maintenance_Utils_Response::methodNotAllowed(array('GET', 'POST'));
    }

} catch (Exception $e) {
    error_log('Sync API Error: ' . $e->getMessage());
    Maintenance_Utils_Response::serverError('An unexpected error occurred');
}

/**
 * Handle GET requests
 *
 * @param string $action
 * @param Maintenance_Models_SyncModel $syncModel
 * @return void
 */
function handleGetRequest($action, $syncModel)
{
    switch ($action) {
        case 'info':
        default:
            getSyncInfo($syncModel);
            break;
            
        case 'tenant':
            getTenantInfo($syncModel);
            break;
            
        case 'counts':
            getSyncCounts($syncModel);
            break;
            
        case 'status':
            getSyncStatus($syncModel);
            break;
    }
}

/**
 * Handle POST requests
 *
 * @param string $action
 * @param Maintenance_Models_SyncModel $syncModel
 * @return void
 */
function handlePostRequest($action, $syncModel)
{
    switch ($action) {
        case 'update-status':
            updateSyncStatus($syncModel);
            break;

        default:
            Maintenance_Utils_Response::error('Invalid action for POST request');
    }
}

/**
 * Get complete sync information
 *
 * @param Maintenance_Models_SyncModel $syncModel
 * @return void
 */
function getSyncInfo($syncModel)
{
    try {
        $syncInfo = $syncModel->getSyncInfo();
        Maintenance_Utils_Response::success($syncInfo);
    } catch (Exception $e) {
        Maintenance_Utils_Response::serverError('Failed to get sync information');
    }
}

/**
 * Get tenant information
 *
 * @param Maintenance_Models_SyncModel $syncModel
 * @return void
 */
function getTenantInfo($syncModel)
{
    try {
        $tenantId = Maintenance_Utils_Validator::sanitizeInt(isset($_GET['tenant_id']) ? $_GET['tenant_id'] : 0);

        if (!$tenantId) {
            Maintenance_Utils_Response::error('Invalid tenant ID');
        }

        $tenantInfo = $syncModel->getTenantInfo($tenantId);

        if (!$tenantInfo) {
            Maintenance_Utils_Response::notFound('Tenant');
        }

        // Remove sensitive information
        unset($tenantInfo['token']);

        Maintenance_Utils_Response::success($tenantInfo);
    } catch (Exception $e) {
        Maintenance_Utils_Response::serverError('Failed to get tenant information');
    }
}

/**
 * Get sync counts only
 *
 * @param Maintenance_Models_SyncModel $syncModel
 * @return void
 */
function getSyncCounts($syncModel)
{
    try {
        $counts = array(
            'orders' => $syncModel->getWaitingOrdersCount(),
            'quotes' => $syncModel->getWaitingQuotesCount(),
            'users' => $syncModel->getWaitingUsersCount(),
            'addresses' => $syncModel->getWaitingAddressesCount()
        );

        Maintenance_Utils_Response::success($counts);
    } catch (Exception $e) {
        Maintenance_Utils_Response::serverError('Failed to get sync counts');
    }
}

/**
 * Get sync status
 *
 * @param Maintenance_Models_SyncModel $syncModel
 * @return void
 */
function getSyncStatus($syncModel)
{
    try {
        global $config;
        $tenantId = isset($config['tnt_id']) ? $config['tnt_id'] : 0;

        $tenant = $syncModel->getTenantInfo($tenantId);

        if (!$tenant) {
            Maintenance_Utils_Response::notFound('Tenant');
        }

        $status = array(
            'sync_reboot' => isset($tenant['sync_reboot']) ? $tenant['sync_reboot'] : 0,
            'last_sync' => isset($tenant['last-sync']) ? $tenant['last-sync'] : null,
            'gescom_type' => $syncModel->getGescomTypeName()
        );

        Maintenance_Utils_Response::success($status);
    } catch (Exception $e) {
        Maintenance_Utils_Response::serverError('Failed to get sync status');
    }
}

/**
 * Update sync status
 *
 * @param Maintenance_Models_SyncModel $syncModel
 * @return void
 */
function updateSyncStatus($syncModel)
{
    try {
        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }

        // Validate input
        $validator = new Maintenance_Utils_Validator($input);
        $validator->integer('sync_reboot')
                 ->minLength('last_sync', 1);

        if ($validator->fails()) {
            Maintenance_Utils_Response::validationError($validator->getErrors());
        }

        // Update sync status
        $updateData = array();

        if ($validator->get('sync_reboot') !== null) {
            $updateData['sync_reboot'] = $validator->get('sync_reboot');
        }

        if ($validator->get('last_sync') !== null) {
            $updateData['last-sync'] = $validator->get('last_sync');
        }

        if (empty($updateData)) {
            Maintenance_Utils_Response::error('No data to update');
        }

        $success = $syncModel->updateSyncStatus($updateData);

        if ($success) {
            Maintenance_Utils_Response::success(null, 'Sync status updated successfully');
        } else {
            Maintenance_Utils_Response::serverError('Failed to update sync status');
        }

    } catch (Exception $e) {
        Maintenance_Utils_Response::serverError('Failed to update sync status');
    }
}
