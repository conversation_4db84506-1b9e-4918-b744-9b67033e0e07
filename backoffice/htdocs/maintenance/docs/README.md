# Maintenance Interface - Refactored (PHP 5.6 Compatible)

## Overview

This is a refactored version of the RiaShop maintenance interface, designed to improve code organization while maintaining full compatibility with PHP 5.6. The interface provides better user experience and follows structured coding practices.

**🎨 Admin Skin Integration**: The refactored system now uses the existing RiaShop admin skin (`admin/skin/header.inc.php` and `admin/skin/footer.inc.php`) to maintain visual consistency with the rest of the admin interface, just like the original maintenance system.

## ✅ CONFIGURATION SYSTEM IMPLEMENTED

The configuration management system has been successfully implemented and is fully functional. You can now access:

- **Main Configuration Page**: `/maintenance/?controller=configuration`
- **With Tenant ID**: `/maintenance/?controller=configuration&tnt_id=1`
- **With Website ID**: `/maintenance/?controller=configuration&tnt_id=1&wst_id=1`

### Configuration Features

✅ **ConfigurationController** - Handles configuration variable management
✅ **ConfigurationModel** - Data access layer for configuration operations
✅ **Configuration Views** - User interface for managing variables
✅ **ViewHelpers** - Utility functions for rendering configuration forms
✅ **CSS Styling** - Responsive design for configuration pages
✅ **Field Type Support** - All original field types (text, boolean, select, etc.)
✅ **GESCOM Integration** - Support for sync and ERP system variables
✅ **Yuto Support** - Configuration variables for Yuto application
✅ **Variable Filtering** - Filter variables by code or type
✅ **Override Management** - View and edit variable overrides
✅ **Hierarchical Variables** - Support for parent-child variable relationships
✅ **Admin Skin Integration** - Uses existing RiaShop admin interface styling
✅ **AJAX Support** - Dynamic updates without full page reload
✅ **Tasks Management** - Complete tasks management interface
✅ **System Status Page** - Formatted system status display
✅ **CSS Hover Fixes** - Proper styling without admin skin conflicts

## Features

- **PHP 5.6 Compatible**: Fully compatible with PHP 5.6 - no modern PHP syntax used
- **Clean Architecture**: Separation of concerns with MVC pattern
- **Improved Security**: Enhanced authentication, input validation, and CSRF protection
- **Responsive Design**: Mobile-friendly interface
- **Real-time Updates**: AJAX-based live updates of synchronization information
- **Error Handling**: Comprehensive error handling and logging
- **API Endpoints**: RESTful API for programmatic access
- **Backward Compatible**: Works with existing RiaShop infrastructure

## Directory Structure

```
maintenance/
├── api/                    # API endpoints
│   └── sync.php           # Synchronization API
├── assets/                # Static assets
│   ├── css/              # Stylesheets
│   │   ├── main.css      # Main styles
│   │   └── dashboard.css # Dashboard-specific styles
│   └── js/               # JavaScript files
├── config/               # Configuration files
│   └── constants.php     # Application constants
├── controllers/          # Business logic controllers
│   ├── BaseController.php
│   ├── MaintenanceController.php
│   ├── TaskController.php
│   └── ConfigurationController.php
├── models/               # Data access layer
│   ├── BaseModel.php
│   ├── SyncModel.php
│   ├── TaskModel.php
│   └── ConfigurationModel.php
├── services/             # Business logic services (future)
├── utils/                # Utility classes
│   ├── Authentication.php
│   ├── Database.php
│   ├── Response.php
│   ├── Validator.php
│   └── ViewHelpers.php
├── views/                # Template files
│   ├── layouts/
│   │   ├── header.php
│   │   └── footer.php
│   ├── maintenance/
│   │   └── dashboard.php
│   ├── tasks/
│   │   └── index.php
│   ├── configuration/
│   │   └── index.php
│   └── error.php
├── index.php             # Main entry point
└── README.md            # This file
```

## Requirements

- **PHP 5.6 or higher** (tested and optimized for PHP 5.6)
- **MySQL** (uses existing RiaShop database connection)
- **Web server** (Apache/Nginx)
- **Existing RiaShop installation**

## Installation

1. **Backup**: Always backup your existing maintenance interface before installing
2. **Upload**: Upload the `maintenance` folder to your `backoffice/htdocs/` directory
3. **Permissions**: Ensure proper file permissions (644 for files, 755 for directories)
4. **Configuration**: The interface will use your existing RiaShop configuration
5. **Test**: Access `/maintenance/test.php` to verify PHP 5.6 compatibility

## Usage

### Main Dashboard

Access the main dashboard at: `/maintenance/`

Features:
- Real-time synchronization information
- Waiting items counts (orders, quotes, users, addresses)
- Quick access to tasks and configuration management
- System status monitoring

### API Endpoints

#### Sync Information
```
GET /maintenance/api/sync.php?action=info
```
Returns complete synchronization information.

#### Sync Counts
```
GET /maintenance/api/sync.php?action=counts
```
Returns only the waiting items counts.

#### Update Sync Status
```
POST /maintenance/api/sync.php?action=update-status
Content-Type: application/json

{
    "sync_reboot": 1,
    "last_sync": "2024-01-15 10:30:00"
}
```

### Controllers

#### Maintenance_Controllers_MaintenanceController
- `index()`: Display main dashboard
- `getSyncInfo()`: Get synchronization information
- `ajaxSyncInfo()`: AJAX endpoint for sync info updates
- `updateSyncStatus()`: Update sync status
- `getSystemStatus()`: Get system status information

#### Maintenance_Controllers_TaskController
- `index()`: Display tasks management page
- `getTasks()`: Get all tasks organized by categories
- `activateTasks()`: Activate/deactivate tasks
- `toggleTask()`: Toggle single task status
- `forceTask()`: Force task execution

#### Maintenance_Controllers_ConfigurationController
- `index()`: Display configuration variables management page
- `save()`: Save configuration variable overrides
- Supports filtering by variable code
- Handles tenant and website-specific configurations
- Manages sync and Yuto application variables

### Models

#### Maintenance_Models_SyncModel
- `getSyncInfo()`: Get complete sync information
- `getWaitingOrdersCount()`: Get waiting orders count
- `getWaitingQuotesCount()`: Get waiting quotes count
- `getWaitingUsersCount()`: Get waiting users count
- `getWaitingAddressesCount()`: Get waiting addresses count
- `getGescomTypeName()`: Get GESCOM type name
- `updateSyncStatus()`: Update sync status

#### Maintenance_Models_TaskModel
- `getAllTasksOrganized()`: Get all tasks organized by categories
- `getTaskActivation()`: Get task activation status
- `isTaskActive()`: Check if task is active
- `activateTasks()`: Activate/deactivate tasks
- `forceTaskExecution()`: Force task execution

#### Maintenance_Models_ConfigurationModel
- `getTenant()`: Get tenant information
- `getWebsites()`: Get websites for a tenant
- `getWebsite()`: Get specific website information
- `getSyncInfo()`: Get synchronization information
- `getConfigurationVariables()`: Get configuration variables with overrides
- `saveConfigurationVariables()`: Save variable overrides
- `clearConfigCache()`: Clear configuration cache

### Utilities

#### Maintenance_Utils_Authentication
- `isAuthorized()`: Check if user is authorized
- `requireAuth()`: Require authentication or send 403
- `generateCsrfToken()`: Generate CSRF token
- `validateCsrfToken()`: Validate CSRF token

#### Maintenance_Utils_Response
- `json()`: Send JSON response
- `success()`: Send success response
- `error()`: Send error response
- `validationError()`: Send validation error response

#### Maintenance_Utils_Validator
- `required()`: Validate required field
- `email()`: Validate email format
- `integer()`: Validate integer
- `minLength()`: Validate minimum length
- `maxLength()`: Validate maximum length

## Security Features

- **Authentication**: Checks for RiaStudio users or authorized emails
- **CSRF Protection**: CSRF tokens for POST requests
- **Input Validation**: Comprehensive input validation and sanitization
- **Rate Limiting**: Basic rate limiting for authentication attempts
- **Error Logging**: Detailed error logging for debugging

## PHP 5.6 Compatibility Features

The refactored interface is fully compatible with PHP 5.6:
- **No namespaces**: Uses prefixed class names (e.g., `Maintenance_Utils_Authentication`)
- **No type declarations**: All method parameters and return types are untyped
- **No null coalescing operator**: Uses `isset()` checks instead of `??`
- **Array syntax**: Uses `array()` instead of `[]` syntax
- **Exception handling**: Uses standard `Exception` class, not typed exceptions
- **Function compatibility**: Checks for function existence before using newer PHP functions

## Backward Compatibility

The refactored interface maintains backward compatibility with the existing RiaShop system:
- Uses existing database functions where available
- Integrates with existing authentication system
- Maintains existing URL patterns where possible
- Falls back to original functions when new implementations are not available
- Compatible with existing PHP 5.6 infrastructure

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance

- **Optimized CSS**: Minified and optimized stylesheets
- **Efficient JavaScript**: Modern JavaScript with minimal dependencies
- **Database Optimization**: Efficient database queries with caching
- **AJAX Updates**: Real-time updates without full page reloads

## Development

### Adding New Features

1. **Controllers**: Add new controllers in the `controllers/` directory
2. **Models**: Add new models in the `models/` directory
3. **Views**: Add new views in the `views/` directory
4. **Routes**: Update `index.php` to handle new routes

### Coding Standards

- Follow PHP 5.6 compatible coding standards
- Use prefixed class names instead of namespaces
- Use `array()` syntax instead of `[]`
- Use `isset()` checks instead of null coalescing operator
- Use meaningful variable and function names
- Add proper documentation and comments
- Implement proper error handling
- Write unit tests for new functionality

## Troubleshooting

### Common Issues

1. **403 Forbidden**: Check user authentication and permissions
2. **500 Internal Server Error**: Check error logs for detailed information
3. **AJAX Errors**: Check browser console for JavaScript errors
4. **Database Errors**: Verify database connection and permissions

### Debug Mode

To enable debug mode, add this to your configuration:
```php
define('MAINTENANCE_DEBUG', true);
```

## Support

For support and bug reports, please contact the RiaShop development team.

## License

This software is proprietary to RiaShop. All rights reserved.

## Recent Fixes & Updates

### 🔧 Latest Fixes (December 2024)

**Issue 1: CSS Hover Effect Fixed**
- ✅ Removed unwanted text-decoration underlines on dashboard action cards
- ✅ Added specific CSS overrides for `.quick-actions .action-card:hover`
- ✅ Maintains admin skin compatibility

**Issue 2: Tasks Management Complete**
- ✅ Created missing `views/tasks/index.php` with full tasks interface
- ✅ Added tasks management with start/stop/force functionality
- ✅ Integrated with admin skin and AJAX support
- ✅ Added responsive CSS styling (`assets/css/tasks.css`)
- ✅ PHP 5.6 compatible implementation

**Issue 3: System Status Page**
- ✅ Created proper HTML view for system status instead of raw JSON
- ✅ Added `systemStatus()` method to MaintenanceController
- ✅ Updated routing to display formatted system status page
- ✅ Added health indicators and system information display
- ✅ Added responsive CSS styling (`assets/css/system-status.css`)

### 🚀 Available URLs

All URLs now work with proper admin skin integration:

- `/maintenance/` - Main dashboard
- `/maintenance/?controller=configuration` - Configuration management
- `/maintenance/?controller=tasks` - Tasks management
- `/maintenance/?action=system-status` - System status page

## Changelog

### Version 2.0.0 (PHP 5.6 Compatible)
- Complete refactoring with MVC architecture
- Full PHP 5.6 compatibility (no modern PHP syntax)
- Improved security and validation
- Responsive design with mobile support
- API endpoints for programmatic access
- Real-time updates with AJAX
- Comprehensive error handling and logging
- Backward compatibility with existing RiaShop system
