# Guide du Workflow Granulaire - Module de Mise à Jour RiaShop

## Vue d'ensemble

Le module de mise à jour RiaShop a été considérablement amélioré pour offrir un contrôle granulaire et une transparence complète sur le processus de mise à jour. Cette nouvelle version permet aux utilisateurs d'exécuter chaque étape individuellement, d'utiliser leurs propres credentials SVN, et de voir exactement où les opérations sont effectuées.

## Nouvelles Fonctionnalités

### 1. Workflow Granulaire

#### Backend (Engine) - 4 Étapes
1. **Mise à Jour SVN Engine** - Met à jour le dépôt SVN de l'engine
2. **Exécution Scripts SQL** - Exécute les scripts de migration de base de données
3. **Exécution Scripts PHP** - Exécute les scripts de migration PHP
4. **Mise à Jour Version** - Met à jour le numéro de version en base

#### Frontend (Client) - 1 Étape
1. **Mise à Jour SVN Frontend** - Met à jour le dépôt SVN du frontend

### 2. Credentials SVN Personnalisés

- **Section optionnelle** pour saisir username/password SVN
- **Utilisation temporaire** - credentials non stockés
- **Fallback automatique** vers les credentials système si non fournis
- **Support pour toutes les étapes SVN**

### 3. Affichage Détaillé des Dépôts

#### Informations Engine Repository
- Chemin local du dépôt
- URL du dépôt SVN
- Révision actuelle
- Statut du dépôt
- Dernière modification

#### Informations Frontend Repository
- Mêmes informations que pour l'engine
- Validation de l'existence et accessibilité

### 4. Gestion d'Erreurs Améliorée

- **Retry individuel** pour chaque étape échouée
- **Skip option** pour ignorer les étapes problématiques
- **Messages d'erreur détaillés** avec solutions suggérées
- **Logs téléchargeables** pour chaque étape

## Interface Utilisateur

### Structure de la Page

```
┌─ Retour au dashboard
├─ Avertissement de sauvegarde
├─ État du Système
├─ Informations des Dépôts
├─ Credentials SVN (Optionnel)
├─ Workflow Backend (4 étapes)
└─ Workflow Frontend (1 étape)
```

### États des Étapes

- **En attente** (gris) - Étape non encore exécutée
- **En cours** (bleu) - Étape en cours d'exécution
- **Terminé** (vert) - Étape terminée avec succès
- **Échec** (rouge) - Étape échouée avec option retry
- **Ignoré** (orange) - Étape ignorée par l'utilisateur

### Contrôles Disponibles

- **Exécuter** - Lance l'étape
- **Réessayer** - Relance une étape échouée
- **Ignorer** - Ignore une étape échouée
- **Télécharger Log** - Télécharge le fichier de log

## Utilisation

### 1. Préparation

1. **Sauvegarde** - Effectuez une sauvegarde complète de la base de données
2. **Vérification** - Consultez l'état du système et les informations des dépôts
3. **Credentials** - Optionnellement, saisissez vos credentials SVN

### 2. Workflow Backend

#### Étape 1: Mise à Jour SVN Engine
```
Objectif: Mettre à jour le code source de l'engine
Prérequis: Accès SVN au dépôt engine
Résultat: Code source à jour dans le répertoire engine
```

#### Étape 2: Exécution Scripts SQL
```
Objectif: Appliquer les modifications de base de données
Prérequis: Étape 1 terminée, scripts SQL disponibles
Résultat: Base de données mise à jour
```

#### Étape 3: Exécution Scripts PHP
```
Objectif: Exécuter les scripts de migration PHP
Prérequis: Étapes 1-2 terminées, scripts PHP disponibles
Résultat: Migrations PHP appliquées
```

#### Étape 4: Mise à Jour Version
```
Objectif: Mettre à jour le numéro de version
Prérequis: Étapes 1-3 terminées
Résultat: Version mise à jour en base de données
```

### 3. Workflow Frontend

#### Étape 1: Mise à Jour SVN Frontend
```
Objectif: Mettre à jour le code source du frontend
Prérequis: Accès SVN au dépôt frontend
Résultat: Code frontend à jour
```

## API Endpoints

### Statut du Système
```
GET /maintenance/?controller=updates&action=system-status
```

### Étapes Backend
```
POST /maintenance/?controller=updates&action=svn-update-engine
POST /maintenance/?controller=updates&action=execute-sql-scripts
POST /maintenance/?controller=updates&action=execute-php-scripts
POST /maintenance/?controller=updates&action=update-version
```

### Étapes Frontend
```
POST /maintenance/?controller=updates&action=svn-update-frontend
```

### Paramètres pour Étapes SVN
```json
{
    "csrf_token": "token_value",
    "username": "svn_username",
    "password": "svn_password"
}
```

## Gestion d'Erreurs

### Erreurs SVN Communes

#### Authentification (E215004)
- **Cause**: Credentials SVN invalides
- **Solution**: Vérifier username/password
- **Action**: Saisir de nouveaux credentials et réessayer

#### Connectivité (E170013)
- **Cause**: Impossible de se connecter au serveur SVN
- **Solution**: Vérifier la connectivité réseau
- **Action**: Vérifier l'URL et réessayer plus tard

#### Working Copy Verrouillée
- **Cause**: Opération SVN précédente interrompue
- **Solution**: Cleanup automatique
- **Action**: Le système tente une résolution automatique

### Stratégies de Récupération

1. **Retry Automatique** - Pour les erreurs temporaires
2. **Cleanup SVN** - Pour les working copies verrouillées
3. **Résolution de Conflits** - Priorité au serveur
4. **Skip Option** - Pour ignorer les étapes problématiques

## Configuration

### Variables de Configuration

```php
// Dans config/constants.php
define('ENGINE_SVN_REPO_PATH', '/var/www/engine');
define('FRONTEND_SVN_REPO_PATH', '/var/www/frontend');
define('MAJS_DIR_PATH', 'www/backend/majs/');
define('LOGS_DIR_PATH', 'maintenance/logs/');
```

### Base de Données

**Configuration simplifiée:**
- Utilise uniquement la table `cfg_overrides` existante
- Une seule opération de base de données: mise à jour de `RIASHOP_VERSION`
- Aucune table supplémentaire créée
- Aucune variable de configuration ajoutée

## Sécurité

### Protection CSRF
- Tokens CSRF requis pour toutes les opérations POST
- Validation côté serveur

### Credentials SVN
- **Non stockés** - utilisation temporaire uniquement
- **Transmission sécurisée** via HTTPS recommandé
- **Pas de persistance** en base de données

### Validation des Entrées
- Échappement SQL pour toutes les requêtes
- Validation des chemins de fichiers
- Vérification des permissions

## Monitoring et Logs

### Logs JSON
```json
{
    "timestamp": "2024-01-15 14:30:25",
    "level": "INFO",
    "operation_type": "backend_update_step",
    "message": "SVN update engine completed successfully",
    "details": {
        "step": "svn_update_engine",
        "duration_ms": 1500
    }
}
```

### Métriques Disponibles
- Temps d'exécution par étape
- Taux de succès/échec
- Erreurs par type
- Utilisation des credentials personnalisés

## Dépannage

### Problèmes Courants

1. **Étape bloquée en "En cours"**
   - Actualiser la page
   - Vérifier les logs serveur
   - Redémarrer le processus

2. **Credentials SVN refusés**
   - Vérifier username/password
   - Tester l'accès SVN manuellement
   - Contacter l'administrateur SVN

3. **Scripts SQL échouent**
   - Vérifier les permissions de base de données
   - Consulter les logs détaillés
   - Vérifier la syntaxe SQL

### Scripts de Diagnostic

- `test-granular-workflow.php` - Test complet du workflow
- `test-svn-errors.php` - Test de gestion d'erreurs SVN
- `setup-database.php` - Configuration de la base de données

## Migration depuis l'Ancienne Version

### Compatibilité
- **Interface legacy** toujours disponible
- **APIs existantes** maintenues
- **Migration transparente** des données

### Activation du Workflow Granulaire
Le workflow granulaire est activé par défaut et ne nécessite aucune configuration de base de données supplémentaire.

## Support

### Logs à Consulter
1. Logs JSON du module (`maintenance/logs/`)
2. Logs Apache/PHP
3. Logs SVN système

### Informations à Fournir
- Version RiaShop actuelle
- Logs d'erreur complets
- Configuration des dépôts SVN
- Étapes déjà exécutées

Le workflow granulaire offre un contrôle total sur le processus de mise à jour tout en maintenant la simplicité d'utilisation pour les cas standards.
