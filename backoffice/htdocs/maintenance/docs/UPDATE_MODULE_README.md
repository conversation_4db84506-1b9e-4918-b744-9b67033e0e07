# Module de Mise à Jour Manuelle Riashop

## Vue d'ensemble

Ce module permet d'effectuer des mises à jour manuelles du backend (engine) et du frontend (client) de Riashop via l'interface de maintenance. Il gère automatiquement les mises à jour SVN, l'exécution des scripts de migration et la journalisation détaillée.

## Fonctionnalités

### Mise à Jour Backend (Engine)
- Mise à jour du dépôt SVN de l'engine
- Détection automatique des scripts de mise à jour applicables
- Exécution séquentielle des scripts SQL et PHP
- Mise à jour de la version en base de données
- Journalisation complète de toutes les opérations

### Mise à Jour Frontend (Client)
- Mise à jour du dépôt SVN du frontend
- Opération simple sans scripts de base de données

### Système de Journalisation
- Logs JSON détaillés pour chaque opération
- Horodatage et niveaux de log (INFO, WARNING, ERROR)
- Téléchargement des fichiers de log depuis l'interface

## Configuration

### Constantes Requises

Les constantes suivantes doivent être définies dans `config/constants.php` :

```php
// Clé de configuration pour la version Riashop dans cfg_overrides
define('RIASHOP_VERSION_CONFIG_KEY', 'RIASHOP_VERSION');

// Répertoire contenant les scripts de mise à jour
define('MAJS_DIR_PATH', 'www/backend/majs/');

// Chemin vers le dépôt SVN de l'engine
define('ENGINE_SVN_REPO_PATH', '/path/to/engine/repo');

// Chemin vers le dépôt SVN du frontend
define('FRONTEND_SVN_REPO_PATH', '/path/to/frontend/repo');

// Répertoire pour les fichiers de log
define('LOGS_DIR_PATH', 'maintenance/logs/');
```

### Prérequis Système

1. **Client SVN** : Le client SVN doit être installé et accessible depuis PHP
2. **Permissions** : Les répertoires de logs doivent être accessibles en écriture
3. **Base de données** : Accès à la table `cfg_overrides` pour la gestion des versions
4. **PHP** : Fonctions `shell_exec` ou `exec` activées pour les commandes SVN

## Structure des Fichiers

```
maintenance/
├── controllers/
│   └── UpdateController.php          # Contrôleur principal
├── models/
│   └── UpdateModel.php               # Modèle de données
├── utils/
│   ├── Logger.php                    # Système de journalisation
│   ├── VersionManager.php            # Gestion des versions
│   ├── SvnManager.php                # Gestion SVN
│   └── ScriptExecutor.php            # Exécution des scripts
├── views/
│   └── update/
│       └── index.php                 # Interface utilisateur
└── maintenance/
    └── logs/                         # Répertoire des logs
```

## Format des Scripts de Mise à Jour

Les scripts de mise à jour doivent suivre la convention de nommage :
- `maj_X.YYY.sql` pour les scripts SQL
- `maj_X.YYY.php` pour les scripts PHP

Où `X.YYY` représente le numéro de version (ex: `maj_1.200.sql`, `maj_1.200.php`).

### Ordre d'Exécution
1. Les scripts sont triés par version croissante
2. Pour une même version, les scripts SQL sont exécutés avant les scripts PHP
3. Seuls les scripts avec une version supérieure à la version actuelle sont exécutés

## Utilisation

### Interface Web

1. Accédez à `/maintenance/?controller=updates`
2. Vérifiez l'état du système dans le panneau d'information
3. Cliquez sur "Lancer la Mise à Jour Backend" ou "Lancer la Mise à Jour Frontend"
4. Suivez les résultats dans le panneau de résultats

### API AJAX

#### Mise à Jour Backend
```javascript
$.ajax({
    url: '/maintenance/?controller=updates&action=backend-update',
    method: 'POST',
    data: { csrf_token: token },
    dataType: 'json'
});
```

#### Mise à Jour Frontend
```javascript
$.ajax({
    url: '/maintenance/?controller=updates&action=frontend-update',
    method: 'POST',
    data: { csrf_token: token },
    dataType: 'json'
});
```

#### Statut du Système
```javascript
$.ajax({
    url: '/maintenance/?controller=updates&action=system-status',
    method: 'GET',
    dataType: 'json'
});
```

## Format des Logs

Les logs sont stockés au format JSON avec la structure suivante :

```json
{
    "timestamp": "2024-01-15 14:30:25",
    "level": "INFO",
    "operation_type": "backend_update",
    "message": "Starting backend update process",
    "details": {
        "current_version": "1.100",
        "scripts_found": 3
    }
}
```

### Types d'Opérations
- `backend_update` : Mise à jour du backend
- `frontend_update` : Mise à jour du frontend
- `svn_update` : Opérations SVN
- `script_execution` : Exécution de scripts
- `version_management` : Gestion des versions

## Sécurité

### Protection CSRF
- Tokens CSRF requis pour toutes les opérations de mise à jour
- Validation des tokens côté serveur

### Validation des Entrées
- Validation des paramètres d'entrée
- Échappement des données pour les requêtes SQL
- Vérification des chemins de fichiers

### Journalisation
- Toutes les opérations sont journalisées
- Les erreurs sont enregistrées avec des détails complets
- Accès aux logs via interface sécurisée

## Gestion d'Erreurs

### Arrêt en Cas d'Échec
- Le processus s'arrête immédiatement en cas d'erreur critique
- Les erreurs sont journalisées avec des détails complets
- Messages d'erreur clairs pour l'utilisateur

### Types d'Erreurs Gérées
- Échec des commandes SVN
- Erreurs d'exécution SQL
- Exceptions PHP dans les scripts
- Problèmes de permissions de fichiers

## Test et Débogage

### Script de Test
Utilisez `test-update.php` pour vérifier la configuration :

```bash
# Accédez au script de test
http://tenant-domain/maintenance/test-update.php
```

### Vérifications Recommandées
1. Connectivité à la base de données
2. Disponibilité du client SVN
3. Permissions des répertoires
4. Existence des dépôts SVN
5. Format des scripts de mise à jour

## Maintenance

### Nettoyage des Logs
Les fichiers de log s'accumulent dans le répertoire `LOGS_DIR_PATH`. Il est recommandé de mettre en place une rotation des logs.

### Sauvegarde
**Important** : Effectuez toujours une sauvegarde complète de la base de données avant d'exécuter des mises à jour backend.

### Surveillance
- Surveillez les logs pour détecter les erreurs récurrentes
- Vérifiez régulièrement l'état des dépôts SVN
- Testez les mises à jour sur un environnement de développement

## Dépannage

### Problèmes Courants

1. **SVN non disponible**
   - Vérifiez l'installation du client SVN
   - Testez les commandes SVN manuellement

2. **Permissions insuffisantes**
   - Vérifiez les permissions des répertoires de logs
   - Assurez-vous que PHP peut écrire dans les répertoires requis

3. **Scripts de mise à jour non trouvés**
   - Vérifiez le chemin `MAJS_DIR_PATH`
   - Vérifiez le format des noms de fichiers

4. **Erreurs de base de données**
   - Vérifiez la connectivité à la base de données
   - Vérifiez l'existence de la table `cfg_overrides`

### Support

Pour obtenir de l'aide :
1. Consultez les logs détaillés
2. Utilisez le script de test pour diagnostiquer les problèmes
3. Vérifiez la configuration des constantes
4. Contactez l'équipe de développement avec les logs d'erreur
