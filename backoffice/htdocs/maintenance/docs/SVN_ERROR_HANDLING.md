# Gestion des Erreurs SVN - Module de Mise à Jour Riashop

## Vue d'ensemble

Le module de mise à jour Riashop inclut une gestion avancée des erreurs SVN pour résoudre automatiquement les problèmes courants et fournir des messages d'erreur clairs aux utilisateurs.

## Erreurs SVN Courantes et Solutions

### 1. Erreur d'Authentification (E215004)

**Erreur typique :**
```
svn: E170013: Unable to connect to a repository at URL 'https://svn.riashop.fr/svn/riashop-engine/branches/localhost'
svn: E215004: No more credentials or we tried too many times. Authentication failed
```

**Causes possibles :**
- Credentials SVN non configurés pour l'utilisateur web
- Mot de passe expiré ou incorrect
- Accès au dépôt révoqué

**Solutions automatiques :**
- Le système détecte automatiquement cette erreur
- Message d'erreur formaté pour l'utilisateur
- Journalisation détaillée pour le débogage

**Solutions manuelles :**
```bash
# Tester l'accès SVN manuellement
sudo -u www-data svn info https://svn.riashop.fr/svn/riashop-engine/branches/localhost

# Configurer les credentials SVN
sudo -u www-data svn checkout https://svn.riashop.fr/svn/riashop-engine/branches/localhost /tmp/test
# Entrer les credentials quand demandé

# Vérifier les credentials stockés
sudo -u www-data svn auth
```

### 2. Working Copy Verrouillée (E155004)

**Erreur typique :**
```
svn: E155004: Working copy '/var/www/engine' locked.
svn: E155004: '/var/www/engine' is already locked.
```

**Causes possibles :**
- Opération SVN précédente interrompue
- Plusieurs opérations SVN simultanées
- Processus SVN bloqué

**Solutions automatiques :**
- Détection automatique du verrouillage
- Exécution de `svn cleanup` automatiquement
- Nouvelle tentative de mise à jour après cleanup

**Solutions manuelles :**
```bash
# Nettoyer le working copy
sudo -u www-data svn cleanup /var/www/engine

# Forcer le déverrouillage si nécessaire
sudo -u www-data svn cleanup --remove-unversioned --remove-ignored /var/www/engine
```

### 3. Conflits SVN (E155015)

**Erreur typique :**
```
svn: E155015: Aborting commit: '/path/to/file' remains in conflict
```

**Causes possibles :**
- Modifications locales en conflit avec le serveur
- Fichiers modifiés localement non committés

**Solutions automatiques :**
- Résolution automatique des conflits avec `--accept theirs-full`
- Priorité donnée à la version du serveur
- Nouvelle tentative de mise à jour après résolution

**Solutions manuelles :**
```bash
# Résoudre tous les conflits en faveur du serveur
sudo -u www-data svn resolve --accept theirs-full /var/www/engine -R

# Voir les conflits en cours
sudo -u www-data svn status /var/www/engine | grep "^C"
```

### 4. Erreur de Connectivité (E170013)

**Erreur typique :**
```
svn: E170013: Unable to connect to a repository at URL 'https://svn.example.com/repo'
svn: E000111: Connection refused
```

**Causes possibles :**
- Serveur SVN indisponible
- Problème de réseau
- URL de dépôt incorrecte
- Firewall bloquant la connexion

**Solutions automatiques :**
- Détection de l'erreur de connectivité
- Message d'erreur informatif
- Pas de tentative de résolution automatique (problème externe)

**Solutions manuelles :**
```bash
# Tester la connectivité
ping svn.riashop.fr
curl -I https://svn.riashop.fr

# Vérifier l'URL du dépôt
sudo -u www-data svn info /var/www/engine
```

## Configuration du Module

### Options SVN Utilisées

Le module utilise les options SVN suivantes pour minimiser les erreurs :

```bash
svn update --accept theirs-full --non-interactive
```

- `--accept theirs-full` : Résout automatiquement les conflits en faveur du serveur
- `--non-interactive` : Évite les prompts interactifs qui pourraient bloquer le processus

### Séquence de Résolution d'Erreurs

1. **Détection d'erreur** : Analyse du code de retour et de la sortie SVN
2. **Classification** : Identification du type d'erreur (auth, lock, conflict, etc.)
3. **Résolution automatique** : Application de la solution appropriée si possible
4. **Nouvelle tentative** : Relance de l'opération SVN après résolution
5. **Journalisation** : Enregistrement détaillé de toutes les étapes

## Journalisation des Erreurs SVN

### Format des Logs

```json
{
    "timestamp": "2024-01-15 14:30:25",
    "level": "ERROR",
    "operation_type": "svn_update",
    "message": "SVN update failed for engine",
    "details": {
        "command": "svn update /var/www/engine --accept theirs-full --non-interactive",
        "output": "svn: E215004: Authentication failed",
        "return_code": 1,
        "type": "engine",
        "error_analysis": {
            "error_code": "E215004",
            "description": "Authentication failed",
            "possible_causes": ["Invalid credentials", "Credentials not stored"]
        }
    }
}
```

### Niveaux de Log

- **INFO** : Opérations normales (début/fin de mise à jour)
- **WARNING** : Problèmes détectés mais résolus automatiquement
- **ERROR** : Erreurs critiques nécessitant une intervention

## Diagnostic et Dépannage

### Script de Test

Utilisez le script de test pour diagnostiquer les problèmes SVN :

```bash
# Accéder au script de test
http://your-domain/maintenance/test-svn-errors.php
```

### Vérifications Recommandées

1. **Connectivité réseau** vers le serveur SVN
2. **Credentials SVN** pour l'utilisateur web
3. **Permissions** sur les répertoires de working copy
4. **État des working copies** (verrouillage, conflits)

### Commandes de Diagnostic

```bash
# Vérifier l'état SVN
sudo -u www-data svn status /var/www/engine

# Voir les informations du dépôt
sudo -u www-data svn info /var/www/engine

# Tester la connectivité
sudo -u www-data svn list https://svn.riashop.fr/svn/riashop-engine/branches/localhost

# Voir les credentials stockés
sudo -u www-data svn auth --show-passwords
```

## Configuration Avancée

### Variables d'Environnement SVN

Pour des configurations spéciales, vous pouvez définir des variables d'environnement :

```bash
# Dans le script de démarrage Apache/PHP
export SVN_SSH="ssh -o StrictHostKeyChecking=no"
export SVN_EDITOR="true"  # Évite les prompts d'éditeur
```

### Configuration SVN Globale

Fichier `~/.subversion/config` pour l'utilisateur web :

```ini
[auth]
store-passwords = yes
store-plaintext-passwords = yes

[miscellany]
global-ignores = *.tmp *.log

[tunnels]
ssh = ssh -o ControlMaster=no
```

## Sécurité

### Bonnes Pratiques

1. **Credentials** : Utilisez des comptes SVN dédiés avec permissions limitées
2. **HTTPS** : Préférez HTTPS à HTTP pour les connexions SVN
3. **Logs** : Surveillez les logs pour détecter les tentatives d'accès non autorisées
4. **Backup** : Sauvegardez toujours avant les mises à jour

### Gestion des Credentials

- Stockez les credentials SVN de manière sécurisée
- Utilisez des mots de passe forts
- Renouvelez régulièrement les credentials
- Limitez les permissions aux dépôts nécessaires

## Support et Maintenance

### Surveillance

- Vérifiez régulièrement les logs de mise à jour
- Surveillez les erreurs récurrentes
- Testez les mises à jour sur un environnement de développement

### Maintenance Préventive

- Nettoyez périodiquement les working copies
- Vérifiez l'espace disque disponible
- Mettez à jour le client SVN si nécessaire
- Testez la connectivité aux dépôts

### Escalade

En cas de problème persistant :

1. Consultez les logs détaillés
2. Utilisez le script de test pour diagnostiquer
3. Vérifiez la configuration SVN
4. Contactez l'administrateur système si nécessaire
