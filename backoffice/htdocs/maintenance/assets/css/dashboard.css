/**
 * Dashboard CSS for Maintenance Interface
 *
 * Styles specific to the dashboard page
 *
 * @package Maintenance\Assets
 * <AUTHOR> Team
 */

/* Override admin skin hover effects for action cards */
.quick-actions .action-card:hover,
.quick-actions .action-card:hover *,
.quick-actions .action-card:hover .action-title,
.quick-actions .action-card:hover .action-description {
    text-decoration: none !important;
}

.quick-actions .action-card {
    text-decoration: none !important;
}

/* Dashboard sections */
.dashboard-section {
    background: #ffffff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #e9ecef;
}

.dashboard-section:last-child {
    margin-bottom: 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.section-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.25rem;
    font-weight: 600;
}

/* Sync info grid */
.sync-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.info-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.info-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    word-break: break-all;
}

/* Token display */
.token-display {
    font-family: 'Courier New', monospace;
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.9rem;
}

.token-full {
    font-family: 'Courier New', monospace;
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    word-break: break-all;
}

/* Waiting items grid */
.waiting-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.waiting-item-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.waiting-item-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007cba, #28a745);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.waiting-item-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-color: #007cba;
}

.waiting-item-card:hover::before {
    opacity: 1;
}

.item-icon {
    font-size: 2.5rem;
    width: 4rem;
    height: 4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg, #007cba, #28a745);
    color: white;
    flex-shrink: 0;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 124, 186, 0.3);
}

.orders-icon {
    background: linear-gradient(135deg, #007cba, #0056b3);
}

.quotes-icon {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.users-icon {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.addresses-icon {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.item-content {
    flex: 1;
    min-width: 0;
}

.item-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.item-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.count-display {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.count {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
}

.task-active {
    font-size: 0.75rem;
    color: #28a745;
    font-weight: 500;
    background: #d4edda;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    white-space: nowrap;
}

.item-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Quick actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.action-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-decoration: none;
    color: inherit;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007cba, #28a745, #ffc107, #dc3545);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.action-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: inherit;
    border-color: #007cba;
}

.action-card:hover::before {
    transform: translateX(0);
}

.action-icon {
    font-size: 2.5rem;
    width: 4rem;
    height: 4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.action-card:hover .action-icon {
    background: linear-gradient(135deg, #007cba, #28a745);
    color: white;
    transform: scale(1.1);
}

.action-content {
    flex: 1;
}

.action-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.action-description {
    font-size: 0.875rem;
    color: #6c757d;
    line-height: 1.4;
}

/* Update indicator */
.update-indicator {
    text-align: right;
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
    min-height: 1.5rem;
}

/* BigShip info */
.bigship-info {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 0.375rem;
    padding: 1rem;
    font-size: 0.9rem;
}

.bigship-info a {
    color: #007cba;
    font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
    .dashboard-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .sync-info-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .waiting-items-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .waiting-item-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
    }
    
    .item-icon {
        font-size: 2rem;
        width: 3rem;
        height: 3rem;
    }
    
    .count {
        font-size: 1.5rem;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .action-card {
        padding: 1rem;
    }
    
    .action-icon {
        font-size: 2rem;
        width: 3rem;
        height: 3rem;
    }
}

@media (max-width: 576px) {
    .info-card {
        padding: 0.75rem;
    }
    
    .info-value {
        font-size: 1rem;
    }
    
    .waiting-item-card {
        padding: 0.75rem;
    }
    
    .item-content {
        text-align: center;
    }
    
    .item-count {
        justify-content: center;
    }
}

/* Print styles */
@media print {
    .dashboard-section {
        box-shadow: none;
        border: 1px solid #000;
        break-inside: avoid;
        margin-bottom: 1rem;
    }
    
    .action-card {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .button {
        display: none;
    }
    
    .item-actions {
        display: none;
    }
}
