/**
 * System Status Styles
 *
 * Minimal styles that complement the admin skin for system status page
 */

/* Status container */
.status-container {
    margin-top: 20px;
}

.status-section {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 20px;
    padding: 20px;
}

.section-title {
    margin: 0 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #007cba;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Status grid */
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.status-label {
    font-weight: 500;
    color: #495057;
    flex: 1;
}

.status-value {
    font-family: monospace;
    color: #2c3e50;
    font-weight: 500;
    text-align: right;
    max-width: 60%;
    word-break: break-all;
}

.status-path {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Status indicators */
.status-indicator {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-ok {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Actions */
.status-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Loading indicator */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    border-radius: 4px;
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 10px;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Health summary */
.health-summary {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.health-indicator {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    min-width: 120px;
}

.health-good {
    background-color: #d4edda;
    border: 2px solid #28a745;
}

.health-warning {
    background-color: #fff3cd;
    border: 2px solid #ffc107;
}

.health-critical {
    background-color: #f8d7da;
    border: 2px solid #dc3545;
}

.health-score {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.health-good .health-score {
    color: #28a745;
}

.health-warning .health-score {
    color: #856404;
}

.health-critical .health-score {
    color: #dc3545;
}

.health-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
}

.health-details {
    flex: 1;
}

.health-details ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.health-details li {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
}

.health-details li:last-child {
    border-bottom: none;
}

.health-details li:before {
    content: '';
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 10px;
    flex-shrink: 0;
}

.check-ok:before {
    background-color: #28a745;
}

.check-error:before {
    background-color: #dc3545;
}

.check-warning:before {
    background-color: #ffc107;
}

/* Button styles that complement admin skin */
.button-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}

.button-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

.button-info {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: #fff;
}

.button-info:hover {
    background-color: #138496;
    border-color: #117a8b;
}

.button-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.button-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
}

.button-success {
    background-color: #28a745;
    border-color: #28a745;
    color: #fff;
}

.button-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

/* Icons (fallback if not available) */
.icon-server:before { content: '🖥️ '; }
.icon-database:before { content: '🗄️ '; }
.icon-sync:before { content: '🔄 '; }
.icon-tools:before { content: '🔧 '; }
.icon-action:before { content: '⚡ '; }
.icon-heart:before { content: '❤️ '; }
.icon-refresh:before { content: '🔄 '; }
.icon-shield:before { content: '🛡️ '; }
.icon-download:before { content: '⬇️ '; }
.icon-cache:before { content: '💾 '; }

/* Responsive design */
@media (max-width: 768px) {
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .status-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .status-value {
        text-align: left;
        max-width: 100%;
    }
    
    .health-summary {
        flex-direction: column;
        gap: 15px;
    }
    
    .health-indicator {
        min-width: auto;
        width: 100%;
    }
    
    .status-actions {
        flex-direction: column;
    }
    
    .status-actions .button {
        width: 100%;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .status-section {
        padding: 15px;
    }
    
    .section-title {
        font-size: 1.1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .status-item {
        padding: 10px;
    }
    
    .health-indicator {
        padding: 15px;
    }
    
    .health-score {
        font-size: 1.5rem;
    }
}
