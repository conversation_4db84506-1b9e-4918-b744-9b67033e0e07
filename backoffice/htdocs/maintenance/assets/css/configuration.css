/**
 * Configuration Page Styles - Simple and Functional
 *
 * Clean, modern styling that enhances the original table-based interface
 * without sacrificing usability for visual complexity
 */

/* Basic layout improvements */
h2 {
    color: #2c3e50;
    margin-bottom: 10px;
}

p {
    color: #6c757d;
    margin-bottom: 20px;
}

/* Form styling */
form {
    margin-bottom: 20px;
}

form div {
    margin-bottom: 15px;
}

/* Simple styling for the main selector */
#form-config-cfg {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

#form-config-cfg > div {
    margin-bottom: 15px;
}

#form-config-cfg label {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #495057;
    cursor: pointer;
}

#form-config-cfg input[type="checkbox"] {
    margin-right: 8px;
    cursor: pointer;
}

/* Loading state for select */
select.loading {
    opacity: 0.6;
    cursor: wait;
}

select:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

select, input[type="text"] {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background-color: #ffffff;
    transition: border-color 0.2s ease;
}

select:focus, input[type="text"]:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.25);
}

select {
    min-width: 250px;
    cursor: pointer;
}

label {
    font-weight: 500;
    color: #495057;
}

label input[type="checkbox"] {
    margin-right: 8px;
}

/* Button styling */
.button {
    display: inline-block;
    padding: 8px 16px;
    margin: 4px 2px;
    background-color: #007cba;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.button:hover {
    background-color: #005a8b;
}

.button-primary {
    background-color: #007cba;
}

.button-secondary {
    background-color: #6c757d;
}

.button-success {
    background-color: #28a745;
}

.button-danger {
    background-color: #dc3545;
}

/* Table styling - enhanced but functional */
.checklist {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    overflow: hidden;
}

.checklist caption {
    padding: 15px;
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.checklist th {
    background-color: #f8f9fa;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.checklist td {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: top;
}

.checklist tr:hover {
    background-color: #f8f9fa;
}

.checklist tfoot td {
    background-color: #f8f9fa;
    border-top: 2px solid #dee2e6;
    border-bottom: none;
    font-weight: 500;
}

/* Variable information styling */
.checklist td strong {
    color: #2c3e50;
    font-size: 1.05rem;
    display: block;
    margin-bottom: 8px;
}

.checklist td ul {
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 0.9rem;
}

.checklist td ul li {
    margin-bottom: 4px;
    color: #6c757d;
}

.checklist td ul li.code {
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
    border: 1px solid #e9ecef;
}

/* Input styling within table */
.checklist textarea {
    width: 100%;
    min-height: 80px;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.checklist textarea:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.25);
}

.checklist input[type="radio"] {
    margin-right: 6px;
}

.checklist label {
    margin-right: 15px;
    font-weight: normal;
    cursor: pointer;
}

/* Child variables styling */
.table-child-variable {
    margin: 10px 0;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.table-child-variable th {
    background-color: #e9ecef;
    color: #495057;
    font-size: 0.9rem;
}

.table-child-variable td {
    padding: 10px 12px;
    font-size: 0.9rem;
}

.tr-list-child {
    background-color: #f8f9fa;
}

.tr-list-child.hidden {
    display: none;
}

/* Multi-select styling */
.col-cls-obj {
    display: inline-block;
    vertical-align: top;
    width: 48%;
    margin-right: 2%;
}

.col-cls-obj label {
    display: block;
    margin-bottom: 5px;
    font-weight: normal;
    font-size: 0.9rem;
}

.col-cls-obj input[type="checkbox"] {
    margin-right: 5px;
}

/* Messages */
.error {
    background-color: #f8d7da;
    color: #721c24;
    padding: 12px 15px;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin: 15px 0;
    border-left: 4px solid #dc3545;
}

.success {
    background-color: #d4edda;
    color: #155724;
    padding: 12px 15px;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    margin: 15px 0;
    border-left: 4px solid #28a745;
}

/* Loading indicator */
.reloadPageAjax {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    border-radius: 4px;
    z-index: 9999;
}

.reloadPageAjax:before {
    content: "Chargement...";
}

/* Filter input styling */
#filter {
    margin-left: 10px;
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 13px;
}

/* Help icon styling */
img[src*="help.png"] {
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

img[src*="help.png"]:hover {
    opacity: 1;
}

/* Smooth transitions for form elements */
.checklist tr {
    transition: background-color 0.2s ease;
}

.checklist textarea,
.checklist input[type="radio"] {
    transition: all 0.2s ease;
}

/* Enhanced focus states */
.checklist textarea:focus {
    transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 768px) {
    .checklist {
        font-size: 14px;
    }

    .checklist th,
    .checklist td {
        padding: 8px 10px;
    }

    .col-cls-obj {
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
    }

    select, #form-config-cfg select {
        min-width: auto;
        width: 100%;
    }

    .checklist textarea {
        min-height: 60px;
    }

    #form-config-cfg {
        padding: 15px;
        margin-bottom: 20px;
    }

    #form-config-cfg select {
        padding: 10px 14px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    #form-config-cfg {
        padding: 12px;
        border-radius: 6px;
    }

    #form-config-cfg select {
        padding: 8px 12px;
        font-size: 13px;
        min-width: auto;
    }

    .reloadPageAjax {
        padding: 20px 25px;
        font-size: 14px;
    }
}
