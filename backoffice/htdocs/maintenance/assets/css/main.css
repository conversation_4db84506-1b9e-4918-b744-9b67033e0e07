/**
 * Main CSS for Maintenance Interface
 * 
 * Base styles and common components
 * 
 * @package Maintenance\Assets
 * <AUTHOR> Team
 */

/* Reset and base styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.2;
    color: #2c3e50;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-top: 0;
    margin-bottom: 1rem;
}

/* Links */
a {
    color: #007cba;
    text-decoration: none;
    transition: color 0.15s ease-in-out;
}

a:hover {
    color: #005a87;
    text-decoration: underline;
}

/* Buttons */
.button, .btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    margin: 0.25rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    user-select: none;
    vertical-align: middle;
}

.button:hover, .btn:hover {
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.button:active, .btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.button:disabled, .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Button variants */
.button-primary, .btn-primary {
    background-color: #007cba;
    border-color: #007cba;
    color: #ffffff;
}

.button-primary:hover, .btn-primary:hover {
    background-color: #005a87;
    border-color: #005a87;
    color: #ffffff;
}

.button-secondary, .btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #ffffff;
}

.button-secondary:hover, .btn-secondary:hover {
    background-color: #545b62;
    border-color: #545b62;
    color: #ffffff;
}

.button-success, .btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: #ffffff;
}

.button-success:hover, .btn-success:hover {
    background-color: #1e7e34;
    border-color: #1e7e34;
    color: #ffffff;
}

.button-warning, .btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.button-warning:hover, .btn-warning:hover {
    background-color: #e0a800;
    border-color: #e0a800;
    color: #212529;
}

.button-danger, .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #ffffff;
}

.button-danger:hover, .btn-danger:hover {
    background-color: #c82333;
    border-color: #c82333;
    color: #ffffff;
}

/* Button sizes */
.button-sm, .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
}

.button-lg, .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 0.5rem;
}

/* Button link */
.button-link {
    background: none;
    border: none;
    color: #007cba;
    text-decoration: underline;
    cursor: pointer;
    padding: 0;
    margin: 0;
    font-size: inherit;
}

.button-link:hover {
    color: #005a87;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #495057;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: #495057;
    background-color: #ffffff;
    border-color: #007cba;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 124, 186, 0.25);
}

.form-control:disabled {
    background-color: #e9ecef;
    opacity: 1;
}

/* Tables */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #495057;
    border-collapse: collapse;
    background-color: #ffffff;
    border-radius: 0.375rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    text-align: left;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.025);
}

/* Notices/Alerts */
.notice, .alert {
    position: relative;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    border-left: 4px solid;
}

.notice-info, .alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
    border-left-color: #17a2b8;
}

.notice-success, .alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
    border-left-color: #28a745;
}

.notice-warning, .alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
    border-left-color: #ffc107;
}

.notice-error, .alert-error, .notice-danger, .alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
    border-left-color: #dc3545;
}

.notice-super-admin {
    color: #721c24;
    background-color: #fff5f5;
    border-color: #fed7d7;
    border-left-color: #dc3545;
    font-weight: 500;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.status-indicator.active {
    color: #155724;
    background-color: #d4edda;
}

.status-indicator.inactive {
    color: #721c24;
    background-color: #f8d7da;
}

.status-badge {
    display: inline-block;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.status-stopped {
    color: #721c24;
    background-color: #f8d7da;
}

.status-restart {
    color: #856404;
    background-color: #fff3cd;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Fade animation */
.fade-update {
    background-color: #fff3cd !important;
    transition: background-color 0.5s ease;
}

/* Icons */
.icon-refresh::before { content: "🔄"; }
.icon-eye::before { content: "👁"; }
.icon-external-link::before { content: "🔗"; }
.icon-download::before { content: "💾"; }
.icon-settings::before { content: "⚙️"; }
.icon-chart::before { content: "📊"; }

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

/* Responsive utilities */
@media (max-width: 576px) {
    .container {
        padding: 0 0.5rem;
    }
    
    .button, .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
    }
}

@media (max-width: 768px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
}
