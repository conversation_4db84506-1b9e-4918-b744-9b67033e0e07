/**
 * Logs Interface CSS - Refactored
 * 
 * Clean, functional styling for the simplified logs interface
 * Prioritizes usability over visual complexity
 */

/* Container */
.maintenance-logs-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.logs-header h2 {
    margin: 0;
    color: #333;
    font-size: 24px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* Sections */
.logs-section {
    margin-bottom: 40px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
}

.logs-section h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 18px;
}

.logs-section p {
    margin: 0 0 20px 0;
    color: #666;
}

/* Tables */
.logs-table {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.table tr:hover {
    background-color: #f8f9fa;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 3px 8px;
    font-size: 12px;
    font-weight: 600;
    border-radius: 3px;
    text-transform: uppercase;
}

.badge-success {
    background-color: #d4edda;
    color: #155724;
}

.badge-warning {
    background-color: #fff3cd;
    color: #856404;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

/* Buttons */
.button {
    display: inline-block;
    padding: 8px 16px;
    margin: 0 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    color: #333;
    text-decoration: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.button:hover {
    background: #f8f9fa;
    text-decoration: none;
}

.button-primary {
    background: #007bff;
    color: #fff;
    border-color: #007bff;
}

.button-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
    color: #fff;
}

.button-secondary {
    background: #6c757d;
    color: #fff;
    border-color: #6c757d;
}

.button-secondary:hover {
    background: #545b62;
    border-color: #545b62;
    color: #fff;
}

.button-danger {
    background: #dc3545;
    color: #fff;
    border-color: #dc3545;
}

.button-danger:hover {
    background: #c82333;
    border-color: #c82333;
    color: #fff;
}

.button-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Log view specific styles */
.log-info-section,
.log-content-section {
    margin-bottom: 30px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
}

.log-info-section h3,
.log-content-section h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.info-item {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

.info-item strong {
    color: #333;
}

/* Content display */
.content-container {
    margin: 20px 0;
}

.log-content {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    overflow-x: auto;
    max-height: 600px;
    overflow-y: auto;
}

.log-content code {
    background: none;
    padding: 0;
    color: #333;
}

.content-actions {
    margin-top: 15px;
    text-align: right;
}

/* Notices */
.notice {
    padding: 12px 16px;
    margin: 15px 0;
    border-radius: 4px;
    border-left: 4px solid;
}

.notice-super-admin {
    background: #e7f3ff;
    border-left-color: #007bff;
    color: #004085;
}

.notice-warning {
    background: #fff3cd;
    border-left-color: #ffc107;
    color: #856404;
}

.notice-error {
    background: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}

/* Responsive design */
@media (max-width: 768px) {
    .logs-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-start;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .table {
        font-size: 14px;
    }
    
    .table th,
    .table td {
        padding: 8px;
    }
}

/* Print styles */
@media print {
    .header-actions,
    .content-actions,
    .button {
        display: none !important;
    }
    
    .logs-section,
    .log-info-section,
    .log-content-section {
        border: none;
        box-shadow: none;
    }
}
