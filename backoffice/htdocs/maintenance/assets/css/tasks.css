/**
 * Tasks Management Styles
 *
 * Minimal styles that complement the admin skin for tasks management
 */

/* Tasks container */
.tasks-container {
    margin-top: 20px;
}

.tasks-header {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

/* Search Section */
.search-section {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #dee2e6;
}

.search-basic {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
}

.search-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.search-input:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.25);
}

.advanced-search {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
}

.search-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 500;
    color: #495057;
    font-size: 0.875rem;
}

.filter-select,
.filter-input {
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.25);
}

.search-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.results-counter {
    margin-top: 10px;
    padding: 8px 12px;
    background-color: #e9ecef;
    border-radius: 4px;
    font-size: 0.875rem;
    color: #495057;
    text-align: center;
}

.tasks-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Tasks grid */
.tasks-grid {
    display: grid;
    gap: 20px;
}

.task-category {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
}

.category-title {
    margin: 0 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #007cba;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Task subcategory */
.task-subcategory {
    margin-bottom: 20px;
}

.task-subcategory:last-child {
    margin-bottom: 0;
}

.subcategory-title {
    margin: 0 0 10px 0;
    padding: 8px 12px;
    background-color: #e9ecef;
    border-left: 4px solid #007cba;
    color: #495057;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 0 4px 4px 0;
}

/* Tasks list */
.tasks-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 15px;
}

.task-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    transition: all 0.2s ease;
}

.task-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #007cba;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.task-info {
    flex: 1;
    min-width: 0;
}

.task-name {
    margin: 0 0 5px 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

.task-id {
    display: inline-block;
    background-color: #007cba;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-right: 8px;
    font-family: monospace;
}

.task-description {
    margin: 0;
    font-size: 0.875rem;
    color: #6c757d;
    line-height: 1.4;
}

.task-status {
    margin-left: 10px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Task details */
.task-details {
    margin: 10px 0;
    padding: 10px 0;
    border-top: 1px solid #e9ecef;
}

/* Task statistics */
.task-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 8px;
    margin-bottom: 10px;
    padding: 8px;
    background-color: #f1f3f4;
    border-radius: 4px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    font-size: 0.8rem;
}

.stat-label {
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 2px;
}

.stat-value {
    font-weight: 600;
    color: #2c3e50;
    font-family: monospace;
    font-size: 0.9rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 0.875rem;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-label {
    font-weight: 500;
    color: #6c757d;
}

.detail-value {
    color: #2c3e50;
    font-family: monospace;
}

/* Task actions */
.task-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
}

/* Button styles that complement admin skin */
.button-sm {
    padding: 4px 8px;
    font-size: 0.875rem;
}

.button-success {
    background-color: #28a745;
    border-color: #28a745;
    color: #fff;
}

.button-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.button-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff;
}

.button-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.button-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.button-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
}

.button-info {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: #fff;
}

.button-info:hover {
    background-color: #138496;
    border-color: #117a8b;
}

.button-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}

.button-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

/* No tasks message */
.no-tasks,
.no-tasks-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-style: italic;
}

/* Loading indicator */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    border-radius: 4px;
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 10px;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notice styles that complement admin skin */
.notice {
    padding: 12px 16px;
    margin-bottom: 16px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.notice-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.notice-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.notice-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.notice:before {
    content: none;
}

/* Icons for search */
.icon-filter:before { content: '🔍 '; }
.icon-clear:before { content: '✖️ '; }

/* Responsive design */
@media (max-width: 768px) {
    .tasks-actions {
        flex-direction: column;
    }

    .tasks-list {
        grid-template-columns: 1fr;
    }

    .task-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .task-actions {
        justify-content: center;
    }

    .detail-item {
        flex-direction: column;
        gap: 2px;
    }

    .search-basic {
        flex-direction: column;
        align-items: stretch;
    }

    .search-filters {
        grid-template-columns: 1fr;
    }

    .search-actions {
        justify-content: stretch;
    }

    .search-actions .button {
        flex: 1;
    }
}

@media (max-width: 576px) {
    .tasks-container {
        margin-top: 10px;
    }
    
    .task-category {
        padding: 15px;
    }
    
    .task-card {
        padding: 12px;
    }
    
    .tasks-header {
        padding: 12px;
    }
}
