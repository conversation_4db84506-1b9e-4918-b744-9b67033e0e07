/**
 * Update Module Styles
 *
 * Styles for the manual update interface that complement the admin skin
 */

/* Update module styles */
.update-section {
    margin-bottom: 20px;
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007cba;
}

.section-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
}

.section-content {
    margin-top: 15px;
}

.loading-message {
    text-align: center;
    padding: 20px;
    color: #666;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.status-item {
    text-align: center;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: #f8f9fa;
}

.status-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 8px;
}

.status-value {
    font-size: 1.1rem;
    font-weight: bold;
}

.status-primary { color: #007cba; }
.status-success { color: #28a745; }
.status-warning { color: #ffc107; }
.status-error { color: #dc3545; }

.update-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.update-card {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
    transition: all 0.2s ease;
}

.update-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #007cba;
}

.card-header h3 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.card-content ul {
    margin: 15px 0;
    padding-left: 20px;
}

.card-content li {
    margin-bottom: 5px;
}

.button-large {
    width: 100%;
    padding: 12px 20px;
    font-size: 1rem;
}

.scripts-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.scripts-section h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
}

.scripts-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.script-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.script-type {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: bold;
    color: white;
}

.script-type-sql {
    background-color: #007cba;
}

.script-type-php {
    background-color: #28a745;
}

.script-name {
    flex: 1;
    font-weight: 500;
}

.script-version {
    font-size: 0.9rem;
    color: #666;
}

.progress-bar {
    width: 100%;
    height: 30px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background-color: #007cba;
    background-image: linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

@keyframes progress-bar-stripes {
    0% { background-position: 1rem 0; }
    100% { background-position: 0 0; }
}

.icon-refresh:before { content: '🔄 '; }
.icon-download:before { content: '⬇️ '; }

/* Button styles to match admin theme */
.button-small {
    padding: 4px 8px;
    font-size: 0.8rem;
}

/* Notice styles to match admin theme */
.notice {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    border-left: 4px solid;
}

.notice-warning {
    background-color: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.notice-success {
    background-color: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.notice-error {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.notice h4 {
    margin-top: 0;
    margin-bottom: 10px;
}

.notice:before {
    content: none;
}

/* Responsive design */
@media (max-width: 768px) {
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .update-actions {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .script-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

/* Loading states */
.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.button:disabled:hover {
    background-color: inherit;
    border-color: inherit;
}

/* Error message styling */
.error-details {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
    font-family: monospace;
    font-size: 0.9rem;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
}

/* Success message styling */
.success-details {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
}

/* Update status indicators */
.update-status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: bold;
}

.update-status.running {
    background-color: #cce5ff;
    color: #0066cc;
}

.update-status.success {
    background-color: #d4edda;
    color: #155724;
}

.update-status.error {
    background-color: #f8d7da;
    color: #721c24;
}

/* Repository information grid */
.repository-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.repository-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
}

.repository-item h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.repo-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.repo-field {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
}

.repo-field strong {
    min-width: 120px;
    color: #495057;
}

.repo-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: bold;
}

.repo-status.status-success {
    background-color: #d4edda;
    color: #155724;
}

.repo-status.status-error {
    background-color: #f8d7da;
    color: #721c24;
}

.repo-status.status-warning {
    background-color: #fff3cd;
    color: #856404;
}

.repo-status.status-neutral {
    background-color: #e9ecef;
    color: #495057;
}

/* Credentials section */
.credentials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.credential-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.credential-group label {
    font-weight: bold;
    color: #495057;
    font-size: 0.9rem;
}

.credential-group input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
}

.credential-group input:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.25);
}

.credentials-note {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 10px;
    font-size: 0.9rem;
    color: #0066cc;
    margin: 0;
}

/* Workflow steps */
.workflow-steps {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.workflow-step {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
    transition: all 0.2s ease;
}

.workflow-step:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.workflow-step.step-optional {
    opacity: 0.7;
    border-style: dashed;
}

.step-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #007cba;
    color: white;
    border-radius: 50%;
    font-weight: bold;
    font-size: 1.1rem;
}

.step-info {
    flex: 1;
}

.step-info h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-size: 1rem;
}

.step-info p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.step-status {
    min-width: 100px;
    text-align: right;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-badge.status-pending {
    background-color: #e9ecef;
    color: #495057;
}

.status-badge.status-running {
    background-color: #cce5ff;
    color: #0066cc;
}

.status-badge.status-success {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.status-error {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.status-skipped {
    background-color: #fff3cd;
    color: #856404;
}

.step-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 15px;
}

.step-result {
    margin-top: 15px;
}

/* Icons */
.icon-database:before { content: '🗄️ '; }
.icon-code:before { content: '💻 '; }
.icon-tag:before { content: '🏷️ '; }
.icon-retry:before { content: '🔄 '; }

/* Responsive design for workflow */
@media (max-width: 768px) {
    .repository-grid {
        grid-template-columns: 1fr;
    }

    .credentials-grid {
        grid-template-columns: 1fr;
    }

    .step-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .step-status {
        text-align: left;
        min-width: auto;
    }

    .step-actions {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Enhanced Script Display Styles */
.step-scripts {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin: 10px 0;
    padding: 15px;
}

.step-scripts h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.scripts-table {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    overflow: hidden;
}

.scripts-header {
    background: #e9ecef;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 80px 120px;
    gap: 1px;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    color: #495057;
}

.scripts-header > span {
    padding: 8px 12px;
    background: #e9ecef;
}

.script-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 80px 120px;
    gap: 1px;
    background: #fff;
    border-bottom: 1px solid #dee2e6;
}

.script-row:last-child {
    border-bottom: none;
}

.script-row > span {
    padding: 8px 12px;
    background: #fff;
    font-size: 13px;
    display: flex;
    align-items: center;
}

.script-col-file {
    font-family: monospace;
    font-weight: 500;
}

.script-col-version {
    color: #6c757d;
    font-weight: 500;
}

.script-col-order {
    text-align: center;
    font-weight: 600;
    color: #495057;
}

.script-col-action {
    text-align: center;
}

.script-col-action .button-small {
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1.2;
}

/* Script Status Styles */
.script-status-newer {
    background-color: #d4edda !important;
}

.script-status-newer .script-col-status {
    color: #155724;
    font-weight: 600;
}

.script-status-current {
    background-color: #fff3cd !important;
}

.script-status-current .script-col-status {
    color: #856404;
    font-weight: 600;
}

.script-status-older {
    background-color: #f8f9fa !important;
}

.script-status-older .script-col-status {
    color: #6c757d;
    font-style: italic;
}

.no-scripts {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    margin: 10px 0;
}

/* Executed Scripts Display */
.executed-scripts {
    background: #e8f5e8;
    border: 1px solid #c3e6c3;
    border-radius: 4px;
    padding: 12px;
    margin: 10px 0;
}

.executed-scripts h5 {
    margin: 0 0 8px 0;
    color: #155724;
    font-size: 14px;
}

.script-execution-list {
    margin: 0;
    padding-left: 20px;
    list-style: none;
}

.script-execution-list li {
    margin: 4px 0;
    font-family: monospace;
    font-size: 13px;
    color: #155724;
}

/* Log Download Section */
.log-download-section {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 4px;
    padding: 12px;
    margin: 10px 0;
    text-align: center;
}

.log-download-section.error-log {
    background: #fff3e0;
    border-color: #ffcc02;
}

.log-download-section p {
    margin: 0 0 8px 0;
    font-weight: 600;
    color: #1565c0;
}

.log-download-section.error-log p {
    color: #ef6c00;
}

/* Execution Results Display */
.execution-results {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 12px;
    margin: 10px 0;
}

.execution-results h5 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 14px;
}

.execution-results-table {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.execution-result-row {
    display: grid;
    grid-template-columns: 30px 2fr 3fr 80px;
    gap: 8px;
    padding: 6px 8px;
    border-radius: 3px;
    font-size: 13px;
    align-items: center;
}

.execution-result-row.success {
    background-color: #d4edda;
    border-left: 3px solid #28a745;
}

.execution-result-row.error {
    background-color: #f8d7da;
    border-left: 3px solid #dc3545;
}

.result-icon {
    text-align: center;
}

.result-script {
    font-family: monospace;
    font-weight: 500;
    color: #495057;
}

.result-message {
    color: #6c757d;
    font-size: 12px;
}

.result-time {
    text-align: right;
    font-size: 11px;
    color: #6c757d;
    font-weight: 500;
}

/* Enhanced Button Styles */
.button-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.button-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
}

.button-warning:disabled {
    background-color: #ffc107;
    border-color: #ffc107;
    opacity: 0.6;
}

.button-success {
    background-color: #28a745;
    border-color: #28a745;
    color: #fff;
}

.button-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff;
}

.button-small {
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1.2;
    border-radius: 3px;
}

/* Individual Script Notifications */
#individual-script-notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
}

.script-notification {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-bottom: 10px;
    animation: slideInRight 0.3s ease-out;
}

.individual-script-result {
    padding: 12px;
}

.individual-script-result.success {
    border-left: 4px solid #28a745;
    background-color: #d4edda;
}

.individual-script-result.error {
    border-left: 4px solid #dc3545;
    background-color: #f8d7da;
}

.individual-script-result h5 {
    margin: 0 0 8px 0;
    font-size: 14px;
}

.individual-script-result p {
    margin: 0 0 8px 0;
    font-size: 13px;
}

.script-output {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    padding: 8px;
    margin: 8px 0;
}

.script-output h6 {
    margin: 0 0 4px 0;
    font-size: 12px;
    color: #6c757d;
}

.script-output pre {
    margin: 0;
    font-size: 11px;
    max-height: 100px;
    overflow-y: auto;
}

/* Progressive Workflow Animation */
.workflow-step {
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.workflow-step[style*="display: none"] {
    opacity: 0;
    transform: translateY(-10px);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Icons for enhanced interface */
.icon-list:before { content: '📋 '; }
.icon-retry:before { content: '🔄 '; }
.icon-skip:before { content: '⏭️ '; }

/* Responsive design for script tables */
@media (max-width: 768px) {
    .scripts-header,
    .script-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .scripts-header > span,
    .script-row > span {
        padding: 6px 12px;
        border-bottom: 1px solid #dee2e6;
    }

    .scripts-header > span:before,
    .script-row > span:before {
        content: attr(data-label) ': ';
        font-weight: bold;
        display: inline-block;
        min-width: 80px;
    }

    .execution-result-row {
        grid-template-columns: 1fr;
        gap: 4px;
        text-align: left;
    }

    .result-time {
        text-align: left;
    }

    #individual-script-notifications {
        position: fixed;
        top: 10px;
        left: 10px;
        right: 10px;
        max-width: none;
    }

    .script-notification {
        margin-bottom: 5px;
    }
}

/* Detailed execution results styles */
.execution-results {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.execution-results h5 {
    margin: 0 0 15px 0;
    color: #0066cc;
    font-size: 1rem;
}

.script-execution-details {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.script-execution-details:last-child {
    margin-bottom: 0;
}

.script-execution-details h6 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 0.95rem;
    font-weight: 600;
}

/* SQL execution details */
.sql-execution-details {
    margin-top: 10px;
}

.sql-statement-result {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin-bottom: 10px;
    overflow: hidden;
}

.sql-statement-result:last-child {
    margin-bottom: 0;
}

.statement-header {
    background: #e9ecef;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 0.85rem;
}

.statement-type {
    background: #007cba;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 0.8rem;
}

.statement-time {
    color: #666;
    font-weight: 500;
}

.statement-rows {
    color: #28a745;
    font-weight: 500;
}

.statement-sql {
    padding: 10px 12px;
}

.statement-sql code {
    background: none;
    padding: 0;
    font-size: 0.85rem;
    color: #495057;
    white-space: pre-wrap;
    word-break: break-all;
}

/* PHP execution details */
.php-execution-details {
    margin-top: 10px;
}

.php-output, .php-result {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
}

.php-output:last-child, .php-result:last-child {
    margin-bottom: 0;
}

.php-output h7, .php-result h7 {
    display: block;
    margin: 0 0 8px 0;
    font-size: 0.85rem;
    color: #495057;
    font-weight: 600;
}

.php-output pre {
    margin: 0;
    font-size: 0.8rem;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    padding: 8px;
}

.php-result code {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    padding: 8px;
    display: block;
    font-size: 0.8rem;
    color: #495057;
}

.php-timing {
    display: flex;
    gap: 10px;
    font-size: 0.85rem;
    color: #666;
    margin-top: 8px;
}

.php-memory {
    font-size: 0.85rem;
    color: #666;
    margin-top: 8px;
}

/* Individual script notifications enhanced */
#individual-script-notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 500px;
}

.individual-script-result {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.individual-script-result.success {
    border-left: 4px solid #28a745;
}

.individual-script-result.error {
    border-left: 4px solid #dc3545;
}

.individual-script-result h5 {
    margin: 0 0 10px 0;
    font-size: 1rem;
}

.individual-script-result p {
    margin: 0 0 10px 0;
    font-size: 0.9rem;
}

.script-output {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
}

.script-output h6 {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    color: #495057;
}

.script-output pre {
    margin: 0;
    font-size: 0.8rem;
    white-space: pre-wrap;
    max-height: 150px;
    overflow-y: auto;
}
