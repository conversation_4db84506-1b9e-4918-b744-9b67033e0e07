/**
 * Logs Interface JavaScript
 * 
 * Handles interactive functionality for the log viewer
 * 
 * @package Maintenance\Assets
 * <AUTHOR> Team
 */

// Global variables
let currentPage = 1;
let currentSearch = '';
let currentSortBy = 'date';
let currentSortOrder = 'desc';
let selectedFiles = [];
let isLoading = false;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeLogsInterface();
    loadLogsStatistics();
});

/**
 * Initialize the logs interface
 */
function initializeLogsInterface() {
    // Search functionality
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                currentSearch = searchInput.value;
                currentPage = 1;
                loadLogs();
            }, 500);
        });
    }

    // Sort controls
    const sortBy = document.getElementById('sort-by');
    const sortOrder = document.getElementById('sort-order');
    
    if (sortBy) {
        sortBy.addEventListener('change', function() {
            currentSortBy = this.value;
            currentPage = 1;
            loadLogs();
        });
    }
    
    if (sortOrder) {
        sortOrder.addEventListener('change', function() {
            currentSortOrder = this.value;
            currentPage = 1;
            loadLogs();
        });
    }

    // Clear search button
    const clearSearch = document.getElementById('clear-search');
    if (clearSearch) {
        clearSearch.addEventListener('click', function() {
            searchInput.value = '';
            currentSearch = '';
            currentPage = 1;
            loadLogs();
        });
    }

    // Refresh button
    const refreshButton = document.getElementById('refresh-logs');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            loadLogs();
            loadLogsStatistics();
        });
    }

    // Select all functionality
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.log-checkbox');
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = this.checked;
            }.bind(this));
            updateSelectedFiles();
        });
    }

    // Individual checkbox handling
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('log-checkbox')) {
            updateSelectedFiles();
        }
    });

    // Select all button
    const selectAllButton = document.getElementById('select-all');
    if (selectAllButton) {
        selectAllButton.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('.log-checkbox');
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = true;
            });
            updateSelectedFiles();
        });
    }

    // Clear selection button
    const clearSelection = document.getElementById('clear-selection');
    if (clearSelection) {
        clearSelection.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('.log-checkbox');
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = false;
            });
            updateSelectedFiles();
        });
    }

    // Delete selected button
    const deleteSelected = document.getElementById('delete-selected');
    if (deleteSelected) {
        deleteSelected.addEventListener('click', function() {
            if (selectedFiles.length > 0) {
                showDeleteModal(selectedFiles, true);
            }
        });
    }

    // Pagination buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('pagination-btn')) {
            const page = parseInt(e.target.getAttribute('data-page'));
            if (page && page !== currentPage) {
                currentPage = page;
                loadLogs();
            }
        }
    });

    // Delete single file buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('delete-single') || e.target.closest('.delete-single')) {
            const button = e.target.classList.contains('delete-single') ? e.target : e.target.closest('.delete-single');
            const filename = button.getAttribute('data-filename');
            if (filename) {
                showDeleteModal([filename], false);
            }
        }
    });

    // Modal functionality
    const modal = document.getElementById('delete-modal');
    if (modal) {
        const closeButton = modal.querySelector('.modal-close');
        const cancelButton = document.getElementById('cancel-delete');
        const confirmButton = document.getElementById('confirm-delete');

        if (closeButton) {
            closeButton.addEventListener('click', function() {
                hideDeleteModal();
            });
        }

        if (cancelButton) {
            cancelButton.addEventListener('click', function() {
                hideDeleteModal();
            });
        }

        if (confirmButton) {
            confirmButton.addEventListener('click', function() {
                const filesToDelete = modal.getAttribute('data-files');
                const isMultiple = modal.getAttribute('data-multiple') === 'true';
                
                if (filesToDelete) {
                    const files = JSON.parse(filesToDelete);
                    if (isMultiple) {
                        deleteMultipleFiles(files);
                    } else {
                        deleteSingleFile(files[0]);
                    }
                }
            });
        }

        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                hideDeleteModal();
            }
        });
    }
}

/**
 * Load logs with current filters and pagination
 */
function loadLogs() {
    if (isLoading) return;
    
    isLoading = true;
    showLoadingIndicator();

    const params = new URLSearchParams({
        page: currentPage,
        search: currentSearch,
        sort_by: currentSortBy,
        sort_order: currentSortOrder
    });

    fetch('/maintenance/?controller=logs&action=search&' + params.toString())
        .then(function(response) {
            return response.json();
        })
        .then(function(data) {
            if (data.success) {
                updateLogsTable(data.data.logs);
                updatePagination(data.data.pagination);
            } else {
                showNotification('Erreur lors du chargement des logs: ' + data.error, 'error');
            }
        })
        .catch(function(error) {
            console.error('Error loading logs:', error);
            showNotification('Erreur de connexion lors du chargement des logs', 'error');
        })
        .finally(function() {
            isLoading = false;
            hideLoadingIndicator();
        });
}

/**
 * Load logs statistics
 */
function loadLogsStatistics() {
    fetch('/maintenance/?controller=logs&action=getStats')
        .then(function(response) {
            return response.json();
        })
        .then(function(data) {
            if (data.success) {
                updateStatistics(data.data);
            }
        })
        .catch(function(error) {
            console.error('Error loading statistics:', error);
        });
}

/**
 * Update statistics display
 */
function updateStatistics(stats) {
    const totalFiles = document.getElementById('total-files');
    const totalSize = document.getElementById('total-size');
    const newestFile = document.getElementById('newest-file');
    const oldestFile = document.getElementById('oldest-file');

    if (totalFiles) totalFiles.textContent = stats.total_files;
    if (totalSize) totalSize.textContent = stats.total_size_formatted;
    if (newestFile) newestFile.textContent = stats.newest_file || '-';
    if (oldestFile) oldestFile.textContent = stats.oldest_file || '-';
}

/**
 * Update logs table with new data
 */
function updateLogsTable(logs) {
    const tableBody = document.getElementById('logs-table-body');
    const logsContainer = document.getElementById('logs-container');
    
    if (!tableBody || !logsContainer) return;

    if (logs.length === 0) {
        logsContainer.innerHTML = '<div class="empty-state"><div class="empty-icon">📄</div><h4>Aucun fichier de log trouvé</h4><p>Aucun fichier ne correspond aux critères de recherche.</p></div>';
        return;
    }

    let html = '';
    logs.forEach(function(log) {
        html += '<tr class="log-row" data-filename="' + escapeHtml(log.filename) + '">';
        html += '<td class="select-column"><input type="checkbox" class="log-checkbox" value="' + escapeHtml(log.filename) + '"></td>';
        html += '<td class="filename-column">';
        html += '<div class="filename-info">';
        html += '<span class="filename">' + escapeHtml(log.filename) + '</span>';
        if (!log.is_valid_json) {
            html += '<span class="invalid-badge" title="JSON invalide">⚠️</span>';
        }
        html += '</div></td>';
        html += '<td class="size-column">' + escapeHtml(log.size_formatted) + '</td>';
        html += '<td class="date-column">';
        html += '<div class="date-info">';
        html += '<span class="date-absolute">' + escapeHtml(log.modified_date) + '</span>';
        html += '<span class="date-relative">' + escapeHtml(log.modified_relative) + '</span>';
        html += '</div></td>';
        html += '<td class="records-column">' + log.record_count + '</td>';
        html += '<td class="preview-column">';
        html += '<span class="preview-text" title="' + escapeHtml(log.preview) + '">';
        html += escapeHtml(log.preview.substring(0, 50)) + (log.preview.length > 50 ? '...' : '');
        html += '</span></td>';
        html += '<td class="actions-column">';
        html += '<div class="action-buttons">';
        html += '<a href="/maintenance/?controller=logs&action=view&file=' + encodeURIComponent(log.filename) + '" class="button button-sm button-primary" title="Visualiser"><i class="icon-view"></i></a>';
        html += '<a href="/maintenance/?controller=logs&action=download&file=' + encodeURIComponent(log.filename) + '" class="button button-sm button-secondary" title="Télécharger"><i class="icon-download"></i></a>';
        html += '<button class="button button-sm button-danger delete-single" data-filename="' + escapeHtml(log.filename) + '" title="Supprimer"><i class="icon-delete"></i></button>';
        html += '</div></td>';
        html += '</tr>';
    });

    tableBody.innerHTML = html;
    
    // Reset selection
    selectedFiles = [];
    updateBulkActionsVisibility();
}

/**
 * Update pagination controls
 */
function updatePagination(pagination) {
    const paginationContainer = document.querySelector('.pagination-container');
    
    if (!paginationContainer || pagination.total_pages <= 1) {
        if (paginationContainer) {
            paginationContainer.style.display = 'none';
        }
        return;
    }

    paginationContainer.style.display = 'flex';
    
    const paginationInfo = paginationContainer.querySelector('.pagination-info');
    const paginationControls = paginationContainer.querySelector('.pagination-controls');
    
    if (paginationInfo) {
        const start = ((pagination.current_page - 1) * pagination.page_size) + 1;
        const end = Math.min(pagination.current_page * pagination.page_size, pagination.total_items);
        paginationInfo.textContent = 'Affichage de ' + start + ' à ' + end + ' sur ' + pagination.total_items + ' fichiers';
    }
    
    if (paginationControls) {
        let html = '';
        
        if (pagination.has_previous) {
            html += '<button class="button button-sm button-secondary pagination-btn" data-page="' + (pagination.current_page - 1) + '">Précédent</button>';
        }
        
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === pagination.current_page;
            html += '<button class="button button-sm ' + (isActive ? 'button-primary' : 'button-secondary') + ' pagination-btn" data-page="' + i + '">' + i + '</button>';
        }
        
        if (pagination.has_next) {
            html += '<button class="button button-sm button-secondary pagination-btn" data-page="' + (pagination.current_page + 1) + '">Suivant</button>';
        }
        
        paginationControls.innerHTML = html;
    }
}

/**
 * Update selected files array and UI
 */
function updateSelectedFiles() {
    const checkboxes = document.querySelectorAll('.log-checkbox:checked');
    selectedFiles = Array.from(checkboxes).map(function(checkbox) {
        return checkbox.value;
    });
    
    updateBulkActionsVisibility();
    updateSelectAllCheckbox();
}

/**
 * Update bulk actions visibility
 */
function updateBulkActionsVisibility() {
    const bulkActionsSection = document.getElementById('bulk-actions-section');
    const selectedCount = document.getElementById('selected-count');
    
    if (bulkActionsSection) {
        if (selectedFiles.length > 0) {
            bulkActionsSection.style.display = 'block';
            if (selectedCount) {
                selectedCount.textContent = selectedFiles.length;
            }
        } else {
            bulkActionsSection.style.display = 'none';
        }
    }
}

/**
 * Update select all checkbox state
 */
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const allCheckboxes = document.querySelectorAll('.log-checkbox');
    
    if (selectAllCheckbox && allCheckboxes.length > 0) {
        const checkedCount = document.querySelectorAll('.log-checkbox:checked').length;
        selectAllCheckbox.checked = checkedCount === allCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < allCheckboxes.length;
    }
}

/**
 * Show delete confirmation modal
 */
function showDeleteModal(files, isMultiple) {
    const modal = document.getElementById('delete-modal');
    const message = document.getElementById('delete-message');
    
    if (!modal || !message) return;
    
    modal.setAttribute('data-files', JSON.stringify(files));
    modal.setAttribute('data-multiple', isMultiple.toString());
    
    if (isMultiple) {
        message.textContent = 'Êtes-vous sûr de vouloir supprimer ' + files.length + ' fichier(s) sélectionné(s) ?';
    } else {
        message.textContent = 'Êtes-vous sûr de vouloir supprimer le fichier "' + files[0] + '" ?';
    }
    
    modal.style.display = 'block';
}

/**
 * Hide delete confirmation modal
 */
function hideDeleteModal() {
    const modal = document.getElementById('delete-modal');
    if (modal) {
        modal.style.display = 'none';
        modal.removeAttribute('data-files');
        modal.removeAttribute('data-multiple');
    }
}

/**
 * Delete single file
 */
function deleteSingleFile(filename) {
    const formData = new FormData();
    formData.append('file', filename);
    formData.append('csrf_token', getCsrfToken());
    
    fetch('/maintenance/?controller=logs&action=delete', {
        method: 'POST',
        body: formData
    })
    .then(function(response) {
        return response.json();
    })
    .then(function(data) {
        if (data.success) {
            showNotification('Fichier supprimé avec succès', 'success');
            loadLogs();
            loadLogsStatistics();
        } else {
            showNotification('Erreur lors de la suppression: ' + data.error, 'error');
        }
    })
    .catch(function(error) {
        console.error('Error deleting file:', error);
        showNotification('Erreur de connexion', 'error');
    })
    .finally(function() {
        hideDeleteModal();
    });
}

/**
 * Delete multiple files
 */
function deleteMultipleFiles(files) {
    const formData = new FormData();
    files.forEach(function(file) {
        formData.append('files[]', file);
    });
    formData.append('csrf_token', getCsrfToken());
    
    fetch('/maintenance/?controller=logs&action=deleteMultiple', {
        method: 'POST',
        body: formData
    })
    .then(function(response) {
        return response.json();
    })
    .then(function(data) {
        if (data.success) {
            const results = data.data;
            let message = results.success_count + ' fichier(s) supprimé(s)';
            if (results.failed_count > 0) {
                message += ', ' + results.failed_count + ' échec(s)';
            }
            showNotification(message, results.failed_count > 0 ? 'warning' : 'success');
            loadLogs();
            loadLogsStatistics();
        } else {
            showNotification('Erreur lors de la suppression: ' + data.error, 'error');
        }
    })
    .catch(function(error) {
        console.error('Error deleting files:', error);
        showNotification('Erreur de connexion', 'error');
    })
    .finally(function() {
        hideDeleteModal();
    });
}

/**
 * Confirm clear all logs
 */
function confirmClearAllLogs() {
    if (confirm('Êtes-vous sûr de vouloir supprimer TOUS les fichiers de logs ? Cette action est irréversible.')) {
        clearAllLogs();
    }
}

/**
 * Clear all logs
 */
function clearAllLogs() {
    const formData = new FormData();
    formData.append('confirm', 'true');
    formData.append('csrf_token', getCsrfToken());
    
    fetch('/maintenance/?controller=logs&action=clearAll', {
        method: 'POST',
        body: formData
    })
    .then(function(response) {
        return response.json();
    })
    .then(function(data) {
        if (data.success) {
            const result = data.data;
            showNotification('Tous les logs ont été supprimés (' + result.deleted_count + ' fichiers)', 'success');
            loadLogs();
            loadLogsStatistics();
        } else {
            showNotification('Erreur lors de la suppression: ' + data.error, 'error');
        }
    })
    .catch(function(error) {
        console.error('Error clearing logs:', error);
        showNotification('Erreur de connexion', 'error');
    });
}

/**
 * Show loading indicator
 */
function showLoadingIndicator() {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.style.display = 'flex';
    }
}

/**
 * Hide loading indicator
 */
function hideLoadingIndicator() {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.style.display = 'none';
    }
}

/**
 * Show notification
 */
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = 'notification notification-' + type;
    notification.textContent = message;
    notification.style.cssText = 'position: fixed; top: 20px; right: 20px; padding: 10px 20px; border-radius: 4px; z-index: 9999; color: white; max-width: 300px;';
    
    switch (type) {
        case 'success':
            notification.style.backgroundColor = '#28a745';
            break;
        case 'warning':
            notification.style.backgroundColor = '#ffc107';
            notification.style.color = '#212529';
            break;
        case 'error':
        default:
            notification.style.backgroundColor = '#dc3545';
            break;
    }
    
    document.body.appendChild(notification);
    
    setTimeout(function() {
        if (notification.parentNode) {
            document.body.removeChild(notification);
        }
    }, 5000);
}

/**
 * Get CSRF token from page
 */
function getCsrfToken() {
    // Try to get from a meta tag or hidden input
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
        return metaToken.getAttribute('content');
    }
    
    const hiddenToken = document.querySelector('input[name="csrf_token"]');
    if (hiddenToken) {
        return hiddenToken.value;
    }
    
    // Fallback - this should be set by the PHP template
    return window.csrfToken || '';
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
