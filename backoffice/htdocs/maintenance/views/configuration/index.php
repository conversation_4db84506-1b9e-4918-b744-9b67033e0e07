<?php
/**
 * Configuration Variables View - Simple Functional Interface
 *
 * Display and manage configuration variables with focus on usability
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

// Include view helpers
require_once('utils/ViewHelpers.php');

// Check if this is an AJAX request
$isAjax = Maintenance_Utils_Authentication::isAjaxRequest();

if (!$isAjax) {
    // Set page title and include admin header
    define('PAGE_TITLE', $pageTitle);
    require_once('../../include/admin/skin/header.inc.php');
    ?>
    <link rel="stylesheet" href="/maintenance/assets/css/configuration.css">
    <?php
}
?>

<div>
    <p>
        <button class="button button-primary" onclick="window.location.href='/maintenance/'">Retour au dashboard</button>
    </p>
    
    <h2>Variables de configuration</h2>
    <p>Gestion des variables de configuration (Site / Yuto / Synchronisation).</p>
    
    <form id="form-config-cfg" action="" method="get">
        <div>
            <select name="tnt_id" id="tnt_id" hidden>
                <option value="<?php echo $tenantId; ?>" selected></option>
            </select>
        </div>

        <?php if ($tenantId) { ?>
            <div>
                <select name="wst_id" id="wst_id">
                    <option value="">Choisissez un site ou un logiciel</option>
                    <?php
                    foreach ($websites as $wst) {
                        $selected = '';
                        if ($websiteId == $wst['id']) {
                            $selected = 'selected="selected"';
                        }
                        ?>
                        <option value="<?php echo $wst['id']; ?>" <?php echo $selected; ?>>
                            <?php echo htmlspecialchars($wst['name']); ?>
                        </option>
                        <?php
                    }

                    if (isset($syncInfo['enabled']) && $syncInfo['enabled']) {
                        $selected = '';
                        if ($websiteId === 'sync') {
                            $selected = 'selected="selected"';
                        }
                        ?>
                        <option value="sync" <?php echo $selected; ?>>Logiciel de synchronisation</option>
                        <?php
                    }

                    if (isset($syncInfo['has_yuto']) && $syncInfo['has_yuto']) {
                        $selected = '';
                        if ($websiteId === 'yuto') {
                            $selected = 'selected="selected"';
                        }
                        ?>
                        <option value="yuto" <?php echo $selected; ?>>Application Yuto</option>
                        <?php
                    }
                    ?>
                </select>
            </div>
        <?php } ?>
        
        <?php if ($tenantId && $websiteId) { ?>
            <div>
                <label for="only_override"><strong>Ne voir que les variables surchargées</strong> :</label>
                <input type="checkbox" name="only_override" id="only_override" <?php echo $onlyOverride ? 'checked="checked"' : ''; ?> />
            </div>
        <?php } ?>
    </form>

    <form id="form-list-override" action="/maintenance/?controller=configuration&action=save<?php echo htmlspecialchars($paramEdit); ?>" method="post">
        <?php
        if (isset($error)) {
            echo '<div class="error">' . nl2br(htmlspecialchars($error)) . '</div>';
        } elseif ($successMessage) {
            echo '<div class="success">' . htmlspecialchars($successMessage) . '</div>';
        }
        
        if ($tenantId && $websiteId) {
            // Sort variables by name
            if (function_exists('array_msort')) {
                $variables = array_msort($variables, array('name' => SORT_ASC));
            } else {
                // Simple fallback sorting
                uasort($variables, function($a, $b) {
                    return strcmp($a['name'], $b['name']);
                });
            }
            ?>
            <table id="list-cfg-override" class="checklist" cellspacing="0" cellpadding="0">
                <caption>Variables de configuration</caption>
                <col width="300" /><col width="400" /><col width="16" />
                <thead>
                    <tr>
                        <th>Variable</th>
                        <th colspan="2" align="right">
                            <label for="filter">Filtre (code) : 
                                <input type="text" name="filter" id="filter" value="<?php echo htmlspecialchars($filter); ?>" />
                            </label>
                        </th>
                    </tr>
                </thead>
                <tfoot>
                    <tr>
                        <td colspan="3">
                            <input type="submit" name="save-override" value="Enregistrer" />
                            <input type="hidden" name="tnt_id" value="<?php echo $tenantId; ?>" />
                            <input type="hidden" name="wst_id" value="<?php echo $websiteId; ?>" />
                            <?php if ($onlyOverride) { ?>
                                <input type="hidden" name="only_override" value="on" />
                            <?php } ?>
                            <?php if ($filter) { ?>
                                <input type="hidden" name="filter" value="<?php echo htmlspecialchars($filter); ?>" />
                            <?php } ?>
                        </td>
                    </tr>
                </tfoot>
                <tbody>
                    <?php
                    if (!count($variables)) {
                        ?>
                        <tr><td colspan="3">Aucune variable ne correspond à vos critères</td></tr>
                        <?php
                    } else {
                        foreach ($variables as $code => $data) {
                            echo Maintenance_Utils_ViewHelpers::renderVariableLine($tenantId, $data);

                            if (count($data['childs'])) {
                                $hidden = '';
                                if ($data['type'] == FLD_TYPE_BOOLEAN_YES_NO && !in_array($data['override'], array('1', 'oui', 'Oui'))) {
                                    $hidden = 'hidden';
                                } elseif (trim($data['override']) === '') {
                                    $hidden = 'hidden';
                                }
                                ?>
                                <tr data-child-id="<?php echo htmlspecialchars($data['code']); ?>" class="tr-list-child <?php echo $hidden; ?>">
                                    <td colspan="3">
                                        <table class="checklist table-child-variable" cellspacing="0" cellpadding="0">
                                            <caption>Variables de configuration</caption>
                                            <col width="300" /><col width="400" /><col width="16" />
                                            <thead>
                                                <tr>
                                                    <th colspan="3">Variables complémentaires</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                foreach ($data['childs'] as $childData) {
                                                    echo Maintenance_Utils_ViewHelpers::renderVariableLine($tenantId, $childData, true);
                                                }
                                                ?>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <?php
                            }
                        }
                    }
                    ?>
                </tbody>
            </table>
            <?php
        }
        ?>
    </form>
</div>

<?php if (!$isAjax) { ?>
    <div class="reloadPageAjax" style="display: none;"></div>
    <script>
        $(document).ready(function() {
            // Initialize event handlers
            initializeEventHandlers();
        });

        function initializeEventHandlers() {
            // Remove existing handlers to prevent duplicates
            $(document).off('change', '[name="tnt_id"], [name="wst_id"], [name="only_override"]');
            $(document).off('blur', '[name="filter"]');
            $(document).off('keypress', '[name="filter"]');
            $(document).off('click', '[data-child-open] [type="radio"]');
            $(document).off('blur', 'textarea');

            // Handle form changes with delegated events
            $(document).on('change', '[name="tnt_id"], [name="wst_id"], [name="only_override"]', function() {
                reloadVariablePage(false, true);
            });

            $(document).on('blur', '[name="filter"]', function() {
                reloadVariablePage(false, true);
            });

            $(document).on('keypress', '[name="filter"]', function(event) {
                if (event.keyCode == 13) {
                    reloadVariablePage(false, true);
                    return false;
                }
            });

            // Handle child variable display with delegated events
            $(document).on('click', '[data-child-open] [type="radio"]', function() {
                var child = $(this).parents('tr').data('child-open');
                var val = $(this).val();
                displayChildOption(child, (val == '1' ? true : false));
            });

            $(document).on('blur', 'textarea', function() {
                var child = $(this).parents('tr').data('child-open');
                var val = $(this).val();
                displayChildOption(child, ($.trim(val) != "" ? true : false));
            });
        }

        // Browser history support
        if (!$.browser || !$.browser.msie) {
            window.onpopstate = function(event) {
                var query = '';
                if (event.state !== null) {
                    query = event.state;
                }
                reloadVariablePage(query, false);
            };
        }

        function displayChildOption(trChild, show) {
            if (show) {
                $('tr[data-child-id="' + trChild + '"]').show();
            } else {
                $('tr[data-child-id="' + trChild + '"]').hide();
            }
        }

        function reloadVariablePage(historyData, pushState) {
            // Show loading indicator
            $('.reloadPageAjax').show();

            // Add loading state to the select element
            var $select = $('#wst_id');
            if ($select.length) {
                $select.prop('disabled', true).addClass('loading');
            }

            var dataAjax = '';

            if (historyData !== false) {
                dataAjax = historyData;
            } else {
                dataAjax = 'controller=configuration&tnt_id=' + $('#tnt_id').val();

                if ($('#wst_id').length) {
                    dataAjax += '&wst_id=' + $('#wst_id').val();
                }

                if ($('#only_override:checked').length) {
                    dataAjax += '&only_override=on';
                }

                if ($('[name="filter"]').length) {
                    dataAjax += '&filter=' + $('[name="filter"]').val();
                }
            }

            $.ajax({
                url: '/maintenance/',
                data: dataAjax,
                cache: false,
                method: 'get',
                dataType: 'text'
            }).done(function(data) {
                if ($(data).find('[id="form-config-cfg"]').length) {
                    $('[id="form-config-cfg"]').html($(data).find('[id="form-config-cfg"]').html());
                }

                if ($(data).find('[id="form-list-override"]').length) {
                    $('[id="form-list-override"]').html($(data).find('[id="form-list-override"]').html());
                    $('[id="form-list-override"]').attr('action', $(data).find('[id="form-list-override"]').attr('action'));
                }

                if (!$.browser || (!$.browser.msie && pushState)) {
                    window.history.pushState(dataAjax, "page", window.location.pathname + '?' + dataAjax);
                }

                // Re-initialize event handlers after content reload
                initializeEventHandlers();

                $('.reloadPageAjax').hide();

                // Remove loading state
                $('#wst_id').prop('disabled', false).removeClass('loading');

            }).fail(function() {
                $('.reloadPageAjax').hide();
                $('#wst_id').prop('disabled', false).removeClass('loading');
                alert('Erreur lors du chargement des données. Veuillez réessayer.');
            });
        }
    </script>
<?php } ?>
