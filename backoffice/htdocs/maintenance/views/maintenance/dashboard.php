<?php
/**
 * Maintenance Dashboard View
 *
 * Main dashboard for the maintenance interface
 *
 * @package Maintenance\Views
 * <AUTHOR> Team
 */

// Prevent direct access
if (!defined('MAINTENANCE_ACCESS')) {
    http_response_code(403);
    exit('Direct access forbidden');
}

// Check if this is an AJAX request
$isAjax = Maintenance_Utils_Authentication::isAjaxRequest();

if (!$isAjax) {
    // Set page title and include admin header
    define('PAGE_TITLE', 'Page de maintenance - Maintenance');
    require_once('../../include/admin/skin/header.inc.php');
    ?>
    <link rel="stylesheet" href="/maintenance/assets/css/dashboard.css">
    <?php
}
?>

<div class="container">
    <div class="notice notice-super-admin">
        <?php echo _("Cette interface n'est visible que par les super-administrateurs."); ?>
    </div>

    <h2><?php echo _("Maintenance"); ?></h2>

    <!-- Sync Information Section -->
    <div class="dashboard-section">
        <div class="section-header">
            <h3><?php echo _("Informations de synchronisation"); ?></h3>
            <button id="refresh-sync-info" class="button button-secondary" title="Actualiser les informations">
                <i class="icon-refresh"></i> Actualiser
            </button>
        </div>

        <div class="sync-info-grid">
            <div class="info-card">
                <div class="info-label"><?php echo _("Type de GESCOM"); ?></div>
                <div class="info-value" id="gescom-type">
                    <?php echo htmlspecialchars(isset($syncInfo['gescom_type']) ? $syncInfo['gescom_type'] : 'Inconnu'); // MODIFIED LINE 41 ?>
                </div>
            </div>

            <div class="info-card">
                <div class="info-label"><?php echo _("Token"); ?></div>
                <div class="info-value" id="token">
                    <span class="token-display"><?php echo htmlspecialchars(substr(isset($syncInfo['token']) ? $syncInfo['token'] : '', 0, 8) . '...'); ?></span>
                    <button class="button-link" onclick="toggleTokenVisibility()" title="Afficher/Masquer le token complet">
                        <i class="icon-eye"></i>
                    </button>
                    <span class="token-full" style="display: none;"><?php echo htmlspecialchars(isset($syncInfo['token']) ? $syncInfo['token'] : ''); ?></span>
                </div>
            </div>

            <div class="info-card">
                <div class="info-label"><?php echo _("Statut de la synchro"); ?></div>
                <div class="info-value" id="sync-reboot-container">
                    <span id="sync-reboot" class="status-badge <?php echo (isset($syncInfo['sync_reboot']) ? $syncInfo['sync_reboot'] : 0) < 0 ? 'status-stopped' : 'status-restart'; ?>">
                        <?php echo (isset($syncInfo['sync_reboot']) ? $syncInfo['sync_reboot'] : 0) < 0 ? 'Arrêt demandé' : 'Redémarrage demandé'; ?>
                    </span>
                </div>
            </div>

            <div class="info-card">
                <div class="info-label"><?php echo _("Date de la dernière synchro"); ?></div>
                <div class="info-value" id="last-sync-container">
                    <span id="last-sync"><?php echo htmlspecialchars(isset($syncInfo['last_sync']) ? $syncInfo['last_sync'] : 'Jamais'); ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Waiting Items Section -->
    <div class="dashboard-section">
        <h3><?php echo _("Éléments en attente de synchronisation"); ?></h3>

        <div class="waiting-items-grid">
            <div class="waiting-item-card">
                <div class="item-icon orders-icon">📦</div>
                <div class="item-content">
                    <div class="item-label"><?php echo _("Commandes en attente"); ?></div>
                    <div class="item-count" id="waiting-orders-container">
                        <span id="waiting-orders" class="count-display">
                            <span class="count"><?php echo isset($syncInfo['waiting_counts']['orders']) ? $syncInfo['waiting_counts']['orders'] : 0; ?></span>
                        </span>
                        <?php if (isset($syncInfo['task_status']['orders']) ? $syncInfo['task_status']['orders'] : false): ?>
                            <span class="task-active">(<?php echo _("Tâche active"); ?>)</span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="item-actions">
                    <a href="<?php echo htmlspecialchars(isset($syncInfo['api_urls']['orders']) ? $syncInfo['api_urls']['orders'] : '#'); ?>"
                       target="_blank" class="button button-sm button-secondary" title="Voir dans l'API">
                        <i class="icon-external-link"></i>
                    </a>
                </div>
            </div>

            <div class="waiting-item-card">
                <div class="item-icon quotes-icon">📋</div>
                <div class="item-content">
                    <div class="item-label"><?php echo _("Devis en attente"); ?></div>
                    <div class="item-count" id="waiting-quote-container">
                        <span id="waiting-quote" class="count-display">
                            <span class="count"><?php echo isset($syncInfo['waiting_counts']['quotes']) ? $syncInfo['waiting_counts']['quotes'] : 0; ?></span>
                        </span>
                        <?php if (isset($syncInfo['task_status']['quotes']) ? $syncInfo['task_status']['quotes'] : false): ?>
                            <span class="task-active">(<?php echo _("Tâche active"); ?>)</span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="item-actions">
                    <a href="<?php echo htmlspecialchars(isset($syncInfo['api_urls']['quotes']) ? $syncInfo['api_urls']['quotes'] : '#'); ?>"
                       target="_blank" class="button button-sm button-secondary" title="Voir dans l'API">
                        <i class="icon-external-link"></i>
                    </a>
                </div>
            </div>

            <div class="waiting-item-card">
                <div class="item-icon users-icon">👥</div>
                <div class="item-content">
                    <div class="item-label"><?php echo _("Utilisateurs en attente"); ?></div>
                    <div class="item-count" id="waiting-users-container">
                        <span id="waiting-users" class="count-display">
                            <span class="count"><?php echo isset($syncInfo['waiting_counts']['users']) ? $syncInfo['waiting_counts']['users'] : 0; ?></span>
                        </span>
                        <?php if (isset($syncInfo['task_status']['users']) ? $syncInfo['task_status']['users'] : false): ?>
                            <span class="task-active">(<?php echo _("Tâche active"); ?>)</span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="item-actions">
                    <a href="<?php echo htmlspecialchars(isset($syncInfo['api_urls']['users']) ? $syncInfo['api_urls']['users'] : '#'); ?>"
                       target="_blank" class="button button-sm button-secondary" title="Voir dans l'API">
                        <i class="icon-external-link"></i>
                    </a>
                </div>
            </div>

            <div class="waiting-item-card">
                <div class="item-icon addresses-icon">📍</div>
                <div class="item-content">
                    <div class="item-label"><?php echo _("Adresses en attente"); ?></div>
                    <div class="item-count" id="waiting-addresses-container">
                        <span id="waiting-addresses" class="count-display">
                            <span class="count"><?php echo isset($syncInfo['waiting_counts']['addresses']) ? $syncInfo['waiting_counts']['addresses'] : 0; ?></span>
                        </span>
                        <?php if (isset($syncInfo['task_status']['addresses']) ? $syncInfo['task_status']['addresses'] : false): ?>
                            <span class="task-active">(<?php echo _("Tâche active"); ?>)</span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="item-actions">
                    <a href="<?php echo htmlspecialchars(isset($syncInfo['api_urls']['addresses']) ? $syncInfo['api_urls']['addresses'] : '#'); ?>"
                       target="_blank" class="button button-sm button-secondary" title="Voir dans l'API">
                        <i class="icon-external-link"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="update-indicator" id="update-indicator"></div>
    </div>

    <!-- BigShip Specific Information -->
    <?php if (isset($syncInfo['bigship_info']) && $syncInfo['bigship_info']): ?>
        <div class="dashboard-section">
            <h3>Informations BigShip</h3>
            <div class="bigship-info">
                <?php echo $syncInfo['bigship_info']; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Quick Actions Section -->
    <div class="dashboard-section">
        <h3><?php echo _("Actions rapides"); ?></h3>
        <div class="quick-actions">
            <a href="/maintenance/?controller=tasks" class="action-card">
                <div class="action-icon">⚙️</div>
                <div class="action-content">
                    <div class="action-title"><?php echo _("Gestion des tâches de synchronisation"); ?></div>
                    <div class="action-description">Démarrer, arrêter et forcer l'exécution des tâches</div>
                </div>
            </a>

            <a href="/maintenance/?controller=configuration" class="action-card">
                <div class="action-icon">🔧</div>
                <div class="action-content">
                    <div class="action-title"><?php echo _("Gestion des variables de synchronisation"); ?></div>
                    <div class="action-description">Configurer les paramètres de synchronisation</div>
                </div>
            </a>

            <a href="/maintenance/?controller=updates" class="action-card">
                <div class="action-icon">🔄</div>
                <div class="action-content">
                    <div class="action-title">Mise à Jour Manuelle Riashop</div>
                    <div class="action-description">Mettre à jour le backend et le frontend via SVN</div>
                </div>
            </a>

            <a href="/maintenance/?action=system-status" class="action-card">
                <div class="action-icon">📊</div>
                <div class="action-content">
                    <div class="action-title">Statut du système</div>
                    <div class="action-description">Vérifier l'état du système et des services</div>
                </div>
            </a>

            <a href="/maintenance/?controller=logs" class="action-card">
                <div class="action-icon">📋</div>
                <div class="action-content">
                    <div class="action-title">Visualiseur de logs</div>
                    <div class="action-description">Consulter et gérer les fichiers de logs JSON</div>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- Dashboard JavaScript -->
<script>
    // Global variables
    let autoRefreshInterval;
    let isRefreshing = false;

    // Initialize dashboard
    $(document).ready(function() {
        initializeDashboard();
        startAutoRefresh();
    });

    function initializeDashboard() {
        // Bind refresh button
        $('#refresh-sync-info').on('click', function() {
            updateSynchroInfo();
        });

        // Initialize tooltips
        $('[title]').each(function() {
            $(this).attr('data-toggle', 'tooltip');
        });
    }

    function startAutoRefresh() {
        // Auto-refresh every 30 seconds
        autoRefreshInterval = setInterval(updateSynchroInfo, 30000);
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    }

    function updateSynchroInfo() {
        if (isRefreshing) return;

        isRefreshing = true;
        const indicator = document.getElementById('update-indicator');
        indicator.textContent = '<?php echo _("Mise à jour en cours..."); ?>';

        // Add spinners to all containers
        addSpinner('sync-reboot-container');
        addSpinner('last-sync-container');
        addSpinner('waiting-orders-container');
        addSpinner('waiting-quote-container');
        addSpinner('waiting-users-container');
        addSpinner('waiting-addresses-container');

        $.ajax({
            url: '/maintenance/?action=sync-info',
            method: 'GET',
            dataType: 'json',
            timeout: 10000
        })
            .done(function(response) {
                if (response.success && response.data) {
                    const data = response.data;

                    // Update sync status
                    // Use ternary operator for compatibility
                    updateElementWithFade('sync-reboot', (data.sync_reboot < 0) ? 'Arrêt demandé' : 'Redémarrage demandé');
                    updateElementWithFade('last-sync', data.last_sync || 'Jamais'); // JavaScript still allows || for null/undefined

                    // Update counts
                    updateElementWithFade('waiting-orders .count', data.waiting_orders.count);
                    updateElementWithFade('waiting-quote .count', data.waiting_quote.count);
                    updateElementWithFade('waiting-users .count', data.waiting_users.count);
                    updateElementWithFade('waiting-addresses .count', data.waiting_addresses.count);

                    // Update task active indicators
                    updateTaskActiveIndicator('waiting-orders', data.waiting_orders.active);
                    updateTaskActiveIndicator('waiting-quote', data.waiting_quote.active);
                    updateTaskActiveIndicator('waiting-users', data.waiting_users.active);
                    updateTaskActiveIndicator('waiting-addresses', data.waiting_addresses.active);

                    const now = new Date();
                    indicator.textContent = '<?php echo _("Dernière mise à jour :"); ?> ' + now.toLocaleTimeString();
                } else {
                    indicator.textContent = '<?php echo _("Erreur lors de la mise à jour"); ?>';
                }
            })
            .fail(function(xhr, status, error) {
                console.error('Update failed:', error);
                indicator.textContent = '<?php echo _("Erreur lors de la mise à jour"); ?>';
            })
            .always(function() {
                removeAllSpinners();
                isRefreshing = false;
            });
    }

    function addSpinner(containerId) {
        const container = document.getElementById(containerId);
        if (container && !container.querySelector('.spinner')) {
            const spinner = document.createElement('div');
            spinner.className = 'spinner';
            const countContainer = container.querySelector('.count-display');
            if (countContainer) {
                countContainer.appendChild(spinner);
            } else {
                container.appendChild(spinner);
            }
        }
    }

    function removeAllSpinners() {
        document.querySelectorAll('.spinner').forEach(spinner => spinner.remove());
    }

    function updateElementWithFade(elementId, newValue) {
        const element = document.getElementById(elementId) || document.querySelector('#' + elementId);
        if (element) {
            element.textContent = newValue;
            element.classList.add('fade-update');
            setTimeout(() => element.classList.remove('fade-update'), 500);
        }
    }

    function updateTaskActiveIndicator(rowId, isActive) {
        const container = document.getElementById(rowId + '-container');
        if (!container) return;

        let taskActiveSpan = container.querySelector('.task-active');

        if (isActive) {
            if (!taskActiveSpan) {
                taskActiveSpan = document.createElement('span');
                taskActiveSpan.className = 'task-active';
                taskActiveSpan.textContent = '(<?php echo _("Tâche active"); ?>)';
                container.appendChild(taskActiveSpan);
            }
        } else if (taskActiveSpan) {
            taskActiveSpan.remove();
        }
    }

    function toggleTokenVisibility() {
        const tokenDisplay = document.querySelector('.token-display');
        const tokenFull = document.querySelector('.token-full');

        if (tokenDisplay && tokenFull) {
            if (tokenFull.style.display === 'none') {
                tokenDisplay.style.display = 'none';
                tokenFull.style.display = 'inline';
            } else {
                tokenDisplay.style.display = 'inline';
                tokenFull.style.display = 'none';
            }
        }
    }

    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        stopAutoRefresh();
    });
</script>

<?php
if (!$isAjax) {
    require_once('../../include/admin/skin/footer.inc.php');
}
?>