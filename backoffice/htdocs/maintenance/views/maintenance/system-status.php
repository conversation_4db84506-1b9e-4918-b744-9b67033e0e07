<?php
/**
 * System Status View
 *
 * Display system status information
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

// Check if this is an AJAX request
$isAjax = Maintenance_Utils_Authentication::isAjaxRequest();

if (!$isAjax) {
    // Set page title and include admin header
    define('PAGE_TITLE', $pageTitle);
    require_once('../../include/admin/skin/header.inc.php');
    ?>
    <link rel="stylesheet" href="/maintenance/assets/css/system-status.css">
    <?php
}
?>

<div>
    <p>
        <button class="button button-primary" onclick="window.location.href='/maintenance/'">Retour au dashboard</button>
    </p>
    
    <h2>Statut du système</h2>
    <p>Informations sur l'état du système et des services de maintenance.</p>
    
    <div class="status-container">
        <!-- System Information -->
        <div class="status-section">
            <h3 class="section-title">
                <i class="icon-server"></i> Informations système
            </h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">Version PHP</div>
                    <div class="status-value"><?php echo htmlspecialchars($status['system']['php_version']); ?></div>
                </div>
                <div class="status-item">
                    <div class="status-label">Utilisation mémoire</div>
                    <div class="status-value"><?php echo number_format($status['system']['memory_usage'] / 1024 / 1024, 2); ?> MB</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Limite mémoire</div>
                    <div class="status-value"><?php echo htmlspecialchars($status['system']['memory_limit']); ?></div>
                </div>
                <div class="status-item">
                    <div class="status-label">Temps d'exécution max</div>
                    <div class="status-value"><?php echo htmlspecialchars($status['system']['max_execution_time']); ?>s</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Serveur web</div>
                    <div class="status-value"><?php echo htmlspecialchars($status['system']['server_software']); ?></div>
                </div>
                <div class="status-item">
                    <div class="status-label">Document root</div>
                    <div class="status-value status-path"><?php echo htmlspecialchars($status['system']['document_root']); ?></div>
                </div>
            </div>
        </div>

        <!-- Database Information -->
        <div class="status-section">
            <h3 class="section-title">
                <i class="icon-database"></i> Base de données
            </h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">Connexion</div>
                    <div class="status-value">
                        <span class="status-indicator <?php echo $status['database']['connected'] ? 'status-ok' : 'status-error'; ?>">
                            <?php echo $status['database']['connected'] ? 'Connectée' : 'Déconnectée'; ?>
                        </span>
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-label">Version MySQL</div>
                    <div class="status-value"><?php echo htmlspecialchars($status['database']['mysql_version']); ?></div>
                </div>
            </div>
        </div>

        <!-- Synchronization Information -->
        <div class="status-section">
            <h3 class="section-title">
                <i class="icon-sync"></i> Synchronisation
            </h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">Dossier de sync</div>
                    <div class="status-value">
                        <span class="status-indicator <?php echo $status['sync']['folder_exists'] ? 'status-ok' : 'status-error'; ?>">
                            <?php echo $status['sync']['folder_exists'] ? 'Existe' : 'Introuvable'; ?>
                        </span>
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-label">Permissions d'écriture</div>
                    <div class="status-value">
                        <span class="status-indicator <?php echo $status['sync']['folder_writable'] ? 'status-ok' : 'status-error'; ?>">
                            <?php echo $status['sync']['folder_writable'] ? 'Accessible en écriture' : 'Non accessible en écriture'; ?>
                        </span>
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-label">Chemin du dossier</div>
                    <div class="status-value status-path"><?php echo htmlspecialchars($status['sync']['folder_path']); ?></div>
                </div>
            </div>
        </div>

        <!-- Memcached Information -->
        <div class="status-section">
            <h3 class="section-title">
                <i class="icon-cache"></i> Memcached
            </h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">Extension disponible</div>
                    <div class="status-value">
                        <span class="status-indicator <?php echo $status['memcached']['available'] ? 'status-ok' : 'status-error'; ?>">
                            <?php echo $status['memcached']['available'] ? 'Disponible' : 'Non disponible'; ?>
                        </span>
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-label">Connexion</div>
                    <div class="status-value">
                        <span class="status-indicator <?php echo $status['memcached']['connected'] ? 'status-ok' : 'status-error'; ?>">
                            <?php echo $status['memcached']['connected'] ? 'Connecté' : 'Déconnecté'; ?>
                        </span>
                    </div>
                </div>

                <?php if ($status['memcached']['connected'] && !empty($status['memcached']['stats'])): ?>
                    <div class="status-item">
                        <div class="status-label">Version</div>
                        <div class="status-value"><?php echo htmlspecialchars($status['memcached']['stats']['version']); ?></div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Uptime</div>
                        <div class="status-value"><?php echo gmdate('H:i:s', $status['memcached']['stats']['uptime']); ?></div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Éléments en cache</div>
                        <div class="status-value"><?php echo number_format($status['memcached']['stats']['curr_items']); ?></div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Utilisation mémoire</div>
                        <div class="status-value">
                            <?php echo round($status['memcached']['stats']['bytes'] / 1024 / 1024, 2); ?> MB
                            (<?php echo $status['memcached']['stats']['memory_usage_percent']; ?>%)
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Taux de succès</div>
                        <div class="status-value">
                            <span class="status-indicator <?php echo $status['memcached']['stats']['hit_ratio'] > 80 ? 'status-ok' : ($status['memcached']['stats']['hit_ratio'] > 50 ? 'status-warning' : 'status-error'); ?>">
                                <?php echo $status['memcached']['stats']['hit_ratio']; ?>%
                            </span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Hits / Misses</div>
                        <div class="status-value">
                            <?php echo number_format($status['memcached']['stats']['get_hits']); ?> /
                            <?php echo number_format($status['memcached']['stats']['get_misses']); ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($status['memcached']['error']): ?>
                    <div class="status-item">
                        <div class="status-label">Erreur</div>
                        <div class="status-value">
                            <span class="status-indicator status-error">
                                <?php echo htmlspecialchars($status['memcached']['error']); ?>
                            </span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Maintenance Information -->
        <div class="status-section">
            <h3 class="section-title">
                <i class="icon-tools"></i> Interface de maintenance
            </h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">Version</div>
                    <div class="status-value"><?php echo htmlspecialchars($status['maintenance']['version']); ?></div>
                </div>
                <div class="status-item">
                    <div class="status-label">Dernière mise à jour</div>
                    <div class="status-value"><?php echo date('d/m/Y H:i:s', strtotime($status['maintenance']['last_update'])); ?></div>
                </div>
                <div class="status-item">
                    <div class="status-label">Niveau d'accès</div>
                    <div class="status-value"><?php echo htmlspecialchars($status['maintenance']['access_level']); ?></div>
                </div>
                <div class="status-item">
                    <div class="status-label">Tenant ID</div>
                    <div class="status-value"><?php echo htmlspecialchars($tenantId); ?></div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="status-section">
            <h3 class="section-title">
                <i class="icon-action"></i> Actions
            </h3>
            <div class="status-actions">
                <button id="refresh-status" class="button button-secondary">
                    <i class="icon-refresh"></i> Actualiser le statut
                </button>
                <button id="test-database" class="button button-info">
                    <i class="icon-database"></i> Tester la base de données
                </button>
                <button id="check-permissions" class="button button-warning">
                    <i class="icon-shield"></i> Vérifier les permissions
                </button>
                <a href="/maintenance/?action=export-sync-data" class="button button-success">
                    <i class="icon-download"></i> Exporter les données
                </a>
            </div>
        </div>

        <!-- System Health Summary -->
        <div class="status-section">
            <h3 class="section-title">
                <i class="icon-heart"></i> Résumé de l'état du système
            </h3>
            <div class="health-summary">
                <?php
                $healthScore = 0;
                $totalChecks = 4;
                
                if ($status['database']['connected']) $healthScore++;
                if ($status['sync']['folder_exists']) $healthScore++;
                if ($status['sync']['folder_writable']) $healthScore++;
                if (version_compare($status['system']['php_version'], '5.6.0', '>=')) $healthScore++;
                
                $healthPercentage = ($healthScore / $totalChecks) * 100;
                $healthClass = $healthPercentage >= 75 ? 'health-good' : ($healthPercentage >= 50 ? 'health-warning' : 'health-critical');
                ?>
                <div class="health-indicator <?php echo $healthClass; ?>">
                    <div class="health-score"><?php echo round($healthPercentage); ?>%</div>
                    <div class="health-label">État général du système</div>
                </div>
                <div class="health-details">
                    <ul>
                        <li class="<?php echo $status['database']['connected'] ? 'check-ok' : 'check-error'; ?>">
                            Base de données : <?php echo $status['database']['connected'] ? 'OK' : 'Erreur'; ?>
                        </li>
                        <li class="<?php echo $status['sync']['folder_exists'] ? 'check-ok' : 'check-error'; ?>">
                            Dossier de synchronisation : <?php echo $status['sync']['folder_exists'] ? 'OK' : 'Erreur'; ?>
                        </li>
                        <li class="<?php echo $status['sync']['folder_writable'] ? 'check-ok' : 'check-error'; ?>">
                            Permissions d'écriture : <?php echo $status['sync']['folder_writable'] ? 'OK' : 'Erreur'; ?>
                        </li>
                        <li class="<?php echo version_compare($status['system']['php_version'], '5.6.0', '>=') ? 'check-ok' : 'check-error'; ?>">
                            Version PHP : <?php echo version_compare($status['system']['php_version'], '5.6.0', '>=') ? 'OK' : 'Obsolète'; ?>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading indicator -->
    <div id="loading-indicator" class="loading-indicator" style="display: none;">
        <div class="spinner"></div>
        <span>Chargement...</span>
    </div>
</div>

<?php if (!$isAjax){ ?>
    <script>
        $(document).ready(function() {
            initializeSystemStatusPage();
        });

        function initializeSystemStatusPage() {
            // Bind refresh button
            $('#refresh-status').on('click', function() {
                location.reload();
            });

            // Bind test database button
            $('#test-database').on('click', function() {
                testDatabaseConnection();
            });

            // Bind check permissions button
            $('#check-permissions').on('click', function() {
                checkPermissions();
            });
        }

        function testDatabaseConnection() {
            showLoading();

            $.ajax({
                url: '/maintenance/?action=get-system-status',
                method: 'GET',
                dataType: 'json'
            })
            .done(function(response) {
                if (response.success && response.data) {
                    const connected = response.data.database.connected;
                    const version = response.data.database.mysql_version;
                    const message = connected ?
                        'Base de données connectée avec succès!\n\nVersion MySQL: ' + version :
                        'Erreur de connexion à la base de données.';
                    alert(message);
                } else {
                    alert('Erreur lors du test de connexion: ' + (response.error || 'Erreur inconnue'));
                }
            })
            .fail(function(xhr, status, error) {
                alert('Erreur de communication avec le serveur: ' + error);
            })
            .always(function() {
                hideLoading();
            });
        }

        function checkPermissions() {
            showLoading();

            $.ajax({
                url: '/maintenance/?action=get-system-status',
                method: 'GET',
                dataType: 'json'
            })
            .done(function(response) {
                if (response.success && response.data) {
                    const sync = response.data.sync;
                    let message = 'Vérification des permissions:\n\n';
                    message += 'Dossier existe: ' + (sync.folder_exists ? 'Oui' : 'Non') + '\n';
                    message += 'Accessible en écriture: ' + (sync.folder_writable ? 'Oui' : 'Non') + '\n';
                    message += 'Chemin: ' + sync.folder_path + '\n\n';

                    if (sync.folder_exists && sync.folder_writable) {
                        message += 'Statut: ✅ Permissions OK';
                    } else {
                        message += 'Statut: ❌ Problème de permissions détecté';
                    }

                    alert(message);
                } else {
                    alert('Erreur lors de la vérification des permissions: ' + (response.error || 'Erreur inconnue'));
                }
            })
            .fail(function(xhr, status, error) {
                alert('Erreur de communication avec le serveur: ' + error);
            })
            .always(function() {
                hideLoading();
            });
        }

        function showLoading() {
            $('#loading-indicator').show();
        }

        function hideLoading() {
            $('#loading-indicator').hide();
        }
    </script>
    <?php
    require_once('../../include/admin/skin/footer.inc.php');
}
?>
