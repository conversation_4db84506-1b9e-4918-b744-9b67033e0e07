<?php
/**
 * Error View
 *
 * Display error messages
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

// Check if this is an AJAX request
$isAjax = Maintenance_Utils_Authentication::isAjaxRequest();

if (!$isAjax) {
    // Set page title and include admin header
    define('PAGE_TITLE', 'Erreur - Maintenance');
    require_once('../../include/admin/skin/header.inc.php');
}
?>

<div class="container">
    <div class="notice notice-error">
        <h2>Une erreur s'est produite</h2>
        <p><?php echo isset($message) ? htmlspecialchars($message) : 'Une erreur inattendue s\'est produite.'; ?></p>
        
        <?php if (defined('MAINTENANCE_DEBUG') && MAINTENANCE_DEBUG && isset($debug)): ?>
            <details>
                <summary>Détails techniques</summary>
                <pre><?php echo htmlspecialchars($debug); ?></pre>
            </details>
        <?php endif; ?>
        
        <p>
            <a href="/maintenance/" class="button button-primary">Retour au dashboard</a>
        </p>
    </div>
</div>

<style>
.notice-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 20px;
    border-radius: 4px;
    margin: 20px 0;
}

.notice-error h2 {
    margin-top: 0;
    color: #721c24;
}

details {
    margin-top: 15px;
}

summary {
    cursor: pointer;
    font-weight: bold;
    margin-bottom: 10px;
}

pre {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    overflow-x: auto;
    font-size: 0.9em;
}
</style>

<?php
if (!$isAjax) {
    require_once('../../include/admin/skin/footer.inc.php');
}
?>
