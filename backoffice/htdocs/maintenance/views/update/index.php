<?php
/**
 * Update Interface View
 *
 * Interface for manual Riashop updates
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

// Prevent direct access
if (!defined('MAINTENANCE_ACCESS')) {
    if (function_exists('http_response_code')) {
        http_response_code(403);
    } else {
        header('HTTP/1.1 403 Forbidden');
    }
    exit('Direct access forbidden');
}

// Include admin skin header
require_once('admin/skin/header.inc.php');

// Generate CSRF token
$updateController = new Maintenance_Controllers_UpdateController();
$csrfToken = $updateController->generateCsrfToken();
?>
<link rel="stylesheet" href="/maintenance/assets/css/update.css">

<div>
    <p>
        <button class="button button-primary" onclick="window.location.href='/maintenance/'">Retour au dashboard</button>
    </p>

    <h2>Mise à Jour Manuelle Riashop</h2>
    <p>Gestion des mises à jour Backend et Frontend</p>

    <!-- Warning Alert -->
    <div class="notice notice-warning">
        <h4><strong>⚠️ Avertissement Important</strong></h4>
        <p><strong>Il est fortement recommandé d'effectuer une sauvegarde complète de la base de données avant de procéder à toute mise à jour.</strong></p>
        <p>Les commandes DDL (Data Definition Language) ne sont pas transactionnelles et ne peuvent pas être annulées automatiquement en cas d'échec. Une sauvegarde est cruciale pour la récupération en cas de problème.</p>
        <p><em>Cet outil ne réalise pas cette sauvegarde automatiquement.</em></p>
    </div>

    <!-- System Status -->
    <div class="update-section">
        <div class="section-header">
            <h3>📊 État du Système</h3>
            <button type="button" class="button button-secondary" onclick="refreshSystemStatus()">
                <i class="icon-refresh"></i> Actualiser
            </button>
        </div>
        <div class="section-content" id="system-status">
            <div class="loading-message">
                <span class="spinner"></span> Chargement de l'état du système...
            </div>
        </div>
    </div>

    <!-- Repository Information -->
    <div class="update-section">
        <div class="section-header">
            <h3>📁 Informations des Dépôts</h3>
        </div>
        <div class="section-content" id="repository-info">
            <div class="loading-message">
                <span class="spinner"></span> Chargement des informations des dépôts...
            </div>
        </div>
    </div>

    <!-- SVN Credentials Section -->
    <div class="update-section" id="credentials-section">
        <div class="section-header">
            <h3>🔐 Credentials SVN (Optionnel)</h3>
            <button type="button" class="button button-secondary" onclick="toggleCredentials()">
                <span id="credentials-toggle-text">Afficher</span>
            </button>
        </div>
        <div class="section-content" id="credentials-form" style="display: none;">
            <div class="credentials-grid">
                <div class="credential-group">
                    <label for="svn-username">Nom d'utilisateur SVN:</label>
                    <input type="text" id="svn-username" placeholder="Votre nom d'utilisateur SVN">
                </div>
                <div class="credential-group">
                    <label for="svn-password">Mot de passe SVN:</label>
                    <input type="password" id="svn-password" placeholder="Votre mot de passe SVN">
                </div>
            </div>
            <p class="credentials-note">
                <strong>Note:</strong> Ces credentials seront utilisés uniquement pour cette session et ne seront pas stockés.
                Si vous laissez vide, le système utilisera les credentials système par défaut.
            </p>
        </div>
    </div>

    <!-- Backend Workflow -->
    <div class="update-section">
        <div class="section-header">
            <h3>🖥️ Workflow Backend (Engine)</h3>
        </div>
        <div class="section-content">
            <div class="workflow-steps" id="backend-workflow">
                <!-- Step 1: SVN Update Engine (Always visible) -->
                <div class="workflow-step" data-step="svn_update_engine" id="step-block-1">
                    <div class="step-header">
                        <div class="step-number">1</div>
                        <div class="step-info">
                            <h4>Mise à Jour SVN Engine</h4>
                            <p>Met à jour le dépôt SVN de l'engine</p>
                        </div>
                        <div class="step-status" id="status-svn_update_engine">
                            <span class="status-badge status-pending">En attente</span>
                        </div>
                    </div>
                    <div class="step-actions">
                        <button type="button" class="button button-primary" onclick="executeStep('svn_update_engine')" id="btn-svn_update_engine">
                            <i class="icon-download"></i> Exécuter
                        </button>
                        <button type="button" class="button button-warning" onclick="executeStep('svn_cleanup_engine')" id="btn-svn_cleanup_engine" title="Nettoie le dépôt SVN en cas de verrouillage">
                            <i class="icon-tools"></i> SVN Cleanup
                        </button>
                        <button type="button" class="button button-secondary" onclick="skipStep('svn_update_engine')" id="skip-svn_update_engine" style="display: none;">
                            Ignorer
                        </button>
                    </div>
                    <div class="step-result" id="result-svn_update_engine" style="display: none;"></div>
                    <div class="step-result" id="result-svn_cleanup_engine" style="display: none;"></div>
                </div>

                <!-- Step 2: SQL Scripts (Hidden initially) -->
                <div class="workflow-step" data-step="execute_sql_scripts" id="step-block-2" style="display: none;">
                    <div class="step-header">
                        <div class="step-number">2</div>
                        <div class="step-info">
                            <h4>Exécution Scripts SQL</h4>
                            <p>Exécute les scripts de migration de base de données</p>
                        </div>
                        <div class="step-status" id="status-execute_sql_scripts">
                            <span class="status-badge status-pending">En attente</span>
                        </div>
                    </div>
                    <div class="step-scripts" id="scripts-sql" style="display: none;">
                        <h5>📄 Scripts SQL détectés:</h5>
                        <div class="scripts-list" id="sql-scripts-list">
                            <!-- Scripts will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="step-actions">
                        <button type="button" class="button button-primary" onclick="executeStep('execute_sql_scripts')" id="btn-execute_sql_scripts" disabled>
                            <i class="icon-database"></i> Exécuter
                        </button>
                        <button type="button" class="button button-secondary" onclick="toggleScriptsList('sql')" id="toggle-sql-scripts" style="display: none;">
                            <i class="icon-list"></i> Voir Scripts
                        </button>
                        <button type="button" class="button button-secondary" onclick="skipStep('execute_sql_scripts')" id="skip-execute_sql_scripts" style="display: none;">
                            <i class="icon-skip"></i> Ignorer
                        </button>
                    </div>
                    <div class="step-result" id="result-execute_sql_scripts" style="display: none;"></div>
                </div>

                <!-- Step 3: PHP Scripts (Hidden initially) -->
                <div class="workflow-step" data-step="execute_php_scripts" id="step-block-3" style="display: none;">
                    <div class="step-header">
                        <div class="step-number">3</div>
                        <div class="step-info">
                            <h4>Exécution Scripts PHP</h4>
                            <p>Exécute les scripts de migration PHP</p>
                        </div>
                        <div class="step-status" id="status-execute_php_scripts">
                            <span class="status-badge status-pending">En attente</span>
                        </div>
                    </div>
                    <div class="step-scripts" id="scripts-php" style="display: none;">
                        <h5>📄 Scripts PHP détectés:</h5>
                        <div class="scripts-list" id="php-scripts-list">
                            <!-- Scripts will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="step-actions">
                        <button type="button" class="button button-primary" onclick="executeStep('execute_php_scripts')" id="btn-execute_php_scripts" disabled>
                            <i class="icon-code"></i> Exécuter
                        </button>
                        <button type="button" class="button button-secondary" onclick="toggleScriptsList('php')" id="toggle-php-scripts" style="display: none;">
                            <i class="icon-list"></i> Voir Scripts
                        </button>
                        <button type="button" class="button button-secondary" onclick="skipStep('execute_php_scripts')" id="skip-execute_php_scripts" style="display: none;">
                            <i class="icon-skip"></i> Ignorer
                        </button>
                    </div>
                    <div class="step-result" id="result-execute_php_scripts" style="display: none;"></div>
                </div>

                <!-- Step 4: Version Update (Hidden initially) -->
                <div class="workflow-step" data-step="update_version" id="step-block-4" style="display: none;">
                    <div class="step-header">
                        <div class="step-number">4</div>
                        <div class="step-info">
                            <h4>Mise à Jour Version</h4>
                            <p>Met à jour le numéro de version en base de données</p>
                        </div>
                        <div class="step-status" id="status-update_version">
                            <span class="status-badge status-pending">En attente</span>
                        </div>
                    </div>
                    <div class="step-actions">
                        <button type="button" class="button button-primary" onclick="executeStep('update_version')" id="btn-update_version" disabled>
                            <i class="icon-tag"></i> Exécuter
                        </button>
                        <button type="button" class="button button-secondary" onclick="skipStep('update_version')" id="skip-update_version" style="display: none;">
                            Ignorer
                        </button>
                    </div>
                    <div class="step-result" id="result-update_version" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Frontend Workflow -->
    <div class="update-section">
        <div class="section-header">
            <h3>🌐 Workflow Frontend (Client)</h3>
        </div>
        <div class="section-content">
            <div class="workflow-steps" id="frontend-workflow">
                <div class="workflow-step" data-step="svn_update_frontend">
                    <div class="step-header">
                        <div class="step-number">1</div>
                        <div class="step-info">
                            <h4>Mise à Jour SVN Frontend</h4>
                            <p>Met à jour le dépôt SVN du frontend client</p>
                        </div>
                        <div class="step-status" id="status-svn_update_frontend">
                            <span class="status-badge status-pending">En attente</span>
                        </div>
                    </div>
                    <div class="step-actions">
                        <button type="button" class="button button-success" onclick="executeStep('svn_update_frontend')" id="btn-svn_update_frontend">
                            <i class="icon-download"></i> Exécuter
                        </button>
                        <button type="button" class="button button-warning" onclick="executeStep('svn_cleanup_frontend')" id="btn-svn_cleanup_frontend" title="Nettoie le dépôt SVN en cas de verrouillage">
                            <i class="icon-tools"></i> SVN Cleanup
                        </button>
                        <button type="button" class="button button-secondary" onclick="skipStep('svn_update_frontend')" id="skip-svn_update_frontend" style="display: none;">
                            Ignorer
                        </button>
                    </div>
                    <div class="step-result" id="result-svn_update_frontend" style="display: none;"></div>
                    <div class="step-result" id="result-svn_cleanup_frontend" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress and Results -->
    <div class="update-section" id="results-panel" style="display: none;">
        <div class="section-header">
            <h3>📋 Résultats de la Mise à Jour</h3>
        </div>
        <div class="section-content">
            <div id="update-progress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill">
                        <span id="progress-text">Mise à jour en cours...</span>
                    </div>
                </div>
            </div>
            <div id="update-results"></div>
        </div>
    </div>
</div>

<script>
// CSRF Token
var csrfToken = '<?php echo htmlspecialchars($csrfToken); ?>';

// Load system status on page load
$(document).ready(function() {
    refreshSystemStatus();
});

// Refresh system status
function refreshSystemStatus() {
    $.ajax({
        url: '/maintenance/?controller=updates&action=system-status',
        method: 'GET',
        dataType: 'json'
    })
    .done(function(response) {
        if (response.success && response.data) {
            displaySystemStatus(response.data);
            displayRepositoryInfo(response.data);
            updateWorkflowSteps(response.data);
        } else {
            $('#system-status').html('<div class="notice notice-error">Erreur lors du chargement de l\'état du système</div>');
        }
    })
    .fail(function() {
        $('#system-status').html('<div class="notice notice-error">Erreur de communication avec le serveur</div>');
    });
}

// Toggle credentials form
function toggleCredentials() {
    var form = $('#credentials-form');
    var toggleText = $('#credentials-toggle-text');

    if (form.is(':visible')) {
        form.hide();
        toggleText.text('Afficher');
    } else {
        form.show();
        toggleText.text('Masquer');
    }
}

// Display system status
function displaySystemStatus(status) {
    var html = '<div class="status-grid">';

    // Current version
    html += '<div class="status-item">';
    html += '<div class="status-label">Version Actuelle</div>';
    html += '<div class="status-value status-primary">' + status.current_version + '</div>';
    html += '</div>';

    // Available updates
    html += '<div class="status-item">';
    html += '<div class="status-label">Mises à Jour Disponibles</div>';
    html += '<div class="status-value ' + (status.available_updates > 0 ? 'status-warning' : 'status-success') + '">';
    html += status.available_updates;
    html += '</div>';
    html += '</div>';

    // SVN status
    html += '<div class="status-item">';
    html += '<div class="status-label">SVN Disponible</div>';
    html += '<div class="status-value ' + (status.svn_available ? 'status-success' : 'status-error') + '">';
    html += (status.svn_available ? '✓ Oui' : '✗ Non');
    html += '</div>';
    html += '</div>';

    // Logs directory
    html += '<div class="status-item">';
    html += '<div class="status-label">Répertoire Logs</div>';
    html += '<div class="status-value ' + (status.logs_directory.writable ? 'status-success' : 'status-error') + '">';
    html += (status.logs_directory.writable ? '✓ Accessible' : '✗ Erreur');
    html += '</div>';
    html += '</div>';

    html += '</div>';
    
    // Update scripts list
    if (status.update_scripts && status.update_scripts.length > 0) {
        html += '<div class="scripts-section">';
        html += '<h4>Scripts de Mise à Jour Disponibles:</h4>';
        html += '<div class="scripts-list">';
        status.update_scripts.forEach(function(script) {
            html += '<div class="script-item">';
            html += '<span class="script-type script-type-' + script.type + '">' + script.type.toUpperCase() + '</span>';
            html += '<span class="script-name">' + script.file + '</span>';
            html += '<span class="script-version">v' + script.version + '</span>';
            html += '</div>';
        });
        html += '</div>';
        html += '</div>';
    }

    $('#system-status').html(html);
}

// Display repository information
function displayRepositoryInfo(status) {
    var html = '<div class="repository-grid">';

    // Engine repository
    html += '<div class="repository-item">';
    html += '<h4>🖥️ Engine Repository</h4>';
    html += '<div class="repo-details">';
    html += '<div class="repo-field"><strong>Chemin:</strong> ' + (status.engine_repository.path || 'Non défini') + '</div>';
    html += '<div class="repo-field"><strong>URL:</strong> ' + (status.engine_repository.url || 'Non disponible') + '</div>';
    html += '<div class="repo-field"><strong>Révision:</strong> ' + (status.engine_repository.revision || 'N/A') + '</div>';
    html += '<div class="repo-field"><strong>Statut:</strong> <span class="repo-status ' + getRepoStatusClass(status.engine_repository.status) + '">' + status.engine_repository.status + '</span></div>';
    if (status.engine_repository.last_changed) {
        html += '<div class="repo-field"><strong>Dernière modification:</strong> ' + status.engine_repository.last_changed + '</div>';
    }
    html += '</div>';
    html += '</div>';

    // Frontend repository
    html += '<div class="repository-item">';
    html += '<h4>🌐 Frontend Repository</h4>';
    html += '<div class="repo-details">';
    html += '<div class="repo-field"><strong>Chemin:</strong> ' + (status.frontend_repository.path || 'Non défini') + '</div>';
    html += '<div class="repo-field"><strong>URL:</strong> ' + (status.frontend_repository.url || 'Non disponible') + '</div>';
    html += '<div class="repo-field"><strong>Révision:</strong> ' + (status.frontend_repository.revision || 'N/A') + '</div>';
    html += '<div class="repo-field"><strong>Statut:</strong> <span class="repo-status ' + getRepoStatusClass(status.frontend_repository.status) + '">' + status.frontend_repository.status + '</span></div>';
    if (status.frontend_repository.last_changed) {
        html += '<div class="repo-field"><strong>Dernière modification:</strong> ' + status.frontend_repository.last_changed + '</div>';
    }
    html += '</div>';
    html += '</div>';

    html += '</div>';

    $('#repository-info').html(html);
}

// Get repository status CSS class
function getRepoStatusClass(status) {
    switch (status) {
        case 'OK': return 'status-success';
        case 'Error': case 'SVN Error': return 'status-error';
        case 'Not a SVN repository': case 'Path does not exist': return 'status-warning';
        default: return 'status-neutral';
    }
}

// Update workflow steps based on system status
function updateWorkflowSteps(status) {
    // Update step requirements based on available scripts
    var allSqlScripts = status.all_sql_scripts ? status.all_sql_scripts : [];
    var allPhpScripts = status.all_php_scripts ? status.all_php_scripts : [];
    var sqlScriptsCount = status.sql_scripts ? status.sql_scripts.length : 0;
    var phpScriptsCount = status.php_scripts ? status.php_scripts.length : 0;

    // Store script data globally for individual replay functionality
    window.allSqlScripts = allSqlScripts;
    window.allPhpScripts = allPhpScripts;

    // Update SQL scripts step
    var sqlStep = $('#btn-execute_sql_scripts').closest('.workflow-step');
    if (allSqlScripts.length === 0) {
        sqlStep.find('.step-info p').text('Aucun script SQL trouvé dans le répertoire');
        sqlStep.find('#btn-execute_sql_scripts').html('<i class="icon-database"></i> Aucun script');
        sqlStep.addClass('step-optional');
        $('#toggle-sql-scripts').hide();
    } else {
        var statusText = sqlScriptsCount > 0 ?
            'Exécute ' + sqlScriptsCount + ' script(s) de migration SQL' :
            'Tous les scripts SQL sont déjà exécutés (' + allSqlScripts.length + ' trouvé(s))';
        sqlStep.find('.step-info p').text(statusText);
        sqlStep.removeClass('step-optional');
        $('#toggle-sql-scripts').show();
        populateScriptsList('sql', allSqlScripts);
    }

    // Update PHP scripts step
    var phpStep = $('#btn-execute_php_scripts').closest('.workflow-step');
    if (allPhpScripts.length === 0) {
        phpStep.find('.step-info p').text('Aucun script PHP trouvé dans le répertoire');
        phpStep.find('#btn-execute_php_scripts').html('<i class="icon-code"></i> Aucun script');
        phpStep.addClass('step-optional');
        $('#toggle-php-scripts').hide();
    } else {
        var statusText = phpScriptsCount > 0 ?
            'Exécute ' + phpScriptsCount + ' script(s) de migration PHP' :
            'Tous les scripts PHP sont déjà exécutés (' + allPhpScripts.length + ' trouvé(s))';
        phpStep.find('.step-info p').text(statusText);
        phpStep.removeClass('step-optional');
        $('#toggle-php-scripts').show();
        populateScriptsList('php', allPhpScripts);
    }
}

// Populate scripts list for display with individual replay buttons
function populateScriptsList(type, scripts) {
    var listId = type + '-scripts-list';
    var html = '';

    if (scripts.length === 0) {
        html = '<p class="no-scripts">Aucun script ' + type.toUpperCase() + ' trouvé</p>';
    } else {
        html = '<div class="scripts-table">';
        html += '<div class="scripts-header">';
        html += '<span class="script-col-file">Fichier</span>';
        html += '<span class="script-col-version">Version</span>';
        html += '<span class="script-col-status">Statut</span>';
        html += '<span class="script-col-order">Ordre</span>';
        html += '<span class="script-col-action">Action</span>';
        html += '</div>';

        scripts.forEach(function(script, index) {
            var statusClass = 'script-status-' + script.status;
            var statusText = script.status === 'newer' ? 'À exécuter' :
                           script.status === 'current' ? 'Version actuelle' : 'Déjà exécuté';

            html += '<div class="script-row ' + statusClass + '">';
            html += '<span class="script-col-file">' + script.file + '</span>';
            html += '<span class="script-col-version">v' + script.version + '</span>';
            html += '<span class="script-col-status">' + statusText + '</span>';
            html += '<span class="script-col-order">' + (index + 1) + '</span>';
            html += '<span class="script-col-action">';
            html += '<button type="button" class="button button-small button-warning" onclick="replayIndividualScript(\'' + script.file + '\', \'' + type + '\')" title="Rejouer ce script">';
            html += '<i class="icon-retry"></i> Rejouer';
            html += '</button>';
            html += '</span>';
            html += '</div>';
        });

        html += '</div>';
    }

    $('#' + listId).html(html);
}

// Toggle scripts list visibility
function toggleScriptsList(type) {
    var scriptsDiv = $('#scripts-' + type);
    var toggleBtn = $('#toggle-' + type + '-scripts');

    if (scriptsDiv.is(':visible')) {
        scriptsDiv.hide();
        toggleBtn.html('<i class="icon-list"></i> Voir Scripts');
    } else {
        scriptsDiv.show();
        toggleBtn.html('<i class="icon-list"></i> Masquer Scripts');
    }
}

// Individual script replay functionality
function replayIndividualScript(scriptFile, scriptType) {
    var confirmMessage = 'Êtes-vous sûr de vouloir rejouer le script "' + scriptFile + '" ?';

    if (!confirm(confirmMessage)) {
        return;
    }

    // Find the script in the global arrays
    var scripts = scriptType === 'sql' ? window.allSqlScripts : window.allPhpScripts;
    var script = scripts.find(function(s) { return s.file === scriptFile; });

    if (!script) {
        alert('Script non trouvé: ' + scriptFile);
        return;
    }

    // Execute individual script
    executeIndividualScript(script, scriptType);
}

// Execute individual script
function executeIndividualScript(script, scriptType) {
    var formData = new FormData();
    formData.append('csrf_token', csrfToken);
    formData.append('script_file', script.file);
    formData.append('script_type', scriptType);

    // Show loading state for the specific script
    var scriptRows = $('.script-row');
    scriptRows.each(function() {
        var fileCell = $(this).find('.script-col-file');
        if (fileCell.text() === script.file) {
            $(this).find('.script-col-action button').prop('disabled', true).html('<i class="icon-loading"></i> En cours...');
        }
    });

    $.ajax({
        url: '?controller=updates&action=replay-individual-script',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        timeout: 60000, // 1 minute timeout for individual scripts
        success: function(response) {
            handleIndividualScriptSuccess(script, scriptType, response);
        },
        error: function(xhr, status, error) {
            handleIndividualScriptError(script, scriptType, xhr, status, error);
        }
    });
}

// Skip step functionality
function skipStep(stepName) {
    var stepType = stepName.replace('execute_', '');
    var confirmMessage = 'Êtes-vous sûr de vouloir ignorer cette étape ?';

    if (!confirm(confirmMessage)) {
        return;
    }

    var skipStepName = 'skip_' + stepType.replace('_scripts', '_scripts');
    executeStep(skipStepName);
}

// Progressive workflow display
function showNextWorkflowBlock(currentBlockNumber) {
    var nextBlockNumber = currentBlockNumber + 1;
    var nextBlock = $('#step-block-' + nextBlockNumber);

    console.log('showNextWorkflowBlock: current=' + currentBlockNumber + ', next=' + nextBlockNumber);
    console.log('Next block found:', nextBlock.length > 0);

    if (nextBlock.length > 0) {
        nextBlock.show();
        console.log('Showing block ' + nextBlockNumber);

        // Enable the next step if it's not the version update block
        if (nextBlockNumber <= 3) {
            var nextStepButton = nextBlock.find('.button-primary');
            nextStepButton.prop('disabled', false);
            console.log('Enabled button for block ' + nextBlockNumber);
        }
    } else {
        console.log('No next block found for number ' + nextBlockNumber);
    }
}

// Auto-complete steps when no scripts are available
function autoCompleteStepIfNoScripts(stepName, scriptsCount) {
    if (scriptsCount === 0) {
        var statusElement = $('#status-' + stepName);
        var resultElement = $('#result-' + stepName);
        var buttonElement = $('#btn-' + stepName);

        // Mark as completed
        statusElement.html('<span class="status-badge status-completed">Terminé</span>');
        buttonElement.prop('disabled', true).html('<i class="icon-check"></i> Terminé');

        // Show completion message
        resultElement.html('<div class="alert alert-success">Aucun script à exécuter - étape terminée automatiquement</div>').show();

        // Determine current block number and show next workflow block
        var currentBlockNumber = 0;
        if (stepName === 'execute_sql_scripts') {
            currentBlockNumber = 2;
        } else if (stepName === 'execute_php_scripts') {
            currentBlockNumber = 3;
        }

        if (currentBlockNumber > 0) {
            showNextWorkflowBlock(currentBlockNumber);

            // Also trigger the next step logic
            setTimeout(function() {
                enableNextStep(stepName);
            }, 100);
        }

        return true;
    }
    return false;
}

// Execute individual workflow step
function executeStep(stepId) {
    var stepElement = $('[data-step="' + stepId + '"]');
    var button = $('#btn-' + stepId);
    var statusElement = $('#status-' + stepId);
    var resultElement = $('#result-' + stepId);

    // Disable button and show running status
    button.prop('disabled', true).html('<span class="spinner"></span> Exécution...');
    statusElement.html('<span class="status-badge status-running">En cours</span>');
    resultElement.hide();

    // Prepare request data
    var requestData = { csrf_token: csrfToken };

    // Add credentials if provided and step requires SVN
    if ((stepId === 'svn_update_engine' || stepId === 'svn_update_frontend') &&
        $('#svn-username').val() && $('#svn-password').val()) {
        requestData.username = $('#svn-username').val();
        requestData.password = $('#svn-password').val();
    }

    // Map step ID to action
    var actionMap = {
        'svn_update_engine': 'svn-update-engine',
        'svn_cleanup_engine': 'svn-cleanup-engine',
        'execute_sql_scripts': 'execute-sql-scripts',
        'execute_php_scripts': 'execute-php-scripts',
        'update_version': 'update-version',
        'svn_update_frontend': 'svn-update-frontend',
        'svn_cleanup_frontend': 'svn-cleanup-frontend',
        'replay_sql_scripts': 'replay-sql-scripts',
        'replay_php_scripts': 'replay-php-scripts',
        'skip_sql_scripts': 'skip-sql-scripts',
        'skip_php_scripts': 'skip-php-scripts'
    };

    $.ajax({
        url: '/maintenance/?controller=updates&action=' + actionMap[stepId],
        method: 'POST',
        data: requestData,
        dataType: 'json'
    })
    .done(function(response) {
        handleStepResult(stepId, response);
    })
    .fail(function(xhr) {
        var errorMsg = 'Erreur de communication avec le serveur';
        try {
            var response = JSON.parse(xhr.responseText);
            errorMsg = response.error || response.message || errorMsg;
        } catch(e) {}

        handleStepResult(stepId, {
            success: false,
            message: errorMsg,
            step: stepId
        });
    });
}

// Handle step execution result
function handleStepResult(stepId, response) {
    var button = $('#btn-' + stepId);
    var statusElement = $('#status-' + stepId);
    var resultElement = $('#result-' + stepId);
    var skipButton = $('#skip-' + stepId);

    if (response.success) {
        // Success - special handling for cleanup operations
        if (stepId === 'svn_cleanup_engine' || stepId === 'svn_cleanup_frontend') {
            // For cleanup operations, reset button to allow future cleanups
            button.html('<i class="icon-tools"></i> SVN Cleanup').prop('disabled', false).removeClass('button-primary').addClass('button-warning');
            // Don't change status element for cleanup operations
        } else {
            // Standard success handling for other operations
            statusElement.html('<span class="status-badge status-success">✓ Terminé</span>');
            button.html('<span class="status-badge status-success">✓ Terminé</span>').removeClass('button-primary').addClass('button-success');
        }

        var resultHtml = '<div class="notice notice-success">';
        resultHtml += '<h4>✓ Étape terminée avec succès</h4>';
        resultHtml += '<p>' + response.message + '</p>';

        if (response.data) {
            // Special handling for SVN cleanup operations
            if (stepId === 'svn_cleanup_engine' || stepId === 'svn_cleanup_frontend') {
                if (response.data.execution_time_ms) {
                    resultHtml += '<p><strong>⏱️ Temps d\'exécution:</strong> ' + response.data.execution_time_ms + 'ms</p>';
                }
                if (response.data.repo_path) {
                    resultHtml += '<p><strong>📁 Dépôt nettoyé:</strong> ' + response.data.repo_path + '</p>';
                }
                if (response.data.output && response.data.output.trim()) {
                    resultHtml += '<div class="svn-output">';
                    resultHtml += '<h5>📤 Sortie de la commande SVN:</h5>';
                    resultHtml += '<pre>' + response.data.output + '</pre>';
                    resultHtml += '</div>';
                }
                resultHtml += '<div class="cleanup-success-message">';
                resultHtml += '<p><strong>✅ Le dépôt SVN a été nettoyé avec succès.</strong></p>';
                resultHtml += '<p>Vous pouvez maintenant réessayer l\'opération SVN qui avait échoué.</p>';
                resultHtml += '</div>';
            } else {
                // Standard handling for other operations
                if (response.data.scripts_executed) {
                    resultHtml += '<p><strong>Scripts exécutés:</strong> ' + response.data.scripts_executed + '</p>';
                }
                if (response.data.scripts_found) {
                    resultHtml += '<p><strong>Scripts trouvés:</strong> ' + response.data.scripts_found + '</p>';
                }
            }
            if (response.data.script_details && response.data.script_details.length > 0) {
                resultHtml += '<div class="executed-scripts">';
                resultHtml += '<h5>📄 Scripts exécutés:</h5>';
                resultHtml += '<ul class="script-execution-list">';
                response.data.script_details.forEach(function(script) {
                    var statusIcon = script.status === 'newer' ? '🆕' :
                                   script.status === 'current' ? '🔄' : '📁';
                    var executionStatus = script.execution_status === 'success' ? '✅' :
                                        script.execution_status === 'failed' ? '❌' : '';
                    var timeInfo = script.execution_time_ms ? ' (' + script.execution_time_ms + 'ms)' : '';
                    resultHtml += '<li>' + statusIcon + ' ' + executionStatus + ' ' + script.file + ' (v' + script.version + ')' + timeInfo + '</li>';
                });
                resultHtml += '</ul>';
                resultHtml += '</div>';
            }

            // Show detailed execution results if available
            if (response.data.execution_results && response.data.execution_results.length > 0) {
                resultHtml += '<div class="execution-results">';
                resultHtml += '<h5>📊 Résultats d\'exécution détaillés:</h5>';

                response.data.execution_results.forEach(function(scriptResult) {
                    resultHtml += '<div class="script-execution-details">';
                    resultHtml += '<h6>📄 ' + scriptResult.script_file + '</h6>';

                    // For SQL scripts - show detailed statement results
                    if (scriptResult.detailed_results) {
                        resultHtml += '<div class="sql-execution-details">';
                        scriptResult.detailed_results.forEach(function(statementResult, index) {
                            var typeIcon = getStatementTypeIcon(statementResult.type);
                            resultHtml += '<div class="sql-statement-result">';
                            resultHtml += '<div class="statement-header">';
                            resultHtml += '<span class="statement-type">' + typeIcon + ' ' + statementResult.type + '</span>';
                            resultHtml += '<span class="statement-time">' + statementResult.execution_time_ms + 'ms</span>';
                            resultHtml += '<span class="statement-rows">Lignes affectées: ' + statementResult.affected_rows + '</span>';
                            resultHtml += '</div>';
                            resultHtml += '<div class="statement-sql"><code>' + statementResult.statement + '</code></div>';
                            resultHtml += '</div>';
                        });
                        resultHtml += '</div>';
                    }

                    // For PHP scripts - show output and results
                    if (scriptResult.output !== undefined) {
                        resultHtml += '<div class="php-execution-details">';
                        if (scriptResult.output && scriptResult.output.trim()) {
                            resultHtml += '<div class="php-output">';
                            resultHtml += '<h7>📤 Sortie du script:</h7>';
                            resultHtml += '<pre>' + scriptResult.output + '</pre>';
                            resultHtml += '</div>';
                        }
                        if (scriptResult.result !== undefined && scriptResult.result !== null) {
                            resultHtml += '<div class="php-result">';
                            resultHtml += '<h7>🔄 Valeur de retour:</h7>';
                            resultHtml += '<code>' + JSON.stringify(scriptResult.result) + '</code>';
                            resultHtml += '</div>';
                        }
                        if (scriptResult.execution_time_ms) {
                            resultHtml += '<div class="php-timing">';
                            resultHtml += '<span>⏱️ Temps d\'exécution: ' + scriptResult.execution_time_ms + 'ms</span>';
                            if (scriptResult.memory_usage) {
                                resultHtml += '<span> | 💾 Mémoire utilisée: ' + formatBytes(scriptResult.memory_usage) + '</span>';
                            }
                            resultHtml += '</div>';
                        }
                        resultHtml += '</div>';
                    }

                    resultHtml += '</div>';
                });
                resultHtml += '</div>';
            }
            if (response.data.old_version && response.data.new_version) {
                resultHtml += '<p><strong>Version:</strong> ' + response.data.old_version + ' → ' + response.data.new_version + '</p>';
            }
            if (response.data.log_file) {
                var logFileName = response.data.log_file.split('/').pop();
                resultHtml += '<div class="log-download-section">';
                resultHtml += '<p><strong>📋 Log d\'exécution:</strong></p>';
                resultHtml += '<a href="/maintenance/?controller=updates&action=download-log&file=' + encodeURIComponent(logFileName) + '" target="_blank" class="button button-secondary">📥 Télécharger le Log</a>';
                resultHtml += '</div>';
            }
        }

        resultHtml += '</div>';
        resultElement.html(resultHtml).show();

        // Enable next step (but not for cleanup operations)
        if (stepId !== 'svn_cleanup_engine' && stepId !== 'svn_cleanup_frontend') {
            enableNextStep(stepId);
        }

    } else {
        // Error
        statusElement.html('<span class="status-badge status-error">✗ Échec</span>');
        button.html('<i class="icon-retry"></i> Réessayer').prop('disabled', false);
        skipButton.show();

        var resultHtml = '<div class="notice notice-error">';
        resultHtml += '<h4>✗ Erreur lors de l\'exécution</h4>';
        resultHtml += '<p>' + response.message + '</p>';

        if (response.error && response.error !== response.message) {
            resultHtml += '<div class="error-details">' + response.error + '</div>';
        }

        if (response.data && response.data.log_file) {
            var logFileName = response.data.log_file.split('/').pop();
            resultHtml += '<div class="log-download-section error-log">';
            resultHtml += '<p><strong>📋 Log d\'erreur:</strong></p>';
            resultHtml += '<a href="/maintenance/?controller=updates&action=download-log&file=' + encodeURIComponent(logFileName) + '" target="_blank" class="button button-warning">📥 Télécharger le Log d\'Erreur</a>';
            resultHtml += '</div>';
        }

        resultHtml += '</div>';
        resultElement.html(resultHtml).show();
    }

    // Refresh system status
    refreshSystemStatus();
}

// Enable next step in workflow with progressive display
function enableNextStep(completedStepId) {
    var stepOrder = ['svn_update_engine', 'execute_sql_scripts', 'execute_php_scripts', 'update_version'];
    var currentIndex = stepOrder.indexOf(completedStepId);

    if (currentIndex >= 0 && currentIndex < stepOrder.length - 1) {
        var nextStepId = stepOrder[currentIndex + 1];

        // Show the next workflow block
        var nextBlockNumber = currentIndex + 2; // Block numbers are 1-indexed
        showNextWorkflowBlock(currentIndex + 1);

        // Special handling for SVN update completion
        if (completedStepId === 'svn_update_engine') {
            handleSvnUpdateCompletion();
            return; // Let handleSvnUpdateCompletion manage the next steps
        }

        // Special handling for SQL scripts completion
        if (completedStepId === 'execute_sql_scripts') {
            handlePhpScriptsAfterSqlCompletion();
            return; // Let handlePhpScriptsAfterSqlCompletion manage the next step
        }

        // Special handling for version update block
        if (nextStepId === 'update_version') {
            handleVersionUpdateBlock();
        } else {
            $('#btn-' + nextStepId).prop('disabled', false);
        }
    }
}

// Handle SVN update completion - auto-complete script steps if no scripts
function handleSvnUpdateCompletion() {
    // Check if SQL scripts step should be auto-completed
    var sqlScriptsCount = window.allSqlScripts ? window.allSqlScripts.length : 0;
    var applicableSqlCount = 0;
    if (window.allSqlScripts) {
        applicableSqlCount = window.allSqlScripts.filter(function(script) {
            return script.status === 'newer';
        }).length;
    }

    if (applicableSqlCount === 0) {
        // Auto-complete SQL step and check PHP step
        autoCompleteStepIfNoScripts('execute_sql_scripts', applicableSqlCount);

        // After SQL auto-completion, check PHP scripts
        setTimeout(function() {
            handlePhpScriptsAfterSqlCompletion();
        }, 200);
    } else {
        $('#btn-execute_sql_scripts').prop('disabled', false);
    }
}

// Handle PHP scripts check after SQL completion
function handlePhpScriptsAfterSqlCompletion() {
    // Check if PHP scripts step should be auto-completed
    var phpScriptsCount = window.allPhpScripts ? window.allPhpScripts.length : 0;
    var applicablePhpCount = 0;
    if (window.allPhpScripts) {
        applicablePhpCount = window.allPhpScripts.filter(function(script) {
            return script.status === 'newer';
        }).length;
    }

    if (applicablePhpCount === 0) {
        // Auto-complete PHP step
        autoCompleteStepIfNoScripts('execute_php_scripts', applicablePhpCount);
    } else {
        $('#btn-execute_php_scripts').prop('disabled', false);
    }
}

// Handle version update block display
function handleVersionUpdateBlock() {
    // Check if any scripts were actually executed
    var scriptsWereExecuted = checkIfScriptsWereExecuted();

    if (!scriptsWereExecuted) {
        // Show "already at latest version" message instead of executable button
        var versionBlock = $('#step-block-4');
        var statusElement = $('#status-update_version');
        var buttonElement = $('#btn-update_version');
        var resultElement = $('#result-update_version');

        statusElement.html('<span class="status-badge status-completed">Terminé</span>');
        buttonElement.prop('disabled', true).html('<i class="icon-check"></i> Déjà à jour');

        resultElement.html('<div class="alert alert-success"><h4>✓ Vous êtes déjà à la dernière version</h4><p>Aucune mise à jour de version nécessaire.</p></div>').show();
    } else {
        $('#btn-update_version').prop('disabled', false);
    }
}

// Check if any scripts were executed during this session
function checkIfScriptsWereExecuted() {
    // Check if SQL or PHP steps show successful execution
    var sqlCompleted = $('#status-execute_sql_scripts .status-badge').hasClass('status-success');
    var phpCompleted = $('#status-execute_php_scripts .status-badge').hasClass('status-success');

    // Check if any scripts were actually executed (not just auto-completed)
    var sqlResult = $('#result-execute_sql_scripts').html();
    var phpResult = $('#result-execute_php_scripts').html();

    var sqlExecuted = sqlResult && sqlResult.indexOf('Scripts exécutés:') > -1 && !sqlResult.indexOf('0') > -1;
    var phpExecuted = phpResult && phpResult.indexOf('Scripts exécutés:') > -1 && !phpResult.indexOf('0') > -1;

    return sqlExecuted || phpExecuted;
}

// Skip step
function skipStep(stepId) {
    if (!confirm('Êtes-vous sûr de vouloir ignorer cette étape ?')) {
        return;
    }

    var button = $('#btn-' + stepId);
    var statusElement = $('#status-' + stepId);
    var skipButton = $('#skip-' + stepId);

    statusElement.html('<span class="status-badge status-skipped">Ignoré</span>');
    button.html('Ignoré').prop('disabled', true).removeClass('button-primary').addClass('button-secondary');
    skipButton.hide();

    // Enable next step
    enableNextStep(stepId);
}

// Perform frontend update
function performFrontendUpdate() {
    if (!confirm('Êtes-vous sûr de vouloir lancer la mise à jour du frontend ?')) {
        return;
    }
    
    showProgress('Mise à jour du frontend en cours...');
    disableButtons();
    
    $.ajax({
        url: '/maintenance/?controller=updates&action=frontend-update',
        method: 'POST',
        data: { csrf_token: csrfToken },
        dataType: 'json'
    })
    .done(function(response) {
        hideProgress();
        enableButtons();
        displayResults(response, 'Frontend');
        refreshSystemStatus();
    })
    .fail(function(xhr) {
        hideProgress();
        enableButtons();
        var errorMsg = 'Erreur de communication avec le serveur';
        try {
            var response = JSON.parse(xhr.responseText);
            errorMsg = response.error || errorMsg;
        } catch(e) {}
        displayResults({success: false, message: errorMsg}, 'Frontend');
    });
}

// Show progress
function showProgress(text) {
    $('#progress-text').text(text);
    $('#update-progress').show();
    $('#results-panel').show();
}

// Hide progress
function hideProgress() {
    $('#update-progress').hide();
}

// Enable/disable buttons
function disableButtons() {
    $('#backend-update-btn, #frontend-update-btn').prop('disabled', true);
}

function enableButtons() {
    $('#backend-update-btn, #frontend-update-btn').prop('disabled', false);
}

// Display results
function displayResults(response, type) {
    var resultClass = response.success ? 'notice-success' : 'notice-error';
    var icon = response.success ? '✓' : '✗';

    var html = '<div class="notice ' + resultClass + '">';
    html += '<h4>' + icon + ' Résultat de la Mise à Jour ' + type + '</h4>';
    html += '<p>' + response.message + '</p>';

    if (response.success && response.data) {
        if (response.data.initial_version && response.data.final_version) {
            html += '<p><strong>Version:</strong> ' + response.data.initial_version + ' → ' + response.data.final_version + '</p>';
        }
        if (response.data.scripts_executed) {
            html += '<p><strong>Scripts exécutés:</strong> ' + response.data.scripts_executed + '</p>';
        }
        if (response.data.log_file) {
            html += '<p><strong>Fichier de log:</strong> <a href="/maintenance/?controller=updates&action=download-log&file=' + encodeURIComponent(response.data.log_file.split('/').pop()) + '" target="_blank" class="button button-secondary button-small">Télécharger</a></p>';
        }
    }

    html += '</div>';

    $('#update-results').html(html);
}

// Handle individual script execution success
function handleIndividualScriptSuccess(script, scriptType, response) {
    // Re-enable the button
    var scriptRows = $('.script-row');
    scriptRows.each(function() {
        var fileCell = $(this).find('.script-col-file');
        if (fileCell.text() === script.file) {
            var button = $(this).find('.script-col-action button');
            button.prop('disabled', false).html('<i class="icon-retry"></i> Rejouer');

            // Show success indicator
            if (response.success) {
                button.removeClass('button-warning').addClass('button-success');
                setTimeout(function() {
                    button.removeClass('button-success').addClass('button-warning');
                }, 3000);
            }
        }
    });

    // Show execution result with detailed information
    var message = 'Script "' + script.file + '" exécuté avec succès';
    if (response.data && response.data.execution_time) {
        message += ' (' + response.data.execution_time + ')';
    }

    showScriptExecutionResult(script.file, true, message, response.data);
}

// Handle individual script execution error
function handleIndividualScriptError(script, scriptType, xhr, status, error) {
    // Re-enable the button
    var scriptRows = $('.script-row');
    scriptRows.each(function() {
        var fileCell = $(this).find('.script-col-file');
        if (fileCell.text() === script.file) {
            var button = $(this).find('.script-col-action button');
            button.prop('disabled', false).html('<i class="icon-retry"></i> Rejouer');

            // Show error indicator
            button.removeClass('button-warning').addClass('button-danger');
            setTimeout(function() {
                button.removeClass('button-danger').addClass('button-warning');
            }, 3000);
        }
    });

    var errorMessage = 'Erreur lors de l\'exécution du script "' + script.file + '"';
    if (xhr.responseJSON && xhr.responseJSON.message) {
        errorMessage += ': ' + xhr.responseJSON.message;
    }

    showScriptExecutionResult(script.file, false, errorMessage, null);
}

// Show script execution result
function showScriptExecutionResult(scriptFile, success, message, data) {
    var resultHtml = '<div class="individual-script-result ' + (success ? 'success' : 'error') + '">';
    resultHtml += '<h5>' + (success ? '✅' : '❌') + ' ' + scriptFile + '</h5>';
    resultHtml += '<p>' + message + '</p>';

    if (success && data) {
        // For SQL scripts - show detailed statement results
        if (data.detailed_results && data.detailed_results.length > 0) {
            resultHtml += '<div class="sql-execution-details">';
            resultHtml += '<h6>📊 Détails d\'exécution SQL:</h6>';
            data.detailed_results.forEach(function(statementResult, index) {
                var typeIcon = getStatementTypeIcon(statementResult.type);
                resultHtml += '<div class="sql-statement-result">';
                resultHtml += '<div class="statement-header">';
                resultHtml += '<span class="statement-type">' + typeIcon + ' ' + statementResult.type + '</span>';
                resultHtml += '<span class="statement-time">' + statementResult.execution_time_ms + 'ms</span>';
                resultHtml += '<span class="statement-rows">Lignes affectées: ' + statementResult.affected_rows + '</span>';
                resultHtml += '</div>';
                resultHtml += '<div class="statement-sql"><code>' + statementResult.statement + '</code></div>';
                resultHtml += '</div>';
            });
            resultHtml += '</div>';
        }

        // For PHP scripts - show output and results
        if (data.output !== undefined) {
            if (data.output && data.output.trim()) {
                resultHtml += '<div class="php-output">';
                resultHtml += '<h6>📤 Sortie du script:</h6>';
                resultHtml += '<pre>' + data.output + '</pre>';
                resultHtml += '</div>';
            }
            if (data.result !== undefined && data.result !== null) {
                resultHtml += '<div class="php-result">';
                resultHtml += '<h6>🔄 Valeur de retour:</h6>';
                resultHtml += '<code>' + JSON.stringify(data.result) + '</code>';
                resultHtml += '</div>';
            }
            if (data.memory_usage) {
                resultHtml += '<div class="php-memory">';
                resultHtml += '<span>💾 Mémoire utilisée: ' + formatBytes(data.memory_usage) + '</span>';
                resultHtml += '</div>';
            }
        }
    }

    if (data && data.log_file) {
        var logFileName = data.log_file.split('/').pop();
        resultHtml += '<p><a href="?controller=updates&action=download-log&file=' + encodeURIComponent(logFileName) + '" target="_blank" class="button button-small button-secondary">📥 Télécharger le Log</a></p>';
    }

    resultHtml += '</div>';

    // Show in a temporary notification area
    var notificationArea = $('#individual-script-notifications');
    if (notificationArea.length === 0) {
        $('body').append('<div id="individual-script-notifications" style="position: fixed; top: 20px; right: 20px; z-index: 1000; max-width: 400px;"></div>');
        notificationArea = $('#individual-script-notifications');
    }

    var notification = $('<div class="script-notification">' + resultHtml + '</div>');
    notificationArea.append(notification);

    // Auto-remove after 15 seconds (increased for detailed results)
    setTimeout(function() {
        notification.fadeOut(500, function() {
            $(this).remove();
        });
    }, 15000);
}

// Helper function to get icon for SQL statement type
function getStatementTypeIcon(type) {
    switch(type) {
        case 'SELECT': return '🔍';
        case 'INSERT': return '➕';
        case 'UPDATE': return '✏️';
        case 'DELETE': return '🗑️';
        case 'CREATE': return '🏗️';
        case 'ALTER': return '🔧';
        case 'DROP': return '💥';
        default: return '📝';
    }
}

// Helper function to format bytes
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    var k = 1024;
    var sizes = ['Bytes', 'KB', 'MB', 'GB'];
    var i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>



<?php
// Include admin skin footer
require_once('admin/skin/footer.inc.php');
?>
