<?php
/**
 * Tasks Management View
 *
 * Display and manage synchronization tasks
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

// Check if this is an AJAX request
$isAjax = Maintenance_Utils_Authentication::isAjaxRequest();

if (!$isAjax) {
    // Set page title and include admin header
    define('PAGE_TITLE', $pageTitle);
    require_once('../../include/admin/skin/header.inc.php');
    ?>
    <link rel="stylesheet" href="/maintenance/assets/css/tasks.css">
    <?php
}
?>

<div>
    <p>
        <button class="button button-primary" onclick="window.location.href='/maintenance/'">Retour au dashboard</button>
    </p>

    <h2>Gestion des tâches de synchronisation</h2>
    <p>D<PERSON>marrer, arrêter et forcer l'exécution des tâches de synchronisation.</p>

    <div class="notice notice-info">
        <p><strong>Information :</strong> Les tâches permettent de synchroniser les données entre votre système et les connecteurs externes.</p>
    </div>

    <?php if (isset($successMessage) && $successMessage): ?>
        <div class="notice notice-success">
            <?php echo htmlspecialchars($successMessage); ?>
        </div>
    <?php endif; ?>

    <?php if (isset($errorMessage) && $errorMessage): ?>
        <div class="notice notice-error">
            <?php echo htmlspecialchars($errorMessage); ?>
        </div>
    <?php endif; ?>

    <!-- Debug section - Remove this after fixing -->
    <?php if (isset($_GET['debug']) && $_GET['debug'] == '1'): ?>
        <div class="notice notice-info">
            <h4>Debug: Structure des données des tâches</h4>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow: auto; max-height: 300px;">
<?php var_dump($tasks); ?>
            </pre>
        </div>
    <?php endif; ?>

    <div class="tasks-container">
        <div class="tasks-header">
            <!-- Search Section -->
            <div class="search-section">
                <div class="search-basic">
                    <input type="text" id="task-search" class="search-input" placeholder="Rechercher une tâche par nom..." />
                    <button id="toggle-advanced-search" class="button button-secondary" title="Recherche avancée">
                        <i class="icon-filter"></i> Filtres avancés
                    </button>
                    <button id="clear-search" class="button button-secondary" title="Effacer la recherche">
                        <i class="icon-clear"></i> Effacer
                    </button>
                </div>

                <div id="advanced-search" class="advanced-search" style="display: none;">
                    <div class="search-filters">
                        <div class="filter-group">
                            <label for="filter-status">Statut :</label>
                            <select id="filter-status" class="filter-select">
                                <option value="">Tous</option>
                                <option value="active">Actif</option>
                                <option value="inactive">Inactif</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="filter-category">Catégorie :</label>
                            <select id="filter-category" class="filter-select">
                                <option value="">Toutes</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="filter-date-from">Date de début :</label>
                            <input type="date" id="filter-date-from" class="filter-input" />
                        </div>

                        <div class="filter-group">
                            <label for="filter-date-to">Date de fin :</label>
                            <input type="date" id="filter-date-to" class="filter-input" />
                        </div>

                        <div class="filter-group">
                            <label for="filter-count-min">Total min :</label>
                            <input type="number" id="filter-count-min" class="filter-input" min="0" placeholder="0" />
                        </div>

                        <div class="filter-group">
                            <label for="filter-count-max">Total max :</label>
                            <input type="number" id="filter-count-max" class="filter-input" min="0" placeholder="∞" />
                        </div>

                        <div class="filter-group">
                            <label for="filter-errors">Avec erreurs :</label>
                            <select id="filter-errors" class="filter-select">
                                <option value="">Tous</option>
                                <option value="yes">Oui</option>
                                <option value="no">Non</option>
                            </select>
                        </div>
                    </div>

                    <div class="search-actions">
                        <button id="apply-filters" class="button button-primary">Appliquer les filtres</button>
                        <button id="reset-filters" class="button button-secondary">Réinitialiser</button>
                    </div>
                </div>
            </div>

            <!-- Task actions removed as requested -->
        </div>

        <?php if (isset($tasks) && is_array($tasks) && count($tasks) > 0): ?>
            <div class="tasks-grid">
                <?php
                // Check if we have the expected nested structure or a flat structure
                $isNestedStructure = false;
                foreach ($tasks as $key => $value) {
                    if (is_array($value) && !isset($value['name']) && !isset($value['active'])) {
                        $isNestedStructure = true;
                        break;
                    }
                }

                if ($isNestedStructure):
                    // Handle nested structure: tasks[category][subcategory][taskId]
                    foreach ($tasks as $category => $categoryData): ?>
                        <div class="task-category">
                            <h3 class="category-title"><?php echo htmlspecialchars($category); ?></h3>

                            <?php if (is_array($categoryData) && count($categoryData) > 0): ?>
                                <?php foreach ($categoryData as $subcategory => $subcategoryTasks): ?>
                                    <?php if (is_array($subcategoryTasks) && count($subcategoryTasks) > 0): ?>
                                        <div class="task-subcategory">
                                            <h4 class="subcategory-title"><?php echo htmlspecialchars($subcategory); ?></h4>
                                            <div class="tasks-list">
                                                <?php foreach ($subcategoryTasks as $taskId => $task): ?>
                                                <?php
                                                // Calculate execution time if both dates are available
                                                $executionTime = '';
                                                if (isset($task['date_start']) && isset($task['date_end']) &&
                                                    $task['date_start'] && $task['date_end']) {
                                                    $startTime = strtotime($task['date_start']);
                                                    $endTime = strtotime($task['date_end']);
                                                    if ($startTime && $endTime && $endTime > $startTime) {
                                                        $executionTime = ($endTime - $startTime) . 's';
                                                    }
                                                }

                                                // Determine if task is active (handle both string and boolean values)
                                                $isActive = false;
                                                if (isset($task['active'])) {
                                                    $isActive = ($task['active'] === '1' || $task['active'] === 1 || $task['active'] === true);
                                                }
                                                ?>
                                                <div class="task-card" data-task-id="<?php echo htmlspecialchars($taskId); ?>">
                                                    <div class="task-header">
                                                        <div class="task-info">
                                                            <h5 class="task-name">
                                                                <span class="task-id">#<?php echo htmlspecialchars($taskId); ?></span>
                                                                <?php echo htmlspecialchars(isset($task['name']) ? $task['name'] : 'Tâche inconnue'); ?>
                                                            </h5>
                                                            <?php if (isset($task['description']) && $task['description']): ?>
                                                                <p class="task-description"><?php echo htmlspecialchars($task['description']); ?></p>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="task-status">
                                                            <span class="status-badge <?php echo $isActive ? 'status-active' : 'status-inactive'; ?>">
                                                                <?php echo $isActive ? 'Actif' : 'Inactif'; ?>
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div class="task-details">
                                                        <div class="task-stats">
                                                            <div class="stat-item">
                                                                <span class="stat-label">⚪ Total :</span>
                                                                <span class="stat-value"><?php echo isset($task['count_total']) ? htmlspecialchars($task['count_total']) : '0'; ?></span>
                                                            </div>
                                                            <div class="stat-item">
                                                                <span class="stat-label">⏳ Restant :</span>
                                                                <span class="stat-value"><?php echo isset($task['count_remaining']) ? htmlspecialchars($task['count_remaining']) : '0'; ?></span>
                                                            </div>
                                                            <div class="stat-item">
                                                                <span class="stat-label">❌ Erreurs :</span>
                                                                <span class="stat-value"><?php echo isset($task['count_fail']) ? htmlspecialchars($task['count_fail']) : '0'; ?></span>
                                                            </div>
                                                        </div>

                                                        <?php if (isset($task['date_start']) && $task['date_start']): ?>
                                                            <div class="detail-item">
                                                                <span class="detail-label">📅 Dernier lancement :</span>
                                                                <span class="detail-value"><?php echo htmlspecialchars($task['date_start']); ?></span>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if ($executionTime): ?>
                                                            <div class="detail-item">
                                                                <span class="detail-label">⏲ Temps d'exécution :</span>
                                                                <span class="detail-value"><?php echo htmlspecialchars($executionTime); ?></span>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if (isset($task['date_end']) && $task['date_end']): ?>
                                                            <div class="detail-item">
                                                                <span class="detail-label">🏁 Fin d'exécution :</span>
                                                                <span class="detail-value"><?php echo htmlspecialchars($task['date_end']); ?></span>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>

                                                    <div class="task-actions">
                                                        <button class="task-toggle button button-sm <?php echo $isActive ? 'button-danger' : 'button-success'; ?>"
                                                                data-task-id="<?php echo htmlspecialchars($taskId); ?>"
                                                                data-active="<?php echo $isActive ? '1' : '0'; ?>">
                                                            <?php echo $isActive ? 'Arrêter' : 'Démarrer'; ?>
                                                        </button>

                                                        <?php if ($isActive): ?>
                                                            <button class="task-force button button-sm button-warning"
                                                                    data-task-id="<?php echo htmlspecialchars($taskId); ?>">
                                                                Forcer
                                                            </button>
                                                        <?php endif; ?>


                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="no-tasks">Aucune tâche dans cette catégorie.</p>
                        <?php endif; ?>
                    </div>
                <?php endforeach;
                else:
                    // Handle flat structure: tasks[taskId] = task_data
                    ?>
                    <div class="task-category">
                        <h3 class="category-title">Toutes les tâches</h3>
                        <div class="task-subcategory">
                            <h4 class="subcategory-title">Tâches disponibles</h4>
                            <div class="tasks-list">
                                <?php foreach ($tasks as $taskId => $task): ?>
                                    <?php
                                    // Calculate execution time if both dates are available
                                    $executionTime = '';
                                    if (isset($task['date_start']) && isset($task['date_end']) &&
                                        $task['date_start'] && $task['date_end']) {
                                        $startTime = strtotime($task['date_start']);
                                        $endTime = strtotime($task['date_end']);
                                        if ($startTime && $endTime && $endTime > $startTime) {
                                            $executionTime = ($endTime - $startTime) . 's';
                                        }
                                    }

                                    // Determine if task is active (handle both string and boolean values)
                                    $isActive = false;
                                    if (isset($task['active'])) {
                                        $isActive = ($task['active'] === '1' || $task['active'] === 1 || $task['active'] === true);
                                    }
                                    ?>
                                    <div class="task-card" data-task-id="<?php echo htmlspecialchars($taskId); ?>">
                                        <div class="task-header">
                                            <div class="task-info">
                                                <h5 class="task-name">
                                                    <span class="task-id">#<?php echo htmlspecialchars($taskId); ?></span>
                                                    <?php echo htmlspecialchars(isset($task['name']) ? $task['name'] : 'Tâche inconnue'); ?>
                                                </h5>
                                                <?php if (isset($task['description']) && $task['description']): ?>
                                                    <p class="task-description"><?php echo htmlspecialchars($task['description']); ?></p>
                                                <?php endif; ?>
                                            </div>
                                            <div class="task-status">
                                                <span class="status-badge <?php echo $isActive ? 'status-active' : 'status-inactive'; ?>">
                                                    <?php echo $isActive ? 'Actif' : 'Inactif'; ?>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="task-details">
                                            <div class="task-stats">
                                                <div class="stat-item">
                                                    <span class="stat-label">⚪ Total :</span>
                                                    <span class="stat-value"><?php echo isset($task['count_total']) ? htmlspecialchars($task['count_total']) : '0'; ?></span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">⏳ Restant :</span>
                                                    <span class="stat-value"><?php echo isset($task['count_remaining']) ? htmlspecialchars($task['count_remaining']) : '0'; ?></span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">❌ Erreurs :</span>
                                                    <span class="stat-value"><?php echo isset($task['count_fail']) ? htmlspecialchars($task['count_fail']) : '0'; ?></span>
                                                </div>
                                            </div>

                                            <?php if (isset($task['date_start']) && $task['date_start']): ?>
                                                <div class="detail-item">
                                                    <span class="detail-label">📅 Dernier lancement :</span>
                                                    <span class="detail-value"><?php echo htmlspecialchars($task['date_start']); ?></span>
                                                </div>
                                            <?php endif; ?>

                                            <?php if ($executionTime): ?>
                                                <div class="detail-item">
                                                    <span class="detail-label">⏲ Temps d'exécution :</span>
                                                    <span class="detail-value"><?php echo htmlspecialchars($executionTime); ?></span>
                                                </div>
                                            <?php endif; ?>

                                            <?php if (isset($task['date_end']) && $task['date_end']): ?>
                                                <div class="detail-item">
                                                    <span class="detail-label">🏁 Fin d'exécution :</span>
                                                    <span class="detail-value"><?php echo htmlspecialchars($task['date_end']); ?></span>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="task-actions">
                                            <button class="task-toggle button button-sm <?php echo $isActive ? 'button-danger' : 'button-success'; ?>"
                                                    data-task-id="<?php echo htmlspecialchars($taskId); ?>"
                                                    data-active="<?php echo $isActive ? '1' : '0'; ?>">
                                                <?php echo $isActive ? 'Arrêter' : 'Démarrer'; ?>
                                            </button>

                                            <?php if ($isActive): ?>
                                                <button class="task-force button button-sm button-warning"
                                                        data-task-id="<?php echo htmlspecialchars($taskId); ?>">
                                                    Forcer
                                                </button>
                                            <?php endif; ?>


                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="no-tasks-message">
                <p>Aucune tâche disponible.</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Loading indicator -->
    <div id="loading-indicator" class="loading-indicator" style="display: none;">
        <div class="spinner"></div>
        <span>Chargement...</span>
    </div>
</div>

<?php if (!$isAjax) {
    ?>
    <script>
        $(document).ready(function() {
            initializeTasksPage();
        });

        function initializeTasksPage() {
            // Initialize search functionality
            initializeSearch();

            // Bind task toggle buttons
            $('.task-toggle').on('click', function() {
                const taskId = $(this).data('task-id');
                const currentActive = $(this).data('active') === '1';
                const newActive = !currentActive;

                toggleTask(taskId, newActive);
            });

            // Bind force task buttons
            $('.task-force').on('click', function() {
                const taskId = $(this).data('task-id');
                forceTask(taskId);
            });

            // Global action buttons removed as requested
        }

        function initializeSearch() {
            // Populate category filter
            populateCategoryFilter();

            // Basic search - real-time filtering
            $('#task-search').on('input', function() {
                filterTasks();
            });

            // Advanced search toggle
            $('#toggle-advanced-search').on('click', function() {
                $('#advanced-search').toggle();
                $(this).toggleClass('active');
            });

            // Clear search
            $('#clear-search').on('click', function() {
                clearAllFilters();
            });

            // Apply filters
            $('#apply-filters').on('click', function() {
                filterTasks();
            });

            // Reset filters
            $('#reset-filters').on('click', function() {
                resetAdvancedFilters();
            });

            // Filter change events
            $('.filter-select, .filter-input').on('change', function() {
                if ($('#advanced-search').is(':visible')) {
                    filterTasks();
                }
            });
        }

        function toggleTask(taskId, active) {
            showLoading();

            // Show immediate feedback for stop actions
            if (!active) {
                showSuccessMessage('Commande d\'arrêt envoyée pour la tâche #' + taskId);
            }

            $.ajax({
                url: '/maintenance/?controller=tasks&action=toggle-task',
                method: 'POST',
                data: {
                    task_id: taskId,
                    active: active ? 1 : 0,
                    csrf_token: '<?php echo $csrfToken; ?>'
                },
                dataType: 'json'
            })
            .done(function(response) {
                if (response.success) {
                    if (!active) {
                        showSuccessMessage('Tâche #' + taskId + ' arrêtée avec succès');
                    }
                    // Delay reload slightly to show the success message
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    alert('Erreur: ' + (response.error || 'Erreur inconnue'));
                }
            })
            .fail(function() {
                alert('Erreur de communication avec le serveur');
            })
            .always(function() {
                hideLoading();
            });
        }

        function forceTask(taskId) {
            if (!confirm('Êtes-vous sûr de vouloir forcer l\'exécution de cette tâche ?')) {
                return;
            }

            showLoading();

            $.ajax({
                url: '/maintenance/?controller=tasks&action=force-task',
                method: 'POST',
                data: {
                    task_id: taskId,
                    csrf_token: '<?php echo $csrfToken; ?>'
                },
                dataType: 'json'
            })
            .done(function(response) {
                if (response.success) {
                    alert('Tâche forcée avec succès');
                    location.reload();
                } else {
                    alert('Erreur: ' + (response.error || 'Erreur inconnue'));
                }
            })
            .fail(function() {
                alert('Erreur de communication avec le serveur');
            })
            .always(function() {
                hideLoading();
            });
        }

        function showSuccessMessage(message) {
            // Remove any existing success messages
            $('.success-feedback').remove();

            // Create and show success message
            const successDiv = $('<div class="notice notice-success success-feedback" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">' +
                '<p><strong>Succès:</strong> ' + message + '</p>' +
                '</div>');

            $('body').append(successDiv);

            // Auto-hide after 3 seconds
            setTimeout(function() {
                successDiv.fadeOut(500, function() {
                    $(this).remove();
                });
            }, 3000);
        }

        function showLoading() {
            $('#loading-indicator').show();
        }

        function hideLoading() {
            $('#loading-indicator').hide();
        }

        // Search and filter functions
        function populateCategoryFilter() {
            const categories = new Set();
            $('.task-category .category-title').each(function() {
                categories.add($(this).text().trim());
            });

            const categorySelect = $('#filter-category');
            categorySelect.empty().append('<option value="">Toutes</option>');

            categories.forEach(function(category) {
                categorySelect.append('<option value="' + category + '">' + category + '</option>');
            });
        }

        function filterTasks() {
            const searchTerm = $('#task-search').val().toLowerCase();
            const statusFilter = $('#filter-status').val();
            const categoryFilter = $('#filter-category').val();
            const dateFrom = $('#filter-date-from').val();
            const dateTo = $('#filter-date-to').val();
            const countMin = parseInt($('#filter-count-min').val()) || 0;
            const countMax = parseInt($('#filter-count-max').val()) || Infinity;
            const errorsFilter = $('#filter-errors').val();

            let visibleCount = 0;

            $('.task-card').each(function() {
                const $card = $(this);
                const taskName = $card.find('.task-name').text().toLowerCase();
                const isActive = $card.find('.status-active').length > 0;
                const categoryName = $card.closest('.task-category').find('.category-title').text().trim();

                // Get task stats
                const totalCount = parseInt($card.find('.stat-value').eq(0).text()) || 0;
                const errorCount = parseInt($card.find('.stat-value').eq(2).text()) || 0;

                // Get task date
                const dateText = $card.find('.detail-value').first().text();
                const taskDate = dateText ? new Date(dateText) : null;

                let shouldShow = true;

                // Basic search filter
                if (searchTerm && !taskName.includes(searchTerm)) {
                    shouldShow = false;
                }

                // Status filter
                if (statusFilter) {
                    if (statusFilter === 'active' && !isActive) shouldShow = false;
                    if (statusFilter === 'inactive' && isActive) shouldShow = false;
                }

                // Category filter
                if (categoryFilter && categoryName !== categoryFilter) {
                    shouldShow = false;
                }

                // Date range filter
                if (dateFrom && taskDate && taskDate < new Date(dateFrom)) {
                    shouldShow = false;
                }
                if (dateTo && taskDate && taskDate > new Date(dateTo)) {
                    shouldShow = false;
                }

                // Count range filter
                if (totalCount < countMin || totalCount > countMax) {
                    shouldShow = false;
                }

                // Errors filter
                if (errorsFilter) {
                    if (errorsFilter === 'yes' && errorCount === 0) shouldShow = false;
                    if (errorsFilter === 'no' && errorCount > 0) shouldShow = false;
                }

                if (shouldShow) {
                    $card.show();
                    visibleCount++;
                } else {
                    $card.hide();
                }
            });

            // Show/hide categories and subcategories based on visible tasks
            $('.task-category').each(function() {
                const $category = $(this);
                const visibleTasks = $category.find('.task-card:visible').length;

                if (visibleTasks > 0) {
                    $category.show();
                    // Show subcategories that have visible tasks
                    $category.find('.task-subcategory').each(function() {
                        const $subcategory = $(this);
                        const visibleSubTasks = $subcategory.find('.task-card:visible').length;
                        if (visibleSubTasks > 0) {
                            $subcategory.show();
                        } else {
                            $subcategory.hide();
                        }
                    });
                } else {
                    $category.hide();
                }
            });

            // Update results count
            updateResultsCount(visibleCount);
        }

        function clearAllFilters() {
            $('#task-search').val('');
            resetAdvancedFilters();
            $('.task-card, .task-category, .task-subcategory').show();
            updateResultsCount($('.task-card').length);
        }

        function resetAdvancedFilters() {
            $('#filter-status').val('');
            $('#filter-category').val('');
            $('#filter-date-from').val('');
            $('#filter-date-to').val('');
            $('#filter-count-min').val('');
            $('#filter-count-max').val('');
            $('#filter-errors').val('');
            filterTasks();
        }

        function updateResultsCount(count) {
            let $counter = $('#results-counter');
            if ($counter.length === 0) {
                $counter = $('<div id="results-counter" class="results-counter"></div>');
                $('.tasks-header').append($counter);
            }

            const total = $('.task-card').length;
            $counter.text(count + ' tâche(s) affichée(s) sur ' + total);
        }
    </script>
    <?php
    require_once('../../include/admin/skin/footer.inc.php');
}
?>