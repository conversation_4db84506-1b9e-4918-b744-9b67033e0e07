<?php
/**
 * Log File View - Refactored
 *
 * Simplified individual log file viewer for both maintenance and system logs
 *
 * @package Maintenance\Views
 * <AUTHOR> Team
 */

// Prevent direct access
if (!defined('MAINTENANCE_ACCESS')) {
    http_response_code(403);
    exit('Direct access forbidden');
}

// Check if this is an AJAX request
$isAjax = Maintenance_Utils_Authentication::isAjaxRequest();

if (!$isAjax) {
    // Set page title and include admin header
    define('PAGE_TITLE', 'Visualisation du log - Maintenance');
    require_once('admin/skin/header.inc.php');
    ?>
    <link rel="stylesheet" href="/maintenance/assets/css/logs.css">
    <?php
}
?>

<div class="maintenance-logs-container">
    <div class="notice notice-super-admin">
        <?php echo _("Cette interface n'est visible que par les super-administrateurs."); ?>
    </div>

    <!-- Header Section -->
    <div class="logs-header">
        <h2>Visualisation du log: <?php echo htmlspecialchars($filename); ?></h2>
        <div class="header-actions">
            <?php if ($source === 'maintenance'): ?>
                <a href="/maintenance/?controller=logs&action=download&file=<?php echo urlencode($filename); ?>&source=<?php echo $source; ?>"
                   class="button button-secondary">Télécharger</a>
                <button id="delete-log" class="button button-danger"
                        data-filename="<?php echo htmlspecialchars($filename); ?>"
                        data-source="<?php echo htmlspecialchars($source); ?>">Supprimer</button>
            <?php endif; ?>
            <a href="/maintenance/?controller=logs" class="button button-primary">
                Retour au dashboard
            </a>
        </div>
    </div>

    <!-- File Information Section -->
    <div class="log-info-section">
        <h3>Informations</h3>
        <div class="info-grid">
            <div class="info-item">
                <strong>Source:</strong> <?php echo $source === 'maintenance' ? 'Logs de maintenance' : 'Logs système'; ?>
            </div>
            <div class="info-item">
                <strong>Fichier:</strong> <?php echo htmlspecialchars($filename); ?>
            </div>
            <?php if ($source === 'maintenance' && isset($logData['file_info'])): ?>
                <div class="info-item">
                    <strong>Taille:</strong> <?php echo htmlspecialchars($logData['file_info']['size_formatted']); ?>
                </div>
                <div class="info-item">
                    <strong>Date:</strong> <?php echo htmlspecialchars($logData['file_info']['modified_date']); ?>
                </div>
                <div class="info-item">
                    <strong>Format:</strong>
                    <?php if ($logData['is_valid_json']): ?>
                        <span class="badge badge-success">JSON valide</span>
                    <?php else: ?>
                        <span class="badge badge-warning">JSON invalide</span>
                    <?php endif; ?>
                </div>
            <?php elseif ($source === 'system'): ?>
                <div class="info-item">
                    <strong>Lignes:</strong> <?php echo isset($logData['line_count']) ? $logData['line_count'] : 'N/A'; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Content Display -->
    <div class="log-content-section">
        <h3>Contenu</h3>

        <?php if ($source === 'maintenance' && !$logData['is_valid_json']): ?>
            <div class="notice notice-warning">
                <strong>Attention:</strong> <?php echo htmlspecialchars($logData['json_error']); ?>
            </div>
        <?php endif; ?>

        <div class="content-container">
            <?php if ($source === 'maintenance' && $logData['is_valid_json']): ?>
                <!-- Formatted JSON for maintenance logs -->
                <pre class="log-content"><code><?php
                    if (function_exists('json_encode') && defined('JSON_PRETTY_PRINT')) {
                        echo htmlspecialchars(json_encode($logData['json_data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                    } else {
                        echo htmlspecialchars(json_encode($logData['json_data']));
                    }
                ?></code></pre>
            <?php else: ?>
                <!-- Raw content for system logs or invalid JSON -->
                <pre class="log-content"><code><?php echo htmlspecialchars($logData['raw_content']); ?></code></pre>
            <?php endif; ?>
        </div>

        <div class="content-actions">
            <button id="copy-content" class="button button-secondary">Copier le contenu</button>
        </div>
    </div>
</div>

<script>
// Simplified JavaScript for log viewing
document.addEventListener('DOMContentLoaded', function() {
    // Copy content functionality
    var copyButton = document.getElementById('copy-content');
    if (copyButton) {
        copyButton.addEventListener('click', function() {
            var content = document.querySelector('.log-content code').textContent;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(content).then(function() {
                    alert('Contenu copié dans le presse-papiers');
                });
            } else {
                // Fallback for older browsers
                var textArea = document.createElement('textarea');
                textArea.value = content;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('Contenu copié dans le presse-papiers');
            }
        });
    }

    // Delete functionality for maintenance logs only
    var deleteButton = document.getElementById('delete-log');
    if (deleteButton) {
        deleteButton.addEventListener('click', function() {
            var filename = this.getAttribute('data-filename');
            var source = this.getAttribute('data-source');

            if (confirm('Êtes-vous sûr de vouloir supprimer le fichier "' + filename + '" ?')) {
                var formData = new FormData();
                formData.append('file', filename);
                formData.append('source', source);
                formData.append('csrf_token', '<?php echo $csrfToken; ?>');

                fetch('/maintenance/?controller=logs&action=delete', {
                    method: 'POST',
                    body: formData
                })
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    if (data.success) {
                        alert('Fichier supprimé avec succès');
                        window.location.href = '/maintenance/?controller=logs';
                    } else {
                        alert('Erreur: ' + (data.error || 'Erreur inconnue'));
                    }
                })
                .catch(function(error) {
                    alert('Erreur de communication avec le serveur');
                });
            }
        });
    }
});
</script>

<?php
if (!$isAjax) {
    require_once('admin/skin/footer.inc.php');
}
?>
