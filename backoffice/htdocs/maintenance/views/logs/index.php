<?php
/**
 * Logs Index View - Refactored
 *
 * Simplified logs interface focusing on maintenance and system logs
 * Clean, functional design prioritizing usability
 *
 * @package Maintenance\Views
 * <AUTHOR> Team
 */

// Prevent direct access
if (!defined('MAINTENANCE_ACCESS')) {
    http_response_code(403);
    exit('Direct access forbidden');
}

// Check if this is an AJAX request
$isAjax = Maintenance_Utils_Authentication::isAjaxRequest();

if (!$isAjax) {
    // Set page title and include admin header
    define('PAGE_TITLE', 'Visualiseur de logs - Maintenance');
    require_once('admin/skin/header.inc.php');
    ?>
    <link rel="stylesheet" href="/maintenance/assets/css/logs.css">
    <?php
}
?>

<div class="maintenance-logs-container">
    <div class="notice notice-super-admin">
        <?php echo _("Cette interface n'est visible que par les super-administrateurs."); ?>
    </div>

    <!-- Header Section -->
    <div class="logs-header">
        <h2>Visualiseur de logs</h2>
        <div class="header-actions">
            <button id="refresh-logs" class="button button-secondary">
                Actualiser
            </button>
            <a href="/maintenance/" class="button button-primary">
                Retour au dashboard
            </a>
        </div>
    </div>

    <!-- Maintenance Logs Section -->
    <div class="logs-section">
        <h3>Logs de maintenance</h3>
        <p>Logs générés par les opérations de mise à jour et de maintenance</p>

        <?php if (empty($maintenanceLogs)): ?>
            <div class="empty-state">
                <p>Aucun log de maintenance trouvé.</p>
            </div>
        <?php else: ?>
            <div class="logs-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Fichier</th>
                            <th>Taille</th>
                            <th>Date</th>
                            <th>Aperçu</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($maintenanceLogs as $log): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($log['filename']); ?></strong>
                                    <?php if (!$log['is_valid_json']): ?>
                                        <span class="badge badge-warning">JSON invalide</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($log['size_formatted']); ?></td>
                                <td>
                                    <div><?php echo htmlspecialchars($log['modified_date']); ?></div>
                                    <small><?php echo htmlspecialchars($log['modified_relative']); ?></small>
                                </td>
                                <td>
                                    <span title="<?php echo htmlspecialchars($log['preview']); ?>">
                                        <?php echo htmlspecialchars(substr($log['preview'], 0, 60)) . (strlen($log['preview']) > 60 ? '...' : ''); ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="/maintenance/?controller=logs&action=view&file=<?php echo urlencode($log['filename']); ?>&source=maintenance"
                                       class="button button-sm button-primary">Voir</a>
                                    <a href="/maintenance/?controller=logs&action=download&file=<?php echo urlencode($log['filename']); ?>&source=maintenance"
                                       class="button button-sm button-secondary">Télécharger</a>
                                    <button class="button button-sm button-danger delete-log"
                                            data-filename="<?php echo htmlspecialchars($log['filename']); ?>"
                                            data-source="maintenance">Supprimer</button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <!-- System Logs Section -->
    <div class="logs-section">
        <h3>Logs système (extraits pertinents)</h3>
        <p>Entrées du journal système liées à la maintenance</p>

        <?php if (empty($systemLogs)): ?>
            <div class="empty-state">
                <p>Aucune entrée système pertinente trouvée.</p>
            </div>
        <?php else: ?>
            <div class="logs-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Hôte</th>
                            <th>Message</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($systemLogs as $log): ?>
                            <tr>
                                <td>
                                    <div><?php echo htmlspecialchars($log['date']); ?></div>
                                    <small><?php echo htmlspecialchars($log['relative']); ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($log['hostname']); ?></td>
                                <td>
                                    <span title="<?php echo htmlspecialchars($log['message']); ?>">
                                        <?php echo htmlspecialchars(substr($log['message'], 0, 80)) . (strlen($log['message']) > 80 ? '...' : ''); ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="/maintenance/?controller=logs&action=view&file=syslog_excerpt&source=system"
                                       class="button button-sm button-primary">Voir extraits</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h4>Confirmer la suppression</h4>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <p id="delete-message">Êtes-vous sûr de vouloir supprimer ce fichier ?</p>
        </div>
        <div class="modal-footer">
            <button id="confirm-delete" class="button button-danger">Supprimer</button>
            <button id="cancel-delete" class="button button-secondary">Annuler</button>
        </div>
    </div>
</div>

<script>
// Set CSRF token for JavaScript
window.csrfToken = '<?php echo $csrfToken; ?>';

// Simplified JavaScript for logs interface
document.addEventListener('DOMContentLoaded', function() {
    var currentCsrfToken = window.csrfToken;

    // Refresh logs
    document.getElementById('refresh-logs').addEventListener('click', function() {
        location.reload();
    });

    // Delete log functionality with fixed CSRF handling
    document.querySelectorAll('.delete-log').forEach(function(button) {
        button.addEventListener('click', function() {
            var filename = this.getAttribute('data-filename');
            var source = this.getAttribute('data-source');

            if (confirm('Êtes-vous sûr de vouloir supprimer le fichier "' + filename + '" ?')) {
                var formData = new FormData();
                formData.append('file', filename);
                formData.append('source', source);
                formData.append('csrf_token', currentCsrfToken);

                fetch('/maintenance/?controller=logs&action=delete', {
                    method: 'POST',
                    body: formData
                })
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    if (data.success) {
                        // Update CSRF token for next operation
                        if (data.new_csrf_token) {
                            currentCsrfToken = data.new_csrf_token;
                            window.csrfToken = data.new_csrf_token;
                        }
                        alert('Fichier supprimé avec succès');
                        location.reload();
                    } else {
                        alert('Erreur: ' + (data.error || 'Erreur inconnue'));
                    }
                })
                .catch(function(error) {
                    alert('Erreur de communication avec le serveur');
                });
            }
        });
    });
});
</script>

<?php
if (!$isAjax) {
    require_once('admin/skin/footer.inc.php');
}
?>
