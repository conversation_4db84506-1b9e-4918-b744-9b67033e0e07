<?php
/**
 * Version Manager Utility Class
 *
 * Manages Riashop version information in cfg_overrides table
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Utils_VersionManager
{
    private $db;
    private $logger;

    /**
     * Constructor
     *
     * @param Maintenance_Utils_Logger $logger
     */
    public function __construct($logger)
    {
        $this->db = Maintenance_Utils_Database::getInstance();
        $this->logger = $logger;
    }

    /**
     * Get current Riashop version
     *
     * @return string
     */
    public function getCurrentVersion()
    {
        global $config;
        
        $tenantId = isset($config['tnt_id']) ? $config['tnt_id'] : 1;
        $versionKey = defined('RIASHOP_VERSION_CONFIG_KEY') ? RIASHOP_VERSION_CONFIG_KEY : 'RIASHOP_VERSION';
        
        $query = "SELECT ovr_value FROM cfg_overrides 
                  WHERE ovr_tnt_id = '" . $this->db->escape($tenantId) . "' 
                  AND ovr_wst_id = 0 
                  AND ovr_usr_id = 0 
                  AND ovr_var_code = '" . $this->db->escape($versionKey) . "'";
        
        $result = $this->db->query($query);
        $row = $this->db->fetchAssoc($result);
        
        $version = $row ? $row['ovr_value'] : '1.000';
        
        $this->logger->info('version_management', 'Current version retrieved', array(
            'version' => $version,
            'tenant_id' => $tenantId,
            'version_key' => $versionKey
        ));
        
        return $version;
    }

    /**
     * Update Riashop version
     *
     * @param string $newVersion
     * @return bool
     */
    public function updateVersion($newVersion)
    {
        global $config;
        
        $tenantId = $config['tnt_id'];
        $versionKey = defined('RIASHOP_VERSION_CONFIG_KEY') ? RIASHOP_VERSION_CONFIG_KEY : 'RIASHOP_VERSION';
        
        // Check if override exists
        $checkQuery = "SELECT ovr_value FROM cfg_overrides 
                       WHERE ovr_tnt_id = '" . $this->db->escape($tenantId) . "' 
                       AND ovr_wst_id = 0 
                       AND ovr_usr_id = 0 
                       AND ovr_var_code = '" . $this->db->escape($versionKey) . "'";
        
        $checkResult = $this->db->query($checkQuery);
        $exists = $this->db->fetchAssoc($checkResult);
        
        if ($exists) {
            // Update existing override
            $updateQuery = "UPDATE cfg_overrides 
                           SET ovr_value = '" . $this->db->escape($newVersion) . "' 
                           WHERE ovr_tnt_id = '" . $this->db->escape($tenantId) . "' 
                           AND ovr_wst_id = 0 
                           AND ovr_usr_id = 0 
                           AND ovr_var_code = '" . $this->db->escape($versionKey) . "'";
            
            $result = $this->db->query($updateQuery);
        } else {
            // Insert new override
            $insertQuery = "INSERT INTO cfg_overrides (ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value) 
                           VALUES ('" . $this->db->escape($tenantId) . "', 0, 0, '" . $this->db->escape($versionKey) . "', '" . $this->db->escape($newVersion) . "')";
            
            $result = $this->db->query($insertQuery);
        }
        
        if ($result) {
            $this->logger->info('version_management', 'Version updated successfully', array(
                'old_version' => $this->getCurrentVersion(),
                'new_version' => $newVersion,
                'tenant_id' => $tenantId,
                'action' => $exists ? 'update' : 'insert'
            ));
            return true;
        } else {
            $this->logger->error('version_management', 'Failed to update version', array(
                'new_version' => $newVersion,
                'tenant_id' => $tenantId,
                'error' => mysql_error()
            ));
            return false;
        }
    }

    /**
     * Compare two version strings numerically
     *
     * @param string $version1
     * @param string $version2
     * @return int -1 if version1 < version2, 0 if equal, 1 if version1 > version2
     */
    public function compareVersions($version1, $version2)
    {
        $v1Parts = explode('.', $version1);
        $v2Parts = explode('.', $version2);
        
        // Pad arrays to same length
        $maxLength = max(count($v1Parts), count($v2Parts));
        $v1Parts = array_pad($v1Parts, $maxLength, '0');
        $v2Parts = array_pad($v2Parts, $maxLength, '0');
        
        for ($i = 0; $i < $maxLength; $i++) {
            $v1Num = (int) $v1Parts[$i];
            $v2Num = (int) $v2Parts[$i];
            
            if ($v1Num < $v2Num) {
                return -1;
            } elseif ($v1Num > $v2Num) {
                return 1;
            }
        }
        
        return 0;
    }

    /**
     * Check if version1 is greater than version2
     *
     * @param string $version1
     * @param string $version2
     * @return bool
     */
    public function isVersionGreater($version1, $version2)
    {
        return $this->compareVersions($version1, $version2) > 0;
    }
}
