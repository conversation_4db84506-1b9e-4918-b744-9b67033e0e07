<?php
/**
 * Database Utility Class
 *
 * Provides database abstraction and helper methods
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Utils_Database
{
    private static $instance = null;
    private $connection = null;

    /**
     * Private constructor for singleton pattern
     */
    private function __construct()
    {
        // Database connection will be handled by existing RiaShop infrastructure
        // This class provides abstraction over the existing mysql_* functions
    }

    /**
     * Get singleton instance
     *
     * @return self
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Execute a query and return result
     *
     * @param string $query
     * @param array $params
     * @return mixed
     */
    public function query($query, $params = array())
    {
        try {
            // For now, we'll use the existing mysql functions
            // In a future refactor, this should be replaced with PDO or mysqli

            if (!empty($params)) {
                foreach ($params as $key => $value) {
                    $escapedValue = is_string($value) ? "'" . mysql_real_escape_string($value) . "'" : $value;
                    $query = str_replace(':' . $key, $escapedValue, $query);
                }
            }

            $result = mysql_query($query);

            if ($result === false) {
                throw new Exception('Database query failed: ' . mysql_error());
            }

            return $result;
        } catch (Exception $e) {
            error_log('Database Error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Fetch single row as associative array
     *
     * @param mixed $result
     * @return array|null
     */
    public function fetchAssoc($result)
    {
        if (is_resource($result)) {
            $row = mysql_fetch_assoc($result);
            return $row !== false ? $row : null;
        }
        return null;
    }

    /**
     * Fetch all rows as associative array
     *
     * @param mixed $result
     * @return array
     */
    public function fetchAllAssoc($result)
    {
        $rows = array();
        if (is_resource($result)) {
            while ($row = mysql_fetch_assoc($result)) {
                $rows[] = $row;
            }
        }
        return $rows;
    }

    /**
     * Get number of rows in result
     *
     * @param mixed $result
     * @return int
     */
    public function numRows($result)
    {
        return is_resource($result) ? mysql_num_rows($result) : 0;
    }

    /**
     * Get last insert ID
     *
     * @return int
     */
    public function lastInsertId()
    {
        return mysql_insert_id();
    }

    /**
     * Get affected rows count
     *
     * @return int
     */
    public function affectedRows()
    {
        return mysql_affected_rows();
    }

    /**
     * Escape string for SQL
     *
     * @param string $string
     * @return string
     */
    public function escape($string)
    {
        return mysql_real_escape_string($string);
    }

    /**
     * Begin transaction
     *
     * @return bool
     */
    public function beginTransaction()
    {
        return $this->query('START TRANSACTION') !== false;
    }

    /**
     * Commit transaction
     *
     * @return bool
     */
    public function commit()
    {
        return $this->query('COMMIT') !== false;
    }

    /**
     * Rollback transaction
     *
     * @return bool
     */
    public function rollback()
    {
        return $this->query('ROLLBACK') !== false;
    }

    /**
     * Execute query with transaction
     *
     * @param callable $callback
     * @return mixed
     * @throws Exception
     */
    public function transaction($callback)
    {
        $this->beginTransaction();

        try {
            $result = $callback($this);
            $this->commit();
            return $result;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * Check if table exists
     *
     * @param string $tableName
     * @return bool
     */
    public function tableExists($tableName)
    {
        $query = "SHOW TABLES LIKE :table";
        $result = $this->query($query, array('table' => $tableName));
        return $this->numRows($result) > 0;
    }

    /**
     * Get table columns
     *
     * @param string $tableName
     * @return array
     */
    public function getTableColumns($tableName)
    {
        $query = "SHOW COLUMNS FROM `{$tableName}`";
        $result = $this->query($query);
        return $this->fetchAllAssoc($result);
    }

    /**
     * Build WHERE clause from conditions
     *
     * @param array $conditions
     * @return string
     */
    public function buildWhereClause($conditions)
    {
        if (empty($conditions)) {
            return '';
        }

        $clauses = array();
        foreach ($conditions as $field => $value) {
            if (is_array($value)) {
                $placeholders = array();
                foreach ($value as $v) {
                    $placeholders[] = "'" . $this->escape($v) . "'";
                }
                $clauses[] = "`{$field}` IN (" . implode(', ', $placeholders) . ")";
            } elseif ($value === null) {
                $clauses[] = "`{$field}` IS NULL";
            } else {
                $clauses[] = "`{$field}` = '" . $this->escape($value) . "'";
            }
        }

        return 'WHERE ' . implode(' AND ', $clauses);
    }

    /**
     * Build ORDER BY clause
     *
     * @param array $orderBy
     * @return string
     */
    public function buildOrderByClause($orderBy)
    {
        if (empty($orderBy)) {
            return '';
        }

        $clauses = array();
        foreach ($orderBy as $field => $direction) {
            $direction = strtoupper($direction) === 'DESC' ? 'DESC' : 'ASC';
            $clauses[] = "`{$field}` {$direction}";
        }

        return 'ORDER BY ' . implode(', ', $clauses);
    }

    /**
     * Build LIMIT clause
     *
     * @param int $limit
     * @param int $offset
     * @return string
     */
    public function buildLimitClause($limit, $offset = 0)
    {
        if ($limit <= 0) {
            return '';
        }

        return $offset > 0 ? "LIMIT {$offset}, {$limit}" : "LIMIT {$limit}";
    }

    /**
     * Simple select query builder
     *
     * @param string $table
     * @param array $conditions
     * @param array $orderBy
     * @param int $limit
     * @param int $offset
     * @return mixed
     */
    public function select($table, $conditions = array(), $orderBy = array(), $limit = 0, $offset = 0)
    {
        $query = "SELECT * FROM `{$table}`";

        $whereClause = $this->buildWhereClause($conditions);
        if ($whereClause) {
            $query .= ' ' . $whereClause;
        }

        $orderByClause = $this->buildOrderByClause($orderBy);
        if ($orderByClause) {
            $query .= ' ' . $orderByClause;
        }

        $limitClause = $this->buildLimitClause($limit, $offset);
        if ($limitClause) {
            $query .= ' ' . $limitClause;
        }

        return $this->query($query);
    }

    /**
     * Count records in table
     *
     * @param string $table
     * @param array $conditions
     * @return int
     */
    public function count($table, $conditions = array())
    {
        $query = "SELECT COUNT(*) as count FROM `{$table}`";

        $whereClause = $this->buildWhereClause($conditions);
        if ($whereClause) {
            $query .= ' ' . $whereClause;
        }

        $result = $this->query($query);
        $row = $this->fetchAssoc($result);

        return (int) (isset($row['count']) ? $row['count'] : 0);
    }
}
