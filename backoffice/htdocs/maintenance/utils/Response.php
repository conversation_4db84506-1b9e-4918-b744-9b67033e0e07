<?php
/**
 * Response Utility Class
 *
 * Handles HTTP responses and JSON formatting
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Utils_Response
{
    /**
     * Send JSON response
     *
     * @param array $data
     * @param int $statusCode
     * @return void
     */
    public static function json($data, $statusCode = null)
    {
        if ($statusCode === null) {
            $statusCode = HTTP_OK;
        }

        if (function_exists('http_response_code')) {
            http_response_code($statusCode);
        } else {
            header('HTTP/1.1 ' . $statusCode);
        }
        header('Content-Type: application/json; charset=utf-8');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

        // PHP 5.6 compatible JSON encoding
        if (defined('JSON_UNESCAPED_UNICODE') && defined('JSON_PRETTY_PRINT')) {
            echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        } else {
            echo json_encode($data);
        }
        exit;
    }

    /**
     * Send success JSON response
     * 
     * @param mixed $data
     * @param string $message
     * @param int $statusCode
     * @return void
     */
    public static function success($data = null, $message = 'Success', $statusCode = null)
    {
        if ($statusCode === null) {
            $statusCode = HTTP_OK;
        }

        $response = array(
            'success' => true,
            'message' => $message,
            'timestamp' => date('c')
        );

        if ($data !== null) {
            $response['data'] = $data;
        }

        self::json($response, $statusCode);
    }

    /**
     * Send error JSON response
     *
     * @param string $message
     * @param int $statusCode
     * @param array $errors
     * @return void
     */
    public static function error($message, $statusCode = null, $errors = array())
    {
        if ($statusCode === null) {
            $statusCode = HTTP_BAD_REQUEST;
        }

        $response = array(
            'success' => false,
            'error' => $message,
            'timestamp' => date('c')
        );

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        // Log error for debugging
        error_log(sprintf(
            'API Error [%d]: %s - Errors: %s',
            $statusCode,
            $message,
            json_encode($errors)
        ));

        self::json($response, $statusCode);
    }

    /**
     * Send validation error response
     *
     * @param array $validationErrors
     * @return void
     */
    public static function validationError($validationErrors)
    {
        self::error('Validation failed', HTTP_BAD_REQUEST, $validationErrors);
    }

    /**
     * Send not found response
     *
     * @param string $resource
     * @return void
     */
    public static function notFound($resource = 'Resource')
    {
        self::error($resource . ' not found', HTTP_NOT_FOUND);
    }

    /**
     * Send unauthorized response
     *
     * @return void
     */
    public static function unauthorized()
    {
        self::error('Unauthorized access', HTTP_UNAUTHORIZED);
    }

    /**
     * Send forbidden response
     *
     * @return void
     */
    public static function forbidden()
    {
        self::error('Access forbidden', HTTP_FORBIDDEN);
    }

    /**
     * Send method not allowed response
     *
     * @param array $allowedMethods
     * @return void
     */
    public static function methodNotAllowed($allowedMethods = array())
    {
        if (!empty($allowedMethods)) {
            header('Allow: ' . implode(', ', $allowedMethods));
        }
        self::error('Method not allowed', HTTP_METHOD_NOT_ALLOWED);
    }

    /**
     * Send internal server error response
     *
     * @param string $message
     * @return void
     */
    public static function serverError($message = 'Internal server error')
    {
        self::error($message, HTTP_INTERNAL_SERVER_ERROR);
    }

    /**
     * Send paginated response
     *
     * @param array $data
     * @param int $total
     * @param int $page
     * @param int $pageSize
     * @param string $message
     * @return void
     */
    public static function paginated($data, $total, $page, $pageSize, $message = 'Success')
    {
        $totalPages = ceil($total / $pageSize);

        $response = array(
            'success' => true,
            'message' => $message,
            'data' => $data,
            'pagination' => array(
                'current_page' => $page,
                'page_size' => $pageSize,
                'total_items' => $total,
                'total_pages' => $totalPages,
                'has_next' => $page < $totalPages,
                'has_previous' => $page > 1
            ),
            'timestamp' => date('c')
        );

        self::json($response);
    }

    /**
     * Redirect to URL
     *
     * @param string $url
     * @param int $statusCode
     * @return void
     */
    public static function redirect($url, $statusCode = 302)
    {
        if (function_exists('http_response_code')) {
            http_response_code($statusCode);
        } else {
            header('HTTP/1.1 ' . $statusCode);
        }
        header('Location: ' . $url);
        exit;
    }

    /**
     * Send file download response
     *
     * @param string $filePath
     * @param string $fileName
     * @param string $mimeType
     * @return void
     */
    public static function download($filePath, $fileName, $mimeType = 'application/octet-stream')
    {
        if (!file_exists($filePath)) {
            self::notFound('File');
        }

        $fileSize = filesize($filePath);
        
        header('Content-Type: ' . $mimeType);
        header('Content-Disposition: attachment; filename="' . $fileName . '"');
        header('Content-Length: ' . $fileSize);
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        
        readfile($filePath);
        exit;
    }

    /**
     * Set CORS headers
     *
     * @param array $allowedOrigins
     * @param array $allowedMethods
     * @param array $allowedHeaders
     * @return void
     */
    public static function setCorsHeaders($allowedOrigins = null, $allowedMethods = null, $allowedHeaders = null)
    {
        if ($allowedOrigins === null) {
            $allowedOrigins = array('*');
        }
        if ($allowedMethods === null) {
            $allowedMethods = array('GET', 'POST', 'PUT', 'DELETE', 'OPTIONS');
        }
        if ($allowedHeaders === null) {
            $allowedHeaders = array('Content-Type', 'Authorization', 'X-Requested-With');
        }

        $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';

        if (in_array('*', $allowedOrigins) || in_array($origin, $allowedOrigins)) {
            header('Access-Control-Allow-Origin: ' . ($origin ? $origin : '*'));
        }

        header('Access-Control-Allow-Methods: ' . implode(', ', $allowedMethods));
        header('Access-Control-Allow-Headers: ' . implode(', ', $allowedHeaders));
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Max-Age: 86400'); // 24 hours
    }

    /**
     * Handle OPTIONS request for CORS preflight
     *
     * @return void
     */
    public static function handleOptionsRequest()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            self::setCorsHeaders();
            if (function_exists('http_response_code')) {
                http_response_code(HTTP_OK);
            } else {
                header('HTTP/1.1 200 OK');
            }
            exit;
        }
    }
}
