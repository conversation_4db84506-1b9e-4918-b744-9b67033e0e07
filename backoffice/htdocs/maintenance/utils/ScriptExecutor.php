<?php
/**
 * Script Executor Utility Class
 *
 * Executes SQL and PHP update scripts
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Utils_ScriptExecutor
{
    private $db;
    private $logger;
    private $versionManager;

    /**
     * Constructor
     *
     * @param Maintenance_Utils_Logger $logger
     * @param Maintenance_Utils_VersionManager $versionManager
     */
    public function __construct($logger, $versionManager)
    {
        $this->db = Maintenance_Utils_Database::getInstance();
        $this->logger = $logger;
        $this->versionManager = $versionManager;
    }

    /**
     * Get applicable update scripts (only newer than current version)
     *
     * @param string $currentVersion
     * @return array
     */
    public function getApplicableScripts($currentVersion)
    {
        return $this->getScripts($currentVersion, 'newer_only');
    }

    /**
     * Get all scripts in the directory (for replay/re-execution)
     *
     * @param string $currentVersion
     * @return array
     */
    public function getAllScripts($currentVersion)
    {
        return $this->getScripts($currentVersion, 'all');
    }

    /**
     * Get scripts based on specified mode
     *
     * @param string $currentVersion
     * @param string $mode 'newer_only', 'all', 'equal_or_newer'
     * @return array
     */
    public function getScripts($currentVersion, $mode = 'newer_only')
    {
        $scriptsDir = defined('MAJS_DIR_PATH') ? MAJS_DIR_PATH : 'www/backend/majs/';

        if (!is_dir($scriptsDir)) {
            $this->logger->warning('script_execution', 'Scripts directory does not exist', array(
                'scripts_dir' => $scriptsDir,
                'mode' => $mode
            ));
            return array();
        }

        $scripts = array();
        $files = scandir($scriptsDir);

        if ($files === false) {
            $this->logger->error('script_execution', 'Cannot read scripts directory', array(
                'scripts_dir' => $scriptsDir,
                'mode' => $mode
            ));
            return array();
        }

        foreach ($files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }

            // Match maj_x.yyy.php or maj_x.yyy.sql pattern
            if (preg_match('/^maj_(\d+\.\d+)\.(php|sql)$/', $file, $matches)) {
                $version = $matches[1];
                $extension = $matches[2];

                $includeScript = false;

                switch ($mode) {
                    case 'newer_only':
                        // Only include scripts with version greater than current
                        $includeScript = $this->versionManager->isVersionGreater($version, $currentVersion);
                        break;
                    case 'all':
                        // Include all scripts regardless of version
                        $includeScript = true;
                        break;
                    case 'equal_or_newer':
                        // Include scripts with version greater than or equal to current
                        $versionCompare = $this->versionManager->compareVersions($version, $currentVersion);
                        $includeScript = ($versionCompare >= 0);
                        break;
                }

                if ($includeScript) {
                    $scripts[] = array(
                        'file' => $file,
                        'path' => $scriptsDir . $file,
                        'version' => $version,
                        'type' => $extension,
                        'status' => $this->getScriptStatus($version, $currentVersion)
                    );
                }
            }
        }

        // Sort by version, then by type (SQL before PHP for same version)
        usort($scripts, array($this, 'compareScripts'));

        $this->logger->info('script_execution', 'Scripts identified', array(
            'current_version' => $currentVersion,
            'mode' => $mode,
            'scripts_count' => count($scripts),
            'scripts' => array_map(function($script) {
                return $script['file'] . ' (' . $script['status'] . ')';
            }, $scripts)
        ));

        return $scripts;
    }

    /**
     * Get script status relative to current version
     *
     * @param string $scriptVersion
     * @param string $currentVersion
     * @return string
     */
    private function getScriptStatus($scriptVersion, $currentVersion)
    {
        $versionCompare = $this->versionManager->compareVersions($scriptVersion, $currentVersion);

        if ($versionCompare > 0) {
            return 'newer';
        } elseif ($versionCompare === 0) {
            return 'current';
        } else {
            return 'older';
        }
    }

    /**
     * Compare scripts for sorting
     *
     * @param array $a
     * @param array $b
     * @return int
     */
    private function compareScripts($a, $b)
    {
        $versionCompare = $this->versionManager->compareVersions($a['version'], $b['version']);
        
        if ($versionCompare !== 0) {
            return $versionCompare;
        }
        
        // For same version, SQL comes before PHP
        if ($a['type'] === 'sql' && $b['type'] === 'php') {
            return -1;
        } elseif ($a['type'] === 'php' && $b['type'] === 'sql') {
            return 1;
        }
        
        return 0;
    }

    /**
     * Execute SQL script
     *
     * @param array $script
     * @return array Execution results with detailed information
     * @throws Exception
     */
    public function executeSqlScript($script)
    {
        $this->logger->info('script_execution', 'Starting SQL script execution', array(
            'script_name' => $script['file'],
            'script_path' => $script['path'],
            'version' => $script['version']
        ));

        if (!file_exists($script['path'])) {
            $message = 'SQL script file not found: ' . $script['path'];
            $this->logger->error('script_execution', $message, array(
                'script_name' => $script['file']
            ));
            throw new Exception($message);
        }

        $sqlContent = file_get_contents($script['path']);
        if ($sqlContent === false) {
            $message = 'Cannot read SQL script file: ' . $script['path'];
            $this->logger->error('script_execution', $message, array(
                'script_name' => $script['file']
            ));
            throw new Exception($message);
        }

        // Split SQL content into individual statements
        $statements = $this->splitSqlStatements($sqlContent);
        $executionResults = array();
        $detailedResults = array();

        foreach ($statements as $index => $statement) {
            $statement = trim($statement);
            if (empty($statement)) {
                continue;
            }

            try {
                $startTime = microtime(true);
                $result = $this->db->query($statement);
                $endTime = microtime(true);
                $executionTime = round(($endTime - $startTime) * 1000, 2);

                if ($result === false) {
                    $error = mysql_error();
                    $this->logger->error('script_execution', 'SQL statement failed', array(
                        'script_name' => $script['file'],
                        'statement_index' => $index,
                        'statement' => substr($statement, 0, 200) . '...',
                        'error' => $error,
                        'execution_time_ms' => $executionTime
                    ));
                    throw new Exception("SQL execution failed: {$error}");
                }

                // Log successful execution
                $affectedRows = mysql_affected_rows();
                $executionResults[] = array(
                    'statement_index' => $index,
                    'affected_rows' => $affectedRows,
                    'execution_time_ms' => $executionTime,
                    'status' => 'success'
                );

                // Capture detailed results for frontend display
                $detailedResults[] = array(
                    'statement' => $statement,
                    'affected_rows' => $affectedRows,
                    'execution_time_ms' => $executionTime,
                    'status' => 'success',
                    'type' => $this->getSqlStatementType($statement)
                );

                $this->logger->info('script_execution', 'SQL statement executed successfully', array(
                    'script_name' => $script['file'],
                    'statement_index' => $index,
                    'affected_rows' => $affectedRows,
                    'execution_time_ms' => $executionTime
                ));

            } catch (Exception $e) {
                $this->logger->error('script_execution', 'SQL statement exception', array(
                    'script_name' => $script['file'],
                    'statement_index' => $index,
                    'statement' => substr($statement, 0, 200) . '...',
                    'error' => $e->getMessage()
                ));
                throw $e;
            }
        }

        $totalExecutionTime = array_sum(array_column($executionResults, 'execution_time_ms'));

        $this->logger->info('script_execution', 'SQL script executed successfully', array(
            'script_name' => $script['file'],
            'statements_count' => count($statements),
            'execution_results' => $executionResults,
            'total_execution_time_ms' => $totalExecutionTime
        ));

        return array(
            'success' => true,
            'statements_executed' => count($executionResults),
            'total_execution_time_ms' => $totalExecutionTime,
            'detailed_results' => $detailedResults
        );
    }

    /**
     * Execute PHP script
     *
     * @param array $script
     * @return array Execution results with detailed information
     * @throws Exception
     */
    public function executePhpScript($script)
    {
        $this->logger->info('script_execution', 'Starting PHP script execution', array(
            'script_name' => $script['file'],
            'script_path' => $script['path'],
            'version' => $script['version']
        ));

        if (!file_exists($script['path'])) {
            $message = 'PHP script file not found: ' . $script['path'];
            $this->logger->error('script_execution', $message, array(
                'script_name' => $script['file']
            ));
            throw new Exception($message);
        }

        try {
            // Capture any output from the script
            $startTime = microtime(true);
            $memoryBefore = memory_get_usage(true);
            ob_start();
            $result = require_once($script['path']);
            $output = ob_get_clean();
            $endTime = microtime(true);
            $memoryAfter = memory_get_usage(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);

            $this->logger->info('script_execution', 'PHP script executed successfully', array(
                'script_name' => $script['file'],
                'output' => $output,
                'result' => $result,
                'execution_time_ms' => $executionTime,
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true)
            ));

            return array(
                'success' => true,
                'output' => $output,
                'result' => $result,
                'execution_time_ms' => $executionTime,
                'memory_before' => $memoryBefore,
                'memory_after' => $memoryAfter,
                'memory_peak' => memory_get_peak_usage(true)
            );
        } catch (Exception $e) {
            ob_end_clean();
            $this->logger->error('script_execution', 'PHP script execution failed', array(
                'script_name' => $script['file'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'memory_usage' => memory_get_usage(true)
            ));
            throw $e;
        }
    }

    /**
     * Split SQL content into individual statements
     *
     * @param string $sqlContent
     * @return array
     */
    private function splitSqlStatements($sqlContent)
    {
        // Simple SQL statement splitting (handles basic cases)
        // For more complex cases, a proper SQL parser would be needed
        $statements = array();
        $lines = explode("\n", $sqlContent);
        $currentStatement = '';
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Skip empty lines and comments
            if (empty($line) || strpos($line, '--') === 0 || strpos($line, '#') === 0) {
                continue;
            }
            
            $currentStatement .= $line . ' ';
            
            // Check if statement ends with semicolon
            if (substr(rtrim($line), -1) === ';') {
                $statements[] = trim($currentStatement);
                $currentStatement = '';
            }
        }
        
        // Add remaining statement if any
        if (!empty(trim($currentStatement))) {
            $statements[] = trim($currentStatement);
        }
        
        return $statements;
    }

    /**
     * Get SQL statement type for display purposes
     *
     * @param string $statement
     * @return string
     */
    private function getSqlStatementType($statement)
    {
        $statement = trim(strtoupper($statement));

        if (strpos($statement, 'SELECT') === 0) {
            return 'SELECT';
        } elseif (strpos($statement, 'INSERT') === 0) {
            return 'INSERT';
        } elseif (strpos($statement, 'UPDATE') === 0) {
            return 'UPDATE';
        } elseif (strpos($statement, 'DELETE') === 0) {
            return 'DELETE';
        } elseif (strpos($statement, 'CREATE') === 0) {
            return 'CREATE';
        } elseif (strpos($statement, 'ALTER') === 0) {
            return 'ALTER';
        } elseif (strpos($statement, 'DROP') === 0) {
            return 'DROP';
        } else {
            return 'OTHER';
        }
    }
}
