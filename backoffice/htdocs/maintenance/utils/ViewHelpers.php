<?php
/**
 * View Helper Functions
 *
 * Helper functions for rendering configuration variables and other UI elements
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Utils_ViewHelpers
{
    /**
     * Get ERP/Gescom code name
     *
     * @param int $erpId
     * @return string
     */
    public static function getGescomCode($erpId)
    {
        if (!is_numeric($erpId) || $erpId < 0) {
            return '';
        }

        $name = '';
        
        switch ($erpId) {
            case GESCOM_TYPE_SAGE:
                $name = 'SAGE';
                break;
            case GESCOM_TYPE_ISIALIS:
                $name = 'ISIALIS';
                break;
            case GESCOM_TYPE_LIMS:
                $name = 'LIMS';
                break;
            case GESCOM_TYPE_HARMONYS:
                $name = 'HARMONYS';
                break;
            case GESCOM_TYPE_PLATON:
                $name = 'PLATON';
                break;
            case GESCOM_TYPE_APINEGOCE:
                $name = 'APINEGOCE';
                break;
            case GESCOM_TYPE_G5:
                $name = 'G5';
                break;
            case GESCOM_TYPE_DIVALTO:
                $name = 'DIVALTO';
                break;
            case GESCOM_TYPE_CLISSON:
                $name = 'CLISSON';
                break;
            case GESCOM_TYPE_EEE:
                $name = 'EEE';
                break;
            case GESCOM_TYPE_DYNAMICS:
                $name = 'DYNAMICS';
                break;
            case GESCOM_TYPE_SOFI:
                $name = 'SOFI';
                break;
            case GESCOM_TYPE_WAVESOFT:
                $name = 'WAVESOFT';
                break;
            case GESCOM_TYPE_CEGID:
                $name = 'CEGID';
                break;
            case GESCOM_TYPE_SAGE_X3:
                $name = 'SAGE_X3';
                break;
            case GESCOM_TYPE_DYNAMICS_NAVISION:
                $name = 'DYNAMICS_NAVISION';
                break;
            case GESCOM_TYPE_SINERES:
                $name = 'SINERES';
                break;
            case GESCOM_TYPE_DIVALTO_SQL:
                $name = 'DIVALTO_SQL';
                break;
            default:
                $name = '';
                break;
        }

        return $name;
    }

    /**
     * Render a configuration variable line for the table
     *
     * @param int $tenantId
     * @param array $data
     * @param bool $child
     * @return string
     */
    public static function renderVariableLine($tenantId, $data, $child = false)
    {
        $override = $data['override'];

        // Check if we have POST data for this variable
        if (isset($_POST['override'][$data['code']])) {
            if (is_array($_POST['override'][$data['code']])) {
                $override = implode(', ', $_POST['override'][$data['code']]);
            } else {
                $override = $_POST['override'][$data['code']];
            }
        }

        ob_start();
        ?>
        <tr <?php echo $child ? '' : 'data-child-open="' . htmlspecialchars($data['code']) . '"'; ?>>
            <td>
                <strong><?php echo htmlspecialchars($data['name']); ?></strong>
                <ul>
                    <li class="code">Code : <?php echo htmlspecialchars($data['code']); ?></li>
                    <li>Par défaut : <?php echo htmlspecialchars($data['default']); ?></li>
                    <li>Type : <?php echo self::getFieldTypeName($data['type']); ?></li>
                    <li><?php echo (trim($data['override']) !== '' ? 'Surchargé' : 'Utilise la valeur par défaut'); ?></li>
                </ul>
            </td>
            <td>
                <?php echo self::renderVariableInput($tenantId, $data, $override); ?>
            </td>
            <td>
                <?php if (trim($data['desc']) !== '') { ?>
                    <img src="/images/help.png" width="16" height="16" title="<?php echo htmlspecialchars($data['desc']); ?>" />
                <?php } ?>
            </td>
        </tr>
        <?php
        return ob_get_clean();
    }

    /**
     * Render a configuration variable as a modern card
     *
     * @param int $tenantId
     * @param array $data
     * @param bool $child
     * @return string
     */
    public static function renderVariableCard($tenantId, $data, $child = false)
    {
        $override = $data['override'];

        // Check if we have POST data for this variable
        if (isset($_POST['override'][$data['code']])) {
            if (is_array($_POST['override'][$data['code']])) {
                $override = implode(', ', $_POST['override'][$data['code']]);
            } else {
                $override = $_POST['override'][$data['code']];
            }
        }

        $isOverridden = trim($data['override']) !== '' && $data['override'] !== $data['default'];
        $hasChildren = count($data['childs']) > 0;
        $typeClass = self::getFieldTypeClass($data['type']);

        ob_start();
        ?>
        <div class="variable-card <?php echo $child ? 'child-variable' : ''; ?> <?php echo $isOverridden ? 'overridden' : 'default'; ?>"
             data-code="<?php echo htmlspecialchars($data['code']); ?>"
             data-type="<?php echo $typeClass; ?>"
             data-category="<?php echo htmlspecialchars(self::getVariableCategory($data['code'])); ?>"
             data-has-children="<?php echo $hasChildren ? 'yes' : 'no'; ?>"
             <?php echo $child ? '' : 'data-child-open="' . htmlspecialchars($data['code']) . '"'; ?>>

            <div class="card-header">
                <div class="card-selection">
                    <input type="checkbox" class="variable-select"
                           data-code="<?php echo htmlspecialchars($data['code']); ?>" />
                </div>

                <div class="card-title-section">
                    <h4 class="variable-name"><?php echo htmlspecialchars($data['name']); ?></h4>
                    <div class="variable-meta">
                        <span class="variable-code"><?php echo htmlspecialchars($data['code']); ?></span>
                        <span class="variable-type type-<?php echo $typeClass; ?>">
                            <?php echo self::getFieldTypeName($data['type']); ?>
                        </span>
                    </div>
                </div>

                <div class="card-status">
                    <span class="status-badge <?php echo $isOverridden ? 'status-overridden' : 'status-default'; ?>">
                        <?php echo $isOverridden ? 'Surchargé' : 'Défaut'; ?>
                    </span>
                    <?php if ($hasChildren): ?>
                        <span class="children-badge" title="Cette variable a des variables enfants">
                            <i class="icon-children"></i> <?php echo count($data['childs']); ?>
                        </span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card-content">
                <div class="variable-info">
                    <div class="info-row">
                        <span class="info-label">Valeur par défaut :</span>
                        <span class="info-value default-value"><?php echo htmlspecialchars($data['default']); ?></span>
                    </div>
                    <?php if (trim($data['desc']) !== ''): ?>
                        <div class="info-row">
                            <span class="info-label">Description :</span>
                            <span class="info-value description"><?php echo htmlspecialchars($data['desc']); ?></span>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="variable-input-section">
                    <label class="input-label">Valeur configurée :</label>
                    <div class="input-container">
                        <?php echo self::renderVariableInput($tenantId, $data, $override); ?>
                    </div>
                </div>
            </div>

            <?php if ($hasChildren && !$child): ?>
                <div class="card-footer">
                    <button type="button" class="toggle-children" data-code="<?php echo htmlspecialchars($data['code']); ?>">
                        <i class="icon-chevron-down"></i>
                        Afficher les variables enfants (<?php echo count($data['childs']); ?>)
                    </button>
                </div>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Render a configuration variable as a table row
     *
     * @param int $tenantId
     * @param array $data
     * @param bool $child
     * @return string
     */
    public static function renderVariableTableRow($tenantId, $data, $child = false)
    {
        $override = $data['override'];

        // Check if we have POST data for this variable
        if (isset($_POST['override'][$data['code']])) {
            if (is_array($_POST['override'][$data['code']])) {
                $override = implode(', ', $_POST['override'][$data['code']]);
            } else {
                $override = $_POST['override'][$data['code']];
            }
        }

        $isOverridden = trim($data['override']) !== '' && $data['override'] !== $data['default'];
        $hasChildren = count($data['childs']) > 0;
        $typeClass = self::getFieldTypeClass($data['type']);

        ob_start();
        ?>
        <tr class="variable-row <?php echo $child ? 'child-variable' : ''; ?> <?php echo $isOverridden ? 'overridden' : 'default'; ?>"
            data-code="<?php echo htmlspecialchars($data['code']); ?>"
            data-type="<?php echo $typeClass; ?>"
            data-category="<?php echo htmlspecialchars(self::getVariableCategory($data['code'])); ?>"
            data-has-children="<?php echo $hasChildren ? 'yes' : 'no'; ?>"
            <?php echo $child ? '' : 'data-child-open="' . htmlspecialchars($data['code']) . '"'; ?>>

            <td class="select-cell">
                <input type="checkbox" class="variable-select"
                       data-code="<?php echo htmlspecialchars($data['code']); ?>" />
            </td>

            <td class="variable-cell">
                <div class="variable-info">
                    <strong class="variable-name"><?php echo htmlspecialchars($data['name']); ?></strong>
                    <div class="variable-meta">
                        <span class="variable-code"><?php echo htmlspecialchars($data['code']); ?></span>
                        <?php if (trim($data['desc']) !== ''): ?>
                            <span class="variable-description" title="<?php echo htmlspecialchars($data['desc']); ?>">
                                <i class="icon-help"></i>
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="default-value">
                        Défaut: <?php echo htmlspecialchars($data['default']); ?>
                    </div>
                </div>
            </td>

            <td class="value-cell">
                <?php echo self::renderVariableInput($tenantId, $data, $override); ?>
            </td>

            <td class="type-cell">
                <span class="type-badge type-<?php echo $typeClass; ?>">
                    <?php echo self::getFieldTypeName($data['type']); ?>
                </span>
            </td>

            <td class="status-cell">
                <span class="status-badge <?php echo $isOverridden ? 'status-overridden' : 'status-default'; ?>">
                    <?php echo $isOverridden ? 'Surchargé' : 'Défaut'; ?>
                </span>
                <?php if ($hasChildren): ?>
                    <span class="children-badge" title="Variables enfants">
                        <i class="icon-children"></i> <?php echo count($data['childs']); ?>
                    </span>
                <?php endif; ?>
            </td>

            <td class="actions-cell">
                <div class="action-buttons">
                    <?php if ($isOverridden): ?>
                        <button type="button" class="button button-sm button-secondary reset-variable"
                                data-code="<?php echo htmlspecialchars($data['code']); ?>"
                                title="Réinitialiser à la valeur par défaut">
                            <i class="icon-reset"></i>
                        </button>
                    <?php endif; ?>
                    <?php if ($hasChildren && !$child): ?>
                        <button type="button" class="button button-sm button-info toggle-children"
                                data-code="<?php echo htmlspecialchars($data['code']); ?>"
                                title="Afficher/masquer les variables enfants">
                            <i class="icon-children"></i>
                        </button>
                    <?php endif; ?>
                </div>
            </td>
        </tr>
        <?php
        return ob_get_clean();
    }

    /**
     * Render input field for a configuration variable
     *
     * @param int $tenantId
     * @param array $data
     * @param string $override
     * @return string
     */
    private static function renderVariableInput($tenantId, $data, $override)
    {
        ob_start();
        
        switch ($data['type']) {
            case FLD_TYPE_BOOLEAN_YES_NO:
                ?>
                <label for="val-<?php echo self::urlAlias($data['code']); ?>-yes">
                    <input type="radio" name="override[<?php echo $data['code']; ?>]" 
                           id="val-<?php echo self::urlAlias($data['code']); ?>-yes" 
                           <?php echo $override == '1' ? 'checked="checked"' : ''; ?> value="1" /> Oui
                </label>
                <label for="val-<?php echo self::urlAlias($data['code']); ?>-no">
                    <input type="radio" name="override[<?php echo $data['code']; ?>]" 
                           id="val-<?php echo self::urlAlias($data['code']); ?>-no" 
                           <?php echo $override == '0' ? 'checked="checked"' : ''; ?> value="0" /> Non
                </label>
                <?php
                break;
                
            default:
                // Check if this is a multi-select field with objects
                $objectResult = false;
                if ($data['type'] == FLD_TYPE_SELECT_MULTIPLE && isset($data['cls_id']) && $data['cls_id']) {
                    $objectResult = self::getConfigurationObjects($tenantId, $data['cls_id'], 0, array(), 
                                                                  isset($data['obj_cls_id']) ? $data['obj_cls_id'] : 0);
                }

                if ($objectResult && ria_mysql_num_rows($objectResult)) {
                    ?>
                    <div>
                        <strong>Valeur(s) en base</strong> : <?php echo htmlspecialchars($data['override']); ?>
                    </div>
                    <?php
                    $div = round(ria_mysql_num_rows($objectResult) / 2);
                    ?>
                    <div class="col-cls-obj">
                        <input type="hidden" name="override[<?php echo $data['code']; ?>][]" value="" />
                        <?php
                        $i = 0;
                        $values = explode(', ', $override);
                        while ($obj = ria_mysql_fetch_assoc($objectResult)) {
                            if ($i == $div) {
                                ?>
                                </div><div class="col-cls-obj">
                                <?php
                            }

                            $selected = '';
                            if (in_array($obj['id'], $values)) {
                                $selected = 'checked="checked"';
                            }
                            ?>
                            <label for="override-<?php echo $data['code']; ?>-<?php echo $obj['id']; ?>">
                                <input type="checkbox" name="override[<?php echo $data['code']; ?>][]" 
                                       id="override-<?php echo $data['code']; ?>-<?php echo $obj['id']; ?>" 
                                       value="<?php echo $obj['id']; ?>" <?php echo $selected; ?> />
                                <?php echo htmlspecialchars($obj['name']); ?>
                            </label>
                            <?php
                            $i++;
                        }
                        ?>
                    </div>
                    <?php
                } else {
                    ?>
                    <textarea name="override[<?php echo $data['code']; ?>]" cols="50" rows="5"><?php echo htmlspecialchars($override); ?></textarea>
                    <?php
                }
                break;
        }
        
        return ob_get_clean();
    }

    /**
     * Get field type name
     *
     * @param int $type
     * @return string
     */
    public static function getFieldTypeName($type)
    {
        if (function_exists('fld_types_get_name')) {
            return fld_types_get_name($type);
        }

        // Fallback type names
        switch ($type) {
            case FLD_TYPE_TEXT:
                return 'Texte court';
            case FLD_TYPE_TEXTAREA:
                return 'Texte long';
            case FLD_TYPE_INT:
                return 'Nombre entier';
            case FLD_TYPE_FLOAT:
                return 'Nombre décimal';
            case FLD_TYPE_SELECT:
                return 'Liste de choix';
            case FLD_TYPE_SELECT_MULTIPLE:
                return 'Liste de choix multiple';
            case FLD_TYPE_BOOLEAN_YES_NO:
                return 'Booléen (Oui/Non)';
            case FLD_TYPE_DATE:
                return 'Date';
            default:
                return 'Inconnu';
        }
    }

    /**
     * Create URL-safe alias
     *
     * @param string $string
     * @return string
     */
    private static function urlAlias($string)
    {
        if (function_exists('urlalias')) {
            return urlalias($string);
        }
        
        // Simple fallback
        return preg_replace('/[^a-zA-Z0-9_-]/', '_', $string);
    }

    /**
     * Get configuration objects for multi-select fields
     *
     * @param int $tenantId
     * @param int $clsId
     * @param int $wstId
     * @param array $where
     * @param int $objClsId
     * @return resource|false
     */
    private static function getConfigurationObjects($tenantId, $clsId, $wstId = 0, $where = array(), $objClsId = 0)
    {
        if (!is_numeric($tenantId) || $tenantId <= 0) {
            return false;
        }

        if (!is_numeric($clsId) || $clsId <= 0) {
            return false;
        }
        
        if (!is_numeric($wstId) || $wstId < 0) {
            return false;
        }

        if (!is_array($where)) {
            return false;
        }
        
        $sqlParts = array(
            'select' => '',
            'from' => '',
            'where' => array(),
        );

        switch ($clsId) {
            case CLS_CTR_MKT:
                $sqlParts['select'] = 'ctr_id as id, ctr_name as name';
                $sqlParts['from'] = 'riashop.ctr_comparators';
                break;
            case CLS_FLD_MODELS:
                $sqlParts['select'] = 'mdl_id as id, mdl_name as name';
                $sqlParts['from'] = 'riashop.fld_models';
                if ($objClsId) {
                    $sqlParts['where'][] = 'mdl_cls_id = ' . $objClsId;
                }
                break;
            default:
                return false;
        }

        if (count($where)) {
            $sqlParts['where'] = array_merge($sqlParts['where'], $where);
        }

        $sql = 'SELECT ' . $sqlParts['select'] . ' FROM ' . $sqlParts['from'] . ' WHERE 1';

        if (count($sqlParts['where'])) {
            $sql .= ' AND ' . implode(' AND ', $sqlParts['where']);
        }

        $result = ria_mysql_query($sql);
        if (!$result) {
            die(mysql_error() . ' => <pre>' . htmlspecialchars($sql) . '</pre>');
        }

        return $result;
    }

    /**
     * Get field type class name
     *
     * @param int $type
     * @return string
     */
    public static function getFieldTypeClass($type)
    {
        switch ($type) {
            case FLD_TYPE_TEXT:
                return 'text';
            case FLD_TYPE_BOOLEAN_YES_NO:
                return 'boolean';
            case FLD_TYPE_SELECT:
                return 'select';
            case FLD_TYPE_TEXTAREA:
                return 'textarea';
            case FLD_TYPE_NUMBER:
                return 'number';
            default:
                return 'text';
        }
    }



    /**
     * Get variable category from code
     *
     * @param string $code
     * @return string
     */
    public static function getVariableCategory($code)
    {
        $codeParts = explode('_', $code);
        if (count($codeParts) > 1) {
            $prefix = strtoupper($codeParts[0]);
            switch ($prefix) {
                case 'SYNC':
                    return 'Synchronisation';
                case 'FDV':
                    return 'Force de vente';
                case 'API':
                    return 'API & Intégrations';
                case 'ORD':
                    return 'Commandes';
                case 'PRD':
                    return 'Produits';
                case 'USR':
                    return 'Utilisateurs';
                case 'DLV':
                    return 'Livraisons';
                default:
                    return ucfirst(strtolower($prefix));
            }
        }
        return 'Général';
    }
}
