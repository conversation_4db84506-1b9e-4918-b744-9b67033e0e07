<?php
/**
 * Validator Utility Class
 *
 * Handles input validation and sanitization
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Utils_Validator
{
    private $errors = array();
    private $data = array();

    /**
     * Constructor
     *
     * @param array $data
     */
    public function __construct($data = array())
    {
        $this->data = $data;
    }

    /**
     * Validate required field
     *
     * @param string $field
     * @param string $message
     * @return self
     */
    public function required($field, $message = null)
    {
        if (!isset($this->data[$field]) || trim($this->data[$field]) === '') {
            $this->errors[$field][] = $message ? $message : "The {$field} field is required.";
        }
        return $this;
    }

    /**
     * Validate email format
     *
     * @param string $field
     * @param string $message
     * @return self
     */
    public function email($field, $message = null)
    {
        if (isset($this->data[$field]) && !filter_var($this->data[$field], FILTER_VALIDATE_EMAIL)) {
            $this->errors[$field][] = $message ? $message : "The {$field} field must be a valid email address.";
        }
        return $this;
    }

    /**
     * Validate integer
     *
     * @param string $field
     * @param string $message
     * @return self
     */
    public function integer($field, $message = null)
    {
        if (isset($this->data[$field]) && !filter_var($this->data[$field], FILTER_VALIDATE_INT)) {
            $this->errors[$field][] = $message ? $message : "The {$field} field must be an integer.";
        }
        return $this;
    }

    /**
     * Validate positive integer
     *
     * @param string $field
     * @param string $message
     * @return self
     */
    public function positiveInteger($field, $message = null)
    {
        if (isset($this->data[$field])) {
            $value = filter_var($this->data[$field], FILTER_VALIDATE_INT);
            if ($value === false || $value <= 0) {
                $this->errors[$field][] = $message ? $message : "The {$field} field must be a positive integer.";
            }
        }
        return $this;
    }

    /**
     * Validate minimum length
     *
     * @param string $field
     * @param int $min
     * @param string $message
     * @return self
     */
    public function minLength($field, $min, $message = null)
    {
        if (isset($this->data[$field]) && strlen($this->data[$field]) < $min) {
            $this->errors[$field][] = $message ? $message : "The {$field} field must be at least {$min} characters.";
        }
        return $this;
    }

    /**
     * Validate maximum length
     *
     * @param string $field
     * @param int $max
     * @param string $message
     * @return self
     */
    public function maxLength($field, $max, $message = null)
    {
        if (isset($this->data[$field]) && strlen($this->data[$field]) > $max) {
            $this->errors[$field][] = $message ? $message : "The {$field} field must not exceed {$max} characters.";
        }
        return $this;
    }

    /**
     * Validate value is in array
     *
     * @param string $field
     * @param array $values
     * @param string $message
     * @return self
     */
    public function in($field, $values, $message = null)
    {
        if (isset($this->data[$field]) && !in_array($this->data[$field], $values)) {
            $this->errors[$field][] = $message ? $message : "The {$field} field must be one of: " . implode(', ', $values);
        }
        return $this;
    }

    /**
     * Validate boolean value
     *
     * @param string $field
     * @param string $message
     * @return self
     */
    public function boolean($field, $message = null)
    {
        if (isset($this->data[$field])) {
            $value = filter_var($this->data[$field], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            if ($value === null) {
                $this->errors[$field][] = $message ? $message : "The {$field} field must be a boolean value.";
            }
        }
        return $this;
    }

    /**
     * Validate URL format
     *
     * @param string $field
     * @param string $message
     * @return self
     */
    public function url($field, $message = null)
    {
        if (isset($this->data[$field]) && !filter_var($this->data[$field], FILTER_VALIDATE_URL)) {
            $this->errors[$field][] = $message ? $message : "The {$field} field must be a valid URL.";
        }
        return $this;
    }

    /**
     * Validate regex pattern
     *
     * @param string $field
     * @param string $pattern
     * @param string $message
     * @return self
     */
    public function regex($field, $pattern, $message = null)
    {
        if (isset($this->data[$field]) && !preg_match($pattern, $this->data[$field])) {
            $this->errors[$field][] = $message ? $message : "The {$field} field format is invalid.";
        }
        return $this;
    }

    /**
     * Validate numeric range
     *
     * @param string $field
     * @param float $min
     * @param float $max
     * @param string $message
     * @return self
     */
    public function range($field, $min, $max, $message = null)
    {
        if (isset($this->data[$field])) {
            $value = floatval($this->data[$field]);
            if ($value < $min || $value > $max) {
                $this->errors[$field][] = $message ? $message : "The {$field} field must be between {$min} and {$max}.";
            }
        }
        return $this;
    }

    /**
     * Custom validation rule
     *
     * @param string $field
     * @param callable $callback
     * @param string $message
     * @return self
     */
    public function custom($field, $callback, $message)
    {
        if (isset($this->data[$field]) && !$callback($this->data[$field])) {
            $this->errors[$field][] = $message;
        }
        return $this;
    }

    /**
     * Check if validation passed
     *
     * @return bool
     */
    public function passes()
    {
        return empty($this->errors);
    }

    /**
     * Check if validation failed
     *
     * @return bool
     */
    public function fails()
    {
        return !$this->passes();
    }

    /**
     * Get validation errors
     *
     * @return array
     */
    public function getErrors()
    {
        return $this->errors;
    }

    /**
     * Get first error for a field
     *
     * @param string $field
     * @return string|null
     */
    public function getFirstError($field)
    {
        return isset($this->errors[$field][0]) ? $this->errors[$field][0] : null;
    }

    /**
     * Get validated data
     *
     * @return array
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * Get specific validated field
     *
     * @param string $field
     * @param mixed $default
     * @return mixed
     */
    public function get($field, $default = null)
    {
        return isset($this->data[$field]) ? $this->data[$field] : $default;
    }

    /**
     * Sanitize string input
     *
     * @param string $input
     * @return string
     */
    public static function sanitizeString($input)
    {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Sanitize integer input
     *
     * @param mixed $input
     * @return int|null
     */
    public static function sanitizeInt($input)
    {
        $value = filter_var($input, FILTER_VALIDATE_INT);
        return $value !== false ? $value : null;
    }

    /**
     * Sanitize email input
     *
     * @param string $input
     * @return string|null
     */
    public static function sanitizeEmail($input)
    {
        $email = filter_var(trim($input), FILTER_SANITIZE_EMAIL);
        return filter_var($email, FILTER_VALIDATE_EMAIL) ? $email : null;
    }

    /**
     * Sanitize URL input
     *
     * @param string $input
     * @return string|null
     */
    public static function sanitizeUrl($input)
    {
        $url = filter_var(trim($input), FILTER_SANITIZE_URL);
        return filter_var($url, FILTER_VALIDATE_URL) ? $url : null;
    }
}
