<?php
/**
 * Logger Utility Class
 *
 * Provides JSON logging functionality for the update module
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Utils_Logger
{
    private $logFile;
    private $logDir;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->logDir = defined('LOGS_DIR_PATH') ? LOGS_DIR_PATH : 'maintenance/logs/';
        // Ensure logs directory exists with proper permissions
        if (!is_dir($this->logDir)) {
            if (!mkdir($this->logDir, 0755, true)) {
                //throw new Exception('Cannot create logs directory: ' . $this->logDir);
            }
        }

        // Ensure directory is writable
        if (!is_writable($this->logDir)) {
            if (!chmod($this->logDir, 0755)) {
                //throw new Exception('Cannot make logs directory writable: ' . $this->logDir);
            }
        }

        // Create log file with timestamp and session ID for uniqueness
        $timestamp = date('Y-m-d_His');
        $sessionId = substr(md5(uniqid()), 0, 8);
        $this->logFile = $this->logDir . 'update_log_' . $timestamp . '_' . $sessionId . '.json';

        // Initialize log file with header
        $this->initializeLogFile();
    }

    /**
     * Initialize log file with header information
     */
    private function initializeLogFile()
    {
        $header = array(
            'log_session_start' => date('Y-m-d H:i:s'),
            'log_file' => basename($this->logFile),
            'php_version' => phpversion(),
            'server_info' => isset($_SERVER['SERVER_SOFTWARE']) ? $_SERVER['SERVER_SOFTWARE'] : 'Unknown',
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'CLI'
        );

        $this->log('INFO', 'session_start', 'Log session initialized', $header);
    }

    /**
     * Log an entry
     *
     * @param string $level Log level (INFO, WARNING, ERROR)
     * @param string $operationType Type of operation
     * @param string $message Log message
     * @param array $details Additional details
     * @return bool
     */
    public function log($level, $operationType, $message, $details = array())
    {
        $entry = array(
            'timestamp' => date('Y-m-d H:i:s'),
            'microtime' => microtime(true),
            'level' => strtoupper($level),
            'operation_type' => $operationType,
            'message' => $message,
            'details' => $details,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true)
        );

        $jsonEntry = json_encode($entry, JSON_PRETTY_PRINT);
        if ($jsonEntry === false) {
            error_log('Failed to encode log entry to JSON: ' . json_last_error_msg());
            return false;
        }

        // Ensure log file exists and is writable
        if (!file_exists($this->logFile)) {
            touch($this->logFile);
            chmod($this->logFile, 0644);
        }

        $result = file_put_contents($this->logFile, $jsonEntry . "\n", FILE_APPEND | LOCK_EX);

        if ($result === false) {
            error_log('Failed to write to log file: ' . $this->logFile);
            return false;
        }

        return true;
    }

    /**
     * Log info message
     *
     * @param string $operationType
     * @param string $message
     * @param array $details
     * @return bool
     */
    public function info($operationType, $message, $details = array())
    {
        return $this->log('INFO', $operationType, $message, $details);
    }

    /**
     * Log warning message
     *
     * @param string $operationType
     * @param string $message
     * @param array $details
     * @return bool
     */
    public function warning($operationType, $message, $details = array())
    {
        return $this->log('WARNING', $operationType, $message, $details);
    }

    /**
     * Log error message
     *
     * @param string $operationType
     * @param string $message
     * @param array $details
     * @return bool
     */
    public function error($operationType, $message, $details = array())
    {
        return $this->log('ERROR', $operationType, $message, $details);
    }

    /**
     * Get log file path
     *
     * @return string
     */
    public function getLogFile()
    {
        return $this->logFile;
    }

    /**
     * Get log directory path
     *
     * @return string
     */
    public function getLogDir()
    {
        return $this->logDir;
    }

    /**
     * Get log file size in bytes
     *
     * @return int
     */
    public function getLogFileSize()
    {
        if (file_exists($this->logFile)) {
            return filesize($this->logFile);
        }
        return 0;
    }

    /**
     * Get log file size in human readable format
     *
     * @return string
     */
    public function getLogFileSizeFormatted()
    {
        $size = $this->getLogFileSize();
        $units = array('B', 'KB', 'MB', 'GB');

        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }

        return round($size, 2) . ' ' . $units[$i];
    }

    /**
     * Check if log file exists and is readable
     *
     * @return bool
     */
    public function isLogFileAccessible()
    {
        return file_exists($this->logFile) && is_readable($this->logFile);
    }

    /**
     * Finalize log session
     */
    public function finalizeSession()
    {
        $this->log('INFO', 'session_end', 'Log session finalized', array(
            'final_file_size' => $this->getLogFileSizeFormatted(),
            'session_duration' => 'Session completed'
        ));
    }
}
