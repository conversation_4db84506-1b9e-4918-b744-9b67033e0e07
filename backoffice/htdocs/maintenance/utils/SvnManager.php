<?php
/**
 * SVN Manager Utility Class
 *
 * Manages SVN update operations for engine and frontend
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Utils_SvnManager
{
    private $logger;
    private $credentials;

    /**
     * Constructor
     *
     * @param Maintenance_Utils_Logger $logger
     */
    public function __construct($logger)
    {
        $this->logger = $logger;
        $this->credentials = array("username" => "update", "password" => "Update34Riashop!");
    }

    /**
     * Set SVN credentials for authentication
     *
     * @param array $credentials
     */
    public function setCredentials($credentials)
    {
        $this->credentials = $credentials;
        $this->logger->info('svn_update', 'SVN credentials set', array(
            'username' => isset($credentials['username']) ? $credentials['username'] : 'not provided'
        ));
    }

    /**
     * Update engine SVN repository
     *
     * @return bool
     * @throws Exception
     */
    public function updateEngine()
    {
        $repoPath = defined('ENGINE_SVN_REPO_PATH') ? ENGINE_SVN_REPO_PATH : '/path/to/engine/repo';
        return $this->updateRepository($repoPath, 'engine');
    }

    /**
     * Update frontend SVN repository
     *
     * @return bool
     * @throws Exception
     */
    public function updateFrontend()
    {
        $repoPath = defined('FRONTEND_SVN_REPO_PATH') ? FRONTEND_SVN_REPO_PATH : '/path/to/frontend/repo';
        return $this->updateRepository($repoPath, 'frontend');
    }

    /**
     * Update SVN repository
     *
     * @param string $repoPath
     * @param string $type
     * @return bool
     * @throws Exception
     */
    private function updateRepository($repoPath, $type)
    {
        if (!is_dir($repoPath)) {
            $message = "Repository path does not exist: {$repoPath}";
            $this->logger->error('svn_update', $message, array(
                'repo_path' => $repoPath,
                'type' => $type
            ));
            throw new Exception($message);
        }

        // First, try to resolve any conflicts automatically
        $this->resolveConflicts($repoPath, $type);

        $command = "svn update " . escapeshellarg($repoPath) . " --accept theirs-full --non-interactive";

        // Add credentials if provided
        if ($this->credentials && isset($this->credentials['username']) && isset($this->credentials['password'])) {
            $command .= " --username " . escapeshellarg($this->credentials['username']);
            $command .= " --password " . escapeshellarg($this->credentials['password']);
            $command .= " --trust-server-cert";
        }

        $command .= " 2>&1";

        $outputCommand = preg_replace(
            [
                '/--username\s+(["\']).*?\1/',
                '/--password\s+(["\']).*?\1/'
            ],
            [
                '--username "[MASKED]"',
                '--password "[MASKED]"'
            ],
            $command
        );

        $this->logger->info('svn_update', "Starting SVN update for {$type}", array(
            'command' => $outputCommand,
            'repo_path' => $repoPath,
            'type' => $type
        ));

        $output = array();
        $returnCode = 0;

        exec($command, $output, $returnCode);

        $outputString = implode("\n", $output);

        if ($returnCode === 0) {
            $this->logger->info('svn_update', "SVN update successful for {$type}", array(
                'command' => $outputCommand,
                'output' => $outputString,
                'return_code' => $returnCode,
                'type' => $type
            ));
            return true;
        } else {
            // Try to handle common SVN errors
            $errorHandled = $this->handleSvnError($outputString, $repoPath, $type);
            if ($errorHandled) {
                // Retry the update after handling the error
                return $this->retryUpdate($repoPath, $type);
            }

            $message = "SVN update failed for {$type}";
            $this->logger->error('svn_update', $message, array(
                'command' => $command,
                'output' => $outputString,
                'return_code' => $returnCode,
                'type' => $type,
                'error_analysis' => $this->analyzeSvnError($outputString)
            ));
            throw new Exception($message . ": " . $this->formatSvnError($outputString));
        }
    }

    /**
     * Check if SVN is available
     *
     * @return bool
     */
    public function isSvnAvailable()
    {
        $command = "svn --version 2>&1";
        $output = array();
        $returnCode = 0;
        
        exec($command, $output, $returnCode);
        
        $available = $returnCode === 0;
        
        $this->logger->info('svn_update', 'SVN availability check', array(
            'available' => $available,
            'command' => $command,
            'return_code' => $returnCode,
            'output' => implode("\n", $output)
        ));
        
        return $available;
    }

    /**
     * Get SVN status for repository
     *
     * @param string $repoPath
     * @return array
     */
    public function getRepositoryStatus($repoPath)
    {
        if (!is_dir($repoPath)) {
            return array(
                'exists' => false,
                'is_svn' => false,
                'status' => 'Repository path does not exist'
            );
        }

        $svnDir = $repoPath . '/.svn';
        if (!is_dir($svnDir)) {
            return array(
                'exists' => true,
                'is_svn' => false,
                'status' => 'Not a SVN repository'
            );
        }

        $command = "svn status " . escapeshellarg($repoPath) . " 2>&1";
        $output = array();
        $returnCode = 0;

        exec($command, $output, $returnCode);

        return array(
            'exists' => true,
            'is_svn' => true,
            'status' => $returnCode === 0 ? 'OK' : 'Error',
            'output' => implode("\n", $output),
            'return_code' => $returnCode
        );
    }

    /**
     * Resolve SVN conflicts automatically
     *
     * @param string $repoPath
     * @param string $type
     * @return bool
     */
    private function resolveConflicts($repoPath, $type)
    {
        $command = "svn resolve --accept theirs-full " . escapeshellarg($repoPath) . " -R 2>&1";
        $output = array();
        $returnCode = 0;

        exec($command, $output, $returnCode);

        $this->logger->info('svn_update', "Conflict resolution attempted for {$type}", array(
            'command' => $command,
            'output' => implode("\n", $output),
            'return_code' => $returnCode
        ));

        return $returnCode === 0;
    }

    /**
     * Handle common SVN errors
     *
     * @param string $errorOutput
     * @param string $repoPath
     * @param string $type
     * @return bool
     */
    private function handleSvnError($errorOutput, $repoPath, $type)
    {
        // Handle authentication errors
        if (strpos($errorOutput, 'Authentication failed') !== false ||
            strpos($errorOutput, 'No more credentials') !== false) {
            $this->logger->warning('svn_update', "Authentication error detected for {$type}", array(
                'error' => $errorOutput,
                'solution' => 'Check SVN credentials and repository access'
            ));
            return false; // Cannot auto-fix authentication issues
        }

        // Handle working copy locked errors
        if (strpos($errorOutput, 'working copy locked') !== false) {
            $this->logger->info('svn_update', "Working copy locked, attempting cleanup for {$type}");
            return $this->cleanupWorkingCopy($repoPath, $type);
        }

        // Handle conflicts
        if (strpos($errorOutput, 'Conflict') !== false || strpos($errorOutput, 'conflict') !== false) {
            $this->logger->info('svn_update', "Conflicts detected, attempting resolution for {$type}");
            return $this->resolveConflicts($repoPath, $type);
        }

        // Handle out of date working copy
        if (strpos($errorOutput, 'out of date') !== false) {
            $this->logger->info('svn_update', "Working copy out of date for {$type}");
            return true; // This should be handled by the update itself
        }

        return false;
    }

    /**
     * Cleanup SVN working copy
     *
     * @param string $repoPath
     * @param string $type
     * @return bool
     */
    private function cleanupWorkingCopy($repoPath, $type)
    {
        $command = "svn cleanup " . escapeshellarg($repoPath) . " 2>&1";
        $output = array();
        $returnCode = 0;

        exec($command, $output, $returnCode);

        $this->logger->info('svn_update', "SVN cleanup attempted for {$type}", array(
            'command' => $command,
            'output' => implode("\n", $output),
            'return_code' => $returnCode
        ));

        return $returnCode === 0;
    }

    /**
     * Cleanup engine SVN repository
     *
     * @return array
     * @throws Exception
     */
    public function cleanupEngine()
    {
        $repoPath = defined('ENGINE_SVN_REPO_PATH') ? ENGINE_SVN_REPO_PATH : '/path/to/engine/repo';
        return $this->cleanupRepository($repoPath, 'engine');
    }

    /**
     * Cleanup frontend SVN repository
     *
     * @return array
     * @throws Exception
     */
    public function cleanupFrontend()
    {
        $repoPath = defined('FRONTEND_SVN_REPO_PATH') ? FRONTEND_SVN_REPO_PATH : '/path/to/frontend/repo';
        return $this->cleanupRepository($repoPath, 'frontend');
    }

    /**
     * Cleanup SVN repository with detailed feedback
     *
     * @param string $repoPath
     * @param string $type
     * @return array
     * @throws Exception
     */
    private function cleanupRepository($repoPath, $type)
    {
        if (!is_dir($repoPath)) {
            $message = "Repository path does not exist: {$repoPath}";
            $this->logger->error('svn_cleanup', $message, array(
                'repo_path' => $repoPath,
                'type' => $type
            ));
            throw new Exception($message);
        }

        $svnDir = $repoPath . '/.svn';
        if (!is_dir($svnDir)) {
            $message = "Not a SVN repository: {$repoPath}";
            $this->logger->error('svn_cleanup', $message, array(
                'repo_path' => $repoPath,
                'type' => $type
            ));
            throw new Exception($message);
        }

        $command = "svn cleanup " . escapeshellarg($repoPath) . " 2>&1";

        $this->logger->info('svn_cleanup', "Starting SVN cleanup for {$type}", array(
            'command' => $command,
            'repo_path' => $repoPath,
            'type' => $type
        ));

        $output = array();
        $returnCode = 0;
        $startTime = microtime(true);

        exec($command, $output, $returnCode);

        $endTime = microtime(true);
        $executionTime = round(($endTime - $startTime) * 1000, 2);
        $outputString = implode("\n", $output);

        if ($returnCode === 0) {
            $this->logger->info('svn_cleanup', "SVN cleanup successful for {$type}", array(
                'command' => $command,
                'output' => $outputString,
                'return_code' => $returnCode,
                'execution_time_ms' => $executionTime,
                'type' => $type
            ));

            return array(
                'success' => true,
                'message' => "SVN cleanup completed successfully for {$type}",
                'output' => $outputString,
                'execution_time_ms' => $executionTime,
                'repo_path' => $repoPath,
                'type' => $type
            );
        } else {
            $message = "SVN cleanup failed for {$type}";
            $this->logger->error('svn_cleanup', $message, array(
                'command' => $command,
                'output' => $outputString,
                'return_code' => $returnCode,
                'execution_time_ms' => $executionTime,
                'type' => $type,
                'error_analysis' => $this->analyzeSvnError($outputString)
            ));

            throw new Exception($message . ": " . $this->formatSvnError($outputString));
        }
    }

    /**
     * Retry SVN update after error handling
     *
     * @param string $repoPath
     * @param string $type
     * @return bool
     * @throws Exception
     */
    private function retryUpdate($repoPath, $type)
    {
        $command = "svn update " . escapeshellarg($repoPath) . " --accept theirs-full --non-interactive";

        // Add credentials if provided
        if ($this->credentials && isset($this->credentials['username']) && isset($this->credentials['password'])) {
            $command .= " --username " . escapeshellarg($this->credentials['username']);
            $command .= " --password " . escapeshellarg($this->credentials['password']);
            $command .= " --trust-server-cert";
        }

        $command .= " 2>&1";

        $output = array();
        $returnCode = 0;

        exec($command, $output, $returnCode);

        $outputString = implode("\n", $output);

        if ($returnCode === 0) {
            $this->logger->info('svn_update', "SVN update retry successful for {$type}", array(
                'command' => $command,
                'output' => $outputString,
                'return_code' => $returnCode
            ));
            return true;
        } else {
            $this->logger->error('svn_update', "SVN update retry failed for {$type}", array(
                'command' => $command,
                'output' => $outputString,
                'return_code' => $returnCode
            ));
            throw new Exception("SVN update retry failed for {$type}: " . $this->formatSvnError($outputString));
        }
    }

    /**
     * Analyze SVN error for better user feedback
     *
     * @param string $errorOutput
     * @return array
     */
    private function analyzeSvnError($errorOutput)
    {
        $analysis = array();

        if (strpos($errorOutput, 'E170013') !== false) {
            $analysis['error_code'] = 'E170013';
            $analysis['description'] = 'Unable to connect to repository';
            $analysis['possible_causes'] = array(
                'Network connectivity issues',
                'Repository URL is incorrect',
                'Repository server is down'
            );
        }

        if (strpos($errorOutput, 'E215004') !== false) {
            $analysis['error_code'] = 'E215004';
            $analysis['description'] = 'Authentication failed';
            $analysis['possible_causes'] = array(
                'Invalid credentials',
                'Credentials not stored',
                'Repository requires different authentication method'
            );
        }

        if (strpos($errorOutput, 'working copy locked') !== false) {
            $analysis['error_code'] = 'LOCKED';
            $analysis['description'] = 'Working copy is locked';
            $analysis['possible_causes'] = array(
                'Previous SVN operation was interrupted',
                'Multiple SVN operations running simultaneously'
            );
        }

        return $analysis;
    }

    /**
     * Format SVN error for user display
     *
     * @param string $errorOutput
     * @return string
     */
    private function formatSvnError($errorOutput)
    {
        $analysis = $this->analyzeSvnError($errorOutput);

        if (!empty($analysis)) {
            $formatted = $analysis['description'];
            if (!empty($analysis['possible_causes'])) {
                $formatted .= "\n\nCauses possibles:\n• " . implode("\n• ", $analysis['possible_causes']);
            }
            return $formatted;
        }

        // Return first few lines of error for unknown errors
        $lines = explode("\n", $errorOutput);
        return implode("\n", array_slice($lines, 0, 3));
    }
}
