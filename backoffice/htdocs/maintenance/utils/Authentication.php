<?php
/**
 * Authentication Utility Class
 *
 * Handles user authentication and authorization for the maintenance interface
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Utils_Authentication
{
    /**
     * Check if user is authenticated and authorized
     *
     * @return bool
     */
    public static function isAuthorized()
    {
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Check if user is a RiaStudio user or specific authorized email
        global $config;

        $isRiaStudioUser = isset($config['USER_RIASTUDIO']) && $config['USER_RIASTUDIO'];
        $isAuthorizedEmail = isset($_SESSION['usr_email']) && $_SESSION['usr_email'] === '<EMAIL>';

        return $isRiaStudioUser || $isAuthorizedEmail;
    }

    /**
     * Require authentication or send 403 response
     *
     * @return void
     */
    public static function requireAuth()
    {
        if (!self::isAuthorized()) {
            self::sendForbiddenResponse();
        }
    }

    /**
     * Send 403 Forbidden response
     *
     * @return void
     */
    public static function sendForbiddenResponse()
    {
        if (function_exists('http_response_code')) {
            http_response_code(HTTP_FORBIDDEN);
        } else {
            header('HTTP/1.1 403 Forbidden');
        }

        // Check if it's an AJAX request
        if (self::isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'Access forbidden',
                'code' => HTTP_FORBIDDEN
            ]);
        } else {
            // Include the HTTP utility if available
            if (function_exists('http_403')) {
                http_403();
            } else {
                echo '<h1>403 Forbidden</h1><p>You do not have permission to access this resource.</p>';
            }
        }
        exit;
    }

    /**
     * Check if the current request is an AJAX request
     *
     * @return bool
     */
    public static function isAjaxRequest()
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Generate CSRF token
     *
     * @return string
     */
    public static function generateCsrfToken()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Use random_bytes if available (PHP 7+), otherwise fallback to openssl_random_pseudo_bytes
        if (function_exists('random_bytes')) {
            $token = bin2hex(random_bytes(CSRF_TOKEN_LENGTH));
        } else {
            $token = bin2hex(openssl_random_pseudo_bytes(CSRF_TOKEN_LENGTH));
        }
        $_SESSION['csrf_token'] = $token;
        $_SESSION['csrf_token_time'] = time();

        return $token;
    }

    /**
     * Validate CSRF token
     *
     * @param string $token
     * @return bool
     */
    public static function validateCsrfToken($token)
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Check if token exists in session
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
            return false;
        }

        // Check token expiration (1 hour)
        if (time() - $_SESSION['csrf_token_time'] > 3600) {
            unset($_SESSION['csrf_token'], $_SESSION['csrf_token_time']);
            return false;
        }

        // Validate token
        $isValid = hash_equals($_SESSION['csrf_token'], $token);
        
        // Remove token after validation (one-time use)
        if ($isValid) {
            unset($_SESSION['csrf_token'], $_SESSION['csrf_token_time']);
        }
        
        return $isValid;
    }

    /**
     * Get current user information
     *
     * @return array
     */
    public static function getCurrentUser()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        return [
            'email' => isset($_SESSION['usr_email']) ? $_SESSION['usr_email'] : null,
            'id' => isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : null,
            'is_riastudio' => isset($GLOBALS['config']['USER_RIASTUDIO']) && $GLOBALS['config']['USER_RIASTUDIO'],
            'is_authenticated' => self::isAuthorized()
        ];
    }

    /**
     * Log authentication attempt
     *
     * @param string $email
     * @param bool $success
     * @param string $ip
     * @return void
     */
    public static function logAuthAttempt($email, $success, $ip)
    {
        $status = $success ? 'SUCCESS' : 'FAILED';
        $message = sprintf(
            'Authentication %s for email: %s from IP: %s',
            $status,
            $email,
            $ip
        );
        
        error_log($message);
    }

    /**
     * Check rate limiting for authentication attempts
     *
     * @param string $ip
     * @return bool
     */
    public static function isRateLimited($ip)
    {
        // Simple file-based rate limiting
        $rateLimitFile = sys_get_temp_dir() . '/auth_attempts_' . md5($ip);
        
        if (!file_exists($rateLimitFile)) {
            return false;
        }
        
        $attempts = json_decode(file_get_contents($rateLimitFile), true);
        if (!$attempts) {
            return false;
        }
        
        // Clean old attempts (older than 1 hour)
        $attempts = array_filter($attempts, function($timestamp) {
            return time() - $timestamp < 3600;
        });
        
        // Update file with cleaned attempts
        file_put_contents($rateLimitFile, json_encode($attempts));
        
        return count($attempts) >= MAX_LOGIN_ATTEMPTS;
    }

    /**
     * Record authentication attempt for rate limiting
     *
     * @param string $ip
     * @return void
     */
    public static function recordAuthAttempt($ip)
    {
        $rateLimitFile = sys_get_temp_dir() . '/auth_attempts_' . md5($ip);
        
        $attempts = [];
        if (file_exists($rateLimitFile)) {
            $attempts = json_decode(file_get_contents($rateLimitFile), true) ?: [];
        }
        
        $attempts[] = time();
        file_put_contents($rateLimitFile, json_encode($attempts));
    }
}
