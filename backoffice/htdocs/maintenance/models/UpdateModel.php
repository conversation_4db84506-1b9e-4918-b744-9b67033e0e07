<?php
/**
 * Update Model Class
 *
 * Handles update operations for backend and frontend
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Models_UpdateModel extends Maintenance_Models_BaseModel
{
    private $logger;
    private $versionManager;
    private $svnManager;
    private $scriptExecutor;

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        
        $this->logger = new Maintenance_Utils_Logger();
        $this->versionManager = new Maintenance_Utils_VersionManager($this->logger);
        $this->svnManager = new Maintenance_Utils_SvnManager($this->logger);
        $this->scriptExecutor = new Maintenance_Utils_ScriptExecutor($this->logger, $this->versionManager);
    }

    /**
     * Perform backend update
     *
     * @return array
     */
    public function performBackendUpdate()
    {
        $this->logger->info('backend_update', 'Starting backend update process');
        
        try {
            // Step 1: Get current version
            $currentVersion = $this->versionManager->getCurrentVersion();
            
            // Step 2: Update SVN repository
            $this->svnManager->updateEngine();
            
            // Step 3: Get applicable scripts
            $scripts = $this->scriptExecutor->getApplicableScripts($currentVersion);
            
            if (empty($scripts)) {
                $this->logger->info('backend_update', 'No update scripts to execute', array(
                    'current_version' => $currentVersion
                ));
                return array(
                    'success' => true,
                    'message' => 'No updates available',
                    'current_version' => $currentVersion,
                    'scripts_executed' => 0,
                    'log_file' => $this->logger->getLogFile()
                );
            }
            
            // Step 4: Execute scripts sequentially
            $lastVersion = $currentVersion;
            $scriptsExecuted = 0;
            
            foreach ($scripts as $script) {
                if ($script['type'] === 'sql') {
                    $executionResult = $this->scriptExecutor->executeSqlScript($script);
                    // Check if execution was successful
                    if (!$executionResult['success']) {
                        throw new Exception('SQL script execution failed');
                    }
                } elseif ($script['type'] === 'php') {
                    $executionResult = $this->scriptExecutor->executePhpScript($script);
                    // Check if execution was successful
                    if (!$executionResult['success']) {
                        throw new Exception('PHP script execution failed');
                    }
                }

                $lastVersion = $script['version'];
                $scriptsExecuted++;
            }
            
            // Step 5: Update version in database
            if ($scriptsExecuted > 0) {
                $this->versionManager->updateVersion($lastVersion);
            }
            
            $this->logger->info('backend_update', 'Backend update completed successfully', array(
                'initial_version' => $currentVersion,
                'final_version' => $lastVersion,
                'scripts_executed' => $scriptsExecuted
            ));
            
            return array(
                'success' => true,
                'message' => 'Backend update completed successfully',
                'initial_version' => $currentVersion,
                'final_version' => $lastVersion,
                'scripts_executed' => $scriptsExecuted,
                'log_file' => $this->logger->getLogFile()
            );
            
        } catch (Exception $e) {
            $this->logger->error('backend_update', 'Backend update failed', array(
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ));
            
            return array(
                'success' => false,
                'message' => 'Backend update failed: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'log_file' => $this->logger->getLogFile()
            );
        }
    }

    /**
     * Perform frontend update
     *
     * @return array
     */
    public function performFrontendUpdate()
    {
        $this->logger->info('frontend_update', 'Starting frontend update process');
        
        try {
            // Update SVN repository
            $this->svnManager->updateFrontend();
            
            $this->logger->info('frontend_update', 'Frontend update completed successfully');
            
            return array(
                'success' => true,
                'message' => 'Frontend update completed successfully',
                'log_file' => $this->logger->getLogFile()
            );
            
        } catch (Exception $e) {
            $this->logger->error('frontend_update', 'Frontend update failed', array(
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ));
            
            return array(
                'success' => false,
                'message' => 'Frontend update failed: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'log_file' => $this->logger->getLogFile()
            );
        }
    }

    /**
     * Get system status for updates
     *
     * @return array
     */
    public function getSystemStatus()
    {
        $currentVersion = $this->versionManager->getCurrentVersion();

        // Get all scripts for comprehensive status display
        $allScripts = $this->scriptExecutor->getAllScripts($currentVersion);
        $applicableScripts = $this->scriptExecutor->getApplicableScripts($currentVersion);

        $enginePath = defined('ENGINE_SVN_REPO_PATH') ? ENGINE_SVN_REPO_PATH : '/var/www/engine';
        $frontendPath = defined('FRONTEND_SVN_REPO_PATH') ? FRONTEND_SVN_REPO_PATH : '/var/www/frontend';

        $engineStatus = $this->getDetailedRepositoryStatus($enginePath, 'engine');
        $frontendStatus = $this->getDetailedRepositoryStatus($frontendPath, 'frontend');

        // Group scripts by type for granular workflow
        $allSqlScripts = array_filter($allScripts, function($script) { return $script['type'] === 'sql'; });
        $allPhpScripts = array_filter($allScripts, function($script) { return $script['type'] === 'php'; });

        $applicableSqlScripts = array_filter($applicableScripts, function($script) { return $script['type'] === 'sql'; });
        $applicablePhpScripts = array_filter($applicableScripts, function($script) { return $script['type'] === 'php'; });

        return array(
            'current_version' => $currentVersion,
            'available_updates' => count($applicableScripts),
            'total_scripts_found' => count($allScripts),
            'sql_scripts' => array_values($applicableSqlScripts),
            'php_scripts' => array_values($applicablePhpScripts),
            'all_sql_scripts' => array_values($allSqlScripts),
            'all_php_scripts' => array_values($allPhpScripts),
            'update_scripts' => array_map(function($script) {
                return array(
                    'file' => $script['file'],
                    'version' => $script['version'],
                    'type' => $script['type'],
                    'status' => $script['status']
                );
            }, $allScripts),
            'svn_available' => $this->svnManager->isSvnAvailable(),
            'engine_repository' => $engineStatus,
            'frontend_repository' => $frontendStatus,
            'logs_directory' => array(
                'path' => $this->logger->getLogDir(),
                'writable' => is_writable($this->logger->getLogDir()),
                'exists' => is_dir($this->logger->getLogDir())
            ),
            'workflow_steps' => $this->getWorkflowSteps($currentVersion, $allScripts)
        );
    }

    /**
     * Get detailed repository status with SVN information
     *
     * @param string $path
     * @param string $type
     * @return array
     */
    private function getDetailedRepositoryStatus($path, $type)
    {
        $status = $this->svnManager->getRepositoryStatus($path);

        // Add detailed SVN information
        $status['path'] = $path;
        $status['url'] = '';
        $status['revision'] = '';
        $status['last_changed'] = '';

        if ($status['exists'] && $status['is_svn']) {
            $svnInfo = $this->getSvnInfo($path);
            $status = array_merge($status, $svnInfo);
        }

        return $status;
    }

    /**
     * Get SVN information for a repository
     *
     * @param string $path
     * @return array
     */
    private function getSvnInfo($path)
    {
        $info = array(
            'url' => '',
            'revision' => '',
            'last_changed' => '',
            'last_author' => ''
        );

        $command = "svn info " . escapeshellarg($path) . " 2>/dev/null";
        $output = shell_exec($command);

        if ($output) {
            if (preg_match('/^URL: (.+)$/m', $output, $matches)) {
                $info['url'] = trim($matches[1]);
            }

            if (preg_match('/^Revision: (\d+)$/m', $output, $matches)) {
                $info['revision'] = trim($matches[1]);
            }

            if (preg_match('/^Last Changed Date: (.+)$/m', $output, $matches)) {
                $info['last_changed'] = trim($matches[1]);
            }

            if (preg_match('/^Last Changed Author: (.+)$/m', $output, $matches)) {
                $info['last_author'] = trim($matches[1]);
            }
        }

        return $info;
    }

    /**
     * Get workflow steps for backend update
     *
     * @param string $currentVersion
     * @param array $scripts
     * @return array
     */
    private function getWorkflowSteps($currentVersion, $scripts)
    {
        $sqlScripts = array_filter($scripts, function($script) { return $script['type'] === 'sql'; });
        $phpScripts = array_filter($scripts, function($script) { return $script['type'] === 'php'; });

        $steps = array(
            'backend' => array(
                array(
                    'id' => 'svn_update_engine',
                    'name' => 'Update SVN Engine',
                    'description' => 'Update the engine repository from SVN',
                    'status' => 'pending',
                    'required' => true
                ),
                array(
                    'id' => 'execute_sql_scripts',
                    'name' => 'Execute SQL Scripts',
                    'description' => 'Execute database migration scripts',
                    'status' => 'pending',
                    'required' => count($sqlScripts) > 0,
                    'scripts_count' => count($sqlScripts)
                ),
                array(
                    'id' => 'execute_php_scripts',
                    'name' => 'Execute PHP Scripts',
                    'description' => 'Execute PHP migration scripts',
                    'status' => 'pending',
                    'required' => count($phpScripts) > 0,
                    'scripts_count' => count($phpScripts)
                ),
                array(
                    'id' => 'update_version',
                    'name' => 'Update Version',
                    'description' => 'Update version number in database',
                    'status' => 'pending',
                    'required' => count($scripts) > 0
                )
            ),
            'frontend' => array(
                array(
                    'id' => 'svn_update_frontend',
                    'name' => 'Update SVN Frontend',
                    'description' => 'Update the frontend repository from SVN',
                    'status' => 'pending',
                    'required' => true
                )
            )
        );

        return $steps;
    }

    /**
     * Execute individual step: SVN Update Engine
     *
     * @param array $credentials Optional SVN credentials
     * @return array
     */
    public function executeSvnUpdateEngine($credentials = null)
    {
        $this->logger->info('backend_update_step', 'Starting SVN update engine step');

        try {
            if ($credentials) {
                $this->svnManager->setCredentials($credentials);
            }

            $this->svnManager->updateEngine();

            $this->logger->info('backend_update_step', 'SVN update engine completed successfully');

            return array(
                'success' => true,
                'message' => 'Engine SVN update completed successfully',
                'step' => 'svn_update_engine',
                'log_file' => $this->logger->getLogFile()
            );

        } catch (Exception $e) {
            $this->logger->error('backend_update_step', 'SVN update engine failed', array(
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ));

            return array(
                'success' => false,
                'message' => 'Engine SVN update failed: ' . $e->getMessage(),
                'step' => 'svn_update_engine',
                'error' => $e->getMessage(),
                'log_file' => $this->logger->getLogFile()
            );
        }
    }

    /**
     * Execute individual step: SQL Scripts
     *
     * @return array
     */
    public function executeSqlScripts()
    {
        $this->logger->info('backend_update_step', 'Starting SQL scripts execution step');

        try {
            $currentVersion = $this->versionManager->getCurrentVersion();
            // Use getApplicableScripts to only get scripts with version > current
            $scripts = $this->scriptExecutor->getApplicableScripts($currentVersion);
            $sqlScripts = array_filter($scripts, function($script) { return $script['type'] === 'sql'; });

            if (empty($sqlScripts)) {
                // Also check if there are any SQL scripts at all in the directory
                $allSqlScripts = $this->scriptExecutor->getScripts($currentVersion, 'all');
                $allSqlScripts = array_filter($allSqlScripts, function($script) { return $script['type'] === 'sql'; });

                $message = empty($allSqlScripts) ? 'No SQL scripts found in directory' : 'No SQL scripts to execute (all scripts are older than or equal to current version)';

                return array(
                    'success' => true,
                    'message' => $message,
                    'step' => 'execute_sql_scripts',
                    'scripts_executed' => 0,
                    'scripts_found' => count($allSqlScripts),
                    'log_file' => $this->logger->getLogFile()
                );
            }

            $scriptsExecuted = 0;
            $scriptDetails = array();
            $allExecutionResults = array();

            foreach ($sqlScripts as $script) {
                $executionResult = $this->scriptExecutor->executeSqlScript($script);
                $scriptsExecuted++;
                $scriptDetails[] = array(
                    'file' => $script['file'],
                    'version' => $script['version'],
                    'status' => $script['status'],
                    'execution_time_ms' => $executionResult['total_execution_time_ms'],
                    'statements_executed' => $executionResult['statements_executed']
                );

                // Add detailed execution results for frontend display
                $allExecutionResults[] = array(
                    'script_file' => $script['file'],
                    'detailed_results' => $executionResult['detailed_results']
                );
            }

            $this->logger->info('backend_update_step', 'SQL scripts execution completed successfully', array(
                'scripts_executed' => $scriptsExecuted,
                'script_details' => $scriptDetails
            ));

            return array(
                'success' => true,
                'message' => 'SQL scripts executed successfully',
                'step' => 'execute_sql_scripts',
                'scripts_executed' => $scriptsExecuted,
                'script_details' => $scriptDetails,
                'execution_results' => $allExecutionResults,
                'log_file' => $this->logger->getLogFile()
            );

        } catch (Exception $e) {
            $this->logger->error('backend_update_step', 'SQL scripts execution failed', array(
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ));

            return array(
                'success' => false,
                'message' => 'SQL scripts execution failed: ' . $e->getMessage(),
                'step' => 'execute_sql_scripts',
                'error' => $e->getMessage(),
                'log_file' => $this->logger->getLogFile()
            );
        }
    }

    /**
     * Execute individual step: PHP Scripts
     *
     * @return array
     */
    public function executePhpScripts()
    {
        $this->logger->info('backend_update_step', 'Starting PHP scripts execution step');

        try {
            $currentVersion = $this->versionManager->getCurrentVersion();
            // Use getApplicableScripts to only get scripts with version > current
            $scripts = $this->scriptExecutor->getApplicableScripts($currentVersion);
            $phpScripts = array_filter($scripts, function($script) { return $script['type'] === 'php'; });

            if (empty($phpScripts)) {
                // Also check if there are any PHP scripts at all in the directory
                $allPhpScripts = $this->scriptExecutor->getScripts($currentVersion, 'all');
                $allPhpScripts = array_filter($allPhpScripts, function($script) { return $script['type'] === 'php'; });

                $message = empty($allPhpScripts) ? 'No PHP scripts found in directory' : 'No PHP scripts to execute (all scripts are older than or equal to current version)';

                return array(
                    'success' => true,
                    'message' => $message,
                    'step' => 'execute_php_scripts',
                    'scripts_executed' => 0,
                    'scripts_found' => count($allPhpScripts),
                    'log_file' => $this->logger->getLogFile()
                );
            }

            $scriptsExecuted = 0;
            $scriptDetails = array();
            $allExecutionResults = array();

            foreach ($phpScripts as $script) {
                $executionResult = $this->scriptExecutor->executePhpScript($script);
                $scriptsExecuted++;
                $scriptDetails[] = array(
                    'file' => $script['file'],
                    'version' => $script['version'],
                    'status' => $script['status'],
                    'execution_time_ms' => $executionResult['execution_time_ms'],
                    'memory_usage' => $executionResult['memory_after'] - $executionResult['memory_before']
                );

                // Add detailed execution results for frontend display
                $allExecutionResults[] = array(
                    'script_file' => $script['file'],
                    'output' => $executionResult['output'],
                    'result' => $executionResult['result'],
                    'execution_time_ms' => $executionResult['execution_time_ms'],
                    'memory_usage' => $executionResult['memory_after'] - $executionResult['memory_before']
                );
            }

            $this->logger->info('backend_update_step', 'PHP scripts execution completed successfully', array(
                'scripts_executed' => $scriptsExecuted,
                'script_details' => $scriptDetails
            ));

            return array(
                'success' => true,
                'message' => 'PHP scripts executed successfully',
                'step' => 'execute_php_scripts',
                'scripts_executed' => $scriptsExecuted,
                'script_details' => $scriptDetails,
                'execution_results' => $allExecutionResults,
                'log_file' => $this->logger->getLogFile()
            );

        } catch (Exception $e) {
            $this->logger->error('backend_update_step', 'PHP scripts execution failed', array(
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ));

            return array(
                'success' => false,
                'message' => 'PHP scripts execution failed: ' . $e->getMessage(),
                'step' => 'execute_php_scripts',
                'error' => $e->getMessage(),
                'log_file' => $this->logger->getLogFile()
            );
        }
    }

    /**
     * Execute individual step: Update Version
     *
     * @return array
     */
    public function executeUpdateVersion()
    {
        $this->logger->info('backend_update_step', 'Starting version update step');

        try {
            $currentVersion = $this->versionManager->getCurrentVersion();
            $scripts = $this->scriptExecutor->getApplicableScripts($currentVersion);

            if (empty($scripts)) {
                return array(
                    'success' => true,
                    'message' => 'No version update needed',
                    'step' => 'update_version',
                    'current_version' => $currentVersion,
                    'log_file' => $this->logger->getLogFile()
                );
            }

            // Get the highest version from executed scripts
            $versions = array_map(function($script) { return $script['version']; }, $scripts);
            usort($versions, array($this->versionManager, 'compareVersions'));
            $newVersion = end($versions);

            $this->versionManager->updateVersion($newVersion);

            $this->logger->info('backend_update_step', 'Version update completed successfully', array(
                'old_version' => $currentVersion,
                'new_version' => $newVersion
            ));

            return array(
                'success' => true,
                'message' => 'Version updated successfully',
                'step' => 'update_version',
                'old_version' => $currentVersion,
                'new_version' => $newVersion,
                'log_file' => $this->logger->getLogFile()
            );

        } catch (Exception $e) {
            $this->logger->error('backend_update_step', 'Version update failed', array(
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ));

            return array(
                'success' => false,
                'message' => 'Version update failed: ' . $e->getMessage(),
                'step' => 'update_version',
                'error' => $e->getMessage(),
                'log_file' => $this->logger->getLogFile()
            );
        }
    }

    /**
     * Execute individual step: SVN Update Frontend
     *
     * @param array $credentials Optional SVN credentials
     * @return array
     */
    public function executeSvnUpdateFrontend($credentials = null)
    {
        $this->logger->info('frontend_update_step', 'Starting SVN update frontend step');

        try {
            if ($credentials) {
                $this->svnManager->setCredentials($credentials);
            }

            $this->svnManager->updateFrontend();

            $this->logger->info('frontend_update_step', 'SVN update frontend completed successfully');

            return array(
                'success' => true,
                'message' => 'Frontend SVN update completed successfully',
                'step' => 'svn_update_frontend',
                'log_file' => $this->logger->getLogFile()
            );

        } catch (Exception $e) {
            $this->logger->error('frontend_update_step', 'SVN update frontend failed', array(
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ));

            return array(
                'success' => false,
                'message' => 'Frontend SVN update failed: ' . $e->getMessage(),
                'step' => 'svn_update_frontend',
                'error' => $e->getMessage(),
                'log_file' => $this->logger->getLogFile()
            );
        }
    }

    /**
     * Execute SVN cleanup for engine repository
     *
     * @return array
     */
    public function executeSvnCleanupEngine()
    {
        $this->logger->info('svn_cleanup_step', 'Starting SVN cleanup engine step');

        try {
            $result = $this->svnManager->cleanupEngine();

            $this->logger->info('svn_cleanup_step', 'SVN cleanup engine completed successfully', array(
                'execution_time_ms' => $result['execution_time_ms'],
                'repo_path' => $result['repo_path']
            ));

            return array(
                'success' => true,
                'message' => $result['message'],
                'step' => 'svn_cleanup_engine',
                'data' => array(
                    'execution_time_ms' => $result['execution_time_ms'],
                    'repo_path' => $result['repo_path'],
                    'output' => $result['output'],
                    'type' => $result['type']
                ),
                'log_file' => $this->logger->getLogFile()
            );

        } catch (Exception $e) {
            $this->logger->error('svn_cleanup_step', 'SVN cleanup engine failed', array(
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ));

            return array(
                'success' => false,
                'message' => 'Engine SVN cleanup failed: ' . $e->getMessage(),
                'step' => 'svn_cleanup_engine',
                'error' => $e->getMessage(),
                'log_file' => $this->logger->getLogFile()
            );
        }
    }

    /**
     * Execute SVN cleanup for frontend repository
     *
     * @return array
     */
    public function executeSvnCleanupFrontend()
    {
        $this->logger->info('svn_cleanup_step', 'Starting SVN cleanup frontend step');

        try {
            $result = $this->svnManager->cleanupFrontend();

            $this->logger->info('svn_cleanup_step', 'SVN cleanup frontend completed successfully', array(
                'execution_time_ms' => $result['execution_time_ms'],
                'repo_path' => $result['repo_path']
            ));

            return array(
                'success' => true,
                'message' => $result['message'],
                'step' => 'svn_cleanup_frontend',
                'data' => array(
                    'execution_time_ms' => $result['execution_time_ms'],
                    'repo_path' => $result['repo_path'],
                    'output' => $result['output'],
                    'type' => $result['type']
                ),
                'log_file' => $this->logger->getLogFile()
            );

        } catch (Exception $e) {
            $this->logger->error('svn_cleanup_step', 'SVN cleanup frontend failed', array(
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ));

            return array(
                'success' => false,
                'message' => 'Frontend SVN cleanup failed: ' . $e->getMessage(),
                'step' => 'svn_cleanup_frontend',
                'error' => $e->getMessage(),
                'log_file' => $this->logger->getLogFile()
            );
        }
    }

    /**
     * Replay SQL scripts (execute all scripts regardless of version)
     *
     * @return array
     */
    public function replaySqlScripts()
    {
        $this->logger->info('backend_update_step', 'Starting SQL scripts replay (all scripts)');

        try {
            $currentVersion = $this->versionManager->getCurrentVersion();
            $scripts = $this->scriptExecutor->getScripts($currentVersion, 'all');
            $sqlScripts = array_filter($scripts, function($script) { return $script['type'] === 'sql'; });

            if (empty($sqlScripts)) {
                return array(
                    'success' => true,
                    'message' => 'No SQL scripts found for replay',
                    'step' => 'replay_sql_scripts',
                    'scripts_executed' => 0,
                    'log_file' => $this->logger->getLogFile()
                );
            }

            $scriptsExecuted = 0;
            $scriptDetails = array();
            $executionResults = array();

            foreach ($sqlScripts as $script) {
                try {
                    $startTime = microtime(true);
                    $executionResult = $this->scriptExecutor->executeSqlScript($script);
                    $endTime = microtime(true);
                    $executionTime = round(($endTime - $startTime) * 1000, 2);

                    $scriptsExecuted++;
                    $scriptDetails[] = array(
                        'file' => $script['file'],
                        'version' => $script['version'],
                        'status' => $script['status'],
                        'execution_status' => 'success',
                        'execution_time_ms' => $executionTime,
                        'statements_executed' => $executionResult['statements_executed']
                    );

                    $executionResults[] = array(
                        'script_file' => $script['file'],
                        'detailed_results' => $executionResult['detailed_results']
                    );
                } catch (Exception $e) {
                    $scriptDetails[] = array(
                        'file' => $script['file'],
                        'version' => $script['version'],
                        'status' => $script['status'],
                        'execution_status' => 'failed',
                        'error' => $e->getMessage()
                    );

                    $executionResults[] = array(
                        'script_file' => $script['file'],
                        'error' => $e->getMessage()
                    );
                }
            }

            $this->logger->info('backend_update_step', 'SQL scripts replay completed', array(
                'scripts_executed' => $scriptsExecuted,
                'total_scripts' => count($sqlScripts),
                'script_details' => $scriptDetails
            ));

            return array(
                'success' => true,
                'message' => 'SQL scripts replay completed',
                'step' => 'replay_sql_scripts',
                'scripts_executed' => $scriptsExecuted,
                'total_scripts' => count($sqlScripts),
                'script_details' => $scriptDetails,
                'execution_results' => $executionResults,
                'log_file' => $this->logger->getLogFile()
            );

        } catch (Exception $e) {
            $this->logger->error('backend_update_step', 'SQL scripts replay failed', array(
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ));

            return array(
                'success' => false,
                'message' => 'SQL scripts replay failed: ' . $e->getMessage(),
                'step' => 'replay_sql_scripts',
                'error' => $e->getMessage(),
                'log_file' => $this->logger->getLogFile()
            );
        }
    }

    /**
     * Replay PHP scripts (execute all scripts regardless of version)
     *
     * @return array
     */
    public function replayPhpScripts()
    {
        $this->logger->info('backend_update_step', 'Starting PHP scripts replay (all scripts)');

        try {
            $currentVersion = $this->versionManager->getCurrentVersion();
            $scripts = $this->scriptExecutor->getScripts($currentVersion, 'all');
            $phpScripts = array_filter($scripts, function($script) { return $script['type'] === 'php'; });

            if (empty($phpScripts)) {
                return array(
                    'success' => true,
                    'message' => 'No PHP scripts found for replay',
                    'step' => 'replay_php_scripts',
                    'scripts_executed' => 0,
                    'log_file' => $this->logger->getLogFile()
                );
            }

            $scriptsExecuted = 0;
            $scriptDetails = array();
            $executionResults = array();

            foreach ($phpScripts as $script) {
                try {
                    $startTime = microtime(true);
                    $executionResult = $this->scriptExecutor->executePhpScript($script);
                    $endTime = microtime(true);
                    $executionTime = round(($endTime - $startTime) * 1000, 2);

                    $scriptsExecuted++;
                    $scriptDetails[] = array(
                        'file' => $script['file'],
                        'version' => $script['version'],
                        'status' => $script['status'],
                        'execution_status' => 'success',
                        'execution_time_ms' => $executionTime,
                        'memory_usage' => $executionResult['memory_after'] - $executionResult['memory_before']
                    );

                    $executionResults[] = array(
                        'script_file' => $script['file'],
                        'output' => $executionResult['output'],
                        'result' => $executionResult['result'],
                        'execution_time_ms' => $executionResult['execution_time_ms'],
                        'memory_usage' => $executionResult['memory_after'] - $executionResult['memory_before']
                    );
                } catch (Exception $e) {
                    $scriptDetails[] = array(
                        'file' => $script['file'],
                        'version' => $script['version'],
                        'status' => $script['status'],
                        'execution_status' => 'failed',
                        'error' => $e->getMessage()
                    );

                    $executionResults[] = array(
                        'script_file' => $script['file'],
                        'error' => $e->getMessage()
                    );
                }
            }

            $this->logger->info('backend_update_step', 'PHP scripts replay completed', array(
                'scripts_executed' => $scriptsExecuted,
                'total_scripts' => count($phpScripts),
                'script_details' => $scriptDetails
            ));

            return array(
                'success' => true,
                'message' => 'PHP scripts replay completed',
                'step' => 'replay_php_scripts',
                'scripts_executed' => $scriptsExecuted,
                'total_scripts' => count($phpScripts),
                'script_details' => $scriptDetails,
                'execution_results' => $executionResults,
                'log_file' => $this->logger->getLogFile()
            );

        } catch (Exception $e) {
            $this->logger->error('backend_update_step', 'PHP scripts replay failed', array(
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ));

            return array(
                'success' => false,
                'message' => 'PHP scripts replay failed: ' . $e->getMessage(),
                'step' => 'replay_php_scripts',
                'error' => $e->getMessage(),
                'log_file' => $this->logger->getLogFile()
            );
        }
    }

    /**
     * Skip SQL scripts step
     *
     * @return array
     */
    public function skipSqlScripts()
    {
        $this->logger->info('backend_update_step', 'SQL scripts step skipped by user');

        return array(
            'success' => true,
            'message' => 'SQL scripts step skipped',
            'step' => 'skip_sql_scripts',
            'scripts_executed' => 0,
            'skipped' => true,
            'log_file' => $this->logger->getLogFile()
        );
    }

    /**
     * Skip PHP scripts step
     *
     * @return array
     */
    public function skipPhpScripts()
    {
        $this->logger->info('backend_update_step', 'PHP scripts step skipped by user');

        return array(
            'success' => true,
            'message' => 'PHP scripts step skipped',
            'step' => 'skip_php_scripts',
            'scripts_executed' => 0,
            'skipped' => true,
            'log_file' => $this->logger->getLogFile()
        );
    }

    /**
     * Replay individual script
     *
     * @param string $scriptFile
     * @param string $scriptType
     * @return array
     */
    public function replayIndividualScript($scriptFile, $scriptType)
    {
        $this->logger->info('individual_script_replay', 'Starting individual script replay', array(
            'script_file' => $scriptFile,
            'script_type' => $scriptType
        ));

        try {
            $currentVersion = $this->versionManager->getCurrentVersion();
            $allScripts = $this->scriptExecutor->getScripts($currentVersion, 'all');

            // Find the specific script
            $targetScript = null;
            foreach ($allScripts as $script) {
                if ($script['file'] === $scriptFile && $script['type'] === $scriptType) {
                    $targetScript = $script;
                    break;
                }
            }

            if (!$targetScript) {
                return array(
                    'success' => false,
                    'message' => 'Script not found: ' . $scriptFile,
                    'step' => 'replay_individual_script',
                    'log_file' => $this->logger->getLogFile()
                );
            }

            // Execute the individual script
            $startTime = microtime(true);
            $executionResult = null;

            if ($scriptType === 'sql') {
                $executionResult = $this->scriptExecutor->executeSqlScript($targetScript);
            } else {
                $executionResult = $this->scriptExecutor->executePhpScript($targetScript);
            }

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);

            $this->logger->info('individual_script_replay', 'Individual script executed successfully', array(
                'script_file' => $scriptFile,
                'script_type' => $scriptType,
                'execution_time_ms' => $executionTime
            ));

            $responseData = array(
                'success' => true,
                'message' => 'Script executed successfully',
                'step' => 'replay_individual_script',
                'script_file' => $scriptFile,
                'script_type' => $scriptType,
                'execution_time' => $executionTime . 'ms',
                'log_file' => $this->logger->getLogFile()
            );

            // Add detailed execution results based on script type
            if ($scriptType === 'sql' && isset($executionResult['detailed_results'])) {
                $responseData['detailed_results'] = $executionResult['detailed_results'];
                $responseData['statements_executed'] = $executionResult['statements_executed'];
            } elseif ($scriptType === 'php') {
                $responseData['output'] = $executionResult['output'];
                $responseData['result'] = $executionResult['result'];
                $responseData['memory_usage'] = $executionResult['memory_after'] - $executionResult['memory_before'];
            }

            return $responseData;

        } catch (Exception $e) {
            $this->logger->error('individual_script_replay', 'Individual script execution failed', array(
                'script_file' => $scriptFile,
                'script_type' => $scriptType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ));

            return array(
                'success' => false,
                'message' => 'Script execution failed: ' . $e->getMessage(),
                'step' => 'replay_individual_script',
                'script_file' => $scriptFile,
                'script_type' => $scriptType,
                'error' => $e->getMessage(),
                'log_file' => $this->logger->getLogFile()
            );
        }
    }

    /**
     * Get logger instance
     *
     * @return Maintenance_Utils_Logger
     */
    public function getLogger()
    {
        return $this->logger;
    }
}
