<?php
/**
 * Logs Model Class - Refactored
 *
 * Handles dual log sources: maintenance logs and system logs
 * Simplified interface focusing on primary use case
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Models_LogsModel extends Maintenance_Models_BaseModel
{
    private $maintenanceLogsPath;
    private $systemLogPath;

    /**
     * Constructor
     *
     * @param array $config
     */
    public function __construct($config = array())
    {
        parent::__construct();
        $this->maintenanceLogsPath = MAINTENANCE_BASE_PATH . '/logs';
        $this->systemLogPath = '/var/log/syslog';

        // Ensure maintenance logs directory exists
        $this->ensureMaintenanceLogsDirectory();
    }

    /**
     * Ensure maintenance logs directory exists and is writable
     *
     * @return void
     */
    private function ensureMaintenanceLogsDirectory()
    {
        if (!is_dir($this->maintenanceLogsPath)) {
            if (!mkdir($this->maintenanceLogsPath, 0755, true)) {
                error_log('Failed to create maintenance logs directory: ' . $this->maintenanceLogsPath);
            }
        }

        if (!is_writable($this->maintenanceLogsPath)) {
            error_log('Maintenance logs directory is not writable: ' . $this->maintenanceLogsPath);
        }
    }

    /**
     * Get maintenance logs - Simplified
     *
     * @return array
     */
    public function getMaintenanceLogs()
    {
        $logs = array();

        if (!is_dir($this->maintenanceLogsPath)) {
            return $logs;
        }

        // Get all JSON files
        $files = glob($this->maintenanceLogsPath . '/*.json');

        if (!$files) {
            return $logs;
        }

        // Process each file
        foreach ($files as $filePath) {
            $fileInfo = $this->getMaintenanceFileInfo($filePath);
            if ($fileInfo) {
                $logs[] = $fileInfo;
            }
        }

        // Sort by modification time (newest first)
        usort($logs, function($a, $b) {
            return $b['modified_time'] - $a['modified_time'];
        });

        return $logs;
    }

    /**
     * Get relevant system logs - Filtered for maintenance-related entries
     *
     * @return array
     */
    public function getRelevantSystemLogs()
    {
        $logs = array();

        if (!file_exists($this->systemLogPath) || !is_readable($this->systemLogPath)) {
            return $logs;
        }

        // Read last 1000 lines of syslog for performance
        $lines = $this->tailFile($this->systemLogPath, 1000);

        // Filter for maintenance-related entries
        $relevantKeywords = array('maintenance', 'riashop', 'update', 'svn', 'php', 'mysql');

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // Check if line contains relevant keywords
            $isRelevant = false;
            foreach ($relevantKeywords as $keyword) {
                if (stripos($line, $keyword) !== false) {
                    $isRelevant = true;
                    break;
                }
            }

            if ($isRelevant) {
                $logEntry = $this->parseSystemLogLine($line);
                if ($logEntry) {
                    $logs[] = $logEntry;
                }
            }
        }

        // Sort by timestamp (newest first)
        usort($logs, function($a, $b) {
            return $b['timestamp'] - $a['timestamp'];
        });

        return array_slice($logs, 0, 50); // Limit to 50 most recent entries
    }

    /**
     * Get maintenance file information
     *
     * @param string $filePath
     * @return array|null
     */
    private function getMaintenanceFileInfo($filePath)
    {
        if (!file_exists($filePath) || !is_readable($filePath)) {
            return null;
        }

        $filename = basename($filePath);
        $fileSize = filesize($filePath);
        $modifiedTime = filemtime($filePath);

        // Try to get basic info from JSON content
        $preview = '';
        $recordCount = 0;
        $isValidJson = false;

        $content = file_get_contents($filePath);
        if ($content !== false) {
            $jsonData = json_decode($content, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $isValidJson = true;

                // Count records if it's an array
                if (is_array($jsonData)) {
                    $recordCount = count($jsonData);
                }

                // Create preview
                $preview = $this->createMaintenancePreview($jsonData);
            }
        }

        return array(
            'filename' => $filename,
            'filepath' => $filePath,
            'size' => $fileSize,
            'size_formatted' => $this->formatFileSize($fileSize),
            'modified_time' => $modifiedTime,
            'modified_date' => date('Y-m-d H:i:s', $modifiedTime),
            'modified_relative' => $this->getRelativeTime($modifiedTime),
            'is_valid_json' => $isValidJson,
            'record_count' => $recordCount,
            'preview' => $preview,
            'source' => 'maintenance'
        );
    }

    /**
     * Read last N lines from a file efficiently
     *
     * @param string $filePath
     * @param int $lines
     * @return array
     */
    private function tailFile($filePath, $lines = 100)
    {
        $handle = fopen($filePath, 'r');
        if (!$handle) {
            return array();
        }

        $linecounter = $lines;
        $pos = -2;
        $beginning = false;
        $text = array();

        while ($linecounter > 0) {
            $t = " ";
            while ($t != "\n") {
                if (fseek($handle, $pos, SEEK_END) == -1) {
                    $beginning = true;
                    break;
                }
                $t = fgetc($handle);
                $pos--;
            }
            $linecounter--;
            if ($beginning) {
                rewind($handle);
            }
            $text[$lines - $linecounter - 1] = fgets($handle);
            if ($beginning) break;
        }
        fclose($handle);

        return array_reverse($text);
    }

    /**
     * Parse system log line
     *
     * @param string $line
     * @return array|null
     */
    private function parseSystemLogLine($line)
    {
        // Basic syslog format: timestamp hostname process[pid]: message
        if (preg_match('/^(\w+\s+\d+\s+\d+:\d+:\d+)\s+(\S+)\s+(.+)$/', $line, $matches)) {
            $timestamp = strtotime($matches[1]);
            if ($timestamp === false) {
                $timestamp = time(); // fallback to current time
            }

            return array(
                'timestamp' => $timestamp,
                'date' => date('Y-m-d H:i:s', $timestamp),
                'relative' => $this->getRelativeTime($timestamp),
                'hostname' => $matches[2],
                'message' => trim($matches[3]),
                'source' => 'system',
                'raw_line' => $line
            );
        }

        return null;
    }

    /**
     * Create preview text from maintenance JSON data
     *
     * @param mixed $jsonData
     * @return string
     */
    private function createMaintenancePreview($jsonData)
    {
        if (is_array($jsonData)) {
            if (empty($jsonData)) {
                return 'Fichier vide';
            }

            $first = reset($jsonData);
            if (is_array($first)) {
                // Look for common maintenance log fields
                if (isset($first['operation'])) {
                    return 'Log d\'opérations: ' . $first['operation'];
                } elseif (isset($first['action'])) {
                    return 'Log d\'actions: ' . $first['action'];
                } elseif (isset($first['timestamp'])) {
                    return 'Log horodaté (' . count($jsonData) . ' entrées)';
                }

                $keys = array_keys($first);
                return 'Données avec clés: ' . implode(', ', array_slice($keys, 0, 3)) .
                       (count($keys) > 3 ? '...' : '');
            }

            return 'Tableau avec ' . count($jsonData) . ' éléments';
        }

        if (is_object($jsonData)) {
            return 'Objet JSON';
        }

        return 'Valeur simple';
    }

    /**
     * Sort logs array
     *
     * @param array &$logs
     * @param string $sortBy
     * @param string $sortOrder
     * @return void
     */
    private function sortLogs(&$logs, $sortBy, $sortOrder)
    {
        usort($logs, function($a, $b) use ($sortBy, $sortOrder) {
            $valueA = $valueB = null;
            
            switch ($sortBy) {
                case 'name':
                    $valueA = $a['filename'];
                    $valueB = $b['filename'];
                    break;
                case 'size':
                    $valueA = $a['size'];
                    $valueB = $b['size'];
                    break;
                case 'date':
                default:
                    $valueA = $a['modified_time'];
                    $valueB = $b['modified_time'];
                    break;
            }
            
            if ($valueA == $valueB) {
                return 0;
            }
            
            $result = ($valueA < $valueB) ? -1 : 1;
            return ($sortOrder === 'desc') ? -$result : $result;
        });
    }

    /**
     * Get maintenance log file content
     *
     * @param string $filename
     * @return array|null
     */
    public function getMaintenanceLogContent($filename)
    {
        $filePath = $this->getMaintenanceLogFilePath($filename);

        if (!$filePath || !file_exists($filePath)) {
            return null;
        }

        $content = file_get_contents($filePath);
        if ($content === false) {
            return null;
        }

        $jsonData = json_decode($content, true);
        $isValidJson = (json_last_error() === JSON_ERROR_NONE);

        $fileInfo = $this->getMaintenanceFileInfo($filePath);

        return array(
            'filename' => $filename,
            'source' => 'maintenance',
            'file_info' => $fileInfo,
            'raw_content' => $content,
            'json_data' => $isValidJson ? $jsonData : null,
            'is_valid_json' => $isValidJson,
            'json_error' => $isValidJson ? null : json_last_error_msg()
        );
    }

    /**
     * Get system log content (filtered excerpt)
     *
     * @param string $identifier
     * @return array|null
     */
    public function getSystemLogContent($identifier)
    {
        // For system logs, we'll show recent relevant entries
        // $identifier could be a date or keyword filter

        if (!file_exists($this->systemLogPath) || !is_readable($this->systemLogPath)) {
            return null;
        }

        $lines = $this->tailFile($this->systemLogPath, 500);
        $relevantLines = array();

        // Filter lines based on identifier or show all relevant maintenance entries
        $relevantKeywords = array('maintenance', 'riashop', 'update', 'svn');

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            $isRelevant = false;
            foreach ($relevantKeywords as $keyword) {
                if (stripos($line, $keyword) !== false) {
                    $isRelevant = true;
                    break;
                }
            }

            if ($isRelevant) {
                $relevantLines[] = $line;
            }
        }

        return array(
            'filename' => 'syslog_excerpt',
            'source' => 'system',
            'raw_content' => implode("\n", $relevantLines),
            'line_count' => count($relevantLines),
            'is_valid_json' => false,
            'json_error' => 'System log is not JSON format'
        );
    }

    /**
     * Get safe maintenance log file path
     *
     * @param string $filename
     * @return string|null
     */
    public function getMaintenanceLogFilePath($filename)
    {
        // Sanitize filename to prevent directory traversal
        $filename = basename($filename);

        // Only allow JSON files
        if (!preg_match('/\.json$/i', $filename)) {
            return null;
        }

        $filePath = $this->maintenanceLogsPath . '/' . $filename;

        // Ensure the file is within the logs directory
        $realLogsPath = realpath($this->maintenanceLogsPath);
        $realFilePath = realpath($filePath);

        if ($realFilePath && strpos($realFilePath, $realLogsPath) === 0) {
            return $filePath;
        }

        return null;
    }

    /**
     * Get system log file path (always returns syslog path)
     *
     * @param string $identifier
     * @return string|null
     */
    public function getSystemLogFilePath($identifier)
    {
        // For system logs, we always return the syslog path
        // $identifier is ignored for now but could be used for log rotation files
        return $this->systemLogPath;
    }

    /**
     * Delete a maintenance log file
     *
     * @param string $filename
     * @return bool
     */
    public function deleteMaintenanceLogFile($filename)
    {
        $filePath = $this->getMaintenanceLogFilePath($filename);

        if (!$filePath || !file_exists($filePath)) {
            return false;
        }

        return unlink($filePath);
    }



    /**
     * Format file size in human readable format
     *
     * @param int $bytes
     * @return string
     */
    private function formatFileSize($bytes)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get relative time string
     *
     * @param int $timestamp
     * @return string
     */
    private function getRelativeTime($timestamp)
    {
        $diff = time() - $timestamp;
        
        if ($diff < 60) {
            return 'Il y a ' . $diff . ' seconde' . ($diff > 1 ? 's' : '');
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return 'Il y a ' . $minutes . ' minute' . ($minutes > 1 ? 's' : '');
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return 'Il y a ' . $hours . ' heure' . ($hours > 1 ? 's' : '');
        } else {
            $days = floor($diff / 86400);
            return 'Il y a ' . $days . ' jour' . ($days > 1 ? 's' : '');
        }
    }
}
