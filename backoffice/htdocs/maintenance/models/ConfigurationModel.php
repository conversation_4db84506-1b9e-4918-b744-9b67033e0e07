<?php
/**
 * Configuration Model
 *
 * Handles configuration variables data operations
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Models_ConfigurationModel
{

    /**
     * Get tenant information
     *
     * @param int $tenantId
     * @return array|false
     */
    public function getTenant($tenantId)
    {
        if (!function_exists('tnt_tenants_exists') || !function_exists('tnt_tenants_get')) {
            return false;
        }

        if (!tnt_tenants_exists($tenantId)) {
            return false;
        }

        $result = tnt_tenants_get($tenantId);
        if (!$result) {
            return false;
        }

        return ria_mysql_fetch_assoc($result);
    }

    /**
     * Get websites for a tenant
     *
     * @param int $tenantId
     * @return array
     */
    public function getWebsites($tenantId)
    {
        if (!function_exists('wst_websites_get')) {
            return array();
        }

        $websites = array();
        $result = wst_websites_get(0, array('name' => 'asc'), $tenantId);
        
        if ($result) {
            while ($website = mysql_fetch_assoc($result)) {
                $websites[] = $website;
            }
        }

        return $websites;
    }

    /**
     * Get website information
     *
     * @param int $websiteId
     * @return array|false
     */
    public function getWebsite($websiteId)
    {
        if (!function_exists('wst_websites_exists') || !function_exists('wst_websites_get')) {
            return false;
        }

        if (!wst_websites_exists($websiteId)) {
            return false;
        }

        $result = wst_websites_get($websiteId);
        if (!$result) {
            return false;
        }

        return ria_mysql_fetch_assoc($result);
    }

    /**
     * Get sync information for tenant
     *
     * @param int $tenantId
     * @return array
     */
    public function getSyncInfo($tenantId)
    {
        $syncInfo = array(
            'enabled' => false,
            'sync_id' => 0,
            'sync_code' => '',
            'has_yuto' => false
        );

        // Get tenant info to check sync status
        $tenant = $this->getTenant($tenantId);
        if ($tenant && isset($tenant['sync_update']) && $tenant['sync_update']) {
            $syncInfo['enabled'] = true;

            // Get sync type
            if (function_exists('cfg_overrides_get_value')) {
                $syncId = cfg_overrides_get_value('sync_global_gescom_type', null, 0, $tenantId);
                if (is_numeric($syncId) && $syncId) {
                    $syncInfo['sync_id'] = $syncId;
                    
                    // Get sync code
                    if (function_exists('ria_gescom_get_code')) {
                        $syncInfo['sync_code'] = strtolower(ria_gescom_get_code($syncId));
                    }
                }
            }
        }

        // Check if tenant has Yuto app
        if (function_exists('wst_websites_is_fdv_app')) {
            $syncInfo['has_yuto'] = wst_websites_is_fdv_app($tenantId);
        }

        return $syncInfo;
    }

    /**
     * Get configuration variables for tenant/website
     *
     * @param int $tenantId
     * @param string|int $websiteId
     * @param bool $onlyOverride
     * @param string $filter
     * @param array $syncInfo
     * @return array
     */
    public function getConfigurationVariables($tenantId, $websiteId, $onlyOverride = false, $filter = '', $syncInfo = array())
    {
        if (!function_exists('cfg_variables_get') || !function_exists('cfg_overrides_get')) {
            return array();
        }

        $variables = array();

        // Get all configuration variables
        $result = cfg_variables_get('', array(), false, null);
        if (!$result) {
            return array();
        }

        while ($variable = ria_mysql_fetch_assoc($result)) {
            // Apply filter if specified
            if ($filter && strpos($variable['code'], $filter) === false) {
                continue;
            }

            // Skip admin variables
            if (substr($variable['code'], 0, 6) === 'admin_') {
                continue;
            }

            // Handle sync variables
            if ($websiteId !== 'sync') {
                if (preg_match('/^(sync_)/', $variable['code'])) {
                    continue;
                }
            } else {
                $syncCode = isset($syncInfo['sync_code']) ? $syncInfo['sync_code'] : '';
                if (!preg_match('/^(sync_global_|sync_' . $syncCode . '_)/', $variable['code'])) {
                    continue;
                }
            }

            // Handle Yuto variables
            if ($websiteId !== 'yuto') {
                if (preg_match('/^(device_|fdv_)/', $variable['code'])) {
                    continue;
                }
            } else {
                if (!preg_match('/^(device_|fdv_)/', $variable['code'])) {
                    continue;
                }
            }

            // Add variable with default value
            $variables[$variable['code']] = array_merge($variable, array(
                'override' => $variable['default'],
                'childs' => array()
            ));
        }

        // Get overrides for this tenant/website
        $websiteOverrideId = $this->getWebsiteOverrideId($tenantId, $websiteId);
        $overrideResult = cfg_overrides_get($websiteOverrideId, array(), '', 0, $tenantId);
        
        if ($overrideResult) {
            while ($override = ria_mysql_fetch_assoc($overrideResult)) {
                if (array_key_exists($override['code'], $variables)) {
                    $variables[$override['code']]['override'] = $override['value'];
                }
            }
        }

        // Filter only overridden variables if requested
        if ($onlyOverride) {
            foreach ($variables as $key => $data) {
                if (trim($data['override']) === '' || $data['override'] === $data['default']) {
                    unset($variables[$key]);
                }
            }
        }

        // Organize parent-child relationships
        $this->organizeVariableHierarchy($variables);

        return $variables;
    }

    /**
     * Get website ID for overrides
     *
     * @param int $tenantId
     * @param string|int $websiteId
     * @return int
     */
    private function getWebsiteOverrideId($tenantId, $websiteId)
    {
        if (is_numeric($websiteId)) {
            return $websiteId;
        }

        if ($websiteId === 'yuto') {
            // Get Yuto website ID
            if (function_exists('wst_websites_get')) {
                $result = wst_websites_get(0, false, $tenantId, false, _WST_TYPE_FDV);
                if ($result && ria_mysql_num_rows($result)) {
                    $yutoWebsite = ria_mysql_fetch_assoc($result);
                    return $yutoWebsite['id'];
                }
            }
        }

        return 0; // For sync or other special cases
    }

    /**
     * Organize variables into parent-child hierarchy
     *
     * @param array &$variables
     * @return void
     */
    private function organizeVariableHierarchy(&$variables)
    {
        foreach ($variables as $key => $data) {
            if (!isset($data['parent_code']) || !array_key_exists($data['parent_code'], $variables)) {
                continue;
            }

            if (trim($data['parent_code']) !== '') {
                $variables[$data['parent_code']]['childs'][] = $data;
                unset($variables[$key]);
            }
        }
    }

    /**
     * Save configuration variables
     *
     * @param int $tenantId
     * @param string|int $websiteId
     * @param array $overrides
     * @return array
     */
    public function saveConfigurationVariables($tenantId, $websiteId, $overrides)
    {
        if (!function_exists('cfg_variables_get') || !function_exists('cfg_overrides_set_value')) {
            return array('success' => false, 'error' => 'Required functions not available');
        }

        try {
            $websiteOverrideId = $this->getWebsiteOverrideId($tenantId, $websiteId);

            // Get all variables to validate codes
            $validVariables = array();
            $result = cfg_variables_get('', array(), false, null);
            if ($result) {
                while ($variable = ria_mysql_fetch_assoc($result)) {
                    $validVariables[$variable['code']] = $variable;
                }
            }

            // Process each override
            foreach ($overrides as $code => $value) {
                if (!array_key_exists($code, $validVariables)) {
                    continue; // Skip invalid variable codes
                }

                $variable = $validVariables[$code];

                // Handle array values (for multi-select fields)
                if (is_array($value)) {
                    $processedValue = '';
                    foreach ($value as $val) {
                        if (trim($val) !== '') {
                            $processedValue .= ($processedValue !== '' ? ', ' : '') . $val;
                        }
                    }
                    $value = $processedValue;
                }

                // Skip if value matches default and no override exists
                if (!function_exists('cfg_overrides_exists')) {
                    continue;
                }

                if (!cfg_overrides_exists($code, $tenantId, $websiteOverrideId) && 
                    trim($value) === trim($variable['default'])) {
                    continue;
                }

                // Save the override
                if (!cfg_overrides_set_value($code, $value, $websiteOverrideId, 0, $tenantId)) {
                    return array(
                        'success' => false, 
                        'error' => 'Erreur lors de l\'enregistrement de la variable "' . htmlspecialchars($code) . '"'
                    );
                }
            }

            return array('success' => true);

        } catch (Exception $e) {
            return array('success' => false, 'error' => $e->getMessage());
        }
    }

    /**
     * Clear configuration cache
     *
     * @param int $tenantId
     * @param string|int $websiteId
     * @return void
     */
    public function clearConfigCache($tenantId, $websiteId)
    {
        // Clear memcached if available
        global $memcached;
        if (isset($memcached) && is_object($memcached)) {
            $cacheKey = 'config:' . $tenantId . ':' . $websiteId;
            $cached = $memcached->get($cacheKey);
            if ($memcached->getResultCode() !== Memcached::RES_NOTFOUND) {
                $memcached->delete($cacheKey);
            }
        }
    }
}
