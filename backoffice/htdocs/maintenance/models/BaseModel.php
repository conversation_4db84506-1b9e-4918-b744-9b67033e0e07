<?php
/**
 * Base Model Class
 *
 * Provides common functionality for all models
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

abstract class Maintenance_Models_BaseModel
{
    protected $db;
    protected $table = '';
    protected $primaryKey = 'id';
    protected $fillable = array();
    protected $hidden = array();

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->db = Maintenance_Utils_Database::getInstance();
    }

    /**
     * Find record by ID
     *
     * @param int $id
     * @return array|null
     */
    public function find($id)
    {
        $result = $this->db->select($this->table, array($this->primaryKey => $id));
        return $this->db->fetchAssoc($result);
    }

    /**
     * Find all records
     *
     * @param array $conditions
     * @param array $orderBy
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function findAll($conditions = array(), $orderBy = array(), $limit = 0, $offset = 0)
    {
        $result = $this->db->select($this->table, $conditions, $orderBy, $limit, $offset);
        return $this->db->fetchAllAssoc($result);
    }

    /**
     * Find first record matching conditions
     *
     * @param array $conditions
     * @param array $orderBy
     * @return array|null
     */
    public function findFirst($conditions = array(), $orderBy = array())
    {
        $result = $this->db->select($this->table, $conditions, $orderBy, 1);
        return $this->db->fetchAssoc($result);
    }

    /**
     * Count records
     *
     * @param array $conditions
     * @return int
     */
    public function count($conditions = array())
    {
        return $this->db->count($this->table, $conditions);
    }

    /**
     * Check if record exists
     *
     * @param array $conditions
     * @return bool
     */
    public function exists($conditions)
    {
        return $this->count($conditions) > 0;
    }

    /**
     * Create new record
     *
     * @param array $data
     * @return int|false
     */
    public function create($data)
    {
        $data = $this->filterFillable($data);

        if (empty($data)) {
            return false;
        }

        $fields = array_keys($data);
        $values = array_values($data);

        $fieldsList = '`' . implode('`, `', $fields) . '`';
        $escapedValues = array();
        foreach ($values as $value) {
            $escapedValues[] = $this->db->escape($value);
        }
        $valuesList = "'" . implode("', '", $escapedValues) . "'";

        $query = "INSERT INTO `{$this->table}` ({$fieldsList}) VALUES ({$valuesList})";

        $result = $this->db->query($query);

        return $result ? $this->db->lastInsertId() : false;
    }

    /**
     * Update record
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update($id, $data)
    {
        $data = $this->filterFillable($data);

        if (empty($data)) {
            return false;
        }

        $setPairs = array();
        foreach ($data as $field => $value) {
            $escapedValue = $this->db->escape($value);
            $setPairs[] = "`{$field}` = '{$escapedValue}'";
        }

        $setClause = implode(', ', $setPairs);
        $query = "UPDATE `{$this->table}` SET {$setClause} WHERE `{$this->primaryKey}` = {$id}";

        $result = $this->db->query($query);

        return $result !== false;
    }

    /**
     * Delete record
     *
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        $query = "DELETE FROM `{$this->table}` WHERE `{$this->primaryKey}` = {$id}";
        $result = $this->db->query($query);

        return $result !== false && $this->db->affectedRows() > 0;
    }

    /**
     * Soft delete record (if table has deleted_at column)
     *
     * @param int $id
     * @return bool
     */
    public function softDelete($id)
    {
        $columns = $this->db->getTableColumns($this->table);
        $hasDeletedAt = false;

        foreach ($columns as $column) {
            if ($column['Field'] === 'deleted_at') {
                $hasDeletedAt = true;
                break;
            }
        }

        if (!$hasDeletedAt) {
            return $this->delete($id);
        }

        return $this->update($id, array('deleted_at' => date('Y-m-d H:i:s')));
    }

    /**
     * Filter data to only include fillable fields
     *
     * @param array $data
     * @return array
     */
    protected function filterFillable($data)
    {
        if (empty($this->fillable)) {
            return $data;
        }

        return array_intersect_key($data, array_flip($this->fillable));
    }

    /**
     * Hide sensitive fields from output
     *
     * @param array $data
     * @return array
     */
    protected function hideFields($data)
    {
        if (empty($this->hidden)) {
            return $data;
        }

        return array_diff_key($data, array_flip($this->hidden));
    }

    /**
     * Get paginated results
     *
     * @param int $page
     * @param int $pageSize
     * @param array $conditions
     * @param array $orderBy
     * @return array
     */
    public function paginate($page = 1, $pageSize = null, $conditions = array(), $orderBy = array())
    {
        if ($pageSize === null) {
            $pageSize = DEFAULT_PAGE_SIZE;
        }

        $page = max(1, $page);
        $pageSize = min(max(1, $pageSize), MAX_PAGE_SIZE);
        $offset = ($page - 1) * $pageSize;

        $total = $this->count($conditions);
        $data = $this->findAll($conditions, $orderBy, $pageSize, $offset);

        return array(
            'data' => $data,
            'pagination' => array(
                'current_page' => $page,
                'page_size' => $pageSize,
                'total_items' => $total,
                'total_pages' => ceil($total / $pageSize),
                'has_next' => $page < ceil($total / $pageSize),
                'has_previous' => $page > 1
            )
        );
    }

    /**
     * Execute raw query
     *
     * @param string $query
     * @param array $params
     * @return mixed
     */
    protected function query($query, $params = array())
    {
        return $this->db->query($query, $params);
    }

    /**
     * Get table name
     *
     * @return string
     */
    public function getTable()
    {
        return $this->table;
    }

    /**
     * Get primary key
     *
     * @return string
     */
    public function getPrimaryKey()
    {
        return $this->primaryKey;
    }

    /**
     * Get fillable fields
     *
     * @return array
     */
    public function getFillable()
    {
        return $this->fillable;
    }

    /**
     * Validate data before save
     *
     * @param array $data
     * @return array Array of validation errors
     */
    protected function validate($data)
    {
        // Override in child classes for specific validation
        return array();
    }

    /**
     * Execute callback within transaction
     *
     * @param callable $callback
     * @return mixed
     * @throws Exception
     */
    protected function transaction($callback)
    {
        return $this->db->transaction($callback);
    }
}
