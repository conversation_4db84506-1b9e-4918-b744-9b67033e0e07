<?php
/**
 * Sync Model Class
 *
 * Handles synchronization data and operations
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Models_SyncModel extends Maintenance_Models_BaseModel
{
    protected $table = 'tnt_tenants'; // Main table for tenant info
    protected $primaryKey = 'id';

    private $config;
    private $tenantId;
    private $token;

    /**
     * Constructor
     *
     * @param array $config
     */
    public function __construct($config = array())
    {
        parent::__construct();
        $this->config = $config;
        $this->tenantId = isset($config['tnt_id']) ? $config['tnt_id'] : 0;
    }

    /**
     * Get tenant information
     *
     * @param int|null $tenantId
     * @return array|null
     */
    public function getTenantInfo($tenantId = null)
    {
        $id = $tenantId !== null ? $tenantId : $this->tenantId;

        if (!$id) {
            return null;
        }

        // Use existing function if available
        if (function_exists('tnt_tenants_get')) {
            $result = tnt_tenants_get($id);
            return $result ? mysql_fetch_assoc($result) : null;
        }

        return $this->find($id);
    }

    /**
     * Get waiting orders count
     *
     * @return int
     */
    public function getWaitingOrdersCount()
    {
        try {
            // Use existing function if available
            if (function_exists('ord_orders_get_new_to_import')) {
                $specialParam = ($this->tenantId === 3) ? 7 : false;
                $result = ord_orders_get_new_to_import($specialParam);
                return $result ? mysql_num_rows($result) : 0;
            }

            // Fallback query
            $query = "SELECT COUNT(*) as count FROM orders WHERE tenant_id = :tenant_id AND sync_status = 'pending'";
            $result = $this->query($query, array('tenant_id' => $this->tenantId));
            $row = $this->db->fetchAssoc($result);

            return (int) (isset($row['count']) ? $row['count'] : 0);
        } catch (Exception $e) {
            error_log('Error getting waiting orders count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get waiting quotes count
     *
     * @return int
     */
    public function getWaitingQuotesCount()
    {
        try {
            // Use existing function if available
            if (function_exists('ord_orders_get_need_sync')) {
                $result = ord_orders_get_need_sync('head', _STATE_DEVIS);
                return is_array($result) ? count($result) : 0;
            }

            // Fallback query
            $query = "SELECT COUNT(*) as count FROM orders WHERE tenant_id = :tenant_id AND state = :state AND sync_status = 'pending'";
            $result = $this->query($query, array(
                'tenant_id' => $this->tenantId,
                'state' => _STATE_DEVIS
            ));
            $row = $this->db->fetchAssoc($result);

            return (int) (isset($row['count']) ? $row['count'] : 0);
        } catch (Exception $e) {
            error_log('Error getting waiting quotes count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get waiting users count
     *
     * @return int
     */
    public function getWaitingUsersCount()
    {
        try {
            // Determine parameters based on tenant ID
            $params = false;
            switch ($this->tenantId) {
                case 16:
                    $params = 1916;
                    break;
                case 13:
                    $params = array(2819 => 'Oui');
                    break;
            }

            // Use existing function if available
            if (function_exists('gu_users_toimport_get')) {
                $result = gu_users_toimport_get($params);
                return $result ? mysql_num_rows($result) : 0;
            }

            // Fallback query
            $query = "SELECT COUNT(*) as count FROM users WHERE tenant_id = :tenant_id AND sync_status = 'pending'";
            $result = $this->query($query, array('tenant_id' => $this->tenantId));
            $row = $this->db->fetchAssoc($result);

            return (int) (isset($row['count']) ? $row['count'] : 0);
        } catch (Exception $e) {
            error_log('Error getting waiting users count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get waiting addresses count
     *
     * @return int
     */
    public function getWaitingAddressesCount()
    {
        try {
            // Use existing function if available
            if (function_exists('gu_adresses_get_updated')) {
                $result = gu_adresses_get_updated();
                return $result ? mysql_num_rows($result) : 0;
            }

            // Fallback query
            $query = "SELECT COUNT(*) as count FROM addresses WHERE tenant_id = :tenant_id AND sync_status = 'pending'";
            $result = $this->query($query, array('tenant_id' => $this->tenantId));
            $row = $this->db->fetchAssoc($result);

            return (int) (isset($row['count']) ? $row['count'] : 0);
        } catch (Exception $e) {
            error_log('Error getting waiting addresses count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get GESCOM type name
     *
     * @return string
     */
    public function getGescomTypeName()
    {
        $gescomType = isset($this->config['sync_global_gescom_type']) ? $this->config['sync_global_gescom_type'] : 0;

        $types = array(
            GESCOM_TYPE_SAGE => 'Sage ' . (isset($this->config['sync_sage_version']) ? $this->config['sync_sage_version'] : '') . ' ' .
                               ((isset($this->config['sync_sage_om_enable']) && $this->config['sync_sage_om_enable']) ? 'OM' : 'ODBC'),
            GESCOM_TYPE_ISIALIS => 'Isialis',
            GESCOM_TYPE_LIMS => 'Lims',
            GESCOM_TYPE_HARMONYS => 'Harmonys',
            GESCOM_TYPE_PLATON => 'Platon',
            GESCOM_TYPE_APINEGOCE => 'APINEGOCE',
            GESCOM_TYPE_G5 => 'G5',
            GESCOM_TYPE_DIVALTO => 'Divalto',
            GESCOM_TYPE_CLISSON => 'Clisson',
            GESCOM_TYPE_EEE => 'EEE',
            GESCOM_TYPE_DYNAMICS => 'DynamicsAx',
            GESCOM_TYPE_SOFI => 'Sofi',
            GESCOM_TYPE_WAVESOFT => 'Wavesoft',
            GESCOM_TYPE_CEGID => 'Cegid',
            GESCOM_TYPE_SAGE_X3 => 'Sage X3',
            GESCOM_TYPE_DYNAMICS_NAVISION => 'Dynamics Ax',
            GESCOM_TYPE_SINERES => 'Sineres',
            GESCOM_TYPE_DIVALTO_SQL => 'Divalto Sql',
            GESCOM_TYPE_BATIGEST => 'BATIGEST',
            GESCOM_TYPE_STAR6000 => 'Star6000',
            GESCOM_TYPE_EBP => 'EBP',
            GESCOM_TYPE_SAGE_50C => 'Sage 50c'
        );

        return isset($types[$gescomType]) ? $types[$gescomType] : 'Inconnue';
    }

    /**
     * Get sync API URLs
     *
     * @return array
     */
    public function getSyncApiUrls()
    {
        $baseUrl = SYNC_API_URL;

        return array(
            'orders' => $baseUrl . '?logtoken=' . $this->token . '&module=orders&action=get-new',
            'quotes' => $baseUrl . '?logtoken=' . $this->token . '&module=orders&action=get-updated&detect_type=head&states=28',
            'users' => $baseUrl . '?logtoken=' . $this->token . '&module=users&action=get-new',
            'addresses' => $baseUrl . '?logtoken=' . $this->token . '&module=addresses&action=get-updated'
        );
    }

    /**
     * Get complete sync information
     *
     * @return array
     */
    public function getSyncInfo()
    {
        $tenant = $this->getTenantInfo();
        $this->token = $tenant['token'];

        return array(
            'tenant_id' => $this->tenantId,
            'token' => $this->token,
            'gescom_type' => $this->getGescomTypeName(),
            'sync_reboot' => isset($tenant['sync_reboot']) ? $tenant['sync_reboot'] : 0,
            'last_sync' => isset($tenant['last-sync']) ? $tenant['last-sync'] : null,
            'waiting_counts' => array(
                'orders' => $this->getWaitingOrdersCount(),
                'quotes' => $this->getWaitingQuotesCount(),
                'users' => $this->getWaitingUsersCount(),
                'addresses' => $this->getWaitingAddressesCount()
            ),
            'api_urls' => $this->getSyncApiUrls()
        );
    }

    /**
     * Update sync status
     *
     * @param array $data
     * @return bool
     */
    public function updateSyncStatus($data)
    {
        try {
            $allowedFields = array('sync_reboot', 'last-sync');
            $updateData = array_intersect_key($data, array_flip($allowedFields));

            if (empty($updateData)) {
                return false;
            }

            return $this->update($this->tenantId, $updateData);
        } catch (Exception $e) {
            error_log('Error updating sync status: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get BigShip specific information
     *
     * @return string|null
     */
    public function getBigShipInfo()
    {
        if ($this->tenantId !== 1) {
            return null;
        }

        try {
            $token = isset($this->config['token']) ? $this->config['token'] : '';
            $url = SYNC_API_URL;

            // Get product update requests
            $productRequests = 0;
            if (function_exists('fld_update_requests_get')) {
                $result = fld_update_requests_get(false, false, false, false, false, true);
                $productRequests = $result ? mysql_num_rows($result) : 0;
            }

            // Get order sync requests
            $orderRequests = 0;
            if (function_exists('ord_orders_get_need_sync')) {
                $result = ord_orders_get_need_sync();
                $orderRequests = $result ? count($result) : 0;
            }

            return sprintf(
                'Commandes fournisseurs : <a href="%s?logtoken=%s&module=orders&action=get-updated" target="_blank">%d</a> - Produits modifiés : <a href="%s?logtoken=%s&module=update-requests&action=get" target="_blank">%d</a>',
                $url, $token, $orderRequests,
                $url, $token, $productRequests
            );
        } catch (Exception $e) {
            error_log('Error getting BigShip info: ' . $e->getMessage());
            return null;
        }
    }
}
