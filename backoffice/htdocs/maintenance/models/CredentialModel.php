<?php
/**
 * Credential Model Class
 *
 * Handles SVN credential management operations
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Models_CredentialModel extends Maintenance_Models_BaseModel
{
    private $logger;
    private $credentialManager;

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        
        $this->logger = new Maintenance_Utils_Logger();
        $this->credentialManager = new Maintenance_Utils_CredentialManager($this->logger);
    }

    /**
     * Save SVN credentials
     *
     * @param array $data
     * @return array
     */
    public function saveCredentials($data)
    {
        try {
            // Validate input data
            $validation = $this->validateCredentialData($data);
            if (!$validation['valid']) {
                return array(
                    'success' => false,
                    'message' => 'Validation failed: ' . implode(', ', $validation['errors'])
                );
            }

            $repository = $data['repository'];
            $username = $data['username'];
            $password = $data['password'];
            $url = isset($data['url']) ? $data['url'] : '';

            // Store credentials
            $result = $this->credentialManager->storeCredentials($repository, $username, $password, $url);

            if ($result) {
                $this->logger->info('credential_model', 'Credentials saved successfully', array(
                    'repository' => $repository
                ));

                return array(
                    'success' => true,
                    'message' => 'Credentials saved successfully'
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Failed to save credentials'
                );
            }
        } catch (Exception $e) {
            $this->logger->error('credential_model', 'Exception saving credentials', array(
                'error' => $e->getMessage()
            ));

            return array(
                'success' => false,
                'message' => 'Error saving credentials: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get SVN credentials (without password for security)
     *
     * @param string $repository
     * @return array
     */
    public function getCredentials($repository)
    {
        try {
            $credentials = $this->credentialManager->getCredentials($repository);

            if ($credentials) {
                // Return credentials without password for security
                return array(
                    'success' => true,
                    'data' => array(
                        'username' => $credentials['username'],
                        'url' => $credentials['url'],
                        'created' => $credentials['created'],
                        'updated' => $credentials['updated'],
                        'has_password' => !empty($credentials['password'])
                    )
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'No credentials found for ' . $repository
                );
            }
        } catch (Exception $e) {
            $this->logger->error('credential_model', 'Exception retrieving credentials', array(
                'repository' => $repository,
                'error' => $e->getMessage()
            ));

            return array(
                'success' => false,
                'message' => 'Error retrieving credentials: ' . $e->getMessage()
            );
        }
    }

    /**
     * Test SVN connectivity
     *
     * @param string $repository
     * @param string $repoPath
     * @return array
     */
    public function testConnectivity($repository, $repoPath)
    {
        try {
            $result = $this->credentialManager->testConnectivity($repository, $repoPath);

            $this->logger->info('credential_model', 'Connectivity test performed', array(
                'repository' => $repository,
                'success' => $result['success']
            ));

            return $result;
        } catch (Exception $e) {
            $this->logger->error('credential_model', 'Exception testing connectivity', array(
                'repository' => $repository,
                'error' => $e->getMessage()
            ));

            return array(
                'success' => false,
                'message' => 'Error testing connectivity: ' . $e->getMessage()
            );
        }
    }

    /**
     * Delete SVN credentials
     *
     * @param string $repository
     * @return array
     */
    public function deleteCredentials($repository)
    {
        try {
            $result = $this->credentialManager->deleteCredentials($repository);

            if ($result) {
                $this->logger->info('credential_model', 'Credentials deleted successfully', array(
                    'repository' => $repository
                ));

                return array(
                    'success' => true,
                    'message' => 'Credentials deleted successfully'
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Failed to delete credentials'
                );
            }
        } catch (Exception $e) {
            $this->logger->error('credential_model', 'Exception deleting credentials', array(
                'repository' => $repository,
                'error' => $e->getMessage()
            ));

            return array(
                'success' => false,
                'message' => 'Error deleting credentials: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get credentials status for all repositories
     *
     * @return array
     */
    public function getCredentialsStatus()
    {
        $repositories = array('engine', 'frontend');
        $status = array();

        foreach ($repositories as $repo) {
            $credentials = $this->credentialManager->getCredentials($repo);
            
            $status[$repo] = array(
                'configured' => $credentials !== false,
                'username' => $credentials ? $credentials['username'] : '',
                'url' => $credentials ? $credentials['url'] : '',
                'updated' => $credentials ? $credentials['updated'] : null
            );
        }

        return array(
            'success' => true,
            'data' => $status
        );
    }

    /**
     * Validate credential data
     *
     * @param array $data
     * @return array
     */
    private function validateCredentialData($data)
    {
        $errors = array();

        // Check required fields
        if (empty($data['repository'])) {
            $errors[] = 'Repository is required';
        } elseif (!in_array($data['repository'], array('engine', 'frontend'))) {
            $errors[] = 'Invalid repository type';
        }

        if (empty($data['username'])) {
            $errors[] = 'Username is required';
        }

        if (empty($data['password'])) {
            $errors[] = 'Password is required';
        }

        // Validate URL format if provided
        if (!empty($data['url'])) {
            if (!filter_var($data['url'], FILTER_VALIDATE_URL)) {
                $errors[] = 'Invalid URL format';
            }
            
            // Check if URL looks like an SVN URL
            if (!preg_match('/^https?:\/\/.*\/svn\//', $data['url'])) {
                $errors[] = 'URL should be a valid SVN repository URL';
            }
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors
        );
    }

    /**
     * Get repository paths and URLs
     *
     * @return array
     */
    public function getRepositoryInfo()
    {
        $enginePath = defined('ENGINE_SVN_REPO_PATH') ? ENGINE_SVN_REPO_PATH : '/var/www/engine';
        $frontendPath = defined('FRONTEND_SVN_REPO_PATH') ? FRONTEND_SVN_REPO_PATH : '/var/www/frontend';

        $info = array(
            'engine' => $this->getRepositoryDetails($enginePath, 'engine'),
            'frontend' => $this->getRepositoryDetails($frontendPath, 'frontend')
        );

        return array(
            'success' => true,
            'data' => $info
        );
    }

    /**
     * Get detailed repository information
     *
     * @param string $path
     * @param string $repository
     * @return array
     */
    private function getRepositoryDetails($path, $repository)
    {
        $details = array(
            'path' => $path,
            'exists' => is_dir($path),
            'is_svn' => false,
            'url' => '',
            'revision' => '',
            'last_changed' => '',
            'status' => 'Unknown'
        );

        if ($details['exists']) {
            $svnDir = $path . '/.svn';
            $details['is_svn'] = is_dir($svnDir);

            if ($details['is_svn']) {
                // Get SVN info
                $command = "svn info " . escapeshellarg($path) . " 2>/dev/null";
                $output = shell_exec($command);

                if ($output) {
                    // Parse SVN info
                    if (preg_match('/^URL: (.+)$/m', $output, $matches)) {
                        $details['url'] = trim($matches[1]);
                    }
                    
                    if (preg_match('/^Revision: (\d+)$/m', $output, $matches)) {
                        $details['revision'] = trim($matches[1]);
                    }
                    
                    if (preg_match('/^Last Changed Date: (.+)$/m', $output, $matches)) {
                        $details['last_changed'] = trim($matches[1]);
                    }

                    $details['status'] = 'OK';
                } else {
                    $details['status'] = 'SVN Error';
                }
            } else {
                $details['status'] = 'Not a SVN repository';
            }
        } else {
            $details['status'] = 'Path does not exist';
        }

        return $details;
    }

    /**
     * Get credential manager instance
     *
     * @return Maintenance_Utils_CredentialManager
     */
    public function getCredentialManager()
    {
        return $this->credentialManager;
    }
}
