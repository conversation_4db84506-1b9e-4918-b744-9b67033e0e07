<?php
/**
 * Task Model Class
 * 
 * Handles task management operations
 * 
 * @package Maintenance\Models
 * <AUTHOR> Team
 */

class Maintenance_Models_TaskModel extends Maintenance_Models_BaseModel
{
    protected $table = 'tasks';
    protected $primaryKey = 'id';
    
    private $tenantId;

    /**
     * Constructor
     * 
     * @param int $tenantId
     */
    public function __construct($tenantId = 0)
    {
        parent::__construct();
        $this->tenantId = $tenantId;
    }

    /**
     * Get task activation status
     * 
     * @param int $taskId
     * @param int|null $tenantId
     * @return array|null
     */
    public function getTaskActivation($taskId, $tenantId = null)
    {
        $tntId = $tenantId ? $tenantId : $this->tenantId;
        
        try {
            // Use existing function if available
            if (function_exists('tsk_tasks_activation_get')) {
                $result = tsk_tasks_activation_get($taskId, $tntId);
                return $result ? mysql_fetch_assoc($result) : null;
            }

            // Fallback query
            $query = "SELECT * FROM task_activations WHERE task_id = :task_id AND tenant_id = :tenant_id";
            $result = $this->query($query, array(
                'task_id' => $taskId,
                'tenant_id' => $tntId
            ));
            
            return $this->db->fetchAssoc($result);
        } catch (Exception $e) {
            error_log('Error getting task activation: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if task is active
     * 
     * @param int $taskId
     * @param int|null $tenantId
     * @return bool
     */
    public function isTaskActive($taskId, $tenantId = null)
    {
        $activation = $this->getTaskActivation($taskId, $tenantId);
        return $activation && ($activation['is_active'] ? $activation['is_active'] : 0) == 1;
    }

    /**
     * Get all active tasks for tenant
     * 
     * @param int|null $tenantId
     * @return array
     */
    public function getActiveTasks($tenantId = null)
    {
        $tntId = $tenantId ? $tenantId : $this->tenantId;
        
        try {
            // Use existing function if available
            if (function_exists('tsk_tasks_activation_get')) {
                $result = tsk_tasks_activation_get(0, $tntId, true);
                $tasks = [];
                
                if ($result) {
                    while ($task = mysql_fetch_assoc($result)) {
                        $tasks[] = $task;
                    }
                }
                
                return $tasks;
            }

            // Fallback query
            $query = "SELECT * FROM task_activations WHERE tenant_id = :tenant_id AND is_active = 1";
            $result = $this->query($query, array('tenant_id' => $tntId));
            
            return $this->db->fetchAllAssoc($result);
        } catch (Exception $e) {
            error_log('Error getting active tasks: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Get task categories
     * 
     * @return array
     */
    public function getTaskCategories()
    {
        try {
            // Use existing function if available
            if (function_exists('tsk_tasks_get')) {
                $result = tsk_tasks_get(null, '', 0);
                $categories = [];
                
                if ($result) {
                    while ($category = mysql_fetch_assoc($result)) {
                        $categories[] = $category;
                    }
                }
                
                return $categories;
            }

            // Fallback query
            $query = "SELECT * FROM tasks WHERE parent_id = 0 ORDER BY name";
            $result = $this->query($query);
            
            return $this->db->fetchAllAssoc($result);
        } catch (Exception $e) {
            error_log('Error getting task categories: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Get task subcategories
     * 
     * @param int $categoryId
     * @return array
     */
    public function getTaskSubcategories($categoryId)
    {
        try {
            // Use existing function if available
            if (function_exists('tsk_tasks_get')) {
                $result = tsk_tasks_get(null, '', $categoryId);
                $subcategories = [];
                
                if ($result) {
                    while ($subcategory = mysql_fetch_assoc($result)) {
                        $subcategories[] = $subcategory;
                    }
                }
                
                return $subcategories;
            }

            // Fallback query
            $query = "SELECT * FROM tasks WHERE parent_id = :parent_id ORDER BY name";
            $result = $this->query($query, array('parent_id' => $categoryId));
            
            return $this->db->fetchAllAssoc($result);
        } catch (Exception $e) {
            error_log('Error getting task subcategories: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Get tasks for subcategory
     * 
     * @param int $subcategoryId
     * @param int|null $tenantId
     * @return array
     */
    public function getTasksForSubcategory($subcategoryId, $tenantId = null)
    {
        $tntId = $tenantId ? $tenantId : $this->tenantId;
        
        try {
            // Use existing function if available
            if (function_exists('tsk_tasks_activation_get')) {
                $result = tsk_tasks_activation_get(0, $tntId, null, $subcategoryId);
                $tasks = [];
                
                if ($result) {
                    while ($task = mysql_fetch_array($result)) {
                        $tasks[$task['tsk_id']] = [
                            'name' => $task['tsk_name'],
                            'active' => $task['is_active']
                        ];
                    }
                }
                
                return $tasks;
            }

            // Fallback query
            $query = "SELECT ta.*, t.name as task_name
                     FROM task_activations ta
                     JOIN tasks t ON ta.task_id = t.id
                     WHERE ta.tenant_id = :tenant_id AND t.parent_id = :subcategory_id";
            $result = $this->query($query, array(
                'tenant_id' => $tntId,
                'subcategory_id' => $subcategoryId
            ));
            
            $tasks = [];
            $rows = $this->db->fetchAllAssoc($result);
            
            foreach ($rows as $row) {
                $tasks[$row['task_id']] = [
                    'name' => $row['task_name'],
                    'active' => $row['is_active']
                ];
            }
            
            return $tasks;
        } catch (Exception $e) {
            error_log('Error getting tasks for subcategory: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Get task activity details
     * 
     * @param int $taskId
     * @return array|null
     */
    public function getTaskActivity($taskId)
    {
        try {
            // Use existing function if available
            if (function_exists('tsk_activities_get')) {
                $result = tsk_activities_get($taskId);
                return $result ? mysql_fetch_assoc($result) : null;
            }

            // Fallback query
            $query = "SELECT * FROM task_activities WHERE task_id = :task_id ORDER BY date_start DESC LIMIT 1";
            $result = $this->query($query, array('task_id' => $taskId));
            
            return $this->db->fetchAssoc($result);
        } catch (Exception $e) {
            error_log('Error getting task activity: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get all tasks organized by categories and subcategories
     * 
     * @param int|null $tenantId
     * @return array
     */
    public function getAllTasksOrganized($tenantId = null)
    {
        $tntId = $tenantId ? $tenantId : $this->tenantId;
        $groups = [];
        
        try {
            $categories = $this->getTaskCategories();
            
            foreach ($categories as $category) {
                $subcategories = $this->getTaskSubcategories($category['id']);
                
                foreach ($subcategories as $subcategory) {
                    $tasks = $this->getTasksForSubcategory($subcategory['id'], $tntId);
                    
                    if (!empty($tasks)) {
                        // Enhance tasks with activity details
                        foreach ($tasks as $taskId => &$task) {
                            $activity = $this->getTaskActivity($taskId);
                            if ($activity) {
                                $task['count_total'] = $activity['count_total'];
                                $task['count_remaining'] = $activity['count_remaining'];
                                $task['count_fail'] = $activity['count_fail'];
                                $task['date_start'] = $activity['date_start'];
                                $task['date_end'] = $activity['date_end'];
                            } else {
                                $task['count_total'] = 0;
                                $task['count_remaining'] = 0;
                                $task['count_fail'] = 0;
                                $task['date_start'] = null;
                                $task['date_end'] = null;
                            }
                        }
                        
                        $groups[$category['name']][$subcategory['name']] = $tasks;
                    }
                }
            }
            
            return $groups;
        } catch (Exception $e) {
            error_log('Error getting organized tasks: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Activate/deactivate tasks
     * 
     * @param array $taskIds
     * @param int|null $tenantId
     * @return bool
     */
    public function activateTasks($taskIds, $tenantId = null)
    {
        $tntId = $tenantId ? $tenantId : $this->tenantId;
        
        try {
            // Get currently active tasks
            $currentActive = [];
            $activeTasks = $this->getActiveTasks($tntId);
            foreach ($activeTasks as $task) {
                $currentActive[] = $task['tsk_id'] ? $task['tsk_id'] : $task['task_id'];
            }
            
            // Calculate tasks to activate and deactivate
            $toDeactivate = array_diff($currentActive, $taskIds);
            $toActivate = array_diff($taskIds, $currentActive);
            
            // Create XML file for connector
            $xml = '<?xml version="1.0" encoding="utf-8"?><tasks>';
            
            foreach ($toDeactivate as $taskId) {
                $xml .= '<task id="' . $taskId . '" active="0" />';
            }
            
            foreach ($toActivate as $taskId) {
                $xml .= '<task id="' . $taskId . '" active="1" />';
            }
            
            $xml .= '</tasks>';
            
            // Save XML file
            $filename = SYNC_FOLDER . 'task-' . $tntId . '-' . time() . '.xml';
            $result = file_put_contents($filename, $xml);
            
            if ($result === false) {
                throw new Exception('Unable to create task activation file.');
            }
            
            return true;
        } catch (Exception $e) {
            error_log('Error activating tasks: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Force task execution
     * 
     * @param int $taskId
     * @param int|null $tenantId
     * @return bool
     */
    public function forceTaskExecution($taskId, $tenantId = null)
    {
        $tntId = $tenantId ? $tenantId : $this->tenantId;
        
        try {
            // Check if task is active
            if (!$this->isTaskActive($taskId, $tntId)) {
                throw new Exception('Cannot force inactive task.');
            }
            
            // Create force execution file
            $filename = SYNC_FOLDER . 'forcetsk-' . $tntId . '-' . time() . '.log';
            $result = file_put_contents($filename, $taskId);
            
            if ($result === false) {
                throw new Exception('Unable to create force execution file.');
            }
            
            return true;
        } catch (Exception $e) {
            error_log('Error forcing task execution: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Create custom configuration file
     * 
     * @param array $params
     * @param int|null $tenantId
     * @return bool
     */
    public function createCustomConfigFile($params, $tenantId = null)
    {
        $tntId = $tenantId ? $tenantId : $this->tenantId;
        
        try {
            // Build XML manually for PHP 5.6 compatibility
            $xml = '<?xml version="1.0" encoding="utf-8"?><params>';

            foreach ($params as $name => $value) {
                if (strpos($name, ' ') !== false) {
                    throw new Exception('Parameter name cannot contain spaces.');
                }

                $xml .= '<param name="' . htmlspecialchars($name) . '">' . htmlspecialchars($value) . '</param>';
            }

            $xml .= '</params>';

            $filename = SYNC_FOLDER . 'settings-' . $tntId . '-' . time() . '.xml';
            $result = file_put_contents($filename, $xml);

            return $result !== false;
        } catch (Exception $e) {
            error_log('Error creating custom config file: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Verify task assignment files
     * 
     * @return array
     */
    public function verifyTaskAssignment()
    {
        try {
            $files = scandir(SYNC_FOLDER);
            return array_filter($files, function($file) {
                return strpos($file, 'task-') === 0;
            });
        } catch (Exception $e) {
            error_log('Error verifying task assignment: ' . $e->getMessage());
            return array();
        }
    }
}
