<?php
/**
 * Main Entry Point for Maintenance Interface
 *
 * Routes requests to appropriate controllers
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

// Define access constant
define('MAINTENANCE_ACCESS', true);

// Error reporting for development (remove in production)
/*ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);*/

// Include required files from the original system
require_once('http.inc.php');
require_once('tenants.inc.php');
require_once('websites.inc.php');
require_once('users.inc.php');
require_once('orders.inc.php');
require_once('products.inc.php');
require_once('fields.inc.php');
require_once('tasks.inc.php');
require_once('cfg.variables.inc.php');

// Include our new configuration and utilities
require_once('config/constants.php');
require_once('utils/Authentication.php');
require_once('utils/Response.php');
require_once('utils/Validator.php');
require_once('utils/Database.php');
require_once('utils/Logger.php');
require_once('utils/VersionManager.php');
require_once('utils/SvnManager.php');
require_once('utils/ScriptExecutor.php');

// Include models
require_once('models/BaseModel.php');
require_once('models/SyncModel.php');
require_once('models/TaskModel.php');
require_once('models/UpdateModel.php');
require_once('models/LogsModel.php');

// Include controllers
require_once('controllers/BaseController.php');
require_once('controllers/MaintenanceController.php');
require_once('controllers/TaskController.php');
require_once('controllers/ConfigurationController.php');
require_once('controllers/UpdateController.php');
require_once('controllers/LogsController.php');

try {
    // Initialize session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Get the action from URL parameters
    $action = isset($_GET['action']) ? $_GET['action'] : 'index';
    $controller = isset($_GET['controller']) ? $_GET['controller'] : 'maintenance';

    // Route the request
    switch ($controller) {
        case 'maintenance':
            $maintenanceController = new Maintenance_Controllers_MaintenanceController();

            switch ($action) {
                case 'index':
                default:
                    $maintenanceController->index();
                    break;

                case 'sync-info':
                    $maintenanceController->ajaxSyncInfo();
                    break;

                case 'update-sync-status':
                    $maintenanceController->updateSyncStatus();
                    break;

                case 'tenant-info':
                    $maintenanceController->getTenantInfo();
                    break;

                case 'system-status':
                    $maintenanceController->systemStatus();
                    break;

                case 'get-system-status':
                    $maintenanceController->getSystemStatus();
                    break;

                case 'tasks':
                    $maintenanceController->redirectToTasks();
                    break;

                case 'configuration':
                    $maintenanceController->redirectToConfiguration();
                    break;

                case 'updates':
                    $maintenanceController->redirectToUpdates();
                    break;

                case 'logs':
                    $maintenanceController->redirectToLogs();
                    break;
            }
            break;

        case 'tasks':
            $taskController = new Maintenance_Controllers_TaskController();

            switch ($action) {
                case 'index':
                default:
                    $taskController->index();
                    break;

                case 'get-tasks':
                    $taskController->getTasks();
                    break;

                case 'activate-tasks':
                    $taskController->activateTasks();
                    break;

                case 'toggle-task':
                    $taskController->toggleTask();
                    break;

                case 'force-task':
                    $taskController->forceTask();
                    break;

                case 'get-task-status':
                    $taskController->getTaskStatus();
                    break;

                case 'get-active-tasks':
                    $taskController->getActiveTasks();
                    break;

                case 'create-custom-config':
                    $taskController->createCustomConfig();
                    break;

                case 'verify-task-assignment':
                    $taskController->verifyTaskAssignment();
                    break;

                case 'export-tasks':
                    $taskController->exportTasks();
                    break;
            }
            break;

        case 'configuration':
            $configurationController = new Maintenance_Controllers_ConfigurationController();

            switch ($action) {
                case 'index':
                default:
                    $configurationController->index();
                    break;

                case 'save':
                    $configurationController->save();
                    break;
            }
            break;

        case 'updates':
            $updateController = new Maintenance_Controllers_UpdateController();

            switch ($action) {
                case 'index':
                default:
                    $updateController->index();
                    break;

                case 'backend-update':
                    $updateController->performBackendUpdate();
                    break;

                case 'frontend-update':
                    $updateController->performFrontendUpdate();
                    break;

                case 'system-status':
                    $updateController->getSystemStatus();
                    break;

                case 'download-log':
                    $updateController->downloadLog();
                    break;

                // Granular workflow steps
                case 'svn-update-engine':
                    $updateController->executeSvnUpdateEngine();
                    break;

                case 'execute-sql-scripts':
                    $updateController->executeSqlScripts();
                    break;

                case 'execute-php-scripts':
                    $updateController->executePhpScripts();
                    break;

                case 'update-version':
                    $updateController->executeUpdateVersion();
                    break;

                case 'svn-update-frontend':
                    $updateController->executeSvnUpdateFrontend();
                    break;

                // SVN cleanup operations
                case 'svn-cleanup-engine':
                    $updateController->executeSvnCleanupEngine();
                    break;

                case 'svn-cleanup-frontend':
                    $updateController->executeSvnCleanupFrontend();
                    break;

                // Script replay operations
                case 'replay-sql-scripts':
                    $updateController->replaySqlScripts();
                    break;

                case 'replay-php-scripts':
                    $updateController->replayPhpScripts();
                    break;

                case 'skip-sql-scripts':
                    $updateController->skipSqlScripts();
                    break;

                case 'skip-php-scripts':
                    $updateController->skipPhpScripts();
                    break;

                case 'replay-individual-script':
                    $updateController->replayIndividualScript();
                    break;
            }
            break;

        case 'logs':
            $logsController = new Maintenance_Controllers_LogsController();

            switch ($action) {
                case 'index':
                default:
                    $logsController->index();
                    break;

                case 'view':
                    $logsController->view();
                    break;

                case 'download':
                    $logsController->download();
                    break;

                case 'delete':
                    $logsController->delete();
                    break;

                case 'deleteMultiple':
                    $logsController->deleteMultiple();
                    break;

                case 'search':
                    $logsController->search();
                    break;

                case 'getStats':
                    $logsController->getStats();
                    break;

                case 'clearAll':
                    $logsController->clearAll();
                    break;
            }
            break;
    }

} catch (Exception $e) {
    error_log('Maintenance Application Error: ' . $e->getMessage());

    if (Maintenance_Utils_Authentication::isAjaxRequest()) {
        Maintenance_Utils_Response::serverError('An unexpected error occurred');
    } else {
        if (function_exists('http_response_code')) {
            http_response_code(500);
        } else {
            header('HTTP/1.1 500 Internal Server Error');
        }
        echo '<h1>500 Internal Server Error</h1>';
        echo '<p>An unexpected error occurred. Please try again later.</p>';

        // Show error details in development
        if (defined('MAINTENANCE_DEBUG') && MAINTENANCE_DEBUG) {
            echo '<pre>' . htmlspecialchars($e->getMessage()) . '</pre>';
            echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
        }
    }
}