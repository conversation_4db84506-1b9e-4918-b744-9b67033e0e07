<?php
/**
 * Constants Configuration
 *
 * Defines all constants used throughout the maintenance application
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

// Prevent direct access
if (!defined('MAINTENANCE_ACCESS')) {
    if (function_exists('http_response_code')) {
        http_response_code(403);
    } else {
        header('HTTP/1.1 403 Forbidden');
    }
    exit('Direct access forbidden');
}

// Application constants
if (!defined('MAINTENANCE_VERSION')) {
    define('MAINTENANCE_VERSION', '2.0.0');
}
if (!defined('MAINTENANCE_BASE_PATH')) {
    define('MAINTENANCE_BASE_PATH', dirname(__DIR__));
}
if (!defined('MAINTENANCE_VIEWS_PATH')) {
    define('MAINTENANCE_VIEWS_PATH', MAINTENANCE_BASE_PATH . '/views');
}
if (!defined('MAINTENANCE_ASSETS_PATH')) {
    define('MAINTENANCE_ASSETS_PATH', MAINTENANCE_BASE_PATH . '/assets');
}

// Database constants
if (!defined('DB_CHARSET')) {
    define('DB_CHARSET', 'utf8mb4');
}
if (!defined('DB_COLLATION')) {
    define('DB_COLLATION', 'utf8mb4_unicode_ci');
}

// Security constants
if (!defined('SESSION_TIMEOUT')) {
    define('SESSION_TIMEOUT', 3600); // 1 hour
}
if (!defined('MAX_LOGIN_ATTEMPTS')) {
    define('MAX_LOGIN_ATTEMPTS', 5);
}
if (!defined('CSRF_TOKEN_LENGTH')) {
    define('CSRF_TOKEN_LENGTH', 32);
}

// API constants
if (!defined('API_VERSION')) {
    define('API_VERSION', 'v1');
}
if (!defined('API_RATE_LIMIT')) {
    define('API_RATE_LIMIT', 100); // requests per minute
}
if (!defined('API_TIMEOUT')) {
    define('API_TIMEOUT', 30); // seconds
}

// Sync constants
if (!defined('SYNC_API_URL')) {
    define('SYNC_API_URL', 'https://berton-preprod-sync.riashop.app/');
}
if (!defined('SYNC_FOLDER')) {
    define('SYNC_FOLDER', '/var/www/engine/htdocs/sync/reports/');
}
if (!defined('SYNC_TIMEOUT')) {
    define('SYNC_TIMEOUT', 300); // 5 minutes
}

// Task constants
if (!defined('TASK_STATUS_ACTIVE')) {
    define('TASK_STATUS_ACTIVE', 1);
}
if (!defined('TASK_STATUS_INACTIVE')) {
    define('TASK_STATUS_INACTIVE', 0);
}
if (!defined('TASK_PRIORITY_HIGH')) {
    define('TASK_PRIORITY_HIGH', 1);
}
if (!defined('TASK_PRIORITY_NORMAL')) {
    define('TASK_PRIORITY_NORMAL', 2);
}
if (!defined('TASK_PRIORITY_LOW')) {
    define('TASK_PRIORITY_LOW', 3);
}

// Configuration constants
if (!defined('CONFIG_CACHE_TTL')) {
    define('CONFIG_CACHE_TTL', 3600); // 1 hour
}
if (!defined('CONFIG_MAX_OVERRIDE_DEPTH')) {
    define('CONFIG_MAX_OVERRIDE_DEPTH', 5);
}

// Response codes
if (!defined('HTTP_OK')) {
    define('HTTP_OK', 200);
}
if (!defined('HTTP_CREATED')) {
    define('HTTP_CREATED', 201);
}
if (!defined('HTTP_BAD_REQUEST')) {
    define('HTTP_BAD_REQUEST', 400);
}
if (!defined('HTTP_UNAUTHORIZED')) {
    define('HTTP_UNAUTHORIZED', 401);
}
if (!defined('HTTP_FORBIDDEN')) {
    define('HTTP_FORBIDDEN', 403);
}
if (!defined('HTTP_NOT_FOUND')) {
    define('HTTP_NOT_FOUND', 404);
}
if (!defined('HTTP_METHOD_NOT_ALLOWED')) {
    define('HTTP_METHOD_NOT_ALLOWED', 405);
}
if (!defined('HTTP_INTERNAL_SERVER_ERROR')) {
    define('HTTP_INTERNAL_SERVER_ERROR', 500);
}

// Log levels
if (!defined('LOG_LEVEL_DEBUG')) {
    define('LOG_LEVEL_DEBUG', 'debug');
}
if (!defined('LOG_LEVEL_INFO')) {
    define('LOG_LEVEL_INFO', 'info');
}
if (!defined('LOG_LEVEL_WARNING')) {
    define('LOG_LEVEL_WARNING', 'warning');
}
if (!defined('LOG_LEVEL_ERROR')) {
    define('LOG_LEVEL_ERROR', 'error');
}
if (!defined('LOG_LEVEL_CRITICAL')) {
    define('LOG_LEVEL_CRITICAL', 'critical');
}

// File upload constants
if (!defined('MAX_FILE_SIZE')) {
    define('MAX_FILE_SIZE', 10485760); // 10MB
}

// Use a global variable for array, as define() doesn't support arrays in PHP 5.6
if (!isset($GLOBALS['ALLOWED_FILE_TYPES'])) {
    $GLOBALS['ALLOWED_FILE_TYPES'] = array('xml', 'json', 'csv');
}

// Pagination constants
if (!defined('DEFAULT_PAGE_SIZE')) {
    define('DEFAULT_PAGE_SIZE', 20);
}
if (!defined('MAX_PAGE_SIZE')) {
    define('MAX_PAGE_SIZE', 100);
}

// Cache constants
if (!defined('CACHE_PREFIX')) {
    define('CACHE_PREFIX', 'maintenance_');
}
if (!defined('CACHE_DEFAULT_TTL')) {
    define('CACHE_DEFAULT_TTL', 1800); // 30 minutes
}

// Validation constants
if (!defined('MIN_PASSWORD_LENGTH')) {
    define('MIN_PASSWORD_LENGTH', 8);
}
if (!defined('MAX_STRING_LENGTH')) {
    define('MAX_STRING_LENGTH', 255);
}
if (!defined('MAX_TEXT_LENGTH')) {
    define('MAX_TEXT_LENGTH', 65535);
}

// GESCOM type constants (from original codebase)
if (!defined('GESCOM_TYPE_SAGE')) {
    define('GESCOM_TYPE_SAGE', 1);
}
if (!defined('GESCOM_TYPE_ISIALIS')) {
    define('GESCOM_TYPE_ISIALIS', 2);
}
if (!defined('GESCOM_TYPE_LIMS')) {
    define('GESCOM_TYPE_LIMS', 3);
}
if (!defined('GESCOM_TYPE_HARMONYS')) {
    define('GESCOM_TYPE_HARMONYS', 4);
}
if (!defined('GESCOM_TYPE_PLATON')) {
    define('GESCOM_TYPE_PLATON', 5);
}
if (!defined('GESCOM_TYPE_APINEGOCE')) {
    define('GESCOM_TYPE_APINEGOCE', 6);
}
if (!defined('GESCOM_TYPE_G5')) {
    define('GESCOM_TYPE_G5', 7);
}
if (!defined('GESCOM_TYPE_DIVALTO')) {
    define('GESCOM_TYPE_DIVALTO', 8);
}
if (!defined('GESCOM_TYPE_CLISSON')) {
    define('GESCOM_TYPE_CLISSON', 9);
}
if (!defined('GESCOM_TYPE_EEE')) {
    define('GESCOM_TYPE_EEE', 10);
}
if (!defined('GESCOM_TYPE_DYNAMICS')) {
    define('GESCOM_TYPE_DYNAMICS', 11);
}
if (!defined('GESCOM_TYPE_SOFI')) {
    define('GESCOM_TYPE_SOFI', 12);
}
if (!defined('GESCOM_TYPE_WAVESOFT')) {
    define('GESCOM_TYPE_WAVESOFT', 13);
}
if (!defined('GESCOM_TYPE_CEGID')) {
    define('GESCOM_TYPE_CEGID', 14);
}
if (!defined('GESCOM_TYPE_SAGE_X3')) {
    define('GESCOM_TYPE_SAGE_X3', 15);
}
if (!defined('GESCOM_TYPE_DYNAMICS_NAVISION')) {
    define('GESCOM_TYPE_DYNAMICS_NAVISION', 16);
}
if (!defined('GESCOM_TYPE_SINERES')) {
    define('GESCOM_TYPE_SINERES', 17);
}
if (!defined('GESCOM_TYPE_BATIGEST')) {
    define('GESCOM_TYPE_BATIGEST', 18);
}
if (!defined('GESCOM_TYPE_STAR6000')) {
    define('STAR6000', 19); // Assuming original defines it like this from output
}
if (!defined('GESCOM_TYPE_EBP')) {
    define('EBP', 20); // Assuming original defines it like this from output
}
if (!defined('GESCOM_TYPE_SAGE_50C')) {
    define('SAGE_50C', 21); // Assuming original defines it like this from output
}
if (!defined('GESCOM_TYPE_DIVALTO_SQL')) {
    define('GESCOM_TYPE_DIVALTO_SQL', 22);
}

// State constants (from original codebase)
if (!defined('_STATE_BASKET')) {
    define('_STATE_BASKET', 1);
}
if (!defined('_STATE_BASKET_SAVE')) {
    define('_STATE_BASKET_SAVE', 2);
}
if (!defined('_STATE_DEVIS')) {
    define('_STATE_DEVIS', 28);
}

// Website type constants
if (!defined('_WST_TYPE_FDV')) {
    define('_WST_TYPE_FDV', 'fdv');
}

// Field type constants (from original codebase)
if (!defined('FLD_TYPE_TEXT')) {
    define('FLD_TYPE_TEXT', 1);
}
if (!defined('FLD_TYPE_TEXTAREA')) {
    define('FLD_TYPE_TEXTAREA', 2);
}
if (!defined('FLD_TYPE_INT')) {
    define('FLD_TYPE_INT', 3);
}
if (!defined('FLD_TYPE_NUMBER')) {
    define('FLD_TYPE_NUMBER', 3);
}
if (!defined('FLD_TYPE_FLOAT')) {
    define('FLD_TYPE_FLOAT', 4);
}
if (!defined('FLD_TYPE_SELECT')) {
    define('FLD_TYPE_SELECT', 5);
}
if (!defined('FLD_TYPE_SELECT_MULTIPLE')) {
    define('FLD_TYPE_SELECT_MULTIPLE', 6);
}
if (!defined('FLD_TYPE_IMAGE')) {
    define('FLD_TYPE_IMAGE', 7);
}
if (!defined('FLD_TYPE_BOOLEAN_YES_NO')) {
    define('FLD_TYPE_BOOLEAN_YES_NO', 8);
}
if (!defined('FLD_TYPE_IMAGES')) {
    define('FLD_TYPE_IMAGES', 9);
}
if (!defined('FLD_TYPE_DATE')) {
    define('FLD_TYPE_DATE', 10);
}
if (!defined('FLD_TYPE_REFERENCES_ID')) {
    define('FLD_TYPE_REFERENCES_ID', 11);
}
if (!defined('FLD_TYPE_SELECT_HIERARCHY')) {
    define('FLD_TYPE_SELECT_HIERARCHY', 12);
}
if (!defined('FLD_TYPE_FILE_UPLOAD')) {
    define('FLD_TYPE_FILE_UPLOAD', 13);
}

// Class constants (from original codebase)
if (!defined('CLS_CTR_MKT')) {
    define('CLS_CTR_MKT', 76);
}
if (!defined('CLS_FLD_MODELS')) {
    define('CLS_FLD_MODELS', 200);
}

// Update module constants
if (!defined('RIASHOP_VERSION_CONFIG_KEY')) {
    define('RIASHOP_VERSION_CONFIG_KEY', 'RIASHOP_VERSION');
}
if (!defined('MAJS_DIR_PATH')) {
    define('MAJS_DIR_PATH', '/var/www/engine/majs/');
}
if (!defined('ENGINE_SVN_REPO_PATH')) {
    define('ENGINE_SVN_REPO_PATH', '/var/www/engine');
}
if (!defined('FRONTEND_SVN_REPO_PATH')) {
    define('FRONTEND_SVN_REPO_PATH', '/var/www/sites/front');
}
if (!defined('LOGS_DIR_PATH')) {
    define('LOGS_DIR_PATH', '/var/www/');
}