<?php
/**
 * Update Controller Class
 *
 * Handles update operations for the maintenance interface
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Controllers_UpdateController extends Maintenance_Controllers_BaseController
{
    private $updateModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->updateModel = new Maintenance_Models_UpdateModel();
    }

    /**
     * Display update interface
     */
    public function index()
    {
        try {
            $systemStatus = $this->updateModel->getSystemStatus();
            
            $this->render('update/index', array(
                'title' => 'Mise à Jour Manuelle Riashop',
                'systemStatus' => $systemStatus
            ));
        } catch (Exception $e) {
            $this->handleError($e, 'Failed to load update interface');
        }
    }

    /**
     * Perform backend update via AJAX
     */
    public function performBackendUpdate()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            // Verify CSRF token if available
            if (isset($_POST['csrf_token'])) {
                if (!$this->verifyCsrfToken($_POST['csrf_token'])) {
                    Maintenance_Utils_Response::badRequest('Invalid CSRF token');
                    return;
                }
            }

            $result = $this->updateModel->performBackendUpdate();
            
            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('Backend Update Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('Backend update failed: ' . $e->getMessage());
        }
    }

    /**
     * Perform frontend update via AJAX
     */
    public function performFrontendUpdate()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            // Verify CSRF token if available
            if (isset($_POST['csrf_token'])) {
                if (!$this->verifyCsrfToken($_POST['csrf_token'])) {
                    Maintenance_Utils_Response::badRequest('Invalid CSRF token');
                    return;
                }
            }

            $result = $this->updateModel->performFrontendUpdate();
            
            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('Frontend Update Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('Frontend update failed: ' . $e->getMessage());
        }
    }

    /**
     * Get system status via AJAX
     */
    public function getSystemStatus()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        try {
            $systemStatus = $this->updateModel->getSystemStatus();
            Maintenance_Utils_Response::success($systemStatus);
        } catch (Exception $e) {
            error_log('System Status Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('Failed to get system status: ' . $e->getMessage());
        }
    }

    /**
     * Execute SVN update engine step
     */
    public function executeSvnUpdateEngine()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            $this->verifyCsrfTokenFromPost();

            $credentials = null;
            if (isset($_POST['username']) && isset($_POST['password'])) {
                $credentials = array(
                    'username' => $_POST['username'],
                    'password' => $_POST['password']
                );
            }

            $result = $this->updateModel->executeSvnUpdateEngine($credentials);

            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('SVN Update Engine Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('SVN update engine failed: ' . $e->getMessage());
        }
    }

    /**
     * Execute SQL scripts step
     */
    public function executeSqlScripts()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            $this->verifyCsrfTokenFromPost();

            $result = $this->updateModel->executeSqlScripts();

            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('SQL Scripts Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('SQL scripts execution failed: ' . $e->getMessage());
        }
    }

    /**
     * Execute PHP scripts step
     */
    public function executePhpScripts()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            $this->verifyCsrfTokenFromPost();

            $result = $this->updateModel->executePhpScripts();

            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('PHP Scripts Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('PHP scripts execution failed: ' . $e->getMessage());
        }
    }

    /**
     * Execute update version step
     */
    public function executeUpdateVersion()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            $this->verifyCsrfTokenFromPost();

            $result = $this->updateModel->executeUpdateVersion();

            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('Update Version Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('Version update failed: ' . $e->getMessage());
        }
    }

    /**
     * Execute SVN update frontend step
     */
    public function executeSvnUpdateFrontend()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            $this->verifyCsrfTokenFromPost();

            $credentials = null;
            if (isset($_POST['username']) && isset($_POST['password'])) {
                $credentials = array(
                    'username' => $_POST['username'],
                    'password' => $_POST['password']
                );
            }

            $result = $this->updateModel->executeSvnUpdateFrontend($credentials);

            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('SVN Update Frontend Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('SVN update frontend failed: ' . $e->getMessage());
        }
    }

    /**
     * Execute SVN cleanup engine step
     */
    public function executeSvnCleanupEngine()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            $this->verifyCsrfTokenFromPost();

            $result = $this->updateModel->executeSvnCleanupEngine();

            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('SVN Cleanup Engine Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('SVN cleanup engine failed: ' . $e->getMessage());
        }
    }

    /**
     * Execute SVN cleanup frontend step
     */
    public function executeSvnCleanupFrontend()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            $this->verifyCsrfTokenFromPost();

            $result = $this->updateModel->executeSvnCleanupFrontend();

            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('SVN Cleanup Frontend Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('SVN cleanup frontend failed: ' . $e->getMessage());
        }
    }

    /**
     * Replay SQL scripts (execute all scripts regardless of version)
     */
    public function replaySqlScripts()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            $this->verifyCsrfTokenFromPost();

            $result = $this->updateModel->replaySqlScripts();

            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('SQL Scripts Replay Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('SQL scripts replay failed: ' . $e->getMessage());
        }
    }

    /**
     * Replay PHP scripts (execute all scripts regardless of version)
     */
    public function replayPhpScripts()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            $this->verifyCsrfTokenFromPost();

            $result = $this->updateModel->replayPhpScripts();

            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('PHP Scripts Replay Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('PHP scripts replay failed: ' . $e->getMessage());
        }
    }

    /**
     * Skip SQL scripts step
     */
    public function skipSqlScripts()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            $this->verifyCsrfTokenFromPost();

            $result = $this->updateModel->skipSqlScripts();

            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('SQL Scripts Skip Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('SQL scripts skip failed: ' . $e->getMessage());
        }
    }

    /**
     * Skip PHP scripts step
     */
    public function skipPhpScripts()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            $this->verifyCsrfTokenFromPost();

            $result = $this->updateModel->skipPhpScripts();

            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('PHP Scripts Skip Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('PHP scripts skip failed: ' . $e->getMessage());
        }
    }

    /**
     * Replay individual script
     */
    public function replayIndividualScript()
    {
        if (!Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::methodNotAllowed('AJAX request required');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            Maintenance_Utils_Response::methodNotAllowed('POST method required');
            return;
        }

        try {
            $this->verifyCsrfTokenFromPost();

            $scriptFile = isset($_POST['script_file']) ? $_POST['script_file'] : '';
            $scriptType = isset($_POST['script_type']) ? $_POST['script_type'] : '';

            if (empty($scriptFile) || empty($scriptType)) {
                Maintenance_Utils_Response::badRequest('Script file and type are required');
                return;
            }

            // Validate script type
            if (!in_array($scriptType, array('sql', 'php'))) {
                Maintenance_Utils_Response::badRequest('Invalid script type');
                return;
            }

            $result = $this->updateModel->replayIndividualScript($scriptFile, $scriptType);

            if ($result['success']) {
                Maintenance_Utils_Response::success($result);
            } else {
                Maintenance_Utils_Response::serverError($result['message'], $result);
            }
        } catch (Exception $e) {
            error_log('Individual Script Replay Error: ' . $e->getMessage());
            Maintenance_Utils_Response::serverError('Individual script replay failed: ' . $e->getMessage());
        }
    }

    /**
     * Download log file
     */
    public function downloadLog()
    {
        try {
            $logFile = isset($_GET['file']) ? $_GET['file'] : '';

            if (empty($logFile)) {
                if (function_exists('http_response_code')) {
                    http_response_code(400);
                } else {
                    header('HTTP/1.1 400 Bad Request');
                }
                echo 'Log file parameter required';
                return;
            }

            $logsDir = defined('LOGS_DIR_PATH') ? LOGS_DIR_PATH : 'maintenance/logs/';
            $fullPath = $logsDir . basename($logFile);

            if (!file_exists($fullPath) || !is_file($fullPath)) {
                if (function_exists('http_response_code')) {
                    http_response_code(404);
                } else {
                    header('HTTP/1.1 404 Not Found');
                }
                echo 'Log file not found: ' . htmlspecialchars($logFile);
                return;
            }

            // Security check: ensure file is in logs directory
            $realLogsDir = realpath($logsDir);
            $realFilePath = realpath($fullPath);

            if (strpos($realFilePath, $realLogsDir) !== 0) {
                if (function_exists('http_response_code')) {
                    http_response_code(403);
                } else {
                    header('HTTP/1.1 403 Forbidden');
                }
                echo 'Access denied';
                return;
            }

            // Enhanced headers for better download experience
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="' . basename($logFile) . '"');
            header('Content-Length: ' . filesize($fullPath));
            header('Cache-Control: no-cache, must-revalidate');
            header('Expires: 0');

            readfile($fullPath);
        } catch (Exception $e) {
            error_log('Download Log Error: ' . $e->getMessage());
            if (function_exists('http_response_code')) {
                http_response_code(500);
            } else {
                header('HTTP/1.1 500 Internal Server Error');
            }
            echo 'Failed to download log file: ' . htmlspecialchars($e->getMessage());
        }
    }

    /**
     * Verify CSRF token
     *
     * @param string $token
     * @return bool
     */
    private function verifyCsrfToken($token)
    {
        // Basic CSRF token verification
        // In a real implementation, this should be more robust
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        return isset($_SESSION['csrf_token']) && $_SESSION['csrf_token'] === $token;
    }

    /**
     * Verify CSRF token from POST data
     *
     * @throws Exception
     */
    private function verifyCsrfTokenFromPost()
    {
        if (isset($_POST['csrf_token'])) {
            if (!$this->verifyCsrfToken($_POST['csrf_token'])) {
                Maintenance_Utils_Response::badRequest('Invalid CSRF token');
                exit;
            }
        }
    }

    /**
     * Generate CSRF token
     *
     * @return string
     */
    public function generateCsrfToken()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(openssl_random_pseudo_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
}
