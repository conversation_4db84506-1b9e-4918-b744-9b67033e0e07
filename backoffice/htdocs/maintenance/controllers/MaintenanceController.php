<?php
/**
 * Maintenance Controller Class
 *
 * Handles the main maintenance dashboard
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Controllers_MaintenanceController extends Maintenance_Controllers_BaseController
{
    private $syncModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->syncModel = new Maintenance_Models_SyncModel($this->config);
    }

    /**
     * Display main dashboard
     *
     * @return void
     */
    public function index()
    {
        try {
            // Get sync information
            $syncInfo = $this->getSyncInfo();

            // Set page title and breadcrumbs
            $pageTitle = _('Page de maintenance') . ' - ' . _('Maintenance');
            $this->setBreadcrumbs();

            // Render dashboard view
            $this->render('maintenance/dashboard', array(
                'pageTitle' => $pageTitle,
                'syncInfo' => $syncInfo,
                'tenantId' => $this->getTenantId(),
                'csrfToken' => Maintenance_Utils_Authentication::generateCsrfToken()
            ));

        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * Get synchronization information
     *
     * @return array
     */
    public function getSyncInfo()
    {
        try {
            $syncInfo = $this->syncModel->getSyncInfo();

            // Add task status information
            $syncInfo['task_status'] = array(
                'orders' => $this->isTaskActive(22), // Orders import task ID
                'quotes' => $this->isTaskActive(329), // Quotes import task ID
                'users' => $this->isTaskActive(49), // Users import task ID
                'addresses' => $this->isTaskActive(52) // Addresses import task ID
            );

            // Add BigShip specific info if applicable
            if ($this->getTenantId() === 1) {
                $syncInfo['bigship_info'] = $this->syncModel->getBigShipInfo();
            }

            return $syncInfo;

        } catch (Exception $e) {
            error_log('Error getting sync info: ' . $e->getMessage());
            return array(
                'tenant_id' => $this->getTenantId(),
                'token' => '',
                'gescom_type' => 'Unknown',
                'sync_reboot' => 0,
                'last_sync' => null,
                'waiting_counts' => array(
                    'orders' => 0,
                    'quotes' => 0,
                    'users' => 0,
                    'addresses' => 0
                ),
                'task_status' => array(
                    'orders' => false,
                    'quotes' => false,
                    'users' => false,
                    'addresses' => false
                )
            );
        }
    }

    /**
     * Check if a task is active
     *
     * @param int $taskId
     * @return bool
     */
    private function isTaskActive($taskId)
    {
        try {
            if (function_exists('tsk_tasks_activation_get')) {
                $result = tsk_tasks_activation_get($taskId, $this->getTenantId());
                $task = $result ? mysql_fetch_assoc($result) : null;
                return $task && (isset($task['is_active']) ? $task['is_active'] : 0) == 1;
            }
            return false;
        } catch (Exception $e) {
            error_log('Error checking task status: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle AJAX request for sync info update
     *
     * @return void
     */
    public function ajaxSyncInfo()
    {
        try {
            $this->requireMethod('GET');

            $syncInfo = $this->getSyncInfo();

            // Format response for AJAX
            $response = array(
                'sync_reboot' => $syncInfo['sync_reboot'],
                'last_sync' => $syncInfo['last_sync'],
                'waiting_orders' => array(
                    'count' => $syncInfo['waiting_counts']['orders'],
                    'active' => $syncInfo['task_status']['orders']
                ),
                'waiting_quote' => array(
                    'count' => $syncInfo['waiting_counts']['quotes'],
                    'active' => $syncInfo['task_status']['quotes']
                ),
                'waiting_users' => array(
                    'count' => $syncInfo['waiting_counts']['users'],
                    'active' => $syncInfo['task_status']['users']
                ),
                'waiting_addresses' => array(
                    'count' => $syncInfo['waiting_counts']['addresses'],
                    'active' => $syncInfo['task_status']['addresses']
                )
            );

            Maintenance_Utils_Response::success($response);

        } catch (Exception $e) {
            Maintenance_Utils_Response::serverError('Failed to get sync information');
        }
    }

    /**
     * Update sync status
     *
     * @return void
     */
    public function updateSyncStatus()
    {
        try {
            $this->requireMethod('POST');
            $this->checkCsrfToken();

            // Validate input
            $validator = $this->validate(array(
                'sync_reboot' => 'integer',
                'last_sync' => 'string'
            ));

            if ($validator->fails()) {
                Maintenance_Utils_Response::validationError($validator->getErrors());
            }

            // Update sync status
            $updateData = array();

            if ($validator->get('sync_reboot') !== null) {
                $updateData['sync_reboot'] = $validator->get('sync_reboot');
            }

            if ($validator->get('last_sync') !== null) {
                $updateData['last-sync'] = $validator->get('last_sync');
            }

            if (empty($updateData)) {
                Maintenance_Utils_Response::error('No data to update');
            }

            $success = $this->syncModel->updateSyncStatus($updateData);

            if ($success) {
                $this->logAction('sync_status_updated', $updateData);
                Maintenance_Utils_Response::success(null, 'Sync status updated successfully');
            } else {
                Maintenance_Utils_Response::serverError('Failed to update sync status');
            }

        } catch (Exception $e) {
            Maintenance_Utils_Response::serverError('Failed to update sync status');
        }
    }

    /**
     * Get tenant information
     *
     * @return void
     */
    public function getTenantInfo()
    {
        try {
            $tenantId = $this->getIntParam('tenant_id', $this->getTenantId());

            if (!$tenantId) {
                Maintenance_Utils_Response::error('Invalid tenant ID');
            }

            $tenantInfo = $this->syncModel->getTenantInfo($tenantId);

            if (!$tenantInfo) {
                Maintenance_Utils_Response::notFound('Tenant');
            }

            // Remove sensitive information
            unset($tenantInfo['token']);

            Maintenance_Utils_Response::success($tenantInfo);

        } catch (Exception $e) {
            Maintenance_Utils_Response::serverError('Failed to get tenant information');
        }
    }

    /**
     * Set breadcrumbs for the page
     *
     * @return void
     */
    private function setBreadcrumbs()
    {
        if (class_exists('Breadcrumbs')) {
            Breadcrumbs::root(_('Accueil'), '/admin/index.php')
                       ->push(_('Page de maintenance'));
        }
    }

    /**
     * Handle navigation to tasks page
     *
     * @return void
     */
    public function redirectToTasks()
    {
        $tenantId = $this->getTenantId();
        $url = "/maintenance/tasks?tnt_id={$tenantId}";
        Maintenance_Utils_Response::redirect($url);
    }

    /**
     * Handle navigation to configuration page
     *
     * @return void
     */
    public function redirectToConfiguration()
    {
        $tenantId = $this->getTenantId();
        $url = "/maintenance/?controller=configuration&tnt_id={$tenantId}";
        Maintenance_Utils_Response::redirect($url);
    }

    /**
     * Handle navigation to updates page
     *
     * @return void
     */
    public function redirectToUpdates()
    {
        $tenantId = $this->getTenantId();
        $url = "/maintenance/?controller=updates&tnt_id={$tenantId}";
        Maintenance_Utils_Response::redirect($url);
    }

    /**
     * Handle navigation to logs page
     *
     * @return void
     */
    public function redirectToLogs()
    {
        $tenantId = $this->getTenantId();
        $url = "/maintenance/?controller=logs&tnt_id={$tenantId}";
        Maintenance_Utils_Response::redirect($url);
    }

    /**
     * Display system status page
     *
     * @return void
     */
    public function systemStatus()
    {
        try {
            $status = $this->getSystemStatusData();

            // Set page title
            $pageTitle = 'Statut du système - Maintenance';

            // Render system status view
            $this->render('maintenance/system-status', array(
                'pageTitle' => $pageTitle,
                'status' => $status,
                'tenantId' => $this->getTenantId()
            ));

        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * Get system status (JSON API)
     *
     * @return void
     */
    public function getSystemStatus()
    {
        try {
            $status = $this->getSystemStatusData();
            Maintenance_Utils_Response::success($status);

        } catch (Exception $e) {
            Maintenance_Utils_Response::serverError('Failed to get system status');
        }
    }

    /**
     * Get system status data
     *
     * @return array
     */
    private function getSystemStatusData()
    {
        return array(
            'system' => array(
                'php_version' => PHP_VERSION,
                'memory_usage' => memory_get_usage(true),
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
                'server_software' => isset($_SERVER['SERVER_SOFTWARE']) ? $_SERVER['SERVER_SOFTWARE'] : 'Unknown',
                'document_root' => isset($_SERVER['DOCUMENT_ROOT']) ? $_SERVER['DOCUMENT_ROOT'] : 'Unknown'
            ),
            'database' => array(
                'connected' => $this->isDatabaseConnected(),
                'mysql_version' => $this->getMysqlVersion()
            ),
            'sync' => array(
                'folder_writable' => defined('SYNC_FOLDER') ? is_writable(SYNC_FOLDER) : false,
                'folder_exists' => defined('SYNC_FOLDER') ? is_dir(SYNC_FOLDER) : false,
                'folder_path' => defined('SYNC_FOLDER') ? SYNC_FOLDER : 'Not defined'
            ),
            'memcached' => $this->getMemcachedStatus(),
            'maintenance' => array(
                'version' => defined('MAINTENANCE_VERSION') ? MAINTENANCE_VERSION : '2.0.0',
                'last_update' => date('c'),
                'access_level' => $this->getCurrentUser() ? 'Authenticated' : 'Anonymous'
            )
        );
    }

    /**
     * Check if database is connected
     *
     * @return bool
     */
    private function isDatabaseConnected()
    {
        try {
            // Try a simple query
            $result = mysql_query('SELECT 1');
            return $result !== false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get MySQL version
     *
     * @return string
     */
    private function getMysqlVersion()
    {
        try {
            $result = mysql_query('SELECT VERSION()');
            if ($result) {
                $row = mysql_fetch_row($result);
                return $row[0];
            }
            return 'Unknown';
        } catch (Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Get Memcached status
     *
     * @return array
     */
    private function getMemcachedStatus()
    {
        $status = array(
            'available' => false,
            'connected' => false,
            'stats' => array(),
            'error' => null
        );

        try {
            // Check if Memcached extension is available
            if (!class_exists('Memcached')) {
                $status['error'] = 'Extension Memcached non disponible';
                return $status;
            }

            $status['available'] = true;

            // Try to connect to Memcached
            $memcached = new Memcached();

            // Use default server or configured server
            $server = '127.0.0.1';
            $port = 11211;

            // Check if there are global config settings for memcached
            global $config;
            if (isset($config['memcached_host'])) {
                $server = $config['memcached_host'];
            }
            if (isset($config['memcached_port'])) {
                $port = $config['memcached_port'];
            }

            $memcached->addServer($server, $port);

            // Test connection by trying to get stats
            $stats = $memcached->getStats();

            if ($stats && !empty($stats)) {
                $status['connected'] = true;

                // Get the first server's stats
                $serverStats = reset($stats);
                if ($serverStats && is_array($serverStats)) {
                    $status['stats'] = array(
                        'version' => isset($serverStats['version']) ? $serverStats['version'] : 'Unknown',
                        'uptime' => isset($serverStats['uptime']) ? $serverStats['uptime'] : 0,
                        'curr_items' => isset($serverStats['curr_items']) ? $serverStats['curr_items'] : 0,
                        'total_items' => isset($serverStats['total_items']) ? $serverStats['total_items'] : 0,
                        'bytes' => isset($serverStats['bytes']) ? $serverStats['bytes'] : 0,
                        'limit_maxbytes' => isset($serverStats['limit_maxbytes']) ? $serverStats['limit_maxbytes'] : 0,
                        'get_hits' => isset($serverStats['get_hits']) ? $serverStats['get_hits'] : 0,
                        'get_misses' => isset($serverStats['get_misses']) ? $serverStats['get_misses'] : 0,
                        'cmd_get' => isset($serverStats['cmd_get']) ? $serverStats['cmd_get'] : 0,
                        'cmd_set' => isset($serverStats['cmd_set']) ? $serverStats['cmd_set'] : 0
                    );

                    // Calculate hit ratio
                    $totalGets = $status['stats']['get_hits'] + $status['stats']['get_misses'];
                    if ($totalGets > 0) {
                        $status['stats']['hit_ratio'] = round(($status['stats']['get_hits'] / $totalGets) * 100, 2);
                    } else {
                        $status['stats']['hit_ratio'] = 0;
                    }

                    // Calculate memory usage percentage
                    if ($status['stats']['limit_maxbytes'] > 0) {
                        $status['stats']['memory_usage_percent'] = round(($status['stats']['bytes'] / $status['stats']['limit_maxbytes']) * 100, 2);
                    } else {
                        $status['stats']['memory_usage_percent'] = 0;
                    }
                }
            } else {
                $status['error'] = 'Impossible de récupérer les statistiques Memcached';
            }

        } catch (Exception $e) {
            $status['error'] = 'Erreur de connexion Memcached: ' . $e->getMessage();
        }

        return $status;
    }


}
