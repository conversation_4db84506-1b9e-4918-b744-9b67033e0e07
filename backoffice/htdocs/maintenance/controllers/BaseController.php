<?php
/**
 * Base Controller Class
 *
 * Provides common functionality for all controllers
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

abstract class Maintenance_Controllers_BaseController
{
    protected $config;
    protected $request;
    protected $method;

    /**
     * Constructor
     */
    public function __construct()
    {
        // Ensure authentication
        Maintenance_Utils_Authentication::requireAuth();

        // Initialize request data
        $this->initializeRequest();

        // Load configuration
        $this->loadConfiguration();

        // Handle CORS if needed
        Maintenance_Utils_Response::handleOptionsRequest();
    }

    /**
     * Initialize request data
     *
     * @return void
     */
    private function initializeRequest()
    {
        $this->method = isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['REQUEST_METHOD'] : 'GET';

        // Merge all request data
        $this->request = array_merge(
            $_GET,
            $_POST,
            $this->getJsonInput()
        );

        // Sanitize request data
        $this->request = $this->sanitizeRequest($this->request);
    }

    /**
     * Get JSON input from request body
     *
     * @return array
     */
    private function getJsonInput()
    {
        $contentType = isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '';

        if (strpos($contentType, 'application/json') !== false) {
            $input = file_get_contents('php://input');
            $decoded = json_decode($input, true);
            return is_array($decoded) ? $decoded : array();
        }

        return array();
    }

    /**
     * Sanitize request data
     *
     * @param array $data
     * @return array
     */
    private function sanitizeRequest($data)
    {
        $sanitized = array();

        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitizeRequest($value);
            } elseif (is_string($value)) {
                $sanitized[$key] = Maintenance_Utils_Validator::sanitizeString($value);
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * Load configuration
     *
     * @return void
     */
    private function loadConfiguration()
    {
        global $config;
        $this->config = isset($config) ? $config : array();

        // Load configuration variables if function exists
        if (function_exists('cfg_variables_load')) {
            cfg_variables_load($this->config);
        }
    }

    /**
     * Get request parameter
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    protected function getParam($key, $default = null)
    {
        return isset($this->request[$key]) ? $this->request[$key] : $default;
    }

    /**
     * Get integer parameter
     *
     * @param string $key
     * @param int $default
     * @return int
     */
    protected function getIntParam($key, $default = 0)
    {
        $value = $this->getParam($key, $default);
        $sanitized = Maintenance_Utils_Validator::sanitizeInt($value);
        return $sanitized !== null ? $sanitized : $default;
    }

    /**
     * Get boolean parameter
     *
     * @param string $key
     * @param bool $default
     * @return bool
     */
    protected function getBoolParam($key, $default = false)
    {
        $value = $this->getParam($key, $default);
        return filter_var($value, FILTER_VALIDATE_BOOLEAN);
    }

    /**
     * Get string parameter
     *
     * @param string $key
     * @param string $default
     * @return string
     */
    protected function getStringParam($key, $default = '')
    {
        $value = $this->getParam($key, $default);
        return is_string($value) ? $value : $default;
    }

    /**
     * Validate request data
     *
     * @param array $rules
     * @return Maintenance_Utils_Validator
     */
    protected function validate($rules)
    {
        $validator = new Maintenance_Utils_Validator($this->request);

        foreach ($rules as $field => $fieldRules) {
            if (!is_array($fieldRules)) {
                $fieldRules = array($fieldRules);
            }

            foreach ($fieldRules as $rule) {
                if (is_string($rule)) {
                    switch ($rule) {
                        case 'required':
                            $validator->required($field);
                            break;
                        case 'email':
                            $validator->email($field);
                            break;
                        case 'integer':
                            $validator->integer($field);
                            break;
                        case 'positive_integer':
                            $validator->positiveInteger($field);
                            break;
                        case 'boolean':
                            $validator->boolean($field);
                            break;
                        case 'url':
                            $validator->url($field);
                            break;
                    }
                } elseif (is_array($rule)) {
                    $ruleName = isset($rule[0]) ? $rule[0] : '';
                    $ruleParams = array_slice($rule, 1);

                    switch ($ruleName) {
                        case 'min_length':
                            $validator->minLength($field, isset($ruleParams[0]) ? $ruleParams[0] : 1);
                            break;
                        case 'max_length':
                            $validator->maxLength($field, isset($ruleParams[0]) ? $ruleParams[0] : 255);
                            break;
                        case 'in':
                            $validator->in($field, isset($ruleParams[0]) ? $ruleParams[0] : array());
                            break;
                        case 'range':
                            $validator->range($field, isset($ruleParams[0]) ? $ruleParams[0] : 0, isset($ruleParams[1]) ? $ruleParams[1] : 100);
                            break;
                        case 'regex':
                            $validator->regex($field, isset($ruleParams[0]) ? $ruleParams[0] : '');
                            break;
                    }
                }
            }
        }

        return $validator;
    }

    /**
     * Check if request method matches
     *
     * @param string|array $methods
     * @return bool
     */
    protected function isMethod($methods)
    {
        if (is_string($methods)) {
            $methods = array($methods);
        }

        return in_array($this->method, $methods);
    }

    /**
     * Require specific HTTP method
     *
     * @param string|array $methods
     * @return void
     */
    protected function requireMethod($methods)
    {
        if (!$this->isMethod($methods)) {
            $allowedMethods = is_array($methods) ? $methods : array($methods);
            Maintenance_Utils_Response::methodNotAllowed($allowedMethods);
        }
    }

    /**
     * Check CSRF token for POST requests
     *
     * @return void
     */
    protected function checkCsrfToken()
    {
        if ($this->method === 'POST') {
            $token = $this->getParam('csrf_token', '');

            if (!Maintenance_Utils_Authentication::validateCsrfToken($token)) {
                Maintenance_Utils_Response::error('Invalid CSRF token', HTTP_FORBIDDEN);
            }
        }
    }

    /**
     * Get current user information
     *
     * @return array
     */
    protected function getCurrentUser()
    {
        return Maintenance_Utils_Authentication::getCurrentUser();
    }

    /**
     * Check if user is authenticated
     *
     * @return bool
     */
    protected function isAuthenticated()
    {
        return Maintenance_Utils_Authentication::isAuthorized();
    }

    /**
     * Get tenant ID from configuration
     *
     * @return int
     */
    protected function getTenantId()
    {
        return isset($this->config['tnt_id']) ? $this->config['tnt_id'] : 0;
    }

    /**
     * Get website ID from configuration
     *
     * @return int
     */
    protected function getWebsiteId()
    {
        return isset($this->config['wst_id']) ? $this->config['wst_id'] : 0;
    }

    /**
     * Render view
     *
     * @param string $view
     * @param array $data
     * @return void
     */
    protected function render($view, $data = array())
    {
        $viewPath = MAINTENANCE_VIEWS_PATH . '/' . $view . '.php';

        if (!file_exists($viewPath)) {
            Maintenance_Utils_Response::error('View not found: ' . $view, HTTP_NOT_FOUND);
        }

        // Extract data to variables
        extract($data);

        // Include the view (views handle their own header/footer like original system)
        include $viewPath;
    }

    /**
     * Log action
     *
     * @param string $action
     * @param array $data
     * @return void
     */
    protected function logAction($action, $data = array())
    {
        $user = $this->getCurrentUser();
        $logData = array(
            'action' => $action,
            'user_email' => isset($user['email']) ? $user['email'] : 'unknown',
            'tenant_id' => $this->getTenantId(),
            'ip_address' => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown',
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'unknown',
            'timestamp' => date('c'),
            'data' => $data
        );

        error_log('Maintenance Action: ' . json_encode($logData));
    }

    /**
     * Handle exceptions
     *
     * @param Exception $e
     * @return void
     */
    protected function handleException($e)
    {
        error_log('Controller Exception: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());

        if (Maintenance_Utils_Authentication::isAjaxRequest()) {
            Maintenance_Utils_Response::serverError('An unexpected error occurred');
        } else {
            $this->render('error', array(
                'message' => 'An unexpected error occurred',
                'debug' => $e->getMessage()
            ));
        }
    }
}
