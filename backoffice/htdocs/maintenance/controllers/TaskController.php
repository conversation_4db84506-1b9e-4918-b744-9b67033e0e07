<?php
/**
 * Task Controller Class
 *
 * Handles task management operations
 *
 * @package Maintenance\Controllers
 * <AUTHOR> Team
 */

class Maintenance_Controllers_TaskController extends Maintenance_Controllers_BaseController
{
    private $taskModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        // Instantiate the model with its correct global name
        $this->taskModel = new Maintenance_Models_TaskModel($this->getTenantId());
    }

    /**
     * Display tasks management page
     *
     * @return void
     */
    public function index() 
    {
        try {
            // Get all tasks organized by categories
            $tasks = $this->taskModel->getAllTasksOrganized();

            // Set page title and breadcrumbs
            $pageTitle = _('Gestion des tâches de synchronisation') . ' - ' . _('Maintenance');
            $this->setBreadcrumbs();

            // Render tasks view
            $this->render('tasks/index', array(
                'pageTitle' => $pageTitle,
                'tasks' => $tasks,
                'tenantId' => $this->getTenantId(),
                'csrfToken' => Maintenance_Utils_Authentication::generateCsrfToken()
            ));

        } catch (Exception $e) { 
            $this->handleException($e);
        }
    }

    /**
     * Get tasks data for AJAX requests
     *
     * @return void
     */
    public function getTasks() 
    {
        try {
            $this->requireMethod('GET');

            $tasks = $this->taskModel->getAllTasksOrganized();

            Maintenance_Utils_Response::success($tasks); 

        } catch (Exception $e) { 
            Maintenance_Utils_Response::serverError('Failed to get tasks'); 
        }
    }

    /**
     * Activate/deactivate tasks
     *
     * @return void
     */
    public function activateTasks() 
    {
        try {
            $this->requireMethod('POST');
            $this->checkCsrfToken();

            // Validate input
            $validator = $this->validate(array(
                'task_ids' => 'required',
                'action' => array('required', array('in', array('start', 'stop'))) 
            ));

            if ($validator->fails()) {
                Maintenance_Utils_Response::validationError($validator->getErrors()); 
            }

            $taskIds = $validator->get('task_ids');
            $action = $validator->get('action');

            // Convert task_ids to array if it's a string
            if (is_string($taskIds)) {
                $taskIds = explode(',', $taskIds);
            }

            // Filter and convert to integers
            $taskIds = array_filter(array_map('intval', $taskIds));

            if (empty($taskIds)) {
                Maintenance_Utils_Response::error('No valid task IDs provided'); 
            }

            // Determine which tasks to activate
            $tasksToActivate = array(); 
            if ($action === 'start') {
                $tasksToActivate = $taskIds;
            }
            // For 'stop' action, we pass an empty array to deactivate all

            $success = $this->taskModel->activateTasks($tasksToActivate);

            if ($success) {
                $this->logAction('tasks_activated', array( 
                    'action' => $action,
                    'task_ids' => $taskIds
                ));

                Maintenance_Utils_Response::success(null, 'Tasks updated successfully'); 
            } else {
                Maintenance_Utils_Response::serverError('Failed to update tasks'); 
            }

        } catch (Exception $e) { 
            Maintenance_Utils_Response::serverError('Failed to update tasks'); 
        }
    }

    /**
     * Toggle single task status
     *
     * @return void
     */
    public function toggleTask() 
    {
        try {
            $this->requireMethod('POST');
            $this->checkCsrfToken();

            // Validate input
            $validator = $this->validate(array( 
                'task_id' => array('required', 'positive_integer'), 
                'active' => array('required', 'boolean') 
            ));

            if ($validator->fails()) {
                Maintenance_Utils_Response::validationError($validator->getErrors()); 
            }

            $taskId = $validator->get('task_id');
            $active = $validator->get('active');

            // Get currently active tasks
            $activeTasks = $this->taskModel->getActiveTasks();
            $currentActiveIds = array_map(function($task) {
                return isset($task['tsk_id']) ? $task['tsk_id'] : (isset($task['task_id']) ? $task['task_id'] : null); // Make sure to use isset
            }, $activeTasks);

            // Update the list based on the toggle
            if ($active) {
                // Add task to active list if not already there
                if (!in_array($taskId, $currentActiveIds)) {
                    $currentActiveIds[] = $taskId;
                }
            } else {
                // Remove task from active list
                $currentActiveIds = array_filter($currentActiveIds, function($id) use ($taskId) {
                    return $id != $taskId;
                });
            }

            $success = $this->taskModel->activateTasks($currentActiveIds);

            if ($success) {
                $this->logAction('task_toggled', array( 
                    'task_id' => $taskId,
                    'active' => $active
                ));

                Maintenance_Utils_Response::success(array( 
                    'task_id' => $taskId,
                    'active' => $active
                ), 'Task status updated successfully');
            } else {
                Maintenance_Utils_Response::serverError('Failed to update task status'); 
            }

        } catch (Exception $e) { 
            Maintenance_Utils_Response::serverError('Failed to update task status'); 
        }
    }

    /**
     * Force task execution
     *
     * @return void
     */
    public function forceTask() 
    {
        try {
            $this->requireMethod('POST');
            $this->checkCsrfToken();

            // Validate input
            $validator = $this->validate(array( 
                'task_id' => array('required', 'positive_integer') 
            ));

            if ($validator->fails()) {
                Maintenance_Utils_Response::validationError($validator->getErrors()); 
            }

            $taskId = $validator->get('task_id');

            // Check if task is active
            if (!$this->taskModel->isTaskActive($taskId)) {
                Maintenance_Utils_Response::error('Cannot force inactive task', HTTP_BAD_REQUEST); 
            }

            $success = $this->taskModel->forceTaskExecution($taskId);

            if ($success) {
                $this->logAction('task_forced', array('task_id' => $taskId)); 
                Maintenance_Utils_Response::success(null, 'Task execution forced successfully'); 
            } else {
                Maintenance_Utils_Response::serverError('Failed to force task execution'); 
            }

        } catch (Exception $e) { 
            Maintenance_Utils_Response::serverError('Failed to force task execution'); 
        }
    }

    /**
     * Get task status
     *
     * @return void
     */
    public function getTaskStatus() 
    {
        try {
            $this->requireMethod('GET');

            $taskId = $this->getIntParam('task_id');

            if (!$taskId) {
                Maintenance_Utils_Response::error('Task ID is required'); 
            }

            $activation = $this->taskModel->getTaskActivation($taskId);
            $activity = $this->taskModel->getTaskActivity($taskId);

            $status = array( 
                'task_id' => $taskId,
                'active' => $activation ? (isset($activation['is_active']) ? $activation['is_active'] : 0) == 1 : false, // Make sure to use isset
                'activity' => $activity ? array( 
                    'count_total' => isset($activity['count_total']) ? $activity['count_total'] : 0,
                    'count_remaining' => isset($activity['count_remaining']) ? $activity['count_remaining'] : 0,
                    'count_fail' => isset($activity['count_fail']) ? $activity['count_fail'] : 0,
                    'date_start' => isset($activity['date_start']) ? $activity['date_start'] : null,
                    'date_end' => isset($activity['date_end']) ? $activity['date_end'] : null
                ) : null
            );

            Maintenance_Utils_Response::success($status); 

        } catch (Exception $e) { 
            Maintenance_Utils_Response::serverError('Failed to get task status'); 
        }
    }

    /**
     * Get active tasks
     *
     * @return void
     */
    public function getActiveTasks() 
    {
        try {
            $this->requireMethod('GET');

            $activeTasks = $this->taskModel->getActiveTasks();

            Maintenance_Utils_Response::success($activeTasks); 

        } catch (Exception $e) { 
            Maintenance_Utils_Response::serverError('Failed to get active tasks'); 
        }
    }

    /**
     * Create custom configuration
     *
     * @return void
     */
    public function createCustomConfig() 
    {
        try {
            $this->requireMethod('POST');
            $this->checkCsrfToken();

            // Validate input
            $validator = $this->validate(array( 
                'params' => 'required'
            ));

            if ($validator->fails()) {
                Maintenance_Utils_Response::validationError($validator->getErrors()); 
            }

            $params = $validator->get('params');

            if (!is_array($params)) {
                Maintenance_Utils_Response::error('Parameters must be an array'); 
            }

            $success = $this->taskModel->createCustomConfigFile($params);

            if ($success) {
                $this->logAction('custom_config_created', array('params' => $params)); 
                Maintenance_Utils_Response::success(null, 'Custom configuration created successfully'); 
            } else {
                Maintenance_Utils_Response::serverError('Failed to create custom configuration'); 
            }

        } catch (Exception $e) {
            Maintenance_Utils_Response::serverError('Failed to create custom configuration'); 
        }
    }

    /**
     * Verify task assignment
     *
     * @return void
     */
    public function verifyTaskAssignment() 
    {
        try {
            $this->requireMethod('GET');

            $assignmentFiles = $this->taskModel->verifyTaskAssignment();

            Maintenance_Utils_Response::success(array(
                'assignment_files' => $assignmentFiles,
                'count' => count($assignmentFiles)
            ));

        } catch (Exception $e) {
            Maintenance_Utils_Response::serverError('Failed to verify task assignment');
        }
    }

    /**
     * Set breadcrumbs for the page
     *
     * @return void
     */
    private function setBreadcrumbs()
    {
        if (class_exists('Breadcrumbs')) {
            Breadcrumbs::root(_('Accueil'), '/admin/index.php')
                ->push(_('Page de maintenance'), '/admin/maintenance/maintenance.php')
                ->push(_('Gestion des tâches de synchronisation'));
        }
    }

    /**
     * Export tasks data
     *
     * @return void
     */
    public function exportTasks()
    {
        try {
            $this->requireMethod('GET');

            $tasks = $this->taskModel->getAllTasksOrganized();
            $activeTasks = $this->taskModel->getActiveTasks();

            $exportData = array(
                'export_date' => date('c'),
                'tenant_id' => $this->getTenantId(),
                'tasks' => $tasks,
                'active_tasks' => $activeTasks
            );

            $filename = 'tasks_data_' . $this->getTenantId() . '_' . date('Y-m-d_H-i-s') . '.json';
            
            if (defined('JSON_PRETTY_PRINT')) {
                $jsonData = json_encode($exportData, JSON_PRETTY_PRINT);
            } else {
                $jsonData = json_encode($exportData);
            }


            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . strlen($jsonData));

            echo $jsonData;
            exit;

        } catch (Exception $e) {
            Maintenance_Utils_Response::serverError('Failed to export tasks data');
        }
    }
}