<?php
/**
 * Configuration Controller
 *
 * Handles configuration variables management
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Controllers_ConfigurationController extends Maintenance_Controllers_BaseController
{
    private $configModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        
        // Initialize configuration model
        require_once('models/ConfigurationModel.php');
        $this->configModel = new Maintenance_Models_ConfigurationModel();
    }

    /**
     * Display configuration variables page
     *
     * @return void
     */
    public function index()
    {
        try {
            // Get parameters from request
            $tenantId = $this->getParam('tnt_id', 0);
            $websiteId = $this->getParam('wst_id', '');
            $onlyOverride = $this->getParam('only_override', '') === 'on';
            $filter = $this->getParam('filter', '');

            // Validate tenant ID
            if (!$tenantId || !is_numeric($tenantId)) {
                $tenantId = $this->getTenantId();
            }

            // Get tenant information
            $tenant = $this->configModel->getTenant($tenantId);
            if (!$tenant) {
                Maintenance_Utils_Response::error('Tenant not found', 404);
                return;
            }

            // Get websites for this tenant
            $websites = $this->configModel->getWebsites($tenantId);
            
            // Get sync information
            $syncInfo = $this->configModel->getSyncInfo($tenantId);
            
            // Get configuration variables if tenant and website are selected
            $variables = array();
            $website = false;
            
            if ($tenantId && $websiteId) {
                // Get website information
                if (is_numeric($websiteId)) {
                    $website = $this->configModel->getWebsite($websiteId);
                } elseif (in_array($websiteId, array('sync', 'yuto'))) {
                    $website = array('id' => $websiteId, 'name' => $websiteId);
                }
                
                // Get variables for this tenant/website combination
                $variables = $this->configModel->getConfigurationVariables(
                    $tenantId, 
                    $websiteId, 
                    $onlyOverride, 
                    $filter,
                    $syncInfo
                );
            }

            // Build parameter string for forms
            $paramEdit = '';
            if ($tenantId) {
                $paramEdit .= '?tnt_id=' . $tenantId;
            }
            if ($websiteId) {
                $paramEdit .= ($paramEdit ? '&' : '?') . 'wst_id=' . $websiteId;
            }
            if ($onlyOverride) {
                $paramEdit .= ($paramEdit ? '&' : '?') . 'only_override=on';
            }
            if ($filter) {
                $paramEdit .= ($paramEdit ? '&' : '?') . 'filter=' . urlencode($filter);
            }

            // Set page title and breadcrumbs
            $pageTitle = 'Variables de configuration - Configuration';
            $this->setBreadcrumbs();

            // Check for success message
            $successMessage = '';
            if (isset($_SESSION['save-variables-success'])) {
                $successMessage = 'Les variables ont bien été mise à jour.';
                unset($_SESSION['save-variables-success']);
            }

            // Render configuration view
            $this->render('configuration/index', array(
                'pageTitle' => $pageTitle,
                'tenant' => $tenant,
                'tenantId' => $tenantId,
                'websites' => $websites,
                'websiteId' => $websiteId,
                'website' => $website,
                'variables' => $variables,
                'onlyOverride' => $onlyOverride,
                'filter' => $filter,
                'paramEdit' => $paramEdit,
                'syncInfo' => $syncInfo,
                'successMessage' => $successMessage,
                'csrfToken' => Maintenance_Utils_Authentication::generateCsrfToken()
            ));

        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * Save configuration variables
     *
     * @return void
     */
    public function save()
    {
        try {
            // Require POST method
            if ($this->method !== 'POST') {
                Maintenance_Utils_Response::error('Method not allowed', 405);
                return;
            }

            // Get parameters
            $tenantId = $this->getParam('tnt_id', 0);
            $websiteId = $this->getParam('wst_id', '');
            $overrides = $this->getParam('override', array());

            // Validate parameters
            if (!$tenantId || !is_numeric($tenantId)) {
                Maintenance_Utils_Response::error('Invalid tenant ID', 400);
                return;
            }

            if (!$websiteId) {
                Maintenance_Utils_Response::error('Website ID is required', 400);
                return;
            }

            if (!is_array($overrides)) {
                Maintenance_Utils_Response::error('Invalid override data', 400);
                return;
            }

            // Save configuration variables
            $result = $this->configModel->saveConfigurationVariables($tenantId, $websiteId, $overrides);

            if ($result['success']) {
                // Clear cache
                $this->configModel->clearConfigCache($tenantId, $websiteId);
                
                // Set success message
                $_SESSION['save-variables-success'] = true;
                
                // Build redirect URL
                $redirectUrl = '/maintenance/?controller=configuration';
                if ($tenantId) {
                    $redirectUrl .= '&tnt_id=' . $tenantId;
                }
                if ($websiteId) {
                    $redirectUrl .= '&wst_id=' . $websiteId;
                }
                
                // Add other parameters if they exist
                if ($this->getParam('only_override') === 'on') {
                    $redirectUrl .= '&only_override=on';
                }
                if ($this->getParam('filter')) {
                    $redirectUrl .= '&filter=' . urlencode($this->getParam('filter'));
                }

                Maintenance_Utils_Response::redirect($redirectUrl);
            } else {
                Maintenance_Utils_Response::error($result['error'], 500);
            }

        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * Set breadcrumbs for configuration pages
     *
     * @return void
     */
    private function setBreadcrumbs()
    {
        // This would set breadcrumbs if the system supports it
        // For now, we'll just set a simple page indicator
    }
}
