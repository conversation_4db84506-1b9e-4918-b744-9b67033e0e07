<?php
/**
 * Logs Controller Class - Refactored
 *
 * Handles log file viewing and management for both maintenance and system logs
 * Simplified interface focusing on primary use case: viewing update logs
 *
 * @package Maintenance
 * <AUTHOR> Team
 */

class Maintenance_Controllers_LogsController extends Maintenance_Controllers_BaseController
{
    private $logsModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->logsModel = new Maintenance_Models_LogsModel($this->config);
    }

    /**
     * Main logs listing page - Simplified interface
     *
     * @return void
     */
    public function index()
    {
        try {
            // Get logs data from both sources
            $maintenanceLogs = $this->logsModel->getMaintenanceLogs();
            $systemLogs = $this->logsModel->getRelevantSystemLogs();

            // Set page title
            $pageTitle = 'Visualiseur de logs - Maintenance';

            // Render logs view
            $this->render('logs/index', array(
                'pageTitle' => $pageTitle,
                'maintenanceLogs' => $maintenanceLogs,
                'systemLogs' => $systemLogs,
                'tenantId' => $this->getTenantId(),
                'csrfToken' => Maintenance_Utils_Authentication::generateCsrfToken()
            ));

        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * View individual log file - Simplified
     *
     * @return void
     */
    public function view()
    {
        try {
            $filename = $this->getStringParam('file', '');
            $source = $this->getStringParam('source', 'maintenance'); // 'maintenance' or 'system'

            if (empty($filename)) {
                Maintenance_Utils_Response::error('Filename is required', HTTP_BAD_REQUEST);
            }

            // Get log file content based on source
            if ($source === 'system') {
                $logData = $this->logsModel->getSystemLogContent($filename);
            } else {
                $logData = $this->logsModel->getMaintenanceLogContent($filename);
            }

            if (!$logData) {
                Maintenance_Utils_Response::error('Log file not found', HTTP_NOT_FOUND);
            }

            // Set page title
            $pageTitle = 'Visualisation du log: ' . htmlspecialchars($filename);

            // Render log view
            $this->render('logs/view', array(
                'pageTitle' => $pageTitle,
                'filename' => $filename,
                'source' => $source,
                'logData' => $logData,
                'tenantId' => $this->getTenantId(),
                'csrfToken' => Maintenance_Utils_Authentication::generateCsrfToken()
            ));

        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * Download log file - Simplified
     *
     * @return void
     */
    public function download()
    {
        try {
            $this->requireMethod('GET');
            $filename = $this->getStringParam('file', '');
            $source = $this->getStringParam('source', 'maintenance');

            if (empty($filename)) {
                Maintenance_Utils_Response::error('Filename is required', HTTP_BAD_REQUEST);
            }

            // Get file path based on source
            if ($source === 'system') {
                $filePath = $this->logsModel->getSystemLogFilePath($filename);
            } else {
                $filePath = $this->logsModel->getMaintenanceLogFilePath($filename);
            }

            if (!$filePath || !file_exists($filePath)) {
                Maintenance_Utils_Response::error('Log file not found', HTTP_NOT_FOUND);
            }

            // Set download headers
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
            header('Content-Length: ' . filesize($filePath));
            header('Cache-Control: no-cache, must-revalidate');
            header('Pragma: no-cache');

            // Output file content
            readfile($filePath);
            exit;

        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * Delete single maintenance log file - Fixed CSRF handling
     *
     * @return void
     */
    public function delete()
    {
        try {
            $this->requireMethod('POST');
            $this->checkCsrfToken();

            $filename = $this->getStringParam('file', '');
            $source = $this->getStringParam('source', 'maintenance');

            if (empty($filename)) {
                Maintenance_Utils_Response::error('Filename is required', HTTP_BAD_REQUEST);
            }

            // Only allow deletion of maintenance logs, not system logs
            if ($source !== 'maintenance') {
                Maintenance_Utils_Response::error('System logs cannot be deleted', HTTP_FORBIDDEN);
            }

            $success = $this->logsModel->deleteMaintenanceLogFile($filename);

            if ($success) {
                $this->logAction('maintenance_log_deleted', array('filename' => $filename));

                // Generate new CSRF token for next operation
                $newToken = Maintenance_Utils_Authentication::generateCsrfToken();

                Maintenance_Utils_Response::success(array(
                    'message' => 'Log file deleted successfully',
                    'new_csrf_token' => $newToken
                ));
            } else {
                Maintenance_Utils_Response::error('Failed to delete log file', HTTP_INTERNAL_SERVER_ERROR);
            }

        } catch (Exception $e) {
            Maintenance_Utils_Response::serverError('Failed to delete log file');
        }
    }

    /**
     * Refresh logs data (AJAX endpoint)
     *
     * @return void
     */
    public function refresh()
    {
        try {
            $this->requireMethod('GET');

            // Get fresh logs data from both sources
            $maintenanceLogs = $this->logsModel->getMaintenanceLogs();
            $systemLogs = $this->logsModel->getRelevantSystemLogs();

            Maintenance_Utils_Response::success(array(
                'maintenanceLogs' => $maintenanceLogs,
                'systemLogs' => $systemLogs,
                'timestamp' => date('Y-m-d H:i:s')
            ));

        } catch (Exception $e) {
            Maintenance_Utils_Response::serverError('Failed to refresh logs');
        }
    }
}
