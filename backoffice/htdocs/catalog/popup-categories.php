<?php

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_CATEG');

	require_once('categories.inc.php');

	// L'argument $_GET['cat'] permet de présélectionner une catégorie dans la liste
	if( !isset($_GET['cat']) || !prd_categories_exists($_GET['cat']) ){
		$_GET['cat'] = 0;
	}

	// Si aucune catégorie n'est passée en argument, l'affichage débutera à la racine du catalogue
	if( !isset($_GET['parent']) || !prd_categories_exists($_GET['parent']) ){
		$_GET['parent'] = 0;
	}else{
		$cat = ria_mysql_fetch_array(prd_categories_get($_GET['parent']));
	}

	// Ajout d'une sous catégorie
	if( isset($_POST['catname']) && trim($_POST['catname']) ){
		if( strstr($_POST['catname'],'"') ){
			$error = _("Les guillemets ne sont pas autorisés dans les noms de catégories.")."\n"._("Veuillez ne pas en utiliser.");
		}else{
			prd_categories_add( $_POST['catname'], '', '', $_GET['parent'] );
		}
	}

	// Charge la liste des catégories enfants
	$categories = prd_categories_get(0,false,$_GET['parent']);

	$add_field = isset($_GET['add_field']) && $_GET['add_field'] ? true : false;

	// nom des champs input qui recevront les valeurs en retour
	$url_other_args = '';
	if( isset($_GET['input_id_cat_id']) && $_GET['input_id_cat_id']!='' ){
		$url_other_args .= '&input_id_cat_id='.urlencode($_GET['input_id_cat_id']);
	}
	if( isset($_GET['input_id_name']) && $_GET['input_id_name']!='' ){
		$url_other_args .= '&input_id_name='.urlencode($_GET['input_id_name']);
	}
	if( isset($_GET['add_field']) && $_GET['add_field']!='' ){
		$url_other_args .= '&add_field='.urlencode($_GET['add_field']);
	}
	if( isset($_GET['goto']) && $_GET['goto']!='' ){
		$url_other_args .= '&goto='.urlencode($_GET['goto']);
	}
	if( isset($_GET['no-add']) && $_GET['no-add']!='' ){
		$url_other_args .= '&no-add='.urlencode($_GET['no-add']);
	}

	define('ADMIN_PAGE_TITLE', _('Choisir une catégorie'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'popup-iframe');
	require_once('admin/skin/header.inc.php');

	// Affichage des messages d'erreur
	if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}

?>
<form action="" method="post" id="form-popup-choosecat">
	<input type="hidden" name="cat" value="" />
	<div class="top-table">
		<div class="margin-bottom-10 back-btn"><?php

					if( $_GET['parent']!=0 && is_numeric($_GET['parent']) ){
						$name = '';
						$last = false;
						$parents = prd_categories_parents_get($_GET['parent']);
						while( $p = ria_mysql_fetch_array($parents) ){
							$name .= '<a href="popup-categories.php?parent='.$p['id'].$url_other_args.'">'.htmlspecialchars($p['title']).'</a> &raquo; ';
							$last = $p;
						}
						print '<a href="popup-categories.php?parent='.$last['id'].$url_other_args.'" class="float-left">
						<input type="button" name="filter" id="filter" value="'._('Retour').'" />
						</a>';
					} ?>
		</div>

		<div class="search-cat pop-form-search">
			<label for="filter"><?php print _('Rechercher une catégorie :')?></label>
			<input type="text" value="" id="filter" name="filter" class="ac_input" />
			<input type="submit" class="btn-action" name="search" value="Rechercher">
		</div>
	</div>
	<table class="tb-popup-choose-cat checklist">
		<caption><?php
			if( $_GET['parent']==0 ){
				print _('Catégories');
			}else{
				print $name.htmlspecialchars($cat['title']);
			}
		?></caption>
		<thead>
			<tr>
				<th class="col20px"></th>
				<th class="col20px"></th>
				<th id="cat-name"><?php print _('Désignation')?></th>
				<th id="cat-publish" class="col100px"><?php print _('Publiée ?')?></th>
				<th id="cat-products" class="col180px align-right"><?php print _('Produits')?></th>
			</tr>
		</thead>
		<tbody>
		<?php
			if( isset($_POST['filter']) && trim($_POST['filter'])!='' ){
				// Affiche le résultat d'une recherche de catégorie de produit
				$rcat = search3( 1, $_POST['filter'], 1, 50, false, false, 6, array('prd-cat') );
				if( $rcat && ria_mysql_num_rows($rcat) ){
					$unique = array();
					while( $cat = ria_mysql_fetch_array($rcat) ){
						if( in_array($cat['tag'], $unique) ){
							continue;
						}

						$rc = prd_categories_get( $cat['tag'] );
						if( $rc && ria_mysql_num_rows($rc) ){
							$c = ria_mysql_fetch_array( $rc );

							$parents = prd_categories_parents_get( $c['id'] );
							$hierarchy = '';
							if( $parents && ria_mysql_num_rows($parents) ){
								while( $parent = ria_mysql_fetch_array($parents) ){
									$hierarchy .= ( trim($hierarchy)!='' ? ' >> ' : '' ).$parent['title'];
								}
							}

							print '
								<tr>
									<td>
										<input type="radio" class="radio" name="cat" value="'.$c['id'].'" id="cat-'.$c['id'].'" />
									</td>
									<td>
										'.view_cat_is_sync( $c ).'
									</td>
									<td headers="cat-name">
										<label for="cat-'.$c['id'].'">'.( trim($hierarchy)!='' ? $hierarchy.' &gt;&gt; ' : '' ).htmlspecialchars($c['title']).'</label>
									</td>
									<td headers="cat-publish">'.( $c['publish'] ? _('Oui') : _('Non') ).'</td>
									<td headers="cat-products" align="right">'.number_format($c['products'],0,',',' ').'</td>
								</tr>
							';

							$unique[] = $c['id'];
						}
					}
				}else{
					print '
						<tr>
							<td colspan="5">'._('Aucune catégorie ne correspond à votre recherche.').'</td>
						</tr>
					';
				}
			}elseif( ria_mysql_num_rows($categories)>0 ){
				// Affiche la liste des catégories enfant
				ria_mysql_data_seek($categories,0);
				while( $r = ria_mysql_fetch_array($categories) ){
					print '
						<tr>
							<td>
								<input type="radio" class="radio" name="cat" value="'.$r['id'].'" id="cat-'.$r['id'].'"
									'.( $_GET['cat'] == $r['id'] ? 'checked="checked"' : '' ).' />
							</td>
							<td>
								'.view_cat_is_sync( $r ).'
							</td>
							<td headers="cat-name">
					';

					// Si la catégorie dispose d'enfants, affiche un lien de navigation
					if( prd_categories_have_childs($r['id']) ){
						print '
								<a href="popup-categories.php?parent='.$r['id'].$url_other_args.'">'.htmlspecialchars($r['title']).'</a>
						';
					}else{ // Sinon, affiche simplement son nom
						print '
							<label for="cat-'.$r['id'].'">'.htmlspecialchars($r['title']).'</label>
						';
					}

					print '
							</td>
							<td headers="cat-publish">'.( $r['publish'] ? _('Oui') : _('Non') ).'</td>
							<td headers="cat-products" align="right">'.number_format($r['products'],0,',',' ').'</td>
						</tr>
					';
				}

				if( $_GET['parent']>0 ){
					print '
						<tr>
							<td headers="cat-name">
								<input type="radio" class="radio" name="cat" id="cat-'.$cat['id'].'" value="'.$cat['id'].'" '.($_GET['cat']==$cat['id']?'checked="checked"':'').' />
							</td>
							<td></td>
							<td>
								<label for="cat-'.$cat['id'].'">'._('Sélectionner cette catégorie').'</label>
							</td>
							<td headers="cat-publish">'.( $cat['publish'] ? _('Oui') : _('Non') ).'</td>
							<td headers="cat-products" align="right">'.number_format($cat['products'],0,',',' ').'</td>
						</tr>
					';
				}
			}
		?>
		</tbody>

		<tfoot class="tfoot-cat">
			<tr>
				<td colspan="5">
					<div>
						<div class="btn-wrapper">
							<input class="btn-main" type="button" value="<?php print _('Choisir')?>" onclick="cat_choose(this.form, <?php print $add_field ? 'true' : 'false'; ?>)" />
							<input class="btn-cancel" type="button" value="<?php print _('Annuler')?>" onclick="cat_cancel()" />
						</div>
						<?php if( !isset($_POST['filter']) && (!isset($_GET['no-add']) || !$_GET['no-add']) ){ // Le formulaire d'ajout n'est pas disponible sur les résultats de recherche ?>
						<div>
							<label for="catname"><?php print _('Ajouter une sous-catégorie :'); ?></label>
							<input type="text" name="catname" id="catname" maxlength="75" />
							<input type="submit" value="<?php print _('Ajouter')?>" />
						</div>
						<?php } ?>
					</div>
				</td>
			</tr>
		</tfoot>
	</table>
	<input type="hidden" name="lastCat" id="lastCat" value="<?php print isset($_GET['parent']) ? $_GET['parent'] : '' ; ?>" />
	<input type="hidden" name="cat" value="" />
</form>

<div class="ajax-load-iframe"></div>

<script>
	$(document).ready(function(){
		$('#form-popup-choosecat').submit(function(){
			$('.ajax-load-iframe').show();
		});

		$('#form-popup-choosecat a').click(function(){
			$('.ajax-load-iframe').show();
		});
	});

	function cat_choose(frm, inField){
		var id = 0;
		for( var i=0; i<frm.cat.length; i++ )
		if( frm.cat[i].checked ){
			id = frm.cat[i].value;
			break;
		}
		if( !id ){
			alert("<?php print _('Veuillez sélectionner la catégorie choisie.')?>");
			return false;
		}

		<?php
			if( isset($_GET['goto']) ){
				$goto = urldecode( $_GET['goto'] );
				if( strstr($goto, '?') ){
					$goto .= '&';
				}else{
					$goto .= '?';
				}

				print '$(".load-ajax-opacity").show();'
				.'window.location.href = "'.$goto.'cat=" + id;'
				.'return false;';
			}
		?>

		var catname = '';
		if( getHtml ){
			catname = $('#popup-content').find('input:checked').parent().find('label').html().replace(/&gt;/g, '>');
		}else if( parent_name ){
			if( categories[id]!='' ){
				catname = parent_name + " >> " + categories[id];
			}else{
				catname = parent_name;
			}
		}else{
			catname = categories[id];
		}

		<?php
		$return_url_args = '';
		if( isset($_GET['input_id_cat_id']) && $_GET['input_id_cat_id']!='' ){
			$return_url_args .= ', \''.$_GET['input_id_cat_id'].'\'';
		}else{
			$return_url_args .= ', \'\'';
		}
		if( isset($_GET['input_id_name']) && $_GET['input_id_name']!='' ){
			$return_url_args .= ', \''.$_GET['input_id_name'].'\'';
		}else{
			$return_url_args .= ', \'\'';
		}
		?>

		if( !inField ){
			parent.updateCat(id,<?php print $_GET['parent']; ?>,catname<?php print $return_url_args; ?>);
		}else{
			parent.addObjectInField(id, catname<?php print $return_url_args; ?>);
		}
		window.parent.hidePopup();
	}

	var categories = new Array();
	<?php
		if( isset($_POST['filter']) ){
			print 'var getHtml = true;';
		}else{
			print 'var getHtml = false;';
		}

		if( $categories && ria_mysql_num_rows($categories) ){
			ria_mysql_data_seek( $categories, 0 );
			while( $r = ria_mysql_fetch_array($categories) ){
				print 'categories['.$r['id'].'] = "'.str_replace('"','\"',$r['title']).'";'."\n";
			}
		}

		if( $_GET['parent']>0 ){
			print 'categories['.$cat['id'].'] = "";'."\n";
			$p_array = array();
			$parents = prd_categories_parents_get($_GET['parent']);
			while( $p = ria_mysql_fetch_array($parents) ){
				$p_array[] = htmlspecialchars($p['title']);
			}
			$p_array[] = htmlspecialchars($cat['title']);
			print 'var parent_name = "'.str_replace('"','\"',implode(' >> ',$p_array)).'";'."\n";
		}else{
			print 'var parent_name = "";'."\n";
		}
	?>

	function cat_cancel(){
		window.parent.hidePopup();
	}
</script>
<?php
	require_once('admin/skin/footer.inc.php');