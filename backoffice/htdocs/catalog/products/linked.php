<?php
	/**	\file linked.php
	 *	Ce fichier est utilisé en include par le fichier catalog/product.php. Il génère le code HTML
	 *	permettant d'afficher la liste des articles liés.
	 */

    // Vérifie que l'utilisateur en cours à bien le droit d'éditer les fiches produits
    gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_EDIT');

    if (isset($_GET['prd_add']) && is_array($_GET['prd_add']) && sizeof($_GET['prd_add'])){
        foreach($_GET['prd_add'] as $prd_id){
            switch($_GET['relation_type']) {
                case 'prd-relations': {
                    if( !isset($_GET['prd'], $_GET['type']) ) {
                        $error = _("Il manque des paramètres");
                        break;
                    }

                    $rel_product = false;

                    $cats = prd_products_categories_get_array( $_GET['prd'] );
                    if( is_array($cats) && sizeof($cats) ){
                        $cat = $cats[0];
                        $parents = prd_categories_parents_get_array( $cat );
                        if( is_array($parents) && sizeof($parents) ){
                            $cat = $parents[0];
                        }

                        $rel_product = prd_products_get_simple( $prd_id, '', false, $cat, true );
                    }

                    if( !$rel_product || !ria_mysql_num_rows($rel_product) ){
                        $rel_product = prd_products_get_simple( $prd_id );
                    }

                    if( !ria_mysql_num_rows($rel_product) ){
                        $error = sprintf(_('Le produit %s n\'a pas été trouvée.'), $prd_id);
                    }else{
                        $rel = ria_mysql_fetch_assoc( $rel_product );
                        if( $_GET['prd']==$rel['id'] ){
                            $error = _("Un produit ne peut pas être lié à lui même.");
                        } elseif ( !prd_relations_add($_GET['prd'], $rel['id'], $_GET['type']) ){
                            $error = _("Une erreur est survenue lors de l'ajout de la relation");
                        }
                    }
                    break;
                }
                case 'prd-parents': {
                    if( !isset( $_GET['prd']) ) $error = _("Il manque des paramètres");
                    $parent = false;

                    $cats = prd_products_categories_get_array( $_GET['prd'] );
                    if( is_array($cats) && sizeof($cats) ){
                        $cat = $cats[0];
                        $parents = prd_categories_parents_get_array( $cat );
                        if( is_array($parents) && sizeof($parents) ){
                            $cat = $parents[0];
                        }

                        $parent = prd_products_get_simple( $prd_id, '', false, $cat, true );
                    }

                    if( !$parent || !ria_mysql_num_rows($parent) ){
                        $parent = prd_products_get_simple( $prd_id );
                    }

                    if( !ria_mysql_num_rows($parent) ){
                        $error = sprintf(_('Le produit %s n\'a pas été trouvée.'), $prd_id);
                    }else{
                        $p = ria_mysql_fetch_assoc($parent);
                        if( $p['id']==$_GET['prd'] ){
                            $error = _("Le produit ne peut pas être son propre enfant.");
                        }elseif( !prd_hierarchy_add( $p['id'], $_GET['prd'] ) ){
                            $error = sprintf(_('L\'ajout de la référence %s a échoué pour une raison inconnue.'), $p['ref']);
                        }
                    }
                    break;
                }
                case 'prd-childs': {
                    if( !isset($_GET['prd']) ) $error = _("Il manque des paramètres");
                    $child = false;

                    $cats = prd_products_categories_get_array( $_GET['prd'] );
                    if( is_array($cats) && sizeof($cats) ){
                        $cat = $cats[0];
                        $parents = prd_categories_parents_get_array( $cat );
                        if( is_array($parents) && sizeof($parents) ){
                            $cat = $parents[0];
                        }

                        $child = prd_products_get_simple( $prd_id, '', false, $cat, true );
                    }

                    if( !$child || !ria_mysql_num_rows($child) ){
                        $child = prd_products_get_simple( $prd_id );
                    }


                    if( !ria_mysql_num_rows($child) ){
                        $error = sprintf(_('Le produit %s n\'a pas été trouvée.'), $prd_id);
                    }else{
                        $c = ria_mysql_fetch_assoc($child);
                        if( $_GET['prd']==$c['id'] ){
                            $error = _("Le produit ne peut pas être son propre enfant.");
                        }elseif( !prd_hierarchy_add( $_GET['prd'], $c['id'] ) ){
                            $error = sprintf(_('L\'ajout de la référence %s a échoué pour une raison inconnue.'), $c['ref']);
                        }
                    }
                    break;
                }
            }
        }
    }

    if (isset($error)) {
        print '<div class="error">' . nl2br(htmlspecialchars($error)) . '</div>';
    } elseif (isset($success)) {
        print '<div class="error-success">' . nl2br(htmlspecialchars($success)) . '</div>';
    }

    $ar_type_linked = array();

    if (isset($_GET['relation_type'])){
        switch($_GET['relation_type']){
            case 'prd-relations' : {
                $types = prd_relations_types_get($_GET['type']);
                if ($types) {
                    while ($type = ria_mysql_fetch_assoc($types)) {
                        $type['key_type'] = 'prd-relations-' . $type['id'];
                        $ar_type_linked[] = array_merge(array('import_id' => $type['id']), $type);
                    }
                }
                break;
            }
            case 'prd-parents' : {
                $ar_type_linked[] = array('id' => 'prd-parents', 'key_type' => 'prd-parents', 'name' => _('Article parent'), 'name_plural' => _('Articles parents'), 'import_id' => 'parents');
                break;
            }
            case 'prd-childs' : {
                $ar_type_linked[] = array('id' => 'prd-childs', 'key_type' => 'prd-childs', 'name' => _('Article enfant'), 'name_plural' => _('Articles enfants'), 'import_id' => 'childs');
                break;
            }
        }
    } else {
        $types = prd_relations_types_get();
        if ($types) {
            while ($type = ria_mysql_fetch_assoc($types)) {
                $type['key_type'] = 'prd-relations-' . $type['id'];
                $ar_type_linked[] = array_merge(array('import_id' => $type['id']), $type);
            }
        }

        $ar_type_linked = array_merge($ar_type_linked, array(
                array('id' => 'prd-parents', 'key_type' => 'prd-parents', 'name' => _('Article parent'), 'name_plural' => _('Articles parents'), 'import_id' => 'parents'),
                array('id' => 'prd-childs', 'key_type' => 'prd-childs', 'name' => _('Article enfant'), 'name_plural' => _('Articles enfants'), 'import_id' => 'childs')
            )
        );
    }

    if( (isset($_GET['published']) && $_GET['published'] == 'true')){
        $published = true;
    } elseif ( (isset($_GET['published']) && $_GET['published'] == 'false')){
        $published = 0;
    } elseif (isset($_SESSION['usr_admin_view_publish_linked']) ){
        $published = $_SESSION['usr_admin_view_publish_linked'];
    } elseif (isset($config['admin_view_publish_linked']) ){
        $published = $config['admin_view_publish_linked'];
    } else {
        $published = false;
    }

    $count = 0;
    foreach ($ar_type_linked as &$type) {
        $ar_prd_relations = array();

        switch ($type['key_type']) {
            case 'prd-parents': {
                $no_products = _('Aucun article parent');

                $parents = prd_products_get_simple(0, '', $published, 0, false, false, false, null, array('childs' => true, 'parent_from_child' => $_GET['prd']));
                if ($parents) {
                    while ($parent = ria_mysql_fetch_assoc($parents)) {
                        $parent['is_sync'] = $parent['hry_parent_is_sync'];
                        $ar_prd_relations[] = $parent;
                    }
                }
                $ordered = -1;
                break;
            }
            case 'prd-childs': {
                $no_products = _('Aucun article enfant');

                $ordered = prd_relations_order_get('childs');

                //$childs = prd_childs_get( $_GET['prd'], false, false );
                $childs = prd_products_get_simple(0, '', $published, 0, false, false, false, $ordered ? array('child_pos' => 'asc') : null, array('childs' => true, 'parent' => $_GET['prd']));
                if ($childs) {
                    while ($child = ria_mysql_fetch_assoc($childs)) {
                        $child['is_sync'] = $child['hry_child_is_sync'];
                        $ar_prd_relations[] = $child;
                    }
                }

                break;
            }
            default: {
                $no_products = _('Aucun article pour ce type de relation');

                $ordered = prd_relations_order_get($type['id']);

                //$relations = prd_relations_get($_GET['prd'],null,$t['id'],);
                $relations = prd_relations_get($_GET['prd'], null, $type['id'], $published, 0, false, $ordered ? array('pos' => 'asc') : null);
                if ($relations) {
                    while ($relation = ria_mysql_fetch_assoc($relations)) {
                        $ar_prd_relations[] = array(
                            'id' => $relation['dst_id'],
                            'ref' => $relation['dst_ref'],
                            'name' => $relation['dst_name'],
                            'is_sync' => false, // $relation['dst_is_sync']
                            'publish' => $relation['dst_publish'],
                            'publish_cat' => $relation['dst_publish_cat']
                        );
                    }
                }

                break;
            }
        }

        // TODO : fonction qui renvoie le tri courant pour ce type de relation
        //$ordered = ($ordered_id == '') ? false : true;
        //$ordered = true;
        // ajout d'un bouton choisir qui permet de d'ouvrir la popup pour faire un choix de un ou plusieurs articles
        // le champs référence permet de de taper à la main une référence voulu en l'ajoutant avec le le boutoon ajouter
        print '
        <form method="post" id="form-'.$type['key_type'].'" action="product.php?cat=' . $_GET['cat'] . '&amp;prd=' . $_GET['prd'] . '&amp;tab=linked" name="edit">
            <input type="hidden" name="data-rel" value="' . $type['key_type'] . '" />
            <input type="hidden" name="rel-source" value="' . $_GET['prd'] . '" />
            <input type="hidden" name="rel-ordered" value="' . ($ordered === -1 ? -1 : ($ordered ? 1 : 0)) . '" />

            <table class="checklist list-prd-relations large">
                <caption>' . htmlspecialchars($type['name_plural']) . '</caption>
                <thead>
                    <tr>
                        <th class="list-prd-relations-rel-check" id="rel-check-' . $type['id'] . '" data-label="'._('Tout cocher :').' "><input type="checkbox" class="checkall" name="checkall" onclick="checkAllClick(this)" /></th>
                        <th id="rel-is-sync" class="thead-none"></th>
                        <th class="list-prd-relations-rel-ref thead-none" id="rel-ref-' . $type['id'] . '">'._('Référence').'</th>
                        <th class="thead-none" id="rel-name-' . $type['id'] . '">'._('Désignation').'</th>
                        <th class="thead-none col150px">'._('Publié gescom ?').'</th>
                        <th class="thead-none col150px">'._('Publié site ?').'</th>
                        ' . ($ordered > 0 ? '<th id="rel-ord-pos" class="thead-none align-center">'._('Déplacer').'</th>' : '') . '
                    </tr>
                </thead>
                <tbody>
		';

		// Affiche la liste des produits en relation
        if( sizeof($ar_prd_relations) ){
            foreach ($ar_prd_relations as $one_rel) {
                print '
                    <tr id="line-' . $one_rel['id'] . '" class="ria-row-orderable">
                        <td headers="rel-check-' . $type['id'] . '"><input type="checkbox" name="prd-relations-' . $type['id'] . '-ref[]" value="' . $one_rel['id'] . '" class="'.( $one_rel['is_sync'] ? 'relIsSync' : '' ).'" /></td>
                        <td headers="rel-is-sync" class="prd-is-sync">
                            ' . (($type['key_type'] == 'prd-parents' || $type['key_type'] == 'prd-childs') ? view_hry_is_sync($one_rel) : view_prd_is_sync($one_rel)) . '
                        </td>
                        <td headers="rel-ref-' . $type['id'] . '"><a href="/admin/catalog/product.php?cat=0&prd=' . $one_rel['id'] . '">' . htmlspecialchars($one_rel['ref']) . '</a></td>
                        <td headers="rel-name-' . $type['id'] . '"><a href="/admin/catalog/product.php?cat=0&prd=' . $one_rel['id'] . '">' . htmlspecialchars($one_rel['name']) . '</a></td>
                        <td headers="rel-publish">' . ($one_rel['publish'] ? _('Oui') : _('Non')) . '</td>
                        <td headers="rel-publish-site">' . ($one_rel['publish'] && $one_rel['publish_cat'] ? _('Oui') : _('Non')) . '</td>' .
                    ($ordered > 0 ? '<td headers="ord-pos" class="ria-cell-move"></td>' : '') . '
                    </tr>
				';
            }
        } else {
            print '
                    <tr>
                        <td colspan="' . ($ordered > 0 ? 7 : 6) . '">' . htmlspecialchars($no_products) . '</td>
                    </tr>
			';
        }

        print '
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" class="tdleft">
                            '.( sizeof($ar_prd_relations) ? '<input type="button" class="del-rel" name="' . $type['key_type'] . '-del" title="'._('Supprimer les relations sélectionnées').'" value="'._('Supprimer').'" />' : '' ).'
                        </td>
                        <td class="align-right" colspan="' . ($ordered > 0 ? 4 : 3) . '">
                            <label for="' . $type['key_type'] . '-ref">'._('Référence :').'</label>
                            <input type="hidden" value="" id="' . $type['key_type'] . '-id" name="' . $type['key_type'] . '-id" class="prd-id-hidden" />
                            <input type="text" autocomplete="off"';
                                if (isset($config["prd_linked_show_poup"]) && !$config["prd_linked_show_poup"]) {
                                    print  ' onkeyup="search_references(\'' . $type['key_type'] . '-ref\', false);" data-open="false"';
                                }
                            print ' size="18" class="ref" id="' . $type['key_type'] . '-ref" name="' . $type['key_type'] . '-ref" />
                            <input type="submit" class="button add-rel-ref" value="'._('Ajouter').'" id="' . $type['key_type'] . '-add-ref" name="' . $type['key_type'] . '-add-ref">
                            <input type="button" class="button add-rel" value="' . _('Choisir') . '" id="' . $type['key_type'] . '-add" name="' . $type['key_type'] . '-add">
                            <input type="submit" onclick="return relationImport(' . $_GET['prd'] . ',\'' . $type['import_id'] . '\');" class="button" value="'._('Importer').'" id="prd-relations-import-' . $type['import_id'] . '-add" name="prd-relations-import-' . $type['import_id'] . '-add">
                        </td>
                    </tr>
                    ' . ($ordered !== -1 && sizeof($ar_prd_relations)>2 ? '
                    <tr>
                        <td colspan="' . ($ordered > 0 ? 7 : 6) . '" class="tfoot-grey">
                            <label>'._('Trier ces articles par ordre :').'</label>
                            <input type="radio" class="radio" name="order" id="order-' . $count . '-0" value="0" ' . (!$ordered ? 'checked="checked"' : '') . ' /> <label for="order-' . $count . '-0">'._('Référence').'</label>
                            <input type="radio" class="radio" name="order" id="order-' . $count . '-1" value="1" ' . ($ordered ? 'checked="checked"' : '') . ' /> <label for="order-' . $count . '-1">'._('Personnalisé').'</label>
                            <input type="submit" name="orderby" value="'._('Appliquer').'" />
                        </td>
                    </tr>
                    ' : '') . '
		';

		// Affiche les fonctions de recopie, uniquement s'il y a des produits sur lesquels recopier
        if( $type['key_type']=='prd-childs' && sizeof($ar_prd_relations) ){
            print '
            <tr>
                <td colspan="' . ($ordered > 0 ? 7 : 6) . '" class="align-left tfoot-grey">
                    '._('Recopier les informations du parent sur les enfants').'
                    <input name="copy-to-childs" value="'._('Copier').'" class="width-auto" onclick="displayPopup(\'Recopier les informations du parent sur les enfants\', \'\', \'/admin/catalog/popup-duplicate-parent-to-child.php?prd='.$_GET['prd'].'\');return false;" type="submit" />
                </td>
            </tr>
        ';
        }

        print '
                </tfoot>
            </table>
        </form>
		';
        ++$count;

    }
