<?php
	require_once('products.inc.php');

	if( !isset($_GET['prdsrc']) || !prd_products_exists($_GET['prdsrc']) ){
		$g_error = _("L'information donnée sur le produit est faux.");
	}elseif( !isset($_GET['type']) || (!in_array($_GET['type'], array('parents', 'childs')) && !prd_relations_types_exists($_GET['type'])) ){
		$g_error = _("L'information données sur le type de relation à importer est faux.");
	}else{

		$type_name = '';

		switch( $_GET['type'] ){
			case 'parents' :
				$type_name = _('Parent');
				break;
			case 'childs' :
				$type_name = _('Enfant');
				break;
			default :
				$rtype = prd_relations_types_get( $_GET['type'] );
				if( $rtype && ria_mysql_num_rows($rtype) ){
					$type = ria_mysql_fetch_array( $rtype );

					$type_name = $type['name'];
				}
		}

		$limit_for_page = 30;

		$page = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] ? $_GET['page'] : 1;

		if( isset($_GET['select']) ){
			if( isset($_GET['sel']) && is_array($_GET['sel']) && sizeof($_GET['sel']) ){
				foreach( $_GET['sel'] as $s ){
					switch( $_GET['type'] ){
						case 'parents' :
							if( !prd_hierarchy_add($s, $_GET['prdsrc']) ){
								$error = true;
							}
							break;
						case 'childs' :
							if( !prd_hierarchy_add($_GET['prdsrc'], $s) ){
								$error = true;
							}
							break;
						default :
							if( !prd_relations_add($_GET['prdsrc'], $s, $_GET['type']) ){
								$error = true;
							}
							break;
					}
				}

				if( isset($error) ){
					$error = _("Une erreur inattendue s'est produite lors de l'import des relations entre produits.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
		}
	}

	define('ADMIN_PAGE_TITLE', 'value');
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'popup-iframe');
	require_once('admin/skin/header.inc.php');

	if( isset($g_error) ){
		print '<div class="error">'.nl2br( $g_error ).'</div>';
	}else{
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}
?>

	<form action="/admin/catalog/popup-import-relations.php" method="get">
		<input type="hidden" name="type" value="<?php print $_GET['type']; ?>" />
		<input type="hidden" name="prdsrc" value="<?php print $_GET['prdsrc']; ?>" />
		<input type="hidden" name="prddst" value="<?php print isset($_GET['prddst']) ? $_GET['prddst'] : 0; ?>" />
		<?php
			if( (!isset($_GET['q']) || trim($_GET['q'])=='') && isset($_GET['prddst']) && prd_products_exists($_GET['prddst']) ){
				$pdst = ria_mysql_fetch_array( prd_products_get_simple($_GET['prddst']) );

				print '
					<p>
						'._('Vous avez choisi le produit suivant').' : '.view_prd_is_sync( $pdst ).' '.htmlspecialchars( $pdst['ref'].' - '.$pdst['name'] ).', '._('vous pouvez importer une partie ou tous les produits liés à celui-ci avec une relation de type').' : "'.htmlspecialchars( $type_name ).'".
					</p>

					<table class="checklist" cellpadding="0" cellspacing="0">
						<caption>'._('Produit(s) lié(s)').'</caption>
						<col width="20" /><col width="450" />
						<thead>
							<tr>
								<th id="res-check">
									<input type="checkbox" class="checkbox" onclick="checkAllClick(this);" />
								</th>
								<th id="res-name">'._('Désignation').'</th>
							</tr>
						</thead>
						<tfoot>
							<tr>
								<td colspan="2">
									<div style="float: left;">
										<input type="submit" name="select" value="'._('Importer').'" title="'._('Les produits sélectionnés seront importés comme produits liés.').'" />
									</div>
								</td>
							</tr>
						</tfoot>
						<tbody>
				';

				$ar_linked = array();
				switch( $_GET['type'] ){
					case 'parents' :
						$rparent = prd_parents_get( $pdst['id'], false, false );
						if( !$rparent || !ria_mysql_num_rows($rparent) ){
							print '
								<tr>
									<td colspan="2">'._('Aucun article parent.').'</td>
								</tr>
							';
						}else{
							while( $parent = ria_mysql_fetch_array($rparent) ){
								$ar_linked[ $parent['id'] ] = view_prd_is_sync($parent).' '.htmlspecialchars( $parent['ref'].' - '.$parent['name'] );
							}
						}
						break;
					case 'childs' :
						$rchild = prd_products_get_simple( 0, '', false, 0, false, false, false, false, array('parent'=>$pdst['id'], 'childs'=>true) );
						if( !$rchild || !ria_mysql_num_rows($rchild) ){
							print '
								<tr>
									<td colspan="2">'._('Aucun article enfant.').'</td>
								</tr>
							';
						}else{
							while( $child = ria_mysql_fetch_array($rchild) ){
								$ar_linked[ $child['id'] ] = view_prd_is_sync($child).' '.htmlspecialchars( $child['ref'].' - '.$child['name'] );
							}
						}
						break;
					default :
						$rrel = prd_relations_get( $pdst['id'], null, $_GET['type'] );
						if( !$rrel || !ria_mysql_num_rows($rrel) ){
							print '
								<tr>
									<td colspan="2">'._('Aucun article n\'est rattaché à ce produit avec une relation de type').' "'.htmlspecialchars( $type_name ).'".</td>
								</tr>
							';
						}else{
							while( $rel = ria_mysql_fetch_array($rrel) ){
								$p = ria_mysql_fetch_array( prd_products_get_simple($rel['dst_id']) );
								$ar_linked[ $p['id'] ] = view_prd_is_sync($p).' '.htmlspecialchars( $p['ref'].' - '.$p['name'] );
							}
						}
						break;
				}

				if( sizeof($ar_linked)>0 ){
					foreach( $ar_linked as $id=>$name ){
						print '
							<tr>
								<td headers="res-check" class="pos-center">
									<input type="checkbox" name="sel[]" id="sel-'.$id.'" value="'.$id.'" />
								</td>
								<td headers="res-name">
									'.$name.'
								</td>
							</tr>
						';
					}
				}

				print '
						</tbody>
					</table>
				';
			}else{
		?>
		<p>
			<?php printf(_('Vous pouvez importer les relations de type "%s" depuis un produit existant, pour cela vous devez commencé par la recherche du produit en question :'), htmlspecialchars( $type_name )); ?>
			<input type="text" name="q" value="<?php print isset($_GET['q']) ? $_GET['q'] : ''; ?>" />
			<input type="submit" value="<?php print _('Rechercher un produit')?>" id="search-prd" name="search-prd" />
		</p>

		<?php
			if( isset($_GET['q']) ){
				$ar_results = array();
				$search = search(
					array('seg'=>1, 'keywords'=>$_GET['q'], 'page'=>1, 'limit'=>0, 'published'=>false, 'section'=>false, 'action'=>6, 'type'=>array('prd')),
					false, 0, -1, true, false, 0, true
				);

				if( is_array($search) && sizeof($search) ){
					foreach( $search as $s ){
						$ar_results[] = array(
							'id' => $s['search']['tag'],
							'name' => $s['get']['ref'].' - '.$s['search']['name']
						);
					}
				}

				$pages = sizeof( $ar_results ) ? ceil(sizeof( $ar_results )/$limit_for_page) : 0;
				$page = $page>$pages ? $pages : $page;

				$pmin = $page-5;
				$pmax = $pmin+9;
				$pmin = $pmin<1 ? 1 : $pmin;
				$pmax = $pmax>$pages ? $pages : $pmax;

				print '
					<table class="checklist" cellpadding="0" cellspacing="0">
						<caption>'.( sizeof($ar_results).' résultat'.( sizeof($ar_results)>1 ? 's' : '') ).'</caption>
						<col width="450" /><col width="100" />
						<thead>
							<tr>
								<th id="res-name">'._('Désignation').'</th>
								<th id="res-check"></th>
							</tr>
						</thead>
						<tfoot>
							<tr>
								<td colspan="2">
									<div style="float: left;">
										<input type="submit" name="select" value="'._('Sélectionner').'" onclick="return !$(\'.pos-center input:checked\').length ? false : true;" />
									</div>
									<div style="float: right;">
				';

				if( $pages>1 ){
					$plink = '/admin/catalog/popup-import-relations.php?type='.$_GET['type'].'&amp;prdsrc='.$_GET['prdsrc'].'&amp;q='.$_GET['q'];

					if( $page>1 ){
						print '<a href="'.$plink.'&amp;page='.($page-1).'">&laquo; '._('Page précédente').'</a> | ';
					}
					for( $i=$pmin; $i<=$pmax; $i++ ){
						if( $i==$page ){
							print '<b>'.$page.'</b>';
						}else{
							print '<a href="'.$plink.'&amp;page='.($i).'">'.$i.'</a>';
						}

						if( $i<$pmax ){
							print ' | ';
						}
					}
					if( $page<$pages ){
						print ' | <a href="'.$plink.'&amp;page='.($page+1).'">'._('Page suivante').' &raquo;</a>';
					}
				}

				print '
									</div>
								</td>
							</tr>
						</tfoot>
						<tbody>
				';

				if( !sizeof($ar_results) ){
					print '
						<tr>
							<td colspan="2">'._('Aucun résultat').'</td>
						</tr>
					';
				}else{
					$i = 1;

					$results = array_slice( $ar_results, $limit_for_page*($page-1), $limit_for_page );
					foreach( $results as $id=>$r ){
						if( $i>$limit_for_page ){
							break;
						}

						print '
							<tr>
								<td headers="res-name">
									<label for="sel-'.$r['id'].'">'.htmlspecialchars( $r['name'] ).'</label>
								</td>
								<td headers="res-check" class="pos-center">
									<a href="/admin/catalog/popup-import-relations.php?type='.$_GET['type'].'&amp;prdsrc='.$_GET['prdsrc'].'&amp;prddst='.$r['id'].'">'._('Sélectionner').'</a>
								</td>
							</tr>
						';

						$i++;
					}
				}

				print '
						</tbody>
					</table>
				';
			}
		}
		?>
	</form>

<?php
	}

	if( isset($_GET['select']) && !isset($error) ){
		print '
			<script><!--
			$(document).ready(
				function(){
						parent.reloadTabRelations();
				}
			);
			--></script>
		';
	}

	require_once('admin/skin/footer.inc.php');
?>