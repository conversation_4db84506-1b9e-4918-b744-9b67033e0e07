<?php
	/**	\file index.php
	 *	Cette page est l'index de catalogue
	 *  Elle affiche les différentes catégories sous forme de tableau
	 *
	 *	Elle permet les actions suivantes :
	 *	- Modifier / Ajouter / Supprimer / Publier / Dépublier / Déplacer / Classer / Exporter / Surveiller des Catégories
	 *  - Exporter le catalogue
	 *  - Appliquer un modèle à tout le catalogue
	 *  - Exporter le référencement des produits
	 *
	 */
	require_once('categories.inc.php');
	require_once('prd/category-filters.inc.php');
	require_once('products.inc.php');
	require_once('fields.inc.php');
	require_once('prices.inc.php');

	define( 'MAX_PRODUCTS_ON_PAGE', 500 );

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	if( isset($_GET['new']) && $_GET['new'] ){
		gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT');
	}elseif( (!isset($_GET['new']) || !$_GET['new']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_CATEG');
	}

	// Produits sans classement
	if (isset($_GET['unclassified'])) {
		header('Location: /admin/catalog/unclassified.php');
		exit;
	}

	if( isset($error) ) unset($error);

	// Téléchargement de l'export Excel "Référencement"
	if( isset($_GET['downloadexportreferencing']) ){
		if( file_exists($config['doc_dir'].'/export-referencing-'.$_SESSION['usr_id'].'.csv') ){
			header('Content-Type: application/x-force-download; charset=utf-8');
			header('Content-Disposition: attachment; filename="referencement.csv"');
			header('Content-Length: '.filesize($config['doc_dir'].'/export-referencing-'.$_SESSION['usr_id'].'.csv'));

			readfile($config['doc_dir'].'/export-referencing-'.$_SESSION['usr_id'].'.csv');
			exit;
		}else{
			$error = _("Le fichier ne semble plus disponible, veuillez préparer un nouvel export en cliquant sur le bouton \"Exporter\".");
		}
		header('Location: index.php?'.( isset($_GET['new']) && $_GET['new'] ? 'new=1' : 'cat='.$_GET['cat']));
	}

	// Téléchargement de l'export Excel des catégories
	if( isset($_GET['downloadexportcategories']) ){
		// Contrôle que le fichier est bien disponible
		$file = $config['doc_dir'] . '/export-categories-'.$_SESSION['usr_id'].'.csv';
		if (file_exists($file)) {
			if (!isset($_GET['for_mac'])) {
				header('Pragma: no-cache');
				header('Expires: 0');
				header('Content-disposition: attachment; filename="export-categories.csv"');

				if (!isset($_GET['for_excel'])) {
					echo "\xEF\xBB\xBF"; // BOM UTF-8
				}
			}else{
				header('Content-Description: File Transfer');
				header('Content-Type: application/octet-stream');
				header('Content-Disposition: attachment; filename="export-categories.csv"');
				header('Expires: 0');
				header('Cache-Control: must-revalidate');
				header('Pragma: public');
				header('Content-Length: ' . filesize($file));
			}

			readfile ($file);
			exit;
		}else{
			$error = _("Le fichier ne semble plus disponible, veuillez préparer un nouvel export en cliquant sur le bouton \"Exporter\".");
		}
	}

	// S'assure que les tarifs associés avec l'utilisateur actuel ne s'appliquent pas à l'affichage des prix
	$prc_session = null; $discount_session = null;
	if( isset($_SESSION['usr_prc_id']) ){
		$prc_session = $_SESSION['usr_prc_id'];
		unset($_SESSION['usr_prc_id']);
	}
	if( isset($_SESSION['usr_discount']) ){
		$discount_session = $_SESSION['usr_discount'];
		unset($_SESSION['usr_discount']);
	}

	// Vérifie l'existance de la catégorie
	if( !isset($_GET['cat']) || !prd_categories_exists($_GET['cat']) )
		$_GET['cat'] = 0;
	if( !isset($_GET['brd']) || ($_GET['brd']!=-1 && !prd_brands_exists($_GET['brd'])) ){
		$_GET['brd'] = 0;
	}
	if( !isset($_GET['new']) )
		$_GET['new'] = false;
	if( !isset($_GET['fld']) || !is_numeric($_GET['fld']) )
		$_GET['fld'] = false;
	if( !isset($_GET['mdl']) || !is_numeric($_GET['mdl']) )
		$_GET['mdl'] = false;
	if( !isset($_GET['completion']) )
		$_GET['completion'] = false;

	// Ajout d'une sous catégorie
	if( isset($_POST['catname']) && trim($_POST['catname']) ){
		if( strstr($_POST['catname'],'"') )
			$error = _("Les guillemets ne sont pas autorisés dans les noms de catégories.")."\n"._("Veuillez ne pas en utiliser.");
		else{
			prd_categories_add( $_POST['catname'], '', '', $_GET['cat'] );
			header('Location: index.php?'.( isset($_GET['new']) && $_GET['new'] ? 'new=1' : 'cat='.$_GET['cat']));
			exit;
		}
	}

	// Modification de la méthode de tri
	if( isset($_POST['orderby'],$_POST['order']) ){
		prd_categories_order_update( $_GET['cat'], $_POST['order'] );
		header('Location: index.php?'.( isset($_GET['new']) && $_GET['new'] ? 'new=1' : 'cat='.$_GET['cat']));
		exit;
	}

	// Modification de la méthode de tri pour les marques
	if( isset($_POST['orderby_brands'],$_POST['order_brands']) ){
		prd_products_order_update( $_GET['brd'], $_POST['order_brands'] );
		header('Location: index.php?brd='.$_GET['brd']);
		exit;
	}

	// Déplacement vers le haut
	if( isset($_GET['up']) ){
		prd_categories_move_up( $_GET['up'] );
		header('Location: index.php?'.( isset($_GET['new']) && $_GET['new'] ? 'new=1' : 'cat='.$_GET['cat']));
		exit;
	}

	// Déplacement vers le bas
	if( isset($_GET['dw']) ){
		prd_categories_move_down( $_GET['dw'] );
		header('Location: index.php?'.( isset($_GET['new']) && $_GET['new'] ? 'new=1' : 'cat='.$_GET['cat']));
		exit;
	}

	// Publication d'une ou plusieurs catégories
	if( isset($_POST['publish-cat']) && isset($_POST['categs']) && is_array($_POST['categs']) ){
		foreach( $_POST['categs'] as $c ){
			if( !prd_categories_get_is_sync($c) ){
				prd_categories_publish($c);
			}
		}
		header('Location: index.php?'.( isset($_GET['new']) && $_GET['new'] ? 'new=1' : 'cat='.$_GET['cat']));
		exit;
	}

	// DéPublication d'une ou plusieurs catégories
	if( isset($_POST['unpublish-cat']) && isset($_POST['categs']) && is_array($_POST['categs']) ){
		foreach( $_POST['categs'] as $c ){
			if( !prd_categories_get_is_sync($c) ){
				prd_categories_unpublish($c);
			}
		}
		header('Location: index.php?'.( isset($_GET['new']) && $_GET['new'] ? 'new=1' : 'cat='.$_GET['cat']));
		exit;
	}

	// Application d'un modèle de saisie
	if( isset($_POST['apply-model']) ){
		if( isset($_POST['model']) && fld_models_exists($_POST['model']) ){
			if( isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat']<=0 ){
				$rproduct = prd_products_get_simple();
				if( $rproduct ){
					$_POST['prd'] = array();

					while( $product = ria_mysql_fetch_assoc($rproduct) ){
						$_POST['prd'][] = $product['id'];
					}
				}
			}

			if( isset($_POST['prd']) && is_array($_POST['prd']) ){
				foreach( $_POST['prd'] as $p ){
					fld_object_models_add( $p, $_POST['model'] );
				}
			}
		}

		if( isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat']<=0 ){
			$_SESSION['save-model-all-prd'] = true;
		}

		header('Location: /admin/catalog/index.php?cat='.( isset($_GET['cat']) ? $_GET['cat'] : 0).''.( isset($_GET['new']) ? '&new='.$_GET['new'] : '' ));
		exit;
	}

	// Suppression des nouveautés
	if( isset($_POST['remove-new']) && isset($_POST['prd']) && is_array($_POST['prd']) ){
		foreach( $_POST['prd'] as $p ){
			prd_products_set_new($p, -1);
		}
		header('Location: index.php?new=1');
		exit;
	}

	// Publication d'un ou plusieurs produits
	if( isset($_POST['publish']) && isset($_POST['prd']) && is_array($_POST['prd']) ){
		foreach( $_POST['prd'] as $p ){
			if( !prd_products_get_is_sync($p) ){
				prd_products_publish($p);
			}
		}
		header('Location: index.php?'.( isset($_GET['new']) && $_GET['new'] ? 'new=1' : 'cat='.$_GET['cat']));
		exit;
	}
	// Dé-Publication d'un ou plusieurs produits
	if( isset($_POST['unpublish']) && isset($_POST['prd']) && is_array($_POST['prd']) ){
		foreach( $_POST['prd'] as $p ){
			if( !prd_products_get_is_sync($p) ){
				prd_products_unpublish($p);
			}
		}
		header('Location: index.php?'.( isset($_GET['new']) && $_GET['new'] ? 'new=1' : 'cat='.$_GET['cat']));
		exit;
	}

	// Ajout d'un produit
	if( isset($_POST['addprd']) && isset($_POST['prdname']) ){

		// Recherche si la valeur saisie dans le champ prdname est la référence d'un produit existant
		$add = false;
		if( trim($_POST['prdname']) && $_GET['cat']>0 ){
			$prd = prd_products_exists_ref( trim($_POST['prdname']), false );
			if( $prd ){
				// Si le produit existe déjà, on l'ajoute simplement à la catégorie en cours
				$add = prd_products_add_to_cat( $prd, $_GET['cat'] );
				$_SESSION['flash_success'] = sprintf(_("La référence <strong>%s</strong>, existant déjà, vient d'être ajoutée à la catégorie."), htmlspecialchars(strtoupper2($_POST['prdname'])) );
			}
		}

		// Envoi l'utilisateur vers la fiche de création d'un nouveau produit (seulement s'il n'y a pas eu d'import d'un produit existant)
		if( !$add ){
			$brd = isset($_POST['brd']) && is_numeric($_POST['brd']) && $_POST['brd']>0 ? '&brd='.$_POST['brd'] : '';
			header('Location: product.php?cat='.$_GET['cat'].'&prdname='.urlencode(trim($_POST['prdname'])).$brd);
			exit;
		}
	}

	// Déplacement de produits ou de catégories
	if( isset($_POST['classify']) ){
		$move = '';
		if( isset($_POST['prd']) ){
			foreach( $_POST['prd'] as $p )
				if( is_numeric($p) )
					$move .= '&products[]='.$p;
		}elseif( isset($_POST['categs']) ){
			foreach( $_POST['categs'] as $c )
				if( is_numeric($c) )
					$move .= '&categs[]='.$c;
		}
		if( $move=='' ){
			$error = _('Veuillez sélectionner les catégories ou les produits à déplacer.');
		}else{
			header('Location: move.php?classify=1&srccat='.$_GET['cat'].$move);
			exit;
		}
	}

	// Dé-classement
	if( isset($_POST['unclassify']) ){
		if( isset($_POST['prd']) ){
			foreach( $_POST['prd'] as $p ){
				if( is_numeric($p) ){
					$res = prd_products_del_from_cat( $p, $_GET['cat']); // la fonction n'agit que sur les classements non synchronisés
					if( !$res ){
						$error = isset($error) ? "" : $error;
						$error .= sprintf(_('Une erreur inattendue est survenue lors de la dépublication du produit %s'), prd_products_get_name( $p, true));
					}
				}
			}
		}else{
			$error = _('Veuillez sélectionner les produits à déplacer.');
		}

		if( !isset($error) ){
			header('Location: /admin/catalog/index.php?cat='.$_GET['cat']);
			exit;
		}
	}

	// Duplication de produits
	if( isset($_POST['duplicate']) ){
		if( isset($_POST['prd']) ){
			foreach( $_POST['prd'] as $p ){
				if( is_numeric($p) ){
					if( !prd_products_duplicate( false, $p, true, true, true, true, true, true ) ){
						$error = _('Une erreur est survenue lors de la duplication de votre produit.');
						break;
					}
				}
			}
		}
	}

	// Suppression de produits ou de catégories
	if( isset($_POST['delete']) ){
		// Suppresssion d'un ou plusieurs produits
		if( isset($_POST['prd']) && is_array($_POST['prd']) ){
			foreach( $_POST['prd'] as $p ){
				if( !prd_products_get_is_sync($p) ){
					prd_products_del( $p );
				}
			}

			header('Location: index.php?'.( isset($_GET['new']) && $_GET['new'] ? 'new=1' : 'cat='.$_GET['cat']));
			exit;
		}

		// Suppression d'une ou plusieurs sous catégories
		if( isset($_POST['categs']) && is_array($_POST['categs']) ){
			foreach( $_POST['categs'] as $c ){
				if( !prd_categories_get_is_sync($c) ){
					prd_categories_del($c);
				}
			}
			header('Location: index.php?'.( isset($_GET['new']) && $_GET['new'] ? 'new=1' : 'cat='.$_GET['cat']));
			exit;
		}
	}

	// Sauvegarde le tri sur la liste des produits de cette catégorie
	if( isset($_POST['orderbyprd']) ){
		if( $_POST['prd-sort-type']=='' && !prd_categories_sort_types_del($_GET['cat']) )
			$error = _("Une erreur inattendue s'est produite lors de la suppression du tri des produits.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur");
		elseif( $_POST['prd-sort-type']!='' && !prd_categories_sort_types_add($_GET['cat'], $_POST['prd-sort-type'], $_POST['sort-prd-dir'], isset($_POST['sort-prd-inherited']) ? true : false) )
			$error = _("Une erreur inattendue s'est produite lors de la sauvegarde du tri des produits.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur");

		if( !isset($error) ){
			header('Location: index.php?'.( isset($_GET['new']) && $_GET['new'] ? 'new=1' : 'cat='.$_GET['cat']));
			exit;
		}
	}

	// Récupère le tri effectué sur la liste des produits de cette catégorie
	$sort = prd_categories_sort_get( $_GET['cat'] );
	if( !is_array($sort) )
		$sort = array ( 'type'=>$config['cat_sort_type'], 'dir'=>$config['cat_sort_dir'], 'is_recursive'=>0, 'inherited'=> $_GET['cat'] );


	$colspan = $sort['type']==SORT_PERSO ? 9 : 8;

	// Détermine si les catégories enfant sont triées alphabétiquement ou non
	$ordered = prd_categories_order_get( $_GET['cat'] );

	// Détermine si les produits sont triés alphabétiquement ou non, et détermine la méthode de tri dans les marques
	if (isset($_GET['brd']) && $_GET['brd']>0){
		$ordered_brands = prd_products_brands_order_get($_GET['brd']);
		if($ordered_brands){
			$sort = array('brd_pos' => 'asc');
		} else {
			$sort = array('name' => 'asc');
		}
	}

	// Charge la liste des sous-catégories
	$categories = prd_categories_get( 0, false, $_GET['cat'] );

	$read_only_categ = array();
	if( isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id']!=0 ){
		// Charge les catégories dont la modification est bloquée pour les non super-admin
		if(isset($config["read_only_categ"]) && $config["read_only_categ"]!=""){
			if(isset($config["read_only_categ"][0]) && $config["read_only_categ"][0]=="all"){
				$read_only_categ="all";
			}else{
				$read_only_categ=$config["read_only_categ"];
			}
		}
	}

	// Détermine le fil d'Ariane
	$breadcrumbs = Breadcrumbs::root( 'Accueil', '/admin/index.php' );

	// Construit le titre de la page
	if( isset($_GET['new']) && $_GET['new'] ){
		$breadcrumbs->push( _('Catalogue'), '/admin/catalog/index.php' );
	}elseif( $_GET['cat']==0 ){
		$page_title = _('Catalogue');
		$breadcrumbs->push( _('Catalogue') );
	}else{
		$cat = ria_mysql_fetch_array(prd_categories_get($_GET['cat']));
		$page_title = $cat['title'];

		// Fil d'Ariane
		$breadcrumbs->push( _('Catalogue'), '/admin/catalog/index.php' );

		$r_cat_parent = prd_categories_parents_get( $cat['id'] );
		if( $r_cat_parent ){
			while( $cat_parent = ria_mysql_fetch_assoc($r_cat_parent) ){
				$breadcrumbs->push( $cat_parent['title'], '/admin/catalog/index.php?cat='.$cat_parent['id'] );
			}
		}
		$breadcrumbs->push( $cat['title'] );
	}

	if( $_GET['brd']!=0 ){
		if( $_GET['brd']==-1 ){
			$brd = -1;
			$page_title = _('Produits sans marque');

			// Fil d'Ariane
			$breadcrumbs->push( _('Marques'), '/admin/catalog/brands/index.php' );
			$breadcrumbs->push( _('Produits sans marque') );
		}else{
			$brd = ria_mysql_fetch_array( prd_brands_get($_GET['brd']) );
			$page_title = sprintf( _('Marque %s'), $brd['title'] );

			// Fil d'Ariane
			$breadcrumbs->push( _('Marques'), '/admin/catalog/brands/index.php' );
			$breadcrumbs->push( $brd['title'] );
		}
	}

	if( $_GET['new'] ){
		$page_title = _('Nouveautés');
		$breadcrumbs->push( _('Nouveautés') );
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', $page_title );

	// Breadcrumbs::root( 'Accueil', '/admin/index.php' )->push( ADMIN_PAGE_TITLE );
	require_once('admin/skin/header.inc.php');

	if (!$_GET['new']) {
		print '<h2>';

		print htmlspecialchars( $page_title );

		// Affiche le nombre de sous-catégories
		if( $_GET['brd']==0 && !$_GET['new'] && !$_GET['fld'] && !$_GET['mdl'] ){
			print ' ('.( $categories===false ? 0 : ria_mysql_num_rows($categories) ).( $_GET['cat']==0 ? _(' catégories'):_(' sous-catégories') ).')';
		}
		// Le lien d'édition dépend des droits de l'utilisateur
		if( $_GET['cat'] != 0 && gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_EDIT') ){
			print '<a href="edit.php?cat='.$_GET['cat'].'" class="edit-cat">'._('Modifier cette catégorie').'</a>';
		}elseif( $_GET['cat'] != 0  && gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_VIEW') ){
			print '<a href="edit.php?cat='.$_GET['cat'].'" class="edit-cat">'._('Voir les informations de cette catégorie').'</a>';
		}
		print '</h2>';
	}
	// Affichage des messages d'erreur
	if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}

	// Lorsqu'on est sur une page catégorie, on affiche le tableau des sous-catégorie
	$no_products = false;
	if( $_GET['brd']==0 && !$_GET['new'] && !$_GET['fld'] && !$_GET['mdl'] ){
		if( $categories===false || !ria_mysql_num_rows($categories) ){
			$no_products = !(isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat'] > 0);

			?><form action="index.php?cat=<?php print $_GET['cat']; ?>&amp;new=<?php echo $_GET['new']; ?>" method="post"><?php
			if($_GET['cat']==0 ){
				print '<div class="tall-insert-holder">'.'<div class="tall-insert-content">'.'<span class="tall-insert-icon-cat">'.'</span>'.'<div class="tall-insert-title">'._('C\'est plutôt calme par ici...').'</div>'.
					'<div>'.'<p class="tall-insert-desc">'._('Pour pouvoir créer ou importer des produits dans votre catalogue, il vous faut créer au moins une catégorie.').'</p>'.'</div>'.
					'<input type="text" name="catname" id="catname" placeholder="'._('Nom de ma première catégorie').'"/>'
					.'<input class="btn-main" id="catname-create" disabled="disabled" type="submit" value="'._('Créer').'"/>'.'<p class="tall-insert-desc-second">'._('ou').'</p>'.
					'<input type="button" name="import" value="Importer" title="'._('Importer des catégories').'" onclick="window.location.href =\'../tools/imports/index.php?imp-class=3\';"/>'.'</div>'.'</div>'.'</form>';
			}else{
				print '<div class="small-insert-holder">'
					.'<div class="small-insert-content">'
						.'<span class="small-insert-icon-cat"></span>'
						.'<div class="small-insert-title">'._('Il n\'y a pas de sous-catégories').'</div>'.
					'</div>'
				.'</div>'

				.'<div class="small-insert-btn-holder">'
					.'<div class="float-left">'
						.'<input type="button" name="import" value="Importer" title="Importer des catégories"
							onclick="window.location.href = \'../tools/imports/index.php?imp-class=3\';"
						/>'
					.'</div>'
					.'<div class="float-right small-insert-new-obj">'
						.'<input type="text" name="catname" id="catname" placeholder="'._('Saisir le nom d\'une nouvelle sous-catégorie').'"/>'
						.'<input class="btn-main" id="catname-create" disabled="disabled" type="submit" value="'._('Créer').'"/>'
					.'</div>'
					.'<div class="clear"></div>'
				.'</div>'.'</form>';
			}
		}else{
			?>
			<form action="index.php?cat=<?php print $_GET['cat']; ?>&amp;new=<?php echo $_GET['new']; ?>" method="post">

				<input type="hidden" name="srccat" value="<?php print $_GET['cat']; ?>" />

				<table id="categories" class="checklist">
					<thead>
						<tr>
							<?php
							if($read_only_categ != "all" || $_GET['cat']!=0){
								print '<th id="cat-sel" data-label="'._('Cocher tout :').'"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>';
							}
							?>
							<th id="cat-name" class="thead-none"><?php print _('Désignation')?></th>
							<th id="cat-publish" class="thead-none"><?php print _('Publiée ?')?></th>
							<th id="cat-products" class="align-right thead-none"><?php print _('Produits publiés')?></th>
							<?php if( $ordered && gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_MOVE') ){ ?><th id="cat-pos" class="thead-none col80px"><?php print _('Déplacer'); ?></th><?php } ?>
						</tr>
					</thead>
					<tbody>
					<?php
						if( $categories===false || !ria_mysql_num_rows($categories) ){
							print '
							<tr>
								<td colspan="'.( $ordered ? 5 : 4 ).'">'.( $_GET['cat']==0 ? _('Aucune catégorie') : _('Aucune sous-catégorie') ).'</td>
							</tr>
							';
						}else{
							$count = ria_mysql_num_rows($categories); $current = 0;
							while( $r = ria_mysql_fetch_array($categories) ){
								print '<tr id="line-' . $r['id'] . '" class="ria-row-orderable">';
								if($read_only_categ != "all" || $_GET['cat']!=0){
									print '<td headers="cat-sel"><input '.(is_array($read_only_categ) && in_array($r['id'], $read_only_categ) ? 'disabled' : '').' type="checkbox" class="checkbox" name="categs[]" value="'.$r['id'].'" /></td>';
								}
								print '
									<td headers="cat-name">'.view_cat_is_sync($r).' <a href="index.php?cat='.$r['id'].'" title="'._('Afficher le contenu de cette catégorie').'">'.htmlspecialchars($r['title']).'</a></td>
									<td headers="cat-publish" align="center">'.( $r['publish'] ? _('Oui') : _('Non') ).'</td>
									<td headers="cat-products" align="right">'.ria_number_format($r['products'], NumberFormatter::DECIMAL).'</td>
								';
								if( $ordered && gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_MOVE') ){
									print '<td headers="cat-pos" align="center" class="ria-cell-move"></td>';
								}
								print '</tr>';
								$current++;
							}
						}
					?>
					</tbody>
					<tfoot>
						<tr>
							<td colspan="<?php print $ordered && gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_MOVE') ? 5 : 4; ?>">
								<div class="float-left">
								<?php if( $categories!==false && ria_mysql_num_rows($categories)>0 ){ ?>
									<?php if( ($read_only_categ != "all" || $_GET['cat']!=0) && gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_ADD') ){
										if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_PUBLISH') ){ ?>
											<input type="submit" name="publish-cat" class="btn-move js-input-disabled none" value="<?php print _('Publier')?>" title="<?php print _('Publier les catégories sélectionnées sur la boutique')?>" disabled="disabled" />
											<input type="submit" name="unpublish-cat" class="btn-move js-input-disabled none" value="<?php print _('Dépublier')?>" title="<?php print _('Ne plus publier les catégories sélectionnées de la boutique')?>" disabled="disabled" /><?php
										}
										if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_CLASSIFY') ){ ?>
											<input type="submit" name="move" id="js-move-categories" class="btn-move js-input-disabled none" value="<?php print _('Déplacer')?>" title="<?php print _('Déplacer les catégories sélectionnées')?>" disabled="disabled" />
											<input type="submit" name="classify" id="js-classify-categories" class="btn-move js-input-disabled none" value="<?php print _('Classer')?>" title="<?php print _('Classer les produits des catégories sélectionnées dans une autre catégorie (le classement actuel sera conservé).')?>" disabled="disabled" /><?php
										}
										if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_DEL') ){ ?>
											<input type="submit" name="delete" class="btn-del js-input-disabled none" value="<?php print _('Supprimer')?>" title="<?php print _('Supprimer les catégories sélectionnées')?>" onclick="return window.confirm('<?php print _('Vous êtes sur le point de supprimer les catégories sélectionnées.').'\n'._('Cette opération est irréversible et ne pourra pas être annulée.').'\n'._('Etes vous sûr(e) de vouloir continuer ?').'\n\n'._('Note : les produits contenus dans cette catégorie ne seront pas supprimés.')?>');" disabled="disabled" /><?php
										}
									}
									?>
									<input type="button" name="import" value="<?php print _('Importer')?>" title="<?php print _('Importer des catégories')?>" onclick="window.location.href = '../tools/imports/index.php?imp-class=3';" />
									<input type="submit" name="export-categories" id="export-categories" value="<?php print _('Exporter')?>" title="<?php print _('Exporter la liste des catégories')?>" />
									<?php
								} ?>
								</div>
								<div class="float-right"><?php
									if( ($read_only_categ != "all" || $_GET['cat']!=0) && gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_ADD') ){
										print '<div class="small-insert-btn-holder">'
											.'<div class="small-insert-new-obj">'
												.'<input type="text" name="catname" id="catname"';

													if( $_GET['cat'] != 0 ){
														print 'placeholder="'._('Saisir le nom d\'une nouvelle sous-catégorie').'"';
													}else{
														print 'placeholder="'._('Saisir le nom d\'une nouvelle catégorie').'"';
													}

												print ' />'
												.'<input class="btn-main" id="catname-create" disabled="disabled" type="submit" value="'._('Créer').'"/>'
											.'</div>'
										.'</div>';
									}
								?></div>
							</td>
						</tr>
						<?php if( $categories!==false && ria_mysql_num_rows($categories)>1 && gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_MOVE') ){ ?>
						<tr>
							<td colspan="<?php print $ordered ? 5 : 4; ?>" class="tfoot-grey">
								<label><?php print _('Trier ces catégories par ordre :')?></label>
								<input type="radio" class="radio" name="order" id="order-0" value="0" <?php if( !$ordered ) print 'checked="checked"'; ?> /> <label for="order-0"><?php print _('Alphabétique')?></label>
								<input type="radio" class="radio" name="order" id="order-1" value="1" <?php if( $ordered ) print 'checked="checked"'; ?> /> <label for="order-1"><?php print _('Personnalisé')?></label>
								<input type="submit" name="orderby" value="<?php print _('Appliquer')?>" />
							</td>
						</tr>
						<?php } ?>
					</tfoot>
				</table>
			</form>
			<?php
		}
	}

	// Si l'utilisateur en cours à le droit d'accéder à la liste des produits
	if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT') && !$no_products ){
		// Charge ou non le tableau des articles
		$show_table_prd = false;

		$ar_sort = array();
		if( $_GET['cat']!=0 || $_GET['brd']!=0 || $_GET['new'] || $_GET['fld'] || $_GET['mdl'] || $_GET['completion'] ){
			$show_table_prd = true;

			$published = $catchilds = $with_price = false;
			$others_params = [];

			{ // Prépare le filtrage sur des champs avancés
				$fld = [];

				if( isset($_GET['fld']) && is_numeric($_GET['fld']) && $_GET['fld'] > 0 ){
					$fld[$_GET['fld']][] = '';
				}

				if( isset($_GET['field']) && is_array($_GET['field']) && sizeof($_GET['field']) ){
					foreach( $_GET['field'] as $f=>$vals ){
						if( !is_array($vals) || !count($vals) ){
							continue;
						}
						foreach( $vals as $v ){
							//$fld[$f][] = $v;
							if(is_array($fld)):
								$fld[$f][] =  $v;
							endif;
						}
					}
				}

				if( !count($fld) ){
					$fld = false;
				}

				// Règle appliqué sur les filtres (une ou plusieurs valeur, un ou tous les filtres respectés)
				if( !isset($_GET['flt-val-or']) || !$_GET['flt-val-or'] ){
					$others_params['or_between_val'] = true;
				}
				if( !isset($_GET['flt-fld-or']) || !$_GET['flt-fld-or'] ){
					$others_params['or_between_fld'] = true;
				}
			}

			{ // Prépare le filtre sur la marque
				$brd = [];
				if( isset($_GET['brd']) ){
					$brd = control_array_integer( $_GET['brd'], false );
				}

				if( isset($_GET['brand']) && is_array($_GET['brand']) && count($_GET['brand']) ){
					$brd = array_merge( $brd, $_GET['brand'] );
				}

				if( count($brd) ){
					$others_params['brand'] = $brd;
				}
			}

			{ // Options supplémentaires choisies par l'administrateur
				// Affichage ou non des articles "enfant seulement"
				if( isset($_SESSION['usr_admin_show_childs']) ){
					$others_params['childs'] = $_SESSION['usr_admin_show_childs'];
				}

				// Les articles "enfant seulement" seront toujours affiché si l'on est sur un listing des produits d'une marque
				if( isset($_GET['brd']) && is_numeric($_GET['brd']) && $_GET['brd'] > 0 ){
					$others_params['childs'] = true;
				}

				// Affichage ou non des articles non publié (par défaut à oui)
				if( isset($_SESSION['usr_admin_catalog_hide_source_unpublished']) ){
					$published = $_SESSION['usr_admin_catalog_hide_source_unpublished'];
				}

				// Affichage ou non des articles en sommeil (par défaut à $config['admin_hide_sleeping'])
				if( !isset($_SESSION['usr_admin_hide_sleeping']) ){
					$others_params['hide_sleep'] = $config['admin_hide_sleeping'];
				}else{
					$others_params['hide_sleep'] = $_SESSION['usr_admin_hide_sleeping'];
				}
			}

			// Dans le cas d'un listing pour un comparateur de prix ou une place de marché
			if( isset($_GET['ctr']) && ctr_comparators_exists($_GET['ctr']) ){
				$exclude = false;

				$rpctr = ctr_catalogs_get( $_GET['ctr'], 0, $_GET['cat'], true );
				if( $rpctr && ria_mysql_num_rows($rpctr) ){
					$exclude = array();
					while( $pctr = ria_mysql_fetch_array($rpctr) ){
						$exclude[] = $pctr['prd_id'];
					}
				}

				$published = true;
				$others_params['exclude'] = $exclude;
				$others_params['orderable'] = true;
				$others_params['childs'] = true;
			}

			// Dans le cas du listing des nouveautés
			if( $_GET['new'] ){
				$others_params['new'] = 1;
				$others_params['childs'] = true;
			}

			// Dans le cas d'un listing des articles incomplet
			if( $_GET['completion'] ){
				$others_params['uncompleted'] = true;
			}

			if( !count($others_params) ){
				$others_params = false;
			}

			if( isset($sort['type']) ){
				switch ($sort['type']) {
					case SORT_PRICE:
						$ar_sort["prcprices"] = $sort['dir'];
						break;
					case SORT_REF:
						$ar_sort["ref"] = $sort['dir'];
						break;
					case SORT_ALPHA:
						$ar_sort["name"] = $sort['dir'];
						break;
					case SORT_STOCK:
						$ar_sort["stock"] = $sort['dir'];
						break;
				}
			}

			// Récupération des produits
			$products = prd_products_get_simple( 0, '', $published, $_GET['cat'], $catchilds, $fld, $with_price, false, $others_params );

			// Nombre de produits total, produits des catégories enfants inclus
			$nb_products = 0;
			if( $products ){
				$nb_products += ria_mysql_num_rows( $products );
			}
			if( isset($products_childs) && $products_childs ){
				$nb_products += ria_mysql_num_rows( $products_childs );
			}

			// Nombre de produits affichés dans la liste (enfants directs de la catégorie)
			$total_products = $products ? ria_mysql_num_rows($products) : 0;

			// Le nombre de produits calculés étant désormais limité à MAX_PRODUCTS_ON_PAGE,
			// un affichage spécial est réalisé si le nombre de produits réel est supérieur
			$str_nb_products = $nb_products != $total_products ?
					ria_number_format($nb_products, NumberFormatter::DECIMAL, 0).'/'.ria_number_format($total_products, NumberFormatter::DECIMAL, 0)
					: ria_number_format($total_products, NumberFormatter::DECIMAL, 0);

			if( $_GET['new'] ){
				print '
					<div class="table-top">
						<h2>'._('Nouveautés').
							' (<span class="nb_products">'.
								$str_nb_products.
							'</span>)
						</h2>
					</div>
				';
			}

			// Charge les filtres personnalisés
			$cat_load_by_pmt = false;

			print '
				<form class="items-list-filters prd-listing" action="/admin/catalog/index.php?cat='.$_GET['cat'].'" onsubmit="return false;">
					<input type="hidden" name="new" value="'.(isset($_GET['new']) && $_GET['new'] ? 1 : 0).'" />
					<input type="hidden" name="fld" value="'.( !$_GET['fld'] ? 0 : htmlspecialchars( $_GET['fld'] ) ).'" />
					<input type="hidden" name="brd" value="'.( !$_GET['brd'] ? 0 : htmlspecialchars( $_GET['brd'] ) ).'" />
			';

			if( is_array($others_params) ){
				foreach( ['or_between_val', 'or_between_fld', 'childs', 'hide_sleep', 'orderable', 'new', 'uncompleted'] as $key ){
					if( array_key_exists($key, $others_params) ){
						print '<input type="hidden" name="other_params['.$key.']" value="'.( $others_params[$key] ? 1 : 0 ).'" />';
					}
				}

				foreach( ['exclude', 'brand'] as $key ){
					if( isset($others_params[$key]) && is_array($others_params[$key]) ){
						foreach($others_params[$key] as $val ){
							print '<input type="hidden" name="other_params['.$key.']" value="'.htmlspecialchars( $val ).'" />';
						}
					}
				}
			}

			if( isset($_GET['cat']) && $_GET['cat']>0 ){
				$flt_brd = fld_object_values_get( $_GET['cat'], _FLD_CAT_FILTER_BRD );
				$rfilter = prd_category_filters_get( $_GET['cat'] );

				$check_publish = $published ? 'checked="checked"' : '';
				$check_hidesleep = isset($others_params['hide_sleep']) && $others_params['hide_sleep'] ? 'checked="checked"' : '';
				$check_childs = isset($others_params['childs']) && $others_params['childs'] ? 'checked="checked"' : '';

				$check_fld_or = !isset($others_params['or_between_fld']) || !$others_params['or_between_fld'] ? 'checked="checked"' : '';
				$check_val_or = !isset($others_params['or_between_val']) || !$others_params['or_between_val'] ? 'checked="checked"' : '';

				if (isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat'] > 0) {
					$pmt_code = prd_categories_codes_get( $_GET['cat'] );
					$cat_load_by_pmt = $pmt_code && ria_mysql_num_rows($pmt_code);
				}



				if ($cat_load_by_pmt) {
					print '<div class="notice">'._('Le listing produit ci-dessous est chargé car la catégorie est liée à une ou plusieurs promotions.').'</div>';
				}

				if (isset($_SESSION['flash_success'])) {
					print '<div class="success">'.$_SESSION['flash_success'].'</div>';
					unset($_SESSION['flash_success']);
				}
				if ($_GET['new']) {
					$table_top_title = _('Nouveautés') ;
				} else {
					$table_top_title = _('Produits');
				}

				print '
					<div class="table-top">
						<h2>'.$table_top_title.' (<span class="nb_products">'.$str_nb_products.'</span>)
							<a class="edit-cat" id="display-filters-options" href="#">'._('Avancé').'</a>
						</h2>
					</div>
				';

				print '
						<input type="hidden" name="cat" value="'.htmlspecialchars( $_GET['cat'] ).'" />';

						print '<div id="filters-options">
							<div>
								<input '.$check_publish.' type="checkbox" name="flt-unpublish" id="flt-unpublish" value="1" />
								<label for="flt-unpublish" title="'._('Les produits qui ne sont pas encore publiés seront masqués.').'">'._('Masquer les produits non publiés site').'</label>
							</div>
							<div>
								<input '.$check_hidesleep.' type="checkbox" name="flt-sleep" id="flt-sleep" value="1" />
								<label for="flt-sleep" title="'._('Les produits qui sont en sommeil seront masqués.').'">'._('Masquer les produits en sommeil').'</label>
							</div>
							<div>
								<input '.$check_childs.' type="checkbox" name="flt-childs" id="flt-childs" value="1" />
								<label for="flt-childs" title="'._('Les produits enfants seulement seront affichés.').'">'._('Afficher les produits apparaissant qu’en tant qu’article lié').'</label>
							</div>
							<div>
								<input '.$check_fld_or.' type="checkbox" name="flt-fld-or" id="flt-fld-or" value="1" />
								<label for="flt-fld-or" title="'._('Les produits respectant tous les filtres seront affichés.').'">'._('Tenir compte de tous les filtres').'</label>
							</div>
							<div>
								<input '.$check_val_or.' type="checkbox" name="flt-val-or" id="flt-val-or" value="1" />
								<label for="flt-val-or" title="'._('Les produits respectant toutes les valeurs par filtre seront affichés.').'">'._('Tenir compte de toutes les valeurs').'</label>
							</div>
						</div>
				';

				if( $flt_brd ){
					$ar_prd_ids = array();

					// Retire le filtre sur la marque pour le chargement des marques
					$others_params_flt_brd = $others_params;
					if( isset($others_params_flt_brd['brand']) ){
						unset( $others_params_flt_brd['brand'] );
					}

					// On ne charge que l'identifiant des produits
					$others_params_flt_brd['only_prd_ids'] = true;

					$rbp = prd_products_get_simple( 0, '', $published, $_GET['cat'], $catchilds, false, false, false, $others_params_flt_brd );
					if( $rbp && ria_mysql_num_rows($rbp) ){
						while( $p = ria_mysql_fetch_array($rbp) ){
							$ar_prd_ids[] = $p['id'];
						}
					}

					$rbrand = prd_brands_get( 0, true, '', '', false, null, false, false, false, false, $ar_prd_ids );
					if( $rbrand && ria_mysql_num_rows($rbrand) ){
						$label = _('Aucune marque sélectionnée');
						if( isset($_GET['brand']) ){
							if( sizeof($_GET['brand'])<ria_mysql_num_rows($rbrand) ){
								$label = '';
								while( $b = ria_mysql_fetch_array($rbrand) ){
									if( in_array($b['id'], $_GET['brand']) ){
										$label .= ( trim($label)!='' ? ', ' : '' ).$b['title'];
									}
								}

								$label = strlen($label)>39 ? substr( $label, 0, 35 ).'...' : $label;
								ria_mysql_data_seek( $rbrand, 0 );
							}else{
								$label = _('Toutes les marques');
							}
						}

						print '
							<div class="riapicker prd-filters" style="margin-bottom: 10px;">
								<div class="selectorview">
									<div class="left">
										<span class="function_name">'._('Par marque').'</span><br/>
										<span class="view">'.htmlspecialchars( $label ).'</span>
									</div>
									<a class="btn" name="btn">
										<img src="/admin/images/stats/fleche.gif" height="8" width="16" alt="" />
									</a>
									<div class="clear"></div>
								</div>
								<div class="selector">
									<a name="all">
										<input class="filter-all" '.( isset($_GET['brand']['all']) ? 'checked="checked"' : '' ).' type="checkbox" name="brand[all]" id="b-all" value="all" />
										<label for="b-all">'._('Toutes les marques').'</label>
									</a>
						';

						// Récupère les valeurs possibles
						while( $brand = ria_mysql_fetch_array($rbrand) ){
							$checked = isset($_GET['brand']) && in_array($brand['id'], $_GET['brand']) ? 'checked="checked"' : '';
							print '
								<a name="b-'.$brand['id'].'">
									<input '.$checked.' type="checkbox" name="brand[]" id="b-'.$brand['id'].'" value="'.$brand['id'].'" />
									<label for="b-'.$brand['id'].'">'.htmlspecialchars( $brand['title'] ).'</label>
								</a>
							';
						}

						print '
								</div>
							</div>
						';
					}
				}

				if( ria_mysql_num_rows($rfilter) ){
					while( $filter = ria_mysql_fetch_assoc($rfilter) ){
						$vals = fld_fields_get_values_array( $filter['fld_id'], $_GET['cat'], true, true, false, false, true );
						if( !is_array($vals) || !sizeof($vals) ){
							continue;
						}

						$check_all = false;
						$label = _('Aucune valeur sélectionnée');
						if( isset($_GET['field'][$filter['fld_id']]) ){
							$get_vals = $_GET['field'][$filter['fld_id']];
							if( sizeof($vals)!=(isset($get_vals['all']) ? sizeof($get_vals)-1 : sizeof($get_vals)) ){
								$label = '';
								foreach( $get_vals as $v ){
									$label .= ( trim($label)!='' ? ', ' : '' ).$v;
								}

								$label = strlen($label)>39 ? substr( $label, 0, 35 ).'...' : $label;
							}else{
								$label = _('Tous');
								$check_all = true;
							}
						}

						print '
							<div class="riapicker prd-filters flt-fld-'.$filter['fld_id'].'" style="margin-bottom: 10px;">
								<div class="selectorview">
									<div class="left">
										<span class="function_name">'.htmlspecialchars( $filter['fld_name'] ).'</span><br/>
										<span class="view">'.$label.'</span>
									</div>
									<a class="btn" name="btn">
										<img src="/admin/images/stats/fleche.gif" height="8" width="16" alt="" />
									</a>
									<div class="clear"></div>
								</div>
								<div class="selector">
									<a name="all">
										<input '.( $check_all ? 'checked="checked"' : '' ).' class="filter-all" type="checkbox" name="field['.$filter['fld_id'].'][all]" id="f-'.$filter['fld_id'].'-all" value="all" />
										<label for="f-'.$filter['fld_id'].'-all">'._('Tous').'</label>
									</a>
						';

						// Récupère les valeurs possibles
						foreach( $vals as $k=>$t ){
							$checked = isset($_GET['field'][$filter['fld_id']]) && in_array($t, $_GET['field'][$filter['fld_id']]) ? 'checked="checked"' : '';
							print '
								<a name="f-'.$filter['fld_id'].'-'.$k.'">
									<input '.$checked.' type="checkbox" name="field['.$filter['fld_id'].'][]" id="f-'.$filter['fld_id'].'-'.$k.'" value="'.htmlspecialchars( $t ).'" />
									<label for="f-'.$filter['fld_id'].'-'.$k.'">'.htmlspecialchars( $t ).'</label>
								</a>
							';
						}

						print '
								</div>
							</div>
						';
					}
				}

				print '
						<div class="clear"></div>
				';
			}

			print '</form>';
		}else{
			$products = false;
			unset($products_childs);
			$nb_products = 0;
		}

		if( !isset($total_products) ){
			$total_products = 0;
		}

		if( !isset($config['ar_cols_prd']) ){
			load_list_cols_products();
		}

		if( $total_products <= 0 && $products ){

			$cat_add_prd = '?';
			if( isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat'] > 0 ){
				$cat_add_prd = '?cat='.$_GET['cat'];
			}

			print '<form action="/admin/catalog/index.php'.$cat_add_prd.'" method="post">'
				.'<div class="small-insert-holder">'
					.'<div class="small-insert-content">'
						.'<span class="small-insert-icon-cat"></span>'
						.'<div class="small-insert-title">'._('Il n\'y a pas de produits dans cette catégorie').'</div>'
					.'</div>'
				.'</div>'
				.'<div class="small-insert-btn-holder">'
					.'<div class="float-left">'
						.'<input type="button" name="import" value="'._('Importer').' "
							title="'._('Importer des produits').'"
							onclick="window.location.href =\'../tools/imports/index.php?imp-class=3\';"
						/>'
						.( $categories!==false && ria_mysql_num_rows($categories) ? ' <input type="submit" name="export" id="export-products" value="'._('Exporter').'" title="'._('Exporter les produits des sous-catégories').'" />' : '' )
					.'</div>'
					.'<div class="float-right small-insert-new-obj obj-prd">'
						.'<div class="notice-prdname-create item-tooltip"
								title="'._('Astuce : saisissez une référence existante pour classer le produit dans cette catégorie.').'"
						></div>'
						.'<input type="text" name="prdname" id="prdname" value="" placeholder="'._('Saisir la désignation d\'un nouveau produit').'" />'
						.'<input type="submit" disabled="disabled" name="addprd" id="prdname-create" class="btn-main small-insert-btn-create" value="'._('Créer').'" />'
					.'</div>'

					.'<input class="btn-main small-insert-btn-create mobile" type="submit" name="" value="'._('Créer un produit').'" />'
				.'</div>'
			.'</form>';

		}else{
			if( $show_table_prd ){
				$pages = ceil( $total_products / 100 );
				$action_list_products = 'index.php';

				if( isset($_GET['new']) && $_GET['new'] ){
					$action_list_products .= '?new=1';
				}elseif ($_GET['cat']>0){
					$action_list_products .= '?cat='.$_GET['cat'].'';
				}

				if ($_GET['brd']>0){
					$action_list_products .= '?brd='.$_GET['brd'].'';
				}
				?>
				<div class="clear"></div>
				<form action="<?php print $action_list_products; ?>" method="post" id="form-catalog-produits" <?php print $total_products > 100 ? 'class="lazyload"' : ''; ?>>

				<?php if( $_GET['cat']>0 ){ ?>
					<input type="hidden" name="srccat" value="<?php print $_GET['cat']; ?>" />
				<?php } ?>

				<?php
					// Menu permettant la personnalisation des colonnes de la liste des produits
					$checked = 'checked="checked"';
					foreach( $config['ar_cols_prd'] as $col ){
						if( !$col['default'] ){
							$checked = '';
							break;
						}
					}

					print '
						<div class="with-cols-options"> <a href="#" id="display-cols-options" class="edit-cat"> '._('Personnaliser colonnes').'</a>
						<span class="menu-cols none">
							<span class="cols">
								<span class="col">
									<input '.$checked.' type="checkbox" name="cols-all" id="cols-all" value="all" />
									<label for="cols-all">'._('Toutes les colonnes').'</label>
								</span>
								<span class="col separate"></span>
					';

					foreach( $config['ar_cols_prd'] as $col ){
						$checked = $class = '';
						if( $col['default'] ){
							$checked = 'checked="checked"';
							$class = ' checked';
						}

						print '
							<span class="col'.$class.'">
								<input '.$checked.' type="checkbox" name="cols-'.$col['code'].'" id="cols-'.$col['code'].'" value="'.$col['code'].'" />
								<label for="cols-'.$col['code'].'">'.htmlspecialchars( $col['name'] ).'</label>
							</span>
						';
					}

					print '
							</span>
						</span>
						</div>
					';
				?>
				<div class="table-layout-large">
					<table id="products" class="js-classified checklist list-cols-changed">
						<thead>
							<tr><?php
								$url = '/admin/catalog/index.php?cat='.$_GET['cat'];

								print '
									<th id="prd-sel" class="minpadding prd-col-nomove col-check" data-label="Cocher tout :">
										<input type="checkbox" class="checkbox" onclick="checkAllClick(this)" />
									</th>
									<th id="prd-is-sync" class="minpadding col-sync"></th>
								';

								// Affichage des entêtes de colonnes
								foreach( $config['ar_cols_prd'] as $col ){
									print '
										<th class="thead-none '.( $col['default'] ? 'th-col-show' : 'th-col-hide' ).'" id="prd-'.$col['code'].'">
											<a href="#">'.htmlspecialchars( _($col['name']) ).'</a>
										</th>
									';
								}
								if( (isset($sort['type']) && $sort['type']==SORT_PERSO) || $sort==array("brd_pos"=>"asc")){
									print '
										<th id="sort-perso" class="th-col-show thead-none" >'._('Déplacer').'</th>
									';
								}


							?></tr>
						</thead>
						<tbody><?php
							$have_products = false;
							if( isset($products) ){
								$have_products = ria_mysql_num_rows($products)>0;
								print view_products_list( $products, $sort );
							}

							// Si un tri personnalisé est mis en place et que l'on affiche les produits des catégories enfants
							if( isset($products_childs) && ria_mysql_num_rows($products_childs) ){
								print view_products_list($products_childs, $sort);
								$have_products = true;
							}
						?></tbody>
					</table>

					<div id="lazy-load-products" data-rowstart="100"><?php
						print str_replace(
							[ '#nb#', '#nbtotal#' ],
							[ '<span class="nbproducts">100</span>', '<span class="nbtotalproduts">'.$str_nb_products.'</span>'],
							_('Afficher plus de produits (#nb# / #nbtotal#)')
						);
					?></div>
				</div>
				<table id="foot-products" class="checklist">
					<tbody><tr class="none"><td></td></tr></tbody>
					<tfoot>
						<tr>
							<td class="align-left">

								<?php if( isset($_GET['brd']) && $_GET['brd']>0 ) { ?>
									<input type="hidden" name="brd" id="brd" value="<?php print $_GET['brd']; ?>" />
								<?php }

								if( $_GET['cat']>0 && !$cat_load_by_pmt && gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_ADD')){ ?>
									<div class="form-add-new-prd small-insert-new-obj obj-prd">
										<div class="notice-prdname-create item-tooltip"
											title="<?php print _('Astuce : saisissez une référence existante pour classer le produit dans cette catégorie.'); ?>"
										></div>
										<input type="text" name="prdname" id="prdname" value=""
											placeholder="<?php print _('Saisir la désignation d\'un nouveau produit'); ?>" />
										<input type="submit" disabled="disabled" name="addprd" id="prdname-create"
											class="btn-main small-insert-btn-create"
											value="<?php print _('Créer'); ?>" />
									</div>
									<?php
								}

								if( $have_products ){
									if(gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_PUBLISH')){ ?>
										<input class="js-input-disabled none" type="submit" name="publish" value="<?php print _('Publier')?>" title="<?php print _('Publier les produits sélectionnés')?>" onclick="return window.confirm('<?php print _('Une fois publiés, les produits sélectionnés pourront être commandés par vos clients.').'\n'._('Etes vous sûr(e) de vouloir continuer ?')?>');" disabled="disabled" />
										<input class="js-input-disabled none" type="submit" name="unpublish" value="<?php print _('Dépublier')?>" title="<?php print _('Ne plus publier les produits sélectionnés')?>" onclick="return window.confirm('<?php print _('Une fois retirés, les produits sélectionnés ne pourront plus être commandés par vos clients.').'\n'._('Etes vous sûr(e) de vouloir continuer ?')?>');" disabled="disabled" /><?php
									}
									if (!$cat_load_by_pmt){
										if(gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_DUPLICATE')) {?>
											<input class="js-input-disabled none" type="submit" name="duplicate" value="<?php print _('Dupliquer')?>" title="<?php print _('Dupliquer les produits sélectionnés')?>" disabled="disabled" /><?php
										}
										if(gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_CLASSIFY')){ ?>
											<input class="js-input-disabled none" type="submit" name="move" id="js-move-products" value="<?php print _('Déplacer')?>" title="<?php print _('Déplacer les produits sélectionnés vers une autre catégorie')?>" disabled="disabled" /><?php
										}
									}
									if(gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_CLASSIFY')){ ?>
										<input class="js-input-disabled none" type="submit" name="classify" id="js-classify-products" value="<?php print _('Classer')?>" title="<?php print _('Classer les produits sélectionnés dans une autre catégorie (le classement actuel sera conservé).')?>" disabled="disabled" /><?php
										if (!$cat_load_by_pmt){?>
											<input class="js-input-disabled none" type="submit" name="unclassify" value="<?php print _('Déclasser')?>" title="<?php print _('Déclasser les produits de cette catégorie, cette action n\'aura aucun impact sur les classements provenant de votre gestion commerciale.')?>" disabled="disabled" /><?php
										}
									}
									if(gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_DEL')){?>
										<input class="js-input-disabled none" type="submit" name="delete" value="<?php print _('Supprimer')?>" title="<?php print _('Supprimer les produits sélectionnés')?>" onclick="return window.confirm('<?php print _('Vous êtes sur le point de supprimer les produits sélectionnés.').'\n'._('Cette opération est irréversible et ne pourra pas être annulée.').'\n'._('Etes vous sûr(e) de vouloir continuer ?')?>');" disabled="disabled" /><?php
									}
									if ( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_CLASSIFY') && $_GET['new'] === '1' ){?>
										<input type="submit" name="remove-new" id="remove-new" value="<?php print _('Enlever des nouveautés')?>" title="<?php print _('Enlever les produits séléctionnnés de la liste des nouveautés')?>" /><?php
									}
								}

								if (!$cat_load_by_pmt){ ?>
								<input type="button" name="import" value="<?php print _('Importer')?>" title="<?php print _('Importer des produits')?>" onclick="window.location.href = '../tools/imports/index.php?imp-class=1';" />
								<input type="submit" name="export" id="export-products" value="<?php print _('Exporter')?>" title="<?php print _('Exporter les produits de la catégorie')?>" />
									<?php
								}
								if( tnt_tenants_have_websites() ){ ?>
								<input type="button" name="export-referencing" id="export-referencing" value="<?php print _('Exporter référencement')?>" onclick="exportreferencing(<?php echo $_GET['cat']; ?>)" title="<?php print _('Exporter les référencements des produits de cette catégorie')?>"/>
								<?php } ?>
							</td>
						</tr>
						<?php
							$models = fld_models_get( 0, 0, CLS_PRODUCT );
							if( $have_products && ria_mysql_num_rows($models)  ){
						?>
						<tr>
							<td class="tfoot-grey tfoot-grey--border">
								<label for="model"><?php print _('Appliquer le modèle suivant aux produits sélectionnés :'); ?></label>
								<select name="model" id="model">
									<option value=""><?php print _('Choisir un modèle'); ?></option>
									<?php
										while( $m = ria_mysql_fetch_array($models) ){
											print '<option value="'.$m['id'].'">'.htmlspecialchars($m['name']).'</option>';
										}
									?>
								</select>
								<input type="submit" name="apply-model" value="<?php print _('Appliquer')?>" title="<?php print _('Appliquer le modèle')?>" />
							</td>
						</tr>
						<?php }
						if( $have_products ){ ?>
						<tr>
							<td class="tfoot-grey tfoot-grey--border">
								<?php if ($_GET['cat'] > 0){ ?>
									<div class="riashop-catalog__cat__foot__section riashop-catalog__cat__foot__section--cat-sort">
										<label for="prd-sort-type"><?php print _('Trier les produits de la catégorie par ordre :'); ?></label>
										<select name="prd-sort-type" id="prd-sort-type" onchange="displaySortDir();">
											<option value="">&nbsp;</option>
											<?php
												$rtype = sys_sort_types_get();
												if( $rtype ){
													while( $type = ria_mysql_fetch_array($rtype) ){
														if( $cat_load_by_pmt && $type['id'] == SORT_PERSO ){
															continue;
														}

														print '<option value="'.$type['id'].'" '.( $sort['type']==$type['id'] ? 'selected="selected"' : '' ).'>'._( $type['name'] ).'</option>';
													}
												}
											?>
										</select>

										<select name="sort-prd-dir" id="sort-prd-dir" <?php print in_array($sort['type'], array(SORT_PERSO,SORT_RANDOM)) ? 'style="display:none;"' : ''; ?>>
											<option value="asc" <?php print $sort['dir']=='asc' ? 'selected="selected"' : ''; ?>><?php print _('Croissant')?></option>
											<option value="desc" <?php print $sort['dir']=='desc' ? 'selected="selected"' : ''; ?>><?php print _('Décroissant')?></option>
										</select>
									</div>

									<?php if( prd_categories_have_childs($_GET['cat']) ){ ?>
										<div class="riashop-catalog__cat__foot__section riashop-catalog__cat__foot__section--to-children">
											<label for="sort-prd-inherited"><?php print _('Appliquer la règle de tri aux catégories enfants :'); ?></label>
											<input type="checkbox" name="sort-prd-inherited" id="sort-prd-inherited" value="" <?php print $sort['is_recursive'] ? 'checked="checked"' : '' ?> />
										</div>
									<?php } ?>

									<div class="riashop-catalog__cat__foot__section riashop-catalog__cat__foot__section--apply">
										<input type="submit" name="orderbyprd" id="orderbyprd" value="<?php print _('Appliquer'); ?>" />
										<?php print $sort['inherited']!=$_GET['cat'] ? '<span class="sort-inherited">('._('Tri hérité d\'une des catégories parentes').')</span>' : ''; ?>
									</div>
								<?php } ?>
							</td>
						</tr>
						<?php if ($_GET['brd'] > 0){ ?>
							<tr>
								<td class="tfoot-grey tfoot-grey--border">
									<div class="riashop-catalog__cat__foot__section riashop-catalog__cat__foot__section--brd-sort">
										<label for="prd-sort-type"><?php print _('Trier les produits de la marque par ordre :'); ?></label>
										<div class="riashop-catalog__cat__foot__group">
											<div class="riashop-catalog__cat__foot__group__radio">
												<input type="radio" class="radio" name="order_brands" id="order-0" value="0" <?php if( !$ordered_brands ) print 'checked="checked"'; ?> />
												<label for="order-0"><?php print _('Alphabétique')?></label>
											</div>
											<div class="riashop-catalog__cat__foot__group__radio">
												<input type="radio" class="radio" name="order_brands" id="order-1" value="1" <?php if( $ordered_brands ) print 'checked="checked"'; ?> />
												<label for="order-1"><?php print _('Personnalisé')?></label>
											</div>
										</div>
										<input type="submit" name="orderby_brands" value="<?php print _('Appliquer')?>" />
									</div>
								</td>
							</tr>
						<?php } ?>
						<?php } ?>
					</tfoot>
				</table>
				</form>
				<?php
			}else{
				if( isset($_SESSION['save-model-all-prd']) ){
					print '<div class="success">'._('Le modèle a bien été appliqué à tous les articles').'</div>';
					unset($_SESSION['save-model-all-prd']);
				}

				print '
					<h2>'._('Produits').'</h2>
				';

				// Affiche une notice dans le cas où des articles non classés existeraient
				$childonly = isset($_SESSION['usr_admin_show_childs']) ? $_SESSION['usr_admin_show_childs'] : true;
				$r_unclassified = prd_products_get_simple(0, '', false, -1, false, false, false, false, array('childs' => $childonly));
				if( $r_unclassified && ria_mysql_num_rows($r_unclassified) ){
					print '<div class="notice">'.str_replace(
						'#param[url]#',
						'/admin/catalog/unclassified.php',
						_('Des articles non classés sont présents dans la rubrique <a href="#param[url]#">Catalogue > Non classés</a>.')
					).'</div>';
				}

				print '
					<form action="index.php?cat='.$_GET['cat'].'&amp;new='.$_GET['new'].'" method="post">
						<div>
							<input type="submit" name="export" id="export-products" value="'._('Exporter tous les produits').'" title="'._('Exporter tous les produits au format Excel ou CSV').'" class="btn-main" />
						</div>
				';

				$models = fld_models_get( 0, 0, CLS_PRODUCT );
				if( ria_mysql_num_rows( $models )>0 ){
					print '
						<div>
							<label for="model">'._('Appliquer le modèle suivant à tous les produits').' :</label>
							<select name="model" id="model">
								<option value="">&nbsp;</option>
					';
					while( $m = ria_mysql_fetch_assoc($models) ){
						print '<option value="'.$m['id'].'">'.htmlspecialchars( $m['name'] ).'</option>';
					}

					print '
							</select>
							<input type="submit" name="apply-model" value="'._('Appliquer').'" title="'._('Appliquer le modèle à tous les produits').'" />
						</div>
					';
				}
				print '
					</form>
				';

				if( tnt_tenants_have_websites() ){
					print '<div>
							<input type="submit" name="export-referencing" id="export-referencing" value="'._('Exporter le référencement produits').'" onclick="exportreferencing(0)" title="'._('Exporter les référencements').'" />
						</div>';
				}

			}
		}
	}

	print '<div class="clear"></div>';

	print '
		<script><!--
			var ordered = '.json_encode($ordered).';
			var cat = '.$_GET['cat'].';
			var brd = '.$_GET['brd'].';
		--></script>
	';

	if( $prc_session!==null ){
		$_SESSION['usr_prc_id'] = $prc_session;
	}

	if( $discount_session!==null ){
		$_SESSION['usr_discount'] = $discount_session;
	}


	require_once('admin/skin/footer.inc.php');

