<?php
	require_once('prd/classify.inc.php');
	require_once('rewrite.inc.php');

	$error = false;
	$content = '';

	// Chargement des redirections 301
	if( isset($_GET['load']) ){
		$ar_redirect = [];

		if( isset($_GET['prd']) ){
			$r_cly = prd_classify_get( false, $_GET['prd'] );

			if( $r_cly ){
				$ar_cats = [];
				$ar_urls_cly = [];

				while( $cly = ria_mysql_fetch_assoc($r_cly) ){
					$ar_cats[] = $cly;
					$ar_urls_cly[] = $cly['url'];

					$cat_title = prd_categories_get_name( $cly['cat'], true );

					$r_from_url = rew_rewritemap_get( '', $cly['url'], 301, $config['i18n_lng'], false, '', 'both', CLS_PRODUCT );
					if( $r_from_url ){
						while( $from_url = ria_mysql_fetch_assoc($r_from_url) ){
							if( array_key_exists( $from_url['url_id'], $ar_redirect) ){
								continue;
							}

							$ar_redirect[ $from_url['url_id'] ] = [
								'cat_id' => $cly['cat'],
								'title' => $cat_title,
								'url_id' => $from_url['url_id'],
								'url' => $cly['url'],
								'dest' => $from_url['intern'],
								'wst_id' => $from_url['wst_id'],
								'wst_name' => $from_url['wst_id'] > 0 ? wst_websites_get_name( $from_url['wst_id'] ) : _('Tous')
							];
						}
					}
				}
			}
		}

		ob_start();

		if( !count($ar_redirect) ){
			print '<tr id="no-url">'
				.'<td headers="url" colspan="3">'._('Aucune URL de redirection enregistrée').'</td>'
			.'</tr>';
		} else {
			$count = 1;

			foreach( $ar_redirect as $redirect ){
				$rparent = prd_categories_parents_get($redirect['cat_id']);

				// récupération de la hiérarchie pour l'affichage
				$hierarchy = array();
				if ($rparent && ria_mysql_num_rows($rparent)) {
					while ($parent = ria_mysql_fetch_array($rparent)){
						$hierarchy[] = $parent['title'];
					}
				}
				$hierarchy[] = $redirect['title'];

				$text = implode(' » ', $hierarchy );

				print '<tr id="url-'.$count.'">'
					.'<td headers="url" data-label="'._('Classement du produit :').'">'
						.'<strong>'.htmlspecialchars( $text ).'</strong>'
						.'<br />'._('URL :').' '.htmlspecialchars($redirect['url'])
						.'<br />'._('Site : ').' '.htmlspecialchars( $redirect['wst_name'] )
					.'</td>'
					.'<td headers="url" data-label="'._('Redirection 301 vers... :').'">'.htmlspecialchars($redirect['dest']).'</td>'
					.'<td headers="action" class="td-action">'
						.'<a class="del edit button" onclick="return delUrlRedirection('.$_GET['prd'].', '.$redirect['cat_id'].', '.$redirect['url_id'].');">'
							._('Supprimer')
						.'</a>'
					.'</td>'
				.'</tr>';

				// Recherche d'une URL (code 200)
				$r_url200 = rew_rewritemap_get( '', $redirect['url'], 200, false, $redirect['wst_id'] );
				if( $r_url200 && ria_mysql_num_rows($r_url200) ){
					$url200 = ria_mysql_fetch_assoc( $r_url200 );

					print '<tr>'
						.'<td colspan="3" class="error">'
							._('Une URL (code 200) est en place et empêche la redirection 301 de fonctionner.')
							.' <a href="#" onclick="return delUrlByID('.$url200['url_id'].', '.$_GET['prd'].');">'._('Supprimer').'</a>'
						.'</td>'
					.'</tr>';
				}

				$count++;
			}
		}

		// Formulaire d'ajout d'une redirection
		$ar_urls_cly = array_unique( $ar_urls_cly );

		print '<tr id="form-add-301" class="hide">'
			.'<td>'
				.'<select name="add-301-cly">'
					.'<option value="">Choisissez un classement</option>';

				foreach( $ar_cats as $one_cat ){
					print '<option value="'.$one_cat['cat'].'">'.prd_categories_get_name( $one_cat['cat'] ).' (URL: '.$one_cat['url'].')</option>';
				}

				print '</select>'
			.'</td>'
			.'<td>'
				.'<input type="text" name="add-301-url" value="" />'
			.'</td>'
			.'<td>'
				.'<input type="submit" name="save-add-301-url" value="'._('Ajouter').'" />'
			.'</td>'
		.'</tr>';



		$content = ob_get_clean();
	}

	// Suppression d'une redirection 301 en place sur un classement de produit
	if( isset($_POST['del']) ){
		if( !isset($_POST['prd']) || !is_numeric($_POST['prd']) || $_POST['prd'] <= 0 ){
			$error = _('Aucun produit identifié');
		}elseif( !isset($_POST['cat']) || !is_numeric($_POST['cat']) || $_POST['cat'] <= 0 ){
			$error = _('Aucune catégorie identifiée');
		}elseif( !isset($_POST['urlID']) || !is_numeric($_POST['urlID']) || $_POST['urlID'] <= 0 ){
			$error = _('Aucune URL identifiée');
		}else{
			// Recherche le classement concerné par la redirection 301
			$r_cly = prd_classify_get( false, $_POST['prd'], $_POST['cat'] );

			if( !$r_cly || !ria_mysql_num_rows($r_cly) ){
				$error = _('Le classement n\'a pas été trouvé');
			}else{
				$cly = ria_mysql_fetch_assoc( $r_cly );
				$rew = ria_mysql_fetch_assoc( rew_rewritemap_get('', '', 0, false, false, '', 'both', 0, 0, $_POST['urlID'] ) );

				$r_page = cfg_urls_get( $rew['wst_id'], CLS_PRODUCT );
				if( !$r_page || !ria_mysql_num_rows($r_page) ){
					$error = _('Une erreur inattendue s\'est produite lors de la suppression de la redirection 301.');
				}else{
					$page = ria_mysql_fetch_assoc( $r_page );
				}

				$intern = $page['url'].'?cat='.$rew['obj_id_0'].'&prd='.$_POST['prd'];
				if( isset($config['url_no_category']) && $config['url_no_category'] ){
					$intern = $page['url'].'?cat=0&prd='.$_POST['prd'];
				}

				// Suppression de la redirection 301 en rétablissant la 200
				if( !rew_rewritemap_update($_POST['urlID'], 200, $intern) ){
					$error = _('Une erreur inattendue s\'est produite lors de la suppression de la redirection 301.');
				}else{
					// Supprime les clés memecached pouvant être liés
					$r_website = wst_websites_languages_get();
					if( $r_website ){
						while( $website = ria_mysql_fetch_assoc($r_website) ){
							@$memcached->delete( 'rew_rewrite-'.$config['tnt_id'].'-'.$website['wst'].'-'.$website['lng_code'].'-'.$cly['url'] );
						}
					}
				}
			}
		}

		if( $error ){
			$content = $error;
		}
	}

	// Suppression d'une URL par son ID
	if( isset($_POST['del-by-id']) ){
		if( !isset($_POST['urlID']) || !is_numeric($_POST['urlID']) || $_POST['urlID'] <= 0 ){
			$error = _('Aucune URL identifiée');
		}else{
			$r_url = rew_rewritemap_get( '', '', 0, false, false, '', 'both', 0, 0, $_POST['urlID'] );

			if( !$r_url || (ria_mysql_num_rows($r_url) != 1) ){
				$error = _('Aucune URL identifiée');
			}else{
				$url = ria_mysql_fetch_assoc( $r_url );

				// Suppression de la redirection 301
				if( !rew_rewritemap_del('', '', false, null, $_POST['urlID']) ){
					$error = _('Une erreur inattendue s\'est produite lors de la suppression de la redirection 301.');
				}else{
					// Supprime les clés memecached pouvant être liés
					$r_website = wst_websites_languages_get();
					if( $r_website ){
						while( $website = ria_mysql_fetch_assoc($r_website) ){
							@$memcached->delete( 'rew_rewrite-'.$config['tnt_id'].'-'.$website['wst'].'-'.$website['lng_code'].'-'.$url['extern'] );
						}
					}
				}
			}
		}

		if( $error ){
			$content = $error;
		}
	}

	print json_encode([
		'success' => $error ? '0' : '1',
		'content' => $content
	]);


	exit;

	header("Content-Type: application/xml");
	$error = false;
	$xml = '<?xml version="1.0" encoding="utf-8"?>';

	// Enregistre les modifications sur les redirections
	if( isset($_POST['save-maj-url'], $_POST['url_id']) && $_POST['url_id']>0 ){
		$id = $_POST['url_id'];
		if( substr($_POST['url-'.$id], 0, 1)!='/' ){
				$xml .= '<result type="0"><error>';
				$xml .= '<![CDATA['._('Une erreur est survenue lors de la mise à jour d\'une url de redirection.').'<br />'._('Le premier caractère ne peut être qu\'un \'/\'.').']]>';
				$xml .= '</error>\n</result>';
				$error = true;
		} elseif( $_POST['url-'.$id]!=$_POST['url_old-'.$id] && rew_rewritemap_exists($_POST['url-'.$id]) ){ // On vérifie que la nouvelle url n'est pas déjà utilisée
			$xml .= '<result type="0"><error>';
			$xml .= '<![CDATA['._('Une erreur est survenue lors de la mise à jour d\'une url de redirection.').'<br />'._('L\'url saisie est déjà utilisée.').']]>';
			$xml .= '</error>\n</result>';
			$error = true;
		} elseif( trim($_POST['url-'.$id]=='' || $_POST['url-'.$id]=='/') ){
			$xml .= '<result type="0"><error>';
			$xml .= '<![CDATA['._('Une erreur est survenue lors de la mise à jour d\'une url de redirection.').'<br />'._('La nouvelle url ne doit pas être vide ni contenir un seul.').']]>';
			$xml .= '</error>\n</result>';
			$error = true;
		} else {
			if( !rew_rewritemap_extern_update($_POST['url_old-'.$id], $_POST['url-'.$id]) ){
				$xml .= '<result type="0"><error>';
				$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de la mise à jour de l\'url de redirection.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
				$xml .= '</error>\n</result>';
				$error = true;
			}
		}

		if( !$error ){
			$xml .= '<result type="1">';
			$xml .= '<cdt-line><![CDATA[';
			$xml .= ']]></cdt-line>';
			$xml .= '</result>';
		}
	}

	// Ajout d'une URL de redirection
	if( isset($_POST['add-url'], $_POST['url-0']) && trim($_POST['url-0'])!='' ){
		if( substr($_POST['url-0'], 0, 1)!='/' ){
				$xml .= '<result type="0"><error>';
				$xml .= '<![CDATA['._('Le premier caractère de la nouvelle redirection ne peut être qu\'un \'/\'.').']]>';
				$xml .= '</error>\n</result>';
				$error = true;
		} elseif( rew_rewritemap_exists($_POST['url-0']) ){ // On vérifie que la nouvelle url n'est pas déjà utilisée
				$xml .= '<result type="0"><error>';
				$xml .= '<![CDATA['._('Une erreur est survenue lors de la mise à jour d\'une url de redirection.').'<br />'._('La nouvelle url est déjà utilisée.').']]>';
				$xml .= '</error>\n</result>';
				$error = true;
		} else {
			$rurl = rew_rewritemap_get( $config['prd_pages'][''].'?cat='.$_POST['cat'].'&prd='.$_POST['prd'], '', 200 );
			if (!$rurl || !ria_mysql_num_rows($rurl)) {
				$rurl = rew_rewritemap_get( $config['prd_pages'][''].'?cat=0&prd='.$_POST['prd'], '', 200 );
			}

			if( $rurl ){
				while( $url = ria_mysql_fetch_array($rurl) ){
					if( !rew_rewritemap_redirection_add($_POST['url-0'], $url['extern'], 0, false, CLS_PRODUCT, array($_POST['cat'], $_POST['prd']) ) ){
						$xml .= '<result type="0"><error>';
						$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de l\'ajout de l\'url de redirection.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
						$xml .= '</error>\n</result>';
						$error = true;
					}
				}
			}
		}

		if( !$error ){
			$xml .= '<result type="1">';
			$xml .= '<cdt-line><![CDATA[';
			$xml .= ']]></cdt-line>';
			$xml .= '</result>';
		}
	}

	if( isset($_POST['del'], $_POST['url']) ){
		if( !rew_rewritemap_redirection_del($_POST['url']) ){
			$xml .= '<result type="0"><error>';
			$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de la suppression de l\'url de redirection.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
			$xml .= '</error>\n</result>';
			$error = true;
		} else {
			$xml .= '<result type="1">';
			$xml .= '<cdt-line><![CDATA[';
			$xml .= ']]></cdt-line>';
			$xml .= '</result>';
		}
	}

	print $xml;

