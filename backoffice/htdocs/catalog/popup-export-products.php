<?php

	/**	\file popup-export-products.php
	 *	Ce fichier est utilisé pour l'exportation du catalogue des produits. Il est affiché dans le back-office sous forme de popup.
	 * 	Il permet de paramétrer le format et les colonnes de l'export.
	 * 	L'export est ensuite enregistré par le fichier export-products.php puis confié à un worker pour exécution.
	 * 	L'export effectif est réalisé par la fonction prd_exports_generated.
	 */

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT');

	require_once('products.inc.php');

	if( !isset($_GET['export']) && !isset($_GET['cat']) && !isset($_GET['brd']) ){
		$g_error = _("Une erreur inattendue s'est produite lors du chargement des informations.");
	}

	$no_edit = isset($_REQUEST['no-edit']) && $_REQUEST['no-edit'] ? true : false;

	if( !isset($g_error) ){
		$export = array(
			'pxp_id' => 0,
			'name' => '',
			'is_auto' => false,
			'period' => '',
			'period_info' => ''
		);

		$thumb_main = $thumb_second = '';
		$default = array( 'ref', 'name', 'title' );

		if( isset($_GET['export']) ){
			if( !prd_exports_exists($_GET['export']) ){
				$g_error = _("Une erreur inattendue s'est produite lors du chargement des informations.");
			}else{
				$export = ria_mysql_fetch_array( prd_exports_get($_GET['export']) );

				$_GET['cat'] = $export['cat_id'];

				$info_export = json_decode( $export['columns'], true );
				$default = isset($info_export['cols']) ? array_keys( $info_export['cols'] ) : array();
				$fields  = isset($info_export['flds']) ? array_keys( $info_export['flds'] ) : array();

				if( isset($info_export['thumb_main']) ){
					$thumb_main = $info_export['thumb_main'];
				}

				if( isset($info_export['thumb_second']) ){
					$thumb_second = $info_export['thumb_second'];
				}
			}
		}
		$cat = 0;
		if( isset($_GET['cat']) && is_numeric($_GET['cat'])){
			if( $_GET['cat'] && $_GET['cat'] == -1 ){
				$cat = -1;
			}elseif( prd_categories_exists($_GET['cat']) ){
				$cat = $_GET['cat'];
			}
		}
		$brand = isset($_GET['brd']) && prd_brands_exists($_GET['brd']) ? $_GET['brd'] : 0;

		$link = 'popup-export-products.php';
		if( $export['pxp_id'] ){
			$link .= '?export='.$export['pxp_id'];
		}else{
			$link = '?cat='.$cat.'&brd='.$brand;
		}

		// Enregistre un export produits
		if( isset($_POST['save-form-export']) ){
			if( !isset($_POST['col-filter']) || !is_array($_POST['col-filter']) || !sizeof($_POST['col-filter']) ){
				$error = _("Veuillez sélection une ou plusieurs informations incluses dans cet export");
			}elseif( !isset($_POST['export-name'], $_POST['export-auto'], $_POST['export-period'], $_POST['day'], $_POST['month']) ){
				$error = _("Une ou plusieurs informations sont manquantes.");
			}elseif( trim($_POST['export-name'])=='' ){
				$error = _("Veuillez renseigner le nom à donner à cet export.");
			}else{
				$period = $_POST['export-auto'] ? $_POST['export-period'] : '';
				$period_info = $period=='week' ? $_POST['day'] : ( $period=='month' ? $_POST['month'] : '' );

				$columns['cols'] = isset($_POST['col-filter']) && is_array($_POST['col-filter']) ? $_POST['col-filter'] : array();
				$columns['flds'] = isset($_POST['fld-filter']) && is_array($_POST['fld-filter']) ? $_POST['fld-filter'] : array();

				if( in_array('img_main', array_keys($columns['cols'])) ){
					$columns['thumb_main'] = $_POST['size-img']['img_main'];
				}
				if( in_array('img_second', array_keys($columns['cols'])) ){
					$columns['thumb_second'] = $_POST['size-img']['img_second'];
				}
				$catchilds = isset($_POST['catchilds']) ? true : false;
				$childonly = isset($_POST['childonly']) ? true : false;

				if( $export['pxp_id']>0 ){
					if( !prd_exports_update( $export['pxp_id'], $_POST['export-name'], $columns, $_POST['export-auto'] ? true : false, $period, $period_info, $catchilds, $childonly) ){
						$error = _("Une erreur inattendue s'est produite lors de la mise à jour de l'export.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
					}
				}else{
					if( !prd_exports_add($_POST['export-name'], $cat, $columns, $catchilds, $_POST['export-auto'] ? true : false, $period, $period_info, $childonly) ){
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'export.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
					}
				}
			}

			if( !isset($error) && $export['pxp_id']<=0 ){
				unset($_POST['save-form-export']);
				$success = true;
			}
		}

		// Exécute un export produits
		if( isset($_POST['export']) ){
			include('export-products.php?cat='.$cat);
			exit;
		}

		if( sizeof($_POST) ){
			$export = array(
				'pxp_id' => $export['pxp_id'],
				'name' => isset($_POST['export-name']) ? $_POST['export-name'] : $export['name'],
				'is_auto' => isset($_POST['export-auto']) ? ($_POST['export-auto'] ? true : false) : $export['is_auto'],
				'period' => isset($_POST['export-auto'], $_POST['export-period']) && $_POST['export-auto'] ? $_POST['export-period'] : $export['period'],
				'period_info' => $_POST['export-period']=='week' ? $_POST['day'] : ( $_POST['export-period']=='month' ? $_POST['month'] : $export['period_info'] ),
				'prd_ids' => isset($_POST['prd_ids']) ? $_POST['prd_ids'] : ''
			);

			$default = isset($_POST['col-filter']) && is_array($_POST['col-filter']) ? array_keys( $_POST['col-filter'] ) : array();
			$fields  = isset($_POST['fld-filter']) && is_array($_POST['fld-filter']) ? array_keys( $_POST['fld-filter'] ) : array();

			$thumb_main = isset($_POST['size-img']['img_main']) ? $_POST['size-img']['img_main'] : '';
			$thumb_second = isset($_POST['size-img']['img_second']) ? $_POST['size-img']['img_second'] : '';
		}
	}

	if (!isset($cat)){
		$cat = 0;
	}
	if (!isset($brand)){
		$brand = 0;
	}

	define('ADMIN_PAGE_TITLE', _('Export des produits') . ' - ' . _('Catalogue'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'popup-iframe');
	require_once('admin/skin/header.inc.php');
?>
	<div id="export">
		<?php if( isset($g_error) ){
			print '<div class="error">'.nl2br( $g_error ).'</div>';
		}else{
			print '
				<form action="'.$link.'" method="post">
					<input type="hidden" name="prd_ids" value="'.(isset($_GET['prd_ids']) ? htmlspecialchars($_GET['prd_ids']) : '').'" />
			';

			if( isset($error) ){
				print '<div class="error">'.nl2br( $error ).'</div>';
			}elseif( isset($success) ){
				print '<div class="success">'._('L\'enregistrement s\'est correctement déroulé.').'</div>';
			}
		?>
			<h2 style="position: fixed; top: 0px; left: 0px; right: 0px; background-color: white; padding: 10px; box-shadow: 0px -2px 8px #aaa; line-height: 41px; padding-left: 20px; height: 4em">
				<div class="<?php print $export['pxp_id']>0 && !$no_edit ? 'block-hide' : ''; ?> export-action">
					<input class="btn-action btn-main float-right" type="submit" name="export" value="<?php print _('Télécharger'); ?>" />
				</div>
				<span><?php print _('Gestion de l\'export')?></span>
				<div class="clear"></div>
			</h2>

			<div class="check-catchilds" style="margin-top: 5em">
				<?php foreach( $config['i18n_lng_used'] as $lng ){ ?>
					<input type="checkbox" name="lng[]" id="<?php echo 'lng_'.$lng?>" value="<?php echo $lng?>" <?php echo ($lng == $config['i18n_lng'] ? 'checked' : '')?>/>
					<label for="<?php echo 'lng_'.$lng?>"><?php echo i18n_languages_get_name($lng) ?></label>
				<?php }?>
			</div>

			<div class="check-catchilds">
				<input type="checkbox" name="for_excel" id="for_excel" value="1" />
				<label for="for_excel"><?php print _('Exporter pour Excel')?></label>
				<div class="clear"></div>
				<input type="checkbox" name="for_mac" id="for_mac" value="1" />
				<label for="for_mac"><?php print _('Exporter pour Excel (Mac)')?></label>
				<div class="clear"></div>
			</div>

			<div class="check-catchilds">
			<?php if( $cat ){ ?>
				<input type="checkbox" name="catchilds" id="catchilds" value="1" checked="checked" />
				<label for="catchilds"><?php print _('Inclure également le contenu des catégories enfants')?></label>
				<div class="clear"></div>
			<?php } ?>
				<input type="checkbox" name="childonly" id="childonly" value="1" />
				<label for="childonly" title="<?php print _('Inclure les articles enfants, quel que soit leur classement, des produits parents présents dans cette catégorie.')?>"><?php print _('Inclure tous les articles enfants liés à cette catégorie')?></label>
				<div class="clear"></div>
				<input type="checkbox" name="seo" id="seo" value="1" />
				<label for="seo" title="<?php print _('L\'export du référencement nécessite d\'exporter en même temps le classement.'); ?>"><?php print _('Inclure le référencement'); ?></label>
			</div>

			<span class="part-export">
				<?php print _('Informations :'); ?>
				<a href="#" class="check-all-col"><?php print _('Cocher tout')?></a> | <a href="#" class="uncheck-all-col"><?php print _('Décocher tout')?></a>
			</span>
			<div class="clear"></div>

			<?php
				$ar_cols = array(
					'classify'		 => _('Classement'),
					'is_sync'		 => _('Synchronisé ?'),
					'id'			 => _('Identifiant'),
					'ref' 			 => _('Référence'),
					'name' 			 => _('Désignation'),
					'title' 		 => _('Titre'),
					'brd_title' 	 => _('Marque'),
					'barcode'		 => _('Code EAN'),
					'orderable' 	 => _('Commandable'),
					'countermark' 	 => _('Contremarque'),
					'new' 			 => _('Nouveauté'),
					'sleep' 		 => _('Sommeil'),
					'follow_stock' 	 => _('Suivi en stock'),
					'stock'			 => _('Stock réel'),
					'stock_res'		 => _('Stock réservé'),
				);

				if( $cat == -1 ){
					unset($ar_cols['classify']);
				}

				$r_dps = prd_deposits_get();
				if( $r_dps ){
					while( $dps = ria_mysql_fetch_assoc($r_dps) ){
						$ar_cols[ 'dps-'.$dps['id'] ] = sprintf( _('Stock réel - %s'), $dps['name'] );
					}
				}

				$ar_cols = array_merge( $ar_cols, array(
					'desc' 			 => _('Description courte'),

					'desc-long'		 => _('Description longue'),
					'weight' 		 => _('Poids Brut'),
					'weight_net' 	 => _('Poids Net'),

					'price_ht' 		 => _('Prix HT'),
					'price_ttc' 	 => _('Prix TTC'),
					'price_weight_ht' => _('Prix HT/kg'),
					'price_weight_ttc' => _('Prix TTC/kg'),
					'tva_rate' 		 => _('TVA'),
					'promo' 		 => _('Prix en promotion'),
					'purchase_avg'	=> _('Prix d\'achat'),
					'gross_margin'	=> _('Marge brute'),
					'margin_rate'	=> _('Taux de marge'),
					'markup_rate'	=> _('Taux de marque'),

					'length' 		 => _('Longueur'),
					'width' 		 => _('Largeur'),
					'height' 		 => _('Hauteur'),
					'selled'		 => _('Nombre de ventes'),

					'publish'		 => _('Publié gescom'),
					'publish-site' 	 => _('Publié site'),
					'fields-missing' => _('Champs manquants'),
					'date_created' 	 => _('Date de création'),
					'date_modified'  => _('Date de dernière modification'),
					'date_published' => _('Date de première publication'),

					'img_main'		 => _('Image principale'),
					'img_second'	 => _('Image(s) secondaire(s)'),

					'url_alias'	 	 => _('Url produit')
				));

				// Ajout des relations entre produits à l'export
				$ar_cols = array_merge( $ar_cols, [
					'parents' => _('Article(s) parent(s)'),
					'childs' => _('Articles(s) enfant(s)')
				]);

				$r_type = prd_relations_types_get();
				if( $r_type ){
					while( $type = ria_mysql_fetch_assoc($r_type) ){
						$ar_cols[ 'rel-'.$type['id'] ] = $type['name'];
					}
				}

				// Si le compte n'a pas accès aux marges, il ne peut exporter le prix d'achat
				if( !gu_user_is_authorized('_RGH_ADMIN_MARGE_SHOW') ){
					unset($ar_cols['purchase_avg']);
				}

				print '
					<div class="cols">
				';

				$i = 0;
				foreach( $ar_cols as $key=>$name ){
					$checked = in_array( $key, $default ) ? 'checked="checked"' : '';

					$sizes = '';
					switch( $key ){
						case 'img_main' :
						case 'img_second' : {
							if( trim($sizes)=='' ){
								foreach( $config['img_sizes'] as $code=>$size ){
									$config['img_sizes'][$code]['score'] = (int) $size['width'] * (int) $size['height'];
								}

								//$sizes .= '<option value="origin">'._('Fichier original').'</option>';

								$tsizes = array_msort( $config['img_sizes'], array('width'=>SORT_DESC, 'height'=>SORT_DESC) );
								foreach( $tsizes as $s ){
									$selected = '';
									if( $key=='img_main' && $thumb_main==$s['width'].'x'.$s['height'] ){
										$selected = 'selected="selected"';
									}elseif( $key=='img_second' && $thumb_second==$s['width'].'x'.$s['height'] ){
										$selected = 'selected="selected"';
									}

									$sizes .= '<option '.$selected.' value="'.$s['width'].'x'.$s['height'].'">'.$s['width'].'x'.$s['height'].' '._('pixels').'</option>';
								}
							}

							print '
								<div  class="elems">
									<input type="checkbox" '.$checked.' value="'.$name.'" id="col-'.$key.'" name="col-filter['.$key.']" class="col-filter" />
									<label for="col-'.$key.'">'._( $name ).'</label>
									<div class="size-images">
										<label for="size-'.$key.'">'._('Taille :').'</label>
										<select name="size-img['.$key.']" id="size-'.$key.'">.'.$sizes.'</select>
									</div>
								</div>
							';

							break;
						}
						case 'stock' : {
							print '
								<div  class="elems">
									<input type="checkbox" '.$checked.' value="'.$name.'" id="col-'.$key.'" name="col-filter['.$key.']" class="col-filter" />
									<label for="col-'.$key.'">'._( $name ).'</label>
								</div>
							';
							break;
						}
						default : {
							print '
								<div  class="elems">
									<input type="checkbox" '.$checked.' value="'.$name.'" id="col-'.$key.'" name="col-filter['.$key.']" class="col-filter" />
									<label for="col-'.$key.'">'._( $name ).'</label>
								</div>
							';

							break;
						}
					}
				}

				print '
					</div>
				';
			?>

			<div class="clear"></div>
			<span class="part-export">
				<?php print _('Informations complémentaires :'); ?>
				<a href="#" class="check-all-fld"><?php print _('Cocher tout')?></a> | <a href="#" class="uncheck-all-fld"><?php print _('Décocher tout')?></a>
			</span>
			<div class="clear"></div>

			<?php
				$rfield = fld_fields_get( 0, 0, -2, 0, 0, 0, false, array(), false, array(), null, CLS_PRODUCT, null, false, null, false );

				print '
					<div class="cols">
				';

				$i = 0;
				while( $field = ria_mysql_fetch_array($rfield) ){
					$checked = isset($fields) && is_array($fields) && in_array($field['id'], $fields) ? 'checked="checked"' : '';

					print '
						<div  class="elems">
							<input '.$checked.' type="checkbox" value="'.$field['name'].'" id="fld-'.$field['id'].'" name="fld-filter['.$field['id'].']" class="fld-filter" />
							<label for="fld-'.$field['id'].'">'.htmlspecialchars( $field['name'] ).'</label>
						</div>
					';

					$i++;
				}

				print '
					</div>
				';
			?>

			<div class="clear"></div>
			<div class="<?php print $export['pxp_id']>0 && !$no_edit ? 'block-hide' : ''; ?> export-action">
				<h2></h2>
				<input type="checkbox" name="export-no-html" id="export-no-html" value="1" />
				<label for="export-no-html"><?php print _('Supprimer automatiquement les balises HTML des champs description')?></label>
				<input class="btn-action btn-main" type="submit" name="export" value="<?php print _('Télécharger')?>" />
			</div>

			<div class="clear"></div>

			<div id="save-form-export" style="display: <?php print ($export['pxp_id'] || isset($_POST['save-form-export'])) && !$no_edit ? 'block' : 'none' ?>;">
				<h2><?php print _('Sauvegarder l\'export')?></h2>
				<p><?php print _('Vous pouvez sauvegarder l\'export :'); ?></p>
				<ul>
					<li><?php print _('Soit pour le réexécuter plus tard')?></li>
					<li><?php print _('Soit pour programmer périodiquement son exécution, à ce moment là, un fichier sera créé pour cet export et sera accessible via une url.')?></li>
				</ul>
				<input type="hidden" name="exp-id" id="exp-id" value="<?php print $export['pxp_id']; ?>" />
				<input type="hidden" name="no-edit" id="no-edit" value="<?php print $no_edit; ?>" />
				<table>
					<caption><?php print _('Enregistrement de l\'export')?></caption>
					<col width="140" /><col width="300" />
					<tfoot>
						<tr>
							<td colspan="2">
								<input type="submit" name="save-form-export" value="<?php print _('Enregistrer')?>" />
								<input type="button" name="cancel-save-export" value="<?php print _('Annuler')?>" />
							</td>
						</tr>
					</tfoot>
					<tbody>
						<tr>
							<th colspan="2"><?php print _('Général')?></th>
						</tr>
						<tr>
							<td>
								<label for="export-name"><span class="mandatory">*</span> <?php print _('Nom :'); ?></label>
							</td>
							<td>
								<input type="text" name="export-name" id="export-name" value="<?php print $export['name']; ?>" />
							</td>
						</tr>
						<tr>
							<td>
								<label for="export-auto-y"><?php print _('Activer l\'export :'); ?></label>
							</td>
							<td>
								<input type="radio" name="export-auto" id="export-auto-y" value="1" <?php print $export['is_auto'] ? 'checked="checked"' : ''; ?> />
								<label for="export-auto-y"><?php print _('Oui')?></label>
								<input type="radio" name="export-auto" id="export-auto-n" value="0" <?php print !$export['is_auto'] ? 'checked="checked"' : ''; ?> />
								<label for="export-auto-n"><?php print _('Non')?></label>
							</td>
						</tr>
						<tr class="<?php print !$export['is_auto'] ? 'block-hide' : ''; ?> export-program">
							<td>
								<label for="export-period"><span class="mandatory">*</span> <?php print _('Exporter :'); ?></label>
							</td>
							<td>
								<select name="export-period" id="export-period">
									<option value="day" <?php print $export['period']=='day' ? 'selected="selected"' : ''; ?>><?php print _('Tous les jours')?></option>
									<option value="week" <?php print $export['period']=='week' ? 'selected="selected"' : ''; ?>><?php print _('Toutes les semaines')?></option>
									<option value="month" <?php print $export['period']=='month' ? 'selected="selected"' : ''; ?>><?php print _('Tous les mois')?></option>
								</select>

								<div class="<?php print $export['period']!='week' ? 'block-hide' : ''; ?> period-week">
									<label for="day">&nbsp;<?php print _('le')?>&nbsp;</label>
									<select name="day" id="day">
										<option value="1" <?php print $export['period']=='week' && $export['period_info']==1 ? 'selected="selected"' : ''; ?>><?php print _('Lundi')?></option>
										<option value="2" <?php print $export['period']=='week' && $export['period_info']==2 ? 'selected="selected"' : ''; ?>><?php print _('Mardi')?></option>
										<option value="3" <?php print $export['period']=='week' && $export['period_info']==3 ? 'selected="selected"' : ''; ?>><?php print _('Mercredi')?></option>
										<option value="4" <?php print $export['period']=='week' && $export['period_info']==4 ? 'selected="selected"' : ''; ?>><?php print _('Jeudi')?></option>
										<option value="5" <?php print $export['period']=='week' && $export['period_info']==5 ? 'selected="selected"' : ''; ?>><?php print _('Vendredi')?></option>
										<option value="6" <?php print $export['period']=='week' && $export['period_info']==6 ? 'selected="selected"' : ''; ?>><?php print _('Samedi')?></option>
										<option value="0" <?php print $export['period']=='week' && $export['period_info']==0 ? 'selected="selected"' : ''; ?>><?php print _('Dimanche')?></option>
									</select>
								</div>

								<div class="<?php print $export['period']!='month' ? 'block-hide' : ''; ?> period-month">
									<label for="month">&nbsp;<?php print _('le')?>&nbsp;</label>
									<select name="month" id="month"><?php
										for( $i=1 ; $i<=31 ; $i++ ){
											$selected = $export['period']=='month' && $export['period_info']==$i ? 'selected="selected"' : '';

											print '
												<option '.$selected.' value="'.$i.'">'.str_pad( $i, 2, '0', STR_PAD_LEFT ).'</option>
											';
										}
									?></select>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
				<div class="clear"></div>
			</div>
		</form>
		<?php } ?>
	</div>
	<script src="/admin/js/jquery.min.js"></script>
	<script>
		$(document).ready(
			function(){
				$('.check-all-fld').click(function(){
					$('.fld-filter').attr('checked', 'checked');
					return false;
				});
				$('.uncheck-all-fld').click(function(){
					$('.fld-filter').removeAttr('checked');
					return false;
				});
				$('.check-all-col').click(function(){
					$('.col-filter').attr('checked', 'checked');
					return false;
				});
				$('.uncheck-all-col').click(function(){
					$('.col-filter').removeAttr('checked');
					return false;
				});

				$('input[name=export]').click(function(){
					var cols = '';
					var img_size = '';

					$('.col-filter:checked').each(function(){
						var p = $(this).attr('name').replace('col-filter[', '').replace(']', '');
						var v = $(this).val();
						cols += ($.trim(cols) != '' ? '|' : '') + p;
						if( p == 'img_main' ){
							img_size += '&'+$(this).parent().find('select').attr('name')+'=';
							img_size += $(this).parent().find('select').val();
						}
						if( p == 'img_second' ){
							img_size += '&'+$(this).parent().find('select').attr('name')+'=';
							img_size += $(this).parent().find('select').val();
						}
					});

					var flds = '';

					$('.fld-filter:checked').each(function(){
						var p = $(this).attr('name').replace('fld-filter[', '').replace(']', '');
						var v = $(this).val();
						flds += ($.trim(flds) != '' ? '|' : '') + p
					});

					var url = '/admin/catalog/export-products.php?cat=<?php print $cat; ?>&brd=<?php print $brand; ?>&cols=' + cols + '&flds=' + flds + img_size;

					if ($('[name=catchilds]:checked').length) {
						url += '&catchilds=1';
					}

					if ($('[name=seo]:checked').length) {
						url += '&seo=1';
					}

					if ($('[name=export-no-html]:checked').length) {
						url += '&export-no-html=1';
					}

					if ($('[name=childonly]:checked').length) {
						url += '&childonly=1';
					}

					if ($('[name=for_excel]:checked').length) {
						url += '&for_excel=1';
					}

					if ($('[name=for_mac]:checked').length) {
						url += '&for_mac=1';
					}

					if ($('[name^=lng]:checked').length) {
						$('[name^=lng]:checked').each(function(i, el){
							url += '&lng[]='+el.value;
						})
					}

					if ($('[name="prd_ids"]').length) {
						url += '&prd_ids=' + $('[name="prd_ids"]').val();
					}

					$('body').append('<div class="popup_ria_back_load"></div>');
					$('body').append('<div class="popup_ria_back_notice notice"><?php print _('Votre export est en cours de préparation, veuillez patienter...')?></div>');

					$.ajax({
						type 	: 'get',
						url 	: url,
						data 	: '',
						async 	: true,
						success : function(urlDownload){
							$('.popup_ria_back_notice').html("<?php print _('Votre demande d\'export a bien été prise en compte, vous pouvez suivre son traitement depuis <a href=\'#\' id=\'link-exports\'> Outils > Exports</a>.'); ?>");
						}
					});
					// window.location.href = url;
					return false;
				});
			}
		).delegate(
			'input[name="save-export"]', 'click', function(){
					$('#save-form-export').show();
					$('input[name="save-export"]').parent().hide();
					return false;
			}
		).delegate(
			'input[name="cancel-save-export"]', 'click', function(){
				var expID = $('#exp-id').val();
				var noEdit = parseInt($('#no-edit').val());

				if( parseInt(expID)>0 && !noEdit ){
					parent.hidePopup();
				}else{
					$('#save-form-export').hide();
					$('input[name="save-export"]').parent().show();
				}

				return false;
			}
		).delegate(
			'#export-period', 'change', function(){
				var period = $(this).val();

				$('.period-week, .period-month').hide();
				if( $('.period-' + period).length ){
					$('.period-' + period).css('display', 'inline')
				}
			}
		).delegate(
			'#export-auto-y', 'click', function(){
				$('.export-program').show();
			}
		).delegate(
			'#export-auto-n', 'click', function(){
				$('.export-program').hide();
			}
		).delegate(
			'#list-export', 'change', function(){
				var expID = $(this).find('option:selected').val();
				window.location.href = 'popup-export-products.php?export=' + expID + '&no-edit=1' + (expID=='-1' ? '&cat=<?php print $cat; ?>' : '');
			}
		).delegate(
			'#link-exports', 'click', function(){
				parent.window.location.href = "/admin/tools/exports/index.php";
				parent.hidePopup();
			}
		).on(
			'change', '[name="seo"]', function(){
				if( $(this).is(':checked') ){
					$('[id="col-classify"]').prop('checked', true);
				}
			}
		).on(
			'change', '[id="col-classify"]', function(){
				if( !$(this).is(':checked') ){
					$('[name="seo"]').prop('checked', false);
				}
			}
		);
	</script>
<?php
	require_once('admin/skin/footer.inc.php');
