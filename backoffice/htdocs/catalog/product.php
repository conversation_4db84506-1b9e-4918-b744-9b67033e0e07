<?php

/**	\file product.php
 *
 *	Cette page permet la création et la mise à jour d'un produit par l'utilisateur. Elle propose de nombreux onglets
 *	pour couvrir tous les aspects du produit :
 *	- general : Propriétés générales
 *	- prices : Tarification
 *	- stocks : Stocks
 *	- nomenclature : Nomenclature
 *	- linked : Articles liés
 *	- fields : Champs avancés
 *	- images : Médiathèque
 *	- delivery : Livraison
 *	- comparator : Comparateurs de prix
 *	- marketplace : Places de marché
 *	- ref : Référencement naturel
 *	- rewards : Fidélité
 *	- reviews : Avis consommateurs
 *	- stats : Statistiques
 *
 */

require_once('products.inc.php');
require_once('categories.inc.php');
require_once('prd/images.inc.php');
require_once('prd/relations.inc.php');
require_once('tenants.inc.php');
require_once('websites.inc.php');
require_once('fields.inc.php');
require_once('strings.inc.php');
require_once('tsk.comparators.inc.php');
require_once('prices.inc.php');
require_once('view.translate.inc.php');
require_once('rewards.inc.php');
require_once('admin/get-filters.php');
require_once('usr/sell-units-rules.inc.php');
require_once('view.admin.inc.php');

// Vérifie que l'utilisateur à bien accès à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_VIEW');

// Vérifie que l'utilisateur à bien le droit de modifier / supprimer un produit
if( isset($_GET['prd']) && $_GET['prd']!=0 ){
	if (
		isset($_POST['save']) || isset($_POST['save_stay']) || isset($_POST['addmdl']) || isset($_POST['savefields']) || isset($_POST['add-nomenclature']) ||
		isset($_POST['save-nomenclature']) || isset($_POST['delimg']) || isset($_POST['save-stock']) || isset($_POST['save-misc']) || isset($_POST['save-ref']) ||
		isset($_POST['save-info']) || isset($_POST['save-rwd-prd']) || isset($_POST['saveWatch']) || isset($_POST['deldocs']) || isset($_POST['savedocs']) || isset($_POST['save-schedule']) ||
		isset($_POST['del-schedule']) || isset($_POST['save-sellunit'])
	) {
		gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_EDIT');
	}
}

// Si une suppression non autorisée est demandée, redirige l'utilisateur
if( isset($_POST['delete']) ){
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_DEL');
}

// S'assure que la variable $_GET['prd'] est définie
if (!isset($_GET['prd'])) {
	$_GET['prd'] = 0;
}

// Permet l'enregistrement même si l'utilisateur a cliqué sur un onglet plutôt que sur Enregistrer
if( $_GET['prd']==0 && sizeof($_POST) ){
	$_POST['save_stay'] = true;
}

// Détermine l'onglet en cours de consultation
// L'onglet Général est l'onglet par défaut
if (!isset($_GET['tab'])) {
	$_GET['tab'] = 'general';
}
$tab = $_GET['tab'];

unset($error);

// Gère l'enregistrement des tarifs par catégorie tarifaire
//INFO: This will be called also using ajax when the tva button was clicked, to update de prices if needed
if( isset($_POST['save-prices']) && isset($_POST['price-ht']) ){

	$tva_rate = -1;
	$tva = false;
	// TVA avec condition pour le produit
	$rtva = prc_tvas_get(0, false, $_GET['prd']);
	if( $tva = ria_mysql_fetch_array($rtva) ){
		$tva_rate = parseFloat($tva['rate']);
	}

	//If tva was changed in form but not saved we update it before updating all prices
	if( isset($_POST['prd-tva']) && $tva_rate != $_POST['prd-tva'] ){
		$tva_ok = true;
		$id_tva = ($tva && isset($tva['id'])) ? $tva['id'] : 0;
		if( $_POST['prd-tva'] == '-1') {
			if($id_tva > 0 && !prc_tvas_del($id_tva)){
				$tva_ok = false;
			}
		} else if( $id_tva == 0 ){
			$tva = prc_tvas_add($_POST['prd-tva'], $_GET['prd'], 0);
			if( !$tva ){
				$tva_ok = false;
			}
		} else {
			$tva_ok = prc_tvas_set_rate($id_tva, $_POST['prd-tva']);
		}
		if( $tva_ok ){
			$tva_rate = $_POST['prd-tva'];
		}
	}

	//If tva_rate is not defined, by default we use 1
	if($tva_rate == -1){
		$tva_rate = 1;
	}

	$prcs = prd_prices_categories_get();

	// Get default price
	$price_ht_default = 0;
	if( $rprice = prc_prices_get( 0, NEW_PRICE, false, false, false, $_GET['prd'], false, false, false, null, false, 1, 1, false, false, array(), array(), null, false, 0, null, 0, 0, false, 0, false, false, false, true) ) {
		if( $price = ria_mysql_fetch_array($rprice) ){
			$price_ht_default = $price['value'];
		}
	}

	// Update all prices
	while( $prc = ria_mysql_fetch_array($prcs) ){

		if( isset($_POST['price-ht'][ $prc['id'] ]) ){
			if($prc['ttc'] == 1){
				$new_price_ht = parseFloat($_POST['price-ttc'][ $prc['id'] ]) / $tva_rate;
			} else {
				$new_price_ht = $_POST['price-ht'][ $prc['id'] ];
			}

			// Si un tarif existe déjà pour ce couple produit / catégorie tarifaire, il doit d'abord être supprimé
			$rprc = prc_prices_get(
				0, NEW_PRICE, false, false, false, $_GET['prd'], false, false, false, null, false, 1,
				1, false, false, array( array( 'fld' => _FLD_USR_PRC, 'symbol' => '=', 'value' => $prc['id'] )), array(), null, false, 0, null, 0, 0, false, 0, false,	false, false, true
			);
			if( $rprc && ria_mysql_num_rows($rprc) ){
				while( $p = ria_mysql_fetch_array($rprc) ){
					prc_prices_del( $p['id'], 0, $_GET['prd'] );
				}
			}

			// Création du nouveau tarif, appliqué au couple produit / catégorie tarifaire
			if( is_numeric($new_price_ht) && ($new_price_ht != $price_ht_default) ){
				$res = prc_prices_add(
					NEW_PRICE, $new_price_ht, null, null, 1, $_GET['prd'], 0, true, false, '',
					array( _FLD_USR_PRC => array( 'symbol' => '=', 'value' => $prc['id'] ) )
				);
				if( !$res ){
					$error = _('L\'ajout de votre tarif a échoué pour une raison inconnue');
				}
			}
		}
	}

	if( IS_AJAX ) {
		$result = array();
		$result['success'] = isset($error) ? 0 : 1;
		if( isset($error) ){
			$result['error'] = $error;
		}
		echo json_encode($result);
		return;
	}else{
		if (!isset($error)) {
			header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=' . $tab);
			exit;
		}
	}
}

// Gestion des TVA par pays
if( isset($_POST['tva_country_save']) ){
	foreach( $_POST['tva_country'] as $key=>$cnt_code ){
		if( $key === 'tva_country_uniqid' || trim($cnt_code) == '' ){
			continue;
		}

		$tva_rate = ( $_POST['tva_country_value'][ $key ] / 100 ) + 1;

		if( !is_numeric($tva_rate) ){
			$error = str_replace(
				'#param[pays]#',
				sys_countries_get_name($cnt_code),
				_('La TVA renseignée pour le pays "#param[pays]#" est incorrecte.')
			);
		}
		// Ajout ou sise à jour de la TVA pour ce pays
		elseif( !prc_tvas_add($tva_rate, $_GET['prd'], 0, false, null, $cnt_code) ){
			$error = _('Une erreur est survenue lors de l\'enregistrement de la TVA par pays.');
		}
	}

	if( !isset($error) ){
		$_SESSION['success_save_prd'] = _('Les conditions de TVA par pays ont bien été mise à jour.');
		header('Location: /admin/catalog/product.php?cat='.$_GET['cat'].'&prd='.$_GET['prd'].'&tab=prices');
		exit;
	}
}

// Suppression de règles d'unité de vente
if( isset($_POST['del-sell-unit-rule']) ){
	if( isset($_POST['rule']) ){
		$res = gu_sell_units_rules_del( $_POST['rule'], false );
		if( $res !== true ){
			$error = _('Une erreur est survenue lors de la suppression des règles');
		}else{
			$_SESSION['success_save_prd'] = _('Les règles sélectionnées ont bien été supprimées.');
			header('Location: /admin/catalog/product.php?cat='.$_GET['cat'].'&prd='.$_GET['prd'].'&tab=stocks');
			exit;
		}
	}
}

// Par défaut, on arrive sur le jour en cours
if (isset($_GET['date1']) && isdate($_GET['date1'])) {
	$_SESSION['datepicker_date1'] = $_GET['date1'];
}
if (isset($_GET['date2']) && isdate($_GET['date2'])) {
	$_SESSION['datepicker_date2'] = $_GET['date2'];
}

$date1 = isset($_SESSION['datepicker_date1']) ? dateparse($_SESSION['datepicker_date1']) : date('Y-m-d');
$date2 = isset($_SESSION['datepicker_date2']) ? dateparse($_SESSION['datepicker_date2']) : date('Y-m-d');
$date2 = strtotime($date2) < strtotime($date1) ? $date1 : $date2;
$_SESSION['datepicker_period'] = isset($_SESSION['datepicker_period']) ? $_SESSION['datepicker_period'] : 'Aujourd\'hui';

// Charge les informations sur le ou les sites administrés
$website = wst_websites_get();
$wst_id = false;
if (isset($_GET['wst'])) {
	if ($_GET['wst'] == 'all') {
		$wst_id = false;
	} else {
		$wst_id = str_replace('w-', '', $_GET['wst']);
	}
}

$product_optimisation_cat = 0;
if (isset($_GET['opt'])) {
	$product_optimisation_cat = $_GET['opt'];
}
// Détermine l'onglet en cours de consultation
// L'onglet Général est l'onglet par défaut
if (!isset($_GET['tab'])) {
	$_GET['tab'] = 'general';
}
$tab = $_GET['tab'];

if (isset($_POST['tabGeneral'])) {
	$tab = 'general';
} elseif (isset($_POST['tabPrices'])) {
	$tab = 'prices';
} elseif (isset($_POST['tabNomenclature'])) {
	$tab = 'nomenclature';
} elseif (isset($_POST['tabLinked']) || isset($_POST['prd-parents-del']) || isset($_POST['prd-childs-del']) || isset($_POST['prd-parents-add']) || isset($_POST['prd-childs-add'])) {
	$tab = 'linked';
} elseif (isset($_POST['tabFields'])) {
	$tab = 'fields';
} elseif (isset($_POST['tabImages']) || isset($_POST['addimg'])) {
	$tab = 'images';
} elseif (isset($_POST['tabStocks'])) {
	$tab = 'stocks';
} elseif (isset($_POST['tabDelivery'])) {
	$tab = 'delivery';
} elseif (isset($_POST['tabComparators'])) {
	$tab = 'comparators';
} elseif (isset($_POST['tabMarketplace'])) {
	$tab = 'marketplace';
} elseif (isset($_POST['tabReferencing'])) {
	$tab = "ref";
} elseif (isset($_POST['tabRewards'])) {
	$tab = 'rewards';
} elseif (isset($_POST['tabReviews']) || (isset($_POST['publish']) && !isset($_POST['alt-cat-id']) && !isset($_POST['tabStats'])) || isset($_GET['rvw'])) {
	$tab = 'reviews';
} elseif (isset($_POST['tabStats']) || isset($_GET['day']) || isset($_GET['week']) || isset($_GET['month']) || isset($_GET['year'])) {
	$tab = 'stats';
}

if ($tab == 'rewards' && !gu_user_is_authorized('_RGH_ADMIN_TOOL_REWARD')) {
	$tab = 'general';
}

if ($tab == 'documents') {
	$tab = 'images';
}

// l'onglet général est forcé pour les nouveaux produits (prd = 0)
if ($_GET['prd'] == 0) {
	$tab = 'general';
}

$_GET['tab'] = $tab;

// Vérifie l'existence de la catégorie renseignée dans l'URL
if( !isset($_GET['cat']) || ($_GET['cat'] != 0 && !prd_categories_exists($_GET['cat'])) ){
	$_GET['cat'] = 0;
}

// Si la catégorie de l'article est égale à zéro, on re-vérifie son appartenance réelle
if( $_GET['cat'] == 0 ){
	$r_cat = prd_products_categories_get( $_GET['prd'] );
	// La catégorie n'est pas égale à zéro
	if( $r_cat && ria_mysql_num_rows($r_cat) ){
		$cat = ria_mysql_fetch_assoc_all($r_cat);
		$_GET['cat'] = $cat[0]['cat']; // On prend la première catégorie disponible
	}
// Sinon on vérifie l'existence de la catégorie renseignée dans l'URL
}else{
	if( !isset($_GET['cat']) || ($_GET['cat'] != 0 && !prd_categories_exists($_GET['cat'])) ){
		$_GET['cat'] = 0;
	}
}

// Bouton Annuler (renvoie à la liste des produits de la catégorie)
if (isset($_POST['cancel'])) {
	header('Location: index.php?cat=' . $_GET['cat']);
	exit;
}

// Vérifie l'existence du produit (mode édition uniquement)
if (isset($_GET['prd']) && ($_GET['prd'] != 0 || !is_numeric($_GET['prd'])) && !prd_products_exists($_GET['prd'])) {
	header('Location: index.php?cat=' . $_GET['cat']);
	exit;
}

if (!isset($_GET['prdname']) && !isset($_POST['save-info']) && !isset($_POST['save']) && !isset($_POST['save_stay'])) {
	if ($_GET['cat'] != 0) {
		if (!prd_classify_exists($_GET['cat'], $_GET['prd'])) {
			$_GET['cat'] = 0;
		}
	}
}
i18n::setLang(view_selected_language());
$params = array(
	'cat' => $_GET['cat'],
	'prd' => $_GET['prd'],
	'lng' => i18n::getLang(),
	'tab' => $tab
);
if (isset($_GET['periode']) && ($_GET['periode'] >= 0 || $_GET['periode'] < 6)) {
	$params['periode'] = $_GET['periode'];
}

if (isset($_GET['brd']) && is_numeric($_GET['brd']) && $_GET['brd'] > 0) {
	$params['brd'] = $_GET['brd'];
}

if (isset($_GET['opt']) && is_numeric($_GET['opt']) && $_GET['opt'] > 0) {
	$params['opt'] = $_GET['opt'];
}
$use_sto_res = isset($config['use_stock_res_web']) && $config['use_stock_res_web'];

// Supprime le lien entre une famille de comparateur / place de marché et un produit
if (isset($_POST['del-link-family_x'])) {
	$tab = $_POST['is_marketplace'] ? 'marketplace' : 'comparators';
	if( isset($_GET['ctr']) && is_numeric($_GET['ctr']) && $_GET['ctr'] ){
		if( !ctr_catalogs_update_categorie($_GET['ctr'], $_GET['prd'], 0) ){
			$error = _('Une erreur inattendue s\'est produite lors de la suppression du lien.');
		}
	}

	if( !$error ){
		header('Location: /admin/catalog/product.php?cat=0&prd='.$_GET['prd'].'&tab='.$tab);
		exit;
	}
}

$url = view_admin_construct_url("product.php", $params);
// Gère l'enregistrement des informations sur les comparateurs de prix
if (isset($_POST['save-info'])) {
	$tab = $_POST['is_marketplace'] ? 'marketplace' : 'comparators';
	$rctr = ctr_comparators_get(0, false, false, null);
	if ($rctr) {
		while ($ctr = ria_mysql_fetch_array($rctr)) {
			$actived = ctr_catalogs_is_publish($ctr['id'], $_GET['prd']);

			// enregistre les informations pour le comparateur de prix
			$ctrID = $ctr['id'];

			switch ($ctrID) {
				case CTR_PRICEMINISTER:
					// enregistre les informations propres à PriceMinister
					if (!isset($_POST['typeproduct'], $_POST['attr']) || trim($_POST['typeproduct']) == '-1') {
						if ($actived) {
							$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées pour la place de marché PriceMinister.");
						} else {
							ctr_catalogs_update_params($ctrID, $_GET['prd']);
						}
					} else {

						$ar_template = array('typeproduct' => $_POST['typeproduct'], 'template' => array());
						foreach ($_POST['attr'] as $key => $val) {
							if ($_POST['mandatory'][$key] && in_array(trim($val), array('', '-1'))) {
								if ($actived) {
									$error = sprintf(_('L\'information " %s " pour PriceMinister est obligatoire.'), $_POST['label'][$key]);
									break;
								}
							}
							$ar_template['template'][$key] = $val;
						}

						if (!isset($error)) {
							if (!ctr_catalogs_update_params($ctrID, $_GET['prd'], json_encode($ar_template))) {
								$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des informations pour le comparateur PriceMinister.") . "\n" . _("Veuillez réessayer ou prendre contact pour signaler le problème.");
							}
						}
					}
					break;
				case CTR_EBAY:
					if (isset($_POST['ebay-cat'], $_POST['param-ebay-type'], $_POST['param-ebay']) && trim($_POST['ebay-cat']) != '') {
						require_once('comparators/ctr.ebay.inc.php');
						$ebay = new EBay();

						$params = ctr_catalogs_get_params(CTR_EBAY, $_GET['prd']);
						$opt_grp = isset($params['opt-grp']) ? $params['opt-grp'] : false;

						$ar_params = array();
						$specifics = $ebay->getCategorySpecifics($_POST['ebay-cat']);

						foreach ($specifics as $s) {
							// Recherche le champ dans les POST
							$key = array_search($s['name'], $_POST['param-ebay-type']);

							// Si le champ est renseigné
							if ($key !== false) {
								if (!isset($_POST['param-ebay'][$key])) {
									continue;
								}

								if (!is_array($_POST['param-ebay'][$key])) {
									error_log(__FILE__ . ':' . __LINE__ . ' param-ebay ' . $key . ' pas un tableau');
									continue;
								}

								// Récupère le nombre de valeur maximale
								$maxlength = isset($s['rules']['maxlength']) ? $s['rules']['maxlength'] : 1;

								// Charge les valeurs dans un tableau
								$vals = array();
								foreach ($_POST['param-ebay'][$key] as $val) {
									if ($val == 'other') {
										$other = isset($_POST['param-other-ebay'][$key]) ? $_POST['param-other-ebay'][$key] : '';
										if (trim($other) != '') {
											$vals = array_merge($vals, explode(';', $other));
										}
									} elseif (trim($val) != '') {
										$vals[] = $val;
									}
								}

								if (ria_array_get($s['rules'], 'minlength', 0) && empty($vals)) {
									$error = sprintf(_('Une valeur est attendue pour le champ "%s" de la place de marché Ebay.'), $s['name']);
									break;
								}

								if (empty($vals)) {
									continue;
								}

								if (sizeof($vals) > $maxlength) {
									$error = sprintf(_('Le nombre de valeur maximale pour "%s" est de %d.'), $s['name'], $maxlength);
									break;
								}

								// Vérifier les relations entre champ
								if (is_array($s['rules']['relations']) && sizeof($s['rules']['relations'])) {
									foreach ($s['rules']['relations'] as $r) {
										$rkey = array_search($r, $_POST['param-ebay-type']);
										if ($rkey === false) {
											$error = sprintf(_('L\'information "%s" ne peut être prise en compte car l\'information "%s" n\'est pas renseignée.'), $s['name'], $r);
											break;
										} else {
											$rvals = array();
											foreach ($_POST['param-ebay'][$rkey] as $val) {
												if ($val == 'other') {
													$other = isset($_POST['param-other-ebay'][$rkey]) ? $_POST['param-other-ebay'][$rkey] : '';
													if (trim($other) != '') {
														$rvals = array_merge($rvals, explode(';', $other));
													}
												} elseif (trim($val) != '') {
													$rvals[] = $val;
												}
											}

											if (!sizeof($rvals)) {
												$error = sprintf(_('L\'information "%s" ne peut être prise en compte car l\'information "%s" n\'est pas renseignée.'), $s['name'], $r);
												break;
											}
										}
									}
								}

								$ar_params[$s['name']] = implode(';', $vals);
							}
						}

						if (!isset($error)) {
							if ($opt_grp) {
								$ar_params['opt-grp'] = $opt_grp;
							}

							if (isset($ar_params) && !ctr_catalogs_update_params($ctrID, $_GET['prd'], json_encode($ar_params))) {
								$error = sprintf(_("Une erreur inattendue s'est produite lors de l'enregistrement des informations pour le comparateur %s."), $ctr['name']) . "\n" . _("Veuillez réessayer ou prendre contact pour signaler le problème.");
							}
						}
					}
					break;
				default:
					if (isset($_POST['vals'][$ctrID]) && is_array($_POST['vals'][$ctrID]) && sizeof($_POST['vals'][$ctrID])) {
						$ar_template = array();
						foreach ($_POST['vals'][$ctrID] as $key => $val) {
							if (is_numeric($val) && $val > 0) {
								$rvalue = ctr_cat_field_values_get($key, $val);
								if ($rvalue && ria_mysql_num_rows($rvalue)) {
									$val = ria_mysql_result($rvalue, 0, 'val');
								}
							}
							$ar_template[$key] = $val;
						}

						if (isset($ar_template) && !ctr_catalogs_update_params($ctrID, $_GET['prd'], json_encode($ar_template))) {
							$error = sprintf(_("Une erreur inattendue s'est produite lors de l'enregistrement des informations pour le comparateur %s"), $ctr['name']) . "\n" . _("Veuillez réessayer ou prendre contact pour signaler le problème.");
						}
					}
					break;
			}

			if (ctr_catalogs_is_publish($ctrID, $_GET['prd'])) {
				tsk_comparators_add($ctrID, $_GET['prd'], 'update');
			}
			if (isset($_POST['auctions'][$ctrID])) {
				$auctions = str_replace(',', '.', $_POST['auctions'][$ctrID]);

				$min = 0;
				// vérification du montant minimum
				if (ctr_comparators_auctions_used($ctrID, true)) {
					$cat = ctr_catalogs_get_categorie($ctrID, $_GET['prd'], false);
					if ($cat) {
						$min = ctr_categories_get_min_auctions($ctrID, $cat);
					}
				}

				if (trim($auctions)) {
					// vérification du montant minimum
					if ($min > 0 && $min > $auctions) {
						$error = sprintf(_("Le montant de l'enchère pour le comparateur \"%s\" ne peut être inférieur à %d €."), ctr_comparators_get_name($ctrID), str_replace('.', ',', $min));
					}
				}

				if (!isset($error) && !ctr_catalogs_set_auctions($ctrID, $_GET['prd'], $auctions)) {
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'enchère.") . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
		}
	}

	// Sauvegarde des paramètres pour BeezUp
	if( isset($_POST['fldbeezup']) ){
		foreach( $_POST['fldbeezup'] as $k=>$v ){
			if( trim($v) == '' ){
				unset( $_POST['fldbeezup'][$k] );
			}
		}

		if( !ctr_catalogs_update_params(CTR_BEEZUP, $_GET['prd'], json_encode($_POST['fldbeezup'])) ){
			$error = _('Une erreur inattendue s\'est produite lors de l\'enregistrement de la configuration BeezUp.');
		}
	}

	if (!isset($error)) {
		header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=' . $tab);
		exit;
	}
}

if (isset($_POST['copy-images-to-childs'])) {
	$_POST['copy-to-childs'] = 1;
	$_POST['what'] = 'images';
}

// Recopie les images du produit sur ses articles enfants
if (isset($_POST['copy-to-childs']) && isset($_POST['what'])) {
	switch ($_POST['what']) {
		case 'colisages': {
				$childs = prd_childs_get($_GET['prd'], false, false);

				if ($childs && ria_mysql_num_rows($childs)) {
					// Récupère les colisages du parent
					$rcol = prd_colisage_classify_get(0, $_GET['prd']);
					if ($rcol && ria_mysql_num_rows($rcol)) {
						while ($col = ria_mysql_fetch_assoc($rcol)) {
							$ref = trim($col['cly_prd_ref']) != '' ? $col['cly_prd_ref'] : null;
							$barcode = trim($col['cly_prd_barcode']) != '' ? $col['cly_prd_barcode'] : null;

							ria_mysql_data_seek($childs, 0);
							while ($c = ria_mysql_fetch_array($childs)) {
								if (!prd_colisage_classify_add($col['col_id'], $c['id'], $ref, $barcode, $col['is_default'])) {
									$error = _('Une erreur inattendue est survenue lors de la copie des conditionnements sur les articles enfants.');
								}
							}
						}
					}

					if (!isset($error)) {
						$success = sprintf(_('Les conditionnements du produit ont été correctement recopiés sur %d article(s) enfant(s)'), ria_mysql_num_rows(prd_childs_get($_GET['prd'])));
					}
				}
				break;
			}
		case 'relations':
			if (prd_relations_copy_to_childs($_GET['prd'])) {
				$success = sprintf(_('Les relations du produit ont été correctement recopiées sur %d article(s) enfant(s)'), ria_mysql_num_rows(prd_childs_get($_GET['prd'])));
			} else {
				$error = _('Une erreur inattendue est survenue lors de la copie des relations sur les articles enfants.');
			}
			break;
		case 'title':
			$prd_title = prd_products_get_name($_GET['prd'], true);
			$sync_parent = prd_products_get_is_sync($_GET['prd']);
			$childs = prd_products_get_simple(0, '', false, 0, false, false, false, false, array('parent' => $_GET['prd'], "centralized" => false, "childs" => true));
			while ($c = ria_mysql_fetch_array($childs)) {
				if ($c['is_sync']) {
					prd_products_update_title($c['id'], $prd_title);
				} else {
					prd_products_update_name($c, $prd_title);
				}
				foreach ($config['i18n_lng_used'] as $lang) {
					if ($lang == $config['i18n_lng']) {
						continue;
					}
					$fld_title = $c['is_sync'] ? _FLD_PRD_TITLE : _FLD_PRD_NAME;
					$fld_title_parent = $sync_parent ? _FLD_PRD_TITLE : _FLD_PRD_NAME;
					$title_lang = fld_object_values_get($_GET['prd'], $fld_title_parent, $lang, false, true, false, true);
					if ($title_lang) {
						if (!fld_object_values_set($c['id'], $fld_title, $title_lang, $lang)) {
							$error = _("Une erreur est survenue lors de la copie des titres sur les produits enfants.");
						}
					}
				}
			}
			if (!isset($error)) {
				$success = _("La copie des titres sur les produits enfants a été effectuée avec succès.");
			}

			break;
		case 'desc':
			$prd = ria_mysql_fetch_assoc(prd_products_get_simple($_GET['prd']));

			$childs = prd_childs_get($_GET['prd'], false, false);
			while ($c = ria_mysql_fetch_array($childs)) {
				prd_products_update_desc($c['id'], $prd['desc']);
				foreach ($config['i18n_lng_used'] as $lang) {
					if ($lang == $config['i18n_lng']) {
						continue;
					}
					$desc_parent = fld_object_values_get($prd['id'], _FLD_PRD_DESC, $lang, false, true, false, true);
					if ($desc_parent) {
						if (!fld_object_values_set($c['id'], _FLD_PRD_DESC, $desc_parent, $lang)) {
							$error = _("Une erreur est survenue lors de la copie de description courte sur les produits enfants.");
						}
					}
				}
				if (!isset($error)) {
					$success = _("La copie de la description courte sur les produits enfants a été effectuée avec succès.");
				}
			}
			break;
		case 'desc-long':
			$prd = ria_mysql_fetch_assoc(prd_products_get_simple($_GET['prd']));

			$childs = prd_childs_get($_GET['prd'], false, false);
			while ($c = ria_mysql_fetch_array($childs)) {
				prd_products_update_desc_long($c['id'], $prd['desc-long']);
				foreach ($config['i18n_lng_used'] as $lang) {
					if ($lang == $config['i18n_lng']) {
						continue;
					}
					$desc_parent_lg = fld_object_values_get($prd['id'], _FLD_PRD_DESC_LG, $lang, false, true, false, true);
					if ($desc_parent_lg) {
						if (!fld_object_values_set($c['id'], _FLD_PRD_DESC_LG, $desc_parent_lg, $lang)) {
							$error = _("Une erreur est survenue lors de la copie de description longue sur les produits enfants.") . $desc_parent_lg;
						}
					}
				}
				if (!isset($error)) {
					$success = _("La copie de la description longue sur les produits enfants a été effectuée avec succès.");
				}
			}
			break;
		case 'images':
			if (prd_images_copy_to_childs($_GET['prd'])) {
				$success = sprintf(_('Les images du produit ont été correctement recopiées sur %d article(s) enfant(s)'), ria_mysql_num_rows(prd_childs_get($_GET['prd'], false, false)));
			} else {
				$error = _('Une erreur inattendue est survenue lors de la copie des images sur les articles enfants.');
			}
			break;
		case 'main-img':
			$prd = ria_mysql_fetch_array(prd_products_get_simple($_GET['prd']));

			$childs = prd_childs_get($_GET['prd'], false, false);
			while ($c = ria_mysql_fetch_array($childs)) {
				prd_images_main_add_existing($c['id'], $prd['img_id']);
			}
			break;
	}

	if (isset($_POST['copy-images-to-childs'])) {
		$tab = 'images';
	} else {
		$tab = 'linked';
	}
}

// Recopie les relations du produit sur ses articles enfants
if (isset($_POST['copy-relations-to-childs'])) {
	if (prd_relations_copy_to_childs($_GET['prd'])) {
		$success = sprintf(_('Les relations du produit ont été correctement recopiées sur %d article(s) enfant(s)'), ria_mysql_num_rows(prd_childs_get($_GET['prd'])));
	} else {
		$error = _('Une erreur inattendue est survenue lors de la copie des relations sur les articles enfants.');
	}
}

// Bouton Enregistrer, onglet Divers
if (isset($_POST['save-misc'])) {
	$prd = ria_mysql_fetch_array(prd_products_get_simple($_GET['prd']));
	if ($prd['is_sync']) {
		$_POST['weight'] = isset($prd['weight']) ? $prd['weight'] : '';
	}
	if ($prd['is_sync']) {
		$_POST['weight-net'] = isset($prd['weight_net']) ? $prd['weight_net'] : '';
	}

	if (!isset($_GET['prd'], $_POST['weight'], $_POST['width'], $_POST['height'], $_POST['length'])) {
		$error = _("Un ou plusieurs paramètres sont manquants pour permettre l'enregistrement.") . "\n" . _("Veuillez vérifier ou prendre contact pour nous signaler l'erreur.") . isset($prd['weight']);
	} else {
		if (!prd_products_update_size($_GET['prd'], $_POST['weight'], $_POST['width'], $_POST['height'], $_POST['length']))
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des dimensions du produit.") . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler l'erreur");
		if (!prd_products_update_weight($_GET['prd'], $_POST['weight'], $_POST['weight-net']))
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des poids du produit.") . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler l'erreur");
		if (!prd_products_set_garantie($_GET['prd'], isset($_POST['garantie']) && $_POST['garantie'] > 0 ? $_POST['garantie'] : '0'))
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la durée de garantie du produit.") . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler l'erreur");

		//enregistrement des services de livraison indisponible
		$services = dlv_services_get();
		if ($services && ria_mysql_num_rows($services)) {
			if (!isset($_POST['services_available']) || !is_array($_POST['services_available'])) $_POST['services_available'] = array();
			while ($srv = ria_mysql_fetch_array($services)) {
				// ajout de l'exception
				if (!in_array($srv['id'], $_POST['services_available'])) {
					$runavailable = dlv_products_unavailable_get($prd['id'], $srv['id']);
					if ($runavailable && ria_mysql_num_rows($runavailable)) continue;

					if (!dlv_products_unavailable_add($prd['id'], $srv['id'])) {
						$error = _('Une erreur est survenue lors de la modification des services disponibles.');
					}
				} else { // retire l'exception
					dlv_products_unavailable_del($prd['id'], $srv['id']);
				}
			}
		}

		if (!isset($error)) {
			header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=delivery');
			exit;
		}
	}

	if (isset($error)) {
		$tab = 'delivery';
	}
}

// Bouton Enregistrer, redirection en cas d’indisponibilité
if( isset($_POST['save-url-unreachable']) ){

	if( !prd_products_set_url_unreachable( $_GET['prd'], $_POST['url_unreachable'] ) ){
		$error = _('Une erreur est survenue lors de l\'enregistrement de l\'url de redirection en cas d’indisponibilité.');
	}

	if (!isset($error)) {
		header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&lng=' . $_GET['lng'] . '&tab=ref');
		exit;
	}
}

// Bouton d'ajout d'une nouvelle redirection 301
if( isset($_POST['save-add-301-url']) ){
	if( !isset($_POST['add-301-cly'], $_POST['add-301-url']) ){
		$error = _('Un ou plusieurs paramètres obligatoires sont manquants.');
	}elseif( !is_numeric($_POST['add-301-cly']) || $_POST['add-301-cly'] <= 0 ){
		$error = _('Veuillez choisir un classement.');
	}elseif( trim($_POST['add-301-url']) == '' ){
		$error = _('Veuillez saisir l\'url de destination de cette redirection 301.');
	}

	$r_cly = prd_classify_get( false, $_GET['prd'], $_POST['add-301-cly'] );
	if( !$r_cly || !ria_mysql_num_rows($r_cly) ){
		$error = _('Le classement choisi semble incorrect.');
	}

	// La création d'une redirection 301 se fait en 2 étape
	// 			1 - création de l'URL 301 ($cly['url'] vers $_POST['add-301-url'])
	// 			2 - suppression des URLs 200 lié à l'URL source (source = $cly['url'] et code = 200)
	if( !isset($error) ){
		$cly = ria_mysql_fetch_assoc( $r_cly );

		$r_website = wst_websites_languages_get();
		if( $r_website && ria_mysql_num_rows($r_website) ){
			while( $website = ria_mysql_fetch_assoc($r_website) ){
				if( !rew_rewritemap_add( $cly['url'], $_POST['add-301-url'], 301, $website['wst'], $website['lng_code'], CLS_PRODUCT, [$_POST['add-301-cly'], $_GET['prd']]) ){
					$error = _('Une erreur inattedue s\'est produite lors de l\'ajout de la redirection.');
				}
			}


			if( !isset($error) ){
				if( !rew_rewritemap_del($cly['url'], '', false, null, 0, 200) ){
					$error = _('Une erreur inattendue s\'est produite lors de la désactivation de l\'ancienne url.');
				}
			}
		}else{
			$error = _('Une erreur inattendue s\'est produite.');
		}
	}

	if( !isset($error) ){
		header('Location: /admin/catalog/product.php?cat='.$_GET['cat'].'&prd='.$_GET['prd'].'&tab=ref');
		exit;
	}
}

// Articles liés
if (isset($_POST['prd-parents-del']) && isset($_POST['prd-relations-prd-parents-ref'])) {

	if (is_array($_POST['prd-relations-prd-parents-ref'])) {
		foreach ($_POST['prd-relations-prd-parents-ref'] as $p) {
			prd_hierarchy_del($p, $_GET['prd']);
		}
	} elseif (is_numeric($_POST['prd-relations-prd-parents-ref'])) {
		prd_hierarchy_del($_POST['prd-relations-prd-parents-ref'], $_GET['prd']);
	}

	header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=linked');
	exit;
} elseif (isset($_POST['prd-childs-del']) && isset($_POST['prd-childs'])) {
	$pos_of_tiret_6 = strpos($_POST['prd-childs'], ' - ');
	if ($pos_of_tiret_6 !== false)
		$_POST['prd-childs'] = substr($_POST['prd-childs'], 0, $pos_of_tiret_6);

	if (is_array($_POST['prd-childs'])) {
		foreach ($_POST['prd-childs'] as $c)
			prd_hierarchy_del($_GET['prd'], $c);
	} elseif (is_numeric($_POST['prd-childs'])) {
		prd_hierarchy_del($_GET['prd'], $_POST['prd-childs']);
	}

	header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=linked');
	exit;
} elseif (isset($_POST['prd-parents-ref']) && trim($_POST['prd-parents-ref'])) {
	$pos_of_tiret_6 = strpos($_POST['prd-parents-ref'], ' - ');
	if ($pos_of_tiret_6 !== false)
		$_POST['prd-parents-ref'] = substr($_POST['prd-parents-ref'], 0, $pos_of_tiret_6);

	$parent = prd_products_get_simple(0, $_POST['prd-parents-ref']);
	if (!ria_mysql_num_rows($parent)) {
		$error = sprintf(_("La référence %s n'a pas été trouvée."), strtoupper2(trim($_POST['prd-parents-ref'])));
	} else {
		$parent = ria_mysql_fetch_array($parent);
		if ($parent['id'] == $_GET['prd']) {
			$error = _("Le produit ne peut pas être son propre parent.");
		} elseif (prd_hierarchy_add($parent['id'], $_GET['prd'], 0)) {
			header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=linked');
			exit;
		} else {
			$error = _("Une erreur inattendue s'est produite lors de l'ajout du produit parent");
		}
	}
	$tab = 'linked';
} elseif (isset($_POST['prd-childs-ref']) && trim($_POST['prd-childs-ref'])) {
	$pos_of_tiret_6 = strpos($_POST['prd-childs-ref'], ' - ');
	if ($pos_of_tiret_6 !== false)
		$_POST['prd-childs-ref'] = substr($_POST['prd-childs-ref'], 0, $pos_of_tiret_6);

	$child = prd_products_get_simple(0, $_POST['prd-childs-ref']);
	if (!ria_mysql_num_rows($child)) {
		$error = sprintf(_("La référence %s n'a pas été trouvée."), strtoupper2(trim($_POST['prd-childs-ref'])));
	} else {
		$child = ria_mysql_fetch_array($child);
		if ($child['id'] == $_GET['prd']) {
			$error = _("Le produit ne peut pas être son propre enfant.");
		} elseif (prd_hierarchy_add($_GET['prd'], $child['id'], 0)) {
			header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=linked');
			exit;
		} else {
			$error = _("Une erreur inattendue s'est produite lors de l'ajout du produit enfant");
		}
	}
	$tab = 'linked';
}

// Onglet Articles liés
if ($_GET['tab'] == 'linked') {
	// Modification de la méthode de tri
	if (isset($_POST['orderby'], $_POST['order'], $_POST['data-rel'])) {

		if ($_POST['data-rel'] == 'prd-childs') {
			prd_relations_order_update($_GET['prd'], 'childs', $_POST['order']);
		} else {
			$rel_type = explode('-', $_POST['data-rel']);
			prd_relations_order_update($_GET['prd'], $rel_type[sizeof($rel_type) - 1], $_POST['order']);
		}

		header('Location: /admin/catalog/product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=linked');
		exit;
	}

	$types = prd_relations_types_get();
	while ($t = ria_mysql_fetch_array($types)) {
		if (isset($_POST['prd-relations-' . $t['id'] . '-del'])) {
			if (isset($_POST['prd-relations-' . $t['id'] . '-ref'])) {
				// $pos_of_tiret_6 = strpos($_POST['prd-relations-'.$t['id']], ' - ');
				// if( $pos_of_tiret_6!==false )

				if (is_array($_POST['prd-relations-' . $t['id'] . '-ref'])) {
					foreach ($_POST['prd-relations-' . $t['id'] . '-ref'] as $dst) {
						prd_relations_del($_GET['prd'], $dst, $t['id']);
						$tab = 'linked';
					}
				} else {
					prd_relations_del($_GET['prd'], $_POST['prd-relations-' . $t['id'] . '-ref'], $t['id']);
					$tab = 'linked';
				}
			}
		} elseif (isset($_POST['prd-relations-' . $t['id'] . '-ref']) && trim($_POST['prd-relations-' . $t['id'] . '-ref'])) {
			$pos_of_tiret_6 = strpos($_POST['prd-relations-' . $t['id'] . '-ref'], ' - ');
			if ($pos_of_tiret_6 !== false)
				$_POST['prd-relations-' . $t['id'] . '-ref'] = substr($_POST['prd-relations-' . $t['id'] . '-ref'], 0, $pos_of_tiret_6);

			$dst_ref = strtoupper2(trim($_POST['prd-relations-' . $t['id'] . '-ref']));

			if ((isset($_GET['published']) && $_GET['published'] == 'true')) {
				$published = true;
			} elseif ((isset($_GET['published']) && $_GET['published'] == 'false')) {
				$published = false;
			} elseif (isset($_SESSION['usr_admin_view_publish_linked'])) {
				$published = $_SESSION['usr_admin_view_publish_linked'];
			} elseif (isset($config['admin_view_publish_linked'])) {
				$published = $config['admin_view_publish_linked'];
			} else {
				$published = false;
			}
			$rel_product = prd_products_get_simple(0, $dst_ref);
			if (!ria_mysql_num_rows($rel_product)) {
				$error = sprintf(_("La référence %s n'a pas été trouvée."), $dst_ref);
			} else {
				$rel = ria_mysql_fetch_array($rel_product);
				$add = prd_relations_add($_GET['prd'], $rel['id'], $t['id']);
				$tab = 'linked';
				if (!$add) {
					if ($_GET['prd'] == $rel['id']) {
						$error = _('Le produit ne peut pas être en relation avec lui-même.');
					}
				}
			}
		}
	}
}

// Action sur l'onglet "Avancés"
view_admin_tab_fields_actions(CLS_PRODUCT, $_GET['prd'], (isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng']));

// Bouton Annuler l'édition d'un avis consommateur
if (isset($_POST['cancel-edit-review'])) {
	header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=reviews');
	exit;
}

// Bouton Supprimer un avis
if (isset($_POST['delete-review'])) {
	require_once('prd/reviews.inc.php');

	if (is_array($_POST['rvw'])){
		foreach ($_POST['rvw'] as $rvw)
			prd_reviews_del($rvw);
	}else{
		prd_reviews_del($_POST['rvw']);
	}
	header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=reviews');
	exit;
}

// Bouton Publier un avis
if (isset($_POST['publish-reviews']) && isset($_POST['rvw'])) {
	require_once('prd/reviews.inc.php');
	foreach ($_POST['rvw'] as $rvw){
		prd_reviews_publish($rvw);
	}
	header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=reviews');
	exit;
}

// Bouton Dépublier un avis
if (isset($_POST['unpublish-reviews']) && isset($_POST['rvw'])) {
	require_once('prd/reviews.inc.php');
	foreach ($_POST['rvw'] as $rvw){
		prd_reviews_unapprove($rvw);
	}
	header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=reviews');
	exit;
}

// Bouton Enregistrer un avis
if (isset($_POST['save-review'])) {
	require_once('prd/reviews.inc.php');
	if (!isset($_POST['note'])) $_POST['note'] = null;
	if (!isset($_POST['publish'])) $_POST['publish'] = false;

	prd_reviews_update($_POST['rvw'], $_POST['name'], $_POST['desc'], $_POST['note'], $_POST['publish']);
	if ($_POST['publish']){
		prd_reviews_publish($_POST['rvw']);
	}else{
		prd_reviews_unapprove($_POST['rvw']);
	}
	header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=reviews');
	exit;
}

// Bouton Enregistrer les stocks
if (isset($_POST['save-stock'])) {
	require_once('prd/deposits.inc.php');

	$depots = prd_deposits_get();
	$tot_qte = $tot_res = $tot_com = $tot_prepa = 0;
	if ($depots && ria_mysql_num_rows($depots)) {
		while ($s = ria_mysql_fetch_array($depots)) {
			if (!$s['is_sync'] || !prd_products_get_is_sync($_GET['prd'])) {

				if (
					isset($_POST['stock-qte-'.$s['id']])
					&& isset($_POST['stock-res-'.$s['id']])
					&& isset($_POST['stock-com-'.$s['id']])
					&& isset($_POST['stock-prepa-'.$s['id']])
					&& isset($_POST['stock-mini-'.$s['id']])
					&& isset($_POST['stock-maxi-'.$s['id']])
				) {

					$_POST['stock-qte-'.$s['id']] = parseInt($_POST['stock-qte-'.$s['id']]);
					$_POST['stock-res-'.$s['id']] = parseInt($_POST['stock-res-'.$s['id']]);
					$_POST['stock-com-'.$s['id']] = parseInt($_POST['stock-com-'.$s['id']]);
					$_POST['stock-prepa-'.$s['id']] = parseInt($_POST['stock-prepa-'.$s['id']]);
					$_POST['stock-mini-'.$s['id']] = parseInt($_POST['stock-mini-'.$s['id']]);
					$_POST['stock-maxi-'.$s['id']] = parseInt($_POST['stock-maxi-'.$s['id']]);

					$date_restocking = '';
					if( isset($_POST['stock-date-'.$s['id']]) && isdateheure($_POST['stock-date-'.$s['id']]) ){
						$date_restocking = dateheureparse( $_POST['stock-date-'.$s['id']] );
					}

					if (!prd_dps_stocks_add($_GET['prd'], $s['id'], $_POST['stock-qte-'.$s['id']], $_POST['stock-res-'.$s['id']], $_POST['stock-com-'.$s['id']], $_POST['stock-prepa-'.$s['id']], $_POST['stock-mini-'.$s['id']], $_POST['stock-maxi-'.$s['id']], 0, $date_restocking))
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des stocks.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				} else
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des stocks.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}
	}

	if (!isset($error)) {
		header('Location: product.php?cat='.$_GET['cat'].'&prd='.$_GET['prd'].'&tab=stocks');
		exit;
	}
}

// Sauvegarde de l'unité de vente
if( isset($_POST['save-sellunit']) ){
	if( !isset($_POST['sellunit']) || !is_numeric($_POST['sellunit']) || $_POST['sellunit'] <= 0 ){
		$error = _('Veuillez saisir une unité de vente supérieure à zéro.');
	}else{
		fld_object_values_set( $_GET['prd'], _FLD_PRD_SALES_UNIT, ($_POST['sellunit'] > 1 ? $_POST['sellunit'] : '') );
	}

	if( isset($_POST['sell-units-rules']) && is_array($_POST['sell-units-rules']) ){
		foreach( $_POST['sell-units-rules'] as $rule_id=>$sell_unit ){
			$res = gu_sell_units_rules_upd( $rule_id, $sell_unit );
			if( $res !== true ){
				$error = _('Une erreur est survenue lors de l\'enregistrement des règles d\'unité de vente.');
			}
		}
	}

	if( !isset($error) ){
		$_SESSION['success_save_prd'] = _('L\'unité de vente ainsi que les règles d\'unité de vente ont été mis à jour.');
		header('Location: product.php?cat='.$_GET['cat'].'&prd='.$_GET['prd'].'&tab=stocks');
		exit;
	}
}

if (!isset($error) && isset($_POST['save-schedule'])  && isset($_POST['stock-livr'])) {
	$stockLivr = \DateTime::createFromFormat('d/m/Y', $_POST['stock-livr']);
	if ((!empty($_POST['stock-livr']) && !$stockLivr) || !prd_stocks_update_datelivr($_GET['prd'], $stockLivr ? $stockLivr->format('Y-m-d') : '')) {
		$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la date de disponibilité.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur.");
	}
}

if (isset($_POST['save-schedule']) || isset($_POST['del-schedule'])) {
	if (
		!isset($error)
		&& intval($_GET['prd'])
		&& isset($_POST['schedule-date']) && is_array($_POST['schedule-date'])
		&& isset($_POST['schedule-qte']) && is_array($_POST['schedule-qte'])
	) {
		foreach ($_POST['schedule-date'] as $scheduleIdx => $scheduleDate) {
			if (isset($error)) {
				break;
			}

			if (empty($scheduleDate)) {
				continue;
			}

			$scheduleDate = \DateTime::createFromFormat('d/m/Y', $scheduleDate);
			if (!$scheduleDate || !isset($_POST['schedule-qte'][$scheduleIdx]) || intval($_POST['schedule-qte'][$scheduleIdx]) <= 0) {
				$error = _("Dates de réapprovisionnement non valides.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur.");
				break;
			}

			$scheduleId = intval($_POST['schedule-id'][$scheduleIdx]);
			if ($scheduleId > 0) {
				if (isset($_POST['del-schedule']) && isset($_POST['schedule-choice'][$scheduleIdx])) {
					if (!prd_stocks_schedule_del($scheduleId)) {
						$error = _("Une erreur inattendue s'est produite lors la suppression d'une date de réapprovisionnement.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur.");
					}
					continue;
				}
				if (!isset($_POST['save-schedule'])) {
					continue;
				}

				if (
					!prd_stocks_schedule_set_date($scheduleId, $scheduleDate->format('d/m/Y'))
					|| !prd_stocks_schedule_set_qte($scheduleId, intval($_POST['schedule-qte'][$scheduleIdx]))
					|| !prd_stocks_schedule_set_confirmed($scheduleId, !isset($_POST['schedule-confirmed'][$scheduleIdx]))
				) {
					$error = _("Une erreur inattendue s'est produite lors la mise à jour d'une date de réapprovisionnement.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
				continue;
			}

			if (!prd_stocks_schedule_add(
				intval($_GET['prd']),
				$scheduleDate->format('Y-m-d'),
				intval($_POST['schedule-qte'][$scheduleIdx]),
				isset($_POST['schedule-confirmed'][$scheduleIdx])
			)) {
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement d'une date de réapprovisionnement.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}
	}

	if (!isset($error)) {
		header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=stocks');
		exit;
	}
}

// Bouton Supprimer
if (isset($_POST['delete'])) {
	prd_products_del($_GET['prd']);
	header('Location: index.php?cat=' . $_GET['cat']);
	exit;
}

// Gère la suppression d'un emplacement (en cas d'emplacements multiples)
if ($_GET['prd'] != 0 && sizeof($_POST) > 0) {
	$cats = prd_products_categories_get($_GET['prd']);
	while ($c = ria_mysql_fetch_array($cats))
		if (isset($_POST['del-cat-' . $c['cat'] . '_x'])) {
			$_POST['save'] = true;
			$_POST['del-cat'] = $c['cat'];
		}
}

// Bouton Enregistrer (onglet Général)
if (isset($_POST['save']) || isset($_POST['save_stay']) || isset($_POST['tabGeneral']) || isset($_POST['tabLinked']) || isset($_POST['tabImages']) || isset($_POST['tabDelivery']) || isset($_POST['tabStats'])) {
	if (isset($_POST['name']) || isset($_POST['title'])) {
		$prd = false;

		if ($_GET['prd'] != 0) {
			$prd = ria_mysql_fetch_array(prd_products_get_simple($_GET['prd']));
			if ($prd['is_sync']) {
				if (isset($_POST['barcode'])) {
					unset($_POST['barcode']);
				}

				if (!$prd['publish']) {
					unset($_POST['publish']);
				} else {
					$_POST['publish'] = 1;
				}
				if (!isset($config['can_edit_prd_brand']) || !$config['can_edit_prd_brand']) {
					$_POST['brand'] = $prd['brd_id'];
				}

				$_POST['sell_unit'] = $prd['sun_id'];
				if (!$prd['countermark']) {
					unset($_POST['countermark']);
				} else {
					$_POST['countermark'] = 1;
				}
				$_POST['ref'] = $prd['ref'];
				$_POST['name'] = $prd['name'];
				$_POST['barcode'] = $prd['barcode'];
			}
		}

		// Mise à jour des informations présentes dans l'onglet Général
		if( isset($_POST['desc-long']) && strstr($_POST['desc-long'], 'data:image/png;base64') ){
			$error = _('L\'import d\'image Base64 n\'est pas autorisé dans la description longue du produit.');
		}elseif (!isset($_GET['prd'], $_POST['ref'], $_POST['desc'])) {
			$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.") . "\n" . _("Veuillez vérifier.");
		} else {
			if (!isset($_POST['brand'])) $_POST['brand'] = '';
			if (!isset($_POST['sell_unit'])) $_POST['sell_unit'] = '';
			if (!trim($_POST['name'])) { // || !is_numeric($_POST['price_ht']) || !is_numeric($_POST['tva']) ){
				$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.") . "\n" . _("Veuillez vérifier. Les champs marqués d'une * sont obligatoires.");
			} else {
				if (!isset($_POST['publish'])) $_POST['publish'] = false;
				if ($_POST['brand'] == '') $_POST['brand'] = 0;
				if ($_POST['sell_unit'] == '') $_POST['sell_unit'] = 0;

				$new_product = false;
				if ($_GET['prd'] == 0) {
					$new_product = true;
					if (!isset($_GET['cat']) || !is_numeric($_GET['cat']) || $_GET['cat'] <= 0) $_GET['cat'] = $_POST['alt-cat-id'];

					if (!is_numeric($_GET['cat']) || $_GET['cat'] <= 0){
						$error = _("Veuillez choisir une catégorie de classement pour ce produit.");
					}else {
						$_GET['prd'] = prd_products_add($_POST['ref'], $_POST['name'], $_POST['desc'], $_POST['brand'], $_POST['publish'], $_POST['weight'], $_POST['length'], $_POST['width'], $_POST['height'], $_POST['keywords']);
						if (!(isset($error) && $error)) {
							if (!$_GET['prd']) {
								$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du produit.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur");
							} elseif (!prd_products_add_to_cat($_GET['prd'], $_GET['cat'])) { // Classement dans la catégorie où le produit est ajouté
								$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du produit.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur");
							}
						}
					}
				}

				$title = '';
				// Si le produit est synchronisé, le champ "Titre" est sauvegardé
				if( $prd['is_sync'] ){
					$title = isset($_POST['title']) ? $_POST['title'] : '';
				}

				$desc = $_POST['desc'];
				$name = $_POST['name'];
				$desc_long = ria_strip_pre($_POST['desc-long']);

				if ($product_optimisation_cat > 0 && $prd) {
					$desc = $prd['desc'];
					$name = $prd['name'];
				}

				if (i18n::getLang() == $config['i18n_lng']) {
					$is_updated = prd_products_update(
						$_GET['prd'],
						$_POST['ref'],
						$name,
						$desc,
						$_POST['brand'],
						$_POST['publish'],
						$_POST['weight'],
						$_POST['length'],
						$_POST['width'],
						$_POST['height'],
						$_POST['keywords']
					);

					if (!isset($is_updated)) {
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du produit.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur");
					} else {
						if ($product_optimisation_cat > 0) {
							if (!prd_classify_update_optimisation($product_optimisation_cat, $_GET['prd'], (trim($title) != '' ? $title : $_POST['name']), $_POST['desc'], $desc_long)) {
								$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du produit.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur");
							}
						} else {
							if (isset($_POST['desc-long'])) {
								if ($prd === false || $prd['desc-long'] != $desc_long) {
									prd_products_update_desc_long($_GET['prd'], $desc_long);
								}
							}

							prd_products_update_title($_GET['prd'], $title);
						}


						// si le produit est correctement chargé et non synchronisé, ou s'il vient d'être créé
						if (($prd !== false && !$prd['is_sync']) || $new_product) {
							// Mise ou non du produit en sommeil
							prd_products_set_sleep($_GET['prd'], (isset($_POST['sleep']) && $_POST['sleep']));
							// Mise à jour de l'unité de vente
							prd_products_set_sell_unit($_GET['prd'], (isset($_POST['sell_unit']) && $_POST['sell_unit'] ? $_POST['sell_unit'] : null));
						}
					}
				} elseif (in_array(i18n::getLang(), $config['i18n_lng_used'])) {
					if ($product_optimisation_cat > 0) {
						$values = array(
							_FLD_CLY_PRD_TITLE => $title,
							_FLD_CLY_PRD_DESC => $_POST['desc'],
							_FLD_CLY_PRD_DESC_LONG => ria_strip_pre($_POST['desc-long'])
						);
						if (!fld_translates_add(array($product_optimisation_cat, $_GET['prd']), i18n::getLang(), $values)) {
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des informations traduites du produit.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur");
						}
					} else {
						$values = array(
							_FLD_PRD_DESC => $_POST['desc'],
							_FLD_PRD_DESC_LG => $desc_long
						);

						if ($prd['is_sync']) {
							$values[_FLD_PRD_TITLE] = $title;
						} else {
							$values[_FLD_PRD_NAME] = $name;
						}
						if (!fld_translates_add($_GET['prd'], i18n::getLang(), $values)) {
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des informations traduites du produit.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur");
						}
					}
				} else {
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du produit.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur");
				}

				if (!isset($error)) {
					if ($prd === false || $prd['is_orderable'] != isset($_POST['orderable'])) {
						prd_products_set_orderable($_GET['prd'], isset($_POST['orderable']));
					}

					if ($prd === false || $prd['countermark'] != isset($_POST['countermark'])) {
						prd_products_set_countermark($_GET['prd'], isset($_POST['countermark']));
					}

					// Indique si ce produit à des frais de port spécifique.
					prd_products_set_specific_shipping_charges($_GET['prd'], isset($_POST['specific_shipping_charges']));

					if ($prd === false || $prd['childonly'] != isset($_POST['childonly'])) {
						prd_products_set_childonly($_GET['prd'], isset($_POST['childonly']));
					}

					if ($prd === false || $prd['new_rule'] != $_POST['new']) {
						prd_products_set_new($_GET['prd'], $_POST['new']);
					}

					if ($prd === false || $prd['follow_stock'] != isset($_POST['follow_stock'])) {
						prd_products_set_follow_stock($_GET['prd'], isset($_POST['follow_stock']));
					}

					if (isset($_POST['alt-cat-id']) && is_numeric($_POST['alt-cat-id'])) {
						prd_products_add_to_cat($_GET['prd'], $_POST['alt-cat-id']);
						$no_redirect = false;
					}

					if (isset($_POST['alt-prd-id']) && is_numeric($_POST['alt-prd-id']) && $_POST['none'] == '' && $_POST['cat_selected'] == '') {
						prd_classify_set_canonical($_GET['prd']);
						prd_product_set_canonical($_GET['prd'], $_POST['alt-prd-id']);
					}

					if (isset($_POST['cat_selected']) && is_numeric($_POST['cat_selected'])) {
						prd_product_remove_canonical($_GET['prd']);
						prd_classify_set_canonical($_GET['prd'], $_POST['cat_selected']);
					}

					if (isset($_POST['none']) && $_POST['none'] == "true") {
						prd_product_remove_canonical($_GET['prd']);
						prd_classify_set_canonical($_GET['prd']);
					}

					if (isset($_POST['del-cat']) && is_numeric($_POST['del-cat'])) {
						prd_products_del_from_cat($_GET['prd'], $_POST['del-cat']);
						$_POST['save_stay'] = true;
					}

					// Enregistre le produit comme étant un produit frais de port
					if( isset($_POST['is_port_ref']) ){
						fld_object_values_set( $_GET['prd'], _FLD_IS_PORT, 'Oui' );
					}else{
						fld_object_values_set( $_GET['prd'], _FLD_IS_PORT, '' );
					}

					if (isset($_POST['index'])) {
						$index = true;
						if ($_POST['index'] == '0') {
							$index = false;
						}

						prd_products_set_index($_GET['prd'], $index);
					}
					if (!$prd['is_sync']) {
						if (!isset($_POST['barcode'])) {
							$_POST['barcode'] = '';
						}

						if ($prd === false || $prd['barcode'] != $_POST['barcode']) {
							prd_products_update_barcode($_GET['prd'], $_POST['barcode']);
						}
					}
				}
			}
		}

		// L'enregistrement a été réalisé avec succès
		if (!isset($error) && (isset($_POST['save']) || isset($_POST['save_stay'])) && (!isset($no_redirect) || isset($no_redirect) && !$no_redirect)) {
			if (isset($_POST['save_stay'])) {
				$_SESSION['success_save_prd'] = _("Vos modifications ont été enregistrées avec succès.");
				header('Location: /admin/catalog/product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd']);
				exit;
			} else {
				if (isset($_GET['brd']) && is_numeric($_GET['brd']) && $_GET['brd'] > 0) {
					header('Location: index.php?brd=' . $_GET['brd']);
					exit;
				} else {
					header('Location: index.php?cat=' . $_GET['cat']);
					exit;
				}
			}
		}
	}
}

// Bouton Dupliquer : crée une copie du produit, et redirige l'utilisateur vers cette copie
if (isset($_POST['duplicate'])) {
	$duplicate = prd_products_duplicate(false, $_GET['prd'], true, true, true, true, true, true);
	if ($duplicate) {
		header('Location: product.php?cat=' . $_GET['cat'] . '&prd=' . $duplicate);
		exit;
	} else {
		$error = _('Une erreur est survenue lors de la duplication de votre produit.');
	}
}

// Bouton Importer
if (isset($_POST['import']) && isset($_POST['dp_prd'])) {
	if ($_POST['dp_prd'] == null) {
		$error = _('Une erreur inattendue s\'est produite lors de la duplication du produit.') . '\n' . _('Veuillez réessayer ou prendre contact avec l\'administrateur.');
	} else {
		$price = (isset($_POST['dp_price']) ? true : false);
		$hierarchy = (isset($_POST['dp_hierarchy']) ? true : false);
		$fields = (isset($_POST['dp_fields']) ? true : false);
		$desc = (isset($_POST['dp_desc']) ? true : false);
		$images = (isset($_POST['dp_imgs']) ? true : false);

		if (!prd_products_duplicate($_GET['prd'], $_POST['dp_prd'], $price, $hierarchy, $fields, $desc, $images)) {
			$error = _("Une erreur inattendue s'est produite lors de la duplication du produit.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}
	}
}

// Enregistrement du type de nomenclature
if (isset($_POST['save-nomenclature'])) {
	if (isset($_POST['prd-type']) && is_numeric($_POST['prd-type'])) {
		$prd_is_sync = prd_products_get_is_sync($_GET['prd']);

		if ($prd_is_sync) {
			$error = _('Ce produit est synchronisé, son type de nomenclature ne peut donc pas être modifié.');
		} elseif (!prd_products_set_nomenclature_type($_GET['prd'], $_POST['prd-type'])) {
			$error = _("Une erreur inattendue s'est produite lors du changement du type du produit.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur.");
		} else {
			$success = _("Le type de nomenclature a été sauvegardé avec succès.");
		}
	}
}

// Bouton ajout de nomenclature
if (isset($_POST['add-nomenclature'])) {
	if (
		!isset($_POST['nomenclature_ref']) || !isset($_POST['nomenclature_prd'])
		|| $_POST['nomenclature_ref'] == '' || !is_numeric($_POST['nomenclature_prd'])
	) {
		$error = _("Un ou plusieurs paramètres sont manquants.") . "\n" . _("Veuillez réessayer ou prendre contact avec l'administrateur.");
	} else {
		$reference = trim(current(explode(' - ', $_POST['nomenclature_ref'])));

		// test si le produit est un frais de port
		if (prd_products_is_port($reference)) {
			$error = _('Le produit est un frais de port et ne peut être ajouté en tant que composant.');
		} else {
			$childs = prd_products_get_byref($reference);
			if (!$childs || ria_mysql_num_rows($childs) <= 0) {
				$error = _('Le produit enfant n\'a pas été trouvé.');
			} else {
				$child = ria_mysql_fetch_array($childs);

				if (isset($_POST['nomenclature_price'])) {
					$_POST['nomenclature_price'] = str_replace(',', '.', $_POST['nomenclature_price']);
					if (trim($_POST['nomenclature_price']) != '' && !is_numeric($_POST['nomenclature_price'])) {
						$error = _('Le prix HT unitaire du produit contient une valeur erronée. Veuillez réessayer.');
					}
				}

				if (!isset($error)) {
					$info_child = array(
						array(
							'prd' => $child['id'],
							'qte' => isset($_POST['nomenclature_qte']) && is_numeric($_POST['nomenclature_qte']) ? $_POST['nomenclature_qte'] : 1,
							'price_ht' => isset($_POST['nomenclature_price']) && is_numeric($_POST['nomenclature_price']) ? parseFloat($_POST['nomenclature_price']) : null
						)
					);

					if (!prd_nomenclatures_products_add($_POST['nomenclature_prd'], $info_child)) {
						$error = _('L\'ajout du produit n\'a pas fonctionné pour une raison inconnue. Veuillez réessayer ou prendre contact avec nous pour nous signaler l\'erreur.');
					} else {
						$success = _("Le produit a bien été enregistré.");
					}
				}
			}
		}
	}
}

// Supression d'un nomenclature
if (isset($_GET['del-nomenclature']) && is_numeric($_GET['del-nomenclature'])) {
	if (!prd_nomenclatures_products_update_qte($_GET['prd'], $_GET['del-nomenclature'], 0)) {
		$error = _('Une erreur est survenue lors de la suppression de la nomenclature.');
	} else {
		$success = _('La nomenclature a été supprimée.');
	}
}

// Charge le produit pour modification
$rprd = false;
if (isset($_GET['prd']) && is_numeric($_GET['prd']) && $_GET['prd'] > 0) {
	$rprd = prd_products_get(
		$_GET['prd'],
		'',
		0,
		false,
		isset($_GET['cat']) ? $_GET['cat'] : 0,
		$rowstart = 0,
		$maxrows = -1,
		$catchilds = false,
		$centralized = false,
		$new = false,
		$destockage = false,
		$promotion = false,
		$childs = false,
		$sort = false,
		$supplier = false,
		$uncompleted = false,
		$have_image_only = false,
		$hide_sleeping = false,
		$fld = false,
		$mdl = false,
		$countermark = null,
		$have_stock = null,
		$dps = false,
		$orderable = null,
		$isSync = false,
		$exclude = false,
		$bestsellers = false,
		$ordered = null,
		$or_between_val = false,
		$no_weight = false,
		$no_related = false,
		$exclude_cat = false,
		$with_price = false
	);

	if( !ria_mysql_num_rows($rprd) ){
		header('Location: /admin/catalog/index.php');
		exit;
	}
}

if ($rprd && ria_mysql_num_rows($rprd)) {
	$prd = ria_mysql_fetch_assoc($rprd);
	$prd['orderable'] = prd_products_get_orderable( $prd['id'] );
} else {

	// colonnes par défaut pour un nouveau produit
	$all_columns = array(
		'img_id', 'ref', 'name', 'title', 'brand', 'brd_id', 'sell_unit', 'sun_id', 'publish', 'date_published', 'date_first_published', 'price_ht',
		'price_ttc', 'desc', 'desc-long', 'stock', 'stock_res', 'stock_com', 'stock_livr', 'weight', 'weight_net', 'length', 'width', 'height',
		'keywords', 'date_created', 'date_modified', 'tag_title', 'tag_desc', 'keywords', 'url_alias', 'barcode', 'orderable',
		'countermark', 'childonly', 'sleep', 'is_sync', 'id', 'garantie', 'new_rule'
	);

	$prd = array();
	foreach ($all_columns as $column) {
		$prd[$column] = '';
		if (isset($_POST[$column])) {
			// valeurs POST éventuelles
			$prd[$column] = $_POST[$column];
		} elseif (in_array($column, array('orderable', 'countermark', 'childonly', 'sleep', 'is_sync'))) {
			$prd[$column] = false;
		} elseif (in_array($column, array('id', 'garantie', 'new_rule'))) {
			$prd[$column] = 0;
		}
	}

	$prd['tva'] = _TVA_RATE_DEFAULT;
	$prd['follow_stock'] = $config['products_follow_stock_default'];

	if( isset($_GET['prdname']) ){
		$prd['name'] = ucfirst(trim($_GET['prdname']));
		$prd['specific_shipping_charges'] = 0;
	}
}

// Enregistrer le mapping des catégories de comparateurs de prix
if (isset($_POST['save-ctr'])) {
	$tab = 'comparators';
	if (isset($_POST['chooseexp'])) {
		foreach ($_POST['chooseexp'] as $ctr => $choose) {
			if ($choose != 0) {
				continue;
			}
			if (!ctr_catalogs_del($ctr, $_GET['prd'])) {
				$error = _("Une erreur inattendue s'est produits lors de l'enregistrement.") . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				break;
			}
		}
	}

	if (!isset($error)) {
		header('Location: /admin/catalog/product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=comparators');
		exit;
	}
}

// Enregistrement des points de fidélité
if (isset($_POST['save-rwd-prd'], $_POST['pts'])) {

	$date_start = null;
	$date_end = null;
	$heure_start = isset($_POST['hour_date_start'])  && $_POST['hour_date_start'] != "" ? $_POST['hour_date_start'] : "00:00";
	$heure_end = isset($_POST['hour_date_start']) && $_POST['hour_date_start'] != ""  ? $_POST['hour_date_end'] : "00:00";


	if (isset($_POST['date_start']) && $_POST['date_start'] != "") {
		if (!preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/', $_POST['date_start'])) {
			$error = _("La date de début de publication doit être saisie au format jj/mm/aaaa.") . "\n" . _("Veuillez réessayer.");
		} elseif (!preg_match('/^[0-9]{1,2}:[0-9]{2}$/', $heure_start)) {
			$error = _("L'heure de début de publication doit être saisie au format hh:mm.") . "\n" . _("Veuillez réessayer");
		} else {
			$date_start = $_POST['date_start'] . ' ' . $heure_start;
		}
	}

	if (isset($_POST['date_end']) && $_POST['date_end'] != "") {
		if (!preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/', $_POST['date_end'])) {
			$error = _("La date de fin de publication doit être saisie au format jj/mm/aaaa.") . "\n" . _("Veuillez réessayer.");
		} elseif (!preg_match('/^[0-9]{1,2}:[0-9]{2}$/', $heure_end)) {
			$error = _("L'heure de fin de publication doit être saisie au format hh:mm.") . "\n" . _("Veuillez réessayer");
		} else {
			$date_end = $_POST['date_end'] . ' ' . $heure_end;
		}
	}

	if ($date_end && !$date_start) {
		$date_start = date('d/m/Y');
		$date_start .= " 00:00";
	}

	$prf = $_POST['prf-id'];
	$pts = $_POST['pts'];
	if (!isset($error)) {
		if (!rwd_prd_rewards_add($_GET['prd'], $prf, $pts, $date_start, $date_end)) {
			$error = _("Une erreur inattendue s'est produite lors de l'ajout des points de fidélité.") . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
		}
	}

	if (!isset($error)) {
		header('Location: /admin/catalog/product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=rewards');
		exit;
	}
}

// Enregistrement du modèle pour les comparateurs de prix / places de marché
if (isset($_POST['save-ctr-mdl'])) {
	if (!isset($_POST['ctr-mdl-id'], $_POST['ctr-mdl-title'], $_POST['ctr-mdl-desc'])) {
		$error = _("Une ou plusieurs informations obligatoires sont manquantes.");
	} else {
		$delete = false;
		if ($_POST['ctr-mdl-id'] == 0) {
			if (trim($_POST['ctr-mdl-title']) != '' || trim($_POST['ctr-mdl-desc']) != '') {
				$_POST['ctr-mdl-id'] = ctr_models_add($_GET['prd'], '', '', ($tab == 'comparators' ? false : true), $_POST['ctr-mdl-title'], $_POST['ctr-mdl-desc']);
				if (!$_POST['ctr-mdl-id']) {
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du modèle.") . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
		} else {
			if (trim($_POST['ctr-mdl-title']) == '' && trim($_POST['ctr-mdl-desc']) == '' && !ctr_models_has_images($_POST['ctr-mdl-id'])) {
				if (!ctr_models_del($_POST['ctr-mdl-id'], $_GET['prd'])) {
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du modèle.") . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				}

				$delete = true;
			} else {
				if (!ctr_models_update($_POST['ctr-mdl-id'], '', '', $_POST['ctr-mdl-title'], $_POST['ctr-mdl-desc'])) {
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du modèle.") . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
		}

		if (!$delete && !isset($error)) {
			$ar_ctr = array();

			$rctr = ctr_comparators_get(0, true, false, ($tab == 'comparators' ? false : true));
			if ($rctr) {
				while ($ctr = ria_mysql_fetch_array($rctr)) {
					$ar_ctr[] = $ctr['id'];
				}
			}

			if (sizeof($ar_ctr) && !ctr_models_comparators_add($_POST['ctr-mdl-id'], $ar_ctr)) {
				$error = _("Une erreur inattendue s'est produite lors de l'application du modèle.") . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
			} elseif (!sizeof($ar_ctr) && !ctr_models_comparators_del($_POST['ctr-mdl-id'])) {
				$error = _("Une erreur inattendue s'est produite lors de l'application du modèle.") . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
			}
		}
	}

	if (!isset($error)) {
		header('Location: /admin/catalog/product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=' . $tab . '&perso-ctr-mdl=1');
		exit;
	}
}

// Suppression du modèle personnalisé pour les comparateurs de prix / places de marché
if (isset($_POST['del-mdl'])) {
	if (!isset($_POST['ctr-mdl-id']) || !is_numeric($_POST['ctr-mdl-id']) || !$_POST['ctr-mdl-id']) {
		$error = _("Une ou plusieurs informations obligatoires sont manquantes empêchant ainsi la suppression du modèle.") . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
	} elseif (!ctr_models_del($_POST['ctr-mdl-id'], $_GET['prd'])) {
		$error = _("Une erreur inattendue s'est produite lors de la suppression du modèle.") . "\n" . _("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
	}

	if (!isset($error)) {
		header('Location: /admin/catalog/product.php?cat=' . $_GET['cat'] . '&prd=' . $_GET['prd'] . '&tab=' . $tab);
		exit;
	}
}

// Chargement des actions onglet "Référencement"
view_admin_tab_referencement_actions(CLS_PRODUCT, $_GET['prd'], (isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng']));

// Mise en place des actions pour les documents de produit
view_admin_tab_documents_actions(CLS_PRODUCT);

if (!isset($_SESSION['websitepicker']) || !is_numeric($_SESSION['websitepicker']) || !$_SESSION['websitepicker']) {
	$_SESSION['websitepicker'] = $config['wst_id'];
}

if (isset($_GET['wst']) && is_numeric($_GET['wst']) && $_GET['wst']) {
	$_SESSION['websitepicker'] = $_GET['wst'];
}

// Fil d'ariane
Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
	->push( _('Catalogue'), '/admin/catalog/index.php' );

// Ajoute la hiérarchie des catégories au fil d'ariane
if( isset($_GET['cat']) && $_GET['cat'] ){
	$parents = prd_categories_parents_get($_GET['cat']);
	while( $r = ria_mysql_fetch_array($parents) ){
		Breadcrumbs::add( $r['title'], '/admin/catalog/index.php?cat='.$r['id'] );
	}

	$cat = ria_mysql_fetch_array(prd_categories_get($_GET['cat']));
	Breadcrumbs::add( $cat['title'], '/admin/catalog/index.php?cat='.$cat['id'] );
}
Breadcrumbs::add( $prd['name'] );

// Définit le titre de la page
define('ADMIN_PAGE_TITLE', $prd['ref'] . ' - ' . $prd['name'] . ' - ' . _('Catalogue'));
require_once('admin/skin/header.inc.php');

?>
<h2><?php print view_prd_is_sync($prd) . ' ' . htmlspecialchars($prd['ref'] . ' - ' . $prd['name']); ?></h2>
<div class="messages">
	<?php
	if( isset($_SESSION['referencement_edit_success']) ){
		$success = $_SESSION['referencement_edit_success'];
		unset($_SESSION['referencement_edit_success']);
	}
	if( isset($_SESSION['referencement_edit_error']) ){
		$error = $_SESSION['referencement_edit_error'];
		unset($_SESSION['referencement_edit_error']);
	}

	if( isset($_SESSION['success_save_prd']) ){
		$success = $_SESSION['success_save_prd'];
		unset($_SESSION['success_save_prd']);
	}

	if( isset($error) ){
		print '<div class="error">' . nl2br(htmlspecialchars($error)) . '</div>';
		unset($error);
	}elseif( isset($success) ){
		print '<div class="error-success">' . nl2br(htmlspecialchars($success)) . '</div>';
	}elseif( isset($notice) ){
		print '<div class="notice">' . nl2br($notice) . '</div>';
	}
	?>
</div>
<?php

// Récupère la langue
$lng = view_selected_language();

// Récupère les informations traduites
if ($_GET['prd'] != 0 && $lng != $config['i18n_lng']) {
	$tsk_prd = fld_translates_get(CLS_PRODUCT, $prd['id'], $lng, $prd, array(_FLD_PRD_TITLE => 'title', _FLD_PRD_NAME => 'name', _FLD_PRD_DESC => 'desc', _FLD_PRD_DESC_LG => 'desc-long', _FLD_PRD_TAG_TITLE => 'tag_title', _FLD_PRD_TAG_DESC => 'tag_desc', _FLD_PRD_TAG_KEYWORD => 'keywords'), true);
	$prd['name'] = $prd['is_sync'] ? $prd['name'] : $tsk_prd['name'];
	$prd['title'] = $tsk_prd['title'];
	$prd['desc'] = $tsk_prd['desc'];
	$prd['desc-long'] = $tsk_prd['desc-long'];
	$prd['tag_title'] = $tsk_prd['tag_title'];
	$prd['tag_desc'] = $tsk_prd['tag_desc'];
	$prd['keywords'] = $tsk_prd['keywords'];
	if (isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat']) {
		$tsk_url = fld_object_values_get(array($_GET['cat'], $_GET['prd']), _FLD_PRD_URL, $lng, false, true);

		$prd['cly_url_alias'] = trim($tsk_url) != '' ? $tsk_url : (isset($prd['cly_url_alias']) ? $prd['cly_url_alias'] : '');
		$prd['url_perso'] = fld_object_values_get(array($_GET['cat'], $_GET['prd']), _FLD_CLY_URL_PERSO, $lng, false, true);

		$prd['url_alias'] = trim($prd['url_perso']) != '' ? $prd['url_perso'] : $prd['cly_url_alias'];
	}
}

if ($product_optimisation_cat > 0) {
	try {
		$prd = prd_classify_optimise_product($product_optimisation_cat, $prd);
	} catch (Exception $e) {
		error_log("Fiche produit erreur chargement optimisation : " . $e->getMessage());
	}
}

?>

<form name="edit" id="product" action="<?php echo $url ?>" method="post" enctype="multipart/form-data">
	<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="<?php print _('Général') ?>" <?php if ($tab == 'general') print 'class="selected"'; ?> /></li>
		<?php if (view_admin_show_tab_fields(CLS_PRODUCT, $_GET['prd'])) { ?>
			<li><input type="submit" name="tabFields" value="<?php print _('Avancé') ?>" <?php if ($tab == 'fields') print 'class="selected"'; ?> /></li>
		<?php } ?>
		<li><input type="submit" name="tabPrices" value="<?php print _('Tarification') ?>" <?php if ($tab == 'prices') print 'class="selected"'; ?> /></li>
		<?php if (!prd_products_is_port_id($_GET['prd'])) { ?>
			<li><input type="submit" name="tabNomenclature" value="<?php print _('Nomenclature') ?>" <?php if ($tab == 'nomenclature') print 'class="selected"'; ?> /></li>
		<?php } ?>
		<li><input type="submit" name="tabLinked" value="<?php print _('Articles liés') ?>" <?php if ($tab == 'linked') print 'class="selected"'; ?> /></li>
		<li><input type="submit" name="tabImages" value="<?php print _('Médiathèque') ?>" <?php if ($tab == 'images') print 'class="selected"'; ?> /></li>
		<?php if (isset($prd['follow_stock']) && $prd['follow_stock']) {
			?>
			<li><input type="submit" name="tabStocks" value="<?php print _('Stocks') ?>" <?php if ($tab == 'stocks') print 'class="selected"'; ?> /></li>
		<?php } ?>
		<?php if (isset($prd['orderable']) && $prd['orderable']) { ?>
			<li><input type="submit" name="tabDelivery" value="<?php print _('Livraison') ?>" <?php if ($tab == 'delivery') print 'class="selected"'; ?> /></li>
		<?php } ?>
		<?php if (tnt_tenants_have_websites()) { ?>
			<li><input type="submit" name="tabReviews" value="<?php print _('Avis') ?>" <?php if ($tab == 'reviews') print 'class="selected"'; ?> /></li>
			<li><input type="submit" name="tabReferencing" value="<?php print _('Référencement') ?>" <?php if ($tab == 'ref') print 'class="selected"'; ?> /></li>
		<?php } ?>
		<?php if (isset($config['ctr_active']) && $config['ctr_active']) {
			if (gu_user_is_authorized('_RGH_ADMIN_COMPARATOR')) {
				$r_ctr = ctr_comparators_get(0, true, false, false);

				if ($r_ctr && ria_mysql_num_rows($r_ctr)) {
					print '
						<li><input type="submit" name="tabComparators" value="' . _('Comparateurs') . '" ' . ($tab == 'comparators' ? 'class="selected"' : '') . ' /></li>
					';
				}
			}

			if (gu_user_is_authorized('_RGH_ADMIN_MARKET_PLACE')) {
				$r_mtk = ctr_comparators_get(0, true, false, true);

				if ($r_mtk && ria_mysql_num_rows($r_mtk)) {
					print '
						<li><input type="submit" name="tabMarketplace" value="' . _('Places de Marché') . '" ' . ($tab == 'marketplace' ? 'class="selected medium"' : 'class="medium"') . ' /></li>
					';
				}
			}
		} ?>
		<li><input type="submit" name="tabStats" value="<?php print _('Statistiques') ?>" <?php if ($tab == 'stats') print 'class="selected"'; ?> /></li>
		<?php if (gu_user_is_authorized('_RGH_ADMIN_TOOL_REWARD')) { ?>
			<li><input type="submit" name="tabRewards" value="<?php print _('Fidélité') ?>" <?php if ($tab == 'rewards') print 'class="selected"'; ?> /></li>
		<?php } ?>
	</ul>
	<?php
	if (in_array($tab, array('linked', 'nomenclature', 'reviews', 'comparators', 'marketplace'))) {
		?>
	</form>
<?php
}
?>

<div id="tabpanel" data-tab="<?php echo $tab ?>">
	<?php if ($tab == 'general') {

		// Filtres dédiés à la gestion des traductions et aux classements multiples
		$stats_menus = view_translate_menu('product.php?cat=' . $_GET['cat'] . '&amp;prd=' . $_GET['prd'] . '&amp;tab=general', $lng, true);
		$stats_menus .= view_product_classement_menu($_GET['prd'], is_numeric($_GET['cat']) ? $_GET['cat'] : 0, $product_optimisation_cat, true);

		if (trim($stats_menus)) {
			?>
			<div class="stats-menu">
				<?php print $stats_menus; ?>
				<div class="clear"></div>
			</div>
		<?php } ?>

		<input type="hidden" name="weight" value="<?php print $prd['weight']; ?>" />
		<input type="hidden" name="length" value="<?php print $prd['length']; ?>" />
		<input type="hidden" name="width" value="<?php print $prd['width']; ?>" />
		<input type="hidden" name="height" value="<?php print $prd['height']; ?>" />
		<input type="hidden" name="keywords" value="<?php print $prd['keywords']; ?>" />

		<?php if ($prd['is_sync']) { ?>
			<div class="notice"><?php print _('Les champs grisés sont synchronisés avec votre ERP : ils ne sont pas modifiables directement dans RiaShop'); ?></div>
		<?php } ?>

		<table id="editproduct">
			<tbody>
				<tr>
					<th colspan="2"><?php print _('Propriétés générales') ?></th>
				</tr>
				<tr>
					<td><label for="ref"> <span class="mandatory">*</span> <?php print _('Référence :') ?></label></td>
					<td><?php
						if ($prd['is_sync']) { ?>
							<span class="readonly"><?php print htmlspecialchars($prd['ref']); ?></span> <?php
																								} else { ?>
							<input type="text" name="ref" id="ref" maxlength="20" value="<?php print htmlspecialchars($prd['ref']); ?>" onblur="this.value = prdFormatRef(this.value)" /><?php
																																													}
																																													?>
					</td>
				</tr>
				<tr>
					<td class="col180px"><label for="name"><span class="mandatory">*</span> <?php print _('Désignation :') ?></label></td>
					<td><?php
						if ($prd['is_sync']) { ?>
							<span class="readonly"><?php print htmlspecialchars($prd['name']); ?></span><?php
																								} else { ?>
							<input type="text" name="name" id="name" maxlength="75" value="<?php print htmlspecialchars($prd['name']); ?>" /><?php
																																		}
																																		?>
					</td>
				</tr>
				<?php if( $prd['is_sync'] ){ ?>
					<tr>
						<td><label for="title"><?php print _('Titre :') ?></label></td>
						<td><input type="text" name="title" id="title" value="<?php if ($prd['name'] != $prd['title']) print htmlspecialchars($prd['title']); ?>" maxlength="75" /></td>
					</tr>
				<?php } ?>
				<tr>
					<td>
						<label for="brand"><?php print _('Marque :') ?></label><br/>
					</td>
					<td style="display: flex; flex-wrap: nowrap"><?php

						require_once('brands.inc.php');

						if ($prd['is_sync'] && (!isset($config['can_edit_prd_brand']) || !$config['can_edit_prd_brand'])) {
							// si c'est synchro, on cherche seulement la valeur demandée
							$brd_name = prd_brands_get_name($prd['brd_id']);
							if( $brd_name ){ ?>
								<span class="readonly"><?php print htmlspecialchars($brd_name); ?></span>
							<?php } else { ?>
								<span class="readonly"><?php print _('Non renseigné dans l\'ERP'); ?></span>
							<?php }
						} else {
							$brands = prd_brands_get();
						?>
							<input type="hidden" id="brand" name="brand" />
							<input type="text" style="max-width: 350px; margin-right: 5px" autocomplete="off" list="brandName" id="brandSelect" name="brandSelect" placeholder="<?php print _('Sélectionnez ou ajoutez une marque'); ?>" />
							<datalist id="brandName">
								<?php
								while ($r = ria_mysql_fetch_array($brands)) {
									if ($prd['brd_id'] == $r['id'] || (isset($_GET['brd']) && is_numeric($_GET['brd']) && $_GET['brd'] == $r['id'])) {
								?>
									<option value="<?php print htmlspecialchars($r['title']); ?>" id="<?php print $r['id']; ?>">
									<script> $('#brandSelect').val("<?php print htmlspecialchars($r['title']); ?>") </script>
								<?php }else{ ?>
									<option value="<?php print htmlspecialchars($r['title']); ?>" id="<?php print $r['id']; ?>">
								<?php }
								} ?>
							</datalist>
							<input type="button" class="btn-main" id="btn-brd-add" value="<?php print _('Ajouter une marque'); ?>" disabled />
						<?php } ?>
					</td>
				</tr>
				<tr>
					<td><label for="barcode"><?php print _('Code à barres :'); ?></label></td>
					<td><?php
						if( $prd['is_sync'] ){
							if( trim($prd['barcode']) != '' ){
								$barcode = $prd['barcode'];
							} else {
								$barcode = _('Non renseigné dans l\'ERP');
							}
						?>
						<span class="readonly"><?php print htmlspecialchars($barcode); ?></span>
					<?php } else { ?>
						<input type="text" name="barcode" id="barcode" value="<?php print htmlspecialchars($prd['barcode']); ?>" size="19" maxlength="19" />
					<?php } ?>
					</td>
				</tr>
				<?php
					// Références fabriquant
					if( $prd['is_sync'] && $prd['id']>0 ){
						$sup_references = array();
						$sup = prd_suppliers_get( $prd['id'], 0, '', true );
						if( $sup && ria_mysql_num_rows($sup) ){
							$ref_sup = ria_mysql_result( $sup, 0, 'ref' );
							if( trim($ref_sup) ){
								$sup_references[] = $ref_sup;
							}
						}
						if( sizeof($sup_references) ){ ?>
							<tr>
								<td><label for=""><?php print sizeof($sup_references)==1 ? _('Référence fournisseur :') : _('Référence(s) fournisseur :'); ?></label></td>
								<td><span class="readonly"><?php print implode( ', ', $sup_references ); ?></span></td>
							</tr>
						<?php }
					}
				?>

				<?php
				$sell_unit = '';
				$runit = false;

				if ($prd['is_sync']) {
					if (is_numeric($prd['sun_id']) && $prd['sun_id']) {
						$sell_unit = prd_sell_units_get_name($prd['sun_id']);
					}
				} else {
					$runit = prd_sell_units_get();
				}

				if (trim($sell_unit) != '' || ($runit && ria_mysql_num_rows($runit))) {
					?>
					<tr>
						<td><label for="sell_unit"><?php print _('Unité de vente :'); ?></label></td>
						<td><?php
							// Chargement de l'unité de vente du produit selon s'il est synchronisé ou non
							if ($prd['is_sync']) {
								?><span class="readonly"><?php print htmlspecialchars($sell_unit); ?></span><?php
																												} elseif ($runit && ria_mysql_num_rows($runit)) {
																													?><select name="sell_unit" id="sell_unit">
									<option value=""><?php print _('Sélectionnez une unité'); ?></option>
									<?php
									while ($r = ria_mysql_fetch_assoc($runit)) {
										if ($prd['sun_id'] == $r['id'] || (isset($_GET['sun']) && is_numeric($_GET['sun']) && $_GET['sun'] == $r['id'])) { ?>
											<option value="<?php print $r['id']; ?>" selected="selected"><?php print htmlspecialchars($r['name']); ?></option> <?php
																																						} else { ?>
											<option value="<?php print $r['id']; ?>"><?php print htmlspecialchars($r['name']); ?></option> <?php
																																	}
																																}
																																?>
								</select><?php
									}
									?></td>
					</tr>
				<?php } ?>

				<tr <?php print view_data_if_is_sync($prd['is_sync']); ?>>
					<td><label for="publish"><?php print _('Publication :'); ?></label></td>
					<td>
						<?php if (isset($config['catalog_parent_publish']) && $config['catalog_parent_publish'] == 'auto' && prd_products_is_parent($prd['id']) && !$prd['is_sync']) { ?>
							<div class="notice inline-block"><?php print _('La publication est dépendante de celle de ses articles enfants') ?></div>
						<?php } else { ?>
							<input type="checkbox" class="checkbox" name="publish" id="publish" value="1" <?php if ($prd['publish']) print 'checked="checked"'; ?> <?php print view_data_if_is_sync($prd['is_sync'], true, "disabled"); ?> />
							<label for="publish" <?php print($prd['is_sync'] ? 'class="readonly"' : ''); ?>><?php print _('Afficher ce produit dans la boutique'); ?></label>
						<?php } ?>
					</td>
				</tr>
				<tr>
					<td><label for="orderable"><?php print _('Commande :'); ?></label></td>
					<td>
						<input type="checkbox" class="checkbox" name="orderable" id="orderable" value="1" <?php print $prd['orderable'] ? 'checked="checked"' : '';
																											?> />
						<label for="orderable"><?php print _('Autoriser les commandes de ce produit') ?></label>
					</td>
				</tr>
				<tr <?php print view_data_if_is_sync($prd['is_sync']); ?>>
					<td><label for="countermark"><?php print _('Contre-marque :'); ?></label></td>
					<td>
						<input type="checkbox" class="checkbox" name="countermark" id="countermark" value="1" <?php print $prd['countermark'] ? 'checked="checked"' : ''; ?> <?php print view_data_if_is_sync($prd['is_sync'], true, "disabled"); ?> />
						<label for="countermark" <?php print($prd['is_sync'] ? 'class="readonly"' : ''); ?>><?php print _('Ce produit est vendu en contre-marque') ?></label>
					</td>
				</tr>
				<tr>
					<td><label for="childonly"><?php print _('Affichage :'); ?></label></td>
					<td>
						<input type="checkbox" class="checkbox" name="childonly" id="childonly" value="1" <?php print $prd['childonly'] ? 'checked="checked"' : ''; ?> />
						<label for="childonly"><?php print _('N\'afficher ce produit que comme article lié') ?></label>
					</td>
				</tr>
				<tr>
					<td><label for="new"><?php print _('Nouveauté :'); ?></label></td>
					<td>
						<select name="new" id="new">
							<option value="0" <?php if ($prd['new_rule'] == '0') print 'selected="selected"' ?>>
								<?php print _('Automatique') ?>
							</option>
							<option value="1" <?php if ($prd['new_rule'] == '1') print 'selected="selected"' ?>><?php print _('Toujours afficher comme Nouveau') ?>
							</option>
							<option value="-1" <?php if ($prd['new_rule'] == '-1') print 'selected="selected"' ?>><?php print _('Ne jamais afficher comme Nouveau') ?>
							</option>
						</select>
					</td>
				</tr>
				<tr <?php print view_data_if_is_sync($prd['is_sync'], true); ?>>
					<td><label for="sleep"><?php print _('Sommeil :'); ?></label></td>
					<td>
						<input type="checkbox" class="checkbox" name="sleep" id="sleep" value="1" <?php print $prd['sleep'] ? 'checked="checked"' : ''; ?> <?php print view_data_if_is_sync($prd['is_sync'], true, "disabled"); ?> />
						<label for="sleep" <?php print($prd['is_sync'] ? ' class="readonly"' : ''); ?>><?php print _('Mettre ce produit en sommeil') ?></label>
					</td>
				</tr>
				<tr>
					<td><label for="follow_stock"><?php print _('Suivi en stock :'); ?></label></td>
					<td>
						<input type="checkbox" class="checkbox" name="follow_stock" id="follow_stock" value="1" <?php print $prd['follow_stock'] ? 'checked="checked"' : ''; ?> />
						<label for="follow_stock"><?php print _('Suivre ce produit en stock') ?></label>
					</td>
				</tr>
				<tr>
					<td>
						<label for="specific_shipping_charges"><?php print _('Frais de port spécifique :'); ?></label>
					</td>
					<td>
						<input type="checkbox" class="checkbox" id="specific_shipping_charges" name="specific_shipping_charges" value="1" <?php print prd_products_has_specific_shipping_charges($prd['id']) ? 'checked' : ''; ?>>
						<label for="specific_shipping_charges"><?php print _('Ce produit est soumis à des frais de port spécifiques')?></label>
					</td>
				</tr>

				<?php
					// Si les zones de livraison sont administrable et détermine les frais de port
					// Alors pour rendre autonome l'utilisateur, il est possible de dire qu'un article est un article frais de port
					if( $config['dlv_active_port'] ){
						print '<tr>'
							.'<td>'
								.'<label for="is_port_ref">'._('Frais de port :').'</label>'
							.'</td>'
							.'<td>'
								.'<input type="checkbox" class="checkbox" id="is_port_ref" name="is_port_ref" value="1"
									'.( in_array($prd['ref'], $config['dlv_prd_references']) ? 'checked="checked"' : '' ).'/> '
								.'<label for="is_port_ref">'._('Ce produit est identifié comme un frais de port').'</label>'
							.'</td>'
						.'</tr>';
					}
				?>

				<tr>
					<th colspan="2"><?php print _('Classement') ?></th>
				</tr>
				<tr>
					<td><span class="mandatory">* </span><?php print _('Catégories :'); ?></td>
					<td class="td_classify">
						<?php
						if ($prd['id'] > 0) {
							$cats = prd_products_categories_get($prd['id']);
							$cats_publish = prd_products_categories_get($prd['id'], true, true);
							$nb_cat_publish = ria_mysql_num_rows($cats_publish);
							$cats_publish_ids = array();

							$cat_canonical = false;
							$rcat_canonical = prd_products_categories_get($prd['id'], true, true, true);
							if( $rcat_canonical && ria_mysql_num_rows($rcat_canonical) ){
								$cat_canonical = ria_mysql_fetch_array($rcat_canonical);
							}

							while ($c_publish = ria_mysql_fetch_array($cats_publish)) {
								$cats_publish_ids[] = $c_publish['cat'];
							}
							while ($c = ria_mysql_fetch_array($cats)) {
								print view_cly_is_sync($c);
								if (!$c['cly_is_sync']) {
									print '<input type="image" src="../images/del-cat-disabled.svg" name="del-cat-' . $c['cat'] . '" title="' . _('Retirer ce produit de la catégorie') . ' ' . htmlspecialchars($c['title']) . '" class="icon-del-cat" /> ';
								}

								$parents = prd_categories_parents_get($c['cat']);
								while ($p = ria_mysql_fetch_array($parents)){
									print '<a href="/admin/catalog/index.php?cat=' . $p['id'] . '">' . htmlspecialchars($p['title']) . '</a> &raquo; ';
								}
								print '<a href="/admin/catalog/index.php?cat=' . $c['cat'] . '">' . htmlspecialchars($c['title']) . '</a><br />';
							}
						} elseif (isset($_GET['cat']) && $_GET['cat'] > 0) {
							$parents = prd_categories_parents_get($_GET['cat']);
							while ($p = ria_mysql_fetch_array($parents)){
								print htmlspecialchars( $p['title'] ). ' &raquo; ';
							}
							$cat = ria_mysql_fetch_array(prd_categories_get($_GET['cat']));
							print htmlspecialchars( $cat['title'] ). '<br />';
						}
						?>
					</td>
				</tr>
				<tr>
					<td><label for="alt-cat-name"><?php print _('Ajouter :'); ?></label></td>
					<td style="display: flex; flex-wrap: nowrap">
						<input type="hidden" name="alt-cat-id" id="alt-cat-id" value="" />
						<input type="hidden" name="alt-cat-parent-id" id="alt-cat-parent-id" value="" />
						<input type="text" name="alt-cat-name" id="alt-cat-name" value="" style="max-width: 341px; margin-right: 5px"
											readonly="readonly"/>
						<input type="button" value="<?php print _('Ajouter une catégorie'); ?>" class="button" onclick="cat_choose(this.form)" />
					</td>
				</tr>
				<?php if ($prd['id'] > 0 && tnt_tenants_have_websites()) { ?>
					<tr>
						<td><label for="canonical_url"><?php print _('Url canonique :'); ?></label></td>
						<td>
							<select id="canonical_url" name="canonical_url">
								<option value="none"></option>
								<?php
								if ($cats_publish && ria_mysql_num_rows($cats_publish)) {
									ria_mysql_data_seek( $cats_publish, 0 );
									while ($c = ria_mysql_fetch_array($cats_publish)) {
										$parents = prd_categories_parents_get($c['cat']);
										$selected = false;
										if (prd_classify_get_canonical_cat($prd['id']) == $c['cat']) {
											$selected = true;
										}
										$txt_canonical = "<option value='" . $c['cat'] . "' " . ($selected ? "selected='selected'" : "") . ">";
										$strong = false;
										if ($c['url_is_canonical']) {
											$strong = true;
										}
										$txt_canonical .= $strong ? "<strong>" : "";
										while ($p = ria_mysql_fetch_array($parents)){
											$txt_canonical .= htmlspecialchars( $p['title'] ) . ' &raquo; ';
										}
										$txt_canonical .= htmlspecialchars( $c['title'] ) . ($strong ? "</strong>" : "") . '</option>';
										print $txt_canonical;
									}
								}

								if (!prd_products_is_used_as_canonical($prd['id'])) {
									?>
									<option <?php print(isset($prd['prd_canonical_id']) && $prd['prd_canonical_id'] ? 'selected="selected"' : '') ?> value="other"><?php print _('Autres...'); ?></option>
								<?php } ?>
							</select>
						</td>
					</tr>
					<tr id="other_urls" <?php print(isset($prd['prd_canonical_id']) && $prd['prd_canonical_id'] ? '' : 'style="display: none;"') ?>>
						<td>
							<label for="alt-prd-name"><?php print _('Ajouter un produit :'); ?></label>
						</td>
						<td>
							<?php
							$prd_name = prd_products_get_name($prd['prd_canonical_id']);
							?>
							<input type="hidden" name="alt-prd-id" id="alt-prd-id" value="<?php print($prd['prd_canonical_id'] ? $prd['prd_canonical_id'] : '') ?>" />
							<input type="hidden" name="none" id="none" value="" />
							<input type="hidden" name="cat_selected" id="cat_selected" value="" />
							<input name="alt-prd-name" id="alt-prd-name" value="<?php print  $prd_name ?>" style="width: 228px;" readonly="readonly" type="text">
							<input value="<?php print _('Parcourir') ?>" class="button" onclick="canonical_choose(this.form)" type="button">
						</td>
					</tr>
					<tr>
						<th colspan="2"><?php print _('Indexation') ?></th>
					</tr>
					<tr>
						<td>
							<label for="index-true"><?php print _('Indexer :'); ?></label>
						</td>
						<td>
							<?php
							$is_index = prd_products_is_index($prd['id'], false);
							$is_index_arbo = prd_products_is_index($prd['id']);
							?>
							<input type="radio" name="index" id="index-true" value="1" <?php print $is_index  ? ' checked="checked"' : ''; ?> /> <label for="index-true"><?php print _('Oui'); ?></label>
							<input type="radio" name="index" id="index-false" value="0" <?php print !$is_index ? ' checked="checked"' : ''; ?> /> <label for="index-false"><?php print _('Non'); ?></label>
							<?php if ($is_index && !$is_index_arbo) { ?>
								<div class="notice" style="margin-top: 5px"><?php printf(_('Le produit "%s" sera indexé, si cette fonctionnalité est activée et s\'il est présent dans un classement indexé et publié.'), htmlspecialchars($prd['name'])) ?></div>
							<?php } ?>
							<sub><?php print _('En indexant ce produit, celui-ci sera visible dans le moteur de recherche et le sitemap.') ?></sub>
						</td>
					</tr>
				<?php } ?>
				<tr>
					<th colspan="2"><?php print _('Description') ?></th>
				</tr>
				<tr>
					<td><label for="desc"><?php print _('Description courte :'); ?></label></td>
					<td><textarea name="desc" id="desc" rows="5" cols="40" class="short"><?php print htmlspecialchars($prd['desc']); ?></textarea></td>
				</tr>
				<tr>
					<td><label for="desc-long"><?php print _('Description longue :'); ?></label></td>
					<td><textarea class="tinymce" name="desc-long" id="desc-long" rows="10" cols="50"><?php require_once('views.inc.php');
						print view_site_format_riawysiwyg($prd['desc-long'], false, true, false, false); ?></textarea>
					</td>
				</tr>
				<?php if ($_GET['prd'] > 0) { ?>
					<tr>
						<th colspan="2"><?php print _('Dates importantes') ?></th>
					</tr>
					<tr>
						<td><label for="date-created"><?php print _('Créé le :'); ?></label></td>
						<td><span id="date-created"><?php print ria_date_format($prd['date_created']); ?></span></td>
					</tr>
					<tr>
						<td><label for="date-modified"><?php print _('Dernière modification le :'); ?></label></td>
						<td><span id="date-modified"><?php print ria_date_format($prd['date_modified']); ?></span></td>
					</tr>
					<?php if (trim($prd['date_first_published'])) { ?>
						<tr>
							<td><label for="date-first-published"><?php print _('Première publication :'); ?></label></td>
							<td><span id="date-first-published"><?php print ria_date_format($prd['date_first_published']); ?></span></td>
						</tr>
					<?php } ?>
					<?php if (trim($prd['date_published']) && $prd['date_first_published'] != $prd['date_published']) { ?>
						<tr>
							<td><label for="date-published"><?php print _('Dernière publication :'); ?></label></td>
							<td><span id="date-published"><?php print ria_date_format($prd['date_published']); ?></span></td>
						</tr>
					<?php } ?>
				<?php } ?>
			</tbody>
			<tfoot>
				<tr>
					<td class="align-left">
						<?php if ($prd['id'] > 0) { ?>
							<input type="submit" name="duplicate" value="<?php print _('Dupliquer') ?>" title="<?php print _('Créer un nouveau produit en dupliquant celui-ci') ?>" />
							<input type="submit" name="import" value="<?php print _('Importer') ?>" title="<?php print _('Importer des données depuis un autre produit') ?>" onclick="return displayPopup('<?php print _('Importer des données') ?>', '', '/admin/catalog/popup-duplicate.php?cat=<?php print $_GET['cat']; ?>&amp;prd=<?php print $_GET['prd']; ?>','',600);" />
						<?php } ?>
					</td>
					<td class="align-right">
						<input type="submit" onclick="return prdValidForm(this.form)" class="btn-main" name="save_stay" id="save_stay" value="<?php print _('Enregistrer'); ?>" />
						<input type="submit" onclick="return prdValidForm(this.form)" class="btn-main" name="save" id="save" value="<?php print _('Enregistrer et revenir à la liste'); ?>" />
						<input class="btn-cancel" type="submit" name="cancel" value="<?php print _('Annuler') ?>" />
						<?php if ($prd['id'] > 0 && !$prd['is_sync']) { ?>
							<input class="button-del" type="submit" name="delete" value="<?php print _('Supprimer') ?>" onclick="return confirmDel();" />
						<?php } ?>
					</td>
				</tr>
				<tr>
					<td colspan="2" class="align-left"><?php
						// Si la prévisualisation des articles est activée alors le formulaire de la fonctionnalité est activée
						// Si le tenant dispose de plusieurs site web alors on affiche un sélecteur
						// Sinon le wst_id est stocké dans un champ hidden

						$wst_preview_actived = [];
						$r_website = wst_websites_get( 0, false, null, false, [_WST_TYPE_SHOP, _WST_TYPE_EXTRANET] );
						if( $r_website ){
							while( $website = ria_mysql_fetch_assoc($r_website) ){
								$preview_actived = cfg_overrides_get_value( 'preview_products', $website['id'] );
								if( cfg_variable_values_parse_type(FLD_TYPE_BOOLEAN_YES_NO, $preview_actived) ){
									$wst_preview_actived[] = $website;
								}
							}
						}

						if( count($wst_preview_actived) ){
							if( count($wst_preview_actived) == 1 ){
								$website = $wst_preview_actived[0];
								print '<input type="hidden" name="prd-preview-wst" value="'.$website['id'].'" />';
							}else{
								print '<select name="prd-preview-wst">';

									foreach( $wst_preview_actived as $website ){
										print '<option value="'.$website['id'].'">'.htmlspecialchars( $website['name'] ).'</option>';
									}

								print '</select>';
							}

							print ' <input type="button" name="prd-preview" data-cat-id="'.$_GET['cat'].'" data-prd-id="'.$prd['id'].'" class="previsualisation" value="'._('Prévisualiser').'" />';
						}
					?></td>
				</tr>
			</tfoot>
		</table>
		<script>
			<!--
			$(document).ready(function() {
				$("#canonical_url").on('change', function(e) {
					if ($("#canonical_url option:selected").val() == "other") {
						$("input#cat_selected").val("");
						$("input#none").val("");
						$("#other_urls").show();
					} else if ($("#canonical_url option:selected").val() == "none") {
						$("input#none").val("true");
						$("input#cat_select").val("");
						$("#other_urls").hide();
					} else {
						$("input#cat_selected").val($("#canonical_url option:selected").val());
						$("input#none").val("");
						$("#other_urls").hide();
					}
				});
			});
			//-->
		</script>
	<?php } elseif ($tab == 'prices') { ?>
		<h3>
			<?php print _('Tarifs par catégorie tarifaire'); ?>
			<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_PRICE') ){ ?>
			<a class="edit-cat" href="/admin/config/prices/index.php" title="<?php print _('Modifier la liste des catégories tarifaires'); ?>"><?php print _('Modifier'); ?></a>
			<?php } ?>
		</h3>

		<input type="hidden" name="all-price-prd" id="all-price-prd" value="<?php print $_GET['prd']; ?>" />

		<table id="all-prices" class="checklist">
			<thead class="thead-none">
				<tr>
					<th id="prc_resume_name"><?php print _('Catégorie'); ?></th>
					<th id="prc_resume_ht" class="align-right"><?php print _('Prix HT'); ?></th>
					<th id="prc_resume_ttc" class="align-right"><?php print _('Prix TTC'); ?></th>
				</tr>
			</thead>
			<tbody>
				<?php
				// Le contenu de ce tableau est chargée en Ajax
				?>
			</tbody>
		</table>

		<?php
			// Le prix d'achat n'est accessible que si l'utilisateur a accès aux marge
			if( gu_user_is_authorized('_RGH_ADMIN_MARGE_SHOW') ){
				if (($prd['is_sync'] && isset($prd['purchase_avg']) && is_numeric($prd['purchase_avg'])) || !$prd['is_sync']) {
					?><div class="info" id="pamp">
						<label for="purchase-avg"><?php print view_purchase_avg_prd_is_sync($prd); ?>&nbsp;<?php print _('Prix d\'achat HT :') ?></label>
						<?php
						$purchase_avg = !isset($prd['purchase_avg']) || !is_numeric($prd['purchase_avg']) ? 'NC' : number_format($prd['purchase_avg'], 2, '.', ' ');
						print '<input ' . ($prd['is_sync'] ? 'disabled="disabled"' : '') . ' type="text" name="purchase-avg" id="purchase-avg" value="' . $purchase_avg . '" />';
						print $prd['is_sync'] ? '' : '&nbsp;<input type="button" name="save-purchase-avg" id="save-purchase-avg" value="' . _('Enregistrer') . '" onclick="savePurchaseAvg()" />';
						?>
					</div><?php
				}
			}
		?>

		<a name="lst-tvas"></a>
		<h3><?php print _('Taxes') ?></h3>
		<div id="taxes">
			<div class="info" id="eco-taxe">
				<label for="prd-ecotaxe"><?php print view_eco_prd_is_sync($prd); ?>
					&nbsp;<?php print _('Ecotaxe') ?>&nbsp;:&nbsp;</label>
				<?php
				$ecotaxe = isset($prd['ecotaxe']) && is_numeric($prd['ecotaxe']) ? $prd['ecotaxe'] : 0;
				print '<input ' . ($prd['is_sync'] ? 'disabled="disabled"' : '') . ' type="text" name="prd-ecotaxe" id="prd-ecotaxe" value="' . number_format($ecotaxe, 3, '.', ' ') . '" />';
				print $prd['is_sync'] ? '' : '&nbsp;<input type="button" name="save-eco" id="save-eco" value="' . _('Enregistrer') . '" onclick="saveEco()" />';
				?>
			</div>
			<div class="info" id="tva-taxe">
				<?php
				// TVA avec condition pour le produit
                // On privilégie la tva qui n'a pas de catégorie comptable
				$rtva = prc_tvas_get(0, false, $_GET['prd'], false, null, array(), false, false, null);
                if( !ria_mysql_num_rows($rtva) ){
                    $rtva = prc_tvas_get(0, false, $_GET['prd']);
                }
				$tva = ria_mysql_fetch_array($rtva);

				// TVA enregistrée dans la base
				$rtva = prd_tvas_get();
				print '
					<label for="prd-tva">' . view_tva_prd_is_sync($tva) . '&nbsp;' . _('TVA') . '&nbsp;:&nbsp;</label>
						<select ' . ($tva['is-sync'] ? 'disabled="disabled"' : '') . ' name="prd-tva" id="prd-tva">
							<option value="1">' . _('Non définie') . '</option>';
					while( $t = ria_mysql_fetch_array($rtva) ){
						print '	<option value="' . $t['rate'] . '"
							' . (number_format($t['name'], 2, '.', '') == number_format(($tva['rate'] - 1) * 100, 2, '.', '') ? 'selected="selected"' : '') . '>'
							. number_format($t['name'], 1, ',', '') . '%</option>';
					}
				print '	</select>';
				print $tva['is-sync'] ? '' : ' <input type="button" name="save-tva" id="save-tva" onclick="javasript:saveTva(' . $tva['id'] . ');" value="' . _('Enregistrer') . '" />';
				?>
				<div id="info-not-tva" class="none">
					<span class="info-fld"><?php print _('La TVA applicable sera celle de sa catégorie.'); ?></span>
				</div>
				<div>
					<span class="info-fld"><?php print _('Le changement de TVA s’appliquera sur le prix HT ou TTC selon les ') ?> <a href="/admin/config/prices/index.php" target="_blank"><?php print _('paramètres de tarification') ?></a> <?php print _('de chaque catégorie tarifaire.') ?></span>
				</div>

				<?php
					print '<br /><table id="table-tva-country">'
						.'<caption>'._('TVA par pays').'</caption>'
						.'<thead>'
							.'<tr>'
								.'<th id="tva_country">'._('Pays').'</th>'
								.'<th id="tva_country_value">'._('TVA').'</th>'
							.'</tr>'
						.'</thead>'
						.'<tfoot>'
							.'<tr>'
								.'<td colspan="2" class="align-right">'
									.'<input type="button" name="tva_country_add" id="tva_country_add" value="'._('Ajouter une condition').'">'
									.' <input class="btn-main" type="submit" name="tva_country_save" id="tva_country_save" value="'._('Enregistrer').'">'
								.'</td>'
							.'</tr>'
						.'</tfoot>'
						.'<tbody>';

						// Charge une fois la liste des pays dans un tableau
						$ar_country = sys_countries_get_array();

						// Chargement du taux de tva défini par pays
						$tva_country_uniqid = 0;
						$r_tva_country = prc_tvas_get( 0, false, $prd['id'], false, null, [], false, false, false, true );
						if( !$r_tva_country || !ria_mysql_num_rows($r_tva_country) ){
							print '<tr><td colspan="2">'._('Aucune condition de définie').'</td></tr>';
						}else{

							while( $tva_country = ria_mysql_fetch_assoc($r_tva_country) ){
								$tva_percent = ( $tva_country['rate'] - 1 ) * 100;
								print '<tr>'
									.'<td headers="tva_country">'
										.'<select name="tva_country['.$tva_country_uniqid.']">';
											foreach( $ar_country as $code => $name ){
												$selected = '';
												if( $code == $tva_country['cnt_code'] ){
													$selected = 'selected="selected"';
												}

												print '<option '.$selected.' value="'.htmlspecialchars( $code ).'">'
													.htmlspecialchars( $name )
												.'</option>';
											}
										print '</select>'
									.'</td>'
									.'<td headers="tva_country_value">'
										.'<input type="number" min="0" step="0.01" name="tva_country_value['.$tva_country_uniqid.']" value="'.$tva_percent.'" /> %'
									.'</td>'
								.'</tr>';
								$tva_country_uniqid += 1;
							}
						}

						print '<tr id="new_tva_country" class="hide">'
						.'<td headers="tva_country">'
							.'<select name="tva_country[tva_country_uniqid]">';
								print '<option value="">'._('Sélectionnez un pays').'</option>';

								foreach( $ar_country as $code => $name ){
									print '<option value="'.htmlspecialchars( $code ).'">'
										.htmlspecialchars( $name )
									.'</option>';
								}
							print '</select>'
						.'</td>'
						.'<td headers="tva_country_value">'
							.'<input type="number" min="0" step="0.01" name="tva_country_value[tva_country_uniqid]" value="" /> %'
						.'</td>'
					.'</tr>';

						print '</tbody>'
					.'</table>'
					.'<div>'
						.'<span class="info-fld">'
							._('Si aucune condition de TVA par pays n\'est définie alors la TVA par défaut sera utilisée.')
						.'</span>'
					.'</div>';
				?>
			</div>
		</div>
		<h3 class="conditions_tarifaires"><?php print _('Tarifs conditionnels pour le produit :'); ?> <?php print htmlspecialchars($prd['ref']); ?></h3>
		<div class="prices-menu">
			<?php
			$periode = isset($_GET['periode']) && ($_GET['periode'] >= 0 || $_GET['periode'] < 7) ? $_GET['periode'] : false;
			if ($periode === false) {
				$periode = (session_get_periodpicker_state()) !== false ? session_get_periodpicker_state() : 6;
			} else {
				session_set_periodpicker_state($periode);
			}
			view_state_periodpicker($periode, "riapriceperiod", 7, _('Période pour les tarifs'));
			?>
		</div>

		<input type="hidden" name="id-price" id="id-price" value="" />
		<input type="hidden" name="id-cdt-del" id="id-cdt-del" value="" />
		<input type="hidden" name="id-price-del" id="id-price-del" value="" />

		<table class="prc-tva-eco checklist" id="lst-prices">
			<?php product_price_table_get($prd['ref'], $prd['id'], $periode); ?>
		</table>

		<span class="info-fld"><?php print _('* : ce champ est obligatoire. Pour un nouveau tarif, la valeur doit être supérieure à 0, pour une remise en pourcentage, la valeur doit être inférieure ou égale à 100.') ?></span><br />
		<span class="info-fld"><?php print _('** : en décochant cette option, la remise ne sera pas prise en compte s\'il existe des tarifs de priorité supérieure.') ?> <a href="/admin/config/prices/index.php" target="_blank"><?php print _('Plus d\'informations sur les priorités') ?></a></span><br />
		<span class="info-fld"><?php print _('*** : si la quantité maximum n\'est pas précisée alors aucun maximum ne sera fixé pour ce tarif.') ?></span>

		<script>
			var tva_country_uniqid = <?php print $tva_country_uniqid + 1; ?>;
			// Gestion de la TVA par pays
			$(document).on(
				'click', '#tva_country_add', function(){
					var newTVACountry = $('#new_tva_country').clone();
					newTVACountry.removeAttr('id').removeClass('hide');

					var attrNameSelect = newTVACountry.find('select').attr('name').replace( 'tva_country_uniqid', tva_country_uniqid );
					var attrNameInput = newTVACountry.find('[type="number"]').attr('name').replace( 'tva_country_uniqid', tva_country_uniqid );

					newTVACountry.find('select').attr( 'name', attrNameSelect );
					newTVACountry.find('[type="number"]').attr( 'name', attrNameInput );

					$('#table-tva-country tbody').append( newTVACountry );
					tva_country_uniqid++;
				}
			);
		</script>

	<?php } elseif ($tab == 'linked') {

	$url = "/admin/catalog/product.php";
	$temp = "";
	foreach ($_GET as $key => $value) {
		if ($temp == "") {
			$symbol = "?";
		} else {
			$symbol = "&";
		}
		if ($key == "published") {
			//  DO nothing, on le fera à la suite
		} else {
			$temp .= $symbol . $key . "=" . $value;
		}
	}

	if (isset($_SESSION['usr_admin_view_publish_linked'])) {
		$display_published = $_SESSION['usr_admin_view_publish_linked'];
	} else {
		$display_published = $config['admin_view_publish_linked'];
	}

	//  On met à jour l'override de la variable ici lorsqu'il y a action.
	if (isset($_GET['published'])) {
		if ($_GET['published'] == "false") {
			$display_published = false;
			cfg_variable_users_add("admin_view_publish_linked", $_SESSION['usr_id'], 0);
			if (isset($_SESSION['usr_admin_view_publish_linked'])) {
				$_SESSION['usr_admin_view_publish_linked'] = false;
			}
		} else {
			$display_published = true;
			cfg_variable_users_add("admin_view_publish_linked", $_SESSION['usr_id'], 1);
			$_SESSION['usr_admin_view_publish_linked'] = true;
			if (isset($_SESSION['usr_admin_view_publish_linked'])) {
				$_SESSION['usr_admin_view_publish_linked'] = true;
			}
		}
	}

	//  On inverse l'état de published reçu pour la page suivante
	if (!$display_published) {
		$temp .= "&published=true";
	} else {
		$temp .= "&published=false";
	}

	$checked = "";
	if ($display_published) {
		$checked = 'checked="checked"';
	}
	print('<div class="stats-menu">');
	print '<div class="div_check_publish"><input ' . $checked . ' data-link-href="' . $url . $temp . '" type="checkbox" class="published_checkbox" id="published_checkbox" /><label for="published_checkbox" class="label_check_publish">' . _('Afficher les produits publiés seulement') . '</label></div>';
	if (gu_user_is_authorized('_RGH_ADMIN_CONFIG_CATALOG_TYPE_REL_AJOUT')) {
		print('<input type="button" class="add_type" name="add_type" value="' . _('Ajouter un type de relation') . '">');
	}
	print('</div>');

	print '
				<input type="hidden" name="rel-source-id" id="rel-source-id" value="' . $_GET['prd'] . '" />
			';
	print '
				<input type="hidden" name="rel-cat-id" id="rel-cat-id" value="' . $_GET['cat'] . '" />
			';

	include("./products/linked.php");

	if ($config['calc_ord_related_activated']) {
		require_once('prd/related.inc.php');

		// Tableau "Produits fréquemment achetés avec ce produit"
		$ord_related = ord_related_get($_GET['prd'], ORD_RELATED_LIMIT, false);
		print '
				<table class="checklist list-prd-ord-related " style="max-width:1247px; width: 100%;">
					<caption>' . _('Produits fréquemment achetés avec ce produit') . '</caption>
					<thead>
						<tr>
							<th id="ord-related-is-sync" class="thead-none"></th>
							<th id="ord-related-ref" class="thead-none">' . _('Référence') . '</th>
							<th id="ord-related-name" class="thead-none">' . _('Désignation') . '</th>
							<th id="ord-related-publish" class="thead-none">' . _('Publié gescom ?') . '</th>
							<th id="ord-related-publish-site" class="thead-none">' . _('Publié site ?') . '</th>
							<th id="ord-related-linked-sells" class="thead-none">' . _('Ventes liées') . '</th>
							<th id="ord-related-linked-sells-percent" class="thead-none">' . _('Vente liées') . '<br/>(' . _('% des ventes') . ')</th>
						</tr>
					</thead>
					<tbody>';
		if ($ord_related && ria_mysql_num_rows($ord_related)) {
			// On récupère le nombre de vente du produits sur la période de calcul des produits fréquemment achetés avec
			// Voir ord_related_refresh_product()
			//
			$date = new DateTime('NOW');
			$date->sub(new DateInterval('P' . floor($config['delay_ord_related'] * (365 / 12)) . 'D'));

			$sells_prd = stats_products_selled($date->format('Y-m-d'), date('Y-m-d'), array(), array(), array($_GET['prd']));
			$sells_prd = stats_view_products_ordered($_GET['prd'], $date->format('Y-m-d'), date('Y-m-d'), 'month');


			$nb_sells_prd = 0;
			if ($sells_prd && ria_mysql_num_rows($sells_prd)) {
				while ($sell_prd = ria_mysql_fetch_assoc($sells_prd)) {
					$nb_sells_prd += $sell_prd['hits'];
				}
			}
			while ($one_rel = ria_mysql_fetch_assoc($ord_related)) {
				print '
							<tr id="line-' . $one_rel['id'] . '">
								<td headers="ord-related-is-sync" class="prd-is-sync align-center">
									' . (($type['key_type'] == 'prd-parents' || $type['key_type'] == 'prd-childs') ? view_hry_is_sync($one_rel) : view_prd_is_sync($one_rel)) . '
								</td>
								<td headers="ord-related-ref-' . $type['id'] . '" data-label="'._('Référence :').' "><a href="/admin/catalog/product.php?cat=0&prd=' . $one_rel['id'] . '">' . htmlspecialchars($one_rel['ref']) . '</a></td>
								<td headers="ord-related-name-' . $type['id'] . '" data-label="'._('Désignation :').' "><a href="/admin/catalog/product.php?cat=0&prd=' . $one_rel['id'] . '">' . htmlspecialchars($one_rel['name']) . '</a></td>
								<td headers="ord-related-publish" data-label="'._('Publié gescom :').' ">' . ($one_rel['publish'] ? _('Oui') : _('Non')) . '</td>
								<td headers="ord-related-publish-site" data-label="'._('Publié site :').' ">' . ($one_rel['publish'] && $one_rel['publish_cat'] ? _('Oui') : _('Non')) . '</td>
								<td headers="ord-related-linked-sells" class="right" data-label="'._('Ventes liées :').' ">' . $one_rel['rel_weight'] . '</td>
								<td headers="ord-related-linked-sells-percent" class="right" data-label="'._('% des ventes :').' ">' . ($nb_sells_prd > 0 ? round($one_rel['rel_weight'] * 100 / $nb_sells_prd) . '%' : 'N/A') . '</td>
							</tr>
					';
			}
		} else {
			print '
							<tr>
								<td colspan="7">' . _('Aucun article lié') . '</td>
							</tr>
				';
		}

		print '
					</tbody>
				</table>
			';

		// Tableau "Les clients ayant acheté ce produit ont également commandé"
		$ord_related = prd_user_related_get($_GET['prd'], ORD_RELATED_LIMIT, false);
		print '
				<table class="checklist list-prd-ord-related2 " style="max-width:1247px; width: 100%;">
					<caption>' . _('Les clients ayant acheté ce produit ont également commandé') . '</caption>
					<thead>
						<tr>
							<th id="ord-related2-is-sync" class="thead-none"></th>
							<th id="ord-related2-ref" class="thead-none">' . _('Référence') . '</th>
							<th id="ord-related2-name" class="thead-none">' . _('Désignation') . '</th>
							<th id="ord-related2-publish" class="thead-none">' . _('Publié gescom ?') . '</th>
							<th id="ord-related2-publish-site" class="thead-none">' . _('Publié site ?') . '</th>
							<th id="ord-related2-linked-sells" class="thead-none">' . _('Ventes liées') . '</th>
							<th id="ord-related2-linked-sells-percent" class="thead-none">' . _('Vente liées') . '<br/>(' . _('% des ventes') . ')</th>
						</tr>
					</thead>
					<tbody>';
		if ($ord_related && ria_mysql_num_rows($ord_related)) {
			// On récupère le nombre de vente du produits sur la période de calcul des produits fréquemment achetés avec
			// Voir ord_related_refresh_product()
			//
			$date = new DateTime('NOW');
			$date->sub(new DateInterval('P' . floor($config['delay_ord_related'] * (365 / 12)) . 'D'));

			$sells_prd = stats_products_selled($date->format('Y-m-d'), date('Y-m-d'), array(), array(), array($_GET['prd']));
			$sells_prd = stats_view_products_ordered($_GET['prd'], $date->format('Y-m-d'), date('Y-m-d'), 'month');


			$nb_sells_prd = 0;
			if ($sells_prd && ria_mysql_num_rows($sells_prd)) {
				while ($sell_prd = ria_mysql_fetch_assoc($sells_prd)) {
					$nb_sells_prd += $sell_prd['hits'];
				}
			}
			while ($one_rel = ria_mysql_fetch_assoc($ord_related)) {
				print '
							<tr id="line-' . $one_rel['id'] . '">
									<td headers="ord-related2-is-sync" class="prd-is-sync align-center">
									' . (($type['key_type'] == 'prd-parents' || $type['key_type'] == 'prd-childs') ? view_hry_is_sync($one_rel) : view_prd_is_sync($one_rel)) . '
								</td>
								<td headers="ord-related2-ref-' . $type['id'] . '" data-label="'._('Référence :').' "><a href="/admin/catalog/product.php?cat=0&prd=' . $one_rel['id'] . '">' . htmlspecialchars($one_rel['ref']) . '</a></td>
								<td headers="ord-related2-name-' . $type['id'] . '" data-label="'._('Désignation :').' "><a href="/admin/catalog/product.php?cat=0&prd=' . $one_rel['id'] . '">' . htmlspecialchars($one_rel['name']) . '</a></td>
								<td headers="ord-related2-publish" data-label="'._('Publié gescom :').' ">' . ($one_rel['publish'] ? _('Oui') : _('Non')) . '</td>
								<td headers="ord-related2-publish-site" data-label="'._('Publié site :').' ">' . ($one_rel['publish'] && $one_rel['publish_cat'] ? _('Oui') : _('Non')) . '</td>
								<td headers="ord-related2-linked-sells" class="right" data-label="'._('Ventes liées :').' ">' . $one_rel['rel_weight'] . '</td>
								<td headers="ord-related2-linked-sells-percent" class="right" data-label="'._('% des ventes :').' ">'. ($nb_sells_prd > 0 ? round($one_rel['rel_weight'] * 100 / $nb_sells_prd) . '%' : 'N/A') . '</td>
							</tr>
					';
			}
		} else {
			print '
							<tr>
								<td colspan="7">' . _('Aucun article lié') . '</td>
							</tr>
				';
		}

		print '
					</tbody>
				</table>
			';
	}
	?>
	<?php
} elseif ($tab == 'fields') {
	print view_admin_tab_fields(CLS_PRODUCT, $_GET['prd'], $lng, 'product.php?cat=' . $_GET['cat'] . '&amp;prd=' . $_GET['prd'] . '&amp;tab=fields');
} elseif (!prd_products_is_port_id($_GET['prd']) && $tab == 'nomenclature') {

	$mytype = prd_products_get_nomenclature_type($_GET['prd']);
	?>

		<form action="/admin/catalog/product.php?cat=<?php print htmlspecialchars($_GET['cat']); ?>&amp;prd=<?php print htmlspecialchars($_GET['prd']); ?>&amp;tab=nomenclature" method="post" onsubmit="return prd_nomenclature_change()">
			<table id="table-nomenclature">
				<caption><?php print _('Type de nomenclature') ?></caption>
				<tbody>
					<tr>
						<td class="col150px valign-center"><?php
							print $prd['is_sync'] ? view_prd_is_sync($prd) . ' ' : '';
							print _('Type :');
							?></td>
						<td>
							<select name="prd-type" id="prd-type" <?php print $prd['is_sync'] ? 'disabled="disabled"' : ''; ?>>
								<?php
								$nomenclatures = prd_nomenclatures_types_get();
								if( $nomenclatures ){
									while( $nomenclature = ria_mysql_fetch_array($nomenclatures) ){
										print '<option ' . ($mytype == $nomenclature['id'] ? 'selected="selected"' : '') . ' value="' . $nomenclature['id'] . '">' . htmlspecialchars($nomenclature['name']) . '</option>';
									}
								}
								?>
							</select>
						</td>
					</tr>
				</tbody>
				<?php if (!$prd['is_sync']) { ?>
					<tfoot>
						<tr>
							<td colspan="2"><input type="submit" name="save-nomenclature" value="<?php print _('Enregistrer'); ?>" /></td>
						</tr>
					</tfoot>
				<?php } ?>
			</table>
		</form>

		<?php if ($mytype == NM_TYP_VARIABLE) {
			?>

			<form action="/admin/catalog/product.php?prd=<?php print htmlspecialchars($_GET['prd']); ?>&amp;tab=nomenclature" method="post" onsubmit="return prd_nomenclature_opt()">

				<table id="tableau-nomenclature">
					<caption><?php print _('Liste des options attachées à la nomenclature') ?></caption>
					<tbody>
						<tr>
							<th colspan="2"><?php print _('Ajouter une option au produit') ?></th>
						</tr>
						<tr>
							<td id="td-tableau-nomenclature-1"><?php print _('Choisissez une option') ?></td>
							<td id="td-tableau-nomenclature-2">
								<select name="nomenclature_opt" id="nomenclature_opt">
									<?php
									$first = -1;
									$opts = prd_options_get();
									$c = 0;
									while ($p = ria_mysql_fetch_array($opts)) {
										if ($c <= 0) $first = $p['id'];

										print '<option value="' . $p['id'] . '">' . htmlspecialchars($p['name']) . '</option>';

										$c++;
									}
									?>
								</select>
							</td>
						</tr>

						<tr>
							<td><label for="nomenclature_qte"><?php print _('Quantité (pour une nomenclature)') ?></label></td>
							<td><input type="text" class="size" id="nomenclature_qte" name="nomenclature_qte" value="" size="2" /></td>
						</tr>
						<tr>
							<th colspan="2" class="align-right">
								<input type="hidden" name="nomenclature_prd" id="nomenclature_prd" value="<?php print htmlspecialchars($_GET['prd']); ?>" />
								<input type="submit" class="button" name="add" value="<?php print _('Ajouter') ?>" onclick="prd_nomenclature_add()" />
							</th>
						</tr>
						<tr>
							<td colspan="2">
								<select name="prd_nomenclature[]" id="prd_nomenclature" multiple="multiple" size="12" class="large">
									<?php
									$options = prd_nomenclatures_options_get($_GET['prd']);
									if ($options && ria_mysql_num_rows($options) > 0) {
										while ($o = ria_mysql_fetch_array($options)) {
											print '<option value="' . $o['opt'] . '">' . htmlspecialchars($o['opt-name']) . ' - ( ' . $o['qte'] . ' )</option>';
										}
									}
									?>
								</select>
							</td>
						</tr>
						<tr>
							<th colspan="2" class="align-left">
								<input type="submit" class="button" name="del" value="<?php print _('Supprimer') ?>" onclick="prd_nomenclature_del()" />
							</th>
						</tr>
					</tbody>
				</table>
			</form>

		<?php } elseif ( in_array($mytype, array(NM_TYP_NONE, NM_TYP_COMPONENT, NM_TYP_LINKED)) ) { ?>

			<form class="nomenclatures" action="/admin/catalog/product.php?cat=<?php print htmlspecialchars($_GET['cat']); ?>&amp;prd=<?php print htmlspecialchars($_GET['prd']); ?>&amp;tab=nomenclature" method="post">

				<table id="table-nomenclature-composition">
					<caption><?php print _('Composition de la nomenclature') ?></caption>
					<tbody>

						<tr>
							<th colspan="5"><?php print _('Composants actuels') ?></th>
						</tr>

						<?php
						$nomenclatures = prd_nomenclatures_products_get($_GET['prd']);
						if ($nomenclatures && ria_mysql_num_rows($nomenclatures) > 0) {
							while ($n = ria_mysql_fetch_array($nomenclatures)) {

								$n['price_ht'] = $n['price_ht'] ? $n['price_ht'] : 0;

								$products = prd_products_get(
									$n['id'],
									$ref = '',
									$brand = 0,
									$published = false,
									$cat = 0,
									$rowstart = 0,
									$maxrows = -1,
									$catchilds = false,
									$centralized = false,
									$new = false,
									$destockage = false,
									$promotion = false,
									$childs = false,
									$sort = false,
									$supplier = false,
									$uncompleted = false,
									$have_image_only = false,
									$hide_sleeping = false,
									$fld = false,
									$mdl = false,
									$countermark = null,
									$have_stock = null,
									$dps = false,
									$orderable = null,
									$isSync = false,
									$exclude = false,
									$bestsellers = false,
									$ordered = null,
									$or_between_val = false,
									$no_weight = false,
									$no_related = false,
									$exclude_cat = false,
									$with_price = false
								);
								if (!$products || ria_mysql_num_rows($products) <= 0) continue;

								$product = ria_mysql_fetch_array($products);

								print ' <tr class="nomenclature-tr">
											<td id="td-table-nomenclature-compo-1">' . htmlspecialchars($product['ref']) . '</td>
											<td id="td-table-nomenclature-compo-2">' . htmlspecialchars($product['name']) . '</td>
											<td id="td-table-nomenclature-compo-3">x ' . number_format($n['qte'], 0, ',', ' ') . '</td>
											<td id="td-table-nomenclature-compo-4">
												<input type="hidden" name="price_ht" value="' . $n['price_ht'] . '" />
												' . number_format($n['price_ht'], 2, ',', ' ') . ' &euro;
											</td>
											<td id="td-table-nomenclature-compo-5" class="align-center">';
								if( !$n['is_sync'] ){
									print '
										<input type="button" name="edit-nomenclature" class="edit-nomenclature" style="margin: 0px 3px 3px;" value="' . _('Modifier') . '" />
										<a href="/admin/catalog/product.php?cat=' . $_GET['cat'] . '&amp;prd=' . $_GET['prd'] . '&amp;tab=nomenclature&amp;del-nomenclature=' . $product['id'] . '">
											<img name="del" title="' . _('Supprimer ce produit') . '" alt="' . _('Supprimer') . '" src="/admin/images/del.svg" class="del-cdt" />
										</a>
									';
								}
								print '
												</td>
											</tr>';
							}
						} else {
							print '<tr class="nomenclature-tr"><td colspan="5">' . _('Aucun composant') . '</td></tr>';
						}
						?>

						<tr>
							<th colspan="5" class="frm-edit-nomenclature"><?php print _('Ajouter un produit') ?></th>
						</tr>
						<tr>
							<td class="col150px"><label for="nomenclature_ref"><?php print _('Référence :'); ?></label></td>
							<td colspan="4"><input type="text" id="nomenclature_ref" name="nomenclature_ref" value="" /></td>
						</tr>
						<tr>
							<td><label for="nomenclature_qte"><?php print _('Quantité :'); ?></label></td>
							<td colspan="4"><input type="text" class="size" id="nomenclature_qte" name="nomenclature_qte" value="" size="2" /></td>
						</tr>
						<tr>
							<td><label for="nomenclature_price"><?php print _('Prix HT unitaire :'); ?></label></td>
							<td colspan="4">
								<input type="text" class="size" id="nomenclature_price" name="nomenclature_price" value="" size="2" /> €
							</td>
						</tr>

						<tr>
							<th colspan="5" class="frm-action-nomenclature align-right">
								<input type="hidden" name="nomenclature_prd" id="nomenclature_prd" value="<?php print $_GET['prd']; ?>" />
								<input type="submit" class="button" name="add-nomenclature" id="add-nomenclature" value="<?php print _('Ajouter') ?>" />
							</th>
						</tr>
					</tbody>
				</table>
			</form>
			<script>
				<!--
				$(document).ready(function() {
					$('#add-nomenclature').on('click', function(e) {
						var ref = $('#nomenclature_ref').val() || $('#nomenclature_ref').text();
						var qte = $('#nomenclature_qte').val();
						var price = $('#nomenclature_price').val();
						if (qte == '' || price == '' || ref == '') {
							if (ref == '') {
								msg = '<?php print _('La référence saisie est invalide.') ?>';
							} else if (qte == '') {
								msg = '<?php print _('La quantité saisie est invalide.') ?>';
							} else if (price == '') {
								msg = '<?php print _('Le prix saisie est invalide.') ?>'
							}
							var error = $('<div>').addClass('error').text(msg)
							$('.messages').html(error);
							return false;
						}

						return true;
					})
				}).delegate(
					'.edit-nomenclature', 'click',
					function() {
						var tr = $(this).parents('tr');
						var ref = tr.find('td').eq(0).html();
						var qte = tr.find('td').eq(2).html().replace('x ', '');
						var price = tr.find('td').eq(3).find('input[type=hidden]').val();

						$('#cancel-edit-nomenclature').remove();
						$('.frm-edit-nomenclature').html('<?php print _('Modification du produit') ?>')
						$('.frm-action-nomenclature').append('<input type="button" name="cancel-edit-nomenclature" id="cancel-edit-nomenclature" value="<?php print _('Annuler') ?>" />')
						$('#add-nomenclature').val('<?php print _('Enregistrer') ?>');

						$('#nomenclature_ref').after('<input type="hidden" name="nomenclature_ref" id="hidden-nomenclature_ref" value="' + ref + '" /><span id="nomenclature_ref">' + ref + '</span>').remove()
						$('#nomenclature_qte').val(qte);
						$('#nomenclature_price').val(price);
					}
				).delegate(
					'#cancel-edit-nomenclature', 'click',
					function() {
						$('.frm-edit-nomenclature').html('<?php print _('Ajouter un produit') ?>');
						$('#add-nomenclature').val('<?php print _('Ajouter') ?>');
						$('#cancel-edit-nomenclature').remove();

						$('#nomenclature_ref').after('<input type="text" id="nomenclature_ref" name="nomenclature_ref" value="" />').remove();
						$('#hidden-nomenclature_ref').remove();
						$('#nomenclature_qte, #nomenclature_price').val('');
					}
				);
				-->
			</script>

		<?php } ?>

	<?php } elseif ($tab == 'images') {
	print view_admin_img_table(CLS_PRODUCT, $prd['id']);
	print view_admin_tab_documents_show(CLS_PRODUCT, $prd['id']);
} elseif ($tab == 'stocks') { ?>
		<script>
			//<![CDATA[
			function addColisagePrd() {
				displayPopup("<?php print _('Modifier les valeurs autorisées') ?>", '', 'popup-colisage.php?prd=<?php print $_GET['prd']; ?>');
				return false;
			}

			function delColisagePrd(col) {
				$.ajax({
					type: "POST",
					url: '/admin/catalog/ajax-colisage.php',
					data: 'del=1&prd=<?php print $_GET['prd']; ?>&col=' + col,
					dataType: 'xml',
					async: false,
					success: function(xml) {
						if ($(xml).find('result').attr('type') == '1') {
							refreshColisage(<?php print $_GET['prd']; ?>);
						} else {
							alert($(xml).find('msg').text());
						}
					},
					error: function(res) {
						alert("<?php print _('Une erreur inattendue s\'est produite lors de l\'ajout du conditionnement.') . '\n' . _('Veuillez réessayer ou bien prendre contact avec l\'administrateur') ?>");
					}
				});
				return false;
			}

			function linkColisage() {
				$.ajax({
					type: "POST",
					url: '/admin/catalog/ajax-colisage.php',
					data: 'link=1&prd=<?php print $_GET['prd']; ?>&col=' + $("#colisage").val(),
					dataType: 'xml',
					async: false,
					success: function(xml) {
						if ($(xml).find('result').attr('type') == '1') {
							refreshColisage(<?php print $_GET['prd']; ?>);
							$("#colisage option[value=0]").attr('selected', 'selected');
						} else {
							alert($(xml).find('msg').text());
						}
					},
					error: function(res) {
						alert("<?php print _('Une erreur inattendue s\'est produite lors de l\'ajout du conditionnement.') . '\n' . _('Veuillez réessayer ou bien prendre contact avec l\'administrateur') ?>");
						return false;
					}
				});
			}

			function refreshColisage(prd) {
				$.ajax({
					type: "POST",
					url: '/admin/catalog/ajax-colisage.php',
					data: 'refresh=1&prd=<?php print $_GET['prd']; ?>',
					dataType: 'xml',
					async: false,
					success: function(xml) {
						if ($(xml).find('result').attr('type') == '1') {
							$("#prd-colisage").html($(xml).find('msg').text());
						} else {
							alert($(xml).find('msg').text());
						}
					},
					error: function(res) {
						alert("<?php print _('Une erreur inattendue s\'est produite lors de l\'ajout du conditionnement.') . '\n' . _('Veuillez réessayer ou bien prendre contact avec l\'administrateur') ?>");
						return false;
					}
				});
			}

			function closeColisage() {
				hidePopup();
			}
			//]]>
		</script>

<?php if( $prd['follow_stock'] ){ // Affiche le stock détaillé, uniquement si le produit est suivi en stock ?>
			<table id="tab-stock" class="tab-stock tb-stock-all checklist">
				<caption><?php print _('Stock actuel'); ?></caption>
				<thead class="thead-none">
					<tr>
						<th id="dps"><?php print _('Dépôt'); ?></th>
						<th id="qte" class="align-right" title="<?php print _('Votre stock physique duquel est retranché les réservations.'); ?>">
							<?php print _('Disponible'); ?>
						</th>
						<th id="res" class="align-right" title="<?php print _('Stock commandé par vos clients mais qui n\'a pas encore été préparé.'); ?>">
							<?php print _('Réservé'); ?>
						</th>
						<?php if ($use_sto_res) {
							?>
							<th id="res_web" class="align-right" title="<?php print _('Stock commandé par vos clients sur la boutique en ligne mais qui n\'a pas encore été préparé.'); ?>">
								<?php print _('Réservé (Web)'); ?>
							</th>
						<?php } ?>
						<th id="com" class="align-right" title="<?php print _('Stock en commande chez votre fournisseur.'); ?>">
							<?php print _('Commandé'); ?>
						</th>
						<th id="prepa" class="align-right" title="<?php print _('Stock commandé par vos clients et en cours de préparation.'); ?>">
							<?php print _('Préparation'); ?>
						</th>
						<th id="mini" class="align-right" title="<?php print _('Stock consommé durant le délai de réapprovisionnement (facultatif).'); ?>">
							<?php print _('Mini'); ?>
						</th>
						<th id="maxi" class="align-right" title="<?php print _('Stock maximal possible en fonction de votre espace de stockage (facultatif).'); ?>">
							<?php print _('Maxi'); ?>
						</th>
						<th id="reappro" class="align-left" title="<?php print _('Date de réapprovionnement.'); ?>">
							<?php print _('Réappro. le'); ?>
						</th>
					</tr>
				</thead>
				<tbody>
					<?php
						// Charge la liste des dépôts
						$depots = prd_deposits_get();
						$updatable = $show_totals = false; // Détermine s'il est utile ou non d'afficher le bouton Enregistrer
						$colspan = $use_sto_res ? 9 : 8;

						if (!$depots || !ria_mysql_num_rows($depots)) { ?>
						<tr>
							<td colspan="<?php print $colspan; ?>">
								<?php print _('Aucun dépôt de stockage n\'a été configuré. Vous avez la possibilité de <a href="/admin/config/livraison/deposits/index.php">le faire ici</a>.'); ?>
							</td>
						</tr>
					<?php }else{ ?>
						<?php
							$tot_qte = $tot_res = $tot_res_web = $tot_com = $tot_prepa = $tot_mini = $tot_maxi = 0;
							$show_totals = true;
						?>
						<?php while ($d = ria_mysql_fetch_array($depots)) { ?>
							<?php
							$stocks = prd_dps_stocks_get($_GET['prd'], $d['id']);

							$s = $stocks && ria_mysql_num_rows($stocks)
								? ria_mysql_fetch_assoc($stocks)
								: array('qte' => 0, 'res' => 0, 'res_web' => 0, 'com' => 0, 'prepa' => 0, 'mini' => 0, 'maxi' => 0, 'date_restocking_en' => '');
							?>
							<tr>
								<td headers="dps" class="nowrap">
									<?php print view_dps_is_sync($d); ?>
									<?php if ($d['is_main']) { ?>
										<strong><?php print htmlspecialchars($d['name']); ?></strong>
									<?php } else { ?>
										<?php print htmlspecialchars($d['name']); ?>
									<?php } ?>
								</td>
								<?php if ($d['is_sync'] && $prd['is_sync']) { ?>
									<td headers="qte" class="align-right" data-label="<?php print _('Disponible : '); ?>"><?php print ria_number_format($s['qte'], NumberFormatter::DECIMAL); ?></td>
									<td headers="res" class="align-right" data-label="<?php print _('Réservé : '); ?>"><?php print ria_number_format($s['res'], NumberFormatter::DECIMAL); ?></td>
									<?php if ($use_sto_res) { ?>
										<td headers="res_web" class="align-right" data-label="<?php print _('Réservé (Web) : '); ?>"><?php print ria_number_format($s['res_web'], NumberFormatter::DECIMAL); ?></td>
									<?php } ?>
									<td headers="com" class="align-right" data-label="<?php print _('Commandé : '); ?>"><?php print ria_number_format($s['com'], NumberFormatter::DECIMAL); ?></td>
									<td headers="prepa" class="align-right" data-label="<?php print _('Préparation : '); ?>"><?php print ria_number_format($s['prepa'], NumberFormatter::DECIMAL); ?></td>
									<td headers="mini" class="align-right" data-label="<?php print _('Mini : '); ?>"><?php print ria_number_format($s['mini'], NumberFormatter::DECIMAL); ?></td>
									<td headers="maxi" class="align-right" data-label="<?php print _('Maxi : '); ?>"><?php print ria_number_format($s['maxi'], NumberFormatter::DECIMAL); ?></td>
									<td headers="reappro" class="align-left" data-label="<?php print _('Réappro. le : '); ?>"><?php print ria_date_format( $s['date_restocking_en'] ); ?></td>
								<?php }else{ $updatable = true; ?>
									<td headers="qte" class="align-right" data-label="<?php print _('Disponible : '); ?>">
										<input type="text" name="stock-qte-<?php print $d['id']; ?>" value="<?php print ria_number_format($s['qte'], NumberFormatter::DECIMAL); ?>">
									</td>
									<td headers="res" class="align-right" data-label="<?php print _('Réservé : '); ?>">
										<input type="text" name="stock-res-<?php print $d['id']; ?>" value="<?php print ria_number_format($s['res'], NumberFormatter::DECIMAL); ?>">
									</td>
									<?php if ($use_sto_res) { ?>
										<td headers="res_web" class="align-right" data-label="<?php print _('Réservé (Web) : '); ?>">
											<input type="text" name="stock-res_web-<?php print $d['id']; ?>" value="<?php print ria_number_format($s['res_web'], NumberFormatter::DECIMAL); ?>">
										</td>
									<?php } ?>
									<td headers="com" class="align-right" data-label="<?php print _('Commandé : '); ?>">
										<input type="text" name="stock-com-<?php print $d['id']; ?>" value="<?php print ria_number_format($s['com'], NumberFormatter::DECIMAL); ?>">
									</td>
									<td headers="prepa" class="align-right" data-label="<?php print _('Préparation : '); ?>">
										<input type="text" name="stock-prepa-<?php print $d['id']; ?>" value="<?php print ria_number_format($s['prepa'], NumberFormatter::DECIMAL); ?>">
									</td>
									<td headers="mini" class="align-right" data-label="<?php print _('Mini : '); ?>">
										<input type="text" name="stock-mini-<?php print $d['id']; ?>" value="<?php print ria_number_format($s['mini'], NumberFormatter::DECIMAL); ?>">
									</td>
									<td headers="maxi" class="align-right" data-label="<?php print _('Maxi : '); ?>">
										<input type="text" name="stock-maxi-<?php print $d['id']; ?>" value="<?php print ria_number_format($s['maxi'], NumberFormatter::DECIMAL); ?>">
									</td>
									<td headers="reappro" class="align-left" data-label="<?php print _('Réappro. le : '); ?>">
										<input class="date" type="text" name="stock-date-<?php print $d['id']; ?>" value="<?php print ria_date_format($s['date_restocking_en']); ?>">
									</td>
								<?php } ?>
							</tr>
						<?php
							$tot_qte += $s['qte'];
							$tot_res += $s['res'];
							$tot_res_web += $s['res_web'];
							$tot_com += $s['com'];
							$tot_prepa += $s['prepa'];
							$tot_mini += $s['mini'];
							$tot_maxi += $s['maxi'];
						?>
						<?php } ?>
					<?php } ?>
				</tbody>
				<tfoot>
					<?php if( $show_totals ){ ?>
						<tr>
							<th><?php print _('Total :'); ?></th>
							<th headers="qte" class="align-right" data-label="<?php print _('Disponible : '); ?>"><?php print ria_number_format($tot_qte, NumberFormatter::DECIMAL); ?></th>
							<th headers="res" class="align-right" data-label="<?php print _('Réservé : '); ?>"><?php print ria_number_format($tot_res, NumberFormatter::DECIMAL); ?></th>
								<?php if ($use_sto_res) { ?>
								<th headers="res_web" class="align-right" data-label="<?php print _('Réservé (Web) : '); ?>"><?php print ria_number_format($tot_res_web, NumberFormatter::DECIMAL); ?></th>
								<?php } ?>
							<th headers="com" class="align-right" data-label="<?php print _('Commandé : '); ?>"><?php print ria_number_format($tot_com, NumberFormatter::DECIMAL); ?></th>
							<th headers="prepa" class="align-right" data-label="<?php print _('Préparation : '); ?>"><?php print ria_number_format($tot_prepa, NumberFormatter::DECIMAL); ?></th>
							<th headers="mini" class="align-right" data-label="<?php print _('Mini : '); ?>"><?php print ria_number_format($tot_mini, NumberFormatter::DECIMAL); ?></th>
							<th headers="maxi" class="align-right" data-label="<?php print _('Maxi : '); ?>"><?php print ria_number_format($tot_maxi, NumberFormatter::DECIMAL); ?></th>
							<th headers="reappro" class="align-left"></th>
						</tr>
					<?php } ?>
					<?php if( $updatable ){ ?>
					<tr>
						<th colspan="<?php print $colspan; ?>" class="align-right">
							<button type="submit" name="save-stock"><?php print _('Enregistrer'); ?></button>
						</th>
					</tr>
					<?php } ?>
					</tfoot>

			</table>
		<?php } ?>

		<table class="tb-stock-all">
			<caption><?php print _('Conditionnements'); ?></caption>
			<tbody>
				<tr>
					<td id="td-stock-conditionnement"><?php print _('Conditionnement :') ?></td>
					<td id="prd-colisage"><?php

						require_once('prd/colisage.inc.php');

						$colisage = prd_colisage_classify_get(0, $_GET['prd'], 0, array('rcol_name' => 'asc', 'qte' => 'asc'));
						if ($colisage && ria_mysql_num_rows($colisage)) {
							while ($col = ria_mysql_fetch_array($colisage)) {
								print '<input onclick="return delColisagePrd(' . $col['col_id'] . ');" type="image" name="del-col-' . $col['col_id'] . '" src="../images/del-cat.svg" width="16" height="16" title="' . _('Retirer ce conditionnement') . ' ' . htmlspecialchars($col['col_name']) . '" class="icon-del-cat" /> ';
								print htmlspecialchars($col['col_name']) . ' (' . _('Qté: ') . number_format($col['qte'], intval($col['qte']) == floatval($col['qte']) ? 0 : 2, ',', '') . ')<br />';
							}
						} else {
							print _('Aucun conditionnement');
						}
						?></td>
				</tr>
				<tr>
					<td><label for="colisage"><?php print _('Ajouter :'); ?></label></td>
					<td>
						<select name="colisage" id="colisage" onchange="return linkColisage();">
							<option value="0"><?php print _('Choisir un conditionnement') ?></option>
							<?php
							$colisage = prd_colisage_types_get(0, false, array('rname' => 'asc', 'qte' => 'asc'));
							if ($colisage) {
								while ($col = ria_mysql_fetch_array($colisage)) {
									print '<option value="' . $col['id'] . '">' . $col['name'] . ' (' . _('Qté: ') . number_format($col['qte'], intval($col['qte']) == floatval($col['qte']) ? 0 : 2, ',', '') . ')' . '</option>';
								}
							}
							?>
						</select>
						<sub><a class="edit" href="#" onclick="return addColisagePrd()"><?php print _('Editer la liste'); ?></a></sub>
					</td>
				</tr>
			</tbody>
		</table>

		<?php
			// Une règle par défaut applicable dans tous les conditions, sauf si une règle existe, est stockée dans le champ avancé : _FLD_PRD_SALES_UNIT
			// Toutes les autres règles sont définies pour un client (cf. /include/usr/sell-units-rules.inc.php)
			gu_sell_units_rules_get_for_product(3576690, $prd['id'], 7471 );
			gu_sell_units_rules_get_for_product(3576688, $prd['id'], 7471 );

			print '<table>'
				.'<caption>'._('Unité de vente').'</caption>'
				.'<tbody>'
					.'<tr>'
						.'<td class="td-label">'.('Unité de vente :').'<br /><sub>'._('(appliquée par défaut)').'</sub></td>'
						.'<td>';
							$sellunit = 1;

							$temp_udv = fld_object_values_get( $prd['id'], _FLD_PRD_SALES_UNIT, '', false, true );
							if( is_numeric($temp_udv) && $temp_udv > 0 ){
								$sellunit = $temp_udv;
							}

							print '<input type="number" min="1" name="sellunit" value="'.$sellunit.'" />'
						.'</td>'
					.'</tr>'
					.'<tr>'
						.'<th colspan="2">'._('Règles').'</th>'
					.'</tr>'
					.'<tr>'
						.'<td colspan="2">'
							.'<table class="checklist" id="list-sell-units-rules" data-cls-main="'.CLS_PRODUCT.'" data-cls-id="'.CLS_PRODUCT.','.CLS_PRD_COLISAGE.'" data-prd-id="'.$prd['id'].'">'
								.'<col width="55" /><col width="*" /><col width="150" /><col width="150" />'
								.'<thead>'
									.'<tr>'
										.'<th>'
											.'<input type="checkbox" class="checkbox" onclick="checkAllClick(this)" />'
										.'</th>'
										.'<th>'._('Client').'</th>'
										.'<th>'._('Conditionnement').'</th>'
										.'<th>'._('Unité de vente').'</th>'
									.'</tr>'
								.'</thead>'
								.'<tfoot>'
									.'<tr>'
										.'<td align="left" colspan="2">'
											.'<input type="submit" name="del-sell-unit-rule" id="del-sell-unit-rule" value="'._('Supprimer').'" />'
										.'</td>'
										.'<td colspan="2">'
											.'<input type="button" name="add-sell-unit-rule" value="'._('Ajouter une règle').'" />'
										.'</td>'
									.'</tr>'
								.'</tfoot>'
								.'<tbody>'
									.'<tr>'
										.'<td colspan="4">'
											._('Chargement en cours...')
										.'</td>'
									.'</tr>'
								.'</tbody>'
							.'</table>'
						.'</td>'
					.'</tr>'
				.'</tbody>'
				.'<tfoot>'
					.'<tr>'
						.'<td colspan="2">'
							.' <input type="submit" name="save-sellunit" value="'._('Enregistrer').'" class="btn-main" />'
							.' <input type="submit" name="cancel" value="'._('Annuler').'" />'
						.'</td>'
					.'</tr>'
				.'</tfoot>'
			.'</table>';
		?>
		<?php
		$reappro_editable = $prd['follow_stock'] && !$prd['is_sync'];
		 ?>
		<table id="table-reapprovisionnement" class="tb-stock-all">
			<caption><?php print _('Prévisions de réapprovisionnement') ?></caption>
			<tbody>
				<tr>
					<th colspan="3"><?php print _('Disponibilité'); ?></th>
					<?php if(!$reappro_editable){  ?>
					<th class="col70px"><?php print _('Quantité') ?></th>
					<?php } ?>
				</tr>

				<?php
				$now = new \DateTime();
				$schedules = prd_stocks_schedule_get(0, $_GET['prd'], $now->format('Y-m-d H:i:s'), null, null, null, null);
				$schedules_rows_count = ria_mysql_num_rows($schedules);
				$counter = 0;

				if($schedules_rows_count){
					while (($schedule = ria_mysql_fetch_assoc($schedules)) && $counter < 5){
						if ($schedule['qte'] <= 0) {
							continue;
						}
						$counter++;
						?>
						<tr>
							<td><label for="stock-livr"><?php print _('Date de disponibilité :'); ?></label></td>
							<td colspan="2">
							<?php $schedule_date = \DateTime::createFromFormat('Y-m-d H:i:s', $schedule['date']);?>
								<input
									type="text"
									class="datepicker"
									name="stock-livr"
									id="stock-livr"
									value="<?php print $schedule_date ? $schedule_date->format('d/m/Y') : $schedule_date; ?>"
									autocomplete="off"
									<?php print !$reappro_editable ? 'disabled="disabled"' : '' ?> />
							</td>
							<td><input type="number" class="schedule-qte" name="schedule-qte[<?php print $counter; ?>]" value="<?php print htmlspecialchars($schedule['qte']); ?>" <?php print !$reappro_editable ? 'disabled="disabled"' : '' ?> /></td>
					</tr>
						<?php }
				}
				if ($counter == 0) { ?>
				<tr>
					<td><label for="stock-livr"><?php print _('Date de disponibilité :'); ?></label></td>
					<td colspan="2">
						<?php $stockLivr = \DateTime::createFromFormat('d/m/Y', $prd['stock_livr']);?>
						<input
							type="text"
							class="datepicker"
							name="stock-livr"
							id="stock-livr"
							value="<?php print htmlspecialchars($stockLivr ? $stockLivr->format('d/m/Y') : ''); ?>"
							autocomplete="off"
							<?php print !$reappro_editable ? 'disabled="disabled"' : '' ?> />
					</td>
				</tr>
					<?php } ?>



				<?php if( $reappro_editable ){ ?>
				<tr>
					<th colspan="3"><?php print _('Prévisions de réapprovisionnement') ?></th>
				</tr>
				<tr>
					<td colspan="3">
						<table class="tab-stock schedule-table" data-prototype="<?php print htmlspecialchars(
																																					<<<PROTOTYPE
							<tr>
								<input type="hidden" name="schedule-id[#idx#]" value="" />
								<td><input type="checkbox" class="schedule-choice" name="schedule-choice[#idx#]" /></td>
								<td><input type="text" class="schedule-date datepicker" name="schedule-date[#idx#]" value="" autocomplete="off" /></td>
								<td><input type="number" class="schedule-qte" name="schedule-qte[#idx#]" value=""/></td>
								<td class="schedule-confirmed-col"><input type="checkbox" class="schedule-confirmed" name="schedule-confirmed[#idx#]" /></td>
							</tr>
PROTOTYPE
																																				); ?>">
							<thead>
								<tr>
									<th class="col40px">&nbsp;</th>
									<th><?php print _('Date') ?></th>
									<th class="col70px"><?php print _('Quantité') ?></th>
									<th class="col100px"><?php print _('Confirmé ?') ?></th>
								</tr>
							</thead>
							<tbody>
								<?php
								$hasSchedule = false;
								$i = 0;
								if ($schedules && $schedules_rows_count) {
									while ($schedules && ($schedule = ria_mysql_fetch_assoc($schedules))) {
										$hasSchedule = true;
										$scheduleDate = \DateTime::createFromFormat('Y-m-d H:i:s', $schedule['date']);
										?>
										<tr>
											<input type="hidden" name="schedule-id[<?php print $i; ?>]" value="<?php print htmlspecialchars($schedule['id']); ?>" />
											<td><input type="checkbox" class="schedule-choice" name="schedule-choice[<?php print $i; ?>]" /></td>
											<td><input type="text" class="schedule-date datepicker" name="schedule-date[<?php print $i; ?>]" value="<?php print $scheduleDate ? $scheduleDate->format('d/m/Y') : $scheduleDate; ?>" autocomplete="off" /></td>
											<td><input type="number" class="schedule-qte" name="schedule-qte[<?php print $i; ?>]" value="<?php print htmlspecialchars($schedule['qte']); ?>" /></td>
											<td class="schedule-confirmed-col"><input type="checkbox" class="schedule-confirmed" name="schedule-confirmed[<?php print $i; ?>]" <?php print ($schedule['confirmed']) ? 'checked="checked"' : ''; ?> /></td>
										</tr>
										<?php
										$i++;
									}
								}

								if (!$hasSchedule) { ?>
									<tr class="add-schedule">
										<td class="align-left"><input type="checkbox" class="schedule-choice" name="schedule-choice[#idx#]" /></td>
										<td class="align-left" data-label="<?php print _('Date :'); ?>"> <input type="text" class="schedule-date datepicker" name="schedule-date[<?php print $i ?>]" autocomplete="off" /></td>
										<td data-label="<?php print _('Quantité : '); ?>"><input type="number" class="schedule-qte" name="schedule-qte[<?php print $i ?>]" /></td>
										<td class="schedule-confirmed-col" data-label="<?php print _('Confirmé ? : '); ?>">
											<input type="checkbox" class="schedule-confirmed" name="schedule-confirmed[<?php print $i ?>]" />
										</td>
									</tr>
								<?php } ?>
								<tr class="add-schedule-row">
									<td colspan="4">
										<a class="edit" href="#" onclick="$($('.schedule-table').data('prototype').replace(/#idx#/ig, $('.schedule-table > tbody > tr').length - 1)).insertBefore('.schedule-table .add-schedule-row').find('input.datepicker').productDatepicker(); return false;"><?php print _('Ajouter une date') ?></a>
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<td colspan="2" class="submit-schedule-row" style="text-align: left;">
										<input type="submit" name="del-schedule" value="<?php print _('Supprimer') ?>" />
									</td>
									<td colspan="2"></td>
								</tr>
							</tfoot>
						</table>
					</td>
				</tr>
				<?php } ?>
			</tbody>
			<?php if( $reappro_editable ){ ?>
			<tfoot>
				<tr>
					<td colspan="3">
						<input type="submit" name="save-schedule" value="<?php print _('Enregistrer') ?>" />
						<input type="submit" name="cancel" value="<?php print _('Annuler') ?>" />
					</td>
				</tr>
			</tfoot>
			<?php } ?>
		</table>

		<?php
		$res = prd_stocks_infos_get($prd['id']);
		if ($res && ria_mysql_num_rows($res)) {
			?>
			<table>
				<caption><?php print view_prd_is_sync($prd); ?> <?php print _('Historique des stocks') ?></caption>
				<thead>
					<tr>
						<th id="psidate"><?php print _('Date') ?></th>
						<th id="psi_label"><?php print _('Informations') ?></th>
					</tr>
				</thead>
				<tbody>
					<?php while ($lgn = ria_mysql_fetch_assoc($res)) { ?>
						<tr>
							<td>
								<?php
								$tmpstp = strtotime($lgn["date"]);
								echo _("Depuis le") . dateformatcomplet($tmpstp, "dFY");
								?>
							</td>
							<td>
								<?php echo $lgn['label']; ?>
							</td>
						</tr>
					<?php } ?>
				</tbody>
			</table>
		<?php
	}
	?>

	<?php } elseif ($tab == 'delivery') { ?>
		<table id="table-poids-dimentions">
			<caption><?php print _('Poids et dimensions'); ?></caption>
			<tfoot>
				<tr>
					<td colspan="2">
						<input type="submit" name="save-misc" value="<?php print _('Enregistrer'); ?>" />
						<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" />
					</td>
				</tr>
			</tfoot>
			<tbody>
				<tr>
					<td class="col145px"><label for="weight"><?php print $prd['is_sync'] ? view_prd_is_sync($prd) : ''; ?> <?php print _('Poids brut :'); ?></label></td>
					<td><input type="text" class="size" name="weight" id="weight" value="<?php if ($prd['weight']) print number_format($prd['weight'], 0, ',', ' '); ?>" onblur="if( trim(this.value) ) this.value = number_format(parseFFloat(this.value),0,',',' ')" onfocus="this.value = this.value.replace(' ',''); this.select();" <?php if ($prd['is_sync']) print 'disabled="disabled"'; ?> />
						<?php print _('grammes') ?>
					</td>
				</tr>
				<tr>
					<td><label for="weight-net"><?php print $prd['is_sync'] ? view_prd_is_sync($prd) : ''; ?> <?php print _('Poids net :'); ?></label></td>
					<td><input type="text" class="size" name="weight-net" id="weight-net" value="<?php if ($prd['weight_net']) print number_format($prd['weight_net'], 0, ',', ' '); ?>" onblur="if( trim(this.value) ) this.value = number_format(parseFFloat(this.value),0,',',' ')" onfocus="this.value = this.value.replace(' ',''); this.select();" <?php if ($prd['is_sync']) print 'disabled="disabled"'; ?> />
						<?php print _('grammes') ?>
					</td>
				</tr>
				<tr>
					<td><label for="length"><?php print _('Longueur :'); ?></label></td>
					<td><input type="text" class="size" name="length" id="length" value="<?php if ($prd['length']) print number_format($prd['length'], 0, ',', ' '); ?>" onblur="if( trim(this.value) ) this.value = number_format(parseFFloat(this.value),0,',',' ')" onfocus="this.value = this.value.replace(' ',''); this.select();" /> <abbr title="<?php print _('Centimètres') ?>"><?php print _('cm') ?></abbr></td>
				</tr>
				<tr>
					<td><label for="width"><?php print _('Largeur :'); ?></label></td>
					<td><input type="text" class="size" name="width" id="width" value="<?php if ($prd['width']) print number_format($prd['width'], 0, ',', ' '); ?>" onblur="if( trim(this.value) ) this.value = number_format(parseFFloat(this.value),0,',',' ')" onfocus="this.value = this.value.replace(' ',''); this.select();" /> <abbr title="<?php print _('Centimètres') ?>"><?php print _('cm') ?></abbr></td>
				</tr>
				<tr>
					<td><label for="height"><?php print _('Hauteur :'); ?></label></td>
					<td><input type="text" class="size" name="height" id="height" value="<?php if ($prd['height']) print number_format($prd['height'], 0, ',', ' '); ?>" onblur="if( trim(this.value) ) this.value = number_format(parseFFloat(this.value),0,',',' ')" onfocus="this.value = this.value.replace(' ',''); this.select();" /> <abbr title="<?php print _('Centimètres') ?>"><?php print _('cm') ?></abbr></td>
				</tr>
				<tr>
					<td><label for="garantie"><?php print _('Garantie :'); ?></label></td>
					<td><input type="text" class="size" name="garantie" id="garantie" value="<?php if ($prd['garantie']) print number_format($prd['garantie'], 0); ?>" onblur="if( trim(this.value) ) this.value = number_format(parseFFloat(this.value),0,'','')" />
						<?php print _('mois') ?>
					</td>
				</tr>
				</tfoot>
		</table>
		<?php
		// Affiche la liste des services de livraison compatibles avec ce produit
		$services_unavailable = array();
		$rservices_unavailable = dlv_products_unavailable_get($prd['id']);
		if ($rservices_unavailable && ria_mysql_num_rows($rservices_unavailable)) {
			while ($s = ria_mysql_fetch_array($rservices_unavailable)) {
				$services_unavailable[] = $s['srv_id'];
			}
		}

		$services = dlv_services_get();
		if( $services && ria_mysql_num_rows($services) ){
			?>
			<table id="table-services-livraison">
				<caption><?php print _('Services de livraison compatibles'); ?></caption>
				<tfoot>
					<tr>
						<td id="td-services-livraison" colspan="2">
							<input type="submit" name="save-misc" value="<?php print _('Enregistrer'); ?>" />
							<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" />
						</td>
					</tr>
				</tfoot>
				<tbody>
					<?php
					print ' <tr>
								<td colspan="2">';
					print '		    <ul class="services_available">
										<li class="edit"><a href="#" class="check-all">' . _('Cocher tout') . '</a> | <a href="#" class="uncheck-all">' . _('Décocher tout') . '</a></li>';
					while( $srv = ria_mysql_fetch_array($services) ){
						print '		    <li><label><input type="checkbox" name="services_available[]" ' . (in_array($srv['id'], $services_unavailable) ? '' : 'checked="checked"') . ' value="' . $srv['id'] . '"/> ' . htmlspecialchars($srv['name']) . '</label></li>';
					}
					print '		    </ul>';
					print '     </td>
							</tr>';
					?>
				</tbody>
			</table>
		<?php } ?>

	<?php } elseif ($tab == 'reviews') { ?>
		<?php
		require_once('prd/reviews.inc.php');
		require_once('messages.inc.php');
		$type = ria_mysql_fetch_array(gu_messages_types_get(0, 'RVW_PRODUCT'));
		?>

		<?php if (!isset($_GET['rvw'])) { ?>
			<table id="cntcontact" class="large">
				<caption><?php print _('Avis consommateurs') ?></caption>
				<thead class="thead-none">
					<tr>
						<th id="th-author"><?php print _('Auteur') ?></th>
						<th id="th-message"><?php print _('Message') ?></th>
					</tr>
				</thead>
				<tfoot>
					<tr id="pagination">
						<td colspan="2">&nbsp;</td>
					</tr>
				</tfoot>
				<tbody><?php
						$rmsg = messages_get(0, 'RVW_PRODUCT', 0, 0, 0, false, true, false, $_GET['prd']);
						if (!$rmsg || !ria_mysql_num_rows($rmsg)) {
							print '<tr><td colspan="2">' . _('Aucun message ne correspond aux critères de sélection.') . '</td></tr>';
						} else {
							ria_mysql_data_seek($rmsg, 0);
							$count = 0;
							while ($msg = ria_mysql_fetch_array($rmsg)) {
								// Limite le nombre de résultat à 25 par page
								if ($count >= 25) break;
								// Information sur le compte rattaché, s'il en existe un
								$usr['is_sync'] = 0;
								if ($msg['usr_id'] > 0) {
									$rusr = gu_users_get($msg['usr_id']);
									if ($rusr && ria_mysql_num_rows($rusr))
										$usr = ria_mysql_fetch_array($rusr);
								}
								print '	<tr id="message-' . $msg['id'] . '">
											<td headers="th-author" class="td-author" data-label="'._('Auteur :').' ">';
								// affiche les informations sur le produit conserné par le commentaire
								if ($msg['type'] == 'RVW_PRODUCT' && prd_products_exists($msg['prd_id'])) {
									$prd = ria_mysql_fetch_array(prd_products_get_simple($msg['prd_id']));

									$url = '#';
									$rcat = prd_products_categories_get($prd['id'], true);
									if ($rcat != false && ria_mysql_num_rows($rcat) > 0) {
										$cat = ria_mysql_fetch_array($rcat);
										$url = '/admin/catalog/product.php?cat=' . $cat['cat'] . '&amp;prd=' . $prd['id'];
									}

									print '	<span class="bold">' . _('Produit concerné :') . ' </span><br />' . view_prd_is_sync($prd) . '&nbsp;
											<a href="' . $url . '" target="_blank">' .htmlspecialchars( $prd['title'] ). '</a><br /><br />';
								}
								// Afficher les informations sur l'expéditeur, destinataire, en copie
								print '			<span class="bold">' . _('Envoyé par :') . '</span><br />' . view_usr_is_sync($usr) . '&nbsp;';
								if ($msg['usr_id'] && isset($usr['adr_firstname'], $usr['adr_lastname'])) {
									print '<a href="/admin/customers/edit.php?usr=' . $msg['usr_id'] . '" target="_blank">';
									print htmlspecialchars( $usr['adr_firstname'] . ' ' . $usr['adr_lastname'] );
									print '</a>';
									$surnom = fld_object_values_get($usr['id'], _FLD_PSEUDO);
									if (trim($surnom)){
										print '<br />(' . _('Surnom :') . ' ' . htmlspecialchars($surnom) . ')';
									}
								} else {
									print htmlspecialchars( $msg['firstname'] . ' ' . $msg['lastname'] );
								}
								print '			<br /><em>' . sprintf( _('Le %s'), ria_date_format($msg['date_created']) ) . '</em><br/><br/>';
								$to = explode(',', $msg['email_to']);
								if (sizeof($to) > 0 && $to[0] != '') {
									print '			<span class="bold">' . _('Destinataire') . ' : </span>';
									foreach ($to as $key => $email) {
										$usr_to = gu_users_get(0, $email);
										if ($usr_to != false && ria_mysql_num_rows($usr_to) > 0) {
											$usr_to = ria_mysql_fetch_array($usr_to);
											$name = '';
											if ($usr_to['type_id'] == 2){
												$name = $usr_to['society'];
											}else{
												$name = $usr_to['adr_firstname'] . ' ' . $usr_to['adr_lastname'];
											}
											print '<br />' . view_usr_is_sync($usr_to) . ' ' . ($usr_to['id'] ? '<a href="/admin/customers/edit.php?usr=' . $usr_to['id'] . '" target="_blank">' . htmlspecialchars( $name ) . '</a>' : htmlspecialchars( $name ));
										} else
											print '<br /><a href="mailto:' . htmlspecialchars($email) . '">' . htmlspecialchars($email) . '</a>';
									}
								}
								$cc = explode(',', $msg['email_cc']);
								if (sizeof($cc) > 0 && $cc[0] != '') {
									print '			<br /><br/><span class="bold">' . _('En copie') . ' : </span>';
									foreach ($cc as $key => $email) {
										$usr_cc = gu_users_get(0, $email);
										if ($usr_cc != false && ria_mysql_num_rows($usr_cc) > 0) {
											$usr_cc = ria_mysql_fetch_array($usr_cc);
											$name = '';
											if ($usr_cc['type_id'] == 2){
												$name = $usr_cc['society'];
											}else{
												$name = $usr_cc['adr_firstname'] . ' ' . $usr_cc['adr_lastname'];
											}
											print '<br />' . view_usr_is_sync($usr_cc) . ' ' . ($usr_cc['id'] ? '<a href="/admin/customers/edit.php?usr=' . $usr_cc['id'] . '" target="_blank">' . $name . '</a>' : $name);
										} else
											print '<br /><a href="mailto:' . htmlspecialchars($email) . '">' . htmlspecialchars($email) . '</a>';
									}
								}
								if ($type['moderate']) {
									$date_publish = '';
									print '			<div id="moderate-' . $msg['id'] . '" class="moderate">';
									// Date de publication ou dépublication
									if ($msg['date_publish'] != '') {
										$date_publish = $msg['date_publish'];
										if ($msg['usr_publish'] > 0 && ($usrP = ria_mysql_fetch_array(gu_users_get($msg['usr_publish'])))) {
											$date_publish .= ' <br />' . _('Par') . ' ' . view_usr_is_sync($usrP);
											$date_publish .= ' <a target="_blank" href="/admin/customers/edit.php?usr=' . $usrP['id'] . '">' . $usrP['adr_firstname'] . ' ' . $usrP['adr_lastname'] . '</a>';
										}
									}

									// Information sur le statut du message
									switch ($msg['publish']) {
										case 1:
											print '	<span class="bold">' . _('Modération') . ' :</span>
													<span class="info-publish">' . _('Approuvé le') . ' ';
											if ($date_publish != '')
												print ria_date_format($date_publish);
											print '<br /><a onclick="moderateMessage(' . $msg['id'] . ', false)" class="unchecked">' . _('Ne plus approuver') . '</a></span>';
											break;
										case 0:
											print '	<span class="bold">' . _('Modération') . ' :</span>
													<span class="info-publish">' . _('Refusé le') . ' ';
											if ($date_publish != '')
												print ria_date_format($date_publish);
											else
												print '<br />' . _('Par') . ' ';
											print '<br /><a onclick="moderateMessage(' . $msg['id'] . ', true)" class="checked">' . _('Approuver') . '</a></span>';
											break;
										default:
											print '	<span class="bold">' . _('Modération') . ' :</span>
													<span class="info-publish">';
											print _('En attente depuis le') . ' ' . ria_date_format($msg['date']);
											print '		<br /><a onclick="moderateMessage( ' . $msg['id'] . ', true)" class="checked">' . _('Approuver') . '</a> | <a onclick="moderateMessage( ' . $msg['id'] . ', false)" class="unchecked">' . _('Désapprouver') . '</a>
													</span>';
											break;
									}
									print '			</div>';
								}
								print '		</td>
											<td headers="th-message" class="td-message" data-label="'._('Message :').' ">
												<span class="bold">' . _('Sujet') . ' : ' . htmlspecialchars($msg['subject']) . '</span><br />';
								print nl2br(htmlspecialchars($msg['body'])) . '<br />';

								if ($msg['note'] > 0)
									print '	 		<br /><span class="bold italic">' . _('Note') . '</span> : ' . $msg['note'];
								if ($msg['note_dlv'] > 0)
									print '	 		<br /><span class="bold italic">' . _('Note sur la livraison') . '</span> : ' . $msg['note_dlv'];
								if ($msg['note_pkg'] > 0)
									print '	 		<br /><span class="bold italic">' . _('Note sur l\'emballage') . '</span> : ' . $msg['note_pkg'];

								// documents joints
								$docs = array();
								$rimg = gu_messages_images_get($msg['id']);
								if ($rimg && ria_mysql_num_rows($rimg)) {
									$sizeImg = $config['img_sizes']['medium'];
									$count = 1;
									while ($img = ria_mysql_fetch_array($rimg)) {
										$docs[] = '<a href="' . $config['img_url'] . '/' . $sizeImg['width'] . 'x' . $sizeImg['height'] . '/' . $img['id'] . '.' . $sizeImg['format'] . '" target="_blank">Image ' . $count . '</a>';
										$count++;
									}
								}

								$rd = messages_files_get(0, $msg['id']);
								if ($rd && ria_mysql_num_rows($rd)) {
									while ($d = ria_mysql_fetch_array($rd)){
										$docs[] = '<li><a href="/admin/customers/dl.php?file=' . $d['id'] . '">' . htmlspecialchars( $d['name'] ) . '</a></li>';
									}
								}

								if (sizeof($docs)) {
									$label = sizeof($docs) > 1 ? _('Pièces jointes :') : _('Pièce jointe :');
									print '<br /><span class="bold italic">' . htmlspecialchars( $label ) . '</span> <ul>' . implode(', ', $docs).'</ul>';
								}

								$fields_libs = fld_fields_get(0, 0, -1, 0, 0, $msg['id'], null, array(), false, array(), null, CLS_MESSAGE);
								if ($fields_libs && ria_mysql_num_rows($fields_libs)) {
									$first = true;
									$div = false;
									while ($fld = ria_mysql_fetch_array($fields_libs)) {
										if ($fld['obj_value'] != '' && $first) {
											print '			<div class="infos-compl"><span class="bold">' . _('Informations complémentaires :') . '</span><br/>';
											$div = true;
											$first = false;
										}
										if ($fld['obj_value'] != '')
											print '<span class="bold italic info-value">' . htmlspecialchars( $fld['name'] ) . '</span> : ' . htmlspecialchars( $fld['obj_value'] ) . '<br/>';
									}
									if ($div)
										print '</div>';
								}
								print '	<div id="reponse-' . $msg['id'] . '">';
								if ($msg['nbrep'] > 0) {
									$messages_reply = contacts_get_replies($msg['id']);
									if ($messages_reply !== false) {
										if (ria_mysql_num_rows($messages_reply) == 1) {
											print '<br />
										<a id="show-lst-rep-' . $msg['id'] . '" onclick="show_rep(' . $msg['id'] . ')">' . _('Afficher la réponse') . '</a>
										<a id="hide-lst-rep-' . $msg['id'] . '" onclick="hide_rep(' . $msg['id'] . ')" style="display:none">' . _('Masquer la réponse') . '</a>
									';
										} elseif (ria_mysql_num_rows($messages_reply) > 1) {
											print '<br />
										<a id="show-lst-rep-' . $msg['id'] . '" onclick="show_rep(' . $msg['id'] . ')">' . _('Afficher les réponses') . '</a>
										<a id="hide-lst-rep-' . $msg['id'] . '" onclick="hide_rep(' . $msg['id'] . ')" style="display:none">' . _('Masquer les réponses') . '</a>
									';
										}

										print '<div id="lst-rep-' . $msg['id'] . '" style="display:none;">';

										while ($msg_reply = ria_mysql_fetch_array($messages_reply)) {

											print '	<div class="rep">
														<span class="bold">' . _('Votre réponse') . ' :</span><br />
												<em>Le ' . ria_date_format($msg_reply['date_created']) . '</em><br /><br />
											';
											print nl2br(htmlspecialchars($msg_reply['body']));
											$r_file = messages_files_get(0, $msg_reply['id']);
											if (ria_mysql_num_rows($r_file) > 0) {

												print '<div>
													<br />' . _('Pièces jointes') . ' :<ul>';
												while ($file = ria_mysql_fetch_array($r_file)) {

													// Taille du fichier
													$size = round(($file['size'] / 1024), 1);
													$size = $size > 1024 ? $size = round(($size / 1024), 1) . ' Mo' : $size . ' Ko';
													print '<li><a href="/admin/customers/dl.php?file=' . $file['id'] . '">' . $file['name'] . '</a> <span class="size-file">(' . $size . ')</span></li>';
												}
												print '</ul>
												</div>';
											}
											print '</div>';
										}
										print '		<div class="clear"></div>
												</div>';
									}
								}
								print '	</div>
										<a name="rep-' . $msg['id'] . '"></a>';
								$stats = stats_origins_get($msg['id'], CLS_MESSAGE);
								if ($stats && ria_mysql_num_rows($stats)) {
									print '<div class="infos-compl">';
									$stat = ria_mysql_fetch_array($stats);
									print view_source_origin($stat);
									print '</div>';
								}
								print '			<div id="action-' . $msg['id'] . '" class="action">
													<input type="button" id="button-rep-' . $msg['id'] . '" class="show-form-replay" value="' . _('Répondre') . '" onclick="show_form_rep(' . $msg['id'] . ')" ' . (isset($_GET['cnt']) && $_GET['cnt'] == $msg['id'] ? 'style="display:none"' : '') . ' />';
								if( $msg['spam_id'] == 0 ){
									print '			<input type="button" id="button-spam-' . $msg['id'] . '" class="show-form-spam button-del" value="' . _('Signaler comme spam') . '" onclick="block_ats_ip(' . $msg['id'] . ')" />';
								}else{
									print '			<input type="button" id="button-spam-' . $msg['id'] . '" class="show-form-spam" value="' . _('Retirer des spams') . '" onclick="unblock_ats_ip(' . $msg['id'] . ')" />';
								}
								print '			</div>
												<div class="clear"></div>
												<div id="form-rep-' . $msg['id'] . '" ' . (isset($_GET['cnt']) && $_GET['cnt'] == $msg['id'] ? 'style="display:block"' : 'style="display:none"') . ' class="new-rep">
													****<form action="moderation.php" id="form-contact-' . $msg['id'] . '" method="post" enctype="multipart/form-data">
														<input type="hidden" name="tab-file" id="tab-file-' . $msg['id'] . '" value="" />
														<input type="hidden" name="msg" value="' . $msg['id'] . '" />
														<span class="title-new-rep">' . _('R&eacute;ponse') . ' : </span>
														<div class="clear"></div>
														<div class="message">
															<textarea name="reponce-message" cols="67" rows="10" placeholder="' . _('Saisissez votre réponse') . '"></textarea>
														</div>
														<a id="link-join-file-' . $msg['id'] . '" onclick="show_join(' . $msg['id'] . ')">' . _('Attacher des fichiers à votre réponse<') . '/a>
														<div class="clear"></div>
														<div id="div-file-' . $msg['id'] . '" class="join-file" style="display:none;">
															<span class="title-new-rep">' . _('Pièces Jointes') . '  : </span>
															<div id="join-file-' . $msg['id'] . '"></div>
															<iframe class="iframe" name="iframe-join-file-' . $msg['id'] . '" src="../customers/join-file.php?msg=' . $msg['id'] . '"></iframe>
														</div>
														<div class="clear"></div>
														<input type="button" name="cancel-rep" value="' . _('Annuler') . '" onclick="hide_form_rep(\'' . $msg['id'] . '\')" />
														<input class="submit-rep" value="' . _('Envoyer') . '" type="button" name="submit-rep" onclick="return sendReponse(' . $msg['id'] . ');" />
														<div class="clear"></div>
													</form>-----
												</div>
												<div class="clear"></div>
											</td>
										</tr>
									';

								$count++;
							}
						}
						?></tbody>
			</table>
		<?php } else { ?>
			<?php
			$review = ria_mysql_fetch_array(prd_reviews_get($_GET['rvw']));
			?>
			<input type="hidden" name="rvw" value="<?php print $review['id']; ?>" />
			<!-- Fiche Avis consommateur -->
			<table>
				<caption><?php print _('Avis consommateur') ?></caption>
				<tfoot>
					<tr>
						<td colspan="2">
							<input type="submit" name="save-review" class="btn-main" value="<?php print _('Enregistrer') ?>" />
							<input type="submit" name="cancel-edit-review" value="<?php print _('Annuler') ?>" />
							<input type="submit" name="delete-review" value="<?php print _('Supprimer') ?>" />
						</td>
					</tr>
				</tfoot>
				<tbody>
					<tr>
						<td id="td-avis-conso-1"><label for="author"><?php print _('Auteur :'); ?></label></td>
						<td id="td-avis-conso-2"><span id="author"><a href="../customers/edit.php?usr=<?php print $review['usr_id']; ?>"><?php print htmlspecialchars($review['usr_firstname'] . ' ' . $review['usr_lastname'] . ' ' . $review['usr_society']); ?></a></span>
						</td>
					</tr>
					<tr>
						<td><label for="date"><?php print _('Date de création :'); ?></label></td>
						<td><span id="date"><?php print ria_date_format($review['date']); ?></span></td>
					</tr>
					<tr>
						<td><label for="name"><?php print _('Titre de la critique :'); ?></label></td>
						<td><input type="text" name="name" id="name" value="<?php print htmlspecialchars($review['name']); ?>" maxlength="75" /></td>
					</tr>
					<tr>
						<td><label for="desc"><?php print _('Critique détaillée :'); ?></label></td>
						<td><textarea name="desc" id="desc" rows="15" cols="40"><?php print htmlspecialchars($review['desc']); ?></textarea></td>
					</tr>
					<?php if ($config['prd_reviews_note']) { ?>
						<tr>
							<td><label for="note"><?php print _('Note attribuée au produit :'); ?></label></td>
							<td>
								<?php
								for ($note = $config['prd_reviews_note_max']; $note > 0; $note -= $config['prd_reviews_note_step']) {
									print '<input type="radio" class="radio" name="note" id="note-' . $note . '" value="' . $note . '" ' . ($review['note'] == $note ? 'checked="checked"' : '') . '/> ';
									print '<label id="lbl-note-' . $note . '" for="note-' . $note . '" title="' . $note . ' / ' . $config['prd_reviews_note_max'] . '"><i>' . $note . ' / ' . $config['prd_reviews_note_max'] . '</i></label><br />';
								}
								?>
							</td>
						</tr>
					<?php } ?>
					<tr>
						<td><label for="publish"><?php print _('Publication :'); ?></label></td>
						<td><input type="checkbox" class="checkbox" name="publish" id="publish" value="1" <?php print $review['publish'] ? 'checked="checked"' : ''; ?> /> <label for="publish"><?php print _('Publier cet avis sur le site') ?></label></td>
					</tr>
				</tbody>
			</table>
			<!-- /Fiche Avis consommateur -->
		<?php } ?>

	<?php } elseif ($tab == 'stats') { ?>
		<?php view_import_highcharts(); ?>

		<div class="stats-menu">
			<div id="riadatepicker"></div>
			<div class="clear"></div>
		</div>

		<script src="/admin/js/riadatepicker.js?12"></script>
		<input type="hidden" name="date1" id="date1" value="<?php print htmlspecialchars($date1); ?>" />
		<input type="hidden" name="date2" id="date2" value="<?php print htmlspecialchars($date2); ?>" />

		<script>
			<!--
			var urlHighcharts = '/admin/catalog/product.php?cat=<?php print $_GET['cat']; ?>&prd=<?php print $_GET['prd']; ?>&tab=stats';
			<?php view_date_initialized(0, '', array(), array('autoload' => true)); ?>
			$(document).ready(function() {
				$('.selector a').mouseup(function() {
					if ($(this).attr('name') != 'perso') {
						setTimeout(function() {
							window.location = urlHighcharts + '&date1=' + $('#date1').val() + '&date2=' + $('#date2').val();
						}, 50);
					}
				});
				$('#btn_submit').mouseup(function() {
					setTimeout(function() {
						window.location = urlHighcharts + '&date1=' + $('#date1').val() + '&date2=' + $('#date2').val();
					}, 50);
				});
			});
			-->
		</script>
		<?php
		require_once('admin/highcharts/graph-orders-and-views.php');
		if (gu_user_is_authorized('_RGH_ADMIN_STATS_ORIGIN')) {
			require_once('admin/highcharts/graph-origins.php');
		}
		?>
	<?php } elseif ($tab == 'ref') { ?>
		<?php print '<h3>'._('Référencement naturel dans les moteurs de recherche').'</h3>'; ?>

		<div class="stats-menu">
			<?php print view_translate_menu('product.php?cat=' . $_GET['cat'] . '&amp;prd=' . $_GET['prd'] . '&amp;tab=ref', $lng, true) ?>
			<?php echo view_product_classement_menu($_GET['prd'], is_numeric($_GET['cat']) ? $_GET['cat'] : 0) ?>
			<div class="clear"></div>
		</div>
		<?php $website = ria_mysql_fetch_array(wst_websites_get($config['wst_id']));
		if (!isset($_GET['cat']) || !is_numeric($_GET['cat']) || $_GET['cat'] <= 0) { ?>
			<div class="notice">
				<?php print _('Aucune classification sélectionné pour le moment. Veuillez sélectionner une des classifications dans le menu "Voir les autres classifications" afin de gérer le référencement du produit') ?>
			</div>

		<?php }


	if (isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat'] > 0) {
		print view_admin_tab_referencement(CLS_PRODUCT, $_GET['prd'], $lng, 'product.php?cat=' . $_GET['cat'] . '&amp;prd=' . $_GET['prd'] . '&amp;tab=ref');

		print '<h3>'._('Redirections 301').'</h3>'
		.'<p class="notice">'
			._('Vous trouverez ci-dessous toutes les redirections 301 en place pour ce produit.')
		.'</p>'

		.'<input type="hidden" name="redirect-prd-id" value="'.$_GET['prd'].'" />'
		.'<table id="tb-redirection" class="checklist">'
			.'<thead class="thead-none">'
				.'<tr>'
					.'<th id="url">'._('Classement du produit').'</th>'
					.'<th id="url">'._('Redirection 301 vers...').'</th>'
					.'<th id="action"></th>'
				.'</tr>'
			.'</thead>'
			.'<tfoot>'
				.'<tr>'
					.'<td colspan="3">'
						.'<input type="button" name="add-url-301" value="Ajouter" />'
					.'</td>'
			.'</tfoot>'
			.'<tbody>'
				.'<tr>'
					.'<td colspan="3">'
						._('Chargement en cours...')
					.'</td>'
				.'</tr>'
			.'</tbody>'
			.'<tfoot>'
				.'<tr>'
					.'<td colspan="3">'
					.'</td>'
				.'</tr>'
			.'</tfoot>'
		.'</table>';

		// URL de redirection dans le cas où le produit ne serait plus disponible sur le site
		// Il peut s'agit d'une indisponibilité temporaire
		$url_unreachable = prd_products_get_url_unreachable( $_GET['prd'] );

		print '<h3>'._('Redirection en cas d’indisponibilité').'</h3>'

		.'<div>'
			.'<p class="notice">'
				._('Vous pouvez renseigner une URL, vers laquelle l’internaute sera redirigé, dans le cas où la fiche du produit n’est plus accessible '
				.'(par exemple dans le cas d’une dé-publication).')
			.'</p>'
			.'<input type="text" name="url_unreachable" placeholder="'._('URL de redirection').'" '
				.'value="'.( $url_unreachable ? htmlspecialchars($url_unreachable) : '' ).'" '
			.'/>'
			.'&nbsp;<input type="submit" name="save-url-unreachable" class="btn-main" value="'._('Enregistrer').'">'
		.'</div>'
	?>

			<h3><?php print _('Recherches internes affichant ce produit') ?></h3>
			<?php
			$ar_website = wst_websites_get_array('name');

			$colspan = 7;
			if (count($ar_website) > 1) {
				$colspan = 8;
			}
			?>
			<table class="checklist" id="table-recherches-internes">
				<thead class="thead-none">
					<tr>
						<th id="search-str" title="<?php print _('Recherches effectuées') ?>"><?php print _('Recherche') ?></th>
						<th id="pos" class="align-center" title="<?php print _('Position du produit dans les résultats de la recherche') ?>"><?php print _('Position') ?></th>
						<th id="clics" class="align-right" title="<?php print _('Nombre de consultation du produit dans ce résultat') ?>"><?php print _('Clics') ?></th>
						<th id="volume" class="align-right" title="<?php print _('Affichage de ce produit dans un résultat de recherche') ?>"><?php print _('Impressions') ?></th>
						<th id="ctr" class="align-right" title="<?php print _('Taux de clics pour 100 impressions') ?>"><?php print _('CTR') ?></th>
						<?php print count($ar_website) > 1 ? '<th id="website" title="' . _('Sites sur lesquels les recherches sont effectuées') . '">' . _('Site web') . '</th>' : '' ?>
						<th id="section" title="<?php print _('Sections utilisées pour cette recherche') ?>"><?php print _('Sections') ?></th>
						<th id="types" title="<?php print _('Filtres utilisés pour cette recherche') ?>"><?php print _('Filtre') ?></th>
					</tr>
				</thead>
				<tbody id="lst_search">
					<tr>
						<td colspan="<?php print count($ar_website) > 1 ? '8' : '7' ?>" class="valign-center">
							<img src="/admin/images/loader2.gif" width="15" height="15" class="loader" title="<?php print _('Chargement en cours, veuillez patienter.') ?>" style="margin:0; padding: 0;" />
							<span><?php print _('Chargement en cours, veuillez patienter.') ?></span>
						</td>
					</tr>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="<?php print count($ar_website) > 1 ? '8' : '7' ?>"></td>
					</tr>
				</tfoot>
			</table>
		<?php
	}
} elseif ($tab == 'comparators' || $tab == 'marketplace') {
	include( dirname(__FILE__).'/../views/catalog/product/tabs/marketplace.php');
} elseif ($tab == 'rewards') {
	$profiles = gu_profiles_get(false, false, false, false, null, false, false);
	?>
		<div class="notice">
			<?php print _('À partir d\'ici, vous avez la possibilité de définir un nombre de points de fidélité qui seront gagnés lors de la commande de ce produit. Le nombre de points de fidélité pourra dépendre du profil du client.'); ?><br />
			<?php print _('Si aucun nombre de points n\'est défini alors les points gagnés lors de la commande de ce produit seront calculés par rapport au ratio défini dans la configuration des <a target="_blank" href="/admin/tools/rewards/index.php">Points de Fidélité</a>.') ?>
		</div>

		<?php
		// Charge les règles d'exception des points de fidélité pour ce produit
		$res_rwd = rwd_prd_rewards_get($_GET['prd']);
		if ($res_rwd && ria_mysql_num_rows($res_rwd)) {
			?>
			<table id="rwp-rewards" class="rwp-rewards">
				<caption><?php print _('Règles de fidélité par profil utilisateur'); ?></caption>
				<thead>
					<tr>
						<th id="inc-check" class="col1"><input class="checkbox" onclick="checkAllClick(this)" name="checkall" type="checkbox" /></th>
						<th id="inc-produit" colspan="2" class="col2"><?php print _('Profil'); ?></th>
						<th id="inc-date-debut" colspan="2" class="col2"><?php print _('Date de début'); ?></th>
						<th id="inc-date-fin" colspan="2" class="col2"><?php print _('Date de fin'); ?></th>
						<th id="inc-point" colspan="2" class="col2"><?php print _('Valeurs'); ?></th>
					</tr>
				</thead>
				<tbody>
					<?php
					if (!$res_rwd || !ria_mysql_num_rows($res_rwd)) {
						print '<tr><td colspan="9">' . _('Aucune offre de fidélité') . '</td></tr>';
					} else {
						while ($rwd = ria_mysql_fetch_array($res_rwd)) {
							print ' <tr class="rwd-elem" id="rwd-' . $rwd['id'] . '">
												<td headers="inc-check"><input name="del[]" class="checkbox" type="checkbox" id="r-' . $rwd['id'] . '" value="' . $rwd['id'] . '"></td>
												<td colspan="2">
													<label>' . gu_profiles_get_name($rwd['prf_id']) . '</label>
												</td>
												<td colspan="2">
													<label>' . ($rwd['date_start'] == "0000-00-00 00:00:00" ? _("immédiatement") : dateheureunparse($rwd['date_start'])) . '</label>
												</td>
												<td colspan="2">
													<label>' . ($rwd['date_end'] == "0000-00-00 00:00:00" ? _("pas de fin") : dateheureunparse($rwd['date_end'])) . '</label>
												</td>
												<td colspan="2">
													<label>' . $rwd['points'] . '</label>
												</td>
											</tr>
									';
						}
					}
					?>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="9">
							<input onclick="delProductRewards();" type="button" value="<?php print _('Supprimer'); ?>" name="rwp-del-reward" class="button" />
						</td>
					</tr>
				</tfoot>
			</table>
		<?php } ?>

		<table class="checklist" id="table-ajout-regle">
			<caption><?php print _('Ajouter une règle') ?></caption>
			<tbody>
				<tr>
					<td id="td-ajout-regle-1">
						<label for="rwd-name"><span class="mandatory">*</span> <?php print _('Profil :'); ?></label>
					</td>
					<td id="td-ajout-regle-2">
						<select name="prf-id">
							<?php
							if ($profiles && ria_mysql_num_rows($profiles)) {
								while ($prf = ria_mysql_fetch_assoc($profiles)) {
									?>
									<option value="<?php echo $prf['id'] ?>"><?php echo htmlspecialchars($prf['name']); ?></option>
								<?php
							}
						}
						?>
						</select>
					</td>
				</tr>
				<tr>
					<td>
						<label for="rwd-pts"><?php print _('Date de début :') ?></label>
					</td>
					<td>
						<input type="text" class="datepicker date" name="date_start" id="rwd-date-start" value="" autocomplete="off" />
						<label for="hour_from_start"><?php print _('à') ?></label>
						<input type="text" class="hour" name="hour_date_start" id="hour_date_start" value="" maxlength="5" autocomplete="off" />
					</td>
				</tr>
				<tr>
					<td>
						<label for="rwd-limit"><?php print _('Date de fin :') ?></label>
					</td>
					<td>
						<input type="text" class="datepicker date" name="date_end" id="rwd-date-end" value="" autocomplete="off" />
						<label for="hour_from_end"><?php print _('à') ?></label>
						<input type="text" class="hour" name="hour_date_end" id="hour_date_end" value="" maxlength="5" autocomplete="off" />
					</td>
				</tr>
				<tr>
					<td header="params">
						<label for="pts"><span class="mandatory">*</span> <?php print _('Valeur en points :') ?></label>
					</td>
					<td>
						<input class="ratio number" type="text" name="pts" id="pts" value="" />
						<label for="pts"><?php print _('point(s)') ?></label>
					</td>
				</tr>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="2">
						<input type="submit" name="save-rwd-prd" id="save-rwd-prd" value="<?php print _('Ajouter'); ?>" />
					</td>
				</tr>
			</tfoot>
		</table>
	<?php } ?>
</div>

<?php if (!in_array($tab, array('linked', 'nomenclature', 'reviews', 'comparators', 'marketplace'))) { ?>
	</form>
<?php } ?>

<script>
	<!--
	$(document).ready(function() {
		$('.check-all').click(function() {
			$(this).parents('tr').find('input[type=checkbox]').attr('checked', 'checked');
			return false;
		});

		$('.uncheck-all').click(function() {
			$(this).parents('tr').find('input[type=checkbox]').removeAttr('checked');
			return false;
		});
	});
	//-->
	<!--
	// Permet de disable tous les champs / boutons si on accède à cette page en lecture seul
	<?php if (!gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_EDIT') && !isset($_GET['prdname']) || (!gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRICE_PRODUCTS') && $tab == 'prices')) { ?>
		$('#tabpanel').find('input, select, textarea').attr('disabled', 'disabled');
		$('table a.edit').remove();
		$('table li.edit').remove();
		$('.edit-url').attr('onclick', '').unbind('click');
		$('input[onclick]').attr('onclick', '').unbind('click');
	<?php } ?>
	<?php if (!gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_PRD') && $tab == 'images') { ?>
		$(document).ready(function() {
			$('#table-images').find('input, select, textarea').attr('disabled', 'disabled');
		});
	<?php } ?>


	$(document).ready(function() {

		if (typeof(Shadowbox) != 'undefined') {
			Shadowbox.init();
		}

		$('#nomenclature_ref').autocomplete({
			source: "ajax-search-nomenclature.php",
			minLength: 3,
			delay: 800
		});

		if ($('#tag_desc').length) {
			$('#tag_desc').riametas({
				padding: true,
				type: "desc"
			});
			$('#tag_title').riametas({
				padding: true,
				type: "title"
			});
			$('#tag_title_ref').riametas({
				type: "title"
			});
			$('#tag_desc_ref').riametas({
				type: "desc"
			});
		}

	});

	function canonical_choose(frm) {
		var args = "";
		if (frm.elements['alt-prd-id'].value) {
			args += '&prd-id=' + frm.elements['alt-prd-id'].value;
		}
		args += '&input_id_prd_id=alt-prd-id';
		args += '&input_id_name=alt-prd-name';
		return displayPopup( '<?php print _('Sélectionner un produit') ?>', '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0&is_canonical=1' + args);
	}

	function cat_choose(frm) {
		var url = 'popup-categories.php';
		if (frm.elements['alt-cat-id'].value) {
			url += '?cat=' + frm.elements['alt-cat-id'].value;
			url += '&parent=' + frm.elements['alt-cat-parent-id'].value;
		}
		displayPopup("<?php print _('Choisir une catégorie') ?>", '', url);
		return false;
	}

	function updateCat(id, idParent, catname) {
		$("#alt-cat-id").val(id);
		$("#alt-cat-parent-id").val(idParent);
		$("#alt-cat-name").val(catname);
		hidePopup();
	}

	function close_cat_popup() {
		hidePopup();
	}

	function dup_alert(ref) {
		return confirm("<?php print _('Etes-vous sur(e) de vouloir continuer ?') . '\n' . _('Cela va écraser les données présentes sur la référence') ?> " + ref);
	}
	<?php if ($tab == 'linked') { ?>
		var passe = false;
		var inp = '';

		function search_references(input, end) {

			if ($('#' + input).val() != '') {
				if (inp != input) {
					$(".ac_results").remove();
				}
				if (!passe || inp != input) {
					$('#' + input).autocomplete({
						source: '/admin/promotions/products/ajax-search-prd.php',
						minChars: 2,
						delay: 200
					});
					passe = true;
					inp = input;
					if (end) {
						window.scroll(0, 10000);
					}
				}
			}

			if (end) {
				window.scroll(0, 10000);
			}

			return false;
		}
	<?php } ?>
	<?php
	if ($tab == 'prices') {
		// Récupère les conditions possibles
		$r_fld = fld_fields_get(0, 0, 0, 0, 0, 0, null, array("prc_priority"), true, array(), null, null, true, true);

		$fld_opt = '<option value="-1">' . _('Choisissez un champ personnalisé') . '</option>';
		// Construit les option des select pour l'ajout d'une condition, le selecte sera construit lors de l'appel pour avoir l'identifiant du tarifs
		if ($r_fld !== false && ria_mysql_num_rows($r_fld) > 0) {
			while ($fld = ria_mysql_fetch_array($r_fld))
				$fld_opt .= '<option value="' . $fld['id'] . '">' . addslashes($fld['name']) . ' (' . addslashes($fld['cls_name']) . ')</option>';

			// Retourne au premier index dans les tableaux MySQL
			ria_mysql_data_seek($r_fld, 0);
		}
		?>

		//Call this function when "#prd-tva" changes
		function onPrdTvaChange() {
			updTVA();
			if ($("#prd-tva").val() == '-1'){
				$("#info-not-tva").removeClass('none')
			}else{
				$("#info-not-tva").addClass('none');
			}
		};

		var reloadPricesTab = "";
		// Gestion du sélectionneur de période
		$(document).ready(function() {
			reloadPricesTab = function(e, ui) {
				$('.prc-tva-eco tbody').css("opacity", 0.2);

				var prd_id = $('#all-price-prd').val();
				var periode = $(this).attr('name');
				periode = periode.substring(periode.indexOf('-') + 1, periode.length);
				var periode_label = $(this).html();
				periode_label = strcut(periode_label, 30);

				var url = "/admin/ajax/catalog/ajax-product-price-table-get.php";

				//  On doit fermer le menu déroulant et mettre à jour la valeur sélectionnée
				$('#riapriceperiod .selector').css('display', "none");
				$('#riapriceperiod .selectorview .view').html(periode_label);

				$.ajax({
					url: url,
					data: 'prd_id=' + prd_id + '&periode=' + periode,
					type: 'post',
					async: true,
					success: function(data) {
						$('.prc-tva-eco').html(data);
						$('.prc-tva-eco tbody').css('transition', "opacity 0.5s");
						$('.prc-tva-eco tbody').css("opacity", 1);
					}
				});
			};

			$("#riapriceperiod .selector a").click(reloadPricesTab);

			if ($("#prd-tva").val() == '1'){
				$("#info-not-tva").removeClass('none');
			}

			$("#prd-tva").change(onPrdTvaChange);
			$("#prd-tva").keydown(onPrdTvaChange);
			$("#prd-tva").keyup(onPrdTvaChange);

		});
		// Ajout d'une condition
		nb_new = 0;

		function addCdt(prc, newCdt, tvaB, ecotaxe) {
			tva = tvaB ? '-tva' : '';
			if (newCdt) {
				$("#add-cdt-0").parent().find('>br').before('<select class="fld" name="cdt-new[' + nb_new + ']" id="cdt-new-' + nb_new + '" onchange="cdtForm(' + prc + ', ' + nb_new + ', true, ' + (tvaB ? 'true' : 'false') + ', false, true);"><?php print $fld_opt; ?></select><img class="del-cdt" src="/admin/images/del.svg" alt="<?php print _('Supprimer') ?>" title="<?php print _('Supprimer cette condition') ?>" name="del" id="del-cdt-new-' + nb_new + '" onclick="delCdt(' + (tvaB ? 'false' : 'true') + ', 0, ' + nb_new + ', true, true, false, 0);" /><div class="conditions-next" id="condition-new-' + nb_new + '"></div>');
				nb_new++;
			} else {
				var nb = $("#nb-cdt" + tva + "-" + prc).val();
				$("#add-cdt" + tva + "-" + prc).parent().find('>br').before('<select class="fld" name="cdt' + tva + '-' + prc + '[' + nb + ']" id="cdt' + tva + '-' + prc + '-' + nb + '" onchange="cdtForm(' + prc + ', ' + nb + ', false, ' + (tvaB ? 'true' : 'false') + ', false, true);"><?php print $fld_opt; ?></select><img class="del-cdt" src="/admin/images/del.svg" alt="<?php print _('Supprimer') ?>" title="<?php print _('Supprimer cette condition') ?>" name="del" id="del-cdt' + tva + '-' + prc + '-' + nb + '" onclick="delCdt(' + (tvaB ? 'false' : 'true') + ', ' + prc + ', ' + nb + ', false, true, false, ' + nb + ');" /><div class="conditions-next" id="condition' + tva + '-' + prc + '-' + nb + '"></div>');
				nb++;
				$("#nb-cdt" + tva + "-" + prc).val(nb);
			}
		}

		function savePurchaseAvg() {
			// Retire les messages d'erreur et de succès actuellement affichés
			removeMessages();
			// Enregistre les modifications
			$.ajax({
				type: 'POST',
				url: '/admin/catalog/ajax-new-price-tva.php',
				data: 'savePrdPurchaseAvg=1&purchase_avg=' + $("#purchase-avg").val() + '&prd=' + <?php print $_GET['prd']; ?>,
				dataType: 'xml',
				success: function(xml) {
					if ($(xml).find('result').attr('type') == '1') {
						$("#pamp").before("<div class=\"error-success\"><?php print _('L\'enregistrement du prix d\'achat s\'est correctement déroulé.') ?></div>");
					} else {
						$("#pamp").before("<div class=\"error\">" + $(xml).find('error').text() + "</div>");
					}
				}
			});
		}

		function saveEco() {
			// Retire les messages d'erreur et de succès actuellement affichés
			removeMessages();
			// Enregistre les modifications
			$.ajax({
				type: 'POST',
				url: '/admin/catalog/ajax-new-price-tva.php',
				data: 'savePrdEco=1&eco=' + $("#prd-ecotaxe").val() + '&prd=' + <?php print $_GET['prd']; ?>,
				dataType: 'xml',
				success: function(xml) {
					if ($(xml).find('result').attr('type') == '1') {
						$("#eco-taxe").before("<div class=\"error-success\"><?php print _('L\'enregistrement de l\'écotaxe, pour ce produit, s\'est correctement déroulé.') ?></div>");
					} else {
						$("#eco-taxe").before("<div class=\"error\">" + $(xml).find('error').text() + "</div>");
					}
				}
			});
		}

		function saveTva(id) {
			// Retire les messages d'erreur et de succès actuellement affichés
			removeMessages();
			// Enregistre les modifications
			$.ajax({
				type: 'POST',
				url: 'ajax-new-price-tva.php',
				data: 'savePrdTva=1&tva=' + $("#prd-tva").val() + '&idTva=' + id + '&prd=' + <?php print $_GET['prd']; ?>,
				dataType: 'xml',
				success: function(xml) {
					if ($(xml).find('result').attr('type') == '1') {
						id = $(xml).find('result').attr('idTva');
						$("#save-tva").replaceWith('<input type="button" name="save-tva" id="save-tva" value="<?php print _('Enregistrer') ?>" onclick="javasript:saveTva(' + id + ');"/>');
						$("#eco-taxe").before("<div class=\"error-success\"><?php print _('L\'enregistrement de la TVA, pour ce produit, s\'est correctement déroulé.') ?></div>");

						//If tva updated, we need to update al prices using the new tva. call with ajax to simulate save-prices button
						var productForm = document.getElementById('product');
						var data = new FormData(productForm);
						data.append('save-prices', 'true');

						$.ajax({
							type: 'POST',
							url: 'product.php'+location.search,
							data: data,
							processData: false,
							contentType: false,
							success: function(json) {
								location.reload();
							}
						});

					} else {
						$("#eco-taxe").before("<div class=\"error\">" + $(xml).find('error').text() + "</div>");
					}
				}
			});
		}
	<?php }
	if ($tab == 'general') { ?>
		$(document).ready(function() {
			$('input[type=radio][name=cly_canonical]').change(function() {

				$.post('/admin/ajax/catalog/ajax-set-canonical.php', 'cat=' + $('input[type=radio][name=cly_canonical]:checked').val() + '&prd=<?php print $_GET['prd'] ?>', function(json) {
					if (!json.result) alert("<?php print _('Une erreur est survenue lors de l\'enregistrement de l\'URL canonique') ?>");
				}, 'json');
			});
		});
	<?php } ?>

	function prd_nomenclature_change() {
		<?php
		if (prd_products_is_nomenclature($_GET['prd'])) {
			// todo : faire passer les identifiants entrainant un warning dans un tableau JS (en utilisant à la base les define PHP)
			?>
			var mytype = <?php print prd_products_get_nomenclature_type($_GET['prd']); ?>;
			if (mytype != $('#prd-type').val() && ($('#prd-type').val() == 1 || $('#prd-type').val() == 2 || $('#prd-type').val() == 5)) {
				return confirm('<?php print _('Attention le changement de type du produit entrainera la perte des produits nomenclaturés.') ?>');
			}
		<?php
	}
	?>
	}

	//variable necessaire pour les contrôles
	var brdName = "";
	var optId = 0;

	//permet de recharger la liste des marques
	function loadBrands() {
		$.ajax({
			url: '../ajax/catalog/ajax-brands-get.php',
			method: 'GET',
			dataType: 'json',
			success: function( brands ) {
				$('#brandName').empty();
				for( const brand of brands ){
					$('#brandName').append( '<option value="' + brand['title'] + '" id="' + brand['id'] + '">' );
				}
			}, error: function(error) {
				new window.FlashMessage( 'Une erreur inattendue s\'est produite lors de la mise à jour de la liste des marques. Veuillez recharger la page.', 'error' );
			}
		});
	}


	//compare la valeur indiquer dans la zone de texte avec les marques déjà existante pour savoir si on peut ajouter la marque
	$('#brandSelect').keyup(function() {

		for (let index = 0; index < $('#brandSelect').next().children().length; index++) {
			var $option = $('#brandSelect').next().find(  $('#brandSelect').next().children()[index]  );

			if ( $option.val().toLowerCase() == $('#brandSelect').val().toLowerCase() ) {
				optId = $option.attr('id');
				$('#brand').val(optId);
				$('#btn-brd-add').prop('disabled', true);
				return;
			} else {
				optId = 0;
			}
		}

		if ( $('#brandSelect').val() != "" && isNaN( $('#brandSelect').val() ) && optId == 0 ) {
			$('#btn-brd-add').prop('disabled', false);
		}

		if ( $('#brandSelect').val() == "" ) {
			$('#btn-brd-add').prop('disabled', true);
		}
	})

	//requete ajax pour ajouter la marque
	$('#btn-brd-add').click(function() {
		if ( $('#brandSelect').val() != "" && isNaN( $('#brandSelect').val() ) && optId == 0 ) {
			$('#btn-brd-add').prop('disabled', true);
			$.ajax({
				url: '../ajax/catalog/ajax-brands-add.php',
				method: 'POST',
				data: 'name=' + $('#brandSelect').val(),
				dataType: 'json',
				success: function( brand ) {
					optId = brand;
					loadBrands();
					new window.FlashMessage( "<?php print _('Ajout de la marque réalisé avec succès.'); ?>", 'success' );
				}, error: function(error) {
					new window.FlashMessage( "<?php print _('Une erreur inattendue s\'est produite lors de l\'ajout de la marque. Veuillez réessayer.'); ?>", 'error' );
					$('#btn-brd-add').prop('disabled', false);
				}
			});
		} else {
			new window.FlashMessage( "<?php print _('L\'ajout de la marque à échoué soit car le champs est vide, soit parce que la marque existe déjà. Veuillez réessayer.'); ?>" );
			$('#btn-brd-add').prop('disabled', false);
		}
	})

	//prend en compte le texte déjà présent dans marque si renseigner
	$(document).ready(function() {
		$('#brandSelect').keyup();
	});

	//Envoi de l'id de la marque dans un champ cacher (problème d'affichage)
	$('input[type="submit"].btn-main').mousedown(function() {
		if ( $('brandSelect').val() != brdName && !$('#btn-brd-add').attr('disabled') && $('#brandSelect').val() != "" && isNaN( $('#brandSelect').val() ) && optId == 0 ) {
			brdName = $('brandSelect').val();
			$('#btn-brd-add').click();
		}
		$('#brand').val(optId);
	})

	//permet d'ajouter une marque si possible quand on est dans le champ de marque au lieyu d'envoyer le form entier.
	$('#brandSelect').keydown(function(e) {
		if ( e.which == 13 && $('brandSelect').val() != brdName && !$('#btn-brd-add').attr('disabled') && $('#brandSelect').val() != "" && isNaN( $('#brandSelect').val() ) && optId == 0 ) {
			brdName = $('brandSelect').val();
			e.preventDefault();
			$('#btn-brd-add').click();
		}
	})

	//$('#product').children().first().children().find($('li')).children().click(function(e){	})

	-->
</script>

<?php
require_once('admin/skin/footer.inc.php');
?>