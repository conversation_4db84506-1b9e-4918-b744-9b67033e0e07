<?php

	/**	\file index.php
	 *	Cette page affiche et permet la gestion des tarifs d'exception
	 */

	require_once('products.inc.php');
	require_once('prices.inc.php');
	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRICE');

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Tarification catalogue') . ' - ' . _('Catalogue'));
	require_once('admin/skin/header.inc.php');

?>
	<form id="prc-cat" action="index.php" method="post">
		<h2><?php print _('Tarification catalogue')?></h2>
		<div class="stats-menu">
		<?php $periode = isset($_GET['periode']) && ( $_GET['periode']>=0 && $_GET['periode']<8 ) ? $_GET['periode'] : false;
			if($periode === false){
				$periode = (session_get_periodpicker_state()) !== false ? session_get_periodpicker_state() : 6;
			}
			else{
				session_set_periodpicker_state($periode);
			}
			view_state_periodpicker($periode, "riawebsitepicker", 7, _("Période pour les tarifs"));
		 ?>

			<div class="clear"></div>
		</div>
		<?php
			// Récupère les tarifs pour un produit donné
			$r_prices = prc_prices_get( 0, 0, false, false, false, null, false,null, false, null, null, 1, $periode);
			$per_page = 25;
			$prices_count = $r_prices ? ria_mysql_num_rows($r_prices) : 0;
			$pages = ceil($prices_count / $per_page);

			if( !isset($_GET['page']) )
				$page = 1;
			else{
				if( !is_numeric($_GET['page']) || $_GET['page']<0 )
					$page = 1;
				elseif( $_GET['page']>$pages )
					$page = $pages;
				else
					$page = $_GET['page'];
			}
		?>
		<input type="hidden" name="id-price" id="id-price" value="" />
		<input type="hidden" name="id-cdt-del" id="id-cdt-del" value="" />
		<input type="hidden" name="id-price-del" id="id-price-del" value="" />
		<table class="prc-tva-eco checklist" id="lst-prices">
			<caption><?php print _('Tarifs conditionnels pour le catalogue');?></caption>
			<thead class="thead-none">
				<tr>
					<th id="sync"></th>
					<th id="information"><?php print _('Informations');?></th>
					<th id="conditions-prc"><?php print _('Conditions');?></th>
					<th id="action-prc"></th>
				</tr>
			</thead>
			<tfoot>
				<tr>
					<td colspan="4" class="align-right">
						<input type="button" name="prices-add" id="prices-add" value="<?php print _('Nouveau tarif conditionnel');?>" onclick="newForm('price', 'prc-cat', '0', '0');" class="btn-main" />
					</td>
				</tr>
				<?php if( $pages>1 ){ ?>
				<tr>
					<td colspan="2" class="align-left"><?php
						if( $pages==0 )
							$pages = 1;
						print $page.'/'.$pages;
					?></td>
					<td colspan="2" class="align-right"><?php
						$links = array();
						if( $page>1 ){
							$links[] = '<a href="index.php?period='.$periode.'&amp;page='.($page-1).'">&laquo; '._('Page précédente').'</a>';
						}
						for( $i=$page-5; $i<$page+5; $i++ ){
							if( $i>=1 && $i<=$pages ){
								if( $i==$page ){
									$links[] = '<b>'.$i.'</b>';
								}else{
									$links[] = '<a href="index.php?period='.$periode.'&amp;page='.$i.'">'.$i.'</a>';
								}
							}
						}
						if( $page<$pages ){
							$links[] = '<a href="index.php?period='.$periode.'&amp;page='.($page+1).'">'._('Page suivante').' &raquo;</a>';
						}

						print implode(' | ',$links);
					?></td>
				</tr>
				<?php } ?>
			</tfoot>
			<tbody>
				<?php
					print prc_prices_list_view(0, 0, $periode,0,false,0, $page, $per_page);
				?>
				<tr id="price-new" style="display: none"><td colspan="4"></td></tr>
			</tbody>
		</table>
		<span class="info-fld"><?php print _('* : ce champ est obligatoire. Pour un nouveau tarif, la valeur doit être supérieure à 0, pour une remise en pourcentage, la valeur doit être inférieure ou égale à 100.');?></span>
		<br />
		<span class="info-fld"><?php print _('** : en décochant cette option, la remise ne sera pas prise en compte s\'il existe des tarifs de priorité supérieure.');?> <a href="/admin/config/prices/index.php" target="_bank"><?php print _('Plus d\'informations sur les priorités');?></a></span>
		<h3><?php print _('Conditions pour l\'exonération de TVA'); ?> </h3>
		<table id="grps-tva" class="prc-tva-eco checklist" cellpadding="0" cellspacing="0">
			<thead class="thead-none">
				<tr>
					<th id="th-grps-tva-1">&nbsp;</th>
					<th id="th-grps-tva-2">&nbsp;</th>
				</tr>
			</thead>
			<tfoot>
				<tr>
					<td colspan="2" class="align-right">
						<input type="button" name="add-grp-tva" id="add-grp-tva" value="<?php print _('Nouvelle condition d\'exonération')?>" onclick="newForm('tva', 'prc-cat', 0, 0);" title="<?php print _('Permet de créer une nouvelle liste de conditions pour l\'exonération de la TVA');?>" class="btn-main" />
					</td>
				</tr>
			</tfoot>
			<tbody>
				<?php
					$rgrp_tva = prc_tva_exempt_groups_get();

					// Aucune liste de conditions
					print '	<tr>';
					print '		<td colspan="2" id="none-grp-tva" '.( $rgrp_tva==false || ria_mysql_num_rows($rgrp_tva)==0 ? '' : 'class="none"' ).'>'._('Aucune liste de conditions enregistrée.').'</td>';
					print '	</tr>';

					if( $rgrp_tva!==false ){
						// Nombre de groupe de conditions
						$grp_count = ria_mysql_num_rows($rgrp_tva);

						$count = 0;
						// Affichage des groupes de conditions
						while( $grp_tva = ria_mysql_fetch_array($rgrp_tva) ){
							// Affichage du séparateurs OU entre les groupes
							if( $count>0 && $grp_count>1 )
								print '<tr id="orTva-'.$grp_tva['id'].'"><td class="or-cdt" colspan="2">OU</td></tr>';

							print '	<tr id="grpTva-'.$grp_tva['id'].'">';
							print '		<td class="conditions" id="cdts-tva-'.$grp_tva['id'].'">';
								$cdt = prc_conditions_view( $grp_tva['id'], TERMS_TVA );
								if( $cdt!==false )
									print $cdt;
							print '		</td>';
							print '		<td class="action">';
							print '			<input class="action btn-main" type="button" name="grp-tva-save" id="grp-tva-save-'.$grp_tva['id'].'" value="'._('Enregistrer').'" onclick="tvaSubmit(\'prc-cat\', '.$grp_tva['id'].');" /><br />';
							print '			<div class="save-load" id="save-load-tva-'.$grp_tva['id'].'">';
							print ' 			<img src="/admin/images/loader2.gif" alt="" />';
							print '			</div>';
							print '			<a id="grp-tva-del-'.$grp_tva['id'].'" class="button btn-cancel" onclick="delTva('.$grp_tva['id'].');">'._('Supprimer').'</a>';
							print '		</td>';
							print '	</tr>';
							$count++;
						}
					}
				?>
				<tr id="grps-tva-new" style="display: none"><td colspan="2"></td></tr>
			</tbody>
		</table>
	</form>

<script><?php
	// Récupère les conditions possibles
	$r_fld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array("prc_priority"), true, array(), null, null, true, true );

	// Construit les option des select pour l'ajout d'une condition, le select sera construit lors de l'appel pour avoir l'identifiant du tarifs
	$fld_opt_prd = '';
	if( $r_fld!==false && ria_mysql_num_rows($r_fld)>0 ){
		$fld_opt_prd = '<optgroup label="'._('Choisissez un champ personnalisé').'">';
		while( $fld = ria_mysql_fetch_array($r_fld) ){
			$fld_opt_prd .= '<option value="'.$fld['id'].'">'.addslashes($fld['name']).' ('.addslashes($fld['cls_name']).')</option>';
		}
		$fld_opt_prd .= '</optgroup>';
	}

	// Récupère les conditions possibles pour les conditions d'exonération de TVA
	$r_fld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array('prc_priority'), true, array(), null, null, false, false, true );

	// Construit les options des selects pour l'ajout d'une condition, le select sera construit lors de l'appel pour avoir l'identifiant du tarif
	$fld_opt_tva = '';
	$fld_opt_tva .=
		'<optgroup label="'._('Choisissez un champ natif').'">'.
			'<option value="456">'._('Catégorie tarifaire du client').'</option>'.
			'<option value="513">'._('Catégorie comptable du client').'</option>'.
		'</optgroup>';
	if( $r_fld!==false && ria_mysql_num_rows($r_fld)>0 ){
		$fld_opt_tva = '<optgroup label="'._('Choisissez un champ personnalisé').'">';
		while( $fld = ria_mysql_fetch_array($r_fld) ){
			$fld_opt_tva .= '<option value="'.$fld['id'].'">'.addslashes($fld['name']).' ('.addslashes($fld['cls_name']).')</option>';
		}
		$fld_opt_tva .= '</optgroup>';
	}
?>
// Gestion du sélectionneur de période
$(document).ready(function(){
	$('#riawebsitepicker .selectorview').click(function(){
		if($('#riawebsitepicker .selector').css('display')=='none'){
			$('#riawebsitepicker .selector').show();
		}else{
			$('#riawebsitepicker .selector').hide();
		}
	});
	$("#riawebsitepicker .selector a").click(function(){
		periode = $(this).attr('name');
		p = periode.substring(periode.indexOf('-')+1, periode.length);
		window.location.href = 'index.php?periode='+p;
	});
});

// Ajout d'une condition
var nb_new = 0;
var fld_opt_prd = '<?php print $fld_opt_prd; ?>';
var fld_opt_tva = '<?php print $fld_opt_tva; ?>';
function addCdt( id, newCdt, tva, ecotaxe ){
	if( newCdt ){
		$("#add-cdt-0").before(
			'<select class="fld" name="cdt-new['+nb_new+']" id="cdt-new-'+nb_new+'" onchange="cdtForm('+id+', '+nb_new+', true, '+( tva ? 'true' : 'false' )+', '+( ecotaxe ? 'true' : 'false' )+', true);">'+( tva ? fld_opt_tva : fld_opt_prd )+'</select><img class="del-cdt" src="/admin/images/del.svg" alt="<?php print _('Supprimer');?>" title="<?php print _('Supprimer cette condition');?>" name="del" id="del-cdt-new-'+nb_new+'" onclick="delCdt('+( tva ? 'false' : 'true' )+', 0, '+nb_new+', true, true, '+( ecotaxe ? 'true' : 'false' )+', 0);" /><div class="conditions-next" id="condition-new-'+nb_new+'"></div>');
		$('#cdt-new-'+nb_new).change();
		nb_new++;

	} else {
		idNb = '';
		if( tva ) idNb = idNb+'-tva';
		if( ecotaxe ) idNb = idNb+'-eco';

		var nb = $("#nb-cdt"+idNb+"-"+id).val();
		$("#add-cdt"+idNb+"-"+id).parent().find('>br').before('<select class="fld" name="cdt'+idNb+'-'+id+'['+nb+']" id="cdt'+idNb+'-'+id+'-'+nb+'" onchange="cdtForm('+id+', '+nb+', false, '+( tva ? 'true' : 'false' )+', '+( ecotaxe ? 'true' : 'false' )+', true);">'+( tva ? fld_opt_tva : fld_opt_prd )+'</select><img class="del-cdt" src="/admin/images/del.svg" alt="<?php print _('Supprimer');?>" title="<?php print _('Supprimer cette condition');?>" name="del" id="del-cdt'+idNb+'-'+id+'-'+nb+'" onclick="delCdt('+( tva ? 'false' : 'true' )+', '+id+', '+nb+', false, true, '+( ecotaxe ? 'true' : 'false' )+', '+nb+');" /><div class="conditions-next" id="condition'+idNb+'-'+id+'-'+nb+'"></div>');
		nb++;
		$("#nb-cdt"+idNb+"-"+id).val(nb);
	}
}
</script>
<?php
	require_once('admin/skin/footer.inc.php');
