<?php

	/**	\file ajax-new-price-tva.php
	 *	Ce fichier est appelé en ajax par les formulaires permettant de créer un tarif d'exception, une exonération de TVA ou d'écotaxe.
	 *	Les paramètres GET suivants sont nécessaires à son bon fonctionnement :
	 *	- type :
	 *		- price : tarif d'exception
	 *		- tva : exonération de tva
	 *		- eco : écotaxe
	 *	- form : identifiant du formulaire appelant
	 *	- cat : identifiant de la catégorie de produits dans laquelle la règle va s'appliquer, ou 0 si non applicable
	 *	- prd : identifiant du produit sur lequel la règle va s'appliquer, ou 0 si non applicable
	 *
	 * 	D'autres paramètres sont à passer en POST pour réaliser l'action souhaitée. Ils dépendent de l'action à réaliser :
	 * 	- Supprimer un tarif d'exception :
	 * 		- del : bouton de suppression
	 * 		- price : identifiant du tarif d'exception à supprimer
	 * 	- Supprimer une condition d'exonération de TVA :
	 * 		- del : bouton de suppression
	 * 		- tva : identifiant de la règle d'exonération de tva à supprimer
	 * 	- Supprimer une règle d'écotaxe :
	 * 		- del : bouton de suppression
	 * 		- tva : identifiant de la règle d'écotaxe à supprimer
	 */

	require_once( 'view.admin.inc.php' );
	require_once( 'prices.inc.php' );
	require_once( 'strings.inc.php' );

	// Vérifie que l'utilisateur en cours à le droit d'intervenir sur les tarifs
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRICE');

	header("Content-Type: application/xml");
	$error = false;
	$xml = '<?xml version="1.0" encoding="utf-8"?>';

	if( isset($_POST['del'], $_POST['price']) && $_POST['price']>0){

		// Suppression d'un tarif
		if( !prc_prices_del($_POST['price']) ){
			$xml .= '<result type="0"><error>';
			$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de la suppression d\'un tarif.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
			$xml .= '</error>\n</result>';
		} else {
			$xml .= '<result type="1">';
			$xml .= '<cdt-line><![CDATA[';
			$xml .= ']]></cdt-line>';
			$xml .= '</result>';
		}

	} elseif( isset($_POST['del'], $_POST['tva']) && $_POST['tva']>0 ){

		// Suppression d'une liste de condition d'exonération de TVA
		if( !prc_tva_exempt_groups_del($_POST['tva']) ){
			$xml .= '<result type="0"><error>';
			$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de la suppression d\'une liste de condition pour l\'exonération de la TVA.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
			$xml .= '</error>\n</result>';
		} else {
			$xml .= '<result type="1">';
			$xml .= '<cdt-line><![CDATA[';
			$xml .= ']]></cdt-line>';
			$xml .= '</result>';
		}

	} elseif( isset($_POST['del'], $_POST['eco']) ){

		// Suppression d'une liste de conditions d'exonération de l'écotaxe
		if( !prc_ecotaxe_exempt_groups_del($_POST['eco']) ){
			$xml .= '<result type="0"><error>';
			$xml .= "<![CDATA["._('Une erreur inattendue s\'est produite lors de la suppression d\'une liste de condition pour l\'exonération de l\'écotaxe.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.')."]]>";
			$xml .= '</error>\n</result>';
		} else {
			$xml .= '<result type="1">';
			$xml .= '<cdt-line><![CDATA[';
			$xml .= ']]></cdt-line>';
			$xml .= '</result>';
		}

	} elseif( isset($_POST['delCdtPrice'], $_POST['price'], $_POST['fld']) ){

		// Suppression d'une condition sur un tarif
		if( !prc_price_conditions_del($_POST['price'], $_POST['fld']) ){
			$xml .= '<result type="0" idPrc="'.$_POST['price'].'"><error>';
			$xml .= '<![CDATA[>'._('Une erreur inattendue s\'est produite lors de la suppression de la condition.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
			$xml .= '</error>\n</result>';
		} else {
			$xml .= '<result type="1">';
			$xml .= '<cdt-line><![CDATA[';
			$xml .= ']]></cdt-line>';
			$xml .= '</result>';
		}

	} elseif( isset($_POST['delGrpPrice'], $_POST['grp'], $_POST['fld']) ){

		// Suppression d'une condition sur un tarif
		if( !prc_group_conditions_del($_POST['grp'], $_POST['fld']) ){
			$xml .= '<result type="0" idGrp="'.$_POST['grp'].'"><error>';
			$xml .= '<![CDATA[>'._('Une erreur inattendue s\'est produite lors de la suppression de la condition.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
			$xml .= '</error>\n</result>';
		} else {
			$xml .= '<result type="1">';
			$xml .= '<cdt-line><![CDATA[';
			$xml .= ']]></cdt-line>';
			$xml .= '</result>';
		}

	} elseif( isset($_POST['delCdtEco'], $_POST['grp'], $_POST['cdt']) ){

		// Permet de connaître le nombre de condition présentent dans la liste
		$rcdt = prc_ecotaxe_exempt_conditions_get( $_POST['grp'] );
		$nbCdt = false;
		if( $rcdt!==false )
			$nbCdt = ria_mysql_num_rows($rcdt);

		// Suppression d'une condition sur une liste d'exonération de l'écotaxe
		if( !prc_ecotaxe_exempt_conditions_del($_POST['grp'], $_POST['cdt']) ){
			$xml .= '<result type="0"><error>';
			$xml .= "<![CDATA[>"._('Une erreur inattendue s\'est produite lors de la suppression de la condition.')."<br />"._('Veuillez réessayer ou prendre contact avec l\'administrateur.')."]]>";
			$xml .= '</error>\n</result>';
		} else {
			if( $nbCdt==1 ){
				// Si il s'agissait de la dernière condition de la liste, on la supprime
				if( !prc_ecotaxe_exempt_groups_del($_POST['grp']) ){
					$xml .= '<result type="0"><error>';
					$xml .= "<![CDATA["._('Une erreur inattendue s\'est produite lors de la suppression d\'une liste de condition pour l\'exonération de l\'écotaxe.')."<br />"._('Veuillez réessayer ou prendre contact avec l\'administrateur.')."]]>";
					$xml .= '</error>\n</result>';
				} else {
					$xml .= '<result type="1">';
					$xml .= '<cdt-line><![CDATA[';
					$xml .= ']]></cdt-line>';
					$xml .= '</result>';
				}
			} else {
				$xml .= '<result type="1">';
				$xml .= '<cdt-line><![CDATA[';
				$xml .= ']]></cdt-line>';
				$xml .= '</result>';
			}
		}

	} elseif( isset($_POST['delCdtTva'], $_POST['tva'], $_POST['fld']) ){

		// Permet de connaître le nombre de condition présentent dans la liste
		$rcdt = prc_tva_exempt_conditions_get( $_POST['tva'] );
		$nbCdt = false;
		if( $rcdt!==false )
			$nbCdt = ria_mysql_num_rows($rcdt);

		// Suppression d'une condition sur une liste d'exonération de la TVA
		if( !prc_tva_exempt_conditions_del($_POST['tva'], $_POST['fld']) ){
			$xml .= '<result type="0"><error>';
			$xml .= '<![CDATA[>'._('Une erreur inattendue s\'est produite lors de la suppression de la condition.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
			$xml .= '</error>\n</result>';
		} else {
			if( $nbCdt==1 ){
				if( !prc_tva_exempt_groups_del($_POST['tva']) ){
					$xml .= '<result type="0"><error>';
					$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de la suppression d\'une liste de condition pour l\'exonération de la TVA.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
					$xml .= '</error>\n</result>';
				} else {
					$xml .= '<result type="1">';
					$xml .= '<cdt-line><![CDATA[';
					$xml .= ']]></cdt-line>';
					$xml .= '</result>';
				}
			} else {
				$xml .= '<result type="1">';
				$xml .= '<cdt-line><![CDATA[';
				$xml .= ']]></cdt-line>';
				$xml .= '</result>';
			}
		}

	}elseif( isset($_POST['addPrice'], $_POST['formulaire'], $_POST['id-price']) ){

		// Sauvegarde des tarifs
		$id = $_POST['id-price'];
		$params = prc_prices_get_params( $id, array('is-sync') );
		$_POST['val-type-'.$id] = str_replace(array(' ', ','), array('', '.'), $_POST['val-type-'.$id]);

		if( !is_numeric($_POST['val-type-'.$id]) ){
			$xml .= '<result type="0" idPrc="'.$id.'"><error>';
			$xml .= '<![CDATA['._('La valeur saisie est incorrecte, veuillez saisir un nombre.').']]>';
			$xml .= '</error>\n</result>';
			$error = true;
		} elseif( $params['is-sync'] ){
			// Le tarif est synchronisé, on ne peut donc mettre à jour que le nom
			if( !prc_prices_set_name($id, $_POST['desc-'.$id]) ){
				$xml .= '<result type="0"><error>';
				$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de la mise à jour du tarif.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
				$xml .= '</error></result>';
				$error = true;
			} else {
				$xml .= '<result type="1">';
				$xml .= '<cdt-line><![CDATA[';
				$xml .= ']]></cdt-line>';
				$xml .= '</result>';
			}
		} else {

			// On vérifie que le champ valeur qui est obligatoire existe bien et n'est pas vide
			if( isset($_POST['val-type-'.$id]) && $_POST['val-type-'.$id]!=='' ){
				$exist = prc_prices_exists($id);
				$unique = isset($_POST['unique-'.$id]) || $_POST['type-'.$id]==NEW_PRICE ? true : false;
				$qteMin = isset($_POST['qte-min-'.$id]) && is_numeric($_POST['qte-min-'.$id]) ? $_POST['qte-min-'.$id] : 1;
				$qteMax = isset($_POST['qte-max-'.$id]) && is_numeric($_POST['qte-max-'.$id]) ? $_POST['qte-max-'.$id] : false;
				$hourStart = trim($_POST['hour-start-'.$id])!=='' ? $_POST['hour-start-'.$id] : '00:00';
				$hourStop = trim($_POST['hour-stop-'.$id])!=='' ? $_POST['hour-stop-'.$id] : '23:59';
				$dps_id = isset($_POST['dps-'.$id]) && is_numeric($_POST['dps-'.$id]) && $_POST['dps-'.$id] > 0 ? $_POST['dps-'.$id] : 0;

				$delta = strtotime(dateheureparse($_POST['date-end-'.$id].' '.$hourStop)) - strtotime(dateheureparse($_POST['date-start-'.$id].' '.$hourStart));
				if( $qteMax>0 && $qteMin>$qteMax ){
					$xml .= '<result type="0" idPrc="'.$id.'"><error>';
					$xml .= "<![CDATA["._('Une erreur s\'est produite lors de l\'enregistrement du tarif.')."<br />"._('La quantité minimum ne peut être supérieur à la quantité maximum.')."]]>";
					$xml .= '</error>\n</result>';
					$error = true;
				}elseif( $delta<0 ){
					$xml .= '<result type="0" idPrc="'.$id.'"><error>';
					$xml .= "<![CDATA["._('Une erreur s\'est produite lors de l\'enregistrement du tarif.')."<br />"._('La date de fin ne peut être antérieure à la date de début.')."]]>";
					$xml .= '</error>\n</result>';
					$error = true;
				}elseif( $_POST['type-'.$id]==DISCOUNT_PERCENT && $_POST['val-type-'.$id]>100 ){
					$xml .= '<result type="0" idPrc="'.$id.'"><error>';
					$xml .= "<![CDATA["._('Une erreur s\'est produite lors de l\'enregistrement du tarif.')."<br />"._('La valeur ne peut être supérieure à 100 lorsque la remise est en pourcentage.')."]]>";
					$xml .= '</error>\n</result>';
					$error = true;
				}

				if( !$error ){
					$valType = $_POST['val-type-'.$id];
					if ($_POST['type-'.$id] == NEW_PRICE && isset($_POST['valeur-type-'.$id]) && $_POST['valeur-type-'.$id] == 'TTC') {

						if (isset($_POST['prd-'.$id])) $rprd = prd_products_get(0, trim($_POST['prd-'.$id]));
						else $rprd = prd_products_get($_POST['prd']);

						if ($rprd && ($prd = ria_mysql_fetch_assoc($rprd)) && $prd['tva_rate']) $valType /= $prd['tva_rate'];
					}

					// On vérifie s'il s'agit d'une mise à jour ou d'un ajout de tarif
					if( $exist ){
						// il s'agit d'une mise à jour
						$prc = prc_prices_update($id, $_POST['type-'.$id], $valType, $_POST['date-start-'.$id].' '.$hourStart, $_POST['date-end-'.$id].' '.$hourStop, $qteMin, $_POST['prd'], $_POST['cat'], $unique, utf8_decode($_POST['desc-'.$id]), $qteMax, null, null, null, $dps_id);
					} else {
						// il s'agit d'un ajout
						$prc = prc_prices_add( $_POST['type-'.$id], $valType, $_POST['date-start-'.$id].' '.$hourStart, $_POST['date-end-'.$id].' '.$hourStop, $qteMin, $_POST['prd'], $_POST['cat'], $unique, false, utf8_decode($_POST['desc-'.$id]), array(), $qteMax, false, 0, null, null, null, $dps_id );
					}

					// On vérifie que la mise à jour ou bien l'ajout s'est bien passé
					if( $prc===false && $exist ){
						$xml .= '<result type="0" idPrc="'.$id.'"><error>';
						$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de la mise à jour du tarif.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
						$xml .= '</error>\n</result>';
						$error = true;
					} elseif( $prc===false ){
						$xml .= '<result type="0" idPrc="0"><error>';
						$xml .= '<![CDATA[2 '._('Une erreur inattendue s\'est produite lors de l\'enregistrement du tarif.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
						$xml .= '</error>\n</result>';
						$error = true;
					} else { // Tout s'est bien déroulé jusque là, on ajoute où met à jour les conditions

						// On vérifie qu'il existe bien des conditions
						$count_cdt = 0;
						if( isset($_POST['cdt-'.$id]) || isset($_POST['cdt-new']) )
							$count_cdt = $exist ? sizeof($_POST['cdt-'.$id]) : sizeof($_POST['cdt-new']);

						if( $exist && $count_cdt>=0 ){
							// Suppression de toutes les conditions pour leur mise à jour
							if( !prc_price_conditions_del($id) ){
								$xml .= '<result type="0" idPrc="'.$prc.'"><error>';
								$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur inattendue s\'est produite lors de la mise à jour des conditions.') : _('Une erreur inattendue s\'est produite lors de la mise à jour de la condition.') ).'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
								$xml .= '</error>\n</result>';
								$error = true;
							}
							if( $count_cdt>0 ){
								$cdt = $_POST['cdt-'.$id];
								$sbl = isset($_POST['sbl-'.$id]) ? $_POST['sbl-'.$id] : '=';
								$val = isset($_POST['val-cdt-'.$id]) ? $_POST['val-cdt-'.$id] : false;
							}
						} elseif( $count_cdt>0 ){
							$cdt = $_POST['cdt-new'];
							$sbl = isset($_POST['sbl-new']) ? $_POST['sbl-new'] : '=';
							$val = isset($_POST['val-cdt-new']) ? $_POST['val-cdt-new'] : false;
						}

						if( !$error && $count_cdt>0 ){// On parcours les conditions
							foreach($cdt as $key=>$fld){
								if( $fld>0 ){
									// On récupère le nom et le type du champ personalisé
									$fld_name = fld_fields_get_name($fld);
									$fld_type = fld_fields_get_type($fld);

									$sb = '';
									if( $fld_type==FLD_TYPE_DATE && (isset($_POST['choose-date-'.$id]) && $_POST['choose-date-'.$id]>0 || isset($_POST['choose-date-new']) && $_POST['choose-date-new']>0) ){
										// Il s'agit d'un champ personnalisé de type date, le traitement est différent
										$chooseDate = isset($_POST['choose-date-new']) ? $_POST['choose-date-new'] : $_POST['choose-date-'.$id];
										switch( $chooseDate ){
											case 1 :
												$temp = $exist ? $_POST['val-cdt-date-'.$id.'-'.$chooseDate] : $_POST['val-cdt-date-new-'.$chooseDate];
												if( $temp==-1 ){
													$xml .= '<result type="0" idPrc="'.($exist ? $prc : 0).'"><error>';
													$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur inattendue s\'est produite lors de la mise à jour des conditions.') : _('Une erreur inattendue s\'est produite lors de la mise à jour de la condition.') ).'<br />'._('Veuillez sélectionner une des valeurs suivantes : Aujourd\'hui, Cette semaine, Ce mois-ci.').']]>';
													$xml .= '</error>\n</result>';
													$error = true;
												}
											case 2 :
												if( $exist ){
													$sb = $_POST['sbl-date-'.$id.'-'.$_POST['choose-date-'.$id]];
													$value = $_POST['val-cdt-date-'.$id.'-'.$_POST['choose-date-'.$id]];
												} else {
													$sb = $_POST['sbl-date-new-'.$_POST['choose-date-new']];
													$value = $_POST['val-cdt-date-new-'.$_POST['choose-date-new']];
												}
												break;
											case 3 :
												$sb = "><";
												if( $exist )
													$value = $_POST['val-cdt-date-'.$id.'-3'].';'.$_POST['val-cdt-date-'.$id.'-3-2'];
												else
													$value = $_POST['val-cdt-date-new-3'].';'.$_POST['val-cdt-date-new-3-2'];
												break;
										}

										if( $value=='' || $value==';' ){
											$xml .= '<result type="0" idPrc="'.($exist ? $prc : 0).'"><error>';
											$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur inattendue s\'est produite lors de la mise à jour des conditions.') : _('Une erreur inattendue s\'est produite lors de la mise à jour de la condition.') ).'<br />'._('Aucune valeur n\'a été sélectionnée ou définie.').']]>';
											$xml .= '</error>\n</result>';
											$error = true;
										}
									} elseif( $fld_type==FLD_TYPE_DATE && !isset($_POST['choose-date-'.$id]) && !isset($_POST['choose-date-new']) ){
										$xml .= '<result type="0" idPrc="'.($exist ? $prc : 0).'"><error>';
										$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur inattendue s\'est produite lors de la mise à jour des conditions.') : _('Une erreur inattendue s\'est produite lors de la mise à jour de la condition.') ).'<br />'._('Aucune valeur n\'a été sélectionnée ou définie.').']]>';
										$xml .= '</error>\n</result>';
										$error = true;
									}else {
										// Il s'agit d'un champ personnalisé autre que le type date, le traitement est le même.
										$sb = isset($sbl[$key]) ? $sbl[$key] : '=';
										if( !isset($val[$key]) )
											$value = false;
										elseif( $exist )
											$value = $sb=="><" ? $val[$key].';'.$_POST['val-cdt-'.$id.'-2'][$key] : $val[$key];
										else
											$value = $sb=="><" ? $val[$key].';'.$_POST['val-cdt-new-2'][$key] : $val[$key];
									}

									if( !$error && $sb!=='' ){
										// On vérifie si la condition est valable
										switch( (int)prc_conditions_is_valid($fld, $sb, $value) ){
											case FLD_NOT_EXISTS :
												$xml .= '<result type="0" idPrc="'.($exist ? $prc : 0).'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions du tarif.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition du tarif.') ).'<br />'.sprintf(_('Le champ personnalisé %s ne peut être utilisé.'), $fld_name).']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											case INVALID_SYMBOL_FOR_FIELD :
												$xml .= '<result type="0" idPrc="'.($exist ? $prc : 0).'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions du tarif.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition du tarif.') ).'<br />'.sprintf(_('L\'opérateur %s ne peut être utilisé pour ce champ personnalisé.'), $fld_name).']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											case WRONG_SIZE_OF_VALUE :
												$xml .= '<result type="0" idPrc="'.($exist ? $prc : 0).'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions du tarif.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition du tarif.') ).'<br />'._('L\'opérateur "Est compris entre" nécessite deux valeurs.').']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											case VALUE_NOT_NUMERIC :
												$xml .= '<result type="0" idPrc="'.($exist ? $prc : 0).'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions du tarif.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition du tarif.') ).'<br />'.sprintf(_('Le champ %s nécessite une valeur de type numérique.'), $fld_name).']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											case VALUE_NOT_INTEGER :
												$xml .= '<result type="0" idPrc="'.($exist ? $prc : 0).'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions du tarif.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition du tarif.') ).'<br />'.sprintf(_('Le champ %s nécessite un chiffre entier.'), $fld_name).']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											case SELECT_VALUE_NOT_EXISTS :
												$xml .= '<result type="0" idPrc="'.($exist ? $prc : 0).'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions du tarif.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition du tarif.') ).'<br />'.sprintf(_('La valeur choisie ne peut être utilisée avec le champ personnalisé %s.'), $fld_name).']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											case VALUE_NOT_BOOLEAN :
												$xml .= '<result type="0" idPrc="'.($exist ? $prc : 0).'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions du tarif.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition du tarif.') ).'<br />'.sprintf(_('La valeur choisie ne peut être utilisée avec le champ personnalisé %s, veuillez choisir entre "oui" ou "non".'), $fld_name).']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											default :  // la condition est valide
												if( !prc_price_conditions_add( $prc, $fld, $value, $sb ) ){
													$xml .= '<result type="0" idPrc="'.($exist ? $prc : 0).'"><error>';
													$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur inattendue s\'est produite lors de l\'enregistrement des conditions du tarif.') : _('Une erreur inattendue s\'est produite lors de l\'enregistrement de la condition du tarif.') ).'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
													$xml .= '</error>\n</result>';
													$error = true;
												}
												break;
										}
									}
								}
							}
						}
					}
				}

			} else {
				$xml .= '<result type=\"0\" idPrc="'.$prc.'"><error>';
				$xml .= '<![CDATA['._('Une erreur s\'est produite lors de l\'ajout du tarif.').'<br />'._('Le champ valeur est obligatoire et doit comporter un chiffre supérieur à 0.').']]>';
				$xml .= '</error>\n</result>';
				$error = true;
			}

			if( !$error ){
				// On récupère les types de tarifs
				$r_types = prc_types_get();

				// Récupère les tarifs pour un produit donné
				$rprice = prc_prices_get( $prc, 0, false, false, false, $_POST['prd'], false, $_POST['cat'] );
				$price = ria_mysql_fetch_array( $rprice );

				// Affiche les informations sur les tarifs
				// Si la demande d'ajout d'un tarif est faite
				$xml .= '<result type="1" idPrc="'.$prc.'">';
				$xml .= '<cdt-line><![CDATA[';
				$xml .= prc_prices_list_view($_POST['prd'], $_POST['cat'], 0, $prc );
				$xml .= ']]></cdt-line>';
				$xml .= '</result>';
			}
		}

	} elseif( isset($_POST['addTva'], $_POST['formulaire'], $_POST['id-tva']) ){

		// Sauvegarde des tarifs
		$id = $_POST['id-tva'];

		// On vérifie qu'il existe bien des conditions
		$count_cdt = $count_sbl = $count_val = 0;
		if( isset($_POST['cdt-tva-'.$id]) || isset($_POST['cdt-new']) )
			$count_cdt += $id>0 ? sizeof($_POST['cdt-tva-'.$id]) : sizeof($_POST['cdt-new']);
		if( isset($_POST['sbl-tva-new']) || isset($_POST['sbl-tva-'.$id]) )
			$count_sbl += $id>0 ? sizeof($_POST['sbl-tva-'.$id]) : sizeof($_POST['sbl-tva-new']);
		if( isset($_POST['choose-date-tva-new']) || isset($_POST['choose-date-tva-'.$id]) ){
			$count_sbl += $id>0 ? sizeof($_POST['choose-date-tva-'.$id]) : sizeof($_POST['choose-date-tva-new']);
			$count_val++;
		}
		if( isset($_POST['val-cdt-tva-'.$id]) || isset($_POST['val-cdt-tva-new']) )
			$count_val += $id>0 ? sizeof($_POST['val-cdt-tva-'.$id]) : sizeof($_POST['val-cdt-tva-new']);

		if( $count_cdt==0 || $count_val==0 ){
			if( $id>0 && !prc_tva_exempt_groups_del($id) ){
				$xml .= "<result type=\"0\"><error>";
				$xml .= '<![CDATA['._('Comme aucune condition n\'était présente dans cette liste, une suppression de cette dernière a été tentée mais sans succès.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
				$xml .= "</error>\n</result>";
				$error = true;
			} elseif( $id==0 ){
				$xml .= "<result type=\"0\"><error>";
				$xml .= '<![CDATA['._('Afin de pouvoir sauvegarder la liste, vous devez au minimum affecter une condition d\'exonération de TVA et renseigner sa valeur.').']]>';
				$xml .= "</error>\n</result>";
				$error = true;
			} else {
				$xml .= '<result type="2">';
				$xml .= '<cdt-line><![CDATA[';
				$xml .= ']]></cdt-line>';
				$xml .= '</result>';
			}
		} elseif( $id>0 ){ // On vérifie s'il s'agit d'un ajout ou d'une mise à jour d'une TVA
			// S'il s'agit d'une mise à jour des conditions de la liste
			$grp = $id;
			$exist = true;
		} else{
			// S'il s'agit d'un ajout d'une liste de conditions
			$grp = prc_tva_exempt_groups_add();
			$exist = false;
		}

		if( !$error ){
			// On vérifie que l'ajout ou la mise à jour s'est déroulée correctement
			if( $grp===false ){
				$xml .= "<result type=\"0\"><error>";
				$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de l\'ajout de la liste des conditions.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
				$xml .= "</error>\n</result>";
				$error = true;
			} else { // Tout s'est bien passé jusque là, on ajout ou met à jour les conditions
				if( $exist && $count_cdt>=0 ){
					if( $count_cdt>0 ){
						$cdt = $_POST['cdt-tva-'.$id];
						$sbl = isset($_POST['sbl-tva-'.$id]) ? $_POST['sbl-tva-'.$id] : '=';
						$val = isset($_POST['val-cdt-tva-'.$id]) ? $_POST['val-cdt-tva-'.$id] : false;
					}
				} elseif( $count_cdt>0 ){
					$cdt = $_POST['cdt-new'];
					$sbl = isset($_POST['sbl-tva-new']) ? $_POST['sbl-tva-new'] : '=';
					$val = isset($_POST['val-cdt-tva-new']) ? $_POST['val-cdt-tva-new'] : false;
				}

				// On parcours les conditions
				if( !$error && $count_cdt>0 ){
					$nbCdt = 0;
					$tfld = array();
					foreach($cdt as $key=>$fld){
						if( $fld>0 ){
							// On récupère le nom et le type du champ personalisé
							$fld_name = fld_fields_get_name($fld);
							$fld_type = fld_fields_get_type($fld);

							if( $fld_type==FLD_TYPE_DATE && (isset($_POST['choose-date-tva-'.$id]) && $_POST['choose-date-tva-'.$id]>0 || isset($_POST['choose-date-tva-new']) && $_POST['choose-date-tva-new']>0) ){
								// Il s'agit d'un champ personnalisé de type date, le traitement est différent
								$chooseDate = isset($_POST['choose-date-tva-new']) ? $_POST['choose-date-tva-new'] : $_POST['choose-date-tva-'.$id];
								switch( $chooseDate ){
									case 1 :
										$temp = $exist ? $_POST['val-cdt-date-tva-'.$id.'-'.$chooseDate] : $_POST['val-cdt-date-tva-new-'.$chooseDate];
										if( $temp==-1 ){
											$xml .= "<result type=\"0\"><error>";
											$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de la mise à jour des conditions.') : _('Une erreur s\'est produite lors de la mise à jour de la condition.') ).'<br />'._('Veuillez sélectionner une des valeurs suivantes : Aujourd\'hui, Cette semaine, Ce mois-ci.').']]>';
											$xml .= "</error>\n</result>";
											$error = true;
										}
									case 2 :
										if( $exist ){
											$sb = $_POST['sbl-date-tva-'.$id.'-'.$chooseDate];
											$value = $_POST['val-cdt-date-tva-'.$id.'-'.$chooseDate];
										} else {
											$sb = $_POST['sbl-date-tva-new-'.$chooseDate];
											$value = $_POST['val-cdt-date-tva-new-'.$chooseDate];
										}
										break;
									case 3 :
										$sb = "><";
										if( $exist ){
											$value = $_POST['val-cdt-date-'.$id.'-3'].';'.$_POST['val-cdt-date-'.$id.'-3-2'];
										}else{
											$value = $_POST['val-cdt-date-tva-new-3'].';'.$_POST['val-cdt-date-tva-new-3-2'];
										}
										break;
								}
								if( $value=='' || $value==';' ){
									$xml .= "<result type=\"0\"><error>";
									$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de la mise à jour des conditions.') : _('Une erreur s\'est produite lors de la mise à jour de la condition.') ).'<br />'._('Aucune valeur n\'a été sélectionnée ou définie.').']]>';
									$xml .= "</error>\n</result>";
									$error = true;
								}
							} elseif( $fld_type==FLD_TYPE_DATE && !isset($_POST['choose-date-'.$id]) ){
								$xml .= "<result type=\"0\"><error>";
								$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de la mise à jour des conditions.') : _('Une erreur s\'est produite lors de la mise à jour de la condition.') ).'<br />'._('Aucune valeur n\'a été sélectionnée ou définie.').']]>';
								$xml .= "</error>\n</result>";
								$error = true;
							}else {
								// Il s'agit d'un champ personnalisé autre que le type date, le traitement est le même.
								$sb = isset($sbl[$key]) ? $sbl[$key] : '=';
								if( !isset($val[$key]) ){
									$value = false;
								}elseif( $exist ){
									$value = $sb=="><" ? $val[$key].';'.$_POST['val-cdt-tva-'.$id.'-2'][$key] : $val[$key];
								}else{
									$value = $sb=="><" ? $val[$key].';'.$_POST['val-cdt-tva-new-2'][$key] : $val[$key];
								}
							}

							if( !$error ){
								// On vérifie si la condition est valable
								switch( (int)prc_conditions_is_valid($fld, $sb, $value) ){
									case FLD_NOT_EXISTS :
										$xml .= "<result type=\"0\"><error>";
										$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la TVA.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la TVA.') ).'<br />'.sprintf(_('Le champ personnalisé %s ne peut être utilisé.'), $fld_name).']]>';
										$xml .= "</error>\n</result>";
										$error = true;
										break;
									case INVALID_SYMBOL_FOR_FIELD :
										$xml .= "<result type=\"0\"><error>";
										$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la TVA.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la TVA.') ).'<br />'.sprintf(_('L\'opérateur %s ne peut être utilisé pour ce champ personnalisé.'), $fld_name).']]>';
										$xml .= "</error>\n</result>";
										$error = true;
										break;
									case WRONG_SIZE_OF_VALUE :
										$xml .= "<result type=\"0\"><error>";
										$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la TVA.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la TVA.') ).'<br />'._('L\'opérateur "Est compris entre" nécessite deux valeurs.').']]>';
										$xml .= "</error>\n</result>";
										$error = true;
										break;
									case VALUE_NOT_NUMERIC :
										$xml .= "<result type=\"0\"><error>";
										$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la TVA.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la TVA.') ).'<br />'.sprintf(_('Le champ %s nécessite une valeur de type numérique.'), $fld_name).']]>';
										$xml .= "</error>\n</result>";
										$error = true;
										break;
									case VALUE_NOT_INTEGER :
										$xml .= "<result type=\"0\"><error>";
										$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la TVA.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la TVA.') ).'<br />'.sprintf(_('Le champ %s nécessite un chiffre entier.'), $fld_name).']]>';
										$xml .= "</error>\n</result>";
										$error = true;
										break;
									case SELECT_VALUE_NOT_EXISTS :
										$xml .= "<result type=\"0\"><error>";
										$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la TVA.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la TVA.') ).'<br />'.sprintf(_('La valeur choisie ne peut être utilisée avec le champ personnalisé %s.'), $fld_name).']]>';
										$xml .= "</error>\n</result>";
										$error = true;
										break;
									case VALUE_NOT_BOOLEAN :
										$xml .= "<result type=\"0\"><error>";
										$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la TVA.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la TVA.') ).'<br />'.sprintf(_('La valeur choisie ne peut être utilisée avec le champ personnalisé %s, veuillez choisir entre "oui" ou "non".'), $fld_name).']]>';
										$xml .= "</error>\n</result>";
										$error = true;
										break;
									default :  // la condition est valide
										if( !prc_tva_exempt_conditions_add($grp, $fld, $value, $sb) ){
											$xml .= "<result type=\"0\"><error>";
											$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur innatendue s\'est produite lors de l\'enregistrement des conditions de la TVA.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la TVA.') ).'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
											$xml .= "</error>\n</result>";
											$error = true;
										} else {
											$nbCdt++; $tfld[] = $fld;
										}
										break;
								}
							}
						}
					}
				}
			}

			// Retire les conditions qui n'existe plus
			if( !$error )
				prc_tva_exempt_conditions_del( $grp, $tfld, true );
			if( $nbCdt==0 ){
				prc_tva_exempt_groups_del($grp);
			}elseif( !$exist && !$error ){

				$xml .= '<result type="1" idTva="'.$grp.'">';
				$xml .= '<cdt-line><![CDATA[';

				// Vérifie s'il existe plusieurs groupes (permet d'afficher ou non le séparateur "OU")
				$rgrp_tva = prc_tva_exempt_groups_get();
				$grp_count = 0;
				if( $rgrp_tva!==false )
					$grp_count = ria_mysql_num_rows($rgrp_tva);

				// Récupère les informations sur le groupe juste ajouté
				$rgrp = prc_tva_exempt_groups_get($grp);
				if( $rgrp!==false ){
					$grp = ria_mysql_fetch_array($rgrp);

					// Affichage du séparateurs "OU" entre les groupes
					if( $grp_count>1 )
						$xml .= '<tr id="orTva-'.$grp['id'].'"><td class="or-cdt" colspan="2">'._('OU').'</td></tr>';

					// Affichage du groupes de conditions
					$xml .=  '	<tr id="grpTva-'.$grp['id'].'">';
					$xml .=  '		<td class="conditions" id="cdts-tva-'.$grp['id'].'">';
					$cdt = prc_conditions_view( $grp['id'], TERMS_TVA );
					if( $cdt!==false )
						$xml .=  $cdt;
					$xml .=  '		</td>';
					$xml .=  '		<td class="action">';
					$xml .=  '			<input class="action btn-main" type="button" name="grp-tva-save" id="grp-tva-save-'.$grp['id'].'" value="'._('Enregistrer').'" onclick="tvaSubmit(\'prc-cat\', '.$grp['id'].');" /><br />';
					$xml .= '			<div class="save-load" id="save-load-tva-'.$grp['id'].'">';
					$xml .= ' 				<img src="/admin/images/loader2.gif" alt="" />';
					$xml .= '			</div>';
					$xml .=  '			<a id="grp-tva-del-'.$grp['id'].'" class="del button btn-cancel" onclick="delTva('.$grp['id'].');">'._('Supprimer').'</a>';
					$xml .=  '		</td>';
					$xml .=  '	</tr>';
				}

				$xml .= ']]></cdt-line>';
				$xml .= '</result>';
			} elseif( !$error ){
				$xml .= '<result type="2">';
				$xml .= '<cdt-line><![CDATA[';

				// Vérifie s'il existe plusieurs groupes (permet d'afficher ou non le séparateur "OU")
				$rgrp_tva = prc_tva_exempt_groups_get();
				$grp_count = 0;
				if( $rgrp_tva!==false )
					$grp_count = ria_mysql_num_rows($rgrp_tva);

				// Récupère les informations sur le groupe juste ajouté
				$rgrp = prc_tva_exempt_groups_get($grp);
				if( $rgrp!==false ){
					$grp = ria_mysql_fetch_array($rgrp);
					$cdt = prc_conditions_view( $grp['id'], TERMS_TVA );
					if( $cdt!==false )
						$xml .=  $cdt;
				}

				$xml .= ']]></cdt-line>';
				$xml .= '</result>';
			}
		}

	} elseif( isset($_POST['addPromo'], $_POST['formulaire'], $_POST['id-pmt']) ){

		// Sauvegarde des tarifs
		$id = $_POST['id-pmt'];
		$params = prc_prices_get_params( $id, array('is-sync') );
		$_POST['val-type-'.$id] = str_replace(array(' ', ','), array('', '.'), $_POST['val-type-'.$id]);

		if( !is_numeric($_POST['val-type-'.$id]) ){
			$xml .= '<result type="0" idPrc="'.$id.'"><error>';
			$xml .= '<![CDATA['._('La valeur saisie est incorrecte, veuillez saisir un nombre supérieur à 0.').']]>';
			$xml .= '</error>\n</result>';
			$error = true;
		} elseif( $params['is-sync'] ){
			// La promotion est synchronisée, on ne peut donc mettre à jour que le nom
			if( !prc_prices_set_name($id, $_POST['desc-'.$id]) ){
				$xml .= '<result type="0"><error>';
				$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de la mise à jour de la promotion.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
				$xml .= '</error></result>';
				$error = true;
			} else {
				$xml .= '<result type="1">';
				$xml .= '<cdt-line><![CDATA[';
				$xml .= ']]></cdt-line>';
				$xml .= '</result>';
			}
		} else {

			// On vérifie que le champ valeur qui est obligatoire existe bien et n'est pas vide
			if( isset($_POST['val-type-'.$id]) && $_POST['val-type-'.$id]!=='' ){

				$rp = prd_products_get(0, trim($_POST['prd-'.$id]));

				$exist = prc_prices_exists($id);
				$unique = isset($_POST['unique-'.$id]) || $_POST['type-'.$id]==NEW_PRICE ? true : false;
				$qteMin = isset($_POST['qte-min-'.$id]) && is_numeric($_POST['qte-min-'.$id]) ? $_POST['qte-min-'.$id] : 1;
				$qteMax = isset($_POST['qte-max-'.$id]) && is_numeric($_POST['qte-max-'.$id]) ? $_POST['qte-max-'.$id] : false;
				$hourStart = trim($_POST['hour-start-'.$id])!=='' ? $_POST['hour-start-'.$id] : '00:00';
				$hourStop = trim($_POST['hour-stop-'.$id])!=='' ? $_POST['hour-stop-'.$id] : '23:59';

				$delta = strtotime(dateheureparse($_POST['date-end-'.$id].' '.$hourStop)) - strtotime(dateheureparse($_POST['date-start-'.$id].' '.$hourStart));
				if( $qteMax>0 && $qteMin>$qteMax ){
					$xml .= '<result type="0" idPrc="'.$id.'"><error>';
					$xml .= '<![CDATA['._('Une erreur s\'est produite lors de l\'enregistrement de la promotion').'<br />'._('La quantité minimum ne peut être supérieur à la quantité maximum.').']]>';
					$xml .= '</error>\n</result>';
					$error = true;
				}elseif( $_POST['date-start-'.$id]=='' || $_POST['date-end-'.$id]=='' ){
					$xml .= '<result type="0" idPrc="'.$id.'"><error>';
					$xml .= '<![CDATA['._('Une erreur s\'est produite lors de l\'enregistrement de la promotion').'<br />'._('Les dates de début et de fin sont obligatoires.').']]>';
					$xml .= '</error>\n</result>';
					$error = true;
				}elseif( !isdate($_POST['date-start-'.$id]) || !isdate($_POST['date-end-'.$id]) ){
					$xml .= '<result type="0" idPrc="'.$id.'"><error>';
					$xml .= '<![CDATA['._('Une erreur s\'est produite lors de l\'enregistrement de la promotion').'<br />'._('Les dates de début et de fin doivent être au format JJ/MM/AAAA.').']]>';
					$xml .= '</error>\n</result>';
					$error = true;
				}elseif( $delta<0 ){
					$xml .= '<result type="0" idPrc="'.$id.'"><error>';
					$xml .= '<![CDATA['._('Une erreur s\'est produite lors de l\'enregistrement de la promotion').'<br />'._('La date de fin ne peut être antérieure à la date de début.').']]>';
					$xml .= '</error>\n</result>';
					$error = true;
				}elseif( $_POST['type-'.$id]==DISCOUNT_PERCENT && $_POST['val-type-'.$id]>100 ){
					$xml .= '<result type="0" idPrc="'.$id.'"><error>';
					$xml .= '<![CDATA['._('Une erreur s\'est produite lors de l\'enregistrement de la promotion').'<br />'._('La valeur ne peut être supérieure à 100 lorsque la remise est en pourcentage.').']]>';
					$xml .= '</error>\n</result>';
					$error = true;
				}elseif( $rp==false || ria_mysql_num_rows($rp)==0 ){
					$xml .= '<result type="0" idPrc="'.$id.'"><error>';
					$xml .= '<![CDATA['._('Une erreur s\'est produite lors de l\'enregistrement de la promotion').'<br />'._('La référence donnée ne correspond à aucun produit.').']]>';
					$xml .= '</error>\n</result>';
					$error = true;
				}

				if( !$error ){
					$prd = ria_mysql_fetch_assoc($rp);
					$idPrd = $prd['id'];

					$valType = $_POST['val-type-'.$id];
					if ($_POST['type-'.$id] == NEW_PRICE && isset($_POST['valeur-type-'.$id]) && $_POST['valeur-type-'.$id] == 'TTC' && $prd['tva_rate']) $valType /= $prd['tva_rate'];

					// On vérifie s'il s'agit d'une mise à jour ou d'un ajout de la promotion
					if( $exist ){
						// il s'agit d'une mise à jour
						$prc = prc_prices_update($id, $_POST['type-'.$id], $valType, $_POST['date-start-'.$id].' '.$hourStart, $_POST['date-end-'.$id].' '.$hourStop, $qteMin, $idPrd, 0, $unique, utf8_decode($_POST['desc-'.$id]), $qteMax);
					} else {
						// il s'agit d'un ajout
						$prc = prc_prices_add( $_POST['type-'.$id], $valType, $_POST['date-start-'.$id].' '.$hourStart, $_POST['date-end-'.$id].' '.$hourStop, $qteMin, $idPrd, 0, $unique, false, utf8_decode($_POST['desc-'.$id]), array(), $qteMax, true );
					}

					// On vérifie que la mise à jour ou bien l'ajout s'est bien passé
					if( $prc===false && $exist ){
						$xml .= '<result type="0" idPrc="'.$id.'"><error>';
						$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de la mise à jour de la promotion.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
						$xml .= '</error>\n</result>';
						$error = true;
					} elseif( $prc===false ){
						$xml .= '<result type="0" idPrc="0"><error>';
						$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de l\'enregistrement de la promotion.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
						$xml .= '</error>\n</result>';
						$error = true;
					} else { // Tout s'est bien déroulé jusque là, on ajoute où met à jour les conditions

						// On vérifie qu'il existe bien des conditions
						$count_cdt = 0;
						if( isset($_POST['cdt-'.$id]) || isset($_POST['cdt-new']) )
							$count_cdt = $exist ? sizeof($_POST['cdt-'.$id]) : sizeof($_POST['cdt-new']);

						if( $exist && $count_cdt>=0 ){
							// Suppression de toutes les conditions pour leur mise à jour
							if( !prc_price_conditions_del($id) ){
								$xml .= '<result type="0" idPrc="'.$prc.'"><error>';
								$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur innatendue s\'est produite lors de la mise à jour des conditions.') : _('Une erreur s\'est produite lors de la mise à jour de la condition.') ).'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
								$xml .= '</error>\n</result>';
								$error = true;
							}
							if( $count_cdt>0 ){
								$cdt = $_POST['cdt-'.$id];
								$sbl = isset($_POST['sbl-'.$id]) ? $_POST['sbl-'.$id] : '=';
								$val = isset($_POST['val-cdt-'.$id]) ? $_POST['val-cdt-'.$id] : false;
							}
						} elseif( $count_cdt>0 ){
							$cdt = $_POST['cdt-new'];
							$sbl = isset($_POST['sbl-new']) ? $_POST['sbl-new'] : '=';
							$val = isset($_POST['val-cdt-new']) ? $_POST['val-cdt-new'] : false;
						}

						if( !$error && $count_cdt>0 ){// On parcours les conditions
							foreach($cdt as $key=>$fld){
								if( $fld>0 ){
									// On récupère le nom et le type du champ personalisé
									$fld_name = fld_fields_get_name($fld);
									$fld_type = fld_fields_get_type($fld);

									$sb = '';
									if( $fld_type==FLD_TYPE_DATE && (isset($_POST['choose-date-'.$id]) && $_POST['choose-date-'.$id]>0 || isset($_POST['choose-date-new']) && $_POST['choose-date-new']>0) ){
										// Il s'agit d'un champ personnalisé de type date, le traitement est différent
										$chooseDate = isset($_POST['choose-date-new']) ? $_POST['choose-date-new'] : $_POST['choose-date-'.$id];
										switch( $chooseDate ){
											case 1 :
												$temp = $exist ? $_POST['val-cdt-date-'.$id.'-'.$chooseDate] : $_POST['val-cdt-date-new-'.$chooseDate];
												if( $temp==-1 ){
													$xml .= '<result type="0" idPrc="'.$prc.'"><error>';
													$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de la mise à jour des conditions.') : _('Une erreur s\'est produite lors de la mise à jour de la condition.') ).'<br />'._('Veuillez sélectionner une des valeurs suivantes : Aujourd\'hui, Cette semaine, Ce mois-ci.').']]>';
													$xml .= '</error>\n</result>';
													$error = true;
												}
											case 2 :
												if( $exist ){
													$sb = $_POST['sbl-date-'.$id.'-'.$_POST['choose-date-'.$id]];
													$value = $_POST['val-cdt-date-'.$id.'-'.$_POST['choose-date-'.$id]];
												} else {
													$sb = $_POST['sbl-date-new-'.$_POST['choose-date-new']];
													$value = $_POST['val-cdt-date-new-'.$_POST['choose-date-new']];
												}
												break;
											case 3 :
												$sb = "><";
												if( $exist )
													$value = $_POST['val-cdt-date-'.$id.'-3'].';'.$_POST['val-cdt-date-'.$id.'-3-2'];
												else
													$value = $_POST['val-cdt-date-new-3'].';'.$_POST['val-cdt-date-new-3-2'];
												break;
										}

										if( $value=='' || $value==';' ){
											$xml .= '<result type="0" idPrc="'.$prc.'"><error>';
											$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de la mise à jour des conditions.') : _('Une erreur s\'est produite lors de la mise à jour de la condition.') ).'<br />'._('Aucune valeur n\'a été sélectionnée ou définie.').']]>';
											$xml .= '</error>\n</result>';
											$error = true;
										}
									} elseif( $fld_type==FLD_TYPE_DATE && !isset($_POST['choose-date-'.$id]) && !isset($_POST['choose-date-new']) ){
										$xml .= '<result type="0" idPrc="'.$prc.'"><error>';
										$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de la mise à jour des conditions.') : _('Une erreur s\'est produite lors de la mise à jour de la condition.') ).'<br />'._('Aucune valeur n\'a été sélectionnée ou définie.').']]>';
										$xml .= '</error>\n</result>';
										$error = true;
									}else {
										// Il s'agit d'un champ personnalisé autre que le type date, le traitement est le même.
										$sb = isset($sbl[$key]) ? $sbl[$key] : '=';
										if( !isset($val[$key]) )
											$value = false;
										elseif( $exist )
											$value = $sb=="><" ? $val[$key].';'.$_POST['val-cdt-'.$id.'-2'][$key] : $val[$key];
										else
											$value = $sb=="><" ? $val[$key].';'.$_POST['val-cdt-new-2'][$key] : $val[$key];
									}

									if( !$error && $sb!=='' ){
										// On vérifie si la condition est valable
										switch( (int)prc_conditions_is_valid($fld, $sb, $value) ){
											case FLD_NOT_EXISTS :
												$xml .= '<result type="0" idPrc="'.$prc.'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la promotion.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la promotion.') ).'<br />'.sprintf(_('Le champ personnalisé %s ne peut être utilisé.'), $fld_name).']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											case INVALID_SYMBOL_FOR_FIELD :
												$xml .= '<result type="0" idPrc="'.$prc.'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la promotion.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la promotion.') ).'<br />'.sprintf(_('L\'opérateur %s ne peut être utilisé pour ce champ personnalisé.'), $fld_name).']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											case WRONG_SIZE_OF_VALUE :
												$xml .= '<result type="0" idPrc="'.$prc.'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la promotion.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la promotion.') ).'<br />'._('L\'opérateur "Est compris entre" nécessite deux valeurs.').']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											case VALUE_NOT_NUMERIC :
												$xml .= '<result type="0" idPrc="'.$prc.'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la promotion.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la promotion.') ).'<br />'.sprintf(_('Le champ %s nécessite une valeur de type numérique.'), $fld_name).']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											case VALUE_NOT_INTEGER :
												$xml .= '<result type="0" idPrc="'.$prc.'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la promotion.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la promotion.') ).'<br />'.sprintf(_('Le champ %s nécessite un chiffre entier.'), $fld_name).']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											case SELECT_VALUE_NOT_EXISTS :
												$xml .= '<result type="0" idPrc="'.$prc.'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la promotion.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la promotion.') ).'<br />'.sprintf(_('La valeur choisie ne peut être utilisée avec le champ personnalisé %s.'), $fld_name).']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											case VALUE_NOT_BOOLEAN :
												$xml .= '<result type="0" idPrc="'.$prc.'"><error>';
												$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur s\'est produite lors de l\'enregistrement des conditions de la promotion.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la promotion.') ).'<br />'.sprintf(_('La valeur choisie ne peut être utilisée avec le champ personnalisé %s, veuillez choisir entre "oui" ou "non".'), $fld_name).']]>';
												$xml .= '</error>\n</result>';
												$error = true;
												break;
											default :  // la condition est valide
												if( !prc_price_conditions_add( $prc, $fld, $value, $sb ) ){
													$xml .= '<result type="0" idPrc="'.$prc.'"><error>';
													$xml .= '<![CDATA['.( $count_cdt>1 ? _('Une erreur innatendue s\'est produite lors de l\'enregistrement des conditions de la promotion.') : _('Une erreur s\'est produite lors de l\'enregistrement de la condition de la promotion.') ).'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
													$xml .= '</error>\n</result>';
													$error = true;
												}
												break;
										}
									}
								}
							}
						}
					}
				}

			} else {
				$xml .= '<result type=\"0\" idPrc="'.$prc.'"><error>';
				$xml .= '<![CDATA['._('Une erreur s\'est produite lors de l\'ajout de la promotion.').'<br />'._('Le champ valeur est obligatoire et doit comporter un chiffre supérieur à 0.').']]>';
				$xml .= '</error>\n</result>';
				$error = true;
			}

			if( !$error ){
				// On récupère les types de tarifs
				$r_types = prc_types_get();

				// Affiche les informations sur les promotions
				// Si la demande d'ajout d'une promotion est faite
				$xml .= '<result type="1" idPrc="'.$prc.'">';
				$xml .= '<cdt-line><![CDATA[';
				$xml .= prc_prices_list_view(0, 0, 0, $prc, true );
				$xml .= ']]></cdt-line>';
				$xml .= '</result>';
			}
		}

	}elseif( isset($_POST['savePrdEco'], $_POST['eco'], $_POST['prd']) ){

		// Vérifie si la valeur saisie est bien un numéric supérieur à 0
		if( !is_numeric($_POST['eco']) || $_POST['eco']<0 ){
			$xml .= "<result type=\"0\"><error>";
			$xml .= '<![CDATA['._('La valeur saisie pour l\'écotaxe est incorrecte.').'<br />'._('Veuillez saisir un nombre supérieur ou égal à 0.').']]>';
			$xml .= "</error>\n</result>";
		} else if( !prd_products_set_ecotaxe($_POST['prd'], $_POST['eco']) ){
			$xml .= "<result type=\"0\"><error>";
			$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de l\'enregistrement de l\'écotaxe de ce produit.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
			$xml .= "</error>\n</result>";
		} else {
			$xml .= '<result type="1">';
			$xml .= '<cdt-line><![CDATA[';
			$xml .= ']]></cdt-line>';
			$xml .= '</result>';
		}

	}elseif( isset($_POST['savePrdPurchaseAvg'], $_POST['purchase_avg'], $_POST['prd']) ){

		// Vérifie si la valeur saisie est bien supérieure ou égale à 0
		if( !is_numeric($_POST['purchase_avg']) || $_POST['purchase_avg']<0 ){
			$xml .= "<result type=\"0\"><error>";
			$xml .= '<![CDATA['._('La valeur saisie pour le prix d\'achat est incorrecte.').'<br />'._('Veuillez saisir un nombre supérieur ou égal à 0.').']]>';
			$xml .= "</error>\n</result>";
		}elseif( !prd_products_set_purchase_avg($_POST['prd'], $_POST['purchase_avg']) ){
			$xml .= "<result type=\"0\"><error>";
			$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de l\'enregistrement du prix d\'achat de ce produit.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
			$xml .= "</error>\n</result>";
		}else{
			$xml .= '<result type="1">';
			$xml .= '<cdt-line><![CDATA[';
			$xml .= ']]></cdt-line>';
			$xml .= '</result>';
		}

	}elseif( isset($_POST['savePrdTva'], $_POST['idTva'], $_POST['tva']) ){

		$prd = isset($_POST['prd']) && $_POST['prd']>0 ? $_POST['prd'] : 0;
		$cat = isset($_POST['cat']) && $_POST['cat']>0 ? $_POST['cat'] : 0;

		if( $_POST['tva']=='-1' ){
			if( $_POST['idTva']>0 && !prc_tvas_del($_POST['idTva']) ){
				$xml .= "<result type=\"0\"><error>";
				$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de l\'enregistrement de la TVA de ce produit.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
				$xml .= "</error>\n</result>";
			} else {
				$xml .= '<result type="1" idTva="'.$_POST['idTva'].'">';
				$xml .= '<cdt-line><![CDATA[';
				$xml .= ']]></cdt-line>';
				$xml .= '</result>';
			}
		}else if( $_POST['idTva']==0 ){
			$tva = prc_tvas_add($_POST['tva'], $prd, $cat);
			if( !$tva ){
				$xml .= "<result type=\"0\"><error>";
				$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de l\'enregistrement de la TVA de ce produit.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
				$xml .= "</error>\n</result>";
			} else {
				$xml .= '<result type="1" idTva="'.$tva.'">';
				$xml .= '<cdt-line><![CDATA[';
				$xml .= ']]></cdt-line>';
				$xml .= '</result>';
			}
		} else {
			if( !prc_tvas_set_rate($_POST['idTva'], $_POST['tva']) ){
				$xml .= "<result type=\"0\"><error>";
				$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de l\'enregistrement de la TVA de ce produit.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
				$xml .= "</error>\n</result>";
			} else {
				$xml .= '<result type="1" idTva="'.$_POST['idTva'].'">';
				$xml .= '<cdt-line><![CDATA[';
				$xml .= ']]></cdt-line>';
				$xml .= '</result>';
			}
		}

	} elseif( isset($_POST['type']) ){

		$xml .= '<result type="1">';
		$xml .= '<cdt-line><![CDATA[';
		// Mise en place du formulaire d'ajout soit d'un tarif, soit d'une TVA
		if( $_POST['type']=='price' ) {
			// On récupère les types de tarifs
			$r_types = prc_types_get();
			$xml .= '		<td headers="sync" class="cdt-center">';
			$xml .= '			<img alt="'._('Ce tarif n\'existe que dans votre boutique en ligne').'" title="'._('Ce tarif n\'existe que dans votre boutique en ligne').'" src="/admin/images/sync/0.svg?'.ADMIN_ASSET_IMGS.'" class="sync">';
			$xml .= '		</td>';
			$xml .= '		<td headers="information" class="information">';
			$xml .= '			<div class="info-fld">'._('Nouveau tarif conditionnel').'</div><br />';
			$xml .= '			<div class="info">';
			$xml .= '				<label for="type-0">'._('Valeur').' :</label>';
			if( $r_types!==false && ria_mysql_num_rows($r_types)>0 ){
				$xml .= '			<select name="type-0" id="type-0" onchange="displayCumulate(0)">';
					while( $type = ria_mysql_fetch_array($r_types) ){
						if( $_POST['form']=='product' )
							$xml .= '		<option value="'.$type['id'].'">'.$type['name'].'</option>';
						elseif( $type['id']!=NEW_PRICE )
							$xml .= '		<option value="'.$type['id'].'">'.$type['name'].'</option>';
					}
			}
			$xml .= '				</select>';

			$xml .= '				<select class="valeur-select" name="valeur-type-0" id="valeur-type-0">';
			$xml .= '				<option value="HT" selected="selected">'._('HT').'</option>';
			$xml .= ' 				<option value="TTC">'._('TTC').'</option>';
			$xml .= '				</select>';

			$xml .= '				<input class="val-select" type="text" name="val-type-0" id="val-type-0" value="" />';
			$xml .= ' 			<span class="info-bulle" title="'._('Ce champ est obligatoire et doit comporter un nombre supérieur à 0').'">*</span>';
			$xml .= '			</div>';
			$xml .= '			<div class="info">';
			$xml .= '				<label for="unique-0" title="'._('En décochant cette option, la remise ne sera pas prise en compte s\'il existe des tarifs de priorité supérieure.').'">'._('Cumuler').'&nbsp:</label>';
			$xml .= '				<input type="checkbox" name="unique-0" id="unique-0" value="0" checked="checked" />';
			$xml .= ' 			<span class="info-bulle" title="'._('En décochant cette option, la remise ne sera pas prise en compte s\'il existe des tarifs de priorité supérieure.').'">**</span>';
			$xml .= '			</div>';
			$xml .= '			<div class="info">';
			$xml .= '				<label for="desc-0">'._('Nom :').'</label>';
			$xml .= 				'<input type="text" name="desc-0" id="desc-0" value="" /><span class="info-fld">('._('Optionnel').')</span>';
			$xml .= '			</div>';
			$xml .= '		</td>';
			$xml .= '		<td headers="conditions" class="conditions" id="cdts-0">';
			if( $_POST['form']=='product' )
				$xml .= '		<div class="bold"><label for="qte-min-0">'._('Quantité comprise entre').'</label>&nbsp;:&nbsp;<input type="text" name="qte-min-0" id="qte-min-0" value="1" />&nbsp;<label for="qte-max-0">'._('et').'</label>&nbsp;<input type="text" name="qte-max-0" id="qte-max-0" value="&#8734;" /><span class="info-bulle" title="'._('Si la quantité maximum n\'est pas précisée alors aucun maximum ne sera fixé pour ce tarif.').'">***</span></div>';
			$xml .= '			<div class="info">';
			$xml .= '				<label for="date-price-0">'._('Date de début :').'</label>';
			$xml .= '				<input class="datepicker date" type="text" name="date-start-0" id="date-price-0" value="" autocomplete="off" />';
			$xml .= '				&nbsp;<label class="lbl-info-cdt" for="hour-start-0">'._('à').'</label>&nbsp;<input class="hour hourpicker" type="text" name="hour-start-0" id="hour-start-0" value="" />';
			$xml .= '			</div>';
			$xml .= '			<div class="info">';
			$xml .= '				<label for="date-end-0">'._('Date de fin :').'</label>';
			$xml .= '				<input class="datepicker date" type="text" name="date-end-0" id="date-end-0" value="" autocomplete="off" />';
			$xml .= '				&nbsp;<label class="lbl-info-cdt" for="hour-stop-0">'._('à').'</label>&nbsp;<input class="hour hourpicker" type="text" name="hour-stop-0" id="hour-stop-0" value="" />';
			$xml .= '			</div>';
			$xml .= '		<div class="bold clear"></div>';
			$xml .= '			<br /><input class="action" type="button" title="'._('Ajouter une condition à ce tarif').'" name="add-cdt" id="add-cdt-0" value="'._('Ajouter une condition').'" onclick="addCdt(0, true , false, false);" />';
			$xml .= '		</td>';
			$xml .= '		<td headers="action" class="action">';
			$xml .= '			<input class="action btn-main" type="button" name="prices-save" id="prices-save-0" value="'._('Enregistrer').'" onclick="priceSubmit(\''.$_POST['form'].'\', 0, '.$_POST['cat'].', '.$_POST['prd'].');" /><br />';
			$xml .= '			<div class="save-load" id="save-load-0">';
			$xml .= ' 				<img src="/admin/images/loader2.gif" alt="" />';
			$xml .= '			</div>';
			$xml .= '			<br /><a class="button button-del" onclick="cancelNewForm();" class="btn-cancel">'._('Supprimer').'</a>';
			$xml .= '		</td>';
		} elseif( $_POST['type']=='tva' ) {
			$xml .= '	<td class="conditions" id="cdts-tva-0">';
			$xml .= '		<input class="action" type="button" name="add-cdt" id="add-cdt-0" value="'._('Ajouter une condition').'" onclick="addCdt(0, true , true, false);" title="'._('Ajouter une condition à cette liste').'" />';
			$xml .= '	</td>';
			$xml .= '	<td class="action">';
			$xml .= '		<input class="action btn-main" type="button" name="grp-tva-save" id="grp-tva-save-0" value="'._('Enregistrer').'" onclick="tvaSubmit(\'prc-cat\', 0)" title="'._('Enregistrer la liste de conditions').'" />';
			$xml .= '		<div class="save-load" id="save-load-tva-0">';
			$xml .= ' 			<img src="/admin/images/loader2.gif" alt="" />';
			$xml .= '		</div>';
			$xml .= '		<br /><a id="grp-tva-del-0" class="button btn-cancel" onclick="cancelNewForm();">'._('Supprimer').'</a>';
			$xml .= '	</td>';
		} elseif( $_POST['type']=='eco' ) {
			$xml .=  '	<td class="conditions" id="cdts-eco-0">';
			$xml .=  '		<input class="action" type="button" title="'._('Ajouter une condition à cette liste').'" onclick="addCdt(0, true, false, true);" value="'._('Ajouter une condition').'" id="add-cdt-0" name="add-cdt" />';
			$xml .=  '	</td>';
			$xml .=  '	<td class="action">';
			$xml .=  '		<input class="action" type="button" name="grp-eco-save" id="grp-eco-save-0" value="'._('Enregistrer').'" onclick="ecoSubmit(\'prc-cat\', 0)"; title="'._('Enregistrer la liste de conditions').'" />';
			$xml .= '		<div class="save-load" id="save-load-eco-0">';
			$xml .= ' 			<img src="/admin/images/loader2.gif" alt="" />';
			$xml .= '		</div>';
			$xml .=  '		<br /><a id="grp-eco-del-0" class="button" onclick="cancelNewForm();">'._('Supprimer').'</a>';
			$xml .=  '	</td>';
		}
		$xml .= ']]></cdt-line>';
		$xml .= '</result>';
	}

	print $xml;

