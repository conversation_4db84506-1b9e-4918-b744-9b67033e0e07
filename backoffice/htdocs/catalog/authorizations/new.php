<?php

	/**	\file new.php
	 *	Cette page permet la création d'une nouvelle règle d'accès au catalogue de produits
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_RIGHT_ADD');

	require_once('products.inc.php');
	require_once('fields.inc.php');
	require_once('prd/restrictions.inc.php');
	require_once('prd/restrictions.admin.inc.php');

	unset( $error );

	// Pour la sélection de site si tenant multisite
	if( isset($_GET['wst']) ){
		if( $_GET['wst']=='all' ){
			$_SESSION['websitepicker'] = '0';
		}else{
			$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
		}
	}
	$wst_id = isset($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ? $_SESSION['websitepicker'] : $config['wst_id'];

	if( IS_AJAX ){
		if( isset( $_POST['act'] ) ){
			if( !isset( $_POST['wst_id'] ) || (!is_numeric( $_POST['wst_id'] ) && !is_array( $_POST['wst_id'] ) ) ) $_POST['wst_id'] = $config['wst_id'];

			if (isset($_POST['usr_fld_id'])) {
				switch( $_POST['act'] ) {
					case 'edit': // Charge les lignes pour l'édition
						$_POST['usr_fld_id'] = trim($_POST['usr_fld_id']) == '' ? null : $_POST['usr_fld_id'];

						$restrictions = prd_restrictions_get( false, $_POST['wst_id'], null, false, $_POST['grp_id'], false, array('fld'=>$_POST['usr_fld_id'], 'value'=>$_POST['usr_value'])  );
						if( !$restrictions || !ria_mysql_num_rows( $restrictions ) ) exit;

						$cpt = 0;
						$nb = ria_mysql_num_rows( $restrictions );
						$first = true;
						while( $ru = ria_mysql_fetch_array( $restrictions ) ){
							print view_conditions_line( $cpt, $ru['fld_id'], $ru, false, $first, $nb+1);
							$cpt ++;
							$first = false;
						}
						// ajoute une ligne pour ajouter un element ou enregistrer les modifications
						print '	<tr class="add-rec-cnd dataID" title="'.$_POST['wst_id'].'-'.$_POST['usr_fld_id'].'-'.$_POST['usr_value'].'-'.$_POST['grp_id'].'">
									<td></td>
									<td colspan="3" class="align-left"><input type="submit" name="add-rec-cnd" value="'._('Ajouter une condition').'"/></td>
								</tr>';
						exit;

						break;
					case 'delgrp': // Supprime un groupe
						if( isset($_POST['usr_fld_id'],$_POST['usr_value'],$_POST['wst_id'],$_POST['grp_id']) ){

							$_POST['usr_fld_id'] = $_POST['usr_fld_id'] == '-1' ? null : $_POST['usr_fld_id'];
							$_POST['usr_value'] = $_POST['usr_value'] == '-1' ? null : $_POST['usr_value'];
							if( !prd_restrictions_del_by_criterion( $_POST['usr_fld_id'], $_POST['usr_value'], $_POST['wst_id'], $_POST['grp_id'], false ) ){
								$error = _('La condition n\'a pas pu être supprimée.');
							}

						}else{
							$error = _('Des données sont manquantes');
						}

						break;
					case 'allcat': // Donne tous les droits sur le catalogue
						prd_restrictions_del_by_criterion(  $_POST['usr_fld_id'],$_POST['usr_value'], $_POST['wst_id'], null, false );
						if( ! prd_restrictions_add( $_POST['usr_fld_id'], $_POST['usr_value'], _FLD_PRD_ID, '0', '!=',  prd_restrictions_get_next_group( $_POST['usr_fld_id'], $_POST['usr_value'], $_POST['wst_id'] ), false, $_POST['wst_id'] ) ){
							$error = _('Une erreur est survenue lors de la création de l\'accès.');
						}
						break;
					case 'del': // Supprime toutes les lignes
						prd_restrictions_del_by_criterion( $_POST['usr_fld_id'], $_POST['usr_value'], $_POST['wst_id'], $_POST['grp_id'], false );
						break;
					case 'upd': // Mise à jour d'un groupe
					case 'add': // Création

						$websites = is_numeric( $_POST['wst_id'] ) ? array( $_POST['wst_id'] ) : $_POST['wst_id'];

						$usr_value = null;
						$_POST['usr_fld_id'] = trim($_POST['usr_fld_id']) == '' ? null : $_POST['usr_fld_id'];
						if( $_POST['usr_fld_id'] == -1 || trim($_POST['usr_fld_id']) == '' ){ // tous le monde
							$usr_fld_id = null;
							$usr_value = null;
						}else if( $_POST['usr_fld_id'] == 0 ){ // anonyme
							$usr_fld_id = _FLD_USR_ID;
							$usr_value = 0;
						}else{ // les autres
							$usr_fld_id = $_POST['usr_fld_id'];

							if( isset( $_POST['usr_value'] ) && is_numeric( $_POST['usr_value'] ) ){
								$usr_value = $_POST['usr_value'];
							}elseif( isset( $_POST['usr_value_'.$usr_fld_id] ) ){
								$usr_value = $_POST['usr_value_'.$usr_fld_id];
							}

							// check si champ utilisateur et si l'utilisateur existe
							if( $usr_fld_id==_FLD_USR_ID && !gu_users_exists( $usr_value )){
								$error = _('L\'utilisateur sélectionné n\'existe pas.');
							}
						}

						if( sizeof( $websites ) <= 0 ) $error = _('Aucun site n\'est concerné par votre condition.');

						if( !isset( $error ) ){
							foreach( $websites as $wst_id ){
								if( !prd_restrictions_reach_all_catalog( $wst_id, $usr_fld_id, $usr_value ) ){ // si le contexte n'a pas tout le catalogue

									// dans le cas d'une création le groupe est inconnu et la clé est 0-0-0-0
									if( !isset( $_POST['grp_id'] ) ) {
										$grp_id = prd_restrictions_get_next_group( $usr_fld_id, $usr_value, $wst_id );
										$key = '0-0-0-0';
									}else{
										$grp_id = $_POST['grp_id'];
										$key = $wst_id.'-'.$usr_fld_id.'-'.$usr_value.'-'.$grp_id;

										// supprime toutes les lignes pour les recréer, beaucoup plus simple niveau controle que de faire des mises à jour de chaque ligne
										prd_restrictions_del_by_criterion( $usr_fld_id, $usr_value, $wst_id, $grp_id, false );
									}

									// parcours tous les champs du post si on tombe sur un select fld non vide on met a jour ou on créer
									foreach( $_POST as $pk => $fld_id ){
										if( preg_match('/^rec-new-fld-'.$key.'/', $pk) ){
											// si tous le catalogue
											if( $fld_id == -1 ){
												$fld_id = _FLD_PRD_ID;
												$symbol = '!=';
												$value = 0;
												// supprime toutes les lignes
												prd_restrictions_del_by_criterion( $usr_fld_id, $usr_value, $wst_id, false );
											} else {
												$cpt = preg_replace('/^rec-new-fld-'.$key.'-([0-9]+)/','$1', $pk);

												$symbol = isset($_POST['rec-new-symbol-'.$key.'-'.$cpt]) ? $_POST['rec-new-symbol-'.$key.'-'.$cpt] : false;
												$value = isset($_POST['rec-new-value-'.$key.'-'.$cpt]) ? $_POST['rec-new-value-'.$key.'-'.$cpt] : false;
											}

											if( $symbol!==false && $value!==false ){

												// si c'est un objet de type pointeur on test l'existance de l'objet fourni
												$rfld = fld_fields_get( $fld_id );
												if( $rfld && ria_mysql_num_rows( $rfld ) ){
													$fld = ria_mysql_fetch_array( $rfld );

													if( $fld['type_id'] == FLD_TYPE_DATE ){
														if( !isdate( $value ) ){
															$error = _('La date ne respecte pas le format français');
														}else{
															$value = dateparse( $value );
														}
													}else if( ($fld['type_id'] == FLD_TYPE_FLOAT || $fld['type_id'] == FLD_TYPE_INT ) && !is_numeric( $value ) ){
														$error = _('Le nombre renseigné n\'est pas valide.');
													}

													if( $fld['related_class'] == 1 && $value !== 0 && !prd_products_exists( $value )){ // produit
														$error = _('La référence du produit fourni n\'existe pas.');
													}else if( $fld['related_class'] == 3 && !prd_categories_exists( $value )){ // catégories
														$error = _('La référence de la catégorie fournie n\'existe pas.');
													}else if( $fld['related_class'] == 5 && !prd_brands_exists( $value ) ){ // marque
														$error = _('La référence de la marque fournie n\'existe pas.');
													}

													if( !isset( $error ) ){
														$ru_id = prd_restrictions_add( $usr_fld_id, $usr_value, $fld_id, $value, $symbol, $grp_id, false, $wst_id );
														if( !$ru_id ) {
															$error = _('Une erreur est survenue lors de la création d\'une restriction');
														}
													}
												}else{
													$error = _('Le champ n\'existe pas.');
												}
											}
											else{
												$error = _('Certaines informations sont manquantes.');
											}
										}
									}
								}else{
									$error = _('Le contexte que vous avez choisi a déjà tous les droits sur le catalogue, la création de la condition ne peut se faire.');
								}
							}
						}
						break;
				}

				if( $_POST['act'] == 'add' || $_POST['act'] == 'allcat'  || $_POST['act'] == 'delgrp' ){
					// ne print rien dans le cas d'un ajout le js rechargera la page s'il n'y a pas d'erreur
					if( isset( $error ) ){
						print '<div class="error">'.$error.'</div>';
					}
				}else{
					if( isset( $error ) ){
						print '<tr class="toremove"><td colspan="7" class="error">'.$error.'</td></tr>';
					}else{
						// on retourne la sorti html du groupe pour actualiser la page
						$restrictions = prd_restrictions_get( false, $_POST['wst_id'], null, false, $_POST['grp_id'], false, array('fld'=>$_POST['usr_fld_id'], 'value'=>$_POST['usr_value'])  );
						if( !$restrictions || !ria_mysql_num_rows( $restrictions ) ) exit;
						$first = true;
						while( $ru = ria_mysql_fetch_array( $restrictions ) ){
							print view_conditions_line_readonly( $ru, $first, ria_mysql_num_rows( $restrictions ) );
							$first = false;
						}
					}
				}
			}
			exit;
		}

		// recharge la ligne avec les symbols / valeurs possible
		// si requete ajax avec le param $fld_selected
		if( isset( $_GET['fld_selected'], $_GET['cpt'], $_GET['key'] ) && is_numeric( $_GET['cpt'] ) && is_numeric( $_GET['fld_selected'] ) ){
			print view_conditions_line( $_GET['cpt'], $_GET['fld_selected'], false, $_GET['key'], false );
			exit;
		}
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Créer un nouvel accès').' - '._('Droits d\'accès') . ' - ' . _('Catalogue'));
	require_once('admin/skin/header.inc.php');
?>

	<h2><?php print _('Créer un nouvel accès');?></h2>

	<?php if( isset($_GET['add']) ){ ?>
		<div class="success"><?php print _('Ajout de la condition réussi.');?></div>
	<?php } ?>
	<?php if( isset($error) ){ ?>
		<div class="error"><?php print htmlspecialchars($error); ?></div>
	<?php } ?>

	<div id="authorizations">
		<form method="post" action="new.php">

			<table class="nocaption large" id="table-new-autorization">
				<tbody>
					<?php
						$wst = wst_websites_get( 0, false, null, true );
						if(ria_mysql_num_rows($wst) > 1) {
						?>
							<tr>
								<td><label><?php print _('Sur les sites :');?></label></td>
								<td>
								<?php
								while( $r = ria_mysql_fetch_array($wst) ){
									print '<input type="checkbox" class="checkbox" name="wst_id[]" id="wst_id-'.$r['id'].'" value="'.$r['id'].'" /> <label for="wst_id-'.$r['id'].'">'.$r['name'].'</label><br/>';
								} ?>
								</td>
							</tr>
				<?php	} ?>

				<?php
				$rflds = fld_fields_get(0, 0, 0, 0, 0, 0, null, array(), false, array(), null, CLS_USER, null, false, null, null, false, null, false, true);
				?>
					<tr>
						<td id="td-new-autorization"><?php print _('Contexte :');?></td>
						<td>
							<select name="usr_fld_id">
								<option id="rec-all" value="-1" <?php print isset($_GET['usr_fld_id']) && $_GET['usr_fld_id'] ==  -1 ? 'selected="selected"':'' ?>><?php print _('Tout le monde'); ?></option>
								<option id="rec-ano" value="0" <?php print isset($_GET['usr_fld_id']) && $_GET['usr_fld_id'] == 0 ? 'selected="selected"':'' ?>><?php print _('Connexion anonyme'); ?></option>
								<?php
									if( $rflds && ria_mysql_num_rows($rflds) ){
										while($fld = ria_mysql_fetch_assoc($rflds) ){
											?>
											<option id="rec-<?php print $fld['id']; ?>" value="<?php print $fld['id']; ?>" <?php print isset($_GET['usr_fld_id']) && $_GET['usr_fld_id'] ==  $fld['id'] ? 'selected="selected"':'' ?>><?php print $fld['name'];?></option>
											<?php
										}
									}
								?>
								<option id="rec-prf" value="<?php print _FLD_USR_PRF; ?>" <?php print isset($_GET['usr_fld_id']) && $_GET['usr_fld_id'] ==  _FLD_USR_PRF ? 'selected="selected"':'' ?>><?php print _('Profil de l\'utilisateur');?></option>
								<option id="rec-cac" value="<?php print _FLD_USR_CAC; ?>" <?php print isset($_GET['usr_fld_id']) && $_GET['usr_fld_id'] ==  _FLD_USR_CAC ? 'selected="selected"':'' ?>><?php print _('Catégorie comptable de l\'utilisateur');?></option>
								<option id="rec-prc" value="<?php print _FLD_USR_PRC; ?>" <?php print isset($_GET['usr_fld_id']) && $_GET['usr_fld_id'] ==  _FLD_USR_PRC ? 'selected="selected"':'' ?>><?php print _('Catégorie tarifaire de l\'utilisateur');?></option>
								<option id="rec-usr" value="<?php print _FLD_USR_ID; ?>" <?php print isset($_GET['usr_fld_id']) && $_GET['usr_fld_id'] ==  _FLD_USR_ID ? 'selected="selected"':'' ?>><?php print _('Utilisateur');?></option>
								<option id="rec-adr" value="<?php print _FLD_ADR_CNT_CODE; ?>" <?php print isset($_GET['usr_fld_id']) && $_GET['usr_fld_id'] ==  _FLD_ADR_CNT_CODE ? 'selected="selected"':'' ?>><?php print _('Pays de facturation');?></option>
								<option id="rec-pay" value="<?php print _FLD_USR_PAY_ID; ?>" <?php print isset($_GET['usr_fld_id']) && $_GET['usr_fld_id'] ==  _FLD_USR_PAY_ID ? 'selected="selected"':'' ?>><?php print _('Moyen de règlement');?></option>
							</select>

							<span class="rec-lbl"><?php print _('égal à');?></span>

							<!-- pour les champs avancé -->
							<?php
							if( $rflds && ria_mysql_num_rows($rflds) ){
								ria_mysql_data_seek($rflds, 0);
								while($fld = ria_mysql_fetch_assoc($rflds) ){
									?>
									<input class="rec-value value-rec-<?php print $fld['id']; ?>" type="text" name="usr_value_<?php print $fld['id']; ?>" value="<?php print isset($_GET['usr_fld_id']) && $_GET['usr_fld_id'] ==  $fld['id'] && isset($_GET['usr_value']) ? $_GET['usr_value'] : '' ?>"/>
									<?php
								}
							}
							?>

							<!-- si profil (_FLD_USR_PRF) -->
							<select class="rec-value value-rec-prf" name="usr_value_<?php print _FLD_USR_PRF; ?>">
							<?php
							if( $rprf = gu_profiles_get() ){
								while( $prf = ria_mysql_fetch_array($rprf) ){
								?>
								<option value="<?php print $prf['id']; ?>" <?php print isset($_GET['usr_value']) && $_GET['usr_value'] == $prf['id'] ? 'selected="selected"':'' ?>><?php print htmlspecialchars($prf['name']); ?></option>
								<?php
								}
							}
							?>
							</select>
							<!-- si catégorie tarifaire (_FLD_USR_PRC) -->
							<select class="rec-value value-rec-prc" name="usr_value_<?php print _FLD_USR_PRC; ?>">
							<?php
							if( $rprc = prd_prices_categories_get() ){
								while( $prc = ria_mysql_fetch_array($rprc) ){
								?>
								<option value="<?php print $prc['id']; ?>" <?php print isset($_GET['usr_value']) && $_GET['usr_value'] == $prc['id'] ? 'selected="selected"':'' ?>><?php print htmlspecialchars($prc['name']); ?></option>
								<?php
								}
							}
							?>
							</select>
							<!-- catégorie comptable (si _FLD_USR_CAC)-->
							<select class="rec-value value-rec-cac" name="usr_value_<?php print _FLD_USR_CAC; ?>">
							<?php
							if( $rcac = gu_accounting_categories_get() ){
								while( $cac = ria_mysql_fetch_array($rcac) ){
								?>
								<option value="<?php print $cac['id']; ?>" <?php print isset($_GET['usr_value']) && $_GET['usr_value'] == $cac['id'] ? 'selected="selected"':'' ?>><?php print htmlspecialchars($cac['name']); ?></option>
								<?php
								}
							}
							?>
							</select>
							<!-- pays de facturation (si _FLD_ADR_CNT_CODE)-->
							<select class="rec-value value-rec-adr" name="usr_value_<?php print _FLD_ADR_CNT_CODE; ?>">
							<?php
							if( $rcnt = sys_countries_get( $ctn_code='', $cnt_code=false ) ){
								while( $cnt = ria_mysql_fetch_array($rcnt) ){
								?>
								<option value="<?php print $cnt['code']; ?>" <?php print isset($_GET['usr_value']) && $_GET['usr_value'] == $cnt['code'] ? 'selected="selected"':'' ?>><?php print htmlspecialchars($cnt['name']); ?></option>
								<?php
								}
							}
							?>
							</select>
							<!-- moyen de paiement (si _FLD_USR_PAY_ID)-->
							<select class="rec-value value-rec-pay" name="usr_value_<?php print _FLD_USR_PAY_ID; ?>">
							<?php
								$r_pay = ord_payment_types_get();
								if ($r_pay) {
									while ($pay = ria_mysql_fetch_assoc($r_pay)) {
										?>
										<option value="<?php print $pay['id']; ?>" <?php print isset($_GET['usr_value']) && $_GET['usr_value'] == $pay['id'] ? 'selected="selected"':'' ?>><?php print htmlspecialchars($pay['name']); ?></option>
										<?php
									}
								}
							?>
							</select>
							<!-- client spécifique (moteur de recherche avec autocomplétion sur un champ texte) (si _FLD_USR_ID) -->

							<input class="rec-value value-rec-usr" type="text" name="usr_value_txt" value="<?php print isset($_GET['usr_fld_id']) && $_GET['usr_fld_id'] ==  _FLD_USR_ID && isset($_GET['usr_value_txt']) ? $_GET['usr_value_txt'] : '' ?>"/>
							<input class="rec-action action-rec-usr" type="button" name="choose-rec-usr" id="choose-rec-usr" value="<?php print _('Choisir'); ?>" />
							<input class="val-autocomplete-usr" type="hidden" name="usr_value_<?php print _FLD_USR_ID; ?>" value="<?php print isset($_GET['usr_fld_id']) && $_GET['usr_fld_id'] ==  _FLD_USR_ID && isset($_GET['usr_value']) ? $_GET['usr_value'] : '' ?>"/>
						</td>
					</tr>
				</tbody>
			</table>


			<table class="large authorizations" id="table-conditions-authorization">
				<caption><?php print _('Conditions');?></caption>
				<thead>
					<tr>
						<th id="rec-fld" colspan="2" class="thead-none"><?php print _('Propriété(s)');?></th>
						<th id="rec-value" colspan="2" class="thead-none"><?php print _('Valeur');?></th>
					</tr>
				</thead>
				<tfoot>
					<tr class="dataID" title="0-0-0-0">
						<td colspan="4" class="tdleft">
							<input type="submit" value="<?php print _('Ajouter une condition');?>" name="add-rec-cnd" title="<?php print _('Ajouter une nouvelle condition');?>" />
						</td>
					</tr>
				</tfoot>
				<tbody>
					<?php
						print view_conditions_line( 0, false, false, false, false );
					?>
				</tbody>
			</table>

			<div class="actions">
				<input type="submit" value="<?php print _('Ajouter');?>" name="save-new-rec" class="btn-main" />
			</div>
		</form>
	</div>
<?php
	require_once('admin/skin/footer.inc.php');
?>