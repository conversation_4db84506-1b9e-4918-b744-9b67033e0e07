<?php

	/**	\file edit.php
	 *	Cette page permet la modification des règles de droits d'accès au catalogue produit pour un groupe d'utilisateurs.
	 */

	// Vérifie que l'utilisateur en cours à accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_RIGHT');

	require_once('products.inc.php');
	require_once('fields.inc.php');
	require_once('prd/restrictions.inc.php');
	require_once('prd/restrictions.admin.inc.php');
	
	unset( $error );	
	if( !isset( $_GET['wst_id'],$_GET['usr_fld_id'],$_GET['usr_value'] ) || !is_numeric( $_GET['wst_id'] ) ){
		header('Location: index.php');
		exit;
	}
	
	$wst_id = $_GET['wst_id']; 
	$usr_fld_id = isset($_GET['usr_fld_id']) && trim($_GET['usr_fld_id']) != '' ? $_GET['usr_fld_id'] : null; 
	$usr_value = isset($_GET['usr_value']) && trim($_GET['usr_value']) != '' ? $_GET['usr_value'] : null;
		
	$user = false;
	if( $usr_fld_id == _FLD_USR_ID && $usr_value > 0 ){
		$ruser = gu_users_get( $usr_value ); 
		if( !$ruser || !ria_mysql_num_rows($ruser)){
			header('Location: index.php');
			exit;
		}
		$user = ria_mysql_fetch_array($ruser);
	}
	
	define('ADMIN_PAGE_TITLE', 'Conditions d\'accès pour '.view_conditions_name( $usr_fld_id, $usr_value ).' - '._('Droits d\'accès') . ' - ' . _('Catalogue'));
	require_once('admin/skin/header.inc.php');

	if( isset($error) ){
		print '<div class="error">'. htmlspecialchars($error) . '</div>';
	}
	print '<h2>Conditions d\'accès pour ' . htmlspecialchars(view_conditions_name( $usr_fld_id, $usr_value )) . '</h2>'; ?>
	
	<form action="/admin/catalog/authorizations/edit.php?wst_id=<?php print $wst_id . '&amp;usr_fld_id=' . $usr_fld_id . "&amp;usr_value=" . $usr_value ?>" method="post">
		<input type="hidden" name="usr_value_txt" value="<?php print $user ? $user['email']:''; ?>"/>
		<?php 
			$restrictions = prd_restrictions_get( false, $wst_id, null, false, false, false, array('fld'=>$usr_fld_id, 'value'=>$usr_value) );
			print view_conditions_table( $restrictions );
		?>		
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>