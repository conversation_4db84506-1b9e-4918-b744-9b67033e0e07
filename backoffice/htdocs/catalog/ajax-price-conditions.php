<?php

	/**	\file ajax-price-conditions.php
	 *	Ce fichier génère le contenu du tableau "Tarifs par catégorie tarifaire", de la fiche produit (onglet Tarification)
	 */

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRICE_PRODUCTS');

	require_once('fields.inc.php');
	require_once('prices.inc.php');

	$print = '';

	// Génère le code HTML du tableau all-price-prd contenu dans la fiche produit, qui affiche les tarifs du produit par catégorie tarifaire
	if( isset($_GET['get-all-price']) ){
		$array_prc = array();

		// Get default price for product
		$price_ht_default = 0;
		if( $rprice = prc_prices_get( 0, NEW_PRICE, false, false, false, $_GET['prd'], false, false, false, null, false, 1, 1, false, false, array(), array(), null, false, 0, null, 0, 0, false, 0, false, false, false, true) ) {
			if( $price = ria_mysql_fetch_array($rprice) ){
				$price_ht_default = $price['value'];
			}
		}

		// Charge la catégorie tarifaire par défaut séparément pour la mettre en premier
		$r_prc = prd_prices_categories_get( $config['default_prc_id'] );
		if( $r_prc && ria_mysql_num_rows($r_prc) ){
			$prc = ria_mysql_fetch_array($r_prc);
			$prc['default'] = true;
			$array_prc[ $prc['id'] ] = $prc;
		}

		// Charge les autres catégories tarifaires pour les mettre à la suite
		$r_prc = prd_prices_categories_get( 0, false, false, false, true, ['name' => 'asc'] );
		if( $r_prc && ria_mysql_num_rows($r_prc) ){
			while( $prc = ria_mysql_fetch_array( $r_prc ) ){
				if( $prc['id'] != $config['default_prc_id'] ){
					$prc['default'] = false;
					$array_prc[ $prc['id'] ] = $prc;
				}
			}
		}

		if( sizeof($array_prc) ){

			// Détermine si le produit est synchronisé pour savoir si ses tarifs seront modifiables
			$prd_is_sync = prd_products_get_is_sync( $_GET['prd'] );
			$have_editable_price = false;

			// TVA avec condition pour le produit
			$tva_rate = 1;
			$rtva = prc_tvas_get(0, false, $_GET['prd']);

			if( ria_mysql_num_rows($rtva) ){
				$tva = ria_mysql_fetch_assoc($rtva);
				$tva_rate = $tva['rate'];
			}

			foreach( $array_prc as $prc_id => $prc ){

				if( $rprice = prc_prices_get( 0, NEW_PRICE, false, false, false, $_GET['prd'], false, false, false, null, false, 1, 1, false, false, array( array( 'fld' => _FLD_USR_PRC, 'symbol' => '=', 'value' => $prc_id )), array(), null, false, 0, null, 0, 0, false, 0, false,	false, false, true) ) {

					// Les tarifs ne seront modifiables que si la catégorie tarifaire et le produit ne sont pas synchronisés
					$price_ht = $price_ht_default;
					if( $price = ria_mysql_fetch_assoc($rprice) ){
						$price_ht = $price['value'];
					}

					$price_ttc = $price_ht * $tva_rate;
					$print .= '<tr'.( $prc['default'] ? ' class="row_tarif_resume_default"' : '').' data-prc-id="'.$prc['id'].'" data-ttc="'.$prc['ttc'].'">';

					// Nom de la catégorie tarifaire
					$print .= '<td class="row_tarif_resume" headers="prc_resume_name" data-label="'._('Catégorie :').' ">';
					$print .= htmlspecialchars( $prc['name'] );
					$print .= '</td>';

					// Prix HT du produit dans cette catégorie tarifaire.
					$print .= '<td class="row_tarif_resume align-right" headers="prc_resume_ht" data-label="'._('Prix HT :').' ">';

					if( $prd_is_sync ){
						$print .= ria_number_format( $price_ht, NumberFormatter::CURRENCY, 2, $prc['money_code'] );
					}else{
						$print .= '<input type="number" class="number" name="price-ht['.$prc['id'].']" id="price-ht-'.$prc['id'].'"
							value="'.number_format($price_ht, 2, '.', '').'" min="0" step="0.01" onchange="updPriceTTC( this )"
							data-field-ttc="#price-ttc-'.$prc['id'].'" data-prc-ttc="'.$prc['ttc'].'"> '.htmlspecialchars( $prc['money_code'] );
						$have_editable_price = true;
					}

					$print .= '</td>';

					// Prix TTC du produit dans cette catégorie tarifaire.
					$print .= '<td class="row_tarif_resume align-right" headers="prc_resume_ttc" data-label="'._('Prix TTC :').' ">';

					if( $prd_is_sync ){
						$print .= ria_number_format( $price_ttc, NumberFormatter::CURRENCY, 2, $prc['money_code'] );
					}else{
						$print .= '<input type="number" class="number" name="price-ttc['.$prc['id'].']" id="price-ttc-'.$prc['id'].'"
							value="'.number_format($price_ttc, 2, '.', '').'" min="0" step="0.01" onchange="updPriceHT( this )"
							data-field-ht="#price-ht-'.$prc['id'].'" data-prc-ttc="'.$prc['ttc'].'"> '.htmlspecialchars( $prc['money_code'] );
					}

					$print .= '</td></tr>';


				}
			}
			// Si le produit à au moins un tarif modifiable, affiche les boutons d'action
			if( $have_editable_price ){

				$print .= '
					<tr><td colspan="3" class="align-right">
						<input type="submit" name="save-prices" value="'._('Enregistrer').'">
					</td></tr>
				';
			}

		}else{
			$print .= '
				<tr>
					<td class="row_tarif_resume row_tarif_resume_default" headers="prc_resume_name" colspan="3">
						'._('Aucune information tarifaire disponible').'
					</td>
				</tr>
			';
		}

	// Ce qui suit permet l'ajout d'un tarif conditionnel
	}elseif( !isset($_POST['fld'], $_POST['id'], $_POST['nb'], $_POST['new'], $_POST['tva']) ){
		return false;
	} else {

		$is_new = $_POST['new']=='1' || $_POST['new']=='true';
		$is_tva = $_POST['tva']=='1' || $_POST['tva']=='true';
		$is_eco = $_POST['ecotaxe']=='1' || $_POST['ecotaxe']=='true';

		// Détermine s'il s'agit d'une condition d'un nouveau tarif ou d'une nouvelle TVA
		$new = $_POST['new'] ? '-new' : '';
		$tva = $_POST['tva']=='1' ? '-tva' : '';
		$eco = $_POST['ecotaxe']=='1' ? '-eco' : '';

		// Complément sur les balises name
		$bname = '';
		$bname .= $_POST['tva']=='true' ? '-tva' : '';
		$bname .= $_POST['ecotaxe']=='true' ? '-eco' : '';
		$bname .= $_POST['new']=='true' ? '-new' : '-'.$_POST['id'];

		// Complément sur les balises id
		$bid = $bname.'-'.$_POST['nb'];

		// Récupère les informations sur le champ libre
		if( $is_tva ){
			$r_fld = fld_fields_get( $_POST['fld'], 0, 0, 0, 0, 0, null, array("prc_priority"), true, array(), null, null, false, true, true);
		}else{
			$r_fld = fld_fields_get( $_POST['fld'], 0, 0, 0, 0, 0, null, array("prc_priority"), true, array(), null, null, true, true);
		}

		// On vérifie qu'un champ a bien été retourné
		if( !$r_fld || !ria_mysql_num_rows($r_fld) ){
			return false;
		}

		$is_price = 'false';
		if( $_POST['tva']=='0' && $_POST['ecotaxe']=='0' ){
			$is_price = 'true';
		}

		$fld = ria_mysql_fetch_array($r_fld);

		// On récupére les symboles possibles pour ce type de champ libre (Hormis le type booléen)
		$r_symbols = prc_symbols_get( $fld['type_id'] );

		// Modifie les symboles si le champ libre est de type date
		if( isset($_POST['selectDate']) && ($_POST['selectDate']==CUSTOM_DATE || $_POST['selectDate']==CUSTOM_PERIOD) ){
			// On affiche la sélection des symboles
			$date_type = isset($_POST['selectDate']) ? $_POST['selectDate'] : 1;
			$r_symbols = prc_symbols_get( $fld['type_id'], $date_type );
		}
		if( $r_symbols==false || ria_mysql_num_rows($r_symbols)==0 ){
			return false;
		}
		if( $fld['type_id']!=FLD_TYPE_BOOLEAN_YES_NO && $fld['type_id']!=FLD_TYPE_DATE ){
			// On affiche la sélection des symboles
			$print .= '<select class="sbl-select" name="sbl'.$bname.'['.$_POST['nb'].']" id="sbl'.$bid.'" onchange="chargInputVal('.$_POST['id'].', '.$_POST['nb'].', '.( $is_new ? 'true' : 'false' ).', '.( $is_tva ? 'true' : 'false' ).', '.( $is_eco ? 'true' : 'false' ).', '.( $_POST['addCdt']=='1' ? 'true' : 'false' ).')">';
			while( $symbol = ria_mysql_fetch_array($r_symbols) ){
				$print .= '<option value="'.htmlspecialchars($symbol['symbol']).'" '.( isset($_POST['sbl']) && trim($_POST['sbl'])==$symbol['symbol'] ? 'selected="selected"' : '' ).'">'.htmlspecialchars( $symbol['desc'] ).'</option>';
			}
			$print .= '</select>';
		}

		// Affiche le composant contenant la valeur de la condition selon le type de champ libre
		switch( $fld['type_id'] ){
		case FLD_TYPE_REFERENCES_ID : // Le champ a une existance physique
			if( fld_fields_get_physical_name($_POST['fld']) == 'usr_prc_id' ){
				// Il s'agit d'une condition sur la catégorie tarifiare d'un utilisateur
				$cat_price = prd_prices_categories_get();
				if( $cat_price!=false ){
					$print .= '	<select name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'">';
					while( $cat = ria_mysql_fetch_array($cat_price) ){
						$print .= '		<option value="'.$cat['id'].'">'.htmlspecialchars($cat['name']).'</option>';
					}
					$print .= '	</select>';
				} else {
					$print .= '<input type="text" name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'" value="" />';
				}
			} elseif( fld_fields_get_physical_name($_POST['fld']) == 'cly_col_id' ){
				$rcol = prd_colisage_types_get();
				if( $rcol!=false ){
					$print .= '	<select name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'">';
					while( $col = ria_mysql_fetch_array($rcol) ){
						$print .= '		<option value="'.$col['id'].'">'.htmlspecialchars($col['name']).'</option>';
					}
					$print .= '	</select>';
				} else {
					$print .= '<input type="text" name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'" value="" />';
				}
			} elseif( fld_fields_get_physical_name($_POST['fld']) == 'usr_cac_id' ){
				$racc = gu_accounting_categories_get();
				if( $racc!=false ){
					$print .= '	<select name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'">';
					while( $acc = ria_mysql_fetch_array($racc) ){
						$print .= '		<option value="'.$acc['id'].'">'.htmlspecialchars($acc['name']).'</option>';
					}
					$print .= '	</select>';
				} else {
					$print .= '<input type="text" name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'" value="" />';
				}
			} else {
				$print .= '<input type="text" name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'" value="" />';
			}
			break;
		case FLD_TYPE_TEXT : // Type acceptant les chaines de caractères
				$print .= '<input type="text" name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'" value="" />';
				break;
			case FLD_TYPE_TEXTAREA : // Type pour les zones de texte
				$print .= '<textarea name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'" rows="5" cols="50"></textarea>';
				break;
			case FLD_TYPE_INT : // Type n'accpetant que les entiers
				$print .= '<input type="text" name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'" value="" />';
				if( isset($_POST['sbl']) && trim($_POST['sbl'])=="><" ){
					$print .= '&nbsp;et&nbsp;<input type="text" name="val-cdt'.$bname.'-2['.$_POST['nb'].']" id="val-cdt'.$bid.'-2" value="" />';
				}
				$print .= '<div class="clear"></div><span class="info-fld">'._('Seuls les nombres entiers sont acceptés pour ce type de condition.').'</span>';
				break;
			case FLD_TYPE_FLOAT : // Type n'acceptant que les décimals
				$print .= '<input type="text" name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'" value="" />';
				if( isset($_POST['sbl']) && trim($_POST['sbl'])=="><" ){
					$print .= '&nbsp;et&nbsp;<input type="text" name="val-cdt'.$bname.'-2['.$_POST['nb'].']" id="val-cdt'.$bid.'-2" value="" />';
				}
				$print .= '<div class="clear"></div><span class="info-fld">'._('Seules les valeurs numériques sont acceptées pour ce type de condition.').'</span>';
				break;
			case FLD_TYPE_SELECT :
				$values = fld_restricted_values_get( 0, $_POST['fld'] );
				$print .= '<select name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'">';
				while( $val = ria_mysql_fetch_array($values) ){
					$print .= '<option value="'.$val['name'].'" title="'.$val['name'].'">'.htmlspecialchars($val['name']).'</option>';
				}
				$print .= '</select>';
				break;
			case FLD_TYPE_SELECT_HIERARCHY :
				// On récupère les valeurs pour le select hiérarchique
				$values = fld_restricted_values_get( 0, $_POST['fld'] );
				if( $values!==false ){
					while( $r = ria_mysql_fetch_array($values) ){
						$name_val = $r['name'];
						$id = $r['id'];
						while( $vals2 = fld_restricted_values_get( $r['parent'] ) ){
							if( $r = ria_mysql_fetch_array($vals2) ){
								$name_val = $r['name'].' >> '.$name_val;
							}
						}
						$all_vals[] = array( 'id' => $id, 'name' => $name_val );
					}

					$all_vals = array_msort( $all_vals, array( 'name'=>SORT_ASC ) );
					// On affiche ce select
					$print .= '<select name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'">';
					foreach( $all_vals as $val ){
						$print .= '<option value="'.$val['id'].'" title="'.$val['name'].'">'.htmlspecialchars( $val['name'] ).'</option>';
					}
					$print .= '</select>';
				}
				break;
			case FLD_TYPE_BOOLEAN_YES_NO :
				$print .= '<input class="bl-cdt" type="radio" name="val-cdt'.$bname.'['.$_POST['nb'].']" id="oui'.$bid.'" value="oui" /><label for="oui'.$bid.'">'._('Oui').'</label><br />';
				$print .= '<input class="bl-cdt" type="radio" name="val-cdt'.$bname.'['.$_POST['nb'].']" id="non'.$bid.'" value="non" checked="checked" /><label for="non'.$bid.'">'._('Non').'</label>';
				break;
			case FLD_TYPE_DATE :
				$print .= '<div class="val-fld-date">';
				$print .= '<input type="radio" name="choose-date'.$bname.'" id="choose-date'.$bid.'-1" value="1" checked="checked" />';
				$print .= '<select name="sbl-date'.$bname.'-1" id="sbl'.$bid.'-date-1" onclick="changeChooseDate(\'choose-date'.$bid.'-1\')">';
				$print .= '<option value="=">'._('Égal à').'</option><option value="!=">'._('Différent de').'</option>';
				$print .= '</select>';
				$print .= '<select name="val-cdt-date'.$bname.'-1" id="val-cdt'.$bid.'-date-1" onclick="changeChooseDate(\'choose-date'.$bid.'-1\')">';
				$print .= '<option value="-1"></option>';
				$print .= '<option value="'.DAY_AND_MONTH_IS_NOW.'" '.( isset($_POST['selectDate']) && $_POST['selectDate']==DAY_AND_MONTH_IS_NOW ? 'selected="selected"' : '' ).'>'._('Aujourd\'hui').'</option>';
				$print .= '<option value="'.WEEK_IS_NOW.'" '.( isset($_POST['selectDate']) && $_POST['selectDate']==WEEK_IS_NOW ? 'selected="selected"' : '' ).'>'._('Cette semaine').'</option>';
				$print .= '<option value="'.MONTH_IS_NOW.'" '.( isset($_POST['selectDate']) && $_POST['selectDate']==MONTH_IS_NOW ? 'selected="selected"' : '' ).'>'._('Ce mois-ci').'</option>';
				$print .= '</select>';
				$print .= '</div>';
				$print .= '<div class="val-fld-date">';
				$print .= '<input type="radio" name="choose-date'.$bname.'" id="choose-date'.$bid.'-2" value="2" />';
				$print .= '<select name="sbl-date'.$bname.'-2" id="sbl'.$bid.'-date-2" onclick="changeChooseDate(\'choose-date'.$bid.'-2\')">';
				$print .= '<option value="=">'._('Égal à').'</option><option value="!=">'._('Différent de').'</option>';
				$print .= '</select>';
				$print .= '<input class="datepicker date" type="text" name="val-cdt-date'.$bname.'-2" id="val-cdt'.$bid.'-date-2" value="" onfocus="changeChooseDate(\'choose-date'.$bid.'-2\')" autocomplete="off" />';
				$print .= '<span class="info-fld">('._('Date personnalisée').')</span>';
				$print .= '</div>';
				$print .= '<div class="val-fld-date">';
				$print .= '<input type="radio" name="choose-date'.$bname.'" id="choose-date'.$bid.'-3" value="3" />';
				$print .= '&nbsp;'._('Est compris entre').'&nbsp;';
				$print .= '<input class="datepicker date" type="text" name="val-cdt-date'.$bname.'-3" id="val-cdt'.$bid.'date-3" value="" onfocus="changeChooseDate(\'choose-date'.$bid.'-3\')" autocomplete="off" />';
				$print .= '&nbsp;et&nbsp;<input class="datepicker date" type="text" name="val-cdt-date'.$bname.'-3-2" id="val-cdt'.$bid.'-date-3-2" value="" onfocus="changeChooseDate(\'choose-date'.$bid.'-3\')" autocomplete="off" />';
				$print .= '<span class="info-fld">('._('Période personnalisée').')</span>';
				$print .= '</div>';
				break;
			default :
				$print .= '<input type="text" name="val-cdt'.$bname.'['.$_POST['nb'].']" id="val-cdt'.$bid.'" value="" />';
				break;
		}
	}
	print $print;
