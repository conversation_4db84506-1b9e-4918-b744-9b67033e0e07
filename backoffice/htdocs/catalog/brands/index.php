<?php

	/**	\file index.php
	 *	Ce fichier affiche la liste des marques et permet plusieurs actions :
	 *	- Ouverture d'une fiche marque
	 *	- Ajout d'une marque
	 *	- Suppression d'une marque
	 *	- Export CSV de la liste des marques
	 *	- Modification de l'ordre de tri
	 *	- Ajout d'un modèle de saisie
	 *	- Publication / Dépublication
	 */

	require_once('brands.inc.php');
	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_BRAND');

	if( isset($_POST['delete']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_BRAND_DEL');
	}

	// Suppression
	if( isset($_POST['delete']) ){
		// Suppression d'une ou plusieurs marques
		if( isset($_POST['brd']) && is_array($_POST['brd']) ){
			foreach( $_POST['brd'] as $p ){
				// Seules les marques non-synchronisées peuvent être supprimées
				$brd = ria_mysql_fetch_array(prd_brands_get($p));
				if( !$brd['is_sync'] ){
					prd_brands_del($p);
				}
			}
			header('Location: index.php');
			exit;
		}
	}

	// Publication des marques
	if( isset($_POST['publish']) ){
		if( isset($_POST['brd']) && !prd_brands_publish($_POST['brd']) ){
			$error = _("Une erreur inattendue s'est produite lors de la publication des marques.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
		}

		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	// Dépublication des marques
	if( isset($_POST['unpublish']) ){
		if( !prd_brands_unpublish($_POST['brd']) )
			$error = _("Une erreur inattendue s'est produite lors de la dé-publication des marques.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");

		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	// Modification de la méthode de tri
	if( isset($_POST['orderby'],$_POST['order']) ){
		prd_brands_order_update( $_POST['order'] );
		header('Location: index.php');
		exit;
	}

	$ordered = prd_brands_order_get();

	// Note : le paramètre mdl a priorité sur le paramètre fld
	$have_fld_filter = $have_mdl_filter = false;
	$count_brands = 0;

	// Récupère un éventuel modèle de saisie sur lequel filtrer le résultat
	if( isset($_GET['mdl']) && is_numeric($_GET['mdl']) && $_GET['mdl']>0 ){
		if( $rmodel = fld_models_get($_GET['mdl']) ){
			$model = ria_mysql_fetch_array($rmodel);
			if( $model['cls_id']==CLS_BRAND ){
				$brands = fld_models_get_objects( $_GET['mdl'] );
				$count_brands = ria_mysql_num_rows($brands);
				$have_mdl_filter = true;
			}
		}
	}

	if( !$have_mdl_filter ){
		// Récupère un éventuel champ libre sur lequel filtrer le résultat
		if( isset($_GET['fld']) && is_numeric($_GET['fld']) && $_GET['fld']>0 ){
			if( $rfield = fld_fields_get($_GET['fld']) ){
				$field = ria_mysql_fetch_array($rfield);
				if( $field['cls_id']==CLS_BRAND ){
					$brands = fld_fields_get_objects( $_GET['fld'] );
					$count_brands = ria_mysql_num_rows($brands);
					$have_fld_filter = true;
				}
			}
		}
	}

	// Aucun filtre
	if( !$have_fld_filter && !$have_mdl_filter ){
		$brands = prd_brands_get();
		$count_brands = ria_mysql_num_rows($brands);
	}

	// Redirige vers l'export "Excel" si cette action est demandée
	if( isset($_POST['export']) ){
		if( $have_mdl_filter ){
			header( 'Location: export.php?mdl='.$_GET['mdl'] );
		}elseif( $have_fld_filter ){
			header( 'Location: export.php?fld='.$_GET['fld'] );
		}else{
			header( 'Location: export.php' );
		}
		exit;
	}

	// Ajout d'une marque
	if( isset($_POST['brdname']) ){
		header('Location: edit.php?brd=0&brdname='.urlencode($_POST['brdname']));
		exit;
	}

	// Affiche une notice d'information si des produits n'ont pas de marque associée.
	$no_brand = prd_products_get_count(0, -1);
	$no_brand_published = prd_products_get_count(0, -1, true);

	$notice = '';

	if( $no_brand && gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW') ){
		$notice = $no_brand != $no_brand_published
			? sprintf(_('Votre catalogue comprend <b>%d</b> produits pour lesquels aucune marque n\'est définie, dont <b>%d</b> sont publiés.'), ria_number_format($no_brand, NumberFormatter::DECIMAL), ria_number_format($no_brand_published, NumberFormatter::DECIMAL))
			: sprintf(_('Votre catalogue comprend <b>%d</b> produits pour lesquels aucune marque n\'est définie, tous ces produits sont publiés.'), ria_number_format($no_brand, NumberFormatter::DECIMAL));
	}

	define('ADMIN_PAGE_TITLE', _('Marques') . ' - ' . _('Catalogue'));

	require_once('admin/skin/header.inc.php');

?>

<form action="index.php" method="post">

<h2>
	<?php print _('Marques'); ?>
	(<?php print ria_number_format($count_brands, NumberFormatter::DECIMAL); ?>)

	<input type="submit" name="export" value="<?php print _('Exporter');?>" title="<?php print _('Exporter la liste des marques');?>" class="float-right" />

	<?php if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_BRAND_ADD') ){ ?>
		<input type="submit" name="add" value="Ajouter" class="btn-main float-right" style="margin-right: 5px">
	<?php } ?>
</h2>

<?php if( isset($error) ){ ?>
	<div class="error"><?php print nl2br(htmlspecialchars($error)); ?></div>
<?php }
if( $notice ){ ?>
	<div class="notice">
		<?php print nl2br($notice); ?>
		<a href="../index.php?brd=-1"><?php print _('Afficher la liste'); ?></a>
	</div>
<?php } ?>
	<table id="brands" class="checklist">
		<thead>
			<tr>
				<th id="brd-sel"<?php print 'data-label="'._('Cocher tout :').'"'?> class="col-check"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
				<th id="brd-name" class="thead-none"><?php print _('Désignation');?></th>
				<th id="brd-url" class="thead-none"><?php print _('Site web');?></th>
				<th id="brd-publish" class="thead-none"><?php print _('Publiée');?></th>
				<th id="brd-prd" class="align-right thead-none"><?php print _('Produits publiés');?></th>
				<?php if( $ordered ){ ?><th id="ord-pos" class="thead-none"><?php print _('Déplacer');?></th><?php } ?>
			</tr>
		</thead>
		<tbody>
		<?php
			if( $count_brands==0 ){ // Aucune marque
				print '<tr><td colspan="'.($ordered ? 6 : 5).'">'._('Aucune marque').'</td></tr>';
			}elseif( $brands!==false ){
				// Affiche la liste des marques
				while( $r = ria_mysql_fetch_array($brands) ){
					if( $have_mdl_filter || $have_fld_filter ){
						if( $ru = prd_brands_get($r['obj_id']) ){
							$r = ria_mysql_fetch_array( $ru );
						}else{
							$r = null;
						}
					}
					if( $r!=null ){
						print '
							<tr id="line-'.$r['id'].'" class="ria-row-orderable">
								<td headers="brd-sel"><input type="checkbox" class="checkbox" name="brd[]" value="'.$r['id'].'" /></td>
						';
						if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_BRAND_VIEW') ){
							print '<td headers="brd-name">'.view_brd_is_sync($r).' <a href="edit.php?brd='.$r['id'].'">'.htmlspecialchars($r['title']).'</a></td>';
						}else{
							print '<td headers="brd-name">'.view_brd_is_sync($r).htmlspecialchars($r['title']).'</td>';
						}
						print '
								<td headers="brd-url">'.htmlspecialchars( $r['url'] ).'</td>
								<td headers="brd-publish">'.( $r['publish'] ? _('Oui') : _('Non') ).'</td>
						';
						if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW') ){
							print '<td headers="brd-prd" class="col-numeric"><a href="../index.php?brd='.$r['id'].'">'.$r['products'].'</a></td>';
						}else{
							print '<td headers="brd-prd" class="col-numeric">'.$r['products'].'</td>';
						}
						if( $ordered ){
							print '<td headers="ord-pos" class="ria-cell-move"></td>';
						}
						print '</tr>';
					}
				}
			}
		?>
		</tbody>
		<tfoot>
			<tr><td colspan="<?php print $ordered ? 6 : 5; ?>" class="align-left">
				<?php if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_BRAND_ADD') ){ ?>
				<input type="button" name="import" value="<?php print _('Importer')?>" title="<?php print _('Importer des marques')?>" onclick="window.location.href = '../../tools/imports/index.php?imp-class=5';" />
				<?php } ?>
				<?php if( $count_brands>0 ){
					if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_BRAND_PUBLISH') ){ ?>
						<input type="submit" name="publish" class="btn-del" value="<?php print _('Publier');?>" title="<?php print _('Publier les marques sélectionnées');?>" />
						<input type="submit" name="unpublish" class="btn-del" value="<?php print _('Retirer');?>" title="<?php print _('Dé-publier les marques sélectionnées');?>" />
					<?php }
					if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_BRAND_DEL') ){ ?>
						<input type="submit" name="delete" class="btn-del" value="<?php print _('Supprimer');?>" title="<?php print _('Supprimer les marques sélectionnées');?>" onclick="return window.confirm('<?php print _('Vous êtes sur le point de supprimer les marques sélectionnées.').'\n'._('Cette opération est irréversible et ne pourra pas être annulée.').'\n'._('Etes-vous sûr(e) de vouloir continuer ?').'\n\n'._('Note : les produits de ces marques ne seront pas supprimés.')?>');" />
					<?php } ?>
						<input type="submit" name="export" id="export" value="<?php print _('Exporter');?>" title="<?php print _('Exporter la liste des marques');?>" />
				<?php }
				if(gu_user_is_authorized('_RGH_ADMIN_CATALOG_BRAND_ADD')){ ?>
					<div style="float: right">
						<label for="brdname"><?php print _('Ajouter une marque :'); ?></label> <input type="text" name="brdname" id="brdname" maxlength="75" /> <input type="submit" value="<?php print _('Ajouter');?>" />
					</div>
				<?php } ?>
			</td></tr>
			<?php if( $count_brands>1 ){ ?>
			<tr>
				<td colspan="<?php print $ordered ? 6 : 5; ?>" class="tfoot-grey">
					<label><?php print _('Trier ces marques par ordre :');?></label>
					<input type="radio" class="radio" name="order" id="order-0" value="0" <?php if( !$ordered ){ print 'checked="checked"'; } ?> /> <label for="order-0"><?php print _('Alphabétique');?></label>
					<input type="radio" class="radio" name="order" id="order-1" value="1" <?php if( $ordered ){ print 'checked="checked"'; } ?> /> <label for="order-1"><?php print _('Personnalisé');?></label>
					<input type="submit" name="orderby" value="<?php print _('Appliquer');?>" />
				</td>
			</tr>
			<?php } ?>
		</tfoot>
	</table>
</form>

<?php

require_once('admin/skin/footer.inc.php');