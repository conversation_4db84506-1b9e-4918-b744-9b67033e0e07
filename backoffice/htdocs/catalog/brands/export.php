<?php

	/**	\file export.php
	 *
	 *	Ce fichier exporte une liste de marques à partir de filtres sélectionnés sur la liste des marques. L'export est au format CSV.
	 *	Contrairement à l'export des produits, les colonnes à exporter ne peuvent pas encore être choisies.
	 *
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_BRAND');

	require_once('brands.inc.php');
	require_once('fields.inc.php');

	// 1 : Charge la liste des marques, triée et filtrée dans le même ordre que sur la liste des marques

	// Note : le paramètre mdl a priorité sur le paramètre fld
	$have_fld_filter = false;
	$have_mdl_filter = false;
	$count_brands = 0;

	// Récupère un éventuel modèle de saisie sur lequel filtrer le résultat
	if( isset($_GET['mdl']) && is_numeric($_GET['mdl']) && $_GET['mdl']>0 ){
		if( $rmodel = fld_models_get($_GET['mdl']) ){
			$model = ria_mysql_fetch_array($rmodel);
			if( $model['cls_id']==CLS_BRAND ){
				$brands = fld_models_get_objects( $_GET['mdl'] );
				$count_brands = ria_mysql_num_rows($brands);
				$have_mdl_filter = true;
			}
		}
	}elseif( isset($_GET['fld']) && is_numeric($_GET['fld']) && $_GET['fld']>0 ){
		// Récupère un éventuel champ libre sur lequel filtrer le résultat
		if( $rfield = fld_fields_get($_GET['fld']) ){
			$field = ria_mysql_fetch_array($rfield);
			if( $field['cls_id']==CLS_BRAND ){
				$brands = fld_fields_get_objects( $_GET['fld'] );
				$count_brands = ria_mysql_num_rows($brands);
				$have_fld_filter = true;
			}
		}
	}else{
		// Aucun filtre
		$brands = prd_brands_get();
		$count_brands = ria_mysql_num_rows($brands);
	}

	// 2 : Création du document CSV

	$fields = array(
		'id' => _('Identifiant'),
		'ref' => _('Référence'),
		'name' => _('Désignation'),
		'title' => _('Titre'),
		'desc' => _('Description'),
		'url' => _('Site web'),
		'publish' => _('Publiée'),
		'products' => _('Produits publiés'),
		'is_sync' => _('Synchronisée'),
		'url_alias' => _('Url'),
		'tag_title' => _('Balise Title'),
		'tag_desc' => _('Balise Meta Description')
	);

	// Ligne d'entête
	$header_row = array();
	foreach( $fields as $code=>$name ){
		$header_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $name);
	}

	// Lignes du document CSV
	$rows = array();
	$rows[] = utf8_decode( implode(';', $header_row) );

	// lignes de données
	while( $r = ria_mysql_fetch_array($brands) ){

		// Si la liste est filtrée sur un champ ou un modèle de saisie, on ne dispose que de l'identifiant de la marque (celle-ci doit donc être chargée en entier avant traitement)
		if( $have_mdl_filter || $have_fld_filter ){
			if( $ru = prd_brands_get($r['obj_id']) ){
				$r = ria_mysql_fetch_array( $ru );
			}else{
				$r = null;
			}
		}

		if( $r!=null ){
			$data_row = array();
			foreach( $fields as $field=>$name ){
				if( $field=='title' ){
					if( $r['title']==$r['name'] ){
						$r['title'] = '';
					}
				}elseif( $field=='url_alias' ){
					$r['url_alias'] = $config['site_url'].$r['url_alias'];
				}
				$data_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $r[$field]);
			}
			$rows[] = utf8_decode( implode(';', $data_row) );
		}

	}

	// 3 : Ecriture du fichier sur disque et proposition en téléchargement
	$path = tempnam( sys_get_temp_dir(), 'brands-' );

	file_put_contents( $path, implode("\n", $rows) );

	$filename = _('marques');

	header('Content-Type: application/x-force-download; charset=utf-8');
	header('Content-Disposition: attachment; filename="'.$filename.'.csv"');
	header('Content-Length: '.filesize($path));

	readfile($path);

	exit;

