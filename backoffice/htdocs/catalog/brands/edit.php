<?php

	/**	\file edit.php
	 *	Cette page permet la création et la modification d'une marque. Les marques sont ensuite utilisées 
	 *	comme attributs sur les produits.
	 */

	// Vérifie que l'utilisateur à bien accès à cette page
	if( $_GET['brd'] != 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_BRAND_VIEW');
	}elseif( $_GET['brd'] == 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_BRAND_ADD');
	}

	// Vérifie que l'utilisateur à bien le droit de modifier / supprimer une marque
	if( isset($_POST['delete']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_BRAND_DEL');
	}
	if( $_GET['brd'] != 0 ){
		if( isset($_POST['save']) || isset($_POST['save_stay']) || isset($_POST['add-model']) || isset($_POST['del-img']) ){
			gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_BRAND_EDIT');
		}
	}	
	
	require_once('brands.inc.php');
	unset($error);

	// Vérifie l'existance de la marque (mode édition uniquement)
	if( isset($_GET['brd']) && $_GET['brd']!=0 && !prd_brands_exists($_GET['brd']) ){
		header('Location: index.php');
		exit;
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Détermine l'onglet en cours de consultation
	if( !isset($_GET['tab']) ){
		$_GET['tab'] = 'general';
	}
	$tab = $_GET['tab'];

	// Définition des valeurs du formulaire
	if( isset($_GET['brd']) && $_GET['brd']!=0 ){
		$brd = ria_mysql_fetch_array(prd_brands_get($_GET['brd']));
	}
	else{
		$brd = array( 'id'=>0, 'name'=>'', 'desc'=>'', 'url'=>'', 'products'=>0, 'publish'=>1, 'img_id'=>'', 'is_sync'=>false, 'tag_title'=>'', 'tag_desc'=>'', 'keywords'=>'' );
	}

	// Bouton Supprimer
	if( isset($_POST['delete']) ){
		if( !$brd['is_sync'] )
			prd_brands_del($_GET['brd']);
		header('Location: index.php');
		exit;
	}

	// Bouton supprimer l'image
	if( isset($_POST['del-img']) ){
		prd_brands_image_del($_GET['brd']);
		header('Location: edit.php?brd='.$_GET['brd'].'&tab=images');
		exit;
	}

	// Ajout d'un nouveau modèle
	if( isset($_POST['add-model']) && isset($_POST['models-pick']) && is_numeric($_POST['models-pick']) && $_POST['models-pick']>0 ){
		$res = fld_object_models_add( $brd['id'],$_POST['models-pick'] );
		if( !$res )
			$error = _("Impossible d'ajouter le nouveau modèle de saisie.");
	}
	
	// Suppression d'un modèle
	if( isset($_GET['delmdl']) && is_numeric($_GET['delmdl']) && $_GET['delmdl']>0 ){
		$res = fld_object_models_del( $brd['id'],$_GET['delmdl'],CLS_BRAND );
		if( !$res )
			$error = _("Impossible de supprimer l'association avec le modèle.");
	}
	
	// Boutons Enregistrer + Enregistrer et revenir à la liste
	if( isset($_POST['save']) || isset($_POST['save_stay']) || isset($_POST['tabGeneral']) || isset($_POST['tabFields']) || isset($_POST['tabImages']) || isset($_POST['addimg']) || isset($_POST['tabReferencing']) ){
		$tab = $_GET['brd']==0 ? 'general' : $tab;
		if( $tab=='general' ){
			if( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] || !$brd['is_sync'] ){
				if( !isset($_POST['title']) ) $_POST['title'] = '';
				
				// Vérifie que toutes les informations obligatoires sont disponibles
				if( !isset($_POST['name'],$_POST['title'],$_POST['url']) ){
					$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.")."\n"._("Veuillez vérifier.");
				}elseif( (!isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng']) && !trim($_POST['name']) ){
					$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.")."\n"._("Veuillez vérifier.")." "._("Les champs marqués d'une * sont obligatoires.");
				}
			}else{
				if( !isset($_POST['name']) ) $_POST['name'] = '';

				if( !isset($_POST['title'],$_POST['url']) ){
					$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.")."\n"._("Veuillez vérifier.");
				}
			}
			
			if( !isset($error) ){
				if( $_GET['brd']==0 ){
					$_GET['brd'] = prd_brands_add($_POST['name'],$_POST['title'],$_POST['url'],false,$_POST['desc']);
					if( !$_GET['brd'] )
						$error = 1; // Utilise le message par défaut
				}elseif( $_GET['brd']>0 ){
					if( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] ){
						if( !prd_brands_update($_GET['brd'],$_POST['name'],$_POST['title'],$_POST['url']) ){
							$error = 1; // Utilise le message par défaut
						}else{
							if( !prd_brands_update_desc( $_GET['brd'], $_POST['desc'] ) ){
								$error = 1;
							}
						}
					} elseif( in_array($_GET['lng'], $config['i18n_lng_used']) ){
						$values = array(
							_FLD_BRD_NAME=>$_POST['name'],
							_FLD_BRD_TITLE=>$_POST['title'],
							_FLD_BRD_DESC=>$_POST['desc'],
							_FLD_BRD_WEB=>$_POST['url']
						);
						
						if( !fld_translates_add($_GET['brd'], $_GET['lng'], $values) )
							$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
					}
				}
			}
		
			// publication / dépublication de la marque
			if( !isset($error) ){
				$_POST['publish'] = isset($_POST['publish']) ? $_POST['publish'] : 0;
				if( $_POST['publish'] ){
					if( !prd_brands_publish($_GET['brd']) ){
						$error = 1;
					}
				}else{
					if( !prd_brands_unpublish($_GET['brd']) ){
						$error = 1;
					}
				}
			}
		} elseif( $tab=='images' ){ // Onglet Images
			if( !isset($error) && isset($_FILES['image']) && $_FILES['image']['error']!=UPLOAD_ERR_NO_FILE ){
				if( !prd_brands_image_upload($_GET['brd'],'image') ){
					$error = 1;
				}
			}
		} elseif( $tab=='fields' ){ // Onglet Avancé
			
			// Sauvegarde les champs libres
			$field_to_save = array();
			
			// Champs orphelin
			$fields = fld_fields_get( 0, 0, -2, 0, 0, $_GET['brd'], null, array(), false, array(), null, CLS_BRAND );
			while( $f = ria_mysql_fetch_array($fields) ){
				$field_to_save[ $f['id'] ] = $f;
			}

			// Champs avec modèle de saisie
			$fields = fld_fields_get( 0, 0, 0, 0, 0, $_GET['brd'], null, array(), false, array(), null, CLS_BRAND );
			while( $f = ria_mysql_fetch_array($fields) ){
				$field_to_save[ $f['id'] ] = $f;
			}

			// Pour chaque champ, sauvegarde les informations venant du formulaire
			foreach( $field_to_save as $f ){
				if( !$f['is-sync'] || !$object['is_sync'] ){
					if( $f['type_id']==FLD_TYPE_IMAGE ){
						if( isset($_FILES['fld'.$f['id']]) && trim($_FILES['fld'.$f['id']]['tmp_name'])!='' ){
							$value = img_images_upload('fld'.$f['id']);
							fld_object_values_set( $_GET['brd'], $f['id'], $value, $config['i18n_lng'] );
						}
						continue;
					}

					if( $f['type_id']==FLD_TYPE_SELECT_MULTIPLE && !isset($_POST['fld'.$f['id']]) ){
						$_POST['fld'.$f['id']] = '';
					}

					if( isset($_POST['fld'.$f['id']]) ){
						$value = $_POST['fld'.$f['id']];
						
						if( $f['type_id'] == FLD_TYPE_REFERENCES_ID ){
							$value = explode( ',', $value );
						}

						fld_object_values_set( $_GET['brd'], $f['id'], $value, $config['i18n_lng'] );
					} else {
						fld_object_values_set( $_GET['brd'], $f['id'], '', $config['i18n_lng'] );
					}
				}
			}
		} elseif( $tab=='ref' ){ // Onglet Référencement
			view_admin_tab_referencement_actions( CLS_BRAND, $_GET['brd'], (isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng']) );
		}
		
		if (isset($_SESSION['referencement_edit_error'])) {
			$error = $_SESSION['referencement_edit_error'];
			unset($_SESSION['referencement_edit_error']);
		}

		if( isset($error) ){
			if( $error == 1 ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la marque.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur");
			}
		}elseif( isset($_POST['save_stay']) ){
			header('Location: edit.php?brd='.$_GET['brd'].'&tab='.$tab.( isset($_GET['lng']) && $_GET['lng'] != $config['i18n_lng'] ? '&lng='.$_GET['lng'] : ''));
			exit;
		}elseif( isset($_POST['save']) ){
			header('Location: index.php');
			exit;
		}
	}

	if( isset($_POST['tabGeneral']) ){
		$tab = 'general';
	}elseif( isset($_POST['tabFields']) ){
		$tab = 'fields';
	}elseif( isset($_POST['tabImages']) || isset($_POST['addimg']) ){
		$tab = 'images';
	}elseif( isset($_POST['tabReferencing']) ){
		$tab = "ref";
	}
		
	if (isset($_SESSION['referencement_edit_success'])) {
		$success = $_SESSION['referencement_edit_success'];
		unset($_SESSION['referencement_edit_success']);
	}

	if( isset($brd['name']) && trim($brd['name'])!='' ){
		$h2_name = _('Marque').' '.$brd['name'];
	} else {
		$h2_name = _('Nouvelle marque');
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', $h2_name.' - '._('Marques') . ' - ' . _('Catalogue'));
	require_once('admin/skin/header.inc.php');

?>

<h2><?php print view_brd_is_sync($brd) .' '. htmlspecialchars( $h2_name ); ?></h2>

<?php
	if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}

	if( isset($success) ){
		print '<div class="success">'.nl2br(htmlspecialchars($success)).'</div>';
	}

	// Récupère la langue 
	require_once('view.translate.inc.php');
	$lng = view_selected_language();

	if( isset($_GET['brdname']) ){
		$brd['name'] = ucfirst(trim($_GET['brdname']));
	}elseif( $lng!=$config['i18n_lng'] ){

		$tsk_brd = fld_translates_get( CLS_BRAND, $brd['id'], $lng, $brd, array(
			_FLD_BRD_NAME=>'name',
			_FLD_BRD_TAG_TITLE=>'tag_title',
			_FLD_BRD_TAG_DESC=>'tag_desc',
			_FLD_BRD_KEYWORDS=>'keywords',
			_FLD_BRD_TITLE=>'title',
			_FLD_BRD_DESC=>'desc',
			_FLD_BRD_WEB=>'url'), true );

		$brd['name'] = $tsk_brd['name'];
		$brd['title'] = $tsk_brd['title'];
		$brd['desc'] = $tsk_brd['desc'];
		$brd['url'] = $tsk_brd['url'];
		$brd['tag_title'] = $tsk_brd['tag_title'];
		$brd['tag_desc'] = $tsk_brd['tag_desc'];
		$brd['keywords'] = $tsk_brd['keywords'];
	}
		
?>

<form action="edit.php?brd=<?php print $_GET['brd']; ?>&amp;lng=<?php print $lng; ?>&amp;tab=<?php print $tab; ?>" enctype="multipart/form-data" method="post">
	<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="<?php print _('Général')?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
		<li><input type="submit" name="tabFields" value="<?php print _('Avancé')?>" <?php if( $tab=='fields' ) print 'class="selected"'; ?> /></li>
		<li><input type="submit" name="tabImages" value="<?php print _('Image')?>" <?php if( $tab=='images' ) print 'class="selected"'; ?> /></li>
		<?php if( tnt_tenants_have_websites() ){ ?>
		<li><input type="submit" name="tabReferencing" value="<?php print _('Référencement')?>" <?php if ( $tab=='ref' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
	</ul>
	<div id="tabpanel">
		<?php
			// Affiche le menu de langue
			if( $brd['id'] > 0 ){
				print view_translate_menu( 'edit.php?brd='.$_GET['brd'].'&amp;tab='.$tab, $lng );
			}
		?>	
		<table <?php if( $tab=='ref' ) { print 'id="table-with-ref"' ; } else { print 'id="table-without-ref"' ; } ?> >
			<tbody>
				<?php if( $tab=='general' ){ ?>
					<tr><th colspan="2"><?php print _('Propriétés générales')?></th></tr>
					<?php if( $brd['is_sync'] ){ ?>
					<tr>
						<td><label for="ref"><?php print _('Référence :'); ?></label></td>
						<td><input type="text" name="ref" id="ref" value="<?php print htmlspecialchars($brd['ref']); ?>" maxlength="75" <?php print $brd['is_sync'] ? 'readonly="readonly"' : '' ?> /></td>
					</tr>
					<?php }
					if( !$brd['is_sync'] || $lng == $config['i18n_lng'] ){ ?>
					<tr>
						<td><label for="name"><span class="mandatory">*</span> <?php print _('Désignation :'); ?></label></td>
						<td><input type="text" name="name" id="name" value="<?php print htmlspecialchars($brd['name']); ?>" maxlength="75" <?php print $brd['is_sync'] ? 'readonly="readonly"' : '' ?> /></td>
					</tr>
					<?php }
					if( $brd['is_sync'] ){ ?>
					<tr>
						<td><label for="title"><?php print _('Titre :'); ?></label></td>
						<td><input type="text" name="title" id="title" value="<?php if($brd['title']!=$brd['name']) print htmlspecialchars($brd['title']); ?>" maxlength="75" /></td>
					</tr>
					<?php } ?>
					<tr>
						<td class="col110px"><label for="url"><?php print _('Site web :'); ?></label></td>
						<td><input type="text" name="url" id="url" value="<?php print htmlspecialchars($brd['url']); ?>" maxlength="75" /></td>
					</tr>
					<tr>
						<td><label for="desc"><?php print _('Description :'); ?></label></td>
						<td><textarea name="desc" id="desc"><?php print htmlspecialchars($brd['desc']); ?></textarea></td>
					</tr>
					<tr>
						<td><?php print _('Produits :'); ?></td>
						<td>
							<?php $products = prd_brands_count_products($_GET['brd']);
							if( !$products ){
								print _('Aucun produits');
							}else{
								if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW') ){ ?>
									<a href="../index.php?brd=<?php print $_GET['brd']; ?>">
								<?php }
								if( $products == 1 ){
									print _('1 produit');
								}else{
									print ria_number_format($products, NumberFormatter::DECIMAL).' '._('produits');
								}
								print ' ';
								printf($brd['products'] != 1 ? _('(dont %d produits publiés)') : _('(dont %d produit publié)'), ria_number_format($brd['products'], NumberFormatter::DECIMAL));
								if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW') ){ ?>
									</a>
								<?php }
							} ?>
						</td>
					</tr>
					<tr>
						<td class="td-no-ref-1"><label for="publish-y"><?php print _('Publié :'); ?></label></td>
						<td class="td-no-ref-2">
							<input class="publish-cat" type="radio" name="publish" id="publish-y" value="1" <?php print $brd['publish'] ? 'checked="checked"' : ''; ?> /><label for="publish-y"><?php print _('Oui')?></label>
							<input class="publish-cat" type="radio" name="publish" id="publish-n" value="0" <?php print !$brd['publish'] ? 'checked="checked"' : ''; ?> /><label for="publish-n"><?php print _('Non')?></label>
						</td>
					</tr>
				<?php } elseif( $tab=='images' ) { ?>
					<tr><th colspan="2"><?php print _('Image associée')?></th></tr>
					<tr>
						<td class="td-no-ref-1"><label for="image"><?php print $brd['img_id'] ? _('Remplacer l\'image par :') : _('Ajouter une image :'); ?></label></td>
						<td class="td-no-ref-2"><input type="file" name="image" id="image" /></td>
					</tr>
					<tr>
						<td><?php print _('Image associée :'); ?></td>
						<td>
							<?php
								if( !$brd['img_id'] ){
									print _('Aucune image associée');
								}else{
									$size = $config['img_sizes']['medium'];
									print '<div class="preview' . (isset($config['img_sizes']['very_high']) ? ' edit-zones' : '') . '" id="img' . $brd['img_id'] . '">';
									print '<img src="'.$config['img_url'].'/'.$size['dir'].'/'.$brd['img_id'].'.jpg" width="'.$size['width'].'" height="'.$size['height'].'" />';
									print '</div>';
								}
							?>
						</td>
					</tr>
				<?php } elseif( $tab=='fields' ) { ?>
					<tr><th colspan="2"><?php print _('Avancé')?></th></tr>
					<?php
					// Affiche les champs disponibles à la saisie via un modèle de saisie
					$fields_none = true;
					if( $models = fld_models_get( 0,$brd['id'],CLS_BRAND  ) ){
						while( $model = ria_mysql_fetch_array($models) ){
							print '
								<tr>
									<th colspan="2"  class="td-no-ref-1" style="background-color: #ccc; text-align: left;">
										<a href="edit.php?brd='.$brd['id'].'&amp;delmdl='.$model['id'].'" style="float: right;">'._('Supprimer').'</a>'.
										_('Modèle de saisie').' : '.htmlspecialchars($model['name']).
									'</th>
								</tr>
							';

							if( $fields = fld_fields_get( 0, 0, $model['id'], 0, 0, $brd['id'], null, array(), false, array(), null, CLS_BRAND ) ){
								while( $field = ria_mysql_fetch_array($fields) ){
									print '<tr><td class="td-no-ref-1">'.htmlspecialchars($field['name']).' :</td><td class="td-no-ref-2">'.fld_fields_edit($field).'</td></tr>';
								}
							}
							$fields_none = false;
						}
					}
					// Affiche les champs orphelins (qui ne sont plus rattachés à la marque via un modèle de saisie, mais qui contiennent des données)
					if( $fields = fld_fields_get( 0, 0, -1, 0, 0, $brd['id'], null, array(), false, array(), null, CLS_BRAND ) ){
						while( $field = ria_mysql_fetch_array($fields) ){
							print '<tr><td class="td-no-ref-1">'.htmlspecialchars($field['name']).' :</td><td class="td-no-ref-2">'.fld_fields_edit($field).'</td></tr>';
							$fields_none = false;
						}
					}
					// Si aucun champ avancé n'est disponible, affiche un message d'erreur/information
					if( $fields_none ){
						print '<tr><td colspan="2" class="td-no-ref-3">'._('Aucun champ avancé').'</td></tr>';
					}
					// Formulaire d'ajout de modèles de saisie (seulement s'il en existe)
					$r_models = fld_models_get( 0, 0, CLS_BRAND );
				} elseif( $tab=='ref' ){ // Onglet Référencement

					$website = ria_mysql_fetch_array(wst_websites_get($config['wst_id']));
					$keywords = $website['meta_kwd'];
					print view_admin_tab_referencement(CLS_BRAND, $_GET['brd'], $lng);
				} ?>
			</tbody>
			<?php if( $tab!='fields' || ($fields_none==false) || (isset($r_models) && ria_mysql_num_rows($r_models)) ){ ?>
				<tfoot>
					<?php if( $tab!='fields' || ($fields_none==false) ){?>
						<tr>
							<td colspan="<?php print $tab=='ref' ? '3' : '2'; ?>">
								<input type="submit" name="save_stay" value="<?php print _('Enregistrer')?>" />
								<input type="submit" name="save" id="save" value="<?php print _('Enregistrer et revenir à la liste')?>"/>
								<input type="submit" name="cancel" value="<?php print _('Annuler')?>" />
								<?php if( $brd['id'] && !$brd['is_sync'] && gu_user_is_authorized('_RGH_ADMIN_CATALOG_BRAND_DEL') ){ ?>
									<input type="submit" name="delete" value="<?php print _('Supprimer')?>" onclick="return confirmDel();" />
								<?php }
								if( $brd['img_id'] && $tab=='images' ){ ?>
									<input type="submit" name="del-img" value="<?php print _('Supprimer l\'image')?>" onclick="return confirmDelImg()" />
								<?php } ?>
							</td>
						</tr>
					<?php }?>
					<?php if( $tab=='fields' && ria_mysql_num_rows($r_models)) {?>
						<tr>
							<td colspan="2" style="text-align: left; background-color: #eee; padding: 3px;">
								<label for="models-pick"><?php print _('Modèle de saisie :'); ?></label>
								<select id="models-pick" name="models-pick">
								<?php
									while( $model = ria_mysql_fetch_array($r_models) ){
										print '<option value="'.$model['id'].'">'.htmlspecialchars($model['name']).'</option>';
									}
								?>
								</select>
								<input type="submit" name="add-model" value="<?php print _('Ajouter')?>" />
							</td>
						</tr>
					<?php } ?>
				</tfoot>
			<?php } ?>
		</table>
	</div>
</form>

<?php if( $brd['id'] && !gu_user_is_authorized('_RGH_ADMIN_CATALOG_BRAND_EDIT') || (!gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_BRD') && $tab=='images') ){ ?>
<script>
	// Disable tous les champs/boutons si on accède à cette page en lecture seule
	$(document).ready(function(){
		$('table').find('input, select, textarea').attr('disabled', 'disabled');
	});
</script>
<?php } ?>
<?php
	require_once('admin/skin/footer.inc.php');
