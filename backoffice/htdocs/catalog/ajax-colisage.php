<?php

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT');

	require_once('products.inc.php');
	require_once('prd/colisage.inc.php');

	header("Content-Type: application/xml");

	if( isset($_POST['add']) ){
		if( isset($_POST['prd'], $_POST['desc'], $_POST['qte']) ){
			$_POST['qte'] = str_replace(array(' ', ','), array('', '.'), $_POST['qte']);
			
			if( !is_numeric($_POST['qte']) || $_POST['qte']<=0 ){
				print '<result type="0"><msg><![CDATA['._('Impossible d\'ajouter ce conditonnement. La quantité doit être un entier supérieur à zéro. Si le conditionnement est un poids, la quantité est enregistrée en tant que gramme.').']]></msg>\n</result>';
			} else {
				$new_col = prd_colisage_types_add( $_POST['desc'], $_POST['qte'] );
				if( $new_col>0 ){
					if( !prd_colisage_classify_add($new_col, $_POST['prd'], 0) )
						print '<result type="0"><msg><![CDATA['._('Impossible de rattacher ce produit avec ce conditionnement.').']]></msg>\n</result>';
					else
						print '<result type="1"><msg><![CDATA[]]></msg>\n</result>';		
				} else { print '<result type="0"><msg><![CDATA['._('Une erreur s\'est produite lors de l\'ajout du conditionnement').']]></msg>\n</result>'; }
			}
		} else { print '<result type="0"><msg><![CDATA['._('Une erreur inattendue s\'est produite.').']]></msg>\n</result>'; }
	} elseif( isset($_POST['refresh']) ){
		$html = '';
		if( isset($_POST['prd']) ){
			$colisage = prd_colisage_classify_get( 0, $_POST['prd'], 0, array('col_qte'=>'desc') );
			if( $colisage &&  ria_mysql_num_rows($colisage) ){
				while( $col = ria_mysql_fetch_array($colisage) ){
					$html .= '<input onclick="return delColisagePrd('.$col['col_id'].');" type="image" name="del-col-'.$col['col_id'].'" src="../images/del-cat.svg" width="16" height="16" title="'._('Retirer ce produit du conditionnement').' '.htmlspecialchars($col['col_name']).'" class="icon-del-cat" /> ';
					$html .= htmlspecialchars( $col['col_name'] ) . ' (Qté: ' . number_format($col['qte'], intval($col['qte']) == floatval($col['qte']) ? 0 : 2, ',', '') .')' .'<br />';
				}
			} else { $html .= _('Aucun conditionnement'); }
		}
		print '<result type="1"><msg><![CDATA['.$html.']]></msg>\n</result>';
	} elseif( isset($_POST['link']) ){
		if( isset($_POST['prd']) ){
			if( !prd_colisage_classify_exists($_POST['prd'], $_POST['col']) ){
				if( !prd_colisage_classify_add($_POST['col'], $_POST['prd'], 0) )
					print '<result type="0"><msg><![CDATA['._('Impossible de rattacher ce produit avec ce conditionnement.').']]></msg>\n</result>';
				else
					print '<result type="1"><msg><![CDATA[]]></msg>\n</result>';
			} else { print '<result type="0"><msg><![CDATA['._('Ce produit est déjà rattaché avec ce conditionnement.').']]></msg>\n</result>'; }
		} else { print '<result type="0"><msg><![CDATA[Une erreur inattendue s\'est produite.]]></msg>\n</result>'; }
	} elseif( isset($_POST['del']) ){
		if( isset($_POST['prd']) && isset($_POST['col']) ){
			if( $_POST['prd']>0 && !prd_colisage_classify_del($_POST['col'], $_POST['prd']) )
				print '<result type="0"><msg><![CDATA['._('Impossible de retirer ce conditionnement pour ce produit.').']]></msg>\n</result>';
			elseif( $_POST['prd']==0 && !prd_colisage_types_del($_POST['col']) )
				print '<result type="0"><msg><![CDATA['._('Impossible de retirer ce conditionnement.').']]></msg>\n</result>';
			else
				print '<result type="1"><msg><![CDATA[]]></msg>\n</result>';
		} else { print '<result type="0"><msg><![CDATA['._('Une erreur inattendue s\'est produite lors de la suppression.').']]></msg>\n</result>'; }
	}