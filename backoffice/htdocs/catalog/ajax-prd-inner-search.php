<?php

/** \file ajax-prd-inner-search.php
 *	Ce fichier va retourner la liste des recherches internes ayant retourné le produit passé en argument
 *	Paramètres :
 *	- prd : Obligatoire, identifiant du produit recherché
 *	- cat : Obligatoire, identifiant de la catégorie dans laquelle le produit est classé
 */

// Vérifie que l'utilisateur à bien accès à cette page
gu_if_authorized_else_403('_RGH_ADMIN_STATS_SEARCH');

require_once('products.inc.php');
require_once('prd/colisage.inc.php');

if( !IS_AJAX ){
	exit;
}
if( !isset($_GET['prd'], $_GET['cat']) ){
	echo json_encode(array('success' => false, 'error' => 'get prd or cat must be set'));
	exit;
}
ob_start();

$ar_website = wst_websites_get_array( 'name' );

$colspan = 7;
if (count($ar_website) > 1){
	$colspan = 8;
}

require_once('search.inc.php');

// Récupère l'identifiant du contenu pour ce produit dans cette catégorie
$rcnt = ria_mysql_fetch_array(prd_classify_get(false, $_GET['prd'], $_GET['cat']));
$cnt = $rcnt['cnt'];

// On récupère tous les caches qui ont dans leurs résultats ce produit.
$rscc = search_caches_get(0, 0, 0, 0, null, null, null, null, null, null, '', null, 0, $cnt);

$tscc = array();
$ar_all_type = array();
if ($rscc && ria_mysql_num_rows($rscc)){
	while ($scc = ria_mysql_fetch_array($rscc)){
		$tscc[] = $scc['id'];
	}

	$r_type = search_content_types_get();
	if ($r_type) {
		while ($type = ria_mysql_fetch_assoc($r_type)) {
			$ar_all_type[ $type['id'] ] = $type['name'];
		}
	}
}

if (sizeof($tscc) > 0) {
	$logs = search_log_get(false, false, false, false, false, $tscc, $cnt);
	if ($logs && ria_mysql_num_rows($logs)) {
		$c_log = 0;

		while ($log = ria_mysql_fetch_array($logs)) {
			if ($c_log>15) {
				break;
			}

			// Détermine les types de recherches
			$ar_type = array();
			if (trim($log['type_id'])) {
				foreach (explode(',', $log['type_id']) as $value) {
					if (array_key_exists($value, $ar_all_type)) {
						$ar_type[] = $ar_all_type[ $value ];
					}
				}
			}

			// Détermine la catégorie de recherches
			$section = 'Toutes';
			if ($log['section'] !== null) {
				$section = prd_categories_get_name( $log['section'] );
			}

			// Position du produit dans le résultat
			$pos = search_results_get_position($log['seg'], $log['scc'], $cnt);
			$pos = !$pos ? 0 : $pos;

			print '<tr>';
			print '<td headers="search-str" class="stat-search-info" data-label="'._('Recherche :').' ">
						<img class="img-stat-search" src="/admin/images/stats/redirection.svg" alt="'._('Créer une redirection').'" title="'._('Créer une redirection à partir de cette recherche').'" width="16" height="16" onclick="return addRedirectionSearch(' . $log['seg'] . ', \'' . addslashes(urldecode($log['search'])) . '\', \'' . $log['lng'] . '\');" />
						<a onclick="return show_search(\'' . addslashes(urldecode($log['search'])) . '\',' . $log['seg'] . ',' . $log['scc'] . '' . ($log['section'] > 0 ? ',' . $log['section'] : ', false') . ', ' . $cnt . ')"  href="/admin/search/index.php?q=' . urlencode($log['search']) . '">' . htmlspecialchars($log['search']) . '</a></td>';
			print '<td headers="pos" class="pos-center" data-label="'._('Position :').' ">#' . $pos . ' (page ' . ceil($pos / $config['prd_list_length']) . ')</td>';
			print '<td headers="clics" class="right" data-label="'._('Clics :').' ">' . $log['nb_click'] . '</td>';
			print '<td headers="volume" class="right" data-label="'._('Impressions :').' ">' . number_format($log['volume'], 0, ",", " ") . '</td>';
			print '<td headers="ctr" class="right" data-label="'._('CTR :').' ">' . number_format($log['nb_click'] / ($log['volume'] > 0 ? $log['volume'] : 1), 2, ",", " ") . '</td>';
			if (count($ar_website) > 1) {
				print '	<td headers="website" data-label="'._('Site web :').' ">' .htmlspecialchars( array_key_exists($log['wst_id'], $ar_website) ? $ar_website[ $log['wst_id'] ] : '' ). '</td>';
			}
			print '<td headers="section" data-label="'._('Section :').' ">' . htmlspecialchars( $section ) . '</td>';
			print '<td headers="types" data-label="'._('Filtre :').' ">' .implode('<br/>', $ar_type). '</td>';
			print '</tr>';

			$c_log++;
		}
	} else {
		print '<tr><td colspan="' . $colspan . '">'._('Ce produit n\'est pour l\'instant apparu dans aucune recherche dans le moteur interne').'</td></tr>';
	}
} else {
	print '<tr><td colspan="' . $colspan . '">'._('Ce produit n\'est pour l\'instant apparu dans aucune recherche dans le moteur interne').'</td></tr>';
}

$content = ob_get_clean();

echo json_encode(array('success' => true, 'content' => $content));
exit;