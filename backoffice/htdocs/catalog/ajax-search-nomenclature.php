<?php
	/** \file ajax-search-nomenclature.php
	 * 	Ce fichier permet de recherche un article en fonction d'un terme (soit via moteur de recherche soit par référence).
	 * 	Il est utilisé lors de la création d'une nomenclature.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT');

	require_once( 'products.inc.php' );
	require_once( 'strings.inc.php' );

	$json = array();
	if( isset($_GET['term']) && $_GET['term']!='' ){
		$search = search3( 1, $_GET['term'], 1, 30, $config['show_admin_ajax_products_published'], false, 6, array('prd') );
		if( $search && ria_mysql_num_rows($search) ){
			while( $r_prd = ria_mysql_fetch_array($search) ){
				$prd = ria_mysql_fetch_array(prd_products_get($r_prd['tag'],'',0,false,0,0,-1,false,false,false,false,false,false,false,false,false,false,true,false,false,null,null));

				//if( !in_array( $prd['nomenclature_type'], array( NM_TYP_COMPONENT, NM_TYP_NONE ) ) && ( $prd['nomenclature_type'] != NM_TYP_LINKED || $config['prd_linked_nomenclature_activate'] ) ) continue;

				$name = $prd['title']!='' ? $prd['title'] : $prd['name'];
				if( $name != '' ){
					$json[] = $prd['ref'].' - '.htmlspecialchars($name)."|".$prd['ref'].' - '.$name;
				}
			}
		} else {
			$r_prd = prd_products_get_byref($_GET['term']);
			if( $r_prd!=false && ria_mysql_num_rows($r_prd)>0 ){
				while( $prd = ria_mysql_fetch_array($r_prd) ){
					//$prd_info = ria_mysql_fetch_array(prd_products_get($prd['id'],'',0,false,0,0,-1,false,false,false,false,false,false,false,false,false,false,true,false,false,null,null));
					//if( !in_array( $prd_info['nomenclature_type'], array( NM_TYP_COMPONENT, NM_TYP_NONE ) ) && ( $prd['nomenclature_type'] != NM_TYP_LINKED || $config['prd_linked_nomenclature_activate'] ) ) continue;

					$name = $prd['title']!='' ? $prd['title'] : $prd['name'];
					if( $name != '' ){
						$json[] = $prd['ref'].' - '.htmlspecialchars($name)."|".$prd['ref'].' - '.$name;
					}
				}
			}
		}
	}

	print json_encode($json);
