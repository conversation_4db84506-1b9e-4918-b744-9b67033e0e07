<?php
	/** \file
	 * 	Ce fichier est charger d'export au format CSV les droits d'accès.
	 * 	Si aucun paramètre n'est donnée, tous les droits liés à l'administration et/ou yuto seront retournés.
	 * 	Si le paramètre GET "usr" est donnée, tous les droits liés à l'administration et/ou yuto seront retournés pour l'utilisateur passé en paramètre.
	 */
	header("Content-Type: text/csv");
	header("Content-Disposition: attachment; filename=rights.csv");
	header("Pragma: no-cache");
	header("Expires: 0");

	// Charge un tableau des droits actives
	if( isset($_GET['usr']) && is_numeric($_GET['usr']) && $_GET['usr'] > 0 ){
		$usr_rights_admin = gu_users_load_admin_rights( false, false, $_GET['usr'] );
		$usr_rights_yuto = gu_users_load_yuto_rights(false, $_GET['usr']);
		$tenant_rights = array_unique( $usr_rights_admin + $usr_rights_yuto );
	}else{
		$tenant_rights = wst_website_rights_get_array($config['tnt_id'], $config['wst_id'], false,  null, true, true, true);
	}

	$ar_cat_rights = [];
	$r_cat_rights = gu_categories_rights_get( $cat=0, $admin=false, $is_yuto=false, $linked_rights=true );
	while( $cat_rights = ria_mysql_fetch_assoc($r_cat_rights) ){
		$ar_cat_rights[ $cat_rights['id'] ] = $cat_rights['name'];
	}

	// Entête du fichier CSV
	print '"Code";"Catégorie";"Libellé";"Admin ?";"Yuto ?";"Code parent"'."\n";

	require_once('rights.inc.php');

	// Charge les droits de premier niveau
	$r_right = gu_rights_get( $rgh=0, $code='', $cat=0, $exclude=array(), $admin=false, $parent=0, $link_tenant=true, $usr=false, $is_yuto=false, $linked_rights=true );
	while( $right = ria_mysql_fetch_assoc($r_right) ){
		// Si le droits n'est pas active, il est exclus du résultat
		if( !in_array($right['id'], $tenant_rights) ){
			continue;
		}

		print $right['code'].';'.$ar_cat_rights[ $right['cat_id'] ].';'.$right['name'].';'.($right['admin'] ? 'Oui' : 'Non').';'.($right['is_yuto'] ? 'Oui' : 'Non').';'."\n";
		temp_get_child( $right['id'], $right['code'] );
	}

	/** Cette fonction propre à ce script permet de charger les droits enfants de façon récursive.
	 * 	@param int $parent Obligatoire, identifiant du droit parent (si 0 la récursivité est coupée)
	 * 	@param string $parent_code Obligatoire, code du droit parent
	 * 	@param int $depth Optionnel, profondeur à laquelle on se trouve dans l'arborescence des droits
	 * 	@return false si la récursivité est coupé
	 */
	function temp_get_child($parent, $parent_code, $depth=1){
		global $tenant_rights, $ar_cat_rights;

		// Stop la récursivité si le parent n'est pas un numérique supérieur à zéro
		if( !is_numeric($parent) || $parent <= 0 ){
			return false;
		}

		$r_right = gu_rights_get( $rgh=0, $code='', $cat=0, $exclude=array(), $admin=false, $parent, $link_tenant=true, $usr=false, $is_yuto=false, $linked_rights=true );
		if( $r_right && ria_mysql_num_rows($r_right) ){
			while( $right = ria_mysql_fetch_assoc($r_right) ){
				if( !in_array($right['id'], $tenant_rights) ){
					continue;
				}

				/*for( $i=0;$i<$depth;$i++ ){
					print '--';
				}*/

				print $right['code'].';'.$ar_cat_rights[ $right['cat_id'] ].';'.$right['name'].';'.($right['admin'] ? 'Oui' : 'Non').';'.($right['is_yuto'] ? 'Oui' : 'Non').';'.$parent_code."\n";
				$new_depth = $depth + 1;

				// Charge les droits enfants
				temp_get_child( $right['id'], $right['code'], $new_depth );
			}
		}
	}