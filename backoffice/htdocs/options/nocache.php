<?php
  require_once('users.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403( '_RGH_ADMIN_OPTION_CACHE' );

  // Désactivation du cache
	if( isset($_POST['nocache']) ){
		gu_users_set_cache_disabled( $_SESSION['usr_id'], false );
		header( 'Location: /admin/options/nocache.php' );
		exit;
	}

  // Réactivation du cache
	if (isset($_POST['cache']) ){
		gu_users_set_cache_disabled( $_SESSION['usr_id'], true );
		header('Location: /admin/options/nocache.php');
		exit;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Mes options'), '/admin/options/index.php' )
		->push( _('Désactiver le cache') );

  $is_disabled = gu_users_get_cache_is_disabled( $_SESSION['usr_id'] );

	// Défini le titre de la page
	define('ADMIN_PAGE', _('Désactiver le cache'));
	require_once('admin/skin/header.inc.php');
?>

<h2><?php print _('Désactivation du cache'); ?></h2>
<?php if ( $is_disabled === false ) { ?>
  <p><?php print _('Vous avez la possibilité de désactiver le cache RiaShop sur votre site pour une durée de 15 minutes, en cliquant sur le bouton ci-dessous.'); ?></p>
	<form action="/admin/options/nocache.php" method="post">
		<input id="btn-nocache" name="nocache" value="<?php print _('Désactiver le cache'); ?>" type="submit"/>
	</form>
<?php
  }else{
    $date = new DateTime( $is_disabled );
    $date->modify('+15 minutes');
?>
	<p><?php print _('Le cache RiaShop est désactivé pour 15 minutes.').'('.dateTimeIntlFormat( $date, true ).')' ?></p>
	<form action="/admin/options/nocache.php" method="post">
		<input id="btn-nocache" name="cache" value="<?php print _('Activer le cache'); ?>" type="submit"/>
	</form>
<?php
	}

	require_once('admin/skin/footer.inc.php');
