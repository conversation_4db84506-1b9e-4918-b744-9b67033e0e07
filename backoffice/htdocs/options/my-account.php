<?php

/**	\file my-account.php
 *	Cette page permet la mise à jour des informations personnelles du compte ainsi que la mise à jour de son mot de passe.
 */

// Si l'on est sur la version "oneriashop", on utilise un fichier autre que celui-ci
if( isset($_SERVER['oneriashop']) && $_SERVER['oneriashop'] === 'true' ){
	include('my-account-gcloud.php');
	exit;
}

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_OPTION_ACCOUNT');

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Chargement des informations à modifier
	$usr = ria_mysql_fetch_array(gu_users_get($_SESSION['usr_id']));
	$adr = ria_mysql_fetch_array(gu_adresses_get($usr['id'],$usr['adr_invoices']));

	// Bouton Enregistrer
	if( isset($_POST['save']) ){
        if( gu_users_set_lng_code($_SESSION['usr_id'], $_POST['language']) ){
			switch( $_POST['language'] ){
				case 'fr':
					$_SESSION['lang'] = 'fr_FR';
					break;
				case 'en':
					 $_SESSION['lang'] = 'en_GB';
					break;
				case 'de':
					 $_SESSION['lang'] = 'de_DE';
					break;
			}
        }else{
			$error = 10;
        }

		// Mise à jour des informations personnelles (Civilité, Nom, Prénom)
		if( !isset($error) && !gu_adresses_update_name( $usr['id'], $adr['id'], $_POST['title'], $_POST['firstname'], $_POST['lastname'] ) ){
			$error = 1;
		}

		// Mise à jour des numéros de téléphone
		if( !isset($error) && !gu_adresses_update_telephone( $usr['id'], $adr['id'], $_POST['phone'], $_POST['fax'], $_POST['mobile'], $_POST['phone-work'] ) ){
			$error = 3;
		}

		// Date de naissance
		if( !isset($error) && $usr['dob']!=$_POST['dob'] ){
			if( !gu_users_set_date_of_birth($usr['id'], $_POST['dob']) ){
				$error = 9;
			}
		}

		// Mise à jour de l'adresse email
		if( !isset($error) && $usr['email']!=trim($_POST['email']) ){
			if( !gu_users_update_email($_GET['usr'],$_POST['email']) ){
				$error = 2;
			}
		}

		// Mise à jour du mot de passe
		if( !isset($error) ){
			if( isset($_POST['password1'],$_POST['password2'])){
				if( strlen($_POST['password1'])!=0 ){
					if( strlen($_POST['password1'])>0 && strlen($_POST['password2'])==0 ){
						$error = 4;
					}elseif( $_POST['password1']!=$_POST['password2'] ){
						$error = 6;
					}elseif( strlen($_POST['password1'])<6 ){
						$error = 7;
					}elseif( !gu_valid_password($_POST['password1']) ){
						$error = 8;
					}elseif( !gu_users_update_password($usr['id'],$_POST['password1']) ) {
						$error = 5;
					}
				}
			}
		}

		// Définit le message d'erreur en fonction du code utilisé
		if( isset($error) ){
			switch( $error ){
				case 1:
					$error = _('Une erreur inattendue s\'est produite lors de la modification de votre adresse postale.') . '<br />' . _('Veuillez réessayer ou prendre contact avec nous pour nous le signaler.');
					break;
				case 2:
					$error = _('Une erreur inattendue s\'est produite lors de la modification de votre adresse e-mail.') . '<br />' . _('Veuillez réessayer ou prendre contact avec nous pour nous le signaler.');
					break;
				case 3:
					$error = _('Une erreur inattendue s\'est produite lors de la modification des numéros de téléphone.') . '<br />' . _('Veuillez réessayer ou prendre contact avec nous pour nous le signaler.');
					break;
				case 4:
					$error = _('Veuillez confirmer le nouveau mot de passe en le saisissant à nouveau.');
					break;
				case 5:
					$error = _('Une erreur inattendue s\'est produite lors de l\'enregistrement de votre nouveau mot de passe.') . '<br />' . _('Veuillez réessayer ou prendre contact avec nous pour nous le signaler.');
					break;
				case 6:
					$error = _('Les deux mots de passe saisis sont différents.') . '<br />' . _('Veuillez les saisir à nouveau.');
					break;
				case 7:
					$error = _('Le mot de passe doit contenir un minimum de 6 caractères pour être accepté.');
					break;
				case 8:
					$error = $config['password_error_message'];
					break;
				case 9 :
					$error = _('Une erreur est survenue lors de la modification de la date de naissance.') . '<br />' . _('Veuillez réessayer ou prendre contact avec nous pour nous le signaler.');
					break;
				case 10 :
					$error = _('Une erreur inconnue est survenue lors de la mise à jour de la langue.') . '<br />' . _('Veuillez réessayer ou prendre contact avec nous pour nous le signaler.');
					break;
			}
		}

		if( !isset($error) ){
			$_SESSION['account_edit_success'] = true;
			header('Location: /admin/options/my-account.php');
			exit;
		}

	}

	if (!isset( $_SESSION['lang'])) {
		 $_SESSION['lang'] = 'fr';
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Mes options'), '/admin/options/index.php' )
		->push( _('Mon compte') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Mon compte') . ' - ' . _('Mes options'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php echo _('Mon compte'); ?></h2>
<?php
	if( isset($_SESSION['account_edit_success']) ){
		echo '<div class="success">'._('Les préférences de votre compte ont été mises à jour avec succès.').'</div>';
		unset($_SESSION['account_edit_success']);
	}

	if( isset($error) ){
		print '<div class="error">'.nl2br($error).'</div>';
	}

?>

<form class="myaccount" id="form-edit-user" action="my-account.php" method="post">

	<script><!--
			$('#form-edit-user').attr('autocomplete','off');
			$(document).ready(function(){
				$('#password1').val('');
				$('#password2').val('');
			});
	--></script>

	<dl>
		<dt><?php print _('Mes informations personnelles'); ?></dt>
		<dd><label><span class="mandatory">*</span> <?php print _('Civilité :'); ?></label>
			<?php
				$titles = gu_titles_get();
				while( $r = ria_mysql_fetch_array($titles) ){
					print '<input type="radio" class="radio" name="title" id="title-'.$r['id'].'" value="'.$r['id'].'" '.( $adr['title_id']==$r['id'] ? ' checked="checked"':'').'/> ';
					print '<label class="inline" for="title-'.$r['id'].'">'.htmlspecialchars(_($r['name'])).'</label> ';
				}
			?>
		</dd>
		<dd><label for="firstname"><span class="mandatory">*</span> <?php print _('Mon prénom :'); ?></label><input type="text" class="large" name="firstname" id="firstname" value="<?php print htmlspecialchars($adr['firstname']); ?>" maxlength="75" /></dd>
		<dd><label for="lastname"><span class="mandatory">*</span> <?php print _('Mon nom de famille :'); ?></label><input type="text" class="large" name="lastname" id="lastname" value="<?php print htmlspecialchars($adr['lastname']); ?>" maxlength="75" /></dd>
		<dd><label for="dob"><?php print _('Ma date de naissance :'); ?></label><input type="text" class="large" id="dob" name="dob" value="<?php print dateheureunparse($usr['dob'], false); ?>"></dd>
	</dl>

	<dl>
		<dt><?php print _('Pour me contacter'); ?></dt>
		<dd><label for="email"><span class="mandatory">*</span> <?php print _('Mon adresse email :'); ?></label><input type="email" class="large" id="email" name="email" value="<?php print htmlspecialchars($usr['email']); ?>" /></dd>
		<dd><label for="tel"><?php print _('Téléphone :'); ?></label><input type="tel" class="large" id="phone" name="phone" value="<?php print htmlspecialchars($adr['phone']); ?>" /></dd>
		<dd><label for="fax"><?php print _('Fax :'); ?></label><input type="tel" class="large" id="fax" name="fax" value="<?php print htmlspecialchars($adr['fax']); ?>" /></dd>
		<dd><label for="mobile"><?php print _('Portable :'); ?></label><input type="tel" class="large" id="mobile" name="mobile" value="<?php print htmlspecialchars($adr['mobile']); ?>" /></dd>
		<dd><label for="phone-work"><?php print _('Ligne directe :'); ?></label><input type="tel" class="large" id="phone-work" name="phone-work" value="<?php ( $adr['phone_work']!='' ? print htmlspecialchars($adr['phone_work']) : '' ); ?>" /></dd>
	</dl>

	<dl>
		<dt><?php print _('Mon mot de passe'); ?></dt>
		<dd class="chgPassComplete">
			<div class="notice"><?php print _('Le nouveau mot de passe ne sera pas conservé ici. À vous de le conserver et/ou de le transmettre à la personne concernée.') ?></div>
		</dd>
		<dd class="chgPassComplete" style="display: flex; flex-wrap: wrap;">
			<label for="password1"><?php print _('Nouveau mot de passe :'); ?></label>
			<div class="password-container input-container">
				<input type="password" value="" class="text password" name="password1" id="password1" maxlength="16" autocomplete="new-password" style="width: 100% !important; max-width: 470px;" />
				<span class="eye" style="top: 9px" id="eye1" onclick="eyeClick($(this))"></span>
			</div>
		</dd>
		<dd class="chgPassComplete" style="display: flex; flex-wrap: wrap;">
			<label for="password2"><?php print _('Confirmation :'); ?></label>
			<div class="password-container input-container">
				<input type="password" value="" class="text password" name="password2" id="password2" maxlength="16" autocomplete="new-password" style="width: 100% !important; max-width: 470px;" />
				<span class="eye" style="top: 9px" id="eye2" onclick="eyeClick($(this))"></span>
			</div>
		</dd>
		<dd class="chgPassComplete">
		<sub style="color: #05114f; font-size: 0.95em">
			<?php if( isset($config['password_regex_description']) ){
				print nl2br( $config['password_regex_description'] );
			} else {
				print _('Pour un mot de passe robuste, utilisez au moins 12 caractères avec des lettres, des chiffres et des symboles.');
				?><br/><?php
				print _("Le mot de passe que vous choisirez doit comporter de 6 à 32 caractères, et être composé à votre convenance : de lettres, de chiffres et de caractères spéciaux tel que :  « - », « _ » et « = ».");
				?><br/><?php
				print _("Les espaces et les caractères accentués (à, é, ï, ô, ù, ç, etc.) ne seront pas acceptés.");
			} ?>
		</sub>
		</dd>
		<dd class="chgPassComplete">
			<input type="button" style="display: none;" id="clrTextPassword" name="clrTextPassword" onclick="resetPassFields()" value="<?php print _('Tout réinitialiser') ?>">
		</dd>
	</dl>

	<dl>
		<dt><?php print _('Langue'); ?></dt>
        <dd><label for="language"><?php print _('Choix de la langue :'); ?></label></dd>
        <dd><select id="language" name="language">
            <option value="fr" <?php print  $_SESSION['lang'] == 'fr_FR' ? 'selected="selected"' : ''; ?>><?php print _('Français'); ?></option>
			<option value="de" <?php print  $_SESSION['lang'] == 'de_DE' ? 'selected="selected"' : ''; ?>><?php print _('Allemand'); ?></option>
            <option value="en" <?php print  $_SESSION['lang'] == 'en_GB' ? 'selected="selected"' : ''; ?>><?php print _('Anglais'); ?></option>
        </select></dd>
	</dl>

	<div class="ria-admin-ui-actions">
		<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" title="<?php print _('Enregistrer les modifications'); ?>" />
		<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" title="<?php print _('Annuler les modifications'); ?>" />
	</div>

</form>

<script src='../js/options/passwords.js'></script>

<script>
	var control = /<?php if( isset($config['password_regex']) ){ print $config['password_regex']; } else { print ('^[a-zA-Z0-9\-\_\=\.$#§\/\[\]\{\}\<\>\&\(\)\+\=\*\%\!\;\,\?\:]{6,}$'); }?>/ ;
</script>

<?php
	require_once('admin/skin/footer.inc.php');
?>