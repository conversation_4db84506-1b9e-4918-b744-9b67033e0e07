<?php
	/**	\file notifications.php
	 *	Cette page permet à un administrateur de s'inscrire aux notifications liées au flux.
	 */
	require_once('flow/notifications.inc.php');

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_OPTION_ACCOUNT');

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Notifications mails').' - '._('Mes options'));

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
	->push( _('Mes options'), '/admin/options/index.php' )
	->push( _('Notifications mails') );

	// Charge les notifications dont l'administrateur est inscript
	$ar_notifs_inscript = [];
	$temp = cfg_overrides_get_value( 'admin_flow_notifications_send', null, $_SESSION['usr_id'] );
	if( trim($temp) != '' ){
		$ar_notifs_inscript = json_decode( $temp, true );
	}

	// Sauvegarde les options de notifications
	if( isset($_POST['save']) ){
		if( !isset($_POST['notif']) ){
			$_POST['notif'] = [];
		}

		if( !cfg_overrides_set_value('admin_flow_notifications_send', json_encode($_POST['notif']), 0, $_SESSION['usr_id']) ){
			$error = _('Une erreur inattendue s\'est produite lors de l\'enregistrement des options de notifications.');
		}

		if( !isset($error) ){
			header('Location: notifications.php');
			exit;
		}
	}


	require_once('admin/skin/header.inc.php');

	print '<h2>'._('Notifications mails').'</h2>';

	if( isset($error) ){
		print '<div class="error">'.nl2br( $error ).'</div>';
	}

	$ar_notifs = flow_notifications_get_codes();

	print '<form id="form-edit-notifs" action="notifications.php" method="post">'
		.'<p>'
			._('Vous pouvez cocher ci-dessous les notifications mails que vous souhaitez recevoir selon les activités du flux :')
		.'</p>';

		$last_group = '';

		foreach( $ar_notifs as $key=>$data ){
			$checked = '';
			if( in_array($data['value'], $ar_notifs_inscript) ){
				$checked = 'checked="checked"';
			}

			if( $last_group != $data['group'] ){
				if( $last_group != '' ){
					print '</dl>';
				}

				print '<dl>'
					.'<dt>'.htmlspecialchars( $data['group'] ).'</dt>';

				$last_group = $data['group'];
			}

			print '<dd>'
				.'<input type="checkbox" name="notif[]" id="notif'.$key.'" value="'.$data['value'].'" '.$checked.' /> '
				.'<label for="notif'.$key.'">'.htmlspecialchars( $data['label'] ).'</label>'
			.'</dd>';
		}

		print '</dl>';

		print '<br /><input type="submit" name="save" value="'._('Enregistrer').'" />'
	.'</form>';

	require_once('admin/skin/footer.inc.php');
