<?php

	/**	\file shorten.php
	 * 	Réduire une url longue en url courte. Utilise pour cela le service hideuri.com.
	 * 	@param string url L'url à réduire (en GET)
	 * 	@return json la réponse est un tableau associatif contenant les clés suivantes :
	 * 		- result_url : contient l'url réduite
	 * 		- error : Facultatif, contient le message d'erreur s'il y en a un.
	 */

	require_once('http.inc.php');

	if( !isset($_GET['url']) ){
		error_log( 'shorten : le paramètre obligatoire url est manquant' );
		http_403();
	}

	// Utilise l'api de hideuri
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, "https://hideuri.com/api/v1/shorten");
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($ch, CURLOPT_POST, true );
	curl_setopt($ch, CURLOPT_POSTFIELDS, [ 'url' => $_GET['url'] ]);
	$output = curl_exec($ch);
	curl_close($ch);

	// Envoie le résultat au navigateur
	header('Content-Type: application/json');
	print $output;
