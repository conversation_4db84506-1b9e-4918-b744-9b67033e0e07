<?php

	/**	\file index.php
	 *	Cette page affiche la liste des profils utilisateurs. Elle permet la création de droits limités à l'installation en cours.
	 */

	require_once('profiles.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_RIGHT');

	// Bouton "Nouveau droit d'accès"
	if( isset($_POST['add-prf']) ){
		header('Location: /admin/customers/profiles/new.php');
		exit;
	}

	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Clients'), '/admin/customers/index.php' )
		->push( _('Gestion des profils') );

	define('ADMIN_PAGE_TITLE', _('Gestion des profils') . ' - ' . _('Comptes clients'));
	require_once('admin/skin/header.inc.php');

	// Affiche un message de confirmation de suppression
	if( isset($_GET['del']) ){
		$success = _('Le profil utilisateur a été supprimé comme demandé.');
	}
?>
<h2><?php print _('Gestion des profils'); ?></h2>

<form action="/admin/customers/profiles/index.php" method="post">
<?php
	if( isset($success) ){
		print '<div class="success">'.nl2br($success).'</div>';
	}
?>
	<p><?php print _('Vous trouverez ci-dessous un tableau contenant tous profils administrables.')?></p>
	<table class="checklist" id="table-profiles">
		<thead>
			<tr>
				<th id="prf-name" class="thead-none"><?php print _('Nom')?></th>
				<th id="prf-pl-name" class="thead-none"><?php print _('Nom au pluriel')?></th>
				<th id="prf-users" class="thead-none"><?php print _('Compte(s) rattaché(s)')?></th>
			</tr>
		</thead>
		<?php if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_RIGHT_ADD') ){ ?>
		<tfoot>
			<tr>
				<td colspan="3">
					<input type="submit" name="add-prf" id="add-prf" value="<?php print _('Nouveau droit d\'accès')?>" title="<?php print _('Ajouter un nouveau droit d\'accès'); ?>" />
				</td>
			</tr>
		</tfoot>
		<?php } ?>
		<tbody><?php
			// Permet de rechercher les profils du tenant et géneriques
			$rprf = gu_profiles_get( false, false, false, false, null);
			if( $rprf && ria_mysql_num_rows($rprf) ){
				while( $prf = ria_mysql_fetch_array($rprf) ){
					print '	<tr>';
					if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_RIGHT_VIEW') ){
						print '	<td data-label="'._('Nom :').'"><a href="/admin/customers/profiles/edit.php?prf='.$prf['id'].'">'.htmlspecialchars($prf['name']).'</a></td>';
					}else{
						print '	<td data-label="'._('Nom :').'">'.$prf['name'].'</td>';
					}
					print '		<td data-label="'._('Nom au pluriel :').'">'.htmlspecialchars( $prf['pl_name'] ).'</td>';
					if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER') ){
						print '	<td data-label="'._('Compte(s) rattaché(s) :').'"><a target="_blank" href="/admin/customers/index.php?prf='.$prf['id'].'">'.$prf['users'].' '.( $prf['users']>1 ? _('comptes rattachés') : _('compte rattaché') ).'</a></td>';
					}else{
						print '	<td data-label="'._('Compte(s) rattaché(s) :').'">'.$prf['users'].' '.( $prf['users']>1 ? _('comptes rattachés') : _('compte rattaché') ).'</td>';
					}
					print '	</tr>';
				}
			} else {
				print '<tr><td colspan="3">'._('Aucun droit d\'accès administrable n\'a été trouvé.').'</td></tr>';
			}
		?></tbody>
	</table>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>