<?php

	/**	\file index.php
	 *
	 *	Ce fichier permet l'affichage d'une liste de comptes utilisateurs, quel que soit leur profil :
	 *	 - Administrateurs,
	 *	 - Particuliers
	 *	 - Professionnels
	 *	 - Représentants
	 *	 - Fournisseurs
	 *	 - Etc (si des profils spécifiques à l'installation existent)
	 *
	 *	Cette liste pourra éventuellement être filtrée suivant différents critères (segment, profil, représentant, etc...)
	 *	et exportée dans un format compatible avec Excel.
	 *
	 */

	require_once('users.inc.php');

	// Pour stocker les messages d'erreur
	$errors = array();

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

	$have_fld_filter = false; 	// Filtre par champ libre
	$have_mdl_filter = false;	// Filtre par modèle de saisie
	$users_count = 0; 			// Nombre d'utilisateurs

	// Contrôle le filtre par Profil (paramètre de type GET)
	if( !isset($_GET['prf']) ){
		$_GET['prf'] = '';
	}elseif( $_GET['prf']!='' ){
		if( !gu_profiles_exists($_GET['prf']) ){
			$_GET['prf'] = '';
		}
	}

	// Contrôle le filtre par profil (formulaire de type POST)
	if( isset($_POST['filter']) && isset($_POST['filter-prf']) ){
		if( gu_profiles_exists($_POST['filter-prf']) ){
			$_GET['prf'] = $_POST['filter-prf'];
		}
	}

	// Charge le profil si un filtre de profil est actif
	if( $_GET['prf']!='' ){
		$rprf = gu_profiles_get( $_GET['prf'] );
		if( ria_mysql_num_rows($rprf) ){
			$profile = ria_mysql_fetch_array( $rprf );
		}else{
			$_GET['prf'] = '';
		}
	}

	// Contrôle le filtre par Segment
	if( isset($_GET['seg']) ){
		if( !seg_segments_exists($_GET['seg']) ){
			$_GET['seg'] = 0;
		}
	}else{
		$_GET['seg'] = 0;
	}

	// Les filtres "En attente de synchronisation" et "Par représentant" utilisent une liste de comptes clients
	$customers_ids = 0; // Contiendra la liste des identifiants de comptes clients à afficher

	// Contrôle le filtre "En attente de synchronisation"
	if( isset($_GET['need-sync']) ){

		$customers_ids = array();
		$rusers = gu_users_toimport_get();
		while( $r = mysql_fetch_assoc($rusers) ){
			$customers_ids[] = $r['id'];
		}

	}

	// Contrôle le filtre par Représentant
	$_GET['seller_id'] = 0;
	if( isset($_GET['seller']) && $_GET['seller'] != 0){
		// Vérifie que le compte utilisateur existe et qu'il s'agit bien d'un représentant
		if( !gu_users_exists( $_GET['seller'], 5 ) ){
			$_GET['seller'] = 0;
		}else{
			// Charge le compte représentant pour être en mesure d'afficher son nom dans le titre de la page
			$rseller = gu_users_get( $_GET['seller'] );
			if( !ria_mysql_num_rows($rseller) ){
				$_GET['seller'] = 0;
			}else{
				$seller = ria_mysql_fetch_array($rseller);
				if( $seller['restrict_portfolio'] == '1' ){
					$customers_ids = gu_users_seller_customers_get($_GET['seller']);
				}
				if( $seller['seller_id'] ){
					$_GET['seller_id'] = $seller['seller_id'];
				}else{
					$_GET['seller'] = 0;
				}

			}
		}
	}else{
		$_GET['seller'] = 0;
	}

	// Redirige vers l'export "Excel" si cette action est demandée
	if( isset($_GET['downloadexport']) ){
		$file = $config['doc_dir'] . '/customers.csv';
		if( file_exists($file) ){
			header('Content-Description: File Transfer');
			header('Content-Type: application/octet-stream');
			header('Content-Disposition: attachment; filename="customers.csv"');
			header('Expires: 0');
			header('Cache-Control: must-revalidate');
			header('Pragma: public');
			header('Content-Length: ' . filesize($file));
			readfile ($file);
			exit;
		}else{
			$errors[] = _("Le fichier ne semble plus disponible, veuillez préparer un nouvel export en cliquant sur le bouton \"Exporter\".");
		}
	}

	// Redirige vers le formulaire de création si cette action est demandée
	if( isset($_POST['add']) ){
		header('Location: new.php?prf='.$_GET['prf']);
		exit;
	}

	// Gère la suppression de comptes utilisateurs
	if( isset($_POST['del-users'], $_POST['usr-id']) ){
		foreach( $_POST['usr-id'] as $key ){
			if( !gu_users_del($key) ){
				if( isset($_SESSION['usr_id']) && $key==$_SESSION['usr_id'] ){
					$errors[] = _("Attention, vous avez failli supprimer votre compte. Cette action n'est pas autorisée.")."\n"._("Si vous souhaitez réellement supprimer votre compte, demandez soit à un autre administrateur, soit au support.");
				}else{
					// Construit un identifiant facilement reconnaissable par l'utilisateur
					$ref = gu_users_get_ref( $key );
					$email = gu_users_get_email( $key );
					$usr_id = $ref ? $ref : $email;
					// Affiche un message qui soit explicite pour l'utilisateur
					if( gu_users_get_is_sync( $key ) ){
						$errors[] = sprintf(_("Le compte %s est synchronisé avec une autre source, il ne peut pas être supprimé ici. Si vous souhaitez le supprimer, il doit être supprimé depuis sa source d'origine."), $usr_id );
					}else{
						$errors[] = sprintf(_("Une erreur inattendue s'est produite lors de la suppression du compte %s"), $usr_id)."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
					}
				}
			}
		}
		if( !count($errors) ){
			header('Location: index.php?prf='.$_GET['prf'].'&seg='.$_GET['seg']);
			exit;
		}
	}

	$checkbox = gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_DEL');

	// Affiche l'entête de la page, uniquement si le chargement n'est pas réalisé en Ajax
	if( !IS_AJAX ){
		$is_set_profile = false;

		Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
			->push( _('Tous les comptes'), '/admin/customers/index.php' );

		// Construit le titre de la page (balise title)
		if( isset($profile) ){
			$is_set_profile = true;
			$page_title = $profile['pl_name'];
			Breadcrumbs::add( $profile['pl_name'] );
		}elseif( isset($_GET['seg']) && $_GET['seg']>0 && ($s = ria_mysql_fetch_array(seg_segments_get($_GET['seg'], CLS_USER))) ) {
			$page_title = $s['name'];
			Breadcrumbs::add( $s['name'] );
		}elseif( isset($seller) && isset($seller['adr_firstname']) && isset($seller['adr_lastname']) ){
			$page_title = _('Clients de ').$seller['adr_firstname'].' '.$seller['adr_lastname'];
			Breadcrumbs::add( _('Représentants'), '/admin/customers/index.php?prf='.PRF_SELLER );
			Breadcrumbs::add( $page_title );
		}elseif( isset($_GET['need-sync']) ){
			$page_title = _('En attente de synchronisation');
			Breadcrumbs::add( $page_title );
		}else{
			$page_title = _('Comptes clients');
		}

		// Affiche l'entête de la page
		define('ADMIN_PAGE_TITLE', $page_title);
		require_once('admin/skin/header.inc.php');
	}

	if( !isset($_REQUEST['sort']) ) $_REQUEST['sort'] = 'created';
	if( !isset($_REQUEST['dir']) ) $_REQUEST['dir'] = 'desc';

	// Récupère un éventuel modèle utilisé comme filtre
	if( isset($_GET['mdl']) && is_numeric($_GET['mdl']) && $_GET['mdl']>0 ){
		if( $rmodel = fld_models_get($_GET['mdl']) ){
			$model = ria_mysql_fetch_array($rmodel);
			if( $model['cls_id']==CLS_USER ){
				$users = fld_models_get_objects( $_GET['mdl'] );
				$users_count = ria_mysql_num_rows($users);
				$have_mdl_filter = true;
			}
		}
	}

	// Récupère un éventuel champ libre utilisé comme filtre (non compatible avec un filtre par modèle)
	if( !$have_mdl_filter ){
		if( isset($_GET['fld']) && is_numeric($_GET['fld']) && $_GET['fld']>0 ){
			if( $rfield = fld_fields_get($_GET['fld']) ){
				$field = ria_mysql_fetch_array($rfield);
				if( $field['cls_id']==CLS_USER ){
					$users = fld_fields_get_objects( $_GET['fld'] );
					$users_count = ria_mysql_num_rows($users);
					$have_fld_filter = true;
				}
			}
		}
	}

	$start = 0;  // Début de la page en cours
	$limit = 50; // Nombre de comptes affichés par page

	if (isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] > 0){
		$start = ($_GET['page'] - 1) * $limit;
	}

	// Filtre sur un segment
	$prf_filter = 0;
	if( $_GET['seg'] || ( !$have_fld_filter && !$have_mdl_filter ) ){

		// argument profil : si non spécifié, on prend tout, sauf si segment
		$prf_filter = is_numeric($_GET['prf']) ? $_GET['prf'] : 0;
		if( isset($_GET['seller']) && is_numeric($_GET['seller']) && $_GET['seller'] > 0 && $customers_ids > 0 && $prf_filter == 0){
			$prf_filter = array(PRF_ADMIN, PRF_CUSTOMER, PRF_CUST_PRO, PRF_RESELLER);
		}

		if( is_array($customers_ids) && empty($customers_ids) ){
			$users = false;
		}else{
			$users = gu_users_get( $customers_ids, '', '', $prf_filter, '', 0, '', $_REQUEST['sort'],$_REQUEST['dir'], false, false, '', false, 0, '', 0, false, true, null, $_GET['seg'] ? $_GET['seg'] : 0, false, false, null, false, 0, 0, false, false, $start, $limit );
		}
		if ($_GET['seg']){
			if( $start==0 && ria_mysql_num_rows($users)<$limit ){
				// La valeur de $seg['objects'] n'est pas toujours fiable, utilise le nombre de comptes retourné par gu_users_get
				// lorsque c'est pertinent
				$users_count = ria_mysql_num_rows($users);
			}else{
				$r_seg = seg_segments_get($_GET['seg']);
				if ($r_seg && ria_mysql_num_rows($r_seg)) {
					$seg = ria_mysql_fetch_assoc($r_seg);
					$users_count = $seg['objects'];
				}
			}
		} elseif(isset($_GET['seller']) && is_numeric($_GET['seller']) && $_GET['seller'] > 0 && $customers_ids > 0 ){
			$seller_customers = gu_users_seller_customers_get($_GET['seller'], true, array(PRF_ADMIN, PRF_CUSTOMER, PRF_CUST_PRO, PRF_RESELLER));
			if (isset($seller_customers[$_GET['seller']])){
				$users_count = count($seller_customers[$_GET['seller']]);
			} else {
				$users_count = 0;
			}
		}elseif( isset($_GET['need-sync']) ){

			$users_count = sizeof($customers_ids);

		} else {
			$r_profile = gu_profiles_get( $prf_filter, false, false, false, null, true );
			if ($r_profile && ria_mysql_num_rows($r_profile)) {
				if ($prf_filter) {
					$profile = ria_mysql_fetch_assoc($r_profile);
					$users_count = $profile['users'];
				} else {
					$users_count = 0;
					while( $profile = ria_mysql_fetch_assoc($r_profile) ){
						$users_count += $profile['users'];
					}
				}
			}
		}
	}

	if( !IS_AJAX ){
		// Construit le titre de la page (balise h2)
		print '<form class="cust-listing-edit" action="index.php?seg='.$_GET['seg'].'&amp;prf='.$_GET['prf'].'" method="post">';
		print '<h2>';
		if( $is_set_profile ){
			print htmlspecialchars( $profile['pl_name'] );
			print ' ('.ria_number_format($users_count, NumberFormatter::DECIMAL).')';
			if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_RIGHT_EDIT') ){
				print '	<a class="edit-cat" href="/admin/customers/profiles/edit.php?prf='.$_GET['prf'].'">'._('Modifier ce droit d\'accès').'</a>';
			}
		}elseif( isset($_GET['seg']) && $_GET['seg']>0 && ($s = ria_mysql_fetch_array(seg_segments_get($_GET['seg'], CLS_USER))) ) {
			print htmlspecialchars( $s['name'] );
			print ' ('.ria_number_format($users_count, NumberFormatter::DECIMAL).')';
			print '	<a class="edit-cat" href="/admin/customers/segments/segment.php?id='.$s['id'].'">'._('Modifier ce segment').'</a>';
		}elseif( isset($seller) && isset($seller['adr_firstname']) && isset($seller['adr_lastname']) ){
			print _('Clients de ').htmlspecialchars( $seller['adr_firstname'].' '.$seller['adr_lastname'] ).' ('.ria_number_format($users_count, NumberFormatter::DECIMAL).')';
		}elseif( isset($_GET['need-sync']) ){
			print _('En attente de synchronisation');
		}else{
			print _('Tous les comptes').' ('.ria_number_format($users_count, NumberFormatter::DECIMAL).')';
		}

		print '</h2>';
		print '<div class="actions">';

		// Bouton Importer
		if( !isset($_GET['need-sync']) ){
			if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD') || gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD_ADMIN') ){
				print '<input type="button" name="import" class="float-right hide-mobile" value="'._('Importer').'" title="'._('Importer des comptes').'" onclick="window.location.href = \'../tools/imports/index.php?imp-class=2\';" style="margin-right: 5px;" />';
			}
		}

		// Bouton Exporter
		if( !isset($_GET['need-sync']) ){
			print '<input type="button" name="export" value="'._('Exporter').'" title="'._('Exporter cette liste de comptes au format CSV.').'" onclick="exportCustomersAccount()" class="float-right hide-mobile" style="margin-right: 5px;" />';
		}

		// Bouton Ajouter
		if( !isset($_GET['need-sync']) ){
			if( isset($profile['name']) && (($profile['id'] == PRF_ADMIN && gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD_ADMIN')) || ($profile['id'] != PRF_ADMIN && gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD'))) ){
				print '<input type="submit" name="add" class="float-right btn-main" value="'._('Ajouter un').' '.htmlspecialchars( strtolower( $profile['name'] ) ).'" title="'._('Ajouter un compte '.strtolower($profile['name']).' à cette liste').'" style="margin-right: 5px;" />';
			}elseif( !isset($profile['name']) && (gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD') || gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD_ADMIN')) ){
				print '<input type="submit" name="add" class="float-right btn-main" value="'._('Ajouter un compte').'" title="'._('Ajouter un nouveau compte à cette liste').'" style="margin-right: 5px;" />';
			}
		}

		print '<div class="clear"></div>';
		print '</div>';
		print '</form>';
	}

	// Affichage des messages d'erreur et de succès
	if( count($errors) ){
		print '<div class="error">';
		print implode('<br />', $errors);
		print '</div>';
	}

	// Calcule le nombre de pages
	$pages = ceil($users_count / $limit);

	// Statistiques de vente pour ce profil (sauf Administrateurs, Représentants et Fournisseurs)
	if( !isset($_GET['need-sync']) ){
		if( $_GET['prf']!=PRF_ADMIN && $_GET['prf']!=PRF_SELLER && $_GET['prf']!=PRF_SUPPLIER && !IS_AJAX && gu_users_admin_rights_used('_MDL_ORDERS') ){
			// Le chargement est réalisé de façon asynchrone en Ajax
			print '
				<div id="tb-stats-menu" class="stats-menu">
					<div id="tb-synthese-order">
						<table class="orders orders-customers" id="table-synthese-order">
							<caption></caption>
							<thead>
								<tr>
									<th id="hd-order-conversion" title="'._('Nombre de clients par nombre de comptes').'">
										<abbr title="'._('Nombre de clients par nombre de comptes').'">'._('Taux de conversion').'</abbr>
									</th>
									<th id="hd-order-total" title="'._('Nombre moyen de commandes réalisées par compte client').'">
										<abbr title="'._('Nombre moyen de commandes réalisées par compte client').'">'._('Commandes / client').'</abbr>
									</th>
									<th id="hd-order-prds" title="'._('Nombre de produits par commande (hors port et assurance)').'">
										<abbr title="'._('Nombre de produits par commande (hors port et assurance)').'">'._('Produits par commande').'</abbr>
									</th>
									<th id="hd-order-avg-ht" title="'._('Chiffre d’affaires moyen par compte client HT').'">
										<abbr title="'._('Chiffre d’affaires moyen par compte client HT').'">'._('CA Moyen').' <abbr title="'._('Hors Taxes').'">'._('HT').'</abbr></abbr>
									</th>
									<th id="hd-order-avg-ttc" title="'._('Chiffre d’affaires moyen par compte client TTC').'">
										<abbr title="'._('Chiffre d’affaires moyen par compte client TTC').'">'._('CA Moyen').' <abbr title="'._('Toutes Taxes Comprises').'">'._('TTC').'</abbr></abbr>
									</th>
			';

			// Contrôle que l'utilisateur a accès à la marge
			if( gu_user_is_authorized('_RGH_ADMIN_MARGE_SHOW') ){
				print '
									<th id="hd-order-margin" title="'._('Marge brute moyenne par compte client (HT)').'">
										<abbr title="'._('Marge brute moyenne par compte client (HT)').'">'._('Marge brute moyenne').'</abbr>
									</th>
				';
			}

			print '
									<th id="hd-order-ht">'._('CA Total').' <abbr title="'._('Hors Taxes').'">'._('HT').'</abbr></th>
									<th id="hd-order-ttc">'._('CA Total').' <abbr title="'._('Toutes Taxes Comprises').'">'._('TTC').'</abbr></th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td headers="hd-order-conversion" data-label="' . _('Taux de conversion :') . ' " id="hd-order-conversion-value"><img src="/admin/images/loader2.gif" class="loader" title="'._('Chargement en cours, veuillez patienter.').'" /></td>
									<td id="hd-order-total-value" data-label="' . _('Commandes / client :') . ' "><img src="/admin/images/loader2.gif" class="loader" title="'._('Chargement en cours, veuillez patienter.').'" /></td>
									<td headers="hd-order-prds" id="hd-order-prds-value" data-label="' . _('Produits par commande :') . ' "><img src="/admin/images/loader2.gif" class="loader" title="'._('Chargement en cours, veuillez patienter.').'" /></td>
									<td headers="hd-order-avg-ht" id="hd-order-avg-ht-value"><img src="/admin/images/loader2.gif" class="loader" title="'._('Chargement en cours, veuillez patienter.').'" /></td>
									<td headers="hd-order-avg-ttc" id="hd-order-avg-ttc-value"><img src="/admin/images/loader2.gif" class="loader" title="'._('Chargement en cours, veuillez patienter.').'" /></td>
			';

			if( gu_user_is_authorized('_RGH_ADMIN_MARGE_SHOW') ){
				print '
									<td headers="hd-order-margin" id="hd-order-margin-value"><img src="/admin/images/loader2.gif" class="loader" title="'._('Chargement en cours, veuillez patienter.').'" /></td>
				';
			}

			print '
									<td headers="hd-order-ht" id="hd-order-ht-value"><img src="/admin/images/loader2.gif" class="loader" title="'._('Chargement en cours, veuillez patienter.').'" /></td>
									<td headers="hd-order-ttc" id="hd-order-ttc-value"><img src="/admin/images/loader2.gif" class="loader" title="'._('Chargement en cours, veuillez patienter.').'" /></td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="clear"></div>
				</div>
			';
		}
	}
?>
<?php if( !IS_AJAX ){ ?>
<div id="user-container" class="table-layout-large">
<?php } ?>
<form action="index.php?seg=<?php print $_GET['seg']; ?>&amp;prf=<?php print $_GET['prf']; ?>" method="post" id="user-table">

	<input type="hidden" name="sort" value="<?php print htmlspecialchars($_REQUEST['sort']); ?>" />
	<input type="hidden" name="dir" value="<?php print htmlspecialchars($_REQUEST['dir']); ?>" />
	<input type="hidden" name="fld" value="<?php print htmlspecialchars(isset($_REQUEST['fld'])? $_REQUEST['fld'] : '0'); ?>" />
	<input type="hidden" id="pages" value="<?php print $pages; ?>" />

	<?php
		$columns = usertableGetColumns();
	?>
	<table id="list-customers" class="checklist tablesorter large">
	<thead>
		<tr>
			<?php if( $checkbox ){ ?>
			<th id="usr-sel" class="col-check" data-label="<?php print _('Tout cocher :'); ?> "><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
			<?php }
				foreach( $columns as $column ){
					$class = $column['sortable'] ? 'header ' : '';
					$class .= $_REQUEST['sort']==$column['code'] ? ( $_REQUEST['dir']=='asc' ? 'headerSortUp' : 'headerSortDown' ) : '';
					$dir = ($_REQUEST['sort']==$column['code'] && $_REQUEST['dir']=='desc') ? 'asc' : 'desc';
					print '<th id="'.$column['id'].'" class="thead-none '.$class.'" title="'.$column['title'].'">';
					if( $column['sortable'] ){
						print '<a  href="index.php?sort='.$column['code'].'&amp;seller='.$_GET['seller'].'&amp;seg='.$_GET['seg'].'&amp;prf='.$_GET['prf'].'&amp;dir='.$dir.'">';
					}
					print htmlspecialchars( $column['name'] );
					if( $column['sortable'] ){
						print '</a>';
					}
					print '</th>';
				}
			?>
		</tr>
	</thead>
	<?php

// Détermine la page en cours de consultation
$page = 1;
if( isset($_GET['page']) && is_numeric($_GET['page']) ){
	if( $_GET['page']>0 && $_GET['page']<=$pages ){
		$page = $_GET['page'];
	}
}

// Détermine les limites inférieures et supérieures pour l'affichage des pages
$pmin = $page-5;
if( $pmin<1 ){
	$pmin = 1;
}
$pmax = $pmin+9;
if( $pmax>$pages ){
	$pmax = $pages;
}

?>
	<tbody>
		<?php
			if( $users_count==0 ){
				print '<tr><td colspan="'.( sizeof($columns)+1 ).'">'.
					( !isset($_GET['need-sync']) ? _('Aucun compte') : _('Aucun compte en attente de synchronisation') )
				.'</td></tr>';
			}else{
				$lcount = 0;
				$seller_ids = $sellers_seller_ids = $stats_orders = $seller_customers = array();
				while( ($r = ria_mysql_fetch_assoc($users)) && $lcount<$limit ){
					if( $have_fld_filter || $have_mdl_filter ){
						if( $ru = gu_users_get($r['obj_id']) ){
							$r = ria_mysql_fetch_array( $ru );
						}else{
							$r = null;
						}
					}
					if( $_GET['prf']==PRF_SELLER && isset($r['seller_id']) && $r['seller_id']!=0 ){
						$sellers_seller_ids[] = $r['seller_id'];
						$seller_ids[] = $r['id'];
					}
				}
				if (!empty($seller_ids)) {
					$seller_customers = gu_users_seller_customers_get($seller_ids, true, array(PRF_ADMIN, PRF_CUSTOMER, PRF_CUST_PRO, PRF_RESELLER));
					$r_stats = ord_orders_totals_get(ord_states_get_ord_valid(), 0, 0, 0, true, false, 0, 0, false, $sellers_seller_ids, array(), 0, 0, 0, false, true);
					if ($r_stats && ria_mysql_num_rows($r_stats)) {
						while( $stat = ria_mysql_fetch_assoc($r_stats)) {
							$stats_orders[$stat['seller_id']] = $stat;
						}
					}
				}
				$lcount = 0;
				ria_mysql_data_seek($users, 0);
				while( ($r = ria_mysql_fetch_assoc($users)) && $lcount<$limit ){
					if( $have_fld_filter || $have_mdl_filter ){
						if( $ru = gu_users_get($r['obj_id']) ){
							$r = ria_mysql_fetch_assoc( $ru );
						}else{
							$r = null;
						}
					}

					if( is_null($r) ){
						$lcount++;
						continue;
					}
					$customers_count = $orders_count = 0;
					$stats = array( 'total_ht' => 0 );

					if (array_key_exists($r['seller_id'], $stats_orders)) {
						$stats = $stats_orders[$r['seller_id']];
						$orders_count = $stats['count'];
					}
					print '<tr>';

					if( $checkbox ){
						print '	<td headers="usr-sel">
									<input type="checkbox" class="checkbox" name="usr-id[]" id="usr-id-'.$r['id'].'" value="'.$r['id'].'"
										'.( $r['id']==$_SESSION['usr_id'] ? 'disabled="disabled" title="'._("Vous ne pouvez pas supprimer votre propre compte. Si c'est votre souhait, demandez soit à un autre administrateur, soit au support.").'"' : '' ).' />
								</td>';
					}

					// Calcul des statistiques pour les représentants
					if( $_GET['prf']==PRF_SELLER && isset($r['seller_id']) && $r['seller_id']!=0 ){
						if (array_key_exists($r['id'], $seller_customers)) {
							$customers_count = count($seller_customers[$r['id']]);
						}
					}

					foreach( $columns as $column ){

						print '<td headers="'.$column['id'].'" class="align-'.$column['align'].'" data-label="'._($column['name'].' :').' ">';
						switch( $column['code'] ){
							case 'ref':
								print htmlspecialchars($r['ref']);
								break;
							case 'name': // Société, Nom et Prénom
								if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_VIEW') ){
									print view_usr_is_sync($r).' <a href="'.uw('edit.php?usr='.$r['id'].'&amp;prf='.$_GET['prf'].'&amp;page='.$page.'&amp;seg=' . $_GET['seg'] . (isset($_GET['seller']) && $_GET['seller'] ? '&seller='.$_GET['seller'] : '') ) . '" title="'._('Afficher la fiche de cet utilisateur').'">';
								}
								if( $r['adr_invoices'] ){
									$r['adr_lastname'] = strtolower2($r['adr_lastname']) == 'nc' ? '' : $r['adr_lastname'];
									$r['adr_firstname'] = strtolower2($r['adr_firstname']) == 'nc' ? '' : $r['adr_firstname'];
									$r['society'] = strtolower2($r['society']) == 'nc' ? '' : $r['society'];

									ob_start();
									if( $r['type_id']==1 ){ // Particulier
										print htmlspecialchars($r['adr_lastname']);
										print trim($r['adr_lastname']) && trim($r['adr_firstname']) ? ', ' : '';
										print htmlspecialchars($r['adr_firstname']);
									}elseif( $r['type_id']==2 ){ // Société
										print htmlspecialchars($r['society']);
									}else{ // Professionnel ou autre
										print trim($r['society']) ? htmlspecialchars($r['society']) : '';
										print trim($r['society']) && (trim($r['adr_lastname']) || trim($r['adr_firstname'])) ? ', ' : '';
										print trim($r['adr_lastname']) ? htmlspecialchars($r['adr_lastname']) : '';
										print trim($r['adr_lastname']) && trim($r['adr_firstname']) ? ', ' : '';
										print htmlspecialchars($r['adr_firstname']);
									}
									$name_user = ob_get_clean();
									if( trim($name_user) == '' ){
										$name_user = htmlspecialchars($r['email']);
									}

									print $name_user;
								}else{
									print _('Non disponible');
								}
								if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_VIEW') ){
									print '</a>';
								}

								break;
							case 'created': // Date de création
								print ria_date_format($r['date_created']);
								break;
							case 'login': // Date de dernière connexion
								print $r['last_login'] ? ria_date_format($r['last_login'], true) : 'N/A';
								break;
							case 'last_login_child': // Date de dernière connexion du compte enfant
								$last_login = 'N/A';
								if( !is_null($r['last_login_child']) ){
									$last_login = ria_date_format( $r['last_login_child'], true );
								}
								print $last_login;
								break;
							case 'orders': // Commandes
								print ria_number_format($r['orders'], NumberFormatter::DECIMAL);
								break;
							case 'canceled': // Commandes annulées
								print ria_number_format($r['orders_canceled'], NumberFormatter::DECIMAL);
								break;
							case 'profile': // Profil utilisateur
								print htmlspecialchars( $r['prf_name'] );
								break;
							case 'seller': // Représentant

								if( $r['id']==$r['seller_id'] ){
									break;
								}
								$rseller = gu_users_get( 0, '', '', PRF_SELLER, '', 0, '', false, false, $r['seller_id'] );
								// $rseller = gu_users_get($r['seller_id'], '', '', PRF_SELLER); // corrections faite pour nipahut

								if( !ria_mysql_num_rows($rseller) ){
									break;
								}
								$seller = ria_mysql_fetch_assoc($rseller);
								$admin_customer_view = gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_VIEW');

								if( $admin_customer_view ){
									print '<a href="edit.php?usr='.$seller['id'].'">';
								}
								print htmlspecialchars( $seller['adr_firstname'].' '.$seller['adr_lastname'] );

								if( $admin_customer_view ){
									print '</a>';
								}
								break;
							case 'accounts': // Nombre de comptes suspects/prospects/clients liés à ce représentant
								if( $customers_count>0 ) print '<a href="index.php?seller='.$r['id'].'">';
								print ria_number_format($customers_count, NumberFormatter::DECIMAL);
								if( $customers_count>0 ) print '</a>';
								break;
							case 'order-avg': // Commandes/client
								if( $customers_count>0 ){
									print ria_number_format($orders_count / $customers_count, NumberFormatter::DECIMAL, 2);
								}else{
									print '0,00';
								}
								break;
							case 'order-avg-ht-value': // Commande moyenne HT des clients de ce représentant
								if( $customers_count>0 ){
									print ria_number_format($stats['total_ht'] / $customers_count, NumberFormatter::CURRENCY, 2);
								}else{
									print '0,00 €';
								}
								break;
							case 'orders-total-ht': // Chiffre d'affaires total du commercial
								print ria_number_format($stats['total_ht'], NumberFormatter::CURRENCY, 2);
								break;
							case 'wst':
								print htmlspecialchars(wst_websites_get_name(intval($r['wst_id']) > 0 ? $r['wst_id'] : $config['wst_id']));
								break;
						}
						print '</td>';
					}

					print '</tr>';
					$lcount++;
				}
			}
		?>
	</tbody>

	<tfoot>
	<?php if( $pages>1 ){ ?>
	<tr id="pagination">
		<td colspan="2" class="page align-left"><?php print sprintf( _('Page %d/%d'), $page, $pages ); ?></td>
		<td colspan="<?php echo sizeof($columns) - 1; ?>" class="pages">
			<?php
				if( $pages>1 ){
					$page_url = 'index.php?'.($_GET['seller'] ? '&amp;seller='.$_GET['seller'] : '').
						( $have_mdl_filter ? '&amp;mdl='.$_GET['mdl'] : $have_fld_filter ? '&amp;fld='.$_GET['fld'] : '' ).
						'&amp;seg='.$_GET['seg'].'&amp;prf='.$_GET['prf'].'&amp;sort='.$_REQUEST['sort'].'&amp;dir='.$_REQUEST['dir'].
						( isset($_GET['need-sync']) ? '&amp;need-sync' : '' );

					if( $page>1 ){
						print '<a href="'.$page_url.'&amp;page='.($page-1).'">&laquo; '._('Page précédente').'</a> | ';
					}
					for( $i=$pmin; $i<=$pmax; $i++ ){
						if( $i==$page ){
							print '<b>'.$page.'</b>';
						}else{
							print '<a href="'.$page_url.'&amp;page='.($i).'">'.$i.'</a>';
						}
						if( $i<$pmax ){
							print ' | ';
						}
					}
					if( $page<$pages ){
						print ' | <a href="'.$page_url.'&amp;page='.($page+1).'">'._('Page suivante').' &raquo;</a>';
					}
				}
			?>
		</td>
	</tr>
	<?php } ?>
	<?php if( !isset($_GET['need-sync']) ){ ?>
	<tr>
		<td colspan="<?php print sizeof($columns) - 1; ?>" class="align-left">
			<input type="hidden" name="prf" value="<?php print isset($_GET['prf']) ? htmlspecialchars($_GET['prf']) : '' ?>" />
			<input type="hidden" name="seg" value="<?php print isset($_GET['seg']) ? htmlspecialchars($_GET['seg']) : '' ?>" />
			<input type="hidden" name="seller" value="<?php print isset($_GET['seller']) ? htmlspecialchars($_GET['seller']) : '' ?>" />
			<input type="hidden" name="sort" value="<?php print htmlspecialchars($_REQUEST['sort']); ?>" />
			<input type="hidden" name="dir" value="<?php print htmlspecialchars($_REQUEST['dir']); ?>" />
			<?php if( $users_count ){ ?>
				<?php if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_DEL') ){ ?>
					<input type="submit" name="del-users" value="<?php print _('Supprimer')?>" title="<?php print _('Supprimer les comptes sélectionnés')?>" />
				<?php } ?>
				<input type="button" name="export" id="export" value="<?php print _('Exporter la liste')?>" title="<?php print _('Exporter cette liste de comptes au format CSV.'); ?>" onclick="exportCustomersAccount()" />
			<?php } ?>
			<?php if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD') || gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD_ADMIN') ){ ?>
				<input type="button" name="import" value="<?php print _('Importer')?>" title="<?php print _('Importer des comptes')?>" onclick="window.location.href = '../tools/imports/index.php?imp-class=2';" />
			<?php } ?>
		</td>
		<td colspan="2" class="align-right">
			<?php
				if( isset($profile['name']) && (($profile['id'] == PRF_ADMIN && gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD_ADMIN')) || ($profile['id'] != PRF_ADMIN && gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD'))) ){
					print '<input type="submit" name="add" value="'._('Ajouter un').' '.htmlspecialchars( strtolower( $profile['name'] ) ).'" title="'._('Ajouter un compte '.strtolower($profile['name']).' à cette liste').'" />';
				}elseif( !isset($profile['name']) && (gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD') || gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD_ADMIN')) ){
					print '<input type="submit" name="add" value="'._('Ajouter un compte').'" title="'._('Ajouter un nouveau compte à cette liste').'" class="btn-main" />';
				}
			?>
		</td>
	</tr>
	<?php } ?>
	</tfoot>
	</table>
</form>
<?php if( !IS_AJAX ){ ?>
	</div>
	<script>
	<?php if( !in_array($_GET['prf'], array(PRF_ADMIN, PRF_SELLER, PRF_SUPPLIER)) ){ ?>
		// Statistiques de profil (chargement asynchrone).
		$(document).ready(function(){
			$.ajax({
				type: 'GET',
				url: '/admin/ajax/customers/json-profile-stats.php',
				data: 'prf=<?php print is_array($prf_filter) ? implode(',', $prf_filter) : $prf_filter; ?>&seg=<?php print $_GET['seg']; ?>&users_count=<?php print $users_count; ?>&seller=<?php print $_GET['seller_id']; ?>',
				dataType: 'json',
				async: true,
				success: function( stats ){
					if( !stats['count'] ){
						$('#tb-stats-menu').hide();

						return;
					}

					$('#hd-order-conversion-value').html(stats.conversion); //  Taux de conversion nombre de client avec au moins une commande par nombre de client
					$('#hd-order-total-value').html(stats.users_avg_orders);
					$('#hd-order-prds-value').html(stats.products_avg_orders);
					$('#hd-order-avg-ht-value').html(stats.avg_turnover_ht);
					$('#hd-order-avg-ttc-value').html(stats.avg_turnover_ttc);
					$('#hd-order-margin-value').html(stats.markup);
					$('#hd-order-ht-value').html(stats.total_ht);
					$('#hd-order-ttc-value').html(stats.total_ttc);
				},
				error: function(){
					return true;
				}
			});
		});
	<?php } ?>
	</script>
	<?php require_once('admin/skin/footer.inc.php'); ?>
<?php } ?>

<?php

function usertableGetColumns(){

	// Colonnes du tableau
	$columns = array();

	// Nom de la société, Prénom et Nom
	$columns[] = array(
		'id' => 'usr-name',
		'code' => 'name',
		'name' => _('Société, Nom, Prénom'),
		'align' => 'left',
		'sortable' => true,
		'title' => ''
	);

	// Code client
	$columns[] = array(
		'id' => 'usr-ref',
		'code' => 'ref',
		'name' => _('Code client'),
		'align' => 'left',
		'sortable' => true,
		'title' => ''
	);

	// Date de création
	$columns[] = array(
		'id' => 'usr-created',
		'code' => 'created',
		'name' => _('Date de création'),
		'align' => 'center',
		'sortable' => true,
		'title' => ''
	);

	// Website de création
	if (tnt_tenants_have_websites()) {
		$columns[] = array(
			'id' => 'usr-wst',
			'code' => 'wst',
			'name' => _('Créé depuis'),
			'align' => 'center',
			'sortable' => true,
			'title' => ''
		);
	}

	// Date de dernière connexion
	if( $_GET['prf']==PRF_ADMIN || $_GET['prf']==PRF_SELLER || tnt_tenants_have_websites() ){
		$columns[] = array(
			'id' => 'usr-last-visit',
			'code' => 'login',
			'name' => _('Dernière visite'),
			'align' => 'center',
			'sortable' => true,
			'title' => '',
		);
	}

	// Dernière visite d'un compte sous responsabilité
	if( tnt_tenants_have_websites() && isset($config['gu_users_log_login_childs']) && $config['gu_users_log_login_childs']==1 ){
		$columns[] = array(
			'id' => 'usr-last-visit-child',
			'code' => 'last_login_child',
			'name' => _('Dernière visite compte sous responsabilité'),
			'align' => 'center',
			'sortable' => true,
			'title' => ''
		);
	}

	// Colonnes spécifiques aux représentants
	if( $_GET['prf']==PRF_SELLER ){

		// Nombre de comptes dans son portefeuille
		$columns[] = array(
			'id' => 'usr-accounts',
			'code' => 'accounts',
			'name' => _('Comptes suivis'),
			'align' => 'right',
			'sortable' => true,
			'title' => _('Nombre de comptes prospects/clients attribués à ce représentant')
		);

		// Commandes/client
		$columns[] = array(
			'id' => 'usr-order-avg',
			'code' => 'order-avg',
			'name' => _('Commandes / client'),
			'align' => 'right',
			'sortable' => true,
			'title' => _('Nombre moyen de commandes réalisées par compte client')
		);

		// CA Moyen HT
		$columns[] = array(
			'id' => 'usr-order-avg-ht-value',
			'code' => 'order-avg-ht-value',
			'name' => _('CA Moyen HT'),
			'align' => 'right',
			'sortable' => true,
			'title' => _('Chiffre d\'affaires moyen par compte client')
		);

		// CA Total HT
		$columns[] = array(
			'id' => 'usr-orders-total-ht',
			'code' => 'orders-total-ht',
			'name' => _('CA Total HT'),
			'align' => 'right',
			'sortable' => true,
			'title' => _('Chiffre d\'affaires total')
		);
	}

	// Colonnes spécifiques aux comptes clients
	if( $_GET['prf']!=PRF_ADMIN && $_GET['prf']!=PRF_SUPPLIER && $_GET['prf']!=PRF_SELLER ){

		if( $_GET['prf']==0 && $_GET['seg']==0 ){
			$columns[] = array(
				'id' => 'usr-prf',
				'code' => 'profile',
				'name' => 'Profil',
				'align' => 'left',
				'sortable' => true,
				'title' => ''
			);
		}

		// Représentant (seulement s'il en existe dans l'instance et si la liste n'est pas déjà filtrée sur un représentant)
		if( ( !isset($_GET['seller']) || $_GET['seller']==0 ) && gu_users_have_sellers() ){
			$columns[] = array(
				'id' => 'usr-seller',
				'code' => 'seller',
				'name' => _('Représentant'),
				'align' => 'left',
				'sortable' => true,
				'title' => '',
				'width' => '*'
			);
		}

		// On affiche pas les colonnes "Commandes validées" et "commandes annulées" pour les Riashop qui n'ont pas accès au module Commmandes
		if( gu_users_admin_rights_used('_MDL_ORDERS') ){

			// Commandes validées
			$columns[] = array(
				'id' => 'usr-orders',
				'code' => 'orders',
				'name' => _('Commandes validées'),
				'align' => 'center',
				'sortable' => true,
				'title' => '',
				'width' => 50
			);

			// Commandes annulées
			$columns[] = array(
				'id' => 'usr-orders-canceled',
				'code' => 'canceled',
				'name' => _('Commandes annulées'),
				'align' => 'center',
				'sortable' => true,
				'title' => '',
				'width' => 50
			);
		}
	}

	return $columns;
}
