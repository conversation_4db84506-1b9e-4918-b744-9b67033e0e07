<?php

	/**	\file popup-select-user.php
	 *	Cette page est affichée en popup et permet la recherche et la sélection d'un compte client.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

	require_once('users.inc.php');

	unset( $error, $results );

	$results = $get_all_seller = false;
	$user = isset($_POST['user']) ? $_POST['user'] : ( isset($_GET['user']) ? $_GET['user'] : 0 );
	$action_type = isset($_POST['action_type']) ? $_POST['action_type'] : ( isset($_GET['action_type']) ? $_GET['action_type'] : 'user' );

	$html = '';

	//
	if( isset($_POST['usr_choose'], $_POST['select-usr']) ){
		if( $action_type=='seller' ){
			$seller_id = gu_users_get_seller_id($_POST['select-usr']);
			if( !$seller_id || !gu_users_set_seller_id( $user, $seller_id ) ){
				$error = _("Impossible de mettre à jour le représentant du client. Merci de réessayer ou de prendre contact avec nous pour nous signaler l'erreur.");
			}else{
				$sll_data = ria_mysql_fetch_array(gu_users_get($_POST['select-usr']));
				$usr_data = ria_mysql_fetch_array(gu_users_get($user));
				$html = ( $sll_data['is_sync'] && $usr_data['is_sync'] ? '<img class="sync" src="/admin/images/sync/1.svg?'.ADMIN_ASSET_IMGS.'" title="'._('Le lien avec ce représentant est synchronisé depuis votre gestion commerciale.').'" alt="'._('Le lien avec ce représentant est synchronisé depuis votre gestion commerciale.').'" /> ' : '' ).'<a href="edit.php?usr="'.$sll_data['id'].'">'.gu_users_get_name( $sll_data['id'] ).'</a>'.( !$sll_data['is_sync'] || !$usr_data['is_sync'] ? '(<a onclick="return showSelectSeller(this, '.$usr_data['id'].')">'._('Modifier').'</a>|<a onclick="return detachSeller(this, '.$usr_data['id'].');" href="?usr='.$usr_data['id'].'&seller-detach=1">'._('Détacher').'</a>)' : '' );
			}
		}else{
			if( !gu_users_set_seller_id( $_POST['select-usr'], $user ) ){
				$error = _("Impossible d'affecter le représentant au client sélectionné. Merci de réessayer ou de prendre contact avec nous pour nous signaler l'erreur.");
			}
		}
	}elseif( isset($_GET['q']) && trim($_GET['q']) ){
		$results = search3( 1, $_GET['q'], 1, 0, false, false, 6, array('usr') );
	}

	if( $action_type=='seller' ){
		$get_all_seller = true;
	}

	define('ADMIN_PAGE_TITLE', $action_type=='seller' ? _('Affecter un représentant') : _('Affecter un client à un représentant'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'popup-iframe');
	require_once('admin/skin/header.inc.php');

	if( isset($error) ){
		print '<div class="error">'.nl2br( $error ).'</div>';
	}
?>
	<form action="popup-select-user.php" method="get">
		<input type="hidden" name="user" value="<?php print $user; ?>" />
		<input type="hidden" name="action_type" value="<?php print htmlspecialchars($action_type); ?>" />
		<label for="q"><?php print $action_type=='seller' ? _('Email ou nom du représentant :') : _('Email ou nom du client :'); ?></label>
		<input type="text" name="q" id="q" <?php if( isset($_GET['q']) ) print 'value="'.htmlspecialchars($_GET['q']).'"'; ?> />
		<input type="submit" value="<?php print _('Chercher'); ?>" class="btn-main" />
	</form>

	<form action="" method="post" class="spacing-top">
		<input type="hidden" name="user" value="<?php print $user; ?>" />
		<input type="hidden" name="action_type" value="<?php print htmlspecialchars($action_type); ?>" />
		<table>
			<caption><?php print $action_type=='seller' ? _('Représentants') : _('Résultats de recherche'); ?></caption>
			<thead>
				<tr>
					<th id="usr-ref"><?php print _('Ref.')?></th>
					<th id="usr-email"><?php print _('Email')?></th>
					<th id="usr-name"><?php print _('Nom')?></th>
				</tr>
			</thead>
			<tfoot>
				<tr>
					<td colspan="3">
						<input type="submit" value="<?php print _('Choisir')?>" name="usr_choose" class="btn-main" style="margin-right: 5px;" />
						<input type="button" value="<?php print _('Annuler')?>" onclick="parent.hidePopup();" class="btn-cancel" />
					</td>
				</tr>
			</tfoot>
			<tbody>
				<?php
				$ar_usr = null;
				if( $results ){
					while( $r = ria_mysql_fetch_assoc($results) ){
						$ar_usr[] = $r['tag'];
					}
				}elseif( $get_all_seller ){
					$ar_usr = 0;
				}

				$no_rows = false;
				if( $ar_usr!==null ){
					$profiles = $action_type=='seller' ? [PRF_SELLER] : [PRF_CUSTOMER, PRF_CUST_PRO, PRF_RESELLER];
					if( $users = gu_users_get($ar_usr, '', '', $profiles, '', 0, '', false, false, false, false, '', false, 0, '', 0, false, true) ){
						if( ria_mysql_num_rows($users) ){
							while( $usr = ria_mysql_fetch_assoc($users) ){
								print '
									<tr>
										<td headers="usr-ref" nowrap="nowrap">
											<input type="radio" class="radio" value="'.$usr['id'].'" name="select-usr" id="usr-'.$usr['id'].'" '.( (isset($_POST['select-usr']) && $_POST['select-usr']==$usr['id']) || ria_mysql_num_rows($users)==1 ? 'checked="checked"':'' ).' />
											<label for="usr-'.$usr['id'].'">'.htmlspecialchars($usr['ref']).'</label>
										</td>
										<td headers="usr-email" nowrap="nowrap">
											<label for="usr-'.$usr['id'].'">'.htmlspecialchars($usr['email']).'</label>
										</td>
										<td headers="usr-name">
											<label for="usr-'.$usr['id'].'">'.htmlspecialchars(gu_users_get_name($usr['id'])).'</label>
										</td>
									</tr>
								';
							}
						}else{
							$no_rows = true;
						}
					}else{
						$no_rows = true;
					}
				}else{
					$no_rows = true;
				}
				if( $no_rows ){
					print '
						<tr>
							<td colspan="3">'.( $action_type=='seller' ? _('Aucun représentant') : _('Aucun résultat') ).'</td>
						</tr>
					';
				}
				?>
			</tbody>
		</table>
	</form>

	<script><!--<?php
		if( isset($_POST['usr_choose'], $_POST['select-usr']) && !isset($error) ){
			if( $action_type=='seller' ){
				print '
					parent.validSellerChange(\''.$html.'\');
					parent.hidePopup();
				';
			}else{
				$r_usr_from_sll = gu_users_get( 0, '', '', PRF_SELLER, '', 0, '', false, false, $user );
				if( $r_usr_from_sll && ria_mysql_num_rows($r_usr_from_sll) ){
					print '
						parent.window.location.href = \'/admin/customers/index.php?seller=\' + '.ria_mysql_result($r_usr_from_sll, 0, 'id').';
					';
				}else{
					print '
							parent.window.location.href = \'/admin/customers/index.php\';
					';
				}
			}
		}
	?>--></script>
<?php
	require_once('admin/skin/footer.inc.php');
