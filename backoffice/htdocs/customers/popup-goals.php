<?php
	/** \file popup-goals.php
	 *  Ce fichier permet de gérer la popup qui permettra l'ajout d'objectif pour un objet données (Catégories, Produits ou Marque)
	 *  et/ou pour un client donné.
	 */
	require_once('users.inc.php');
	require_once('goals.inc.php');

	define('ADMIN_PAGE_TITLE', _('Ajout d\'un objectif'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'popup-iframe');
	require_once('admin/skin/header.inc.php');

	// Contrôle que l'accès à cette popup est réalisé pour un compte précis
	// Un objectif est défini pour un représentant ou un revendeur
	if( !isset($_GET['seller']) || !gu_users_exists($_GET['seller']) ){
		$g_error = _('Une ou plusieurs informations obligatoires sont manquantes.');
	}

	// Contrôle que l'identifiant de l'objectif à personnaliser est bien passé en paramètre
	if( !isset($_GET['goal']) || !is_numeric($_GET['goal']) || $_GET['goal'] <= 0 ){
		$g_error = _('Une ou plusieurs informations obligatoires sont manquantes.');
	}

	$error = false;
	$obj = false;

	// Identifinant d'un compte client, permet de définir un objectif pour un client en particulier
	// /!\ Attention à ne pas remplacer "gu_users_exists" car plus bas la récupération du compte est fait
	$usr = isset($_GET['usr']) && gu_users_exists($_GET['usr']) ? $_GET['usr'] : false;

	// Identifiant du compte et du représentant
	// /!\ Si aucun compte client est choisi alors $seller_id est égale à null
	$user_id = $usr ? $usr : $_GET['seller'];
	$seller_id = $usr ? $_GET['seller'] : null;

	$date = new DateTime();
	$action = isset($_GET['action']) && $_GET['action'] == 'upd' ? 'upd' : 'add';
	$year = isset($_GET['year']) && is_numeric($_GET['year']) && $_GET['year'] > 0 ? $_GET['year'] : $date->format('Y');

	if( !isset($g_error) ){
		$form_action = '/admin/customers/popup-goals.php?goal='.$_GET['goal'].'&amp;year='.$year;
	}

	if( $usr ){
		$form_action .= '&amp;seller='.$seller_id.'&amp;usr='.$user_id;
	}else{
		$form_action .= '&amp;seller='.$user_id;
	}

	// Détermine si l'objectif est restreint sur un objet en particulier
	// null = pas encore choisi (le choix est obligatoire pour passer à la suite de la création)
	// all = pas de restriction d'objet dans ce cas il faudra au minimum choisir un compte client (sinon il s'agit des objectifs de base du compte)
	// [CLS_ID] = restriction sur une classe en particuler
	$cls = false;

	if( isset($_GET['cls']) && trim($_GET['cls']) != '' ){
		if( in_array($_GET['cls'], [CLS_CATEGORY, CLS_PRODUCT, CLS_BRAND]) ){
			$cls = $_GET['cls'];
			$form_action .= '&amp;cls='.$cls;
		}elseif( $_GET['cls'] === 'all' ){
			$cls = 'all';
		}
	}

	// Le'objet est passé en paramètre
	switch( $cls ){
		case CLS_PRODUCT :
			if( isset($_GET['prd']) && is_numeric($_GET['prd']) && $_GET['prd'] > 0 ){
				$r_prd = prd_products_get_simple( $_GET['prd'] );
				if( !$r_prd || !ria_mysql_num_rows($r_prd) ){
					$g_error = _('Le produit donné en paramètre n\'a pas été trouvé dans votre catalogue.');
				}else{
					$prd = ria_mysql_fetch_assoc( $r_prd );
					$obj = [ $prd['id'] ];
				}

				$form_action .= '&amp;prd='.$_GET['prd'];
			}
		break;
		case CLS_CATEGORY :
			if( isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat'] > 0 ){
				$r_cat = prd_categories_get( $_GET['cat']
			 );
				if( !$r_cat || !ria_mysql_num_rows($r_cat) ){
					$g_error = _('La catégorie donnée en paramètre n\'a pas été trouvée dans votre catalogue.');
				}else{
					$cat = ria_mysql_fetch_assoc( $r_cat );
					$obj = [ $cat['id'] ];
				}

				$form_action .= '&amp;cat='.$_GET['cat'];
			}
		break;
		case CLS_BRAND :
			if( isset($_GET['brd']) && is_numeric($_GET['brd']) && $_GET['brd'] > 0 ){
				$r_brd = prd_brands_get( $_GET['brd'] );

				if( !$r_brd || !ria_mysql_num_rows($r_brd) ){
					$g_error = _('La marque donnée en paramètre n\'a pas été trouvée dans votre catalogue.');
				}else{
					$brd = ria_mysql_fetch_assoc( $r_brd );
					$obj = [ $brd['id'] ];
				}

				$form_action .= '&amp;brd='.$_GET['brd'];
			}
		break;
	}

	if( isset($_POST['save-goals']) ){
		foreach( $_POST['month'] as $month => $value ){
			if( !$value ){
				$value = 0;
			}else{
				$value = str_replace( ' ', '', $value);
				$value = str_replace( ',', '.', $value);
			}
			$first_day_of_month = date("Y-m-d", mktime(0, 0, 0, $month, 1 ,$_POST['goal-year']));
			$last_day_of_month = date("Y-m-d", mktime(0, 0, 0, $month +1, 0, $_POST['goal-year']));

			if( !obj_periods_add( $user_id, $_POST['obj_id'], 'month', $first_day_of_month, $last_day_of_month, $value, null, $seller_id, $cls, $obj )){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des objectifs mensuels");
			}
		}
		unset($value);

		foreach( $_POST['trimester'] as $trimester => $value ){
			if( !$value ){
				$value = 0;
			}else{
				$value = str_replace( ',', '.', $value);
			}
			$first_day_of_trimester = date("Y-m-d", mktime(0, 0, 0, -2 + 3 * $trimester, 1 ,$_POST['goal-year']));
			$last_day_of_trimester = date("Y-m-d", mktime(0, 0, 0, 3 * $trimester +1, 0 ,$_POST['goal-year']));
			if(!obj_periods_add( $user_id, $_POST['obj_id'], 'trimester', $first_day_of_trimester, $last_day_of_trimester, $value, null, $seller_id, $cls, $obj )){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des objectifs trimestriels");
			}
		}

		foreach( $_POST['semester'] as $semester => $value ){
			if( $semester == 1 ){
				$first_day_of_semester = $_POST['goal-year'].'-01-01';
				$last_day_of_semester = $_POST['goal-year'].'-06-30';
			}else{
				$first_day_of_semester = $_POST['goal-year'].'-07-01';
				$last_day_of_semester = $_POST['goal-year'].'-12-31';
			}

			if( !$value ){
				$value = 0;
			}else{
				$value = str_replace( ',', '.', $value);
			}

			if(!obj_periods_add( $user_id, $_POST['obj_id'], 'semester', $first_day_of_semester, $last_day_of_semester, $value, null, $seller_id, $cls, $obj )){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des objectifs semestriels");
			}
		}

		if( !$_POST['year'] ){
			$_POST['year'] = 0;
		}else{
			$_POST['year'] = str_replace( ',', '.', $_POST['year']);
		}
		$first_day_of_year =  date("Y-m-d", mktime(0, 0, 0, 1, 1 ,$_POST['goal-year']));
		$last_day_of_year =  date("Y-m-d", mktime(0, 0, 0, 1, 0 ,$_POST['goal-year']+1));

		if(!obj_periods_add( $user_id, $_POST['obj_id'], 'year', $first_day_of_year, $last_day_of_year, $_POST['year'], null, $seller_id, $cls, $obj )){
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des objectifs annuels");
		}

		if (isset($_POST['perso']) && is_array($_POST['perso'])) {
			foreach( $_POST['perso'] as $id => $value ){
				if( $value ){
					$value = str_replace(' ', '', $value);
				}else{
					$value = 0;
				}
				if( !obj_periods_upd( $id, $value )){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des objectifs sur des périodes personalisées");
				}
			}
		}

		if (!isset($error)) {
			$_SESSION['success-user-edit'] = 'Les objectifs ont bien été mis à jour.';
		}
	}

	if( isset($g_error) ){
		print '<div class="error">'.$g_error.'</div>';
		exit;
	}

	// Titre de la page
	print '<h2>';
	if( $action == 'add' ){
		print _('Ajout d\'un nouvel objectif');
	}else{
		print _('Mise à jour de l\'objectif');
	}
	print '</h2>';

	if( $error ){
		print '<div class="error">'.$error.'</div>';
	}

	// Affiche le sélection de classe
	print '<form action="'.$form_action.'" method="post">';

		// Un compte client est choisi
		if( $usr !== false ){
			$user = ria_mysql_fetch_assoc( gu_users_get($usr) );

			print '<div class="zone-select-context" id="zone-user">'
				.'<strong>'._('Client concerné :').'</strong> ';

				if( trim($user['ref']) != '' ){
					print htmlspecialchars( $user['ref'] ).' - ';
				}

				print trim( htmlspecialchars($user['adr_firstname'].' '.$user['adr_lastname'].' '.$user['society']) )
					.' &lt;'.htmlspecialchars( $user['email'] ).'&gt;'
			.'</div>';
		}else{
			print '<div class="zone-select-context" id="zone-cls">';

			if( $cls !== false ){
				print '<strong>'._('Restriction sur :').'</strong> '.($cls == 'all' ? _('Aucune restriction') : fld_classes_get_name( $cls ) );

				if( $action == 'add' ){
					print ' <a id="change-cls" href="#">'._('modifier').'</a>';
				}
			}else{
				// Gestion de la sélection d'une classe sur laquelle créer un objectif
				print '<p><strong>'._('Souhaitez-vous restreinte l\'objectif ?').'</strong></p>'
					.'<select name="cls" id="cls">'
						.'<option value="">'._('Choisissez une restriction').'</option>';

						if( $user_id > 0 ){
							print '<option value="all">'._('Global').'</option>';
						}

						print '<option value="'.CLS_CATEGORY.'">'._('À une catégorie').'</option>'
						.'<option value="'.CLS_PRODUCT.'">'._('À un produit').'</option>'
						.'<option value="'.CLS_BRAND.'">'._('À une marque').'</option>'
					.'</select>';
			}

			print '</div>';
		}

		print '<div class="zone-select-context" id="zone-prd">';

		if( $cls != 'all' ){
			switch( $cls ){
				case CLS_PRODUCT:
					$title_selected = _('Produit concerné :');
					$label_select_obj = _('Quel produit est concerné ?');
					$button_selected_obj = _('Sélectionner un produit');
					$obj_name = isset($prd) ? $prd['name'] : '';
				break;
				case CLS_CATEGORY:
					$title_selected = _('Catégorie concernée :');
					$label_select_obj = _('Quelle catégorie est concernée ?');
					$button_selected_obj = _('Sélectionner une catégorie');
					$obj_name = isset($cat) ? $cat['name'] : '';
				break;
				case CLS_BRAND:
					$title_selected = _('Marque concernée :');
					$label_select_obj = _('Quelle marque est concernée ?');
					$button_selected_obj = _('Sélectionner une marque');
					$obj_name = isset($brd) ? $brd['name'] : '';
					break;
			}

			if( $obj !== false ){
				print '<strong>'.$title_selected.'</strong> '.htmlspecialchars( $obj_name );
			}elseif( $cls !== false ){
				// Gestion de la sélection d'un article (seulement si la règle d'ajout est faite depuis la fiche d'un produit)
				print '<p><strong>'.$label_select_obj.'</strong></p>'
					.'<input type="button" name="select-obj" id="select-obj" value="'.$button_selected_obj.'" />';
			}
		}

		// Affichage du tableau de saisi lorsqu'une classe et un objet est choisi
		$objs = [];
		if( $cls > 0 && $obj !== false ){
			$objs = [ 'cls_id' => $cls, 'obj_id_0' => $obj[0] ];
		}

		if( $cls == 'all' ){
			$objs = false;
		}

		print view_goals( $user_id, $_GET['goal'], $year, $objs, true, $seller_id, false, false );

		print '</div>'
	.'</form>';

	require_once('admin/skin/footer.inc.php');
?>
<script>
	var classID = '<?php print htmlspecialchars($cls); ?>';
	var seller = '<?php print htmlspecialchars($_GET['seller']); ?>';
	var goalID = '<?php print htmlspecialchars($_GET['goal']); ?>';
	var year = '<?php print htmlspecialchars($year); ?>';
	var user = <?php print $usr !== false ? '\''.htmlspecialchars( $usr ).'\'' : 'false'; ?>

	<?php
		// Dans le cas d'une mise à jour ou d'un ajout d'un objectif par object
		// Si aucune erreur, la popup sera fermée et le tableau des objectifs mis à jour
		if( isset($_POST['save-goals']) && $error === false ){
			if( $usr !== false ){
				print 'parent.loadGoalsGroupByCustomers( true, year );';
			}else{
				print 'parent.loadGoalsByObjects( true, year );';
			}
		}
	?>
</script>
<script src="/js/goals.js"></script>