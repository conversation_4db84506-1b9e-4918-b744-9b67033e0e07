<?php

	/**	\file popup-login-history.php
	 *	Ce fichier permet la consultation de l'historique de connexion d'un utilisateur.
	 */

	require_once('http.inc.php');
	require_once('users.inc.php');

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_VIEW');

	if( !isset($_GET['usr']) || !is_numeric($_GET['usr']) ){
		http_404();
		exit;
	}


	define('ADMIN_PAGE_TITLE', _('Historique des connexions') . ' - ' . _('Clients'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'popup-iframe');
	require_once('admin/skin/header.inc.php');
?>

<table>
	<thead>
		<tr>
			<th><?php print _('Date de connexion'); ?></th>
			<th><?php print _('Navigateur'); ?></th>
			<th><?php print _('Adresse IP'); ?></th>
			<th><?php print _('Nom d\'hôte'); ?></th>
			<th><?php print _('Connexion réussie ?'); ?></th>
		</tr>
	</thead>
	<tbody>
		<?php
			$history = gu_users_logins_get( $_GET['usr'] );
			if( !ria_mysql_num_rows($history) ){
				print '<tr><td colspan="5">'._('Aucune tentative de connexion').'</td></tr>';
			}else{
				while( $h = ria_mysql_fetch_assoc($history) ){
					print '<tr>';
					print '<td>'.$h['date_login'].'</td>';
					print '<td>'.htmlspecialchars( $h['user_agent'] ).'</td>';
					print '<td>'.htmlspecialchars( $h['usl_ip'] ).'</td>';
					print '<td>'.htmlspecialchars( $h['ip_host'] ).'</td>';
					print '<td>';
					if( $h['attempt_success'] ){
						 print '<span class="tag tag-yes">'._('Oui').'</span>';
					}else{
						print '<span class="tag tag-no">'._('Non').'</span>';
					}
					print '</td>';
					print '</tr>';
				}
			}
		?>
		<tr>

		</tr>
	</tbody>
</table>
<?php
?>

<?php
	require_once('admin/skin/footer.inc.php');
?>
