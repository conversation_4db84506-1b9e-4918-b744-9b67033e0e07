<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

	unset($error);
	unset($msg);

	if( !isset($_REQUEST['email']) ){
		$error = _("Aucune adresse email n'a été renseignée.")."\n"._("Cette fenêtre va être automatiquement fermée.");
		$_REQUEST['email'] = '';
	}elseif( isset($_POST['lost-pwd']) ){
		if( isset($_POST['select-wst']) ){
			if( !gu_users_send_lostpassword( $_REQUEST['email'], 0, false, false, 'pwd-lost', $_POST['select-wst'] ) ){
				$error = _("Une erreur inattendue est survenue pendant l'envoi de la procédure de récupération de mot de passe.");
			}else{
				$msg = _("La procédure de récupération de mot de passe a bien été envoyée.");
			}
		}else{
			$error = _("Veuillez sélectionner le site qui servira de modèle à la notification.");
		}
	}

	define('ADMIN_PAGE_TITLE', 'value');
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'popup-iframe');
	require_once('admin/skin/header.inc.php');

	if( isset($error) ){
		print '<div class="error">'.nl2br($error).'</div>';
	}
	if( isset($msg) ){
		print '<div class="success">'.nl2br($msg).'</div>';
	}
?>
	<div class="notice"><?php print _('Vous disposez de plusieurs modèles pour cette notification. Chaque modèle est personnalisé (entête, pied de page, liens) selon le site auquel il fait référence. Merci de sélectionner ci-dessous le modèle le plus adapté.'); ?></div>
	<form action="#" method="post">
		<input type="hidden" name="email" value="<?php print htmlspecialchars($_REQUEST['email']); ?>" />
		<?php
		$curr_wst = gu_users_get_wst( 0, $_REQUEST['email'], true );
		$curr_wst = !$curr_wst ? $config['wst_id'] : $curr_wst;
		if( $rcfg = cfg_emails_get('pwd-lost', null) ){
			while( $cfg = ria_mysql_fetch_array($rcfg) ){
				?>
				<div class="in-block">
					<input type="radio" class="in-radio" name="select-wst" id="select-wst-<?php print $cfg['wst_id']; ?>" value="<?php print $cfg['wst_id']; ?>" <?php print $cfg['wst_id']==$curr_wst ? 'checked="checked"' : ''; ?> />
					<label class="in-lbl" for="select-wst-<?php print $cfg['wst_id']; ?>"><?php print htmlspecialchars(wst_websites_get_name( $cfg['wst_id'] )); ?></label>
				</div>
				<?php
			}
		}
		?>
		<div class="in-block in-block-action">
			<input type="submit" name="lost-pwd" value="<?php print _('Sélectionner')?>" class="btn-main" />
			<input type="submit" name="cancel-pwd" value="<?php print _('Annuler')?>" onclick="window.parent.hidePopup();" class="btn-cancel" />
		</div>
	</form>

	<style>
	/* Popup ajax-usr-password */
	.in-block{
		padding-top: 5px;
	}
	.in-block-action{
		padding-top: 10px;
	}
	</style>
<?php
	require_once('admin/skin/footer.inc.php');
