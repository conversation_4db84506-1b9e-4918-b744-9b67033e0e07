<?php

	/**	\file ajax-join-file.php
	 * 
	 * 	Permet l'upload d'un fichier pour utilisation dans la messagerie.
	 * 
	 */

	require_once( 'messages.inc.php' );
	
	$print ='';
	
	if( isset($_POST['file'], $_POST['msg']) )
	{
		$file = explode(',',$_POST['file']);
		$r_file = messages_files_get($file);

 		if( $r_file && ria_mysql_num_rows($r_file) ){
		
			$print .= '<ul>';
  			while( $f = ria_mysql_fetch_array($r_file) ){
				
				$print .= '<li>';
					// Nom du fichier
					$ext = preg_replace('/.*\./','',$f['name']);
					$print .= '<a href="dl.php?file='.$f['id'].'">'.$f['name'].'</a>';
					
 					// <PERSON>lle du fichier
					$size = round(($f['size']/1024),1);
					if( $size > 1000 )
						$size = round(($size/1024),1).' Mo';
					else 
						$size = $size.' Ko';
					
					$print .= ' <span class="size-file">('.$size.')</span>';
					
					// Image de suppression d'une pièce jointe
					$print .= '<img class="del-file" name="del-file" src="'.$config['site_url'].'/admin/images/del.svg" title="'._('Retirer la pièce jointe').'" onclick="del_file(\''.$_POST['msg'].'\','.$f['id'].')" />';
 				$print .= '</li>';
			}
			$print .= '</ul>';
 		
		}
  	}
	
	print $print;
