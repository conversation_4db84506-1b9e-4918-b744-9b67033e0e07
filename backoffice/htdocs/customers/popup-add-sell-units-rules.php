<?php
	/** \file popup-add-sell-units-rules.php
	 * 	Ce fichier permet de gérer la création d'une nouvelle règle d'unité de vente.
	 * 	Pour le moment elle n'est appelée que depuis la fiche d'un produit > onglet "Livraison", mais à terme elle pourra être appelé depuis
	 * 	n'importe quel endroit (fiche client, fiche catégorie, fiche marque, etc...)
	 */

	// L'accès à cette popup est limité au administrateur pouvant éditer les fiches produits
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_CATEG_EDIT');

	require_once('usr/sell-units-rules.inc.php');

	$g_error = $error = false;

	// Contrôle des paramètres obligatoires pour l'affichage de cette popup
	if( !isset($_GET['cls']) || !in_array($_GET['cls'], gu_sell_units_rules_get_classes()) ){
		$g_error = _('Un ou plusieurs paramètres obligatoires à la création d\'une nouvelle règle sont manquants.');
	}

	// Différentes actions, seulement si les paramètres obligatoire à l'affichage de cette popup sont bien renseignés
	if( !$g_error ){
		// Ces paramètres sont renseignes en parcourant les paramètres $_GET et permettent la sauvegarde de la nouvelle règle
		$cls_id = $obj = null;

		// Ces paramètres sont renseignés en parcourant les paramètres $_GET et permettent l'affichage du formulaire
		$prd = $usr = false;

		// Sélection d'objet possible, dépendant de l'arrivé sur cette popup
		// (par exemple si l'on arrive depuis la fiche d'une marque, il ne sera pas possible de choisir un produit)
		$select_prd = false;
		switch( $_GET['cls'] ){
			case CLS_PRODUCT:
				$select_prd = true;
				break;
		}

		// Défini l'action du formulaire
		$form_action = '/admin/customers/popup-add-sell-units-rules.php?cls='.$_GET['cls'];

		// Le produit est passé en paramètre
		if( isset($_GET['prd']) && is_numeric($_GET['prd']) && $_GET['prd'] > 0 ){
			$r_prd = prd_products_get_simple( $_GET['prd'] );
			if( !$r_prd || !ria_mysql_num_rows($r_prd) ){
				$g_error = _('Le produit donné en paramètre n\'a pas été trouvé dans votre catalogue.');
			}else{
				$prd = ria_mysql_fetch_assoc( $r_prd );
				$cls_id = CLS_PRODUCT;
				$obj = [ $prd['id'] ];
			}

			$form_action .= '&amp;prd='.$_GET['prd'];
		}

		// Le client est passé en paramètre
		if( isset($_GET['usr']) && is_numeric($_GET['usr']) && $_GET['usr'] > 0 ){
			$r_usr = gu_users_get( $_GET['usr'] );
			if( !$r_usr || !ria_mysql_num_rows($r_usr) ){
				$g_error = _('Le compte client passé en paramètre n\'a pas été trouvé.');
			}else{
				$usr = ria_mysql_fetch_assoc( $r_usr );
			}

			$form_action .= '&amp;usr='.$_GET['usr'];
		}

		// Cette action permet de sauvegarder une règle
		if( isset($_POST['save-rule']) ){
			if( !isset($_POST['sell-unit']) || !is_numeric($_POST['sell-unit']) || $_POST['sell-unit'] <= 0 ){
				$error = _('Veuillez renseigner une unité de vente.');
			}else{
				if( isset($_POST['colisage']) && is_numeric($_POST['colisage']) && $_POST['colisage'] > 0 ){
					$cls_id = CLS_PRD_COLISAGE;
					$obj[] = $_POST['colisage'];
				}

				$add_rule = gu_sell_units_rules_add( $usr['id'], $cls_id, $obj, $_POST['sell-unit'], 0 );
				if( !is_numeric($add_rule) || $add_rule <= 0 ){
					$error = _('Une erreur inattendue s\'est produite lors de l\'enregistre de la règle.');
				}
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Créer une nouvelle règle d\'unité de vente'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	print '<body class="popup-iframe">';

		if( $g_error ){
			print '<div class="error">'.$g_error.'</div>';
		}else{
			if( $error ){
				print '<div class="error">'.$error.'</div>';
			}

			print '<h2>'._('Créer une nouvelle règle d\'unité de vente').'</h2>'
			.'<form id="create-sell-units-rule" action="'.$form_action.'" method="post">'
				.'<div id="zone-usr">';

					if( $usr !== false ){
						$user_name = trim( $usr['adr_firstname'].' '.$usr['adr_lastname'].' '.$usr['society'] );
						if( $user_name == '' ){
							$user_name = $usr['email'];
						}

						print '<strong>'._('Client concerné :').'</strong> '.htmlspecialchars( $user_name )
						.' <a id="change-user" href="#">'._('modifier').'</a>';
					}else{
						// Gestion de la sélection d'un compte client
						print '<p><strong>'._('Quel client est concerné ?').'</strong></p>'
							.'<input type="button" name="select-usr" id="select-usr" value="'._('Sélectionner un client').'" />';
					}

				print '</div>'
				.'<div id="zone-prd">';

					if( $prd !== false ){
						print '<strong>'._('Produit concerné :').'</strong> '.htmlspecialchars( $prd['name'] );

						// Lorsque le produit est sélection, on peut choisi un conditionnemnt
						// Récupération des conditions liés aux produit
						$r_colisage = prd_colisage_classify_get( 0, $prd['id'] );
						if( $r_colisage && ria_mysql_num_rows($r_colisage) ){
							print '<div>'
								.'<label for="colisage"><strong>'._('Limiter cette règle à un conditionnement :').'</strong></label> '
								.'<select name="colisage" id="colisage">'
									.'<option value="all">'._('Tous les conditionnements sont inclus').'</option>';

									while( $colisage = ria_mysql_fetch_assoc($r_colisage) ){
										$selected = '';
										if( isset($_POST['colisage']) && $colisage['col_id'] == $_POST['colisage'] ){
											$selected = 'selected="selected"';
										}

										print '<option value="'.$colisage['col_id'].'" '.$selected.'>'.htmlspecialchars( $colisage['col_name'] ).'</option>';
									}

								print '</select>'
							.'</div>';
						}
					}elseif( $select_prd ){
						// Gestion de la sélection d'un article (seulement si la règle d'ajout est faite depuis la fiche d'un produit)
						print '<p><strong>'._('Quel produit est concerné ?').'</strong></p>'
							.'<input type="button" name="select-prd" id="select-prd" value="'._('Sélectionner un produit').'" />';
					}

				print '</div>';

				// Il est possible d'enregistrer la règle seulement si le compte client est choisi ainsi qu'un objet (produit, catégorie, conditionnement
				// ou marque)
				if( $usr && ($prd) ){
					$sell_unit = '';
					if( isset($_POST['sell-unit']) ){
						$sell_unit = $_POST['sell-unit'];
					}

					print '<div>'
						.'<label for="sell-unit"><strong>'._('Unité de vente :').'</strong></label>'
						.' <input type="number" name="sell-unit" id="sell-unit" min="0.01" step="0.01" value="'.htmlspecialchars( $sell_unit ).'" />'
					.'</div>'

					.'<input type="submit" name="save-rule" id="save-rule" value="'._('Enregistrer').'" />';
				}

			print '</form>';
		}

		print '<div class="load-ajax-opacity"></div>'
	.'</body>';
?>
<script>
	// Initialise des variables pour la gestion des actions JavaScript
	var classID = <?php print $_GET['cls']; ?>;
	var user = <?php print $usr ? $usr['id'] : 0; ?>;
	var product = <?php print $prd ? $prd['id'] : 0; ?>;

	<?php if( isset($_POST['save-rule']) && $error === false ){ ?>
		// Mise à jour du tableau des règles
		parent.loadSellUnitBody(1);

		// Ferme la popup
		parent.hidePopup();
	<?php } ?>
</script>
<script src="/admin/js/customers/sell-units-rules.js"></script>