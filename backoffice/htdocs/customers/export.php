<?php

	/**	\file export.php
	 *
	 *	Ce fichier exporte une liste de comptes utilisateurs (clients, fournisseurs, représentants, etc...) à partir
	 *	de filtres sélectionnés sur la liste des comptes. L'export est au format CSV car les essais au format Excel
	 *	étaient beaucoup trop lents.
	 *
	 */

	require_once('strings.inc.php');
	require_once('users.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

	// Dresse la liste des champs à exporter
	$cols = isset($_GET['cols']) && trim($_GET['cols']) != '' ? explode('|', $_GET['cols']) : array();
	$fields = array();
	foreach($cols as $col){
		switch ($col){
			case 'id':
				$fields["id"]=_("Identifiant client");
				break;
			case 'ref':
				$fields["ref"] = _("Code client");
				break;
			case 'is_sync':
				$fields['is_sync'] = _('Synchronisé ?');
				break;
			case 'title_name':
				$fields["title_name"]=_("Civilité");
				break;
			case 'adr_firstname':
				$fields["adr_firstname"]=_("Prénom");
				break;
			case 'adr_lastname':
				$fields["adr_lastname"]=_("Nom");
				break;
			case "society":
				$fields["society"]=_("Société");
				break;
			case "siret":
				$fields["siret"]=_("Siret");
				break;
			case "naf":
				$fields["naf"]=_("Code NAF");
				break;
			case "email":
				$fields["email"]=_("Email");
				break;
			case "can_login":
				$fields["can_login"]=_("Peut se connecter ?");
				break;
			case "address1":
				$fields["address1"]=_("Adresse 1");
				break;
			case "address2":
				$fields["address2"]=_("Adresse 2");
				break;
			case "zipcode":
				$fields["zipcode"]=_("Code postal");
				break;
			case "city":
				$fields["city"]=_("Ville");
				break;
			case "country":
				$fields["country"]=_("Pays");
				break;
			case "prf_name":
				$fields["prf_name"]=_("Profil");
				break;
			case "prc_id":
				$fields["prc_id"]=_("Catégorie tarifaire");
				break;
			case 'notes':
				$fields['notes'] = _('Commentaires');
				break;
			case "parent_id":
				$fields["parent_id"]=_("Email compte parent");
				break;
			case 'wst_id':
				$fields["wst_id"] = _("Créé depuis");
				break;
			case "date_created":
				$fields["date_created"]=_("Date d'inscription");
				break;
			case "first_login":
				$fields["first_login"]=_("Première connexion");
				break;
			case "last_login":
				$fields["last_login"]=_("Dernière connexion");
				break;
			case "orders":
				$fields["orders"]=_("Commandes validées");
				break;
			case "orders_canceled":
				$fields["orders_canceled"]=_("Commandes annulées");
				break;
			case "longitude":
				$fields["longitude"]=_("longitude");
				break;
			case "latitude":
				$fields["latitude"]=_("latitude");
				break;
			case "rewards":
				$fields["rewards"]=_("Points de fidélité");
				break;
			case "total_ht":
				$fields["total_ht"]=_("CA HT");
				break;
			case "total_ttc":
				$fields["total_ttc"]=_("CA TTC");
				break;
			case "source":
				$fields["source"]=_("Source");
				break;
			case "first_visit":
				$fields["first_visit"]=_("Première visite");
				break;
			case "nb_visit":
				$fields["nb_visit"]=_("Nombre de visites");
				break;
			case "ip":
				$fields["ip"]=_("Adresse ip du client");
				break;
			case "phone":
				$fields["phone"]=_("Téléphone");
				break;
			case "fax":
				$fields["fax"]=_("Fax");
				break;
			case "mobile":
				$fields["mobile"]=_("Mobile");
				break;
			case "work":
				$fields["work"]=_("Téléphone en journée");
				break;
			case "total_marge":
				$fields["total_marge"]=_("Marge HT");
				break;
			case "seller_id":
				$fields["seller_id"]=_("Représentant");
				break;
			case "last_cgv_accepted":
				$fields["last_cgv_accepted"]=_("Dernière acceptation CGV");
				break;
			case "is_masked":
				$fields["is_masked"]=_("Masquer ce compte dans Yuto");
				break;
		}
	}

	// Dresse la liste des champs avancés à exporter
	$flds = isset($_GET['flds']) && trim($_GET['flds']) != '' ? explode('|', $_GET['flds']) : array();
	$advanced_fields = array();
	foreach($flds as $fld){
		$rfield = fld_fields_get($fld);
		$f = ria_mysql_fetch_array($rfield);
		$advanced_fields[$fld]=$f['name'];
	}

	// Lignes du document CSV
	$rows = array();

	// ligne d'entête
	$header_row1 = array();
	$header_row2 = array();
	foreach( $fields as $field=>$name ){
		$header_row1[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $name);
	}
	foreach($advanced_fields as $field=>$name){
		$header_row2[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $name);
	}
	$rows[] = array_merge($header_row1, $header_row2);

	// Chargement des comptes client
	$prf = isset($_GET['prf']) && is_numeric($_GET['prf']) && $_GET['prf']>0 ? $_GET['prf'] : 0;
	$seg = isset($_GET['seg']) && is_numeric($_GET['seg']) && $_GET['seg']>0 ? $_GET['seg'] : 0;
	$seller_id = false;
	$sort = isset($_GET['sort']) ? $_GET['sort'] : false;
	$dir = isset($_GET['dir']) ? $_GET['dir'] : false;
	$customers_ids = 0;
	if(!isset($_GET['usr_id']) && isset($_GET['seller']) && is_numeric($_GET['seller']) && $_GET['seller'] > 0 ){
		$rseller = gu_users_get( $_GET['seller'], '', '', PRF_SELLER );
			if( ria_mysql_num_rows($rseller) ){
				$customers_ids = gu_users_seller_customers_get($_GET['seller'], false, array(PRF_ADMIN, PRF_CUSTOMER, PRF_CUST_PRO, PRF_RESELLER));
				$seller = ria_mysql_fetch_array($rseller);
				if( $seller['seller_id'] ){
					$seller_id = $seller['seller_id'];
				}

				if( empty($customers_ids) ){
					$customers_ids = 0;
				}
			}
	}

	$users = gu_users_get(isset($_GET['usr_id']) ? $_GET['usr_id'] : $customers_ids, '', '', $prf, '', 0, '', $sort, $dir, $seller_id, false, '', false, 0, '', 0, false, true, null, $seg );


	// Effectue un premier parcours de la liste des comptes clients pour initialiser à 0 les statistiques de vente, qui seraient fausses ou moins pertinentes
	// que celles retournées par ord_orders_totals_get
	$users_ar = array();
	while( $u = ria_mysql_fetch_assoc($users) ){
		if( is_null($u) || $u == false ){
			continue;
		}
		$u['total_ht'] = $u['total_ttc'] = $u['total_marge'] = 0;
		$users_ar[ $u['id'] ] = $u;

		// Récupère la dernière date d'acceptation de CGV
		$cgv_accepted = "";
		$r_cgv = gu_users_cgv_get( $u['id']);
		if( $r_cgv && ria_mysql_num_rows($r_cgv) ){
			$cgv = ria_mysql_fetch_assoc($r_cgv);
			$cgv_accepted = date( _('d/m/Y à H:i'), strtotime( $cgv['date_accepted'] ) );
		}
		$users_ar[ $u['id'] ]['last_cgv_accepted'] = $cgv_accepted;
	}

	// Effectue un second parcours de liste des clients pour renseigner les données de chiffre d'affaires et de marge grâce à la fonction ord_orders_totals_get
	if( count($users_ar) && (in_array('total_ht', $cols) || in_array('total_ttc',$cols) || in_array('total_marge', $cols) )){
		// calcul des totaux et de la marge pour tous les clients d'un seul bloc
		if( $rtot = ord_orders_totals_get( 0, array_keys($users_ar), 0, ord_states_get_ord_invalid(), true, true ) ){
			while( $tot = ria_mysql_fetch_assoc($rtot) ){
				$users_ar[ $tot['usr_id'] ]['total_ht'] = $tot['total_ht'];
				$users_ar[ $tot['usr_id'] ]['total_ttc'] = $tot['total_ttc'];
				$users_ar[ $tot['usr_id'] ]['total_marge'] = $tot['marge'];
			}
		}
	}

	$ar_prc_cat_name = array();

	$websites_names = array();

	// Lignes de données du fichier CSV
	foreach( $users_ar as $k_uid => $u ){
		// Origine du compte client
		if( in_array('source', $cols) || in_array('first_visit',$cols) || in_array('nb_visit', $cols) || in_array('ip', $cols) ){
			$stats = stats_origins_get( $u['id'], CLS_USER );
			$origin = array( 'source' => '', 'first_visit' => '', 'nb_visit' => '', 'ip' => '' );
			if( $stats && ria_mysql_num_rows($stats) ){
				$stat = ria_mysql_fetch_array( $stats );

				$origin['source'] = view_source_origin( $stat, 'showsource' );
				$origin['first_visit'] = $stat['first_visit'] ? date( 'd/m/Y à H:i', strtotime( $stat['first_visit'] ) ) : '';
				$origin['nb_visit'] = number_format( $stat['times_visited'], 0, ',', '' );
				$origin['ip'] = $stat['ip'].' - '.$stat['ip_host'];
			}
		}
		$data_row = array();
		foreach( $fields as $field=>$name ) {
			switch( $field ){
				case 'notes': // Chargement des commentaires Yuto
					$rnotes = fld_object_notes_get( 0, CLS_USER, $u['id'], 0, null );
					if( ria_mysql_num_rows($rnotes) ){
						$n = ria_mysql_fetch_array($rnotes);
						$data_row[] = $n['content'];
					}else{
						$data_row[] = '';
					}
					break;
				case 'parent_id': // Récupére l'email du compte parent
					if( $u['parent_id']!=0 ){
						$u['parent_id'] = gu_users_get_email($u['parent_id']);
					}else{
						$u['parent_id'] = '';
					}
					$data_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $u[$field]);
					break;
				case 'rewards': // Points de fidélité
					$points = gu_users_get_rewards_balance( $u['id'] );
					$u['rewards'] = $points>0 ? $points : 0;
					$data_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $u[$field]);
					break;
				case 'first_login': // Première visite, donnée issue de gu_users_logins_get
					$u['first_login'] = '';
					$logins = gu_users_logins_get( $u['id'] );
					if($logins && ria_mysql_num_rows($logins)) {
						$login = ria_mysql_fetch_array($logins);
						$u['first_login'] = $login['date_login'];
					}
					$data_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $u[$field]);
					break;
				case 'prc_id': // Catégorie comptable et tarifaire. Le nom est utilisé plutôt que son identifiant.
					$prc_name = '';
					if( is_numeric($u['prc_id']) && $u['prc_id'] > 0 ){
						if( array_key_exists($u['prc_id'], $ar_prc_cat_name) ){
							$prc_name = $ar_prc_cat_name[ $u['prc_id'] ];
						}else{
							$ar_prc_cat_name[ $u['prc_id'] ] = prd_prices_categories_get_name( $u['prc_id'] );
							$prc_name = $ar_prc_cat_name[ $u['prc_id'] ];
						}
					}
					$u['prc_id'] = $prc_name;
					$data_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $u[$field]);
					break;
				case 'ref': // Référence client (nécessite un formatage spécial)
					$u[ 'ref' ] = '="'. $u['ref'] .'"';
					$data_row[] = $u[$field];
					break;
				case 'is_sync': // Synchronisé ?
					$u[ 'is_sync' ] = $u['is_sync'] ? _('Oui') : _('Non');
					$data_row[] = $u[$field];
					break;
				case 'siret': // Siret (nécessite un formatage spécial)
					$u[ 'siret' ] = '="'.formatSiret( $u['siret'] ).'"';
					$data_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $u[$field]);
					break;
				case 'naf': // Siret (nécessite un formatage spécial)
					$u[ 'naf' ] = '="'. $u['naf'] .'"';
					$data_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $u[$field]);
					break;
				case 'last_login_child':
					if( isset($u['last_login_child'])){
						$date = new DateTime($u['last_login_child']);
						$u['last_login_child'] = $date->format('d/m/Y \à H:i');
						$data_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $u[$field]);
					}
					break;
				case 'total_ht':
					$data_row[] = number_format( $u['total_ht'], 2, ',', '' );
					break;
				case 'total_ttc':
					$data_row[] = number_format( $u['total_ttc'], 2, ',', '' );
					break;
				case 'phone':
					$data_row[] = '="'.$u['phone'].'"';
					break;
				case 'fax':
					$data_row[] = '="'.$u['fax'].'"';
					break;
				case 'mobile':
					$data_row[] = '="'.$u['mobile'].'"';
					break;
				case 'work':
					$data_row[] = '="'.$u['work'].'"';
					break;
				case 'total_marge':
					$data_row[] = number_format( $u['total_marge'], 2, ',', '' );
					break;
				case 'source':
					$data_row[] = $origin['source'];
					break;
				case 'first_visit':
					$data_row[] = $origin['first_visit'];
					break;
				case 'nb_visit':
					$data_row[] = $origin['nb_visit'];
					break;
				case 'ip':
					$data_row[] = $origin['ip'];
					break;
				case 'seller_id':
					if( $u['seller_id'] && $u['prf_id'] != PRF_SELLER ){
						$sellers = gu_users_sellers_get($u['id']);
						$seller_names = array();
						foreach ($sellers as $seller) {
							$seller_names[] = $seller['email'];
						}
						$data_row[] = implode(', ',$seller_names);
						break;
					}
					$data_row[] = '';
					break;

				case 'is_masked':
					$data_row[] = $u['is_masked'] ? _('Oui') : _('Non');
					break;
				case 'wst_id':
					$wst_id = intval($u['wst_id']) == 0 ? $config['wst_id'] : $u['wst_id'];
					if (!isset($websites_names[$wst_id])) {
						$websites_names[$wst_id] = wst_websites_get_name($wst_id);
					}
					$data_row[] = $websites_names[$wst_id];
					break;
				default:
					if( !in_array($field, ['seller_id']) ){
						$data_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $u[$field]);
					}
					break;
			}
		}

		unset($websites_names);

		// Rempli la colonne "Représentant", sauf s'il s'agit d'un compte représentant
		// if( in_array('seller_id', $cols) ){
		// 	if( $u['seller_id'] && $u['prf_id']!=PRF_SELLER ){
		// 		$sellers = gu_users_sellers_get($u['id']);
		// 		$seller_names = array();
		// 		foreach ($sellers as $seller) {
		// 			$seller_names[] = $seller['email'];
		// 		}
		// 		$data_row[] = implode(', ',$seller_names);
		// 	}else{
		// 		$data_row[] = '';
		// 	}
		// }

		foreach( $advanced_fields as $field=>$name ){
			$data = fld_object_values_get($u['id'], $field);
			if(is_array($data)){
				$data = implode(',', $data);
			}
			$data_row[] = $data;
		}


		$rows[] = $data_row;
	}

	// Ecrit les données sur disque avant envoi au navigateur
	// ! Attention race condition possible !
	$file_csv = $config['doc_dir'].'/customers.csv';
	$handle = fopen($file_csv, 'w');

	$for_excel = isset($_GET['for_excel']) && $_GET['for_excel'] ? true : false;
	$for_mac = isset($_GET['for_mac']) && $_GET['for_mac'] ? true : false;

	// si que 2 ligne cela correspond a un export d'un seul client pour que ce soit plus simple à lire le passe en colonne
	$is_one_user = count($rows) == 2;
	foreach( $rows as $line ){
		if( $for_excel || $for_mac ){
			foreach( $line as $key => $data ){
				if( $for_mac ){
					$data = str_replace("\r\n", " ", $data);
					$data = preg_replace('/[ ]{2,}/', ' ', $data);
					$line[ $key ] = (is_string($data)) ? iconv("UTF-8", "macintosh", $data) : $data;
				}else{
					$line[ $key ] = (is_string($data)) ? iconv("UTF-8", "ISO-8859-1//TRANSLIT", $data) : $data;
				}
			}
		}

		if( !$is_one_user ){
			fputcsv($handle, $line, ';');
		}
	}

	if( $is_one_user ){
		$lenght = count($rows[0]);
		for ($i=0; $i < $lenght; $i++) {
			fputcsv($handle, array($rows[0][$i], $rows[1][$i]), ';');
		}
	}

	fclose($handle);
