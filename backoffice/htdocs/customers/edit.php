<?php

	/**	\file edit.php
	 *	Cette page permet la mise à jour d'un compte client. Elle comporte de nombreux onglets pour gérer un maximum de données sur le client :
	 *	- Général : adresse de facturation, mot de passe, profil, etc...
	 *	- Avancé : modèles de saisie et champs avancés
	 *	- Rapports : rapports de visite et d'appel (Yuto)
	 *	- Droits : autorisations, droits sur le catalogue, relation parent / enfant
	 *	- Commandes : Historique des commandes
	 *	- Adresses : adresses de facturation et de livraison
	 *	- Contacts : messages échangés avec ce compte
	 *	- Avis : avis consommateurs déposés sur le site
	 *	- Suggestions : utilisations de la fonction Recommander à un ami
	 *	- Options : alertes email activées et mise en copie
	 *	- Disponibilité : alertes de disponibilités activées sur des produits
	 *	- Favoris : produits mis en favori
	 *	- Reliquats : produits qui ont été commandés par ce client et qui ne sont pas encore livrés
	 *	- Relations : comptes parents/enfants et représentants
	 *	- Images : images associées au compte
	 *	- Statistiques : produits et catégories consultées par ce client
	 */

	require_once('users.inc.php');
	require_once('orders.inc.php');
	require_once('sys.countries.inc.php');
	require_once('contacts.inc.php');
	require_once('messages.inc.php');
	require_once('fields.inc.php');
	require_once('rights.inc.php');
	require_once('websites.inc.php');
	require_once('goals.inc.php');
	require_once('tasks.inc.php');
	require_once('tenants.inc.php');
	require_once('sys.naf.inc.php');

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_VIEW');

	// Vérifie que l'utilisateur à bien le droit de modifier / supprimer un client
	if( isset($_POST['del']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_DEL');
	}
	if( isset($_POST['save-main']) || isset($_POST['savefieds']) || isset($_POST['addmdl']) || isset($_POST['save-rights']) || isset($_POST['save-new-parent']) ||
		isset($_POST['save-new-relation']) || isset($_POST['new-usr-child']) || isset($_POST['save-godson']) || isset($_POST['save-options']) || isset($_POST['default-options']) ||
		isset($_POST['saveimg']) ){
			gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_EDIT');
	}

	// Charge le tenant pour déterminer s'il dispose d'une synchronisation ou non
	// (impacte le formulaire de modification)
	$rtnt = tnt_tenants_get( $config['tnt_id'] );
	$tnt = ria_mysql_fetch_array( $rtnt );

	// Bouton Exporter
	if( isset($_GET['downloadexport']) ){
		if (intval($_GET['downloadexport']) == 2) {
			$file = $config['doc_dir'] . '/customers.csv';
			$filename = 'customers.csv';

			header('Content-Description: File Transfer');
			header('Content-Type: application/octet-stream');
			header('Content-Disposition: attachment; filename="customers.csv"');
			header('Expires: 0');
			header('Cache-Control: must-revalidate');
			header('Pragma: public');
			header('Content-Length: ' . filesize($file));
		} else {
			$file = $config['doc_dir'] . '/export-rewards-' . $_SESSION['usr_id'] . '.csv';
			$filename = 'export-rewards.csv';

			header('Content-disposition: attachment; filename="' . $filename . '"');
			header('Content-type: application/octetstream; charset=utf-8');
			header('Pragma: no-cache');
			header('Expires: 0');
			echo "\xEF\xBB\xBF"; // BOM UTF-8
		}

		// Contrôle que le fichier est bien disponible
		if (file_exists($file)) {
			readfile($file);
			exit;
		}else{
			$error = _("Le fichier ne semble plus disponible, veuillez préparer un nouvel export en cliquant sur le bouton \"Exporter\".");
		}
	}

	// Demande d'export des tarifs du clients
	if( isset($_GET['downloadprice']) ){
		$filename = 'export-price-'.$_GET['usr'].'.xls';
		$filepath = $config['doc_dir'].'/'.$filename;

		$exp_id = exp_exports_add( CLS_USER, $filepath, $filename );
		if( $exp_id ){
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_EXPORT_GENERATE, array(
				'cls_id' => CLS_USER,
				'exp_id' => $exp_id,
				'type' => 'userprice',
				'user' => $_GET['usr']
			));

			$_SESSION['success-user-edit'] = _('Votre demande d\'export a bien été prise en compte, vous pouvez suivre son traitement depuis <a href="/admin/tools/exports/index.php" id="link-exports"> Outils > Exports</a>.');
			header('Location: /admin/customers/edit.php?usr='.$_GET['usr']);
			exit;
		}
	}

	// Vérifie les paramètres de suivi pour la liste des comptes clients
	if( !isset($_GET['prf']) || !is_numeric($_GET['prf']) ){
		$_GET['prf'] = '';
	}
	if( !isset($_GET['page']) || !is_numeric($_GET['page']) ){
		$_GET['page'] = 1;
	}

	// si suppression d'une relation on reste sur l'onglet
	if( isset($_REQUEST['del-rel']) ){
		$_POST['tabRelations'] = 'relations';
	}

	// Détermine l'onglet en cours de consultation
	if( !isset($_GET['tab']) ) $_GET['tab'] = '';
		$tab = 'general';
	if( isset($_POST['tabReviews']) || isset($_GET['rvw']) || $_GET['tab']=='reviews' ){
		$tab = 'reviews';
	}elseif( isset($_POST['tabRights']) || $_GET['tab']=='rights' ){
		$tab = 'rights';
	}elseif( isset($_POST['tabOrders']) || $_GET['tab']=='orders' ){
		$tab = 'orders';
	}elseif( isset($_POST['tabOptions']) || $_GET['tab']=='options' ){
		$tab = 'options';
	}elseif( isset($_POST['tabReference']) || $_GET['tab']=='reference' ){
		$tab = 'reference';
	}elseif( isset($_POST['tabAvailable']) || $_GET['tab']=='availability' ){
		$tab = 'availability';
	}elseif( isset($_POST['tabDelayed']) || $_GET['tab']=='delayed' ){
		$tab = 'delayed';
	}elseif( isset($_POST['tabContacts']) || $_GET['tab']=='contacts' ){
		$tab = 'contacts';
	}elseif( isset($_POST['tabStats']) || $_GET['tab']=='stats' ){
		$tab = 'stats';
	}elseif( isset($_POST['tabImages']) || $_GET['tab']=='images' ){
		$tab = 'images';
	}elseif( isset($_POST['tabRelations']) || $_GET['tab']=='relations' ){
		$tab = 'relations';
	}elseif( isset($_POST['tabFields']) || $_GET['tab']=='fields' ){
		$tab = 'fields';
	}elseif( isset($_POST['tabReports']) || $_GET['tab']=='reports' ){
		$tab = 'reports';
	}elseif( isset($_POST['tabBookmarks']) || $_GET['tab']=='bookmarks' ){
		$tab = 'bookmarks';
	}elseif( isset($_POST['tabRewards']) || $_GET['tab']=='rewards' ){
		$tab = 'rewards';
	}elseif( isset($_POST['tabAddresses']) || $_GET['tab']=='addresses' ){
		$tab = 'addresses';
	}elseif( isset($_POST['tabGoals']) || $_GET['tab']=='goals' ){
		$tab = 'goals';
	}

	if( isset($_POST['submit-rep-0']) ){
		$tab = 'contacts';
	}

	// L'accès à l'onglet Contacts est limité aux seuls utilisateurs ayant laissé des messages
	$contacts = isset($_GET['usr']) ? contacts_get( $_GET['usr'] ) : false;
	if( $contacts && ria_mysql_num_rows($contacts) )
	{
		for( $j=0; $j<=ria_mysql_num_rows($contacts); $j++ ){
			if( isset($_POST['submit-rep-'.$j]) ){
				$tab = 'contacts';
			}
		}
	}

	// Vérifie l'existence de l'utilisateur
	if( !isset($_GET['usr']) || !gu_users_exists($_GET['usr']) ){
		if (isset($_GET['seg']) && is_numeric($_GET['seg']) && $_GET['seg']) {
			header('Location: /admin/customers/index.php?seg=' . $_GET['seg']);
			exit;
		}
		if (isset($_GET['seller']) && is_numeric($_GET['seller']) && $_GET['seller']) {
			header('Location: /admin/customers/index.php?seller='.$_GET['seller']);
			exit;
		}

		header('Location: index.php?prf='.$_GET['prf'].'&page='.$_GET['page']);
		exit;
	}

	// Détermine si l'utilisateur en cours est lié à un locataire, ou non
	$tenant_link = gu_users_is_tenant_linked($_GET['usr']);

	// Bouton mot de passe oublié
	if( isset($_POST['lost-pwd']) ){
		if( !cfg_emails_code_exists('pwd-lost', $config['wst_id']) ){
			$send_result = '-1';
		}else{
			$send_result = gu_users_send_lostpassword( $_POST['email'] );
		}
	}

	// Chargement des informations à modifier
	$usr = ria_mysql_fetch_assoc(gu_users_get($_GET['usr']));

	// Protection sur sa propre fiche
	if( $usr['id']==$_SESSION['usr_id'] && $tab=='rights' ){
		$tab = 'general';
		$error = _('Vous ne pouvez pas mettre à jour vos droits d\'accès par vous-même.');
	}

	if (!gu_adresses_exists($usr['adr_invoices'])) {
		$usr['adr_invoices'] = null;
	}

	// Déblocage du compte
	if (isset($_POST['unlock-user'])) {
		$send_result = gu_users_send_lostpassword( $_POST['email'], PRF_ADMIN, true, false, 'pwd-lost', 0, true );
	}

	// Chargement des commentaires Yuto
	$rnotes = fld_object_notes_get( 0, CLS_USER, $_GET['usr'], 0, null );
	if( ria_mysql_num_rows($rnotes) ){
		$n = ria_mysql_fetch_array($rnotes);
		$usr['notes_id'] = $n['id'];
		$usr['notes'] = $n['content'];
	}else{
		$usr['notes_id'] = false;
		$usr['notes'] = '';
	}

	// Enregistrement des objectifs commerciaux (onglet Objectifs)
	if( isset($_POST['save-goals']) || (isset($_POST['save-main']) && isset($_POST['obj_id'], $_POST['month'], $_POST['trimester'], $_POST['semester'], $_POST['year'])) ){
		$tab = 'goals';

		foreach( $_POST['month'] as $month => $value ){
			if( !$value ){
				$value = 0;
			}else{
				$value = str_replace( ' ', '', $value);
				$value = str_replace( ',', '.', $value);
			}
			$first_day_of_month = date("Y-m-d", mktime(0, 0, 0, $month, 1 ,$_POST['goal-year']));
			$last_day_of_month = date("Y-m-d", mktime(0, 0, 0, $month +1, 0, $_POST['goal-year']));
			if( !isset($cls) && !isset($obj) ){
				if( !obj_periods_add( $_GET['usr'], $_POST['obj_id'], 'month', $first_day_of_month, $last_day_of_month, $value )){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des objectifs mensuels");
				}
			}
		}


		foreach( $_POST['trimester'] as $trimester => $value ){
			if( !$value ){
				$value = 0;
			}else{
				$value = str_replace( ',', '.', $value);
			}
			$first_day_of_trimester = date("Y-m-d", mktime(0, 0, 0, -2 + 3 * $trimester, 1 ,$_POST['goal-year']));
			$last_day_of_trimester = date("Y-m-d", mktime(0, 0, 0, 3 * $trimester +1, 0 ,$_POST['goal-year']));
			if(!obj_periods_add( $_GET['usr'], $_POST['obj_id'], 'trimester', $first_day_of_trimester, $last_day_of_trimester, $value )){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des objectifs trimestriels");
			}
		}

		foreach( $_POST['semester'] as $semester => $value ){
			if( $semester == 1 ){
				$first_day_of_semester = $_POST['goal-year'].'-01-01';
				$last_day_of_semester = $_POST['goal-year'].'-06-30';
			}else{
				$first_day_of_semester = $_POST['goal-year'].'-07-01';
				$last_day_of_semester = $_POST['goal-year'].'-12-31';
			}

			if( !$value ){
				$value = 0;
			}else{
				$value = str_replace( ',', '.', $value);
			}

			if(!obj_periods_add( $_GET['usr'], $_POST['obj_id'], 'semester', $first_day_of_semester, $last_day_of_semester, $value )){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des objectifs semestriels");
			}
		}

		if( !$_POST['year'] ){
			$_POST['year'] = 0;
		}else{
			$_POST['year'] = str_replace( ',', '.', $_POST['year']);
		}
		$first_day_of_year =  date("Y-m-d", mktime(0, 0, 0, 1, 1 ,$_POST['goal-year']));
		$last_day_of_year =  date("Y-m-d", mktime(0, 0, 0, 1, 0 ,$_POST['goal-year']+1));

		if(!obj_periods_add( $_GET['usr'], $_POST['obj_id'], 'year', $first_day_of_year, $last_day_of_year, $_POST['year'] )){
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des objectifs annuels");
		}

		if (isset($_POST['perso']) && is_array($_POST['perso'])) {
			foreach( $_POST['perso'] as $id => $value ){
				if( $value ){
					$value = str_replace(' ', '', $value);
				}else{
					$value = 0;
				}
				if( !obj_periods_upd( $id, $value )){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des objectifs sur des périodes personalisées");
				}
			}
		}

		if (!isset($error)) {
			$_SESSION['success-user-edit'] = 'Les objectifs ont bien été mis à jour.';
			header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].'&tab=goals');
			exit;
		}
	}

	// Bouton Enregistrer
	if( isset($_POST['save-main']) && $tenant_link ){
		// Dans le cas ou le champ Droits d'accès est disabled, évite un warning
		if( !isset($_POST['usr-prf-id']) ){
			$_POST['usr-prf-id'] = $usr['prf_id'];
		}
		if( isset($_POST['type'],$_POST['address1']) ){
			if (!is_numeric($usr['adr_invoices']) || !$usr['adr_invoices']) {
				// Création d'un adresse de facturation
				$new_adr = gu_adresses_add($usr['id'],$_POST['type'], '', '', '');
				if (!$new_adr || !is_numeric($new_adr) || !gu_users_address_set($usr['id'], $new_adr, false)) {
					$error = 3;
				}else{
					$usr['adr_invoices'] = $new_adr;
				}
			}

			if (!isset($error)) {
				// Vérifie que tous les champs demandés ont été envoyés
				if( $_POST['type']==1 ){ // Particulier
					if( !isset($_POST['title'],$_POST['firstname'],$_POST['lastname']) ){
						$error = 1;
					}
				}elseif( $_POST['type']==2 ){ // Société
					if( !isset($_POST['society'],$_POST['siret'],$_POST['naf']) ){
						$error = 1;
					}
				}elseif( $_POST['type']==3 ){ // Professionnel
					if( !isset($_POST['title'],$_POST['firstname'],$_POST['lastname'],$_POST['society'],$_POST['siret'],$_POST['naf']) ){
						$error = 1;
					}
				}
			}

			if( !isset($error) && !isset($_POST['country']) ){
				$error = 1;
			}

			// Vérifie le SIRET
			if( !isset($error) && isset($_POST['siret']) && trim($_POST['siret']) ){
				$_POST['siret'] = $siret = str_replace(' ', '', $_POST['siret']);
				if( !validSIRET($siret) ){
					$error = 10;
				}
			}

			// Vérifie le code NAF
			if( !isset($error) && isset($_POST['naf']) && trim($_POST['naf']) ){
				if( !sys_naf_codes_exists($_POST['naf']) ){
					$error = 17;
				}
			}

			// Vérifie que les informations fournies sont valides
			if( !isset($error) ){
				if( $_POST['type']==1 ){ // Particulier
					if( !gu_titles_exists($_POST['title']) ){
						$error = 2;
					}
				}elseif( $_POST['type']==2 ){ // Société
					if( !trim($_POST['society']) ){
						$error = 2;
					}
				}elseif( $_POST['type']==3 ){ // Professionnel
					if( !gu_titles_exists($_POST['title']) || !trim($_POST['society']) ){
						$error = 2;
					}
				}
			}

			// Enregistre les modifications
			if( !isset($error) ){
				if( $_POST['type']==1 ){ // Particulier
					if( !gu_adresses_update($_GET['usr'],$usr['adr_invoices'],$_POST['type'],$_POST['title'],$_POST['firstname'],$_POST['lastname'],'','',$_POST['address1'],ria_array_get($_POST,'address2', ''), $_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['tel'],$_POST['fax'],$_POST['mobile'],$_POST['phone-work'], false, null, null, null, ria_array_get($_POST, 'address3', '')) ){
						$error = 3;
					}
				}elseif( $_POST['type']==2 ){ // Professionnel
					if( !gu_adresses_update($_GET['usr'],$usr['adr_invoices'],$_POST['type'],'','','',$_POST['society'],$_POST['siret'],$_POST['address1'],ria_array_get($_POST,'address2', ''), $_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['tel'],$_POST['fax'],$_POST['mobile'],$_POST['phone-work'], false, null, null, null, ria_array_get($_POST, 'address3', '')) ){
						$error = 3;
					}
				}else{
					if (!isset($_POST['title'])) {
						$_POST['title'] = 0;
					}

					if( !gu_adresses_update($_GET['usr'],$usr['adr_invoices'],$_POST['type'],$_POST['title'],$_POST['firstname'],$_POST['lastname'],$_POST['society'],$_POST['siret'],$_POST['address1'],ria_array_get($_POST,'address2', ''), $_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['tel'],$_POST['fax'],$_POST['mobile'],$_POST['phone-work'], false, null, null, null, ria_array_get($_POST, 'address3', '')) ){
						$error = 3;
					}
				}
			}

			// Enregistre le nouveau dépôt principale
			if( !isset($error) && isset($_POST['main-dps'])  && !$usr['is_sync'] ){
				if( !gu_users_update_dps($_GET['usr'], $_POST['main-dps']) ){
					$error = 18;
				}
			}

			if( !isset($error) ){
				// si profil vendeur / représentant, on utilise le usr_id comme seller_id
				if( $_POST['usr-prf-id'] == PRF_SELLER && (!is_numeric($usr['seller_id']) || $usr['seller_id']<0) ){
					if( !gu_users_set_seller_id( $_GET['usr'], $_GET['usr'] ) ){
						$error = _("Une erreur inattendue s'est produite lors de l'ajout d'un id représentant.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
					}
				}
			}

			// Moyen de paiement si le compte n'est pas synchronisé
			if( !$usr['is_sync'] || (isset($config['admin_users_car_edit_payments']) && $config['admin_users_car_edit_payments']) ){
				gu_users_payment_types_del_all($usr['id']);
				if (isset($_POST['paiements'])) {
					foreach ($_POST['paiements'] as $key => $value) {
						if( !gu_users_payment_types_add($usr['id'],$value,0,0,0) ){
							$error = 9;
						}
					}
				}
			}

			// Code NAF
			if( isset($_POST['naf']) ){
				if( !gu_users_set_naf( $usr['id'], $_POST['naf'] ) ){
					$error = 17;
				}
			}

			// Date de naissance
			if( isset($_POST['dob']) ){
				if( !gu_users_set_date_of_birth($usr['id'], $_POST['dob']) ){
					$error = 15;
				}
			}

			// Notion de masquage
			if( !gu_users_set_masked($usr['id'], isset($_POST['mask'])) ){
				$error = 16;
			}

			// Code client
			if( !isset($error) && !$usr['is_sync'] ){
				if( isset($_POST['ref']) && strlen($_POST['ref'])<=17 && $_POST['ref'] != $usr['ref'] ){
					if( trim($_POST['ref']) != '' && gu_users_exists(0, 0, '', $_POST['ref']) ){
						$error = 14;
					}elseif( !gu_users_update_ref($usr['id'],$_POST['ref']) ){
						$error = 8;
					}
				}
			}

			// Mise à jour de l'adresse email
			if( !isset($error) ){
				if( !gu_valid_email($_POST['email']) ){
					$error = 19;
				}else{
					if( !gu_users_update_email($_GET['usr'],$_POST['email']) ){
						$error = 4;
					}
				}
			}

			// Mise à jour de la longitude et de la latitude
			if( !isset($error) ){
				if (isset($_POST['latitude']) && isset($_POST['longitude'])){
					if( !gu_adresses_set_coordinates($usr['adr_invoices'], $_POST['latitude'], $_POST['longitude'], date('Y-m-d')) ){
						$error = 13;
					}
				}
			}

			if( !isset($error) ){
				$protocol = 'https';
				if( isset($_POST['website-protocol']) && in_array($_POST['website-protocol'], ['http', 'https']) ){
					$protocol = $_POST['website-protocol'];
				}

				if( trim($_POST['website']) != '' ){
					$set_website = gu_users_set_website( $_GET['usr'], $protocol.'://'.$_POST['website'] );
					if( !$set_website ){
						$error = _('La mise à jour du site web du compte a échoué pour une raison inconnue.');
					}
				}
			}

			// Mise à jour du mot de passe
			if (!isset($error)) {
				if (isset($_POST['password1'], $_POST['password2'])) {
					if (strlen($_POST['password1']) != 0) {
						if (strlen($_POST['password1']) > 0 && strlen($_POST['password2']) == 0) {
							$error = 5;
						} elseif ($_POST['password1'] != $_POST['password2']) {
							$error = 7;
						} elseif (!gu_valid_password($_POST['password1'])) {
							$error = 12;
						} elseif (!gu_users_update_password($_GET['usr'], $_POST['password1'])) {
							$error = 6;
						} else {
							if ($usr['prf_id'] == PRF_ADMIN && $usr['can_login'] == 0 && $usr['id'] !== $_SESSION['usr_id']) {
								gu_users_set_can_login($usr['id'], true);
							}
						}
					}
				}
			}

			// Mise à jour des commentaires associés à la fiche client
			if( !isset($error) ){
				if( $usr['notes_id']!=false || trim($_POST['notes'])!='' ){
					if( $usr['notes_id']!=false ){
						if( trim($_POST['notes'])!='' ){
							$result = fld_object_notes_upd( $usr['notes_id'], _('Commentaire fiche client'), $_POST['notes'] );
						}else{
							$result = fld_object_notes_del( $usr['notes_id'] );
						}
					}elseif( trim($_POST['notes'])!='' ){
						$result = fld_object_notes_add( CLS_USER, $usr['id'], _('Commentaire fiche client'), $_POST['notes'] );
					}
					if( isset($result) ){
						if( $result ){
							$success = _('Le commentaire associé au compte a été enregistré avec succès.');
						}else{
							$error = _('Une erreur inattendue s\'est produite lors de l\'enregistrement du commentaire associé au compte.');
						}
					}
				}
				$usr['notes'] = $_POST['notes'];
			}

			// Mise à jour de la possibilité de connexion du compte
			if( !isset($error) ){
				if( isset($_POST['can_login']) && !$_POST['can_login']){
					gu_users_set_can_login( $usr['id'], false );
				}elseif( isset($_POST['can_login']) && $_POST['can_login']){
					gu_users_set_can_login( $usr['id'], true );
				}
			}

			// En cas d'erreur
			if( isset($error) ){
				// Sauvegarde les modifications apportées par l'utilisateur pour ne pas qu'il perde sa saisie
				if( isset($_POST['ref']) ){ $usr['ref'] = $_POST['ref']; }
				if( isset($_POST['usr-prf-id']) ){ $usr['prf_id'] = $_POST['usr-prf-id']; }
				if( isset($_POST['usr-cac-id']) ){ $usr['cac_id'] = $_POST['usr-cac-id']; }
				// Définit le message d'erreur en fonction du code utilisé
				switch( $error ){
					case 1:
						$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.\nVeuillez vérifier.");
						break;
					case 2:
						$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.")."\n"._("Veuillez vérifier. Les champs marqués d'une <span class=\"mandatory\">*</span> sont obligatoires.");
						break;
					case 3:
						$error = _("Une erreur inattendue s'est produite lors de la modification de l'adresse de l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
						break;
					case 4:
						$error = _("Une erreur inattendue s'est produite lors de la modification de l'adresse e-mail de l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
						break;
					case 5:
						$error = _("Veuillez confirmer le nouveau mot de passe en le saisissant à nouveau.");
						break;
					case 6:
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre nouveau mot de passe.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur");
						break;
					case 7:
						$error = _("Les deux mots de passe saisis sont différents.")."\n"._("Veuillez les saisir à nouveau.");
						break;
					case 8:
						$error = _("Une erreur inattendue s'est produite lors de la modification du code client de l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
						break;
					/*case 9:
						$error = _("Le champ code client doit être renseigné.");*/
					case 10:
						$error = _("La valeur saisie pour le numéro de SIRET est incorrecte.");
						break;
					case 11:
						$error = _('Le mot de passe doit contenir un minimum de 6 caractères pour être accepté.');
						break;
					case 12:
						$error = _($config['password_error_message']);
						break;
					case 13 :
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des coordonnées géographiques.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
						break;
					case 14 :
						$error = _("Ce code client est déjà attribué à un autre compte, veuillez en choisir un autre.");
						break;
					case 15 :
						$error = _("Une erreur est survenue lors de la modification de la date de naissance.");
						break;
					case 16 :
						$error = _("Une erreur est survenue lors de la modification du masquage du client.");
						break;
					case 17 :
						$error = _("Le code NAF saisi n'est pas reconnu.");
						break;
					case 18 :
						$error = _('Une erreur est survenue lors de la modification du dépôt principal.');
						break;
					case 19:
						$error = _('L\'adresse e-mail renseignée est invalide.');
						break;
				}
			}
		}

		if( !isset($error) ){
			// Mise à jour de la catégorie comptable
			if( isset($_POST['restrict_portfolio']) ){
				if( !gu_users_set_restrict_portfolio( $_GET['usr'], $_POST['restrict_portfolio'] == '1' ) ){
					$error = _("Une erreur inattendue s'est produite lors de la modification de l'accès au fichier client'.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}else{
					// Demande de synchro des comptes sur les appareils du représentant
					dev_devices_need_sync_add( 0, _DEV_TASK_RELOAD_USERS, $_GET['usr'] );
				}
			}
		}
		if( !isset($error) ){
			// Mise à jour de la catégorie comptable
			if( isset($_POST['usr-cac-id']) && gu_accouting_categories_exists($_POST['usr-cac-id']) ){
				if( !gu_users_set_accouting_category( $_GET['usr'], $_POST['usr-cac-id'] ) ){
					$error = _("Une erreur inattendue s'est produite lors de la modification de la catégorie comptable.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}
		}
		if( !isset($error) ){
			// Mise à jour du code de TVA intra-communautaire
			if( isset($_POST['usr-taxcode']) && !$usr['is_sync'] ){
				if( !gu_users_set_taxcode($_GET['usr'], $_POST['usr-taxcode']) ){
					$error = _("Une erreur inattendue s'est produite lors de la mise à jour du numéro de TVA intracommunautaire.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}
		}
		if( !isset($error) ){
			// Mise à jour du code risque
			if( isset($_POST['code_risk']) && !$usr['is_sync'] ){
				if( !gu_users_set_risk_code($_GET['usr'], $_POST['code_risk']) ){
					$error = _("Une erreur inattendue s'est produite lors de la mise à jour du code risque.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}
		}

		if( !isset($error) ){
			// Mise à jour du profil
			// En cas de compte synchronisé sur les compte admin ou représentant peuvent être mis à jour
			if( $usr['is_sync'] ){
				if( in_array($usr['prf_id'], [PRF_ADMIN, PRF_SELLER]) ){
					if( $_POST['usr-prf-id']!=$usr['prf_id'] && !gu_users_set_profile($_GET['usr'],$_POST['usr-prf-id']) ){
						$error = _("Une erreur inattendue s'est produite lors de la modification du profil de l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
					}
				}
			}elseif( isset($_POST['usr-prf-id']) ){
				if( $_POST['usr-prf-id']!=$usr['prf_id'] && !gu_users_set_profile($_GET['usr'],$_POST['usr-prf-id']) ){
					$error = _("Une erreur inattendue s'est produite lors de la modification du profil de l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}
		}

		if( !isset($error) ){
			// Modifie la catégorie tarifaire
			if(isset($_POST['usr-prc-id'])  && trim($_POST['usr-prc-id']) && !gu_users_set_prc($_GET['usr'],$_POST['usr-prc-id']) ){
				$error = _("Une erreur inattendue s'est produite lors de la modification de la catégorie tarifaire.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}elseif( $_POST['usr-prf-id']!=$usr['prf_id'] && !gu_users_set_profile($_GET['usr'],$_POST['usr-prf-id']) ){
				$error = _("Une erreur inattendue s'est produite lors de la modification du profil de l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}else{
				if( !isset($error) ){
					$_SESSION['riashop']['customer'] = _('Les données ont été modifiées avec succès.');
					header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : ''));
					exit;
				}
			}
		}
	}

	// Bouton Annuler principal
	if( isset($_POST['cancel-main']) ){
		if (isset($_GET['seg']) && is_numeric($_GET['seg']) && $_GET['seg']) {
			header('Location: /admin/customers/index.php?seg='.$_GET['seg']);
			exit;
		}
		if (isset($_GET['seller']) && is_numeric($_GET['seller']) && $_GET['seller']) {
			header('Location: /admin/customers/index.php?seller='.$_GET['seller']);
			exit;
		}

		header('Location: index.php?prf='.$_GET['prf'].'&page='.$_GET['page']);
		exit;
	}
	// Bouton Annuler avis utilisateur
	if( isset($_POST['cancel-rvw']) ){
		header('Location: edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=general&prf='.$_GET['prf'].'&page='.$_GET['page']);
		exit;
	}
	// Bouton Annuler commandes
	if( isset($_POST['cancel-ord']) ){
		header('Location: edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=general&prf='.$_GET['prf'].'&page='.$_GET['page']);
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del']) && $tenant_link ){
		if( $usr['id']==$_SESSION['usr_id'] ){
			$errors[] = _("Attention, vous avez failli supprimer votre compte. Cette action n'est pas autorisée.")."\n"._("Si vous souhaitez réellement supprimer votre compte, demandez soit à un autre administrateur, soit au support.");
		}elseif( $tab == 'general' ){
			// Suppression du compte dans RiaShop
			gu_users_del($_GET['usr']);

			// Redirection sur le listing client en fonction de la façon dont on est arrivé sur la fiche
			header('Location: index.php?prf='.$_GET['prf'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&page='.$_GET['page']);
			exit;
		}
	}

	// Bouton Annuler édition d'avis
	if( isset($_POST['cancel-edit-review']) ){
		header('Location: edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=reviews&prf='.$_GET['prf'].'&page='.$_GET['page']);
		exit;
	}

	// Bouton Supprimer un avis
	if( isset($_POST['delete-review']) ){
		require_once('prd/reviews.inc.php');

		if( is_array($_POST['rvw']) ){
			foreach( $_POST['rvw'] as $rvw ){
				prd_reviews_del($rvw);
			}
		}else{
			prd_reviews_del($_POST['rvw']);
		}
		header('Location: edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=reviews&prf='.$_GET['prf'].'&page='.$_GET['page']);
		exit;
	}

	// Bouton Enregistrer un avis
	if( isset($_POST['save-review']) ){
		require_once('prd/reviews.inc.php');
		if( !isset($_POST['note']) ) $_POST['note'] = null;
		if( !isset($_POST['publish']) ) $_POST['publish'] = false;

		prd_reviews_update($_POST['rvw'],$_POST['name'],$_POST['desc'],$_POST['note'],$_POST['publish']);
		if( $_POST['publish'] ){
			prd_reviews_publish($_POST['rvw']);
		}else{
			prd_reviews_unapprove($_POST['rvw']);
		}
		header('Location: edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=reviews');
		exit;
	}

	// Bouton enregistrer les options
	if( isset($_POST['save-options']) && $tenant_link ){
		// Charge les alertes choisies par l'utilisateur
		$saved_states = array();
		$r_states = gu_ord_alerts_get($_GET['usr']);
		while( $r = ria_mysql_fetch_array($r_states) ){
			$saved_states[] = $r['state_id'];
		}
		// Ajoute les notifications supplémentaires demandées
		if( !isset($_POST['ord-states']) ) $_POST['ord-states'] = array();
		foreach( $_POST['ord-states'] as $s )
			if( !in_array($s,$saved_states) )
				gu_ord_alerts_add( $_GET['usr'], $s );

		// Supprime les notifications qui ne sont plus souhaitées
		foreach( $saved_states as $s ){
			if( !in_array($s,$_POST['ord-states']) ){
				gu_ord_alerts_del( $_GET['usr'], $s );
			}
		}

		if( isset($_POST['alert-cc']) ){
			gu_users_set_alert_cc( $_GET['usr'], $_POST['alert-cc'] );
		}

		header('Location: edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=options&prf='.$_GET['prf'].'&page='.$_GET['page']);
		exit;
	}

	// Bouton options par défaut
	if( isset($_POST['default-options']) && $tenant_link ){
		gu_ord_alerts_set_default($_GET['usr']);
		header('Location: edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=options&prf='.$_GET['prf'].'&page='.$_GET['page']);
		exit;
	}

	// Alertes emails de disponibilité
	if( isset($_POST['add-ref']) && isset($_POST['ref']) && trim($_POST['ref']) ){
		$rprd = prd_products_get(0,$_POST['ref'],0,true);
		if( !ria_mysql_num_rows($rprd) ){
			$error = sprintf(_('La référence %s n\'a pas été trouvée parmi les produits publiés'), htmlspecialchars($_POST['ref']));
		}else{
			$prd = ria_mysql_fetch_array($rprd);
			if( !$prd['orderable'] ){
				$error = sprintf(_('La référence %s est générique, elle désigne un produit qui n\'est pas commandable.'), htmlspecialchars($_POST['ref']));
			}elseif( prd_products_is_available($prd['id']) ){
				$error = sprintf(_('La référence %s est disponible, et peut déjà être commandée.'), htmlspecialchars($_POST['ref']));
			}else{
				$result = gu_livr_alerts_add( $usr['email'], $prd['id'] );
				if( !$result )
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de cette alerte de disponibilité.");
			}
		}
		$tab = 'availability';
	}

	// Suppression d'une alerte de disponibilité
	if( isset($_POST['del-sel']) && isset($_POST['prd-sel']) && is_array($_POST['prd-sel']) ){
		foreach( $_POST['prd-sel'] as $p ){
			gu_livr_alerts_del($usr['email'],$p);
		}
		header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=availability');
		exit;
	}

	// Ajout d'un parrainage
	if (isset($_POST['save-godson'])) {
		$tab = "rewards";

		if (!isset($_POST['godson']) || !isemail($_POST['godson'])) {
			$error = _("Merci de bien vouloir renseigner l'adresse mail du filleul");
		}else{
			// Array ( [count_wst] => 1 [godson] => <EMAIL> [save-godson] => Enregistrer )
			if( $_POST['godson']==gu_users_get_email($_GET['usr']) ){
				$error = _("Un client ne peut pas se parrainer lui-même.");
			} elseif( gu_sponsor_promotions_exists($_GET['usr'], $_POST['godson']) || gu_sponsor_points_exists($_GET['usr'], $_POST['godson']) ){
				$error = sprintf(_('Vous parrainez déjà la personne utilisant l\'adresse mail "%s".'), $_POST['godson']);
			}elseif( !gu_sponsor_create($_GET['usr'], $_POST['godson'], '') ){
				$error = sprintf(_('Une erreur inattendue s\'est produite lors du parrainage de la personne utilisant l\'adresse mail "%s". Veuillez réessayer ou prendre contact pour nous signaler le problème.'), $_POST['godson']);
			}

			if (!isset($error)) {
				header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=rewards');
				exit;
			}
		}
	}

	// Suppression d'un parrainage
	if (isset($_POST['sp_link_del'])) {
		if (isset($_POST['sp_link_id']) && is_array($_POST['sp_link_id']) && count($_POST['sp_link_id'])) {
			if (!gu_sponsor_links_del( $_GET['usr'], $_POST['sp_link_id'])) {
				$error = _('Une erreur inattendue s\'est produite lors la suppression des liens de parrainages. Veuillez réessayer ou prendre contact pour nous signaler le problème.');
			}
		}

		if (!isset($error)) {
			header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=rewards');
			exit;
		}
	}

	// Suppression d'un ou plusieurs objectifs sur des objets
	if( isset($_POST['del-goal-obj']) ){
		if( isset($_POST['del-goal-byclass']) && is_array($_POST['del-goal-byclass']) ){
			foreach( $_POST['del-goal-byclass'] as $del ){
				$ar = explode( '-', $del );

				$first = true;
				$obj_ids = [];
				foreach( $ar as $a ){
					if( $first ){
						$first = false;
						continue;
					}

					if( is_numeric($a) && $a > 0 ){
						$obj_ids[] = $a;
					}
				}

				if( !obj_periods_del_by_class($_POST['obj_id'], $ar[0], $obj_ids) ){
					$error = _('Une erreur inattendue s\'est produite lors de la suppression de l\'objectif.');
					break;
				}
			}
		}

		if( !isset($error) ){
			header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].'&tab=goals&goal='.$_POST['obj_id'] );
		}
	}

	// Suppression d'un ou plusieurs objectifs sur des clients
	if( isset($_POST['del-goal-user']) ){
		if( isset($_POST['del-goal-groupby_user']) && is_array($_POST['del-goal-groupby_user']) ){
			foreach( $_POST['del-goal-groupby_user'] as $one_goal ){
				$ar = explode( '-', $one_goal );

				if( !obj_periods_del_for_user($_POST['obj_id'], $ar[1], $ar[0]) ){
					$error = _('Une erreur inattendue s\'est produite lors de la suppression de l\'objectif.');
					break;
				}
			}
		}

		if( !isset($error) ){
			header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].'&tab=goals&goal='.$_POST['obj_id'] );
		}
	}

	// Action sur l'onglet "Avancés"
	view_admin_tab_fields_actions( CLS_USER, $_GET['usr'], $config['i18n_lng'] );

	// Gestion de la réponse d'un message d'un contact
	$contacts = isset($_GET['usr']) ? contacts_get( $_GET['usr'] ) : false;

	$res = false;
	if( isset($_POST['submit-rep-0']) && $_POST['reponce-message-0'] != "") {

		$file = isset($_POST['tab_file']) ? explode(",",$_POST['tab_file']) : null;
		$res = contacts_send_reply($usr['email'], 0, $_SESSION['usr_id'], $_POST['subject-mess'], $_POST['reponce-message-0'], '',$file,true);

	}elseif( $contacts && ria_mysql_num_rows($contacts) ){

		$j = 1;
		while( $cnt = ria_mysql_fetch_array($contacts) )
		{
			if( isset($_POST['submit-rep-'.$j]) && $_POST['reponce-message-'.$j] != "")
			{
				$file = isset($_POST['tab_file']) ? explode(",",$_POST['tab_file']) : null;
				$res = contacts_send_reply($cnt['email'], $_POST['mess-'.$j], $_SESSION['usr_id'], $cnt['subject'], $_POST['reponce-message-'.$j], $cnt['body'],$file);
			}
			$j++;
		}

	}

	// Ajout d'une image principale sur l'utilisateur
	if( isset($_POST['saveimg']) && sizeof($_FILES)>0 ){
		if( isset($_FILES['main-img']) && $_FILES['main-img']['error']!=UPLOAD_ERR_NO_FILE ){
			$img_id = gu_images_main_upload( $_GET['usr'], 'main-img' );
			if( !$img_id ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'image principale.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}else{
				$usr['img_id'] = $img_id;
			}
		}
		$tab = 'images';
		unset( $_POST['save'] );
	}

	// Supprime l'image principale sur l'utilisateur
	if( isset($_POST['del-imo-main']) ){
		if( !gu_images_main_del($_GET['usr']) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression de l'image principale.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}

		$tab = 'images';
		$usr['img_id'] = 0;
		unset( $_POST['del-imo-main'] );
	}

	$adr = array( 'title_id' => 0, 'type_id' => 3, 'firstname'=>'', 'lastname'=>'', 'society'=>'', 'siret'=>'', 'address1'=>'', 'address2'=>'', 'address3'=>'', 'zipcode'=>'', 'postal_code'=>'', 'city'=>'', 'phone'=>'', 'fax'=>'', 'mobile'=>'', 'phone_work'=>'', 'country'=>'FRANCE' );

	// Chargement de l'adresse de facturation
	if( is_numeric($usr['adr_invoices']) ){
		$radr = gu_adresses_get( $usr['id'],$usr['adr_invoices'] );
		if( $radr && ria_mysql_num_rows($radr) ){
			$adr = ria_mysql_fetch_assoc( $radr );

			// Préserve les modifications apportées via le formulaire, même si elles n'ont pas pu être enregistrées
			if( sizeof($_POST) ){
				$adr['title_id'] = isset($_POST['title']) ? $_POST['title'] : '';
			}
			$fields = array( 'firstname', 'lastname', 'society', 'siret', 'address1', 'address2', 'address3', 'zipcode', 'postal_code', 'city', 'phone', 'fax', 'mobile', 'phone_work', 'country' );
			foreach( $fields as $f ){
				if( isset($_POST[$f]) ){
					$adr[ $f ] = $_POST[ $f ];
				}
			}
		}
	}

	// Lier un compte enfant
	if( isset($_POST['save-new-relation']) ){
		$tab = 'rights';
		$_POST['new-relation'] = isset($_POST['new-relation']) && $_POST['new-relation']!='' ? $_POST['new-relation'] : $_POST['select-new-relation'];
		$end = stripos( $_POST['new-relation'], ' - ' );
		$email = trim( $end !== false ? substr($_POST['new-relation'], 0, $end) : $_POST['new-relation'] );

		// vérifier que le compte existe
		if( $email=='' || !gu_users_exists(0,0,$email) ){
			$error = _("Cette adresse mail n'est pas reconnue.");
		}else{
			$rchild = gu_users_get( 0, $email );
			if( !$rchild || !ria_mysql_num_rows($rchild) ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du lien entre les deux comptes.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}else{
				$child = ria_mysql_fetch_array( $rchild );
				$parent = gu_users_get_parent_id( $child['id'] );

				if( $child['id']==$usr['id'] ){ // les deux compte doivent être différents
					$error = sprintf(_("Le compte &lt;<a target=\"_blank\" href=\"/admin/customers/edit.php?usr=%d&amp;tab=rights\">%s</a>&gt; ne peut pas être lié à lui-même."), $child['id'], $email) ;
				}elseif( $parent==$_GET['usr'] ){ // le lien entre les comptes existe déjà
					$error = sprintf(_("Le compte &lt;<a target=\"_blank\" href=\"/admin/customers/edit.php?usr=%d&amp;tab=rights\">%s</a>&gt; est déjà lié à ce compte."), $child['id'], $email) ;
				}elseif( $parent ){ // le compte enfant est déjà lié à un autre compte
					$error = sprintf(_("Le compte &lt;<a target=\"_blank\" href=\"/admin/customers/edit.php?usr=%d&amp;tab=rights\">%s</a>&gt; a déjà un responsable. Vous pouvez le modifier en vous rendant sur <a  target=\"_blank\" href=\"/admin/customers/edit.php?usr=%d&tab=rights\">sa fiche</a>."), $child['id'], $email, $child['id']) ;
				}else{
					if( !gu_users_set_parent_id($child['id'], $usr['id']) ){
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du lien entre les deux comptes.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
					}else{
						header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=rights');
						exit;
					}
				}
			}
		}
	}

	// Retirer un compte enfant d'un compte parent
	if( isset($_GET['del-link-usr'], $_GET['usr'], $_GET['child']) ){
		$tab = 'rights';
		if( !gu_users_del_parent_id($_GET['child']) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression du lien entre les deux comptes.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}
		if( !isset($error) ){
			header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=rights');
			exit;
		}
	}

	// Supprime un compte enfant (seulement si un identifiant parent est présent)
	if( isset($_GET['del-usr-child'], $_GET['child']) ){
		$tab = 'rights';
		$usr_parent = gu_users_get_parent_id( $_GET['child'] );
		if( $usr_parent && !gu_users_del($_GET['child']) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression du compte.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}

		if( !isset($error) ){
			header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=rights');
			exit;
		}
	}

	// Lier le compte à un compte référent
	if( isset($_POST['save-new-parent']) ){
		$tab = 'rights';
		$_POST['new-parent'] = isset($_POST['new-parent']) && $_POST['new-parent']!='' ? $_POST['new-parent'] : $_POST['select-new-parent'];
		$end = stripos( $_POST['new-parent'], ' - ' );
		$email = trim( $end!==false ? substr($_POST['new-parent'], 0, $end) : $_POST['new-parent'] );

		// vérifier que le compte existe
		if( $email=='' || !gu_users_exists(0,0,$email) ){
			$error = _("Cette adresse mail n'est pas reconnue.");
		}else{
			$name = gu_users_get_name( $_GET['usr'] );
			$rparent = gu_users_get( 0, $email );
			if( !$rparent || !ria_mysql_num_rows($rparent) ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du lien entre les deux comptes.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}else{
				$parent = ria_mysql_fetch_array( $rparent );
				$usr_parent = gu_users_get_parent_id( $parent['id'] );

				if( $parent['id']==$usr['id'] ){ // les deux compte doivent être différents
					$error = sprintf(_("Le compte &lt;<a target=\"_blank\" href=\"/admin/customers/edit.php?usr=%d&amp;tab=rights\">%s</a>&gt; ne peut pas être lié à lui-même."), $parent['id'], $email);
				}elseif( $parent['parent_id'] ){ // le compte parent ne doit pas être un compte enfant
					$error = sprintf(_("Le compte &lt;<a target=\"_blank\" href=\"/admin/customers/edit.php?usr=%d&amp;tab=rights\">%s</a>&gt; se trouve sous la responsabilité d'un compte client et ne peut donc pas être utilisé comme responsable de %s."), $parent['id'], $email, trim($name));
				}elseif( $parent['id']==$usr['parent_id'] ){ // le lien entre les comptes existe déjà
					$error = sprintf(_("Le compte &lt;<a target=\"_blank\" href=\"/admin/customers/edit.php?usr=%d&amp;tab=rights\">%s</a>&gt; est déjà lié à ce compte."), $parent['id'], $email);
				}else{
					if( !gu_users_set_parent_id($usr['id'], $parent['id']) ){
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement du lien entre les deux comptes.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
					}else{
						header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=rights');
						exit;
					}
				}
			}
		}
	}

	// Sauvegarde les droits d'un utilisateur
	if( isset($_POST['save-rights']) ){
		$tab = 'rights';

		$_POST['rghprf'] = isset($_POST['rghprf']) ? $_POST['rghprf'] : array();
		if( !gu_users_rights_affected($_GET['usr'], $_POST['rghprf']) ){
			$error = _("Une erreur inattendue s'est produite lors de la sauvegarde des droits d'accès de l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
		}
		if( !isset($error) ){
			header('Location: /admin/customers/edit.php?wst_id=-1&amp;usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=rights');
			exit;
		}
	}

	// Supprimer le lien avec le compte référent
	if( isset($_GET['del-parent']) ){
		$tab = 'rights';

		if( !gu_users_del_parent_id($_GET['usr']) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression du compte référent.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}else{
			header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=rights');
			exit;
		}
	}

	// Bouton Enregistrer (droits d'accès à l'interface d'administration)
	if( isset($_POST['save-admin-rights']) ){
		$tab = 'rights';
		$allRGH = array();
		$active_right_user = true;
		$is_yuto = $_GET['usr'] > 0 && gu_users_get_prf($_GET['usr']) == PRF_SELLER && !ria_array_get($config, 'seller_admin_access', false);
		// Cette règle est désactivée pour les super-administrateurs quand la gestion des droits n'est pas activée
		if( $config['USER_RIASTUDIO'] ){
			$active_right_user = false;
		}
		gu_rights_get_all_rights_visible($config['tnt_id'], $config['wst_id'], $active_right_user, $is_yuto, $allRGH);
		if( gu_users_rights_del( $_GET['usr'], $allRGH, false, false, false, true) ){
			if( isset($_POST['rgh']) ){
				foreach( $_POST['rgh'] as $rgh=>$value ){
					$_POST['rgh'][$rgh] = 1;
				}
				gu_users_rights_add( $_GET['usr'], $_POST['rgh'] );
			}
		}else{
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des droits d'accès.");
		}

		// Recharge le cache
		gu_users_load_admin_rights( true, true, $_GET['usr'] );
		gu_users_load_admin_rights( false, true, $_GET['usr'] );
	}

	// Bouton Enregistrer (droits d'accès à Yuto)
	if( isset($_POST['save-yuto-rights']) ){
		$tab = 'rights';
		$allRGH = array();
		$active_right_user = true;
		$is_yuto = $_GET['usr'] > 0 && gu_users_get_prf($_GET['usr']) == PRF_SELLER && !ria_array_get($config, 'seller_admin_access', false);
		// Cette règle est désactivée pour les super-administrateurs quand la gestion des droits n'est pas activée
		if( $config['USER_RIASTUDIO'] ){
			$active_right_user = false;
		}
		gu_rights_get_all_rights_visible($config['tnt_id'], $config['wst_id'], $active_right_user, $is_yuto, $allRGH);
		if( gu_users_rights_del( $_GET['usr'], $allRGH, false, false, false, true) ){
			if( isset($_POST['rgh-yuto']) ){
				foreach( $_POST['rgh-yuto'] as $rgh=>$value ){
					$_POST['rgh-yuto'][$rgh] = 1;
				}
				gu_users_rights_add( $_GET['usr'], $_POST['rgh-yuto'] );
			}
		}else{
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des droits d'accès.");
		}

		// Recharge le cache
		gu_users_load_yuto_rights( true, $_GET['usr'] );
		gu_users_load_yuto_rights( false, $_GET['usr'] );
	}

	// Créer un nouveau compte avec un identifiant parent
	if( isset($_POST['new-usr-child']) ){
		header('Location: /admin/customers/new.php?parent='.$_GET['usr']);
		exit;
	}

	// Action sur l'onglet Favoris
	if( isset($_POST['del-bookmarks']) ){
		$tab = 'bookmarks';
		if( isset($_POST['bookmark']) && is_array($_POST['bookmark']) && sizeof($_POST['bookmark']) ){
			foreach( $_POST['bookmark'] as $bmk ){
				if( !gu_bookmarks_del($_GET['usr'], $bmk) ){
					$error = _("Une erreur inattendue s'est produite lors de la suppresion des favoris.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}
		}

		if( !isset($error) ){
			header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=bookmarks');
			exit;
		}
	}

	// Supprimer une liste de favoris
	if( isset($_POST['del-wishlist']) ){
		$tab = 'bookmarks';
		if( isset($_POST['wishlist']) && is_array($_POST['wishlist']) && sizeof($_POST['wishlist']) ){
			foreach( $_POST['wishlist'] as $gwt ){
				if( !gu_wishlists_del($gwt) ){
					$error = _("Une erreur inattendue s'est produite lors de la suppression des listes personnalisées.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}
		}

		if( !isset($error) ){
			header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=bookmarks');
			exit;
		}
	}

	// Fonction Mot de passe oublié
	$rwst_cfg = cfg_emails_get( 'pwd-lost', null );
	$count_wst = $rwst_cfg ? ria_mysql_num_rows($rwst_cfg) : 1;

	// Enregistrer les points de fidélité
	if( isset($_POST['save-reward']) ){
		$tab = 'rewards';
		$date = isdate($_POST['rwd-limit']) ? dateparse($_POST['rwd-limit']) : false;

		if( !isset($_POST['rwd-name'], $_POST['rwd-pts'], $_POST['rwd-limit']) ){
			$error = _("Une ou plusieurs informations sont manquantes.");
		}elseif( trim($_POST['rwd-name'])=='' ){
			$error = _("Veuillez renseigner une désignation pour cet ajout.");
		}elseif( !is_numeric($_POST['rwd-pts']) || $_POST['rwd-pts']<=0 ){
			$error = _("Veuillez renseigner un nombre de points supérieur à zéro.");
		}elseif( !$date || strtotime($date)<time() ){
			$error = sprintf(_("Veuillez renseigner une date limite supérieur à aujourd'hui au format JJ/MM/AAAA, exemple : 31/12/%d."), (date('Y')+1) );
		}else{
			$days = round( (strtotime($date)-time()) / 86400 );
			if( !stats_rewards_add( $_GET['usr'], gu_users_get_prf($_GET['usr']), 0, false, $_POST['rwd-name'], 0, false, $_POST['rwd-pts'], false, false, $days) ){
				$error = _("Une erreur inattendue s'est produite lors de l'ajout des points de fidélité.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}else{
				header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=rewards');
				exit;
			}
		}
	}

	// Inscription au Programme de Fidélité
	if( isset($_POST['rwu-inscript']) ){
		if (!rwd_users_enabled($_GET['usr'])) {
			$error = _("Une erreur inattendue s'est produite lors de l'inscription au programme de fidélité.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}else{
			if ($config['tnt_id']==13) {
				// Propre à Pierre Oteiza, génère un numéro de carte fidélité
				// TODO : SI DES MODIFICATIONS SONT APPORTEES DANS CE HACK PROPRE A PIERRE OTEIZA, IL FAUT PENSER A FAIRE DE MÊME SUR LE SITE
				// FICHIER : /htdocs/include/view.site.inc.php (du projet Pierre Oteiza)
				$tmp_card = fld_object_values_get( $_GET['usr'], _FLD_USR_CARD_RWD, '', false, true );

				// Si le client n'a pas de numéro de carte fidélité alors :
				if($tmp_card == false)
				{
					$fetch_highest_promotionnal_code_query = ria_mysql_query("SELECT MAX(`pv_value`) FROM `fld_object_values` WHERE `pv_tnt_id` = 13 AND `pv_fld_id` = 5005 AND `pv_value` LIKE '20000006%'");
					$query_result = ria_mysql_fetch_assoc($fetch_highest_promotionnal_code_query);
					
					if(!$query_result['MAX(`pv_value`)'] OR is_null($query_result['MAX(`pv_value`)']))
					{
						// on initialise à la demande du client la range 60000
						$promo_code = ean13_check_digit('200000060000');
					}
					else
					{
						if(intval($query_result['MAX(`pv_value`)']) > 2000000699900)
						{
							// Si notre plage est complète alors mettre le message suivant pour le code fidélité client.
							$promo_code = "Plus de place dans la plage 60000-70000";
						}
						else
						{
							// formate la chaine de caractère du résultat de la requête BDD
							$format_to_ean = substr($query_result['MAX(`pv_value`)'], 0, -1);
							// Incrémente la valeur pour le client ( pour un id unique )
							$unique_id = intval($format_to_ean + 1);
							// Créer le format ean pour l'id code fidélité client.
							$promo_code = ean13_check_digit(strval($unique_id));
						}
					}
					
					fld_object_values_set( $_GET['usr'], _FLD_USR_CARD_RWD, $promo_code );
					gu_users_set_need_sync( $_GET['usr'], true );
				}
			}
		}

		if (!isset($error)) {
			$_SESSION['rwu-inscript-success'] = true;
			header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=rewards');
			exit;
		}
	}

	// Désinscription du programme de fidélité
	if( isset($_POST['rwu-uninscript']) ){
		if (!rwd_users_disabled($_GET['usr'])) {
			$error = _("Une erreur inattendue s'est produite lors de la désinscription au programme de fidélité.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}else{
			if ($config['tnt_id'] == 13) {
				fld_object_values_set( $_GET['usr'], _FLD_USR_CARD_RWD, '' );
				gu_users_set_need_sync( $_GET['usr'], true );
			}
		}

		if (!isset($error)) {
			$_SESSION['rwu-uninscript-success'] = true;
			header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=rewards');
			exit;
		}
	}

	// Action sur les points de fidélité
	if( isset($_POST['del-rewards']) ){
		$tab = 'rewards';

		if (isset($_POST['rwd-id']) && is_array($_POST['rwd-id']) && count($_POST['rwd-id'])) {
			if (!stats_rewards_del_by_ids($_POST['rwd-id'])) {
				$error = _("Une erreur inattendue est survenue lors de la suppression des points de fidélité.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}

		if (!isset($error)) {
			$_SESSION['riashop']['customer'] = _('Les points de fidélité ont bien été supprimés.');
			header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=rewards');
			exit;
		}
	}

	// Confirmation de création de compte client
	if( isset($_POST['usr-create-confirmed']) ){
		gu_users_set_confirmed($_GET['usr']);
		header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : ''));
		exit;
	}

	// Suppression de la souscription à une ou des newsletter pour un client
	$email = gu_users_get_email($_GET['usr']);

	if( isset($_POST['unsubscribe-newsletter'])){
		if(isset($_REQUEST['sub']) && is_array($_REQUEST['sub'])) {
			foreach( $_REQUEST['sub'] as $subs ) {
				nlr_subscribers_del( $email, $subs );
			}
		} else if( isset($_REQUEST['sub'] ) ) {
			nlr_subscribers_del( $email, $_REQUEST['sub'] );
		}

		header('Location: /admin/customers/edit.php?usr='.$_GET['usr'].(isset($_GET['seg']) ? '&seg='.$_GET['seg'] : '').(isset($_GET['seller']) ? '&seller='.$_GET['seller'] : '').'&tab=options');

		exit;
	}

	if (isset($_POST['create-order'])){
		header("Location: /admin/orders/order.php?ord=new&usr=".$_GET['usr']);
		exit;
	}

	$name_user = gu_users_get_name($_GET['usr']);
	if( trim($name_user) == '' ){
			$name_user = $usr['email'];
	}

	// Défini le fil d'Ariane
	view_admin_location_set_edit_customer_breadcrumbs();

	define('ADMIN_PAGE_TITLE', (is_numeric($usr['adr_invoices']) ? $name_user.' - ' : '')._('Comptes clients'));
	require_once('admin/skin/header.inc.php');
?>

<script>
function usrSendEmail(){
	window.open( 'mailto:' + $('#email').val() );
}
function usrMeeting(){
<?php
	use Spatie\CalendarLinks\Link;
	$icalLink = Link::create(
		'Rendez-vous : '.trim( $usr['society'].' '.$usr['adr_firstname'].' '.$usr['adr_lastname'] ),
		DateTime::createFromFormat('Y-m-d H:i', date('Y-m-d H:i') ),
		DateTime::createFromFormat('Y-m-d H:i', date('Y-m-d H:i', strtotime('+1 hour') ) )
	)
		->description(
			'Code client : '.$usr['ref']."\n".
			'Téléphone : '.$usr['phone']."\n".
			'Mobile : '.$usr['mobile']."\n".
			'Adresse email :'.$usr['email']
		)
		->address( $usr['address1'].' '.$usr['address2'].' '.$usr['zipcode'].' '.$usr['city'] )
		->google();
	print 'window.open( \''.$icalLink.'\' );';
?>
}
</script>

<form action="edit.php?wst_id=-1&amp;usr=<?php print $_GET['usr']; ?>&amp;prf=<?php print $_GET['prf']; ?>&amp;page=<?php print $_GET['page']; ?><?php print isset($_GET['seg']) ? '&amp;seg='.$_GET['seg'] : ''; ?><?php print isset($_GET['seller']) ? '&amp;seller='.$_GET['seller'] : ''; ?>" id="form-edit-user" method="post" enctype="multipart/form-data">

	<input type="hidden" name="count_wst" id="count_wst" value="<?php print $count_wst; ?>" />

<h2>
	<?php print view_usr_is_sync($usr) ?>
	<?php print is_numeric($usr['adr_invoices']) ? htmlspecialchars($name_user) : ''; ?>
	<?php if( $usr['id']>0 ){ ?>
		<?php if( gu_user_is_authorized('_RGH_ADMIN_ORDER_CREATE') ){ ?>
		<a href="/orders/order.php?ord=new&amp;usr=<?php print $usr['id']; ?>" class="button float-right hide-mobile" style="margin-left: 5px"><?php print _('Nouveau devis'); ?></a>
		<?php } ?>
		<input type="button" value="RDV" class="float-right hide-mobile" onclick="usrMeeting()">
		<input type="button" value="Email" class="float-right hide-mobile" onclick="usrSendEmail()">
		<input type="submit" name="save-main" class="btn-main float-right hide-mobile" value="<?php print _('Enregistrer'); ?>" title="<?php print _('Enregistrer les modifications'); ?>">
	<?php } ?>
</h2>

<?php
	/*Phrase pour savoir si un email de récupération de mot de passe a été envoyé ou non.*/

	$errors = array();
	if (isset($error)) $errors[] = nl2br($error);

	$messages = array();
	if (isset($_SESSION['riashop']['customer'])) {
		$messages[] = $_SESSION['riashop']['customer'];
		unset($_SESSION['riashop']['customer']);
	}

	if (isset($_SESSION['riashop']['avoir'])) {
		$messages[] = $_SESSION['riashop']['avoir'];
		unset($_SESSION['riashop']['avoir']);
	}

	if (isset($send_result)){
		if( $send_result === '-1' ){
			$errors[] = _("La fonction \"Mot de passe oublié\" n’est pas activée sur votre installation, merci de prendre contact avec un administrateur pour plus d’informations.");
		}else{
			if( $send_result ){
				$messages[] = _('Un email a été envoyé à l\'adresse').' '.htmlspecialchars($_POST['email']);
			}else{
				$errors[] = _("Une erreur inattendue s'est produite lors de l'envoi de la réinitialisation de mot de passe.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
			}
		}
	}

	if( isset($_GET['upd-fields-success']) ){
		echo '<div class="success">'._('Les données saisies ont été mises à jour avec succès.').'</div>';
	}
	if( count($messages) ){
		echo '<div class="error-success">'. implode('<br />', $messages) . '</div>';
	}
	if( count($errors) ){
		echo '<div class="error">' . implode('<br />', $errors) . '</div>';
	}
	if (isset($_SESSION['success-user-edit'])) {
		echo '<div class="success">'.nl2br($_SESSION['success-user-edit']).'</div>';
		unset($_SESSION['success-user-edit']);
	}
?>

	<script><!--
			$('#form-edit-user').attr('autocomplete','off');
			$(document).ready(function(){
				$('#password1').val('');
			});
	--></script>
	<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="<?php print _('Général')?>" <?php if( $tab == 'general' ) print 'class="selected"'; ?> /></li>
		<?php if( view_admin_show_tab_fields( CLS_USER, $_GET['usr'] ) ){ ?>
		<li><input type="submit" name="tabFields" value="<?php print _('Avancé')?>" <?php if( $tab == 'fields' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
		<?php if( tnt_tenants_have_yuto() ){ ?>
		<li><input type="submit" name="tabReports" value="<?php print _('Rapports'); ?>" <?php if( $tab == 'reports' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
		<?php if( $usr['prf_id']!=PRF_ADMIN && $usr['prf_id']!=PRF_SELLER && gu_users_admin_rights_used('_MDL_ORDERS') ){ ?>
		<li><input type="submit" name="tabOrders" value="<?php print _('Commandes')?>" <?php if( $tab == 'orders' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
		<li><input type="submit" name="tabAddresses" value="<?php print _('Adresses')?>" <?php if( $tab == 'addresses' ) print 'class="selected"'; ?> /></li>
		<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_REWARD') ){
			if( $usr['prf_id']!=PRF_ADMIN && $usr['prf_id']!=PRF_SELLER && gu_user_is_authorized('_RGH_ADMIN_TOOL_REWARD') ){ ?>
			<li><input type="submit" name="tabRewards" value="<?php print _('Fidélité')?>" <?php if( $tab == 'rewards' ) print 'class="selected"'; ?> /></li>
			<?php }
		}
		if( $usr['prf_id']!=PRF_ADMIN && $usr['prf_id']!=PRF_SELLER && tnt_tenants_have_websites() ){ ?>
		<li><input type="submit" name="tabContacts" value="<?php print _('Contacts')?>" <?php if( $tab == 'contacts' ) print 'class="selected"'; ?> /></li>
		<?php }
		if( $usr['prf_id']!=PRF_ADMIN && $usr['prf_id']!=PRF_SELLER && tnt_tenants_have_websites() ){ ?>
		<li><input type="submit" name="tabReviews" value="<?php print _('Avis')?>" <?php if( $tab == 'reviews' ) print 'class="selected"'; ?> /></li>
		<?php }
		if( $usr['prf_id']!=PRF_ADMIN && $usr['prf_id']!=PRF_SELLER && tnt_tenants_have_websites() ){ ?>
        <li><input type="submit" name="tabReference" value="<?php print _('Suggestions')?>" <?php if( $tab == 'reference' ) print 'class="selected"'; ?> /></li>
		<?php }
		if( $usr['prf_id']!=PRF_ADMIN ){ ?>
		<li><input type="submit" name="tabOptions" value="<?php print _('Options')?>" <?php if( $tab == 'options' ) print 'class="selected"'; ?> /></li>
		<?php }
		if( tnt_tenants_have_websites() ){ ?>
		<li><input type="submit" name="tabAvailable" value="<?php print _('Disponibilité')?>" <?php if( $tab == 'availability' ) print 'class="selected"'; ?> /></li>
		<li><input type="submit" name="tabBookmarks" value="<?php print _('Favoris')?>" <?php if( $tab == 'bookmarks' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
		<li><input type="submit" name="tabRelations" value="<?php print _('Relations')?>" <?php if( $tab == 'relations' ) print 'class="selected"'; ?> /></li>
		<li><input type="submit" name="tabImages" value="<?php print _('Images')?>" <?php if( $tab == 'images' ) print 'class="selected"'; ?> /></li>
		<?php if( $usr['prf_id']!=PRF_ADMIN && $usr['prf_id']!=PRF_SELLER && tnt_tenants_have_websites() ){ ?>
		<li><input type="submit" name="tabStats" value="<?php print _('Statistiques')?>" <?php if( $tab == 'stats' ) print 'class="selected"'; ?> /></li>
		<?php }
		if( ($usr['prf_id']==PRF_ADMIN || $usr['prf_id']==PRF_SELLER || $usr['prf_id']==PRF_RESELLER) ){ ?>
		<li><input type="submit" name="tabGoals" value="<?php print _('Objectifs')?>" <?php if( $tab == 'goals' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
		<?php if( $usr['id']!=$_SESSION['usr_id'] ){ ?>
		<li><input type="submit" name="tabRights" value="<?php print _('Droits')?>" <?php if( $tab == 'rights' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
	</ul>
	<div id="tabpanel">

<?php if( $tab != 'addresses' ){ ?>
	<div class="block-notice-container<?php print ( $tab == 'relations' ? ' relations-container' : '' ); ?>">
<?php } ?>
		<?php
			if( !$tenant_link ){
				print '<div class="notice">'._('Ce compte utilisateur est verrouillé, il ne peut pas être modifié ou supprimé.').'</div>';
			}
			if( $usr['prf_id'] == PRF_ADMIN && $usr['can_login'] == 0 ){
				print '<div class="notice">'._('Ce compte administrateur est bloqué, car trop de tentatives de connexions erronés. Pour le débloquer veuillez cliquer ici').' : <button type="submit" method="post" name="unlock-user">'._('Débloquer').'</button>.</div>';
			}
			if( gu_users_get_is_locked($_GET['usr']) ){
				print '<div class="error">'._('Ce compte est actuellement bloqué, toute activité sur ce compte doit faire l\'objet d\'une attention particulière').'</div>';
			}

			if( $tab == 'general' ){ // Onglet Général
				include './../views/customers/tabs/general.php';
			}elseif( $tab == 'orders' ){ // Onglet Commandes
				include './../views/customers/tabs/orders.php';
			}elseif( $tab == 'addresses' ){ // Onglet Adresses
				include './../views/customers/tabs/adresses.php';
			}elseif( $tab == 'reviews' ){ // Onglet Avis consommateurs
				include './../views/customers/tabs/reviews.php';
			}elseif( $tab == 'reference' ){ // Onglet Recommandations (envoyer à un ami)
				include './../views/customers/tabs/reference.php';
			}elseif( $tab == 'options' ){ // Onglet Options
				include './../views/customers/tabs/options.php';
			}elseif( $tab == 'availability' ){ // Onglet Disponibilité
				include './../views/customers/tabs/availability.php';
			}elseif( $tab == 'bookmarks' ){ // Onglet Favoris
				include './../views/customers/tabs/bookmarks.php';
			}elseif( $tab == 'delayed' ){ // Onglet Reliquats
				include './../views/customers/tabs/delayed.php';
			}elseif( $tab == 'relations' ){ // Onglet Relations
				$src_cls = CLS_USER;
				$src = array($_GET['usr'], 0, 0);
				include(dirname(__FILE__).'/../relations/edit.php');
			}elseif( $tab == 'contacts' ){ // Onglet Contacts
				include './../views/customers/tabs/contacts.php';
			}elseif( $tab == 'stats' ){ // Onglet Statistiques
				include './../views/customers/tabs/stats.php';
			}elseif( $tab == 'images' ){ // Onglet Images
				include './../views/customers/tabs/images.php';
			}elseif( $tab == 'reports' ){ // Onglet Rapports (de visite et d'appels)
				include './../views/customers/tabs/reports.php';
			}elseif( $tab == 'fields' ){ // Onglet Champs avancés
				echo view_admin_tab_fields( CLS_USER, $usr['id'], $config['i18n_lng'] );
			}elseif( $tab == 'rights' ){ // Onglet Droits d'accès
				if( $usr['prf_id'] == PRF_ADMIN && (!isset($_GET['siteright']) || !$_GET['siteright']) ){
					include './../views/customers/tabs/admin-rights.php';
				}else if( $usr['prf_id'] == PRF_SELLER && (!isset($_GET['siteright']) || !$_GET['siteright']) ) {
					include './../views/customers/tabs/rights.php';
					include './../views/customers/tabs/admin-rights.php';
				} else {
					include './../views/customers/tabs/rights.php';
				}
			}elseif( $tab == 'rewards' ){ // Onglet Fidélité
				include './../views/customers/tabs/rewards.php';
			}elseif( $tab == 'goals' ){ // Onglet Objectifs
				include './../views/customers/tabs/goals.php';
			}
		?>
	</div>
<?php if( $tab != 'addresses' ){ ?>
</div>
<?php } ?>

</form>
<?php if( $tab == 'rights' && ($usr['prf_id'] == 1 || $usr['prf_id'] == 5)){
	print '<script src="/admin/js/options/admin-rights.js?'.ADMIN_ASSET.'"></script>';
} ?>
<script><!--
	<?php if( $tab != 'general' ){ ?>
		// Ne pas tenter d'initialiser la carte si on est pas sur l'onglet Général
		function initMap() {
			return;
		}
	<?php } ?>
	// Disable tous les champs/boutons si on accède à cette page en lecture seule
    <?php if( !gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_EDIT') ){ ?>
		$(document).ready(function(){
			$('#tabpanel').find('input, select, textarea').attr('disabled', 'disabled');
			$('#tabpanel a.edit').remove();
			$('input[onclick]').attr('onclick','').unbind('click');
		});

	<?php } ?>
//--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
