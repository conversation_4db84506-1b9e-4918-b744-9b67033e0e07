<?php


ria_mysql_query('delete from prd_cat_hierarchy where cat_tnt_id=1350');


// Reconstruit la hiérarchie pour toutes les catégories enfants
$categories = prd_categories_get( 0, false, 0 );
while( $c = ria_mysql_fetch_array($categories) ){
    prd_categories_hierarchy_add( $c['parent_id'], $c['id'] );
    prd_categories_hierarchy_rebuild( $c['id'] );
}

/*if( isset($config['prd_product_positions_lvl']) && $config['prd_product_positions_lvl'] > 0 ){

    $parent = $config['cat_root'];
    $final_sort = array(); // cat_id => array(prd_ids)
    $final_sort[$parent] = array();

    recursive_search($final_sort, $parent, 0);

    foreach( $final_sort as $cat => $prds ){
        prd_product_positions_set($config['wst_id'], $cat, $prds);
    }
}
else {
    echo "Le tri par position n'est pas activé.";
}*/
