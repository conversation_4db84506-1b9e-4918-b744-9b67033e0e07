<?php

	/**	\file api.php
	 *
	 * 	Ce fichier sert de proxy à l'interface d'administration pour lui permettre d'effectuer des appels à l'api (api.riashop.fr).
	 * 	Son fonction est quasiment identique à ce que fait Axios dans la version Nuxt / VueJS du back-office.
	 *
	 * 	Les appels doivent convenir la variable 'endpoint' qui contient ce qu'il faut envoyer à l'api.
	 *
	 */

	require_once('tenants.inc.php');

	// Vérifie que la paramètre endpoint, est fourni ; ainsi que l'identifiant de tenant
	if( !isset($_GET['method']) || !isset($_GET['endpoint']) || !isset($config['tnt_id']) ){
		exit;
	}

	// Recherche le logtoken correspondant à l'utilisateur actuellement connecté
	$token = tnt_tenants_get_logtoken( $config['tnt_id'] );

	// Construit la requête à effectuer à l'api
	// $query_full_url = 'https://api.riashop.fr';

	$query_full_url = getenv('ENVRIA_API_URL');

	if( isset($config['env_sandbox']) && $config['env_sandbox'] ){
		// TODO
		// $query_full_url = 'http://api.team_api.dev.riashop.fr';
	}

	$query_full_url .= $_GET['endpoint'].'?logtoken='.$token;

	if( $_GET['endpoint'] == '/activity/' ){
		if( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] == PRF_SELLER ){
			$_GET['seller_id'] = $_SESSION['usr_id'];
		}
	}

	// Les paramètres de type GET sont simplement envoyés à l'api
	unset( $_GET['endpoint'] );
	foreach( $_GET as $k=>$v ){
		if( is_array($v) ){
			foreach( $v as $kv => $vv ){
				$query_full_url .= '&'.$k.'[]='.$vv;
			}
		}else{
			$query_full_url .= '&'.$k.'='.$v;
		}
	}

	// Interroge l'api api.riashop.fr via http
	$client = new GuzzleHttp\Client();

	$request = new \GuzzleHttp\Psr7\Request( $_REQUEST['method'], $query_full_url );
	$promise = $client->sendAsync($request)->then(function ($response) {

		// Retourne la réponse reçue de l'api
		print $response->getBody();

	});
	$promise->wait();

	exit;