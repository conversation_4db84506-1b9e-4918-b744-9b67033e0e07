<?php
	require_once( 'tenants.inc.php' );
	require_once( 'users.inc.php' );
	require_once( 'orders.inc.php' );
	require_once( 'products.inc.php' );
	require_once( 'fields.inc.php' );
	require_once( 'tasks.inc.php' );

    class Synchro {
        // Nombres de commandes en attente de synchro
        private $waiting_orders;
        // Nombres de devis en attente de synchro
        private $waiting_quote;
        // Nombres d'utilisateur en attente de synchro
        private $waiting_users;
        // Nombres d'adresses en attente de synchro
        private $waiting_addresses;

        // Configuration du tenant
        private $config;

        // Type de GESCOM utilisé
        private $gescom_type;

        // URL de l'API pour les ORDERS, QUOTES, UTILISATEURS et ADRESSES
        private $api_url = "https://sync.riashop.fr/";
        private $orders_url;
        private $quotes_url;
        private $users_url;
        private $address_url;

        // Informations sur les tâches 
        private $orders_import_task;
        private $quotes_import_task;	
        private $users_import_task;

        // Informations complémentaires pour BIGSHIP
        private $bigship_info;

        public function __construct($config) {
            cfg_variables_load($config);
            $this->config = $config;

            switch($config['tnt_id']){
                case 16: $params_for_users = 1916; break;
                case 13: $params_for_users = array(2819 => 'Oui'); break;
                default: $params_for_users = false; break;
            }

            // Nombre de commandes en attente de synchro
            $this->waiting_orders = mysql_num_rows( ord_orders_get_new_to_import( ($config['tnt_id'] === 3) ? 7 : false) );

            // Nombre de devis en attente de synchro
            $this->waiting_quote = sizeof(ord_orders_get_need_sync( 'head', _STATE_DEVIS ));

            // Nombre d'utilisateur à synchro
            $this->waiting_users = mysql_num_rows(gu_users_toimport_get( $params_for_users ));

            // Nombre d'adresse à synchro
            $this->waiting_addresses = mysql_num_rows(gu_adresses_get_updated());

            // Nom du type de GESCOM utilisé
            $this->gescom_type = $config['sync_global_gescom_type'];

            // URL de l'API pour les ORDERS
            $this->orders_url = $this->api_url .'?logtoken='.$config['token'].'&module=orders&action=get-new';

            // URL de l'API pour les DEVIS
            $this->quote_url = $this->api_url.'?logtoken='.$config['token'].'&module=orders&action=get-updated&detect_type=head&states=28';

            // URL de l'API pour les UTILISATEURS
            $this->users_url = $this->api_url.'?logtoken='.$config['token'].'&module=users&action=get-new';

            // URL de l'API pour les ADRESSES
            $this->address_url = $this->api_url.'?logtoken='.$config['token'].'&module=addresses&action=get-updated';

            // Récupération des informations sur les tâches liées à l'import des commandes
            $this->orders_import_task = mysql_fetch_assoc(tsk_tasks_activation_get(22, $config['tnt_id']));

            // Récupération des informations sur les tâches liées à l'import des devis
            $this->quotes_import_task = mysql_fetch_assoc(tsk_tasks_activation_get(329, $config['tnt_id']));

            // Récupération des informations sur les tâches liées à l'import des utilisateurs
            $this->users_import_task = mysql_fetch_assoc(tsk_tasks_activation_get(49, $config['tnt_id']));

            // Récupération des informations sur les tâches liées à l'import des adresses n
            $this->addresses_import_task = mysql_fetch_assoc(tsk_tasks_activation_get(52, $config['tnt_id']));

            // Nombre total de tâches
            //$total_activities_number = array_sum(array_column(tsk_activities_get(0, true) ?: [], 'count_remaining'));

            // Info supplémentaire TNT_ID = 1 ( BIGSHIP )
            if( $config['tnt_id'] === 1 ) {
            	$rprd = fld_update_requests_get( false, false, false, false, false, true );
            	$rord = ord_orders_get_need_sync(); 
                $this->bigship_info = 'Commandes fournisseurs : <a href="'.$url.'?logtoken='.$tnt['token'].'&module=orders&action=get-updated" target="_blank">'.( $rord ? sizeof($rord) : 0 ).'</a> - Produits modifiés : <a href="'.$url.'?logtoken='.$tnt['token'].'&module=update-requests&action=get" target="_blank">'.( $rprd ? mysql_num_rows($rprd) : 0 ).'</a>';
            }
        }

        public function get_waiting_orders() {
            return $this->waiting_orders;
        }
        
        public function get_waiting_quote() {
            return $this->waiting_quote;
        }
        
        public function get_waiting_users() {
            return $this->waiting_users;
        }
        
        public function get_waiting_addresses() {
            return $this->waiting_addresses;
        }

        public function get_gescom_type() {
            switch($this->gescom_type) {
                case GESCOM_TYPE_SAGE:
                    return 'Sage ' . $this->config['sync_sage_version'] . ' ' . ($this->config['sync_sage_om_enable'] ? 'OM' : 'ODBC');
                case GESCOM_TYPE_ISIALIS:
                    return 'Isialis';
                case GESCOM_TYPE_LIMS:
                    return 'Lims';
                case GESCOM_TYPE_HARMONYS:
                    return 'Harmonys';
                case GESCOM_TYPE_PLATON:
                    return 'Platon';
                case GESCOM_TYPE_APINEGOCE:
                    return 'APINEGOCE';
                case GESCOM_TYPE_G5:
                    return 'G5';
                case GESCOM_TYPE_DIVALTO:
                    return 'Divalto';
                case GESCOM_TYPE_CLISSON:
                    return 'Clisson';
                case GESCOM_TYPE_EEE:
                    return 'EEE';
                case GESCOM_TYPE_DYNAMICS:
                    return 'DynamicsAx';
                case GESCOM_TYPE_SOFI:
                    return 'Sofi';
                case GESCOM_TYPE_WAVESOFT:
                    return 'Wavesoft';
                case GESCOM_TYPE_CEGID:
                    return 'Cegid';
                case GESCOM_TYPE_SAGE_X3:
                    return 'Sage X3';
                case GESCOM_TYPE_DYNAMICS_NAVISION:
                    return 'Dynamics Ax';
                case GESCOM_TYPE_SINERES:
                    return 'Sineres';
                case GESCOM_TYPE_DIVALTO_SQL:
                    return 'Divalto Sql';
                case 18:
                    return 'BATIGEST';
                case 19:
                    return 'Star6000';
                case 20:
                    return 'EBP';
                case 21:
                    return 'Sage 50c';
                default:
                    return 'Inconnue';
            }
        }

        public function get_orders_url() {
            return $this->orders_url;
        }

        public function get_quotes_url() {
            return $this->quotes_url;
        }

        public function get_users_url() {
            return $this->users_url;
        }

        public function get_address_url() {
            return $this->address_url;
        }

        public function is_order_task_active() {
            return $this->orders_import_task['is_active'];
        }

        public function is_quote_task_active() {
            return $this->quotes_import_task['is_active'];
        }
        
        public function is_user_task_active() {
            return $this->users_import_task['is_active'];
        }
        
        public function is_address_task_active() {
            return $this->addresses_import_task['is_active'];
        }
    }