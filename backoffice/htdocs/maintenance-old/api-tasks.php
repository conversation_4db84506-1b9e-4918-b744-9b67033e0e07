<?php
require_once('http.inc.php');
require_once('./tasks.inc.php');

// Récupération des informations du tenant 
$config['token'] = mysql_fetch_array(tnt_tenants_get())['token'];
$config['tnt_id'] = mysql_fetch_array(tnt_tenants_get())['id'];
$config['sync_reboot'] = mysql_fetch_array(tnt_tenants_get())['sync_reboot'];
$config['last-sync'] = mysql_fetch_array(tnt_tenants_get())['last-sync'];

// Vérification de l'authentification et des autorisations
if (!$config['USER_RIASTUDIO'] && $_SESSION['usr_email'] !== '<EMAIL>') {
    http_response_code(403);
    exit;
}

$instance = new Tasks($config);
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Traitement des actions
    if (isset($_POST['action'], $_POST['task_id'])) {
        $taskId = $_POST['task_id'];
        $success = false;

        switch ($_POST['action']) {
            case 'start_task':
                $success = tsk_tasks_activation_set($taskId, $config['tnt_id'], true);
                break;
            case 'stop_task':
                $success = tsk_tasks_activation_set($taskId, $config['tnt_id'], false);
                break;
            case 'force_task':
                try {
                    $success = $instance->force_task_execution($taskId);
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
                break;
        }

        header('Content-Type: application/json');
        if (isset($error)) {
            echo json_encode(['success' => false, 'error' => $error]);
        } else {
            echo json_encode(['success' => $success]);
        }
        exit;
    }
}

$allTasks = $instance->list_all_tasks();
$activeTasks = $instance->list_active_tasks();

header('Content-Type: application/json');
echo json_encode($allTasks);
