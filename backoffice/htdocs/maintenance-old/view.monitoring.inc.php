<?php
require_once('websites.inc.php');

function ria_gescom_get_code($erp_id){
	if (!is_numeric($erp_id) || $erp_id < 0) {
		return '';
	}

	$name = ""; 
	
	switch ($erp_id) {
		case GESCOM_TYPE_SAGE :
			$name = "SAGE";
			break;
		case GESCOM_TYPE_ISIALIS :
			$name = "ISIALIS";
			break;
		case GESCOM_TYPE_LIMS :
			$name = "LIMS";
			break;
		case GESCOM_TYPE_HARMONYS :
			$name = "HARMONYS";
			break;
		case GESCOM_TYPE_PLATON :
			$name = "PLATON";
			break;
		case GESCOM_TYPE_APINEGOCE :
			$name = "APINEGOCE";
			break;
		case GESCOM_TYPE_G5 :
			$name = "G5";
			break;
		case GESCOM_TYPE_DIVALTO :
			$name = "DIVALTO";
			break;
		case GESCOM_TYPE_CLISSON :
			$name = "CLISSON";
			break;
		case GESCOM_TYPE_EEE :
			$name = "EEE";
			break;
		case GESCOM_TYPE_DYNAMICS :
			$name = "DYNAMICS";
			break;
		case GESCOM_TYPE_SOFI :
			$name = "SOFI";
			break;
		case GESCOM_TYPE_WAVESOFT :
			$name = "WAVESOFT";
			break;
		case GESCOM_TYPE_CEGID :
			$name = "CEGID";
			break;
		case GESCOM_TYPE_SAGE_X3 :
			$name = "SAGE_X3";
			break;
		case GESCOM_TYPE_DYNAMICS_NAVISION :
			$name = "DYNAMICS_NAVISION";
			break;
		case GESCOM_TYPE_SINERES :
			$name = "SINERES";
			break;
		case GESCOM_TYPE_DIVALTO_SQL :
			$name = "DIVALTO_SQL";
			break;
		default:
			$name = "";
			break;
	}

	return $name;
}

/** Affiche les lignes du tableau pour gérer les variables de configuration.
 * 	\param $tnt_id Identifiant d'un locataire
 * 	\param $data Tableau contenant toutes les informations nécessaire à l'affichage
 * 	\param $child Optionnel, s'il s'agit d'une variable dépendante d'une autre (par défaut à False)
 * 	\return Le code HTML à afficher
 */
function view_monitoring_line_variable( $tnt_id, $data, $child=false ){
	$override = $data['override'];

	if (isset($_POST['override'][ $data['code'] ])) {
		if (is_array($_POST['override'][ $data['code'] ])) {
			$override = implode(', ', $_POST['override'][ $data['code'] ]);
		}else{
			$override = $_POST['override'][ $data['code'] ];
		}
	}
	ob_start();
	?><tr <?php print $child ? '' : 'data-child-open="'.htmlspecialchars($data['code']).'"'; ?>>
		<td>
			<strong><?php print htmlspecialchars($data['name']); ?></strong>
			<ul>
				<li class="code">Code : <?php print htmlspecialchars($data['code']); ?></li>
				<li>Par défaut : <?php print htmlspecialchars($data['default']); ?></li>
				<li>Type : <?php print fld_types_get_name($data['type']); ?></li>
				<li><?php print(trim($data['override']) != '' ? 'Surchargé' : 'Utilise la valeur par défaut'); ?></li>
			</ul>
		</td>
		<td><?php
			switch ($data['type']) {
				case FLD_TYPE_BOOLEAN_YES_NO:
					?>
						<label for="val-<?php print urlalias($data['code']); ?>-yes">
							<input type="radio" name="override[<?php print $data['code']; ?>]" id="val-<?php print urlalias($data['code']); ?>-yes" <?php print $override == '1' ? 'checked="checked"' : ''; ?> value="1" /> Oui
						</label>
						<label for="val-<?php print urlalias($data['code']); ?>-no">
							<input type="radio" name="override[<?php print $data['code']; ?>]" id="val-<?php print urlalias($data['code']); ?>-no" <?php print $override == '0' ? 'checked="checked"' : ''; ?> value="0" /> Non
						</label>
					<?php
				break;
				default:
					$r_obj = false;
					if ($data['type'] == FLD_TYPE_SELECT_MULTIPLE && $data['cls_id']) {
						$r_obj = view_monitoring_get_objects($tnt_id, $data['cls_id'], 0, array(), $data['obj_cls_id']);
					}

					if ($r_obj && ria_mysql_num_rows($r_obj)) {
						?><div>
							<strong>Valeur(s) en base</strong> : <?php print htmlspecialchars($data['override']); 
						?></div><?php
						$div = round( ria_mysql_num_rows($r_obj) / 2 );
						?><div class="col-cls-obj">
							<input type="hidden" name="override[<?php print $data['code']; ?>][]" value="" /><?php
						
						$i = 0;
						$values = explode(', ', $override);
						while ($obj = ria_mysql_fetch_assoc($r_obj)) {
							if($i == $div){
								?></div><div class="col-cls-obj"><?php
							}

							$selected = '';
							if (in_array($obj['id'], $values)) {
								$selected = 'checked="checked"';
							}

							?><label for="override-<?php print $data['code']; ?>-<?php print $obj['id']; ?>">
								<input type="checkbox" name="override[<?php print $data['code']; ?>][]" id="override-<?php print $data['code']; ?>-<?php print $obj['id']; ?>" value="<?php print $obj['id']; ?>" <?php print $selected; ?> />
								<?php print htmlspecialchars( $obj['name'] ); ?>
							</label><?php
							$i++;
						}

						?></div><?php
					}else{
						?><textarea name="override[<?php print $data['code']; ?>]" cols="50" rows="5"><?php print htmlspecialchars($override); ?></textarea><?php
					}
					break;
			}
			?></td>
		<td>
			<?php if (trim($data['desc']) != '') { ?>
				<img src="/images/help.png" width="16" height="16" title="<?php print htmlspecialchars($data['desc']); ?>" />
			<?php } ?>
		</td>
	</tr><?php
	$content = ob_get_clean();
	return $content;
}

/** Cette fonction permet de retourner une liste d'object selon la classe donnée en paramètre.
 * 	\param $tnt_id Obligatoire, identifiant d'un locataire
 * 	\param $cls_id Obligatoire, classe pour laquelle les objets doivent être retournés
 * 	\param $wst_id Optionnel, identifiant d'un site (selon la classe cette information peut devenir obligatoire)
 * 	\param $where Optionnel, tableau venant compléter le where de la requete (ex. array('year(ord_date) >= 2017, '(ord_masked = 0 or ord_usr_id=0000)') )
 * 	\return Un résultat MySQL contenant : 
 * 					* obj_id : identifiant de l'objet
 * 					* obj_id_1 et obj_id_2 : identifiant complémentaire (ligne de commande par exemple)
 * 					* name : nom de l'objet
 */
function view_monitoring_get_objects( $tnt_id, $cls_id, $wst_id=0, $where=array(), $obj_cls_id=0 ){
	if (!is_numeric($tnt_id) || $tnt_id <= 0) {
		return false;
	}

	if (!is_numeric($cls_id) || $cls_id <= 0) {
		return false;
	}
	
	if (!is_numeric($wst_id) || $wst_id < 0) {
		return false;
	}

	if (!is_array($where)) {
		return false;
	}
	
	$ar_sql = array(
		'select' => '',
		'from' => '',
		'where' => array(),
	);

	switch ($cls_id) {
		case CLS_CTR_MKT:
			$ar_sql['select'] = 'ctr_id as id, ctr_name as name';
			$ar_sql['from'] = 'riashop.ctr_comparators';
			break;
		case CLS_FLD_MODELS:
			$ar_sql['select'] = 'mdl_id as id, mdl_name as name';
			$ar_sql['from'] = 'riashop.fld_models';
			if ($obj_cls_id) {
				$ar_sql['where'][] = 'mdl_cls_id = '.$obj_cls_id;
			}
			break;
		default:
			return false;
	}

	if (count($where)) {
		$ar_sql['where'] = array_merge( $ar_sql['where'], $where );
	}

	$sql = '
		select '.$ar_sql['select'].'
		from '.$ar_sql['from'].'
		where 1
	';

	if (count($ar_sql['where'])) {
		$sql .= ' and '.implode(' and ', $ar_sql['where']);
	}

	$res = ria_mysql_query( $sql );
	if (!$res) {
		die(mysql_error().' => <pre>'.htmlspecialchars($sql).'</pre>');
	}

	return $res;
}
?>
