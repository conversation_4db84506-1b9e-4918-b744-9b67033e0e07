<?php
require_once('http.inc.php');
require_once('./configuration.inc.php');

// Vérification de l'authentification et des autorisations
if (!$config['USER_RIASTUDIO'] && $_SESSION['usr_email'] !== '<EMAIL>') {
    http_403();
    exit;
}

// Récupération des informations du tenant
$rtnt = tnt_tenants_get($config['tnt_id']);
$tenant = ria_mysql_fetch_array($rtnt);

// Initialisation de la classe Configuration
$configInstance = new Configuration($tenant);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'update_variable') {
        $code = $_POST['variable_code'];
        $value = $_POST['variable_value'];

        $result = $configInstance->update_variable($code, $value);

        header('Content-Type: application/json');
        echo json_encode(['success' => $result]);
        exit;
    }
}

// Si aucune action valide n'est spécifiée, renvoyer une erreur
header('HTTP/1.1 400 Bad Request');
echo json_encode(['error' => 'Invalid action']);