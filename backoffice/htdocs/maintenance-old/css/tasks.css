.task-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 10px;
}
.task-table th {
    background-color: #f8f9fa;
    text-align: left;
    padding: 10px;
}
.category-row th, .subcategory-row th {
    cursor: pointer;
    user-select: none;
}
.category-row th {
    background-color: #e9ecef;
    font-size: 1.2em;
    padding: 15px 10px;
}
.subcategory-row th {
    background-color: #f1f3f5;
    font-size: 1.1em;
    padding: 12px 10px 12px 20px;
}
.task-row td {
    background-color: #ffffff;
    padding: 10px;
    border-top: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
}
.task-row td:first-child {
    border-left: 1px solid #dee2e6;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}
.task-row td:last-child {
    border-right: 1px solid #dee2e6;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}
.task-id {
    font-weight: bold;
    color: #495057;
}
.task-name {
    color: #212529;
}
.task-status .status-indicator {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.9em;
}
.status-indicator.active {
    background-color: #d4edda;
    color: #155724;
}
.status-indicator.inactive {
    background-color: #f8d7da;
    color: #721c24;
}
.task-actions .btn {
    padding: 5px 10px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s;
}
.btn-force {
    transition: background-color 0.3s ease;
}

.btn-force:hover {
    background-color: #dc3545 !important; /* Red color on hover */
}
@keyframes fadeBtnColor {
    0% { background-color: #ffc107; }
    100% { background-color: #28a745; }
}

.btn-force.fading {
    animation: fadeBtnColor 30s linear;
    pointer-events: none;
}

.btn-start {
    background-color: #28a745;
    color: white;
}
.btn-stop {
    background-color: #dc3545;
    color: white;
}
.btn-start:hover {
    background-color: #218838;
}
.btn-stop:hover {
    background-color: #c82333;
}
.hidden {
    display: none;
}
.toggle-icon::before {
    content: '▶';
    margin-right: 10px;
}
.expanded .toggle-icon::before {
    content: '▼';
}
.spinner {
border: 4px solid #f3f3f3;
border-top: 4px solid #3498db;
border-radius: 50%;
width: 30px;
height: 30px;
animation: spin 1s linear infinite;
}

@keyframes spin {
0% { transform: rotate(0deg); }
100% { transform: rotate(360deg); }
}