<?php

require_once( 'tenants.inc.php' );
require_once( 'strings.inc.php' );
require_once( 'users.inc.php' );
require_once( 'orders.inc.php' );
require_once( 'products.inc.php' );
require_once( 'fields.inc.php' );
require_once( 'tasks.inc.php' );

class Tasks {
    const SYNC_FOLDER = '/tmp/';

    private $tnt_id = 1;
    private $current_tasks_activated;

    public function __construct($config) {
        // On définit le tnt_id pour les différentes méthodes.
        $this->tnt_id = $config['tnt_id'];

        // Vérification de l'existence du dossier de synchro.
        $this->create_sync_folder();

        // Liste les tâches assigné au connecteur.
        //var_dump($this->verify_task_assignment());

        // Lister les tâches qui sont actuellement activé 
        //var_dump($this->list_active_tasks());

        // Lister toute les tâches existantes
        //var_dump($this->list_all_tasks()); // dernier check

        // Activer une liste de tâches
        //var_dump($this->activate_tasks([11,12,13,14]));

        // Forcer une tâche
        //var_dump($this->force_task_execution(5));

        // Test du fichier XML custom de config
        //var_dump($this->create_custom_config_file(['param' => 'value']));
    }

    // Fonction pour initialiser le dossier de synchro pour les fichiers de "commande" au connecteur.
    private function create_sync_folder() {
        if (!is_dir(self::SYNC_FOLDER)) {
            if (is_writable(self::SYNC_FOLDER)) {
                $createFolder = mkdir(self::SYNC_FOLDER, 0755, true);

                if (!$createFolder) {
                    return false;
                }
                return true;           
            }
            die("Error: No write permissions on " . self::SYNC_FOLDER);
        }
        return true;
	}

    // Vérifier si il existe actuellement une tâche assigné.
    public function verify_task_assignment() {
        return array_filter(scandir('.'), function($file) { //'/var/www/sync.riashop.fr/reports/';
            return strpos($file, 'task-') === 0;
        });
    }

    // Liste des id's des tâches actives
    public function list_active_tasks() {
        $ractived = tsk_tasks_activation_get( 0, $this->tnt_id, true);

        while( $actived = mysql_fetch_array($ractived) ){
            // Liste des ID's des tâches actives
            $this->current_tasks_activated[] = $actived['tsk_id'];
        }
        return $this->current_tasks_activated;
    }

    // Liste toute les tâches existantes
    public function list_all_tasks() {
        $groups = array();
        $categories = $this->get_categories();

        foreach ($categories as $category) {
            $subcategories = $this->get_subcategories($category['id']);

            foreach ($subcategories as $subcategory) {
                $tasks = $this->get_tasks_for_subcategory($subcategory['id']);

                if (!empty($tasks)) {
                    foreach($tasks as $id => $task_details) {
                        $task_details = ria_mysql_fetch_assoc(tsk_activities_get($id));
                        $tasks[$id]['count_total'] = $task_details['count_total'];
                        $tasks[$id]['count_remaining'] = $task_details['count_remaining'];
                        $tasks[$id]['count_fail'] = $task_details['count_fail'];
                        $tasks[$id]['date_start'] = $task_details['date_start'];
                        $tasks[$id]['date_end'] = $task_details['date_end'];
                    }

                    $groups[$category['name']][$subcategory['name']] = $tasks;
                }
            }
        }
        return $groups;
    }

    // Liste des catégories principal pour les différentes tâches
    private function get_categories() {
        $categories = [];
        $rtasks = tsk_tasks_get(null, '', 0);

        while ($cat = mysql_fetch_assoc($rtasks)) {
            $categories[] = $cat;
        }

        return $categories;
    }

    // Liste des sous-catégories de tâches
    private function get_subcategories($category_id) {
        $subcategories = [];
        $rtasks2 = tsk_tasks_get(null, '', $category_id);

        while ($subcategory = mysql_fetch_assoc($rtasks2)) {
            $subcategories[] = $subcategory;
        }

        return $subcategories;
    }

    // Liste de toute les tâches existantes
    private function get_tasks_for_subcategory($subcategory_id) {
        $tasks = [];
        $rtasks3 = tsk_tasks_activation_get(0, $this->tnt_id, null, $subcategory_id);
        while ($task = mysql_fetch_array($rtasks3)) {
            $tasks[$task['tsk_id']] = [
                'name' => $task['tsk_name'],
                'active' => $task['is_active']
            ];
        }

        return $tasks;
    }

    // Activation des tâches
    public function activate_tasks($tasks_to_activate) {
        // Liste des tâches actuellement activées
		$current_active = $this->list_active_tasks();

        // création du différenciel entre les tâches actives et celles à activer
        // desactivation des tâches actives qui ne sont plus dans la liste à activer
        // activation des tâches qui ne sont pas déjà actives
		$desactived = array_diff( $current_active, $tasks_to_activate );
		$actived = array_diff( $tasks_to_activate, $current_active );

		// création du fichier
        $xml = '<?xml version="1.0" encoding="utf-8"?><tasks>';

        foreach( $desactived as $td )
            $xml .= '<task id="'.$td.'" active="0" />';

        foreach( $actived as $ta )
            $xml .= '<task id="'.$ta.'" active="1" />';

        $xml .= '</tasks>';

        var_dump(self::SYNC_FOLDER . 'task-' . $_GET['tnt_id'] . '-' . time() . '.xml');

        if(file_put_contents( self::SYNC_FOLDER . 'task-' . $_GET['tnt_id'] . '-' . time() . '.xml', $xml ) === false)
            die('Impossible de créer le fichier d\'activation de tâches.');

        return true;
    }

    // Forcer l'execution d'une tâche
    public function force_task_execution($taskId) {
        $task_path = self::SYNC_FOLDER.'forcetsk-' .$this->tnt_id . '-'.time().'.log';

		if(mysql_fetch_assoc(tsk_tasks_activation_get($taskId, $this->tnt_id, true))['is_active'] === '1'){
			if( @file_put_contents($task_path ,$taskId) === 1 ) return true;
            else $error = "Impossible de créer le fichier contenant la tâche à traiter dans le chemin : $task_path";
		}
        else $error = 'Impossible de forcer cette tâche, elle n\'est pas active pour ce locataire.';

        
        header('Content-Type: application/json');
        die(json_encode(['success' => false, 'error' => $error]));
    }

    // Assigner des paramètres de synchronisation custom au connecteur
    public function create_custom_config_file($params) {
        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?><params></params>');

        foreach ($params as $name => $value) {
            if (strpos($name, ' ') !== false)
                return 'Le nom de paramètre ne peut pas contenir d\'espaces.';

            $param = $xml->addChild('param', htmlspecialchars($value));
            $param->addAttribute('name', htmlspecialchars($name));
        }
        return file_put_contents(self::SYNC_FOLDER . 'settings-' . $this->tnt_id . '-' . time() . '.xml', $xml->asXML());
    }
}