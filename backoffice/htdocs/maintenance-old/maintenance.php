<?php
/**
 * \file maintenance.php
 * Page de maintenance
 */

// Debug
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);

require_once('http.inc.php');
require_once('synchro.inc.php');

// Vérification de l'authentification et des autorisations
if (!$config['USER_RIASTUDIO'] && $_SESSION['usr_email'] !== '<EMAIL>') {
    http_403();
    exit;
}

// Configuration de la page
define('ADMIN_PAGE_TITLE', _('Page de maintenance') . ' - ' . _('Maintenance'));
Breadcrumbs::root(_('Accueil'), '/admin/index.php')->push(_('Page de maintenance'));

// Récupération des informations du tenant 
$config['token'] = mysql_fetch_array(tnt_tenants_get())['token'];
$config['tnt_id'] = mysql_fetch_array(tnt_tenants_get())['id'];
$config['sync_reboot'] = mysql_fetch_array(tnt_tenants_get())['sync_reboot'];
$config['last-sync'] = mysql_fetch_array(tnt_tenants_get())['last-sync'];

// Initialisation de la classe Synchro
$synchro = new Synchro($config);

// En-tête de la page
require_once('admin/skin/header.inc.php');

// echo "<pre>";
// echo print_r($config,true);
// echo "</pre>";
?>

<link rel="stylesheet" href="styles.css">

<div class="container">
    <div class="notice notice-super-admin">
        <?php echo _("Cette interface n'est visible que par les super-administrateurs."); ?>
    </div>

    <h2><?php echo _("Maintenance"); ?></h2>

    <div class="synchro-info">
        <h3><?php echo _("Informations de synchronisation"); ?></h3>
        <table>
            <tr>
                <th><?php echo _("Type de GESCOM"); ?></th>
                <td id="gescom-type"><?php echo htmlspecialchars($synchro->get_gescom_type()); ?></td>
            </tr>
            <tr>
                <th><?php echo _("Token"); ?></th>
                <td id="gescom-type"><?php echo $config['token']; ?></td>
            </tr>
            <tr>
                <th><?php echo _("Statut de la synchro"); ?></th>
                <td id="sync-reboot-container">
                    <span id="sync-reboot"><?php echo $config['sync_reboot'] < 0 ? 'Arrêt demandé' : 'Redémarrage demandé'; ?></span>
                </td>
            </tr>
            <tr>
                <th><?php echo _("Date de la dernière synchro"); ?></th>
                <td id="last-sync-container">
                    <span id="last-sync"><?php echo $config['last-sync']; ?></span>
                </td>
            </tr>
            <tr>
                <th><?php echo _("Commandes en attente"); ?></th>
                <td id="waiting-orders-container">
                    <span id="waiting-orders" class="count-container">
                        <span class="count"><?php echo $synchro->get_waiting_orders(); ?></span>
                    </span>
                    <?php if ($synchro->is_order_task_active()): ?>
                        <span class="task-active">(<?php echo _("Tâche active"); ?>)</span>
                    <?php endif; ?>
                </td>
            </tr>

            <tr>
                <th><?php echo _("Devis en attente");?></th>
                <td id="waiting-quote-container">
                    <span id="waiting-quote" class="count-container">
                        <span class="count"><?php echo $synchro->get_waiting_quote();?></span>
                    </span>
                    <?php if ($synchro->is_quote_task_active()):?>
                        <span class="task-active">(<?php echo _("Tâche active");?>)</span>
                    <?php endif;?>
                </td>
            </tr>

            <tr>
                <th><?php echo _("Utilisateurs en attente");?></th>
                <td id="waiting-users-container">
                    <span id="waiting-users" class="count-container">
                        <span class="count"><?php echo $synchro->get_waiting_users();?></span>
                    </span>
                    <?php if ($synchro->is_user_task_active()):?>
                        <span class="task-active">(<?php echo _("Tâche active");?>)</span>
                    <?php endif;?>
                </td>
            </tr>

            <tr>
                <th><?php echo _("Adresses en attente");?></th>
                <td id="waiting-addresses-container">
                    <span id="waiting-addresses" class="count-container">
                        <span class="count"><?php echo $synchro->get_waiting_addresses();?></span>
                    </span>
                    <?php if ($synchro->is_address_task_active()):?>
                        <span class="task-active">(<?php echo _("Tâche active");?>)</span>
                    <?php endif;?>
                </td>
            </tr>
        </table>
        <div id="update-indicator" style="text-align: right; font-size: 0.8em; color: #888; margin-top: 10px;"></div>
    </div>

    <div class="notice" style="margin-top: 15px">
        <p>Voici les sections qui permettent de modifier les variables ainsi que les tâches des connecteurs</p>
    </div>

    <div class="button-container">
        <?php
        $buttons = [
            'tasks.php' => _("Gestion des tâches de synchronisation"),
            'configuration.view.php' => _("Gestion des variables de synchronisation")
        ];

        foreach ($buttons as $url => $label) {
            echo "<a href='{$url}?tnt_id={$config['tnt_id']}' class='button button-primary'>{$label}</a>";
        }
        ?>
    </div>
</div>


<script>
function updateSynchroInfo() {
    const indicator = document.getElementById('update-indicator');
    indicator.textContent = '<?php echo _("Mise à jour en cours..."); ?>';

    addSpinner('sync-reboot-container');
    addSpinner('last-sync-container');
    addSpinner('waiting-orders-container');
    addSpinner('waiting-quote-container');
    addSpinner('waiting-users-container');
    addSpinner('waiting-addresses-container');
    fetch('api-synchro.php')
        .then(response => response.json())
        .then(data => {
            updateElementWithFade('sync-reboot', data.sync_reboot < 0 ? 'Arrêt demandé' : 'Redémarrage demandé');
            updateElementWithFade('last-sync', data.last_sync);
            updateElementWithFade('waiting-orders .count', data.waiting_orders.count);
            updateElementWithFade('waiting-quote .count', data.waiting_quote.count);
            updateElementWithFade('waiting-users .count', data.waiting_users.count);
            updateElementWithFade('waiting-addresses .count', data.waiting_addresses.count);

            // Mettre à jour les indicateurs de tâche active
            updateTaskActiveIndicator('waiting-orders', data.waiting_orders.active);
            updateTaskActiveIndicator('waiting-quote', data.waiting_quote.active);
            updateTaskActiveIndicator('waiting-users', data.waiting_users.active);
            updateTaskActiveIndicator('waiting-addresses', data.waiting_addresses.active);
            const now = new Date();
            indicator.textContent = '<?php echo _("Dernière mise à jour :"); ?> ' + now.toLocaleTimeString();
        })
        .catch(error => {
            console.error('Error:', error);
            indicator.textContent = '<?php echo _("Erreur lors de la mise à jour"); ?>';
        })
        .finally(() => {
            // Supprimer tous les spinners
            removeAllSpinners();
        });
}

function addSpinner(containerId) {
    const container = document.getElementById(containerId);
    if (container && !container.querySelector('.spinner')) {
        const spinner = document.createElement('div');
        spinner.className = 'spinner';
        const countContainer = container.querySelector('.count-container');
        if (countContainer) {
            countContainer.appendChild(spinner);
        } else {
            container.appendChild(spinner);
        }
    }
}


function removeAllSpinners() {
    document.querySelectorAll('.spinner').forEach(spinner => spinner.remove());
}

function updateElementWithFade(elementId, newValue) {
    const element = document.getElementById(elementId) || document.querySelector(elementId);
    if (element) {
        element.textContent = newValue;
        element.classList.add('fade-update');
        setTimeout(() => element.classList.remove('fade-update'), 500);
    }
}

function updateTaskActiveIndicator(rowId, isActive) {
    const row = document.getElementById(rowId);
    if (!row) {
        console.error(`Row with id '${rowId}' not found`);
        return;
    }
    let taskActiveSpan = row.querySelector('.task-active');
    const tdElement = row.querySelector('td');

    if (!tdElement) {
        return;
    }

    if (isActive) {
        if (!taskActiveSpan) {
            taskActiveSpan = document.createElement('span');
            taskActiveSpan.className = 'task-active';
            taskActiveSpan.textContent = '(<?php echo _("Tâche active"); ?>)';
            tdElement.appendChild(taskActiveSpan);
        }
    } else if (taskActiveSpan) {
        taskActiveSpan.remove();
    }
}


// Lancer la mise à jour toutes les 30 secondes
setInterval(updateSynchroInfo, 30000);

// Lancer une première mise à jour immédiatement
updateSynchroInfo();
</script>


<?php
require_once('admin/skin/footer.inc.php');
?>
