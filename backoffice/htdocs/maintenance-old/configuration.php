<?php
	// Enable error reporting for debugging (remove in production)
	ini_set('display_errors', 1);
	ini_set('display_startup_errors', 1);
	error_reporting(E_ALL);
    
	require_once('tenants.inc.php');
	require_once('websites.inc.php');
	require_once('cfg.variables.inc.php');
	require_once('view.monitoring.inc.php');
	

	$param_edit = '';
	$system = $sync_id = $filter = null;
	$tnt_id = $wst_id = 0;
	$tenant = $website = $only_override = false;

	{ // Contrôle des paramètres 
		if (isset($_GET['tnt_id']) && is_numeric($_GET['tnt_id']) && tnt_tenants_exists($_GET['tnt_id'])) {
			$tnt_id = $_GET['tnt_id'];
			$tenant = ria_mysql_fetch_assoc( tnt_tenants_get($tnt_id) );
			$param_edit .= (trim($param_edit) != '' ? '&' : '?').'tnt_id='.$tnt_id;
		}
		
		if (isset($_GET['wst_id'])) {
			if ((is_numeric($_GET['wst_id']) && wst_websites_exists($_GET['wst_id'],$tnt_id)) || in_array($_GET['wst_id'], array('sync', 'yuto'))) {
				$wst_id = $_GET['wst_id'];
				$param_edit .= (trim($param_edit) != '' ? '&' : '?').'wst_id='.$wst_id;

				if (!in_array($wst_id, array('sync', 'yuto'))) {
					$website = ria_mysql_fetch_assoc( wst_websites_get($wst_id) );
				}
			}
		}

		if (isset($_GET['only_override']) && $_GET['only_override'] == 'on') {
			$only_override = true;
			$param_edit .= (trim($param_edit) != '' ? '&' : '?'). 'only_override=on';
		}

		if (isset($_GET['filter']) && trim($_GET['filter']) != "") {
			$filter = $_GET['filter'];
			$param_edit .= (trim($param_edit) != '' ? '&' : '?'). 'filter='.$_GET['filter'];
		}
		
		if ($tenant['sync_update']) {
			$sync_id = 0;

			$cfg_sync_id = cfg_overrides_get_value('sync_global_gescom_type', null, 0, $tnt_id);
			if (is_numeric($cfg_sync_id) && $cfg_sync_id) {
				$sync_id = $cfg_sync_id;
			}
		}
	}

	{ // Sauvegarde des variables de configuration
		if (isset($_POST['save-override'])) {
			if (isset($_POST['override']) && is_array($_POST['override'])) {
				$save_wst_id = 0;

				if (is_numeric($wst_id) && $wst_id) {
					$save_wst_id = $wst_id;
				}elseif ($wst_id === 'yuto') {
					$r_wst_yuto = wst_websites_get(0, false, $tnt_id, false, _WST_TYPE_FDV);
					if ($r_wst_yuto && ria_mysql_num_rows($r_wst_yuto)) {
						$wst_yuto = ria_mysql_fetch_assoc($r_wst_yuto);
						$save_wst_id = $wst_yuto['id'];
					}
				}
				
				$r_cfg_variable = cfg_variables_get('',array(),false,$system);
				if ($r_cfg_variable) {
					while ($cfg_variable = ria_mysql_fetch_assoc($r_cfg_variable)) {
						if (!array_key_exists($cfg_variable['code'], $_POST['override'])) {
							continue;
						}
						
						$value = $_POST['override'][ $cfg_variable['code'] ];
						if (is_array($value)) {
							$tmp_val = '';
							
							foreach ($value as $temp) {
								if (trim($temp) != '') {
									$tmp_val .= (trim($tmp_val) != '' ? ', ' : '').$temp;
								}
							}
							
							$value = $tmp_val;
						}
						
						if (!cfg_overrides_exists($cfg_variable['code'], $tnt_id, $save_wst_id) && trim($value) == trim($cfg_variable['default'])) {
							continue;
						}
						
						if (!cfg_overrides_set_value($cfg_variable['code'], $value, $save_wst_id, 0, $tnt_id)) {
							$error = "Une erreur inattendue s'est produite lors de l'enregistrement de la variable \"".htmlspecialchars($cfg_variable['code'])."\".";
							break;
						}
					}
				}
			}

			if (!isset($error)) {
				// Supprime le cache sur les variables de configuration
				$cfg_memcached = $memcached->get('config:'.$tnt_id.':'.$wst_id);
				if ($memcached->getResultCode() != Memcached::RES_NOTFOUND) {
					$memcached->delete('config:'.$tnt_id.':'.$wst_id);
				}
				
				$_SESSION['save-variables-success'] = true;
				header('Location: /maintenance/configuration.php'.$param_edit);
				exit;
			}
		}
	}
	
	$ar_variables = array();

 { // Charge les variables de configuration selon les paramètres
     if ($tnt_id && $wst_id) {
         // Récupère toutes les variables de configuration
         $r_cfg_variable = cfg_variables_get('',array(),false,$system);

         if ($r_cfg_variable) {
             $sync_code = strtolower2(ria_gescom_get_code($sync_id));

             while ($cfg_variable = ria_mysql_fetch_assoc($r_cfg_variable)) {
                 // Filtre les variables si un filtre est défini
                 if ($filter !== null && !strstr($cfg_variable['code'], $filter)) {
                     continue;
                 }

                 // Ignore les variables commençant par 'admin_'
                 if (substr($cfg_variable['code'], 0, 6) == 'admin_') {
                     continue;
                 }

                 // Gère les variables de synchronisation
                 if ($wst_id != "sync") {
                     if (preg_match('/^(sync_)/', $cfg_variable['code'])) {
                         continue;
                     }
                 } else {
                     if (!preg_match('/^(sync_global_|sync_' . $sync_code . '_)/', $cfg_variable['code'])) {
                         continue;
                     }
                 }

                 // Gère les variables Yuto
                 if ($wst_id != "yuto") {
                     if (preg_match('/^(device_|fdv_)/', $cfg_variable['code'])) {
                         continue;
                     }
                 } else {
                     if (!preg_match('/^(device_|fdv_)/', $cfg_variable['code'])) {
                         continue;
                     }
                 }

                 // Ajoute la variable au tableau des variables
                 $ar_variables[$cfg_variable['code']] = array_merge($cfg_variable, array('override' => $cfg_variable['default'], 'childs' => array()));
             }
         }

         // Gère le cas spécial pour Yuto
         $wst_override = $wst_id;
         if ($wst_id === 'yuto') {
             $r_wst_yuto = wst_websites_get(0, false, $tnt_id, false, _WST_TYPE_FDV);
             if ($r_wst_yuto && ria_mysql_num_rows($r_wst_yuto)) {
                 $wst_yuto = ria_mysql_fetch_assoc($r_wst_yuto);
                 $wst_override = $wst_yuto['id'];
             }
         }

         // Récupère les surcharges de configuration
         $r_cfg_override = cfg_overrides_get((in_array($wst_override, array('sync', 'yuto')) ? 0 : $wst_override), array(), '', 0, $tnt_id);
         if (!$r_cfg_override) {
             $ar_variables = array();
         } else {
             while ($cfg_override = ria_mysql_fetch_assoc($r_cfg_override)) {
                 if (array_key_exists($cfg_override['code'], $ar_variables)) {
                     $ar_variables[$cfg_override['code']]['override'] = $cfg_override['value'];
                 }
             }

             // Si on ne veut que les surcharges, on supprime les variables non surchargées
             if ($only_override) {
                 foreach ($ar_variables as $key => $data) {
                     if (trim($data['override']) === "") {
                         unset($ar_variables[ $key ]);
                     }
                 }
             }
         }

         // Organise les variables en arborescence (parent-enfant)
         foreach ($ar_variables as $key => $data) {
             if (!array_key_exists($data['parent_code'], $ar_variables)) {
                 continue;
             }

             if (trim($data['parent_code']) != "") {
                 $ar_variables[ $data['parent_code'] ]['childs'][] = $data;
                 unset($ar_variables[ $key ]);
             }
         }
     }
 }
	
	if (!IS_AJAX) {
		define('PAGE_TITLE', 'Variables de configuration - Configuration');
		require_once('admin/skin/header.inc.php');
	}
?>
<div>
    <p>
        <button class="button button-primary" onclick="window.location.href='maintenance.php'">Retour au dashboard</button>
    </p>
	<h2>Variables de configuration</h2>
	<p>Gestion des variables de configuration (Site / Yuto / Synchronisation).</p>
	<form id="form-config-cfg" action="" method="get">
		<div>
			<select name="tnt_id" id="tnt_id" hidden>
				<option value="<?php echo $tnt_id; ?>" default></option>
			</select>
		</div>

		<?php if ($tnt_id) { ?>
			<div>
				<select name="wst_id" id="wst_id">
					<option value="">Choisissez un site ou un logiciel</option>
					<?php
						$r_wst = wst_websites_get(0, array('name'=>'asc'), $tnt_id);
						while ($wst = mysql_fetch_assoc($r_wst)) {
							$selected = "";
							if ($wst_id == $wst['id']) {
								$selected = 'selected="selected"';
							}

							?><option value="<?php print $wst['id']; ?>" <?php print $selected; ?>><?php print htmlspecialchars($wst['name']); ?></option><?php
						}

						if ($sync_id !== null) {
							$selected = "";
							if ($wst_id === 'sync') {
								$selected = 'selected="selected"';
							}

							?><option value="sync" <?php print $selected; ?>>Logiciel de synchronisation</option><?php
						}

						if (wst_websites_is_fdv_app($tnt_id)) {
							$selected = "";
							if ($wst_id === 'yuto') {
								$selected = 'selected="selected"';
							}

							?><option value="yuto" <?php print $selected; ?>>Application Yuto</option><?php
						}
					?>
				</select>
			</div>
		<?php } ?>
		
		<?php if($tnt_id && $wst_id) { ?>
			<div>
				<label for="only_override"><strong>Ne voir que les variables surchargées</strong> :</label>
				<input type="checkbox" name="only_override" id="only_override" <?php print $only_override ? 'checked="checked"' : ''; ?> />
			</div>
		<?php } ?>
	</form>

	<form id="form-list-override" action="./configuration.php<?php print htmlspecialchars($param_edit); ?>" method="post">
		<?php
			if (isset($error)) {
				print '<div class="error">'.nl2br( $error ).'</div>';
			} elseif (isset($_SESSION['save-variables-success'])) {
				print '<div class="success">Les variables ont bien été mise à jour.</div>';
				unset($_SESSION['save-variables-success']);
			}
			
			if ($tnt_id && $wst_id) {
				$ar_variables = array_msort( $ar_variables, array('name'=>SORT_ASC) );

				?><table id="list-cfg-override" class="checklist" cellspacing="0" cellpadding="0">
					<caption>Variables de configuration</caption>
					<col width="300" /><col width="400" /><col width="16" />
					<thead>
						<tr>
							<th>Variable</th>
							<th colspan="2" align="right">
								<label for="filter">Filtre (code) : 
									<input type="text" name="filter" id="filter" value="<?php print htmlspecialchars($filter); ?>" />
								</label>
							</th>
						</tr>
					</thead>
					<tfoot>
						<tr>
							<td colspan="3">
								<input type="submit" name="save-override" value="Enregistrer" />
							</td>
						</tr>
					</tfoot>
					<tbody><?php
						if (!count($ar_variables)) {
							?><tr><td colspan="3">Aucune variable ne correspond à vos critères</td></tr><?php
						}else{
							foreach ($ar_variables as $code => $data) {
								print view_monitoring_line_variable($tnt_id, $data);

								if (count($data['childs'])) {
									$hidden = '';
									if ($data['type'] == FLD_TYPE_BOOLEAN_YES_NO && !in_array($data['override'], array('1', 'oui', 'Oui'))) {
										$hidden = 'hidden';
									}elseif (trim($data['override']) == "") {
										$hidden = 'hidden';
									}

									?><tr data-child-id="<?php print htmlspecialchars($data['code']); ?>" class="tr-list-child <?php print $hidden; ?>"><td colspan="3">
											<table class="checklist table-child-variable" cellspacing="0" cellpadding="0">
											<caption>Variables de configuration</caption>
											<col width="300" /><col width="400" /><col width="16" />
											<thead>
												<tr>
													<th colspan="3">Variables complémentaires</th>
												</tr>
											</thead>
											<tbody><?php
												foreach ($data['childs'] as $code => $value) {
													print view_monitoring_line_variable($tnt_id, $value, true);
												}
											?></tbody>
										</table>
									</td></tr><?php
								}
							}
						}
					?></tbody>
				</table><?php
			}
		?>
	</form>
</div>
<?php 
	if (!IS_AJAX) { 
		?>
			<div class="reloadPageAjax"></div>
			<script>
                $(document).ready(
                    function(){

                    }
                ).on(
                    'change', '[name="tnt_id"], [name="wst_id"], [name="only_override"]', function(){
                        reloadVariablePage(false, true);
                    }
                ).on(
                    'blur', '[name="filter"]', function(){
                        reloadVariablePage(false, true);
                    }
                ).on(
                    'keypress', '[name="filter"]', function(event){
                        if (event.keyCode==13) {
                            reloadVariablePage(false, true);
                            return false;
                        }
                    }
                ).on(
                    'click', '[data-child-open] [type="radio"]', function(){
                        var child = $(this).parents('tr').data('child-open');
                        var val = $(this).val();
                        
                        displayChildOption( child, (val == '1' ? true : false) );
                    }
                ).on(
                    'blur', 'textarea', function(){
                        var child = $(this).parents('tr').data('child-open');
                        var val = $(this).val();
                        
                        displayChildOption( child, ($.trim(val) != "" ? true : false) );
                    }
                );

                if (!$.browser.msie) {
                    window.onpopstate = function(event) {
                        var query = '';
                        
                        if (event.state!==null) {
                            query = event.state;
                        }
                        
                        reloadVariablePage(query, false);
                    };
                }

                function displayChildOption(trChild, show){
                    if (show) {
                        $('tr[data-child-id="' + trChild + '"]').show();
                    }else{
                        $('tr[data-child-id="' + trChild + '"]').hide();
                    }
                }

                function reloadVariablePage(historyData, pushState) {
                    $('.reloadPageAjax').show();

                    var dataAjax = '';

                    if (historyData !== false) {
                        dataAjax = historyData;
                    }else{
                        dataAjax = 'tnt_id=' + $('#tnt_id').val();

                        if ($('#wst_id').length) {
                            dataAjax += '&wst_id=' + $('#wst_id').val();
                        }
                        
                        if ($('#only_override:checked').length) {
                            dataAjax += '&only_override=on';
                        }
                        
                        if ($('[name="filter"]').length) {
                            dataAjax += '&filter=' + $('[name="filter"]').val();
                        }
                    }
                    
                    $.ajax({
                        url 	 : './configuration.php',
                        data 	 : dataAjax,
                        cache	 : false,
                        method	 : 'get',
                        dataType : 'text'
                    }).done(function (data) {
                        if ($(data).find('[id="form-config-cfg"]').length) {
                            $('[id="form-config-cfg"]').html( $(data).find('[id="form-config-cfg"]').html() );
                        }
                        
                        if ($(data).find('[id="form-list-override"]').length) {
                            $('[id="form-list-override"]').html( $(data).find('[id="form-list-override"]').html() );
                            $('[id="form-list-override"]').attr( 'action', $(data).find('[id="form-list-override"]').attr('action') );
                        }

                        if(!$.browser.msie && pushState){
                            window.history.pushState(dataAjax, "page", window.location.pathname + '?' + dataAjax);
                        }

                        $('.reloadPageAjax').hide();
                    });
                }
            </script>
		<?php
		require_once('admin/skin/footer.inc.php');
	}