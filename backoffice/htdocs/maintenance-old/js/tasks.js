function toggleSpinner(show) {
    let spinner = document.getElementById('data-spinner');
    if (!spinner) {
        spinner = document.createElement('div');
        spinner.id = 'data-spinner';
        spinner.innerHTML = '<div class="spinner"></div>';
        spinner.style.position = 'fixed';
        spinner.style.top = '50%';
        spinner.style.left = '50%';
        spinner.style.transform = 'translate(-50%, -50%)';
        spinner.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
        spinner.style.padding = '20px';
        spinner.style.borderRadius = '5px';
        spinner.style.display = 'none';
        document.body.appendChild(spinner);
    }
    spinner.style.display = show ? 'block' : 'none';
}
function updateTaskData() {
    const updateIndicator = document.getElementById('update-indicator') || createUpdateIndicator();
    updateIndicator.textContent = 'Mise à jour en cours...';
    toggleSpinner(true);
    fetch('api-tasks.php')
        .then(response => response.json())
        .then(data => {
            Object.keys(data).forEach(category => {
                Object.keys(data[category]).forEach(subcategory => {
                    Object.keys(data[category][subcategory]).forEach(taskId => {
                        const task = data[category][subcategory][taskId];
                        updateTaskRow(taskId, task);
                    });
                });
            });

            const now = new Date();
            updateIndicator.textContent = 'Dernière mise à jour :' + now.toLocaleTimeString();
            toggleSpinner(false);
        })
        .catch(error => {
            console.error('Error:', error);
            updateIndicator.textContent = 'Erreur lors de la mise à jour';
            toggleSpinner(false);
        });
}


function createUpdateIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'update-indicator';
    indicator.style.cssText = 'text-align: right; font-size: 0.8em; color: #888; margin-top: 10px;';
    document.querySelector('.container').appendChild(indicator);
    return indicator;
}

function updateTaskRow(taskId, task) {
    console.log(`Updating task ${taskId}:`, task);
    const rows = document.querySelectorAll(`tr[data-task-id="${taskId}"]`);
    if (rows.length > 0) {
        console.log(`Found ${rows.length} row(s) for task ${taskId}`);
        rows.forEach(row => {
            row.querySelector('.task-total').textContent = task.count_total;
            row.querySelector('.task-remaining').textContent = task.count_remaining;
            row.querySelector('.task-error').textContent = task.count_fail;
            row.querySelector('.task-last-run').textContent = task.date_start;

            // Calculate execution time
            const startDate = new Date(task.date_start);
            const endDate = new Date(task.date_end);
            const executionTimeInSeconds = Math.round((endDate - startDate) / 1000);
            row.querySelector('.task-execution-time').textContent = `${executionTimeInSeconds}s`;

            const statusIndicator = row.querySelector(`.status-indicator-${taskId}`);
            if (statusIndicator) {
                console.log(`Status indicator found for task ${taskId}. Active: ${task.active}`);
                statusIndicator.textContent = task.active == '1' ? 'Actif' : 'Inactif';
                statusIndicator.className = `status-indicator status-indicator-${taskId} ${task.active == '1' ? 'active' : 'inactive'}`;
            } else {
                console.log(`Status indicator not found for task ${taskId}`);
            }

            updateActionButton(row, task.active == '1');
        });
    } else {
        console.log(`No rows found for task ${taskId}`);
    }
}




function updateActionButton(row, isActive) {
    const actionForm = row.querySelector('.task-action-form');
    const actionInput = actionForm.querySelector('input[name="action"]');
    const actionButton = actionForm.querySelector('button');

    if (isActive) {
        actionInput.value = 'stop_task';
        actionButton.textContent = 'Arrêter';
        actionButton.className = 'btn btn-stop';
    } else {
        actionInput.value = 'start_task';
        actionButton.textContent = 'Démarrer';
        actionButton.className = 'btn btn-start';
    }
}

// Update task data every 5 seconds
setInterval(updateTaskData, 50000);

// Initial update
updateTaskData();
document.querySelectorAll('.task-action-form').forEach(form => {
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const action = this.querySelector('input[name="action"]').value;
        let confirmMessage = '';

        if (action === 'stop_task') {
            confirmMessage = 'Êtes-vous sûr de vouloir arrêter cette tâche ?';
        } else if (action === 'force_task') {
            confirmMessage = 'Êtes-vous sûr de vouloir forcer l\'exécution de cette tâche ?';
        }

        if (confirmMessage && !confirm(confirmMessage)) {
            return;
        }

        fetch('api-tasks.php', {
            method: 'POST',
            body: new FormData(this)
        })
        .then(response => response.text())
        .then(data => {
            try {
                const jsonData = JSON.parse(data);

                if (jsonData.success) {
                    console.log('success : ' + (jsonData.message || 'Action effectuée avec succès'));
                    updateTaskData();

                    // Add fade effect for force button
                    if (action === 'force_task') {
                        const taskId = this.querySelector('input[name="task_id"]').value;
                        const forceBtn = document.getElementById(`force-btn-${taskId}`);
                        forceBtn.classList.add('fading');
                        setTimeout(() => {
                            forceBtn.classList.remove('fading');
                        }, 30000);
                    }
                } else {
                    console.error("Erreur lors de l'exécution de l'action : " + (jsonData.error || 'Erreur inconnue'));
                    alert(jsonData.error );
                }
            } catch (error) {
                console.error('Erreur de parsing JSON:', error);
                alert('Erreur inattendue. Veuillez vérifier la console pour plus de détails.');
            }
        })
        .catch(error => console.error('Error:', error));
    });
});


// Gestion des accordéons
document.querySelectorAll('.category-row, .subcategory-row').forEach(row => {
    row.addEventListener('click', function() {
        this.classList.toggle('expanded');
        const isCategory = this.classList.contains('category-row');
        const category = this.dataset.category;
        const subcategory = this.dataset.subcategory;

        if (isCategory) {
            const elementsToToggle = document.querySelectorAll(`[data-category="${category}"]:not(.category-row)`);
            elementsToToggle.forEach(el => el.classList.toggle('hidden'));
        } else {
            const elementsToToggle = document.querySelectorAll(`.task-row[data-category="${category}"][data-subcategory="${subcategory}"]`);
            elementsToToggle.forEach(el => el.classList.toggle('hidden'));
        }
    });
});