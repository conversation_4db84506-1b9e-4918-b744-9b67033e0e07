<?php
require_once('tenants.inc.php');
require_once('websites.inc.php');
require_once('cfg.variables.inc.php');
require_once('view.monitoring.inc.php');

class Configuration {
    private $tenant;
    private $tnt_id;
    private $wst_id;

    private $platform_list;
    private $variables_list;

	/// Type de champ avancé : texte court
    const FLD_TYPE_TEXT = 1;
	/// Type de champ avancé : texte formaté TinyMCE (si "fld_old_txt_type" = 0, texte long non formaté)
    const FLD_TYPE_TEXTAREA = 2;
	/// Type de champ avancé : nombre entier
    const FLD_TYPE_INT = 3;
	/// Type de champ avancé : nombre à virgule flottante
    const FLD_TYPE_FLOAT = 4;
	/// Type de champ avancé : liste de choix à sélection unique (stockage val_name)
    const FLD_TYPE_SELECT = 5;
	/// Type de champ avancé : liste de choix à sélection multiple (stockage val_name, séparateur ", ")
    const FLD_TYPE_SELECT_MULTIPLE = 6;
	/// Type de champ avancé : image
    const FLD_TYPE_IMAGE = 7;
	/// Type de champ avancé : booléen (stockage "Oui" / "Non" ou "1" / "0")
    const FLD_TYPE_BOOLEAN_YES_NO = 8;
	/// Type de champ avancé : Obsolète, type images
    const FLD_TYPE_IMAGES = 9;
	/// Type de champ avancé : date ("fld_old_txt_type" = 0 / 1 pour le stockage de l'heure oui / non)
    const FLD_TYPE_DATE = 10;
	/// Type de champ avancé : pointeur (stockage de l'ID pointé, la classe est stockée dans fld_related_class)
    const FLD_TYPE_REFERENCES_ID = 11;
	/// Type de champ avancé : liste de choix hiérarchisée à sélection multiple (stockage val_id, séparateur ", ")
    const FLD_TYPE_SELECT_HIERARCHY = 12;
	/// Type de champ avancé : type d'upload de fichier
    const FLD_TYPE_FILE_UPLOAD = 13;

    public function __construct($tenant) {
        // Initialisation des informations sur le tenant ( $config, tnt_id, wst_id)
        $this->tenant = $tenant;
        $this->tnt_id = $tenant['id'];
    }

    public function list_all_platforms() {
        $r_wst = wst_websites_get(0, array('name'=>'asc'), $this->tnt_id);
        
        while($wst = mysql_fetch_assoc($r_wst)) {
            $this->platform_list['site'][] = $wst['name'];
        }

        if (is_numeric(cfg_overrides_get_value('sync_global_gescom_type', null, 0, $this->tnt_id))) {
            $this->platform_list['sync'] = 'Logiciel de synchronisation';
        }

        if (wst_websites_is_fdv_app($this->tnt_id)) {
            $this->platform_list['yuto'] = 'Application Yuto'; // on détermine si il existe yuto sur le tenant
            //$this->platform_list['yuto']['id'] = ria_mysql_fetch_assoc(wst_websites_get(0, false, $this->tnt_id, false, _WST_TYPE_FDV))['id']; // on recup le wst_id
        }

        //var_dump($this->platform_list);

        return $this->platform_list;
    }

    public function list_all_variables($wst = false, $filter = '') {
        if($wst === 'sync') $wst_pattern = '^(sync_)';
        elseif($wst === 'yuto') $wst_pattern = '^(device_|fdv_)';
        else $wst_pattern = '^(?!sync_|device_|fdv_|admin_)';

        $r_cfg_variable = cfg_variables_get('', [], $wst_pattern);

        $supposed;

        while ($cfg_variable = ria_mysql_fetch_assoc($r_cfg_variable)) {
            if ($filter && stripos($cfg_variable['code'], $filter) === false) {
                continue;
            }

            $override = cfg_overrides_get_value($cfg_variable['code'], null, 0, $this->tnt_id);

            $this->variables_list[] = [
                'code'  => $cfg_variable['code'],
                'name'  => $cfg_variable['name'],
                'desc'  => $cfg_variable['desc'],
                'type'  => $cfg_variable['type'], // défini dans les const FLD_TYPE_*
                'value' => $override !== false ? $override : $cfg_variable['default']
            ];

            // Comment les variables sont recupéré de la manière "originale"
            //$supposed[$cfg_variable['code']] = array_merge($cfg_variable, array('override' => $cfg_variable['default'], 'childs' => array()));
        }

        return $this->variables_list;
    }

    public function update_variable($code, $value) {
        // on peut check si la variable existe : 
        // cfg_overrides_exists($cfg_variable['code'], $tnt_id, $save_wst_id) 

        return cfg_overrides_set_value($code, $value, 0, 0, $this->tenant['id']);
    }
}