.button-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 20px;
}

.button-container .button {
    flex: 1;
    margin: 0 5px;
    text-align: center;
}

.synchro-info {
    margin-top: 20px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 5px;
}

.synchro-info table {
    width: 100%;
    border-collapse: collapse;
}

.synchro-info th, .synchro-info td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
    text-align: left;
}

.synchro-info th {
    font-weight: bold;
    width: 40%;
}

.task-active {
    color: green;
    font-style: italic;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spinner {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid #ccc;
    border-top: 2px solid #333;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 5px;
}

.fade-update {
    animation: fadeUpdate 0.5s ease-in-out;
}

@keyframes fadeUpdate {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

.count-container {
    position: relative;
    display: inline-block;
}
.spinner {
    display: inline-block;
    width: 15px;
    height: 15px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 5px;
    vertical-align: middle;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}