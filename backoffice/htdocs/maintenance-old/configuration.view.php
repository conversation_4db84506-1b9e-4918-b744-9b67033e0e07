<?php
/**
 * \file configuration.php
 * Page de gestion des variables de configuration
 */

require_once('http.inc.php');
require_once('./configuration.inc.php');

// Vérification de l'authentification et des autorisations
if (!$config['USER_RIASTUDIO'] && $_SESSION['usr_email'] !== '<EMAIL>') {
    http_403();
    exit;
}

// Configuration de la page
define('ADMIN_PAGE_TITLE', _('Gestion des variables de configuration') . ' - ' . _('Maintenance'));
Breadcrumbs::root(_('Accueil'), '/admin/index.php')
    ->push(_('Page de maintenance'), '/admin/maintenance/maintenance.php')
    ->push(_('Gestion des variables de configuration'));

// Récupération des informations du tenant
$tenant = ria_mysql_fetch_array(tnt_tenants_get($config['tnt_id']));

// Initialisation de la classe Configuration
$configInstance = new Configuration($tenant);

// Récupération de toutes les plateformes
$platforms = $configInstance->list_all_platforms();

// Traitement des actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'update_variable') {
        $code = $_POST['variable_code'];
        $value = $_POST['variable_value'];
        $configInstance->update_variable($code, $value);
    }
}

// Récupération des variables de configuration
$selectedPlatform = isset($_GET['platform']) ? $_GET['platform'] : 'site';
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$variables = $configInstance->list_all_variables($selectedPlatform, $filter);

// En-tête de la page
require_once('admin/skin/header.inc.php');
?>

<link rel="stylesheet" href="/maintenance/css/configuration.css">

<div class="container">
    <div class="notice notice-super-admin">
        <?php echo _("Cette interface n'est visible que par les super-administrateurs."); ?>
    </div>

    <p>
        <button class="button button-primary" onclick="window.location.href='maintenance.php'">Retour au dashboard</button>
    </p>

    <h2><?php echo _("Gestion des variables de configuration"); ?></h2>

    <form method="get" class="filter-form">
        <select name="platform" onchange="this.form.submit()">
            <?php foreach ($platforms as $key => $value): ?>
                <?php if (is_array($value)): ?>
                    <optgroup label="<?php echo htmlspecialchars(ucfirst($key)); ?>">
                        <?php foreach ($value as $subValue): ?>
                            <option value="<?php echo htmlspecialchars($key . '_' . $subValue); ?>" <?php echo $selectedPlatform === $key . '_' . $subValue ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($subValue); ?>
                            </option>
                        <?php endforeach; ?>
                    </optgroup>
                <?php else: ?>
                    <option value="<?php echo htmlspecialchars($key); ?>" <?php echo $selectedPlatform === $key ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($value); ?>
                    </option>
                <?php endif; ?>
            <?php endforeach; ?>
        </select>
        <input type="text" name="filter" value="<?php echo htmlspecialchars($filter); ?>" placeholder="Filtrer les variables">
        <button type="submit">Filtrer</button>
    </form>


    <table class="table config-table">
        <colgroup>
            <col style="width: 30%;">
            <col style="width: 30%;">
            <col style="width: 30%;">
            <col style="width: 10%;">
        </colgroup>
        <thead>
            <tr>
                <th><?php echo _("Nom - Description"); ?></th>
                <th><?php echo _("Code"); ?></th>
                <th><?php echo _("Valeur"); ?></th>
                <th><?php echo _("Actions"); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php 
                if(!isset($variables) OR empty($variables)) $variables = array();
                
                foreach ($variables as $variable): ?>
                <tr>
                    <td><strong><?php echo htmlspecialchars($variable['name']) . "</strong> <br><br> " . htmlspecialchars($variable['desc']); ?></td>
                    <td><strong><?php echo htmlspecialchars($variable['code']); ?></strong></td>
                    <td>
                        <form method="post" class="variable-form">
                            <input type="hidden" name="action" value="update_variable">
                            <input type="hidden" name="variable_code" value="<?php echo htmlspecialchars($variable['code']); ?>">
                            <?php
                            switch ($variable['type']) {
                                case Configuration::FLD_TYPE_TEXT:
                                    echo '<input type="text" name="variable_value" value="' . htmlspecialchars($variable['value']) . '">';
                                    break;
                                case Configuration::FLD_TYPE_TEXTAREA:
                                    echo '<textarea name="variable_value">' . htmlspecialchars($variable['value']) . '</textarea>';
                                    break;
                                case Configuration::FLD_TYPE_INT:
                                    echo '<input type="number" name="variable_value" value="' . htmlspecialchars($variable['value']) . '">';
                                    break;
                                case Configuration::FLD_TYPE_FLOAT:
                                    echo '<input type="number" step="0.01" name="variable_value" value="' . htmlspecialchars($variable['value']) . '">';
                                    break;
                                case Configuration::FLD_TYPE_SELECT:
                                    // Assuming options are stored somewhere, you'd need to fetch them
                                    echo '<select name="variable_value">';
                                    // Add options here
                                    echo '</select>';
                                    break;
                                case Configuration::FLD_TYPE_SELECT_MULTIPLE:
                                    // Assuming options are stored somewhere, you'd need to fetch them
                                    echo '<select name="variable_value[]" multiple>';
                                    // Add options here
                                    echo '</select>';
                                    break;
                                case Configuration::FLD_TYPE_IMAGE:
                                    echo '<input type="file" name="variable_value" accept="image/*">';
                                    break;
                                case Configuration::FLD_TYPE_BOOLEAN_YES_NO:
                                    $checked = ($variable['value'] == 'Oui' || $variable['value'] == '1') ? 'checked' : '';
                                    echo '<input type="checkbox" name="variable_value" value="1" ' . $checked . '>';
                                    break;
                                case Configuration::FLD_TYPE_DATE:
                                    echo '<input type="date" name="variable_value" value="' . htmlspecialchars($variable['value']) . '">';
                                    break;
                                case Configuration::FLD_TYPE_REFERENCES_ID:
                                    // This might need special handling depending on how references are stored
                                    echo '<input type="text" name="variable_value" value="' . htmlspecialchars($variable['value']) . '">';
                                    break;
                                case Configuration::FLD_TYPE_SELECT_HIERARCHY:
                                    // This would require a custom hierarchical select implementation
                                    echo '<select name="variable_value[]" multiple>';
                                    // Add hierarchical options here
                                    echo '</select>';
                                    break;
                                case Configuration::FLD_TYPE_FILE_UPLOAD:
                                    echo '<input type="file" name="variable_value">';
                                    break;
                                default:
                                    echo '<input type="text" name="variable_value" value="' . htmlspecialchars($variable['value']) . '">';
                                    break;
                            }
                            ?>
                    </td>
                    <td>
                            <button type="submit" class="btn btn-primary update-btn"><?php echo _("Mettre à jour"); ?></button>
                        </form>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('.variable-form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            if (confirm('Êtes-vous sûr de vouloir mettre à jour cette variable ?')) {
                const formData = new FormData(this);
                fetch('api-configuration.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Variable mise à jour avec succès');
                    } else {
                        alert('Erreur lors de la mise à jour de la variable');
                    }
                });
            }//
        });
    });
});
</script>



<?php
require_once('admin/skin/footer.inc.php');
?>