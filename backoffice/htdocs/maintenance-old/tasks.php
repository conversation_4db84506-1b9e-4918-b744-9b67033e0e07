<?php
/**
 * \file tasks.php
 * Page de gestion des tâches de synchronisation
 */

require_once('http.inc.php');
require_once('synchro.inc.php');
require_once('./tasks.inc.php');

// Vérification de l'authentification et des autorisations
if (!$config['USER_RIASTUDIO'] && $_SESSION['usr_email'] !== '<EMAIL>') {
    http_403();
    exit;
}

// Configuration de la page
define('ADMIN_PAGE_TITLE', _('Gestion des tâches de synchronisation') . ' - ' . _('Maintenance'));
Breadcrumbs::root(_('Accueil'), '/admin/index.php')
    ->push(_('Page de maintenance'), '/admin/maintenance/maintenance.php')
    ->push(_('Gestion des tâches de synchronisation'));

// Récupération des informations du tenant
$rtnt = tnt_tenants_get($config['tnt_id']);
$tenant = ria_mysql_fetch_array($rtnt);

// Initialisation de la classe Tasks
$tenant['tnt_id'] = mysql_fetch_array(tnt_tenants_get())['id'];
$tasksInstance = new Tasks($tenant);

// Récupération de toutes les tâches
$tasks = $tasksInstance->list_all_tasks();
// Traitement des actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && isset($_POST['task_id'])) {
        $taskId = $_POST['task_id'];
        switch ($_POST['action']) {
            case 'start_task':
                $tasksInstance->activate_tasks([$taskId]);
                break;
            case 'stop_task':
                $tasksInstance->activate_tasks([]); // Désactive toutes les tâches
                break;
        }
    }
}
// En-tête de la page
require_once('admin/skin/header.inc.php');

?>

<link rel="stylesheet" href="/maintenance/css/tasks.css">

<div class="container">
    <div class="notice notice-super-admin">
        <?php echo _("Cette interface n'est visible que par les super-administrateurs."); ?>
    </div>

    <p>
        <button class="button button-primary" onclick="window.location.href='maintenance.php'">Retour au dashboard</button>
    </p>

    <h2><?php echo _("Gestion des tâches de synchronisation"); ?></h2>

    <table class="table task-table">
        <thead>
            <tr>
                <th><?php echo _("ID"); ?></th>
                <th><?php echo _("Nom"); ?></th>
                <th>⚪ Total</th>
                <th>⏳ Restant</th>
                <th>❌ Erreur</th>
                <th><?php echo _("📅 Dernier lancement"); ?></th>
                <th><?php echo _("⏲ Temps d'exécution"); ?></th>
                <th><?php echo _("Statut"); ?></th>
                <th><?php echo _("Actions"); ?></th>
                <th><?php echo _("Forcer"); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($tasks as $category => $subcategories): ?>
                <tr class="category-row" data-category="<?php echo htmlspecialchars($category); ?>">
                    <th colspan="10"><span class="toggle-icon"></span><?php echo htmlspecialchars($category); ?></th>
                </tr>
                <?php foreach ($subcategories as $subcategory => $subcategoryTasks): ?>
                    <tr class="subcategory-row hidden" data-category="<?php echo htmlspecialchars($category); ?>" data-subcategory="<?php echo htmlspecialchars($subcategory); ?>">
                        <th colspan="10"><span class="toggle-icon"></span><?php echo htmlspecialchars($subcategory); ?></th>
                    </tr>
                    <?php foreach ($subcategoryTasks as $taskId => $task): ?>
                        <tr class="task-row hidden" data-category="<?php echo htmlspecialchars($category); ?>" data-subcategory="<?php echo htmlspecialchars($subcategory); ?>" data-task-id="<?php echo $taskId; ?>">
                            <td class="task-id"><?php echo htmlspecialchars($taskId); ?></td>
                            <td class="task-name"><?php echo htmlspecialchars($task['name']); ?></td>
                            <td class="task-total"><?php echo $task['count_total'];?></td>
                            <td class="task-remaining"><?php echo $task['count_remaining'];?></td>
                            <td class="task-error"><?php echo $task['count_fail'];?></td>
                            <td class="task-last-run"><?php echo $task['date_start'];?></td>
                            <td class="task-execution-time"><?php echo strtotime($task['date_end']) - strtotime($task['date_start']);?>s</td>
                            <td class="task-status">
                                <span class="status-indicator status-indicator-<?php echo $taskId; ?> <?php echo $task['active'] == '1' ? 'active' : 'inactive'; ?>">
                                    <?php echo $task['active'] == '1' ? _("Actif") : _("Inactif"); ?>
                                </span>
                            </td>


                            <td class="task-actions">
                                <?php if ($task['active'] == '1'): ?>
                                    <form method="post" class="task-action-form">
                                        <input type="hidden" name="action" value="stop_task">
                                        <input type="hidden" name="task_id" value="<?php echo $taskId; ?>">
                                        <button type="submit" class="btn btn-stop"><?php echo _("Arrêter"); ?></button>
                                    </form>
                                <?php else: ?>
                                    <form method="post" class="task-action-form">
                                        <input type="hidden" name="action" value="start_task">
                                        <input type="hidden" name="task_id" value="<?php echo $taskId; ?>">
                                        <button type="submit" class="btn btn-start"><?php echo _("Démarrer"); ?></button>
                                    </form>
                                <?php endif; ?>
                            </td>
                            <td class="task-force">
                                <form class="task-action-form" method="post">
                                    <input type="hidden" name="task_id" value="<?php echo $taskId; ?>">
                                    <input type="hidden" name="action" value="force_task">
                                    <button type="submit" class="btn btn-warning btn-sm btn-force" id="force-btn-<?php echo $taskId; ?>"><?php echo _('Forcer'); ?></button>
                                </form>
                            </td>

                        </tr>
                    <?php endforeach; ?>
                <?php endforeach; ?>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<script src="/maintenance/js/tasks.js"></script>

<?php
require_once('admin/skin/footer.inc.php');
?>