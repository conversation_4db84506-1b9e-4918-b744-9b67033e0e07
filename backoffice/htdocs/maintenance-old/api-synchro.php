<?php
require_once('http.inc.php');
require_once('synchro.inc.php');

// Vérification de l'authentification et des autorisations
if (!$config['USER_RIASTUDIO'] && $_SESSION['usr_email'] !== '<EMAIL>') {
    http_response_code(403);
    exit;
}

// Récupération des informations du tenant 
$config['token'] = mysql_fetch_array(tnt_tenants_get())['token'];

// Initialisation de la classe Synchro
$synchro = new Synchro($config);

// Préparer les données à renvoyer
$data = [
    'sync_reboot' => mysql_fetch_array(tnt_tenants_get())['sync_reboot'],
    'last_sync' => mysql_fetch_array(tnt_tenants_get())['last-sync'],
    'waiting_orders' => [
        'count' => $synchro->get_waiting_orders(),
        'active' => $synchro->is_order_task_active()
    ],
    'waiting_quote' => [
        'count' => $synchro->get_waiting_quote(),
        'active' => $synchro->is_quote_task_active()
    ],
    'waiting_users' => [
        'count' => $synchro->get_waiting_users(),
        'active' => $synchro->is_user_task_active()
    ],
    'waiting_addresses' => [
        'count' => $synchro->get_waiting_addresses(),
        'active' => $synchro->is_address_task_active()
    ]
];

// Renvoyer les données au format JSON
header('Content-Type: application/json');
echo json_encode($data);