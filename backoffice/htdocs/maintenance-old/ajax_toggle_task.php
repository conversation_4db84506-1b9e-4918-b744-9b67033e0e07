<?php
/**
 * ajax_toggle_task.php
 * Handles AJAX requests to toggle task status
 */

require_once('../http.inc.php');
require_once('../admin/includes/auth.inc.php');
require_once('../includes/task_functions.inc.php');

function sendJsonResponse($success, $message = '', $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode(['success' => $success, 'message' => $message]);
    exit;
}

try {
    // Ensure only authorized users can access this script
    if (!isAuthorizedUser()) {
        sendJsonResponse(false, 'Unauthorized access', 403);
    }

    // Validate and sanitize input
    $tskId = filter_input(INPUT_POST, 'tsk', FILTER_VALIDATE_INT);
    $tntId = filter_input(INPUT_POST, 'tnt', FILTER_VALIDATE_INT);
    $active = filter_input(INPUT_POST, 'active', FILTER_VALIDATE_BOOLEAN);

    if ($tskId === false || $tntId === false || $active === null) {
        sendJsonResponse(false, 'Invalid input', 400);
    }

    // Update the task status in the database using the existing function
    $result = tsk_tasks_activation_set($tskId, $tntId, $active);

    if ($result) {
        sendJsonResponse(true, 'Task status updated successfully');
    } else {
        sendJsonResponse(false, 'Failed to update task status', 500);
    }

} catch (Exception $e) {
    // Log the error
    error_log('Error in ajax_toggle_task.php: ' . $e->getMessage());
    
    // Send a generic error message to the client
    sendJsonResponse(false, 'An unexpected error occurred', 500);
}
