<?php
	require_once('PaymentExternal/Lyra.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	if( !isset($_GET['ord']) || !is_numeric($_GET['ord']) || $_GET['ord'] <= 0 ){
		$g_error = _('Un ou plusieurs paramètres obligatoires sont manquants, empêchant ainsi le chargement des informations.');
	}else{
		// Détermine le module bancaire utilisé pas le site de la commande
		$wst_id = ord_orders_get_website( $_GET['ord'] );

		$module = '';
		if( is_numeric($wst_id) && $wst_id > 0 ){
			$module = wst_websites_get_cb_module( $wst_id );
		}

		if( !in_array($module, ['lyra']) ){
			$couchDb_ar_payment_attempts = ord_payment_couchdb_get( $_GET['ord'] );
		}else{ // Les actions qui suivent ne peuvent être lancé que pour le module bancaire LYRA
			// Remboursement partiel ou total d'une transaction
			if( isset($_POST['refund']) ){
				try{
					Lyra::refund( $_POST['trans-id'], $_POST['amount'] );

					$_SESSION['SUCCESS_LYRA_TRANSACTION'] = _('Le remboursement a bien été réalisé.');
					header('Location: /admin/orders/popup-payment-attempts.php?ord='.$_GET['ord']);
					exit;
				}catch( Exception $e ){
					$error = $e->getMessage();
				}
			}

			// Annulation d'une transaction
			if( isset($_POST['cancel']) ){
				try{
					Lyra::cancel( $_POST['trans-id'] );

					$_SESSION['SUCCESS_LYRA_TRANSACTION'] = _('La transaction a bien été annulée.');
					header('Location: /admin/orders/popup-payment-attempts.php?ord='.$_GET['ord']);
					exit;
				}catch( Exception $e ){
					$error = $e->getMessage();
				}
			}

			// Modification du montant d'une transaction
			if( isset($_POST['update']) ){
				try{
					Lyra::update( $_POST['trans-id'], $_POST['amount'], $_POST['currency'] );

					$_SESSION['SUCCESS_LYRA_TRANSACTION'] = _('Le montant de la transaction a bien été mis à jour.');
					header('Location: /admin/orders/popup-payment-attempts.php?ord='.$_GET['ord']);
					exit;
				}catch( Exception $e ){
					$error = $e->getMessage();
				}
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Historique des paiements').' - '._('Commandes'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'popup-iframe');
	require_once('admin/skin/header.inc.php');

	if( isset($g_error) ){
		print '<div class="error">'.$g_error.'</div>';
	}else{
		if( in_array($module, ['lyra']) ){
			if( isset($error) ){
				print '<div class="error">'.nl2br( $error ).'</div>';
			}
			if( isset($_SESSION['SUCCESS_LYRA_TRANSACTION']) ){
				print '<div class="success">'.nl2br( $_SESSION['SUCCESS_LYRA_TRANSACTION'] ).'</div>';
				unset($_SESSION['SUCCESS_LYRA_TRANSACTION']);
			}
			$ar_trans = [];

			switch( $module ){
				case 'lyra':
					$ord_id = $_GET['ord'];

					// hack Coopcorico/Cavac/Terre de Viande
					// Ce hack est lié au fait que la commande initial est splittée en commande frais et sec
					// le paiement lui est réalisé sur la commande source (fld_id = 101641 - propre à coopcorico)
					if( $config['tnt_id'] == 14 ){
						$ord_id = fld_object_values_get( $_GET['ord'], 101641, '', false, true );
					}

					// Charge les transactions effectués pour cette commande
					$ar_trans = Lyra::getOrderTransactions( $ord_id );

					// Charge le montant déjà remboursé
					$refund = Lyra::getOrderRefund( $ord_id );
					break;
			}

			?>
			<table id="list-payment-attempts" style="margin-top: 7px;" max-width="100%">
				<caption><?php print _('Liste des paiements'); ?></caption>
				<thead>
				</thead>
				<tbody><?php
					if( !count($ar_trans) ){
						print ' <tr>
							<td colspan="3">'._('Aucune tentative de paiement n\'a été effectuée ou enregistrée pour cette commande.').'</td>
						</tr>';
					}else{
						foreach( $ar_trans as $key => $one_trans ){
							print '<tr>'
								.'<td>'.$one_trans['date']->format('d/m/Y H:i:s').'<td>'
								.'<td>'.number_format( $one_trans['amount'], 2, ',', ' ' ).' '.$one_trans['currency'].'</td>'
								.'<td>'
									.$one_trans['status']['name']
									.(trim($one_trans['error']['msg']) != '' ? ' - ' : '')
									.$one_trans['error']['msg'];

									// Selon le statut il est possible d'annuler ou remboursé un paiement
									if( $one_trans['status']['code'] == 'AUTHORISED' ){
										// Possible de modifier ou annuler
										print ' <a data-id-trans="cancel'.$key.'" href="#">'._('Annuler').'</a>'
										.' - '
										.'<a data-id-trans="update'.$key.'" href="#" title="'._('Modifier le montant de la transaction.').'">'._('Modifier').'</a>';

										// Formulaire d'annulation
										print '<div><form class="form-refund" data-id-trans="cancel'.$key.'" method="post" action="?ord='.$_GET['ord'].'">'
											.'<a class="close" href="#">'._('Fermer X').'</a>'
											.'<input type="hidden" name="trans-id" value="'.$key.'" />'
											.'<p>'._('Vous êtes sur le point d\'annuler cette transaction, cette action est irréversible. Êtes-vous sur ?.').'</p>'
											.'<div>'
												.'<input class="btn-main" type="submit" name="cancel" value="'._('Annuler la transaction').'" />'
											.'</div>'
										.'</form></div>';

										// Formulaire de modification
										print '<div><form class="form-refund" data-id-trans="update'.$key.'" method="post" action="?ord='.$_GET['ord'].'">'
											.'<a class="close" href="#">'._('Fermer X').'</a>'
											.'<input type="hidden" name="trans-id" value="'.$key.'" />'
											.'<input type="hidden" name="currency" value="'.$one_trans['currency'].'" />'
											.'<p>'._('La remise en banque n\'a pas encore eu lieu. Vous pouvez donc mettre à jour le montant qui sera débité auprès de la banque.').'</p>'
											.'<div>'
												.'<label>'._('Montant :').'</label> '
												.'<input type="number" name="amount" max="'.$one_trans['amount'].'" min="0.01" step="0.01"
												value="'.$one_trans['amount'].'" /> '
												.'<input class="btn-main" type="submit" name="update" value="'._('Mettre à jour').'" />'
											.'</div>'
										.'</form></div>';
									}elseif( $one_trans['status']['code'] == 'CAPTURED' ){
										// Calcul le montant pouvant encore être remboursé
										$can_refund = $one_trans['amount'] - $refund;

										if( $can_refund > 0 ){
											// Possible de réaliser un remboursement partiel ou total
											print ' <a data-id-trans="'.$key.'" href="#" title="'._('Réaliser un remboursement partiel ou total de la transaction.').'">'
												._('Rembourser')
											.'</a>';

											// Formulaire de remboursement
											print '<div><form class="form-refund" data-id-trans="'.$key.'" method="post" action="?ord='.$_GET['ord'].'">'
												.'<a class="close" href="#">'._('Fermer X').'</a>'
												.'<input type="hidden" name="trans-id" value="'.$key.'" />'
												.'<p>'._('Vous pouvez réaliser un remboursement partiel ou total de la transaction.').'</p>'
												.'<div>'
													.'<label>'._('Montant :').'</label> '
													.'<input type="number" name="amount" max="'.$can_refund.'" min="0.01" step="0.01"
													value="'.$can_refund.'" /> '
													.'<input class="btn-main" type="submit" name="refund" value="'._('Procéder au remboursement').'" />'
												.'</div>'
											.'</form></div>';
										}
									}
								print '</td>'
							.'</tr>';
						}
					}
				?></tbody>
			</table>

			<script>
				$(document).ready(function(){
					$('.message-ajax-opacity')
						.removeClass('notice')
						.addClass('refund');
				}).on(
					'click', 'a[data-id-trans]', function(){console.log('passe');
						var ID = $(this).data('id-trans');
						$('.load-ajax-opacity, .message-ajax-opacity').show();
						$('.message-ajax-opacity').html( $('form[data-id-trans="' + ID + '"]').parent().html() );
						$('.message-ajax-opacity').find('.form-refund').show()
						return false;
					}
				).on(
					'click', '.close', function(){
						$('.load-ajax-opacity, .message-ajax-opacity').hide();
					}
				);
			</script>
			<?php
		}else{
			?>
			<table id="list-payment-attempts" style="margin-top: 7px;" max-width="100%">
				<caption><?php print _('Liste des paiements'); ?></caption>
				<thead>
					<?php
						if (is_array($couchDb_ar_payment_attempts) && sizeof($couchDb_ar_payment_attempts)){
							print ' <th class="col150px">'._('Date').'</th>
									<th>'._('Code de retour').'</th>
							';
							if ($config['USER_RIASTUDIO']) {
								print '<th class="col150px"></th>';
							}
						}
					?>
				</thead>
				<tbody>
					<?php
						if (is_array($couchDb_ar_payment_attempts) && sizeof($couchDb_ar_payment_attempts)){
							$cpt = 0;
							foreach ($couchDb_ar_payment_attempts as $attempt => $content) {
								print '<tr>
									<td>'.date('d/m/Y à H:i', $content['date']).'</td>
									<td>'.(isset($content['code_id']) && isset($content['code_name']) ? '['.$content['code_id'].'] - '.$content['code_name'] : _('Tentative d\'accès')).'</td>
								';
									if ($config['USER_RIASTUDIO']){
										print '<td style="text-align: right">';
											print '<a class="attempts_details" id="attempts_details-'.$cpt.'">'._('Voir les détails').'</a>';
											print '<a class="attempts_reduct" id="attempts_reduct-'.$cpt.'">'._('Réduire').'</a>';
										print '</td>';
									}
								print ' </tr>
										<tr>
											<td class="attempt_data" id="attempt_data-'.$cpt.'" colspan="'.($config['USER_RIASTUDIO'] ? 3 : 2).'">
								';
										if(is_array($content['data'])){
											print '<pre>'.print_r($content['data'], true).'</pre>';
										} else {
											print nl2br($content['data']);
										}
									print '
											</td>
										</tr>
								';
								$cpt++;
							}
						} else {
							print ' <tr>
										<td colspan="'.($config['USER_RIASTUDIO'] ? 3 : 2).'">'._('Aucune tentative de paiement n\'a été effectuée ou enregistrée pour cette commande').'</td>
									</tr>';
						}
					?>
				</tbody>
			</table>

			<script src="/admin/js/jquery.min.js"></script>
			<script>
				$(document).ready(function(){
					$('.message-ajax-opacity').removeClass('notice');

					$(".attempt_data").hide();
					$(".attempts_reduct").hide();

					$(".attempts_details").on('click', function(){
						var id = $(this).attr('id').substring($(this).attr('id').indexOf('-')+1, $(this).attr('id').length);
						$(".attempt_data").hide();
						$(".attempts_reduct").hide();
						$(".attempts_details").show();
						$("#attempt_data-"+id).show();
						$("#attempts_details-"+id).hide();
						$("#attempts_reduct-"+id).show();
					});

					$(".attempts_reduct").on('click', function(){
						var id = $(this).attr('id').substring($(this).attr('id').indexOf('-')+1, $(this).attr('id').length);
						$("#attempt_data-"+id).hide();
						$("#attempts_details-"+id).show();
						$("#attempts_reduct-"+id).hide();
					});
				}).on(
					'click', 'a[data-id-trans]', function(){console.log('passe');
						var ID = $(this).data('id-trans');
						$('.load-ajax-opacity, .message-ajax-opacity').show();
						$('.message-ajax-opacity').html( $('form[data-id-trans="' + ID + '"]').html() );
						return false;
					}
				);
			</script>
			<?php
		}
	}

	require_once('admin/skin/footer.inc.php');