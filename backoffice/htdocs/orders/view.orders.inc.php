<?php
//suppression de produit au panier 
if( isset( $_GET['prd_del'] ) && is_array($_GET['prd_del']) ){
	foreach( $_GET['prd_del'] as $line => $prd ){
		if( !ord_products_del($order['id'], $prd, $line) ){
			$error = _("Une erreur inattendue est survenue lors de la suppression du produit.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur.");
		}
	}
}

// mise à jour de la quantité d'un produit
if( isset( $_GET['prd_upd'], $_GET['prd_qte'], $_GET['prd_line'] ) && is_numeric($_GET['prd_upd']) && is_numeric($_GET['prd_qte']) && is_numeric($_GET['prd_line']) ){
	$is_colisage = prd_colisage_classify_exists($_GET['prd_upd']);
	$colisage_id = 0;
	if ($is_colisage && $colisage_id = fld_object_values_get(array($order['id'], $_GET['prd_upd'], $_GET['prd_line']), _FLD_PRD_COL_ORD_PRODUCT) ){
		$r_colisage = prd_colisage_types_get(parseInt($colisage_id));
		$colisage = ria_mysql_fetch_assoc($r_colisage);
	} else {
		$is_colisage = false;
	}
	if( !ord_products_update($order['id'], $_GET['prd_upd'], $_GET['prd_qte'],-1, $colisage_id ? $colisage_id : 0, $_GET['prd_line']) ){
		$error = _("Une erreur est survenue lors de la modification de la quantité du produit.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur.");
	}
}

// mise à jour du commentaire sur les lignes produits
if( isset( $_GET['prd_upd'], $_GET['prd_comment'], $_GET['prd_line'] ) && is_numeric($_GET['prd_upd']) && is_numeric($_GET['prd_line'])){
	if( !ord_products_notes_update( $order['id'], $_GET['prd_upd'], $_GET['prd_comment'], null, $_GET['prd_line'] ) ){
		$error = _("Une erreur est survenue lors de la modification du commentaire du produit.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur.");
	}
}

// ajout de produit dans le panier
if( isset( $_GET['prd_add'] ) && is_numeric($_GET['prd_add']) ){
	$col_products = '';
	if (isset($_GET['prd_col_prop']) && is_array($_GET['prd_col_prop'])){
		foreach ($_GET['prd_col_prop'] as $value) {
			if (!ord_products_add($order['id'], $_GET['prd_add'], $value['qte'], '', false, null, $value['col_id']) ){
				$error = _("Une erreur inattendue est survenue lors de la mise en panier d'un produit conditionné.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur.");
			}
		}
	} elseif (prd_colisage_classify_exists($_GET['prd_add'])){
		print json_encode(array('code' => '200', 'message' => 'le produit possède des conditionnements'));
		exit;
	} elseif (!ord_products_add($order['id'], $_GET['prd_add'], 1, '', false) ){
		$error = _("Une erreur inattendue est survenue lors de la mise en panier.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur.");
	}
}

// ajout de produit dans le panier multiple
if( isset( $_GET['prd_add'] ) && is_array($_GET['prd_add']) ){
	$col_products = '';
	foreach( $_GET['prd_add'] as $prd ){		
		if (isset($_GET['prd_col_prop']) && is_array($_GET['prd_col_prop'])){
			foreach ($_GET['prd_col_prop'][$prd] as $value) {
				if (!ord_products_add($order['id'], $prd, $value['qte'], '', false, null, $value['id']) ){
					$error = _("Une erreur inattendue est survenue lors de la mise en panier d'un produit conditionné.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur.");
				}
			}
		} elseif (prd_colisage_classify_exists($prd)){
			if ($col_products != ''){
				$col_products .= '&';
			}
			$col_products .= 'prd_id[]=' . $prd;
		} elseif( !ord_products_add($order['id'], $prd, 1, '', false) ){
			$error = _("Une erreur inattendue est survenue lors de la mise en panier.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur.");
		}
	}
	if (trim($col_products) != '' && !isset($error)){
		print json_encode(array('code' => '200', 'message' => 'Le produit possède des conditionnements', 'data' => $col_products));
		exit;
	}
	
	if (trim($col_products) == '' && !isset($error)){
		print json_encode(array('code' => '100', 'message' => 'Les produits ont été ajoutés correctement au panier'));
		exit;
	}

}

/**
*	Cette fonction permet d'afficher le panier d'une commande 
*	\param $ord_id : Identifiant de la commande 
*	\param $readonly  (Facultatif) : détermine si le panier est en lecture seul ou non
*	\param $model  (Facultatif) : Si c'est pour un modèle alors on n'affiche pas les prix
*
*	\return le code html du panier
*
*/
function ncmd_show_cart( $ord_id, $readonly = true, $model = false ){
	if( !is_numeric( $ord_id ) ) return false; 
	global $config;

	if( $model ){
		$ordered = ord_models_products_order_get();
		if ($ordered) {
			ord_models_products_order_update($ord_id, true);
		}
	}

	$ord = ria_mysql_fetch_array( ord_orders_get( 0, $ord_id ) ); 
	$products = ord_products_get($ord['id'], ($model && $ordered ? array('pos' => 'asc') : array('ref' => 'asc')));
	$cols = 7;
	if( $model && $ordered ){
		$cols = 5;
	}elseif( !$model ){
		$cols = 6;
	}

	$html = '<table class="ncmd_cart checklist table-articles" ' . ($model ? ' id="order-products"' : '') . '>
				<caption>'._('Articles').'</caption>
				<thead>
					<tr>
						<th class="th-art-w20" data-label="'._('Tout cocher :').' ">'.( $readonly ? '':'<input type="checkbox" onclick="checkAllClick(this)" class="checkbox"/>').'</th>
						<th class="th-art-w150 thead-none">'._('Référence').'</th>
						<th class="thead-none">'._('Désignation').'</th>
						'.( !$model ? '<th class="th-art-w180 thead-none">'._('Prix Unitaire').'</th>' : '' ).' 
						<th class="th-art-w150 thead-none align-center">'._('Quantité').'</th>
						'.( !$model ? '<th class="th-art-w180 thead-none">'._('Total').'</th>' : '' ).' 
						'.( $model && $ordered ? '<th id="ord-pos thead-none">'._('Déplacer').'</th>' : '') . '
					</tr>
				</thead>
				<tbody>';
				
					$odd = false;
					if( $products && ria_mysql_num_rows( $products ) ){
						while( $prd = ria_mysql_fetch_array($products) ){

							$is_colisage = prd_colisage_classify_exists($prd['id']);
							$colisage_id = false;
							if ($is_colisage && $colisage_id = fld_object_values_get(array($ord_id, $prd['id'], $prd['line']), _FLD_PRD_COL_ORD_PRODUCT) ){
								$r_colisage = prd_colisage_types_get(parseInt($colisage_id));
								$colisage = ria_mysql_fetch_assoc($r_colisage);
							} else {
								$is_colisage = false;
							}
							
							$odd = !$odd;
							$html .= '<tr class="'.($odd ? 'odd':'even').' ria-row-orderable" ' . ($model && $ordered ? 'id="line-' . $ord_id . '-' . $prd['id'] . '-' . $prd['line'].'"' : '' ) . '>' ;
							$html .= '	  <td class="valign-center">'.( !$readonly  ? '<input id="ncmd_remove_prd_'.$prd['line'].'" type="checkbox" name="ncmd_remove_prd[]" class="ncmd_remove_prd" value="'.$prd['id'].'"/>':'').'</td>';
							
							$categories = prd_products_categories_get($prd['id']);
							if( $cat = ria_mysql_fetch_array($categories) ){
								$html .= '<td class="valign-center" data-label="'._('Référence :').' "><a href="../catalog/product.php?prd='.$prd['id'].'&amp;cat='.$cat['cat'].'">'.htmlspecialchars($prd['ref']).'</a></td>';
							}else{
								$html .= '<td class="valign-center" data-label="'._('Référence :').' ">'.htmlspecialchars($prd['ref']).'</td>';
							}
							
							$html .= '<td data-label="'._('Désignation :').' ">';
							$html .= 	'<p>'.htmlspecialchars($prd['name']).' '.($is_colisage ? ' - '.htmlspecialchars($colisage['name']).' ('.parseInt($colisage['qte']).')' : '').'</p>';
							if( !$readonly ){
								$html .= '	<label for="ncmd_prd_comment_'.$prd['id'].'">'._('Commentaires :').'</label> <input class="ncmd_prd_comment" type="text" name="ncmd_prd_comment_'.$prd['id'].'" id="ncmd_prd_comment_'.$prd['id'].'" value="'.htmlspecialchars( $prd['notes'] ).'" />';
							}
							$html .= '</td>';
							
							if( !$model ) $html .= '<td class="valign-center align-right" data-label="'._('Prix Unitaire :').' ">'.number_format( $prd['price_ht'], 2, ',', ' ' ).' &euro;</td>';
							
							$html .= '<td class="valign-center align-center" data-label="'._('Quantité :').' ">';
							if( !$readonly ){
								$html .= '<input type="text" class="ncmd_qte" name="ncmd_qte_'.$prd['id'].'" value="'.$prd['qte'].'"/>'; 
							}else{
								$html .= $prd['qte'];
							}
							$html .= '</td>';
							
							if( !$model )$html .= '<td class="valign-center align-right" data-label="'._('Total :').' ">'.number_format( $prd['total_ht'], 2, ',', ' ' ).' &euro;</td>';
							
							if( $model && $ordered ){
								$html .= '<td headers="ord-pos" class="ria-cell-move"></td>';
							}

							$html .= '</tr>';
						}
					}else{
						$html .= '<tr><td colspan="' . ($ordered ? 6 : 4) .'">'._('Aucun produit dans le panier').'</td></tr>';
					}
					
	$html .= '	</tbody>
		<tfoot>';
			
		if( !$model ){
			$html .= '
						<tr>';
							if( !$readonly && $products && ria_mysql_num_rows( $products ) ){
								$html .= '<td class="tdleft" colspan="2"><input type="button" name="del" value="'._('Supprimer').'"/></td>';
							}
			$html .= '		<td colspan="'.(!$readonly && $products && ria_mysql_num_rows( $products ) ? '2':'4').'" class="align-right">'._('Code promotion').' :</td>
							<td colspan="2">';
								if( $ord['pmt_id'] ){
									$rpmt = pmt_codes_get( $ord['pmt_id'] );
									if( ria_mysql_num_rows($rpmt) ){
										$pmt = ria_mysql_fetch_array($rpmt);
										$html .= '<a href="/admin/promotions/codes/edit.php?pmt='.$pmt['id'].'">'.$pmt['code'].'</a> ';
										$html .= '<input class="btn-action" type="submit" name="detach" style="width: auto" value="'._('Détacher').'" />';
									}
								}else{
									$html .= '<input type="text" class="text" name="pmt-code" maxlength="16" style="width: 110px" />';
									$html .= '<input class="btn-action" type="submit" style="width: auto" name="attach" value="'._('Attacher').'" />';
								}
			$html .= '		</td>
						</tr>
						<tr><th colspan="4" class="align-right">'._('Total HT').' :</th><td colspan="2">'.number_format($ord['total_ht'],2,',',' ').' &euro;</td></tr>
						<tr><th colspan="4" class="align-right">'._('TVA').' :</th><td colspan="2">'.number_format($ord['total_ttc']-$ord['total_ht'],2,',',' ').' &euro;</td></tr>
						<tr><th colspan="4" class="align-right">'._('Total TTC').' :</th><td colspan="2">'.number_format($ord['total_ttc'],2,',',' ').' &euro;</td></tr>
					'; 

		}else{

			if( !$readonly && $products && ria_mysql_num_rows( $products ) ){
				$html .= '<tr><td class="tdleft" colspan="' . ($ordered ? 6 : 4) . '"><input type="button" name="del" value="'._('Supprimer').'"/></td></tr>';
				$html .= '
				<tr>
					<td colspan="' . ($ordered ? 6 : 4) . '" class="tfoot-grey">
						<label>'._('Trier ces produits par ordre').' :</label>
						<input type="radio" class="radio" name="order" id="order-0" value="0" ' . ( !$ordered ? 'checked="checked"' : ''). ' /> <label for="order-0">'._('Alphabétique').' (Référence)</label>
						<input type="radio" class="radio" name="order" id="order-1" value="1" ' . ( $ordered ? 'checked="checked"' : '') . ' /> <label for="order-1">'._('Personnalisé').'</label>
						<input type="submit" name="orderby" value="'._('Appliquer').'" />
					</td>
				</tr>';
			}
		}

	$html .= '	</tfoot>
			</table>';
		
	if( !$readonly ){
		$html .= '<div class="align-right">'; 
		$html .= '<input type="button" value="'._('Ajouter un produit').'" id="ncmd_select_product" class="btn-action" />&nbsp;'; 
		$html .= '<input type="button" value="'._('Ajout rapide').'" title="'._('Ajouter des produits via une interface tableur').'" id="ncmd_add_products" class="btn-action" />'; 
		$html .= '</div>'; 
	}
	
	return $html;
}