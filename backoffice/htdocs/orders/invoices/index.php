<?php
	/**	\file index.php
	 *	Cette page affiche une liste de factures
	 */

	require_once('fields.inc.php');
	require_once('orders.inc.php');
	require_once('ord/bl.inc.php');
	require_once('view.admin.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_INVOICES_VIEW');

	/*$orders_count = 0;
	$have_mdl_filter = $have_fld_filter = false;

	// Filtre sur l'état de la commande, ou 0 pour toutes les commandes "actives"
	if( !isset($_GET['state'] ) || !ord_states_exists($_GET['state'] ) ){
		$_GET['state'] = 0;
	}

	// Le paramètre ref est utilisé pour les recherches
	if( !isset($_GET['ref'] ) ){
		$_GET['ref'] = 0;
	}else{
		// Réalise un trim sur la référence saisie par l'internaute car la recherche de commande attend une référence exacte
		$_GET['ref'] = trim($_GET['ref']);
	}

	// Filtre sur l'origine de la commande
	$params = view_origins_get_params();
	$origin = $params['origin'];
	$gescom = $params['gescom'];
	$is_web = $params['is_web'];
	*/

	if( isset($_GET['pay']) && is_numeric($_GET['pay']) ){
		$_SESSION['ord_pay_id'] = $_GET['pay'];
	}

	if( isset($_GET['seller']) && is_numeric($_GET['seller']) ){
		$_SESSION['ord_seller_id'] = $_GET['seller'];
	}

	if( isset($_GET['dps']) && is_numeric($_GET['dps']) ){
		$_SESSION['ord_dps_id'] = $_GET['dps'];
	}

	// Variable pour la mise en place des périodes
	$date1 = isset($_SESSION['datepicker_date1'] ) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2'] ) ? dateparse( $_SESSION['datepicker_date2'] ) : false;

	if( isset($_GET['date1']) && isdate($_GET['date1']) ){
		$date1 = $_SESSION['datepicker_date1'] = $_GET['date1'];
	}

	if( isset($_GET['date2']) && isdate($_GET['date2']) ){
		$date2 = $_SESSION['datepicker_date2'] = $_GET['date2'];
	}

	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;

	$_SESSION['datepicker_period'] = isset($_SESSION['datepicker_period'] ) ? $_SESSION['datepicker_period'] : _('Aujourd\'hui');
	$_SESSION['ord_pay_id'] = isset($_SESSION['ord_pay_id'] ) ? $_SESSION['ord_pay_id'] : 0;
	$_SESSION['ord_dps_id'] = isset($_SESSION['ord_dps_id'] ) ? $_SESSION['ord_dps_id'] : 0;
	$_SESSION['ord_seller_id'] = isset($_SESSION['ord_seller_id'] ) ? $_SESSION['ord_seller_id'] : 0;

	$ar_payments = ord_payment_types_get_array();

	/*$pmt_id = isset($_GET['pmt'] ) && is_numeric($_GET['pmt'] ) && $_GET['pmt'] ? $_GET['pmt'] : 0;

	// Extraction des commandes
	$_GET['piece'] = '';
	if( !is_numeric($_GET['ref'] ) ){
		$_GET['piece'] = $_GET['ref'];
		$_GET['ref'] = 0;
	}*/

	//$ord_ids = array();

	/*{ // Filtre sur modèle de saisie / Champs avancé
		if( isset($_GET['mdl'] ) && is_numeric($_GET['mdl'] ) && $_GET['mdl']>0 ){
			if( $rmdl = fld_models_get($_GET['mdl'] ) ){
				$mdl = ria_mysql_fetch_array($rmdl);
				if( $mdl['cls_id']==CLS_ORDER ){
					$r_object = fld_models_get_objects($mdl['id']);
					if( $r_object ){
						while( $object = ria_mysql_fetch_assoc($r_object) ){
							$ord_ids[] = $object['obj_id'];
						}
					}

					$have_mdl_filter = true;
				}
			}
		}

		if( !$have_mdl_filter ){
			if (isset($_GET['fld'] ) && is_numeric($_GET['fld'] ) && $_GET['fld'] > 0) {
				if( $rfld = fld_fields_get($_GET['fld'])) {
					$fld = ria_mysql_fetch_array($rfld);
					if( $fld['cls_id'] == CLS_ORDER) {
						$r_object = fld_fields_get_objects($fld['id']);
						if( $r_object) {
							while ($object = ria_mysql_fetch_assoc($r_object)) {
								$ord_ids[] = $object['obj_id'];
							}
						}

						$have_fld_filter = true;
					}
				}
			}
		}

		if( !$have_mdl_filter && !$have_fld_filter ){
			$ord_ids = 0;
		}
	}

	$ar_states = ord_states_get_array();*/

	// Calcule le nombre de factures
	$invs = ord_invoices_get( 0, 0, 0, false, false, false, false, 0, $date1, $date2, false, '', $_SESSION['ord_pay_id'], $_SESSION['ord_seller_id'], $_SESSION['ord_dps_id'] );
	$invs_count = $invs ? ria_mysql_num_rows($invs) : 0;

	// Calcule le montant total des factures
	$inv_ids = array();
	$totals = array('total_ht' => 0, 'total_ttc' => 0);
	if( $invs_count ){
		while( $inv = ria_mysql_fetch_assoc($invs) ){
			$inv_ids[] = $inv['id'];
			$totals['total_ht']  += $inv['total_ht'];
			$totals['total_ttc'] += $inv['total_ttc'];
		}

		ria_mysql_data_seek( $invs, 0 );
	}

	$key = array(
		'id' => is_array($inv_ids) ? $inv_ids : '',
		'ref' => '',
		'piece' => '',
	);

	$filter = array(
		'pay_id'		=> $_SESSION['ord_pay_id'],
		'dps_id' 		=> $_SESSION['ord_dps_id'],
		'seller_id' 	=> $_SESSION['ord_seller_id'],
	);

	$period = array(
		'start' => $date1,
		'end' => $date2,
	);

	// Récupère les devises utilisées
	$currencies = prd_prices_get_all_currencies();

	// Export des factures
	$msg = null;
	if( isset($_GET['export'] ) ){
		ria_mysql_data_seek( $invs, 0 );

		$file_name = 'factures-'.time().'.xls';
		$file_csv = $config['doc_dir'].'/'.$file_name;

		$exp_id = exp_exports_add( CLS_INVOICE, $file_csv, $file_name );
		try{
			// Ajoute l'import dans la file d'attente
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_EXPORT_GENERATE, array(
				'wst_id' => $config['wst_id'],
				'cls_id' => CLS_INVOICE,
				'exp_id' => $exp_id,
				'key' => $key,
				'filter' => $filter,
				'period' =>$period,
			));
			$_SESSION['msg']['text'] = _('Votre demande d\'export a bien été prise en compte, vous pouvez suivre son traitement depuis <a href="/admin/tools/exports/index.php" id="link-exports"> Outils > Exports</a>.');
			$_SESSION['msg']['class'] = 'success';
		}catch(Exception $e){
			$_SESSION['msg']['text'] = _('Une erreur est survenue lors de l\'export');
			$_SESSION['msg']['class'] = 'error';
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
		header('Location: /admin/orders/invoices/index.php');
		exit;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Commandes'), '/admin/orders/index.php' )
		->push( _('Factures') );


	// Compose le titre de la page
	$title = _('Factures');

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', $title );
	require_once('admin/skin/header.inc.php');
?>

<form action="index.php" method="get">

<?php
	print '<h2 id="title-state">';
	print '<input class="btn-move float-right hide-mobile" type="submit" name="export" value="'._('Exporter').'" title="'._('Exporter la liste des factures au format Microsoft Excel').'" />';
	print htmlspecialchars( $title ).' ('.number_format( $invs_count, 0, ',', ' ' ).')';
	print '</h2>';

	if( isset($_SESSION['msg'] ) ){ ?>
		<div class="<?php print $_SESSION['msg']['class']; ?>"><?php print $_SESSION['msg']['text']; ?></div><?php
		unset($_SESSION['msg']);
	}

	// Calcul des paramètres de pagination
	$by_page = 25;

	$_GET['page'] = isset($_GET['page'] ) && is_numeric($_GET['page'] ) && $_GET['page'] ? $_GET['page'] : 1;
	$pages = ceil( $invs_count / $by_page );
	if( $_GET['page']>$pages ){
		$_GET['page'] = $pages;
	}
	if( $_GET['page']>1 ){
		ria_mysql_data_seek( $invs, ($_GET['page'] - 1) * $by_page );
	}

	// Dates de début et de fin
	if( isset($_GET['date1'], $_GET['date2'], $_GET['last'] ) ){
		print '
			<input type="hidden" name="date1" id="date1" value="'.$_GET['date1'].'"/>
			<input type="hidden" name="date2" id="date2" value="'.$_GET['date2'].'"/>
			<input type="hidden" name="last" id="last" value="'.$_GET['last'].'"/>
		';
	}
?>

<div class="stats-menu">
	<div class="ria-admin-ui-filters">
		<div id="riadatepicker"></div>
		<?php
			// Filtre sur le moyen de paiement
			print view_pay_selector();

			// Filtre sur le représentant
			print view_sellers_selector();

			// Filtre sur les dépôts
			print view_deposits_selector($config['tnt_id']);
		?>
	</div>
	<div class="clear"></div>
</div>

	<table class="list" id="table-liste-commandes">
		<caption class="thead-title-mobile"><?php print _('Factures')?></caption>
		<thead class="thead-none">
			<tr>
				<th id="ord-id"><?php print _('Numéro')?></th>
				<th id="ord-date"><?php print _('Date')?></th>
				<th id="ord-inv"><?php print _('Adresse de facturation')?></th>
				<th id="ord-livr"><?php print _('Adresse de livraison')?></th>
				<th id="ord-type-pay"><?php print _('Moyen de paiement')?></th>
				<th id="ord-ht"><?php print _('Total H.T.')?></th>
				<th id="ord-ttc"><?php print _('Total T.T.C.')?></th>
			</tr>
		</thead>
		<tbody id="lst_orders">
		<?php
			// Affichage des factures
			if( !$invs_count ){
				print '<tr><td colspan="7">'._('Aucune facture ne correspond à vos critères de sélection.').'</td></tr>';
			}else{
				$count = 0;
				while( ( $r = ria_mysql_fetch_assoc($invs) ) && $count<$by_page ){
					$user = array();
					$prc = array( 'money_code' => '' );
					$r_user = gu_users_get( $r['usr_id'] );

					if( $r_user && ria_mysql_num_rows($r_user) ){
						$user = ria_mysql_fetch_assoc( $r_user );

						$prc = ria_mysql_fetch_assoc(
							prd_prices_categories_get( $user['prc_id'] )
						);
					}

					$count++;

					/*	$id=ord_orders_name('','',$r['id']);

						if( $id == $r['piece'] ){
							$ord_id = $r['piece'];
						}elseif( $r['piece'] !== "" ){
							$ord_id = $r['piece'].' ('.$r['id'].')';
						}else{
							$ord_id = $r['id'];
						}

						// false permet de ne pas vérifier l'utilisateur sur les adresses
						$ord_address = ord_orders_address_load($r, false);*/
						unset($inv_address);
						if( $r['adr_invoices'] ){
							$rinv_address = gu_adresses_get( $r['usr_id'], $r['adr_invoices'] );
							if( ria_mysql_num_rows($rinv_address) ){
								$inv_address = ria_mysql_fetch_array( $rinv_address );
							}
						}

						unset($dlv_address);
						if( $r['adr_delivery'] ){
							$rdlv_address = gu_adresses_get( $r['usr_id'], $r['adr_delivery'] );
							if( ria_mysql_num_rows($rdlv_address) ){
								$dlv_address = ria_mysql_fetch_array( $rdlv_address );
							}
						}

					?>
						<tr>
							<td headers="ord-id" id="td-ord-id">
								<?php print view_inv_is_sync($r); ?>
								<a href="../invoice.php?inv=<?php echo $r['id']?>" title="<?php print _('Afficher la fiche de cette facture')?>"><?php
									echo htmlspecialchars( $r['piece'] ? $r['piece'] : str_pad( $r['id'], 8, '0', STR_PAD_LEFT) );
								?></a>
							</td>
							<td headers="ord-date" class="align-right"><?php echo ria_date_format($r['datenotime']); ?></td>
							<td headers="ord-inv">
								<?php
									if( isset($inv_address) ){
										print '<span class="td-caption">'._('Adresse de facturation :').'</span>';
										if( $inv_address['society'] ) print htmlspecialchars($inv_address['society']).'<br />';

										if( $inv_address['title_name'] ) print $inv_address['title_name'].' ';
										if( $inv_address['lastname'] ) print htmlspecialchars($inv_address['lastname']);
										if( $inv_address['firstname'] ) print ', '.htmlspecialchars($inv_address['firstname']).'<br />';
										if( $inv_address['address1'] ) print htmlspecialchars($inv_address['address1']).'<br />';
										if( $inv_address['address2'] ) print htmlspecialchars($inv_address['address2']).'<br />';
										if( $inv_address['address3'] ) print htmlspecialchars($inv_address['address3']).'<br />';
										print htmlspecialchars($inv_address['postal_code']).' '.htmlspecialchars($inv_address['city']).'<br />';
										print htmlspecialchars( $inv_address['country'] ).'<br />';
									}
									if (isset($r['usr_id'])) {
										$email = gu_users_get_email($r['usr_id']);
										if( $email) {
											print '<a href="mailto:'.htmlspecialchars($email).'" target="_top">'.htmlspecialchars($email).'</a>';
										}
									}
								?>
							</td>
							<td headers="ord-livr">
								<?php
									print '<span class="td-caption">'._('Adresse de livraison :').'</span>';

									if( isset($dlv_address) ){
										if( $dlv_address['society'] ) print htmlspecialchars($dlv_address['society'] ) . '<br />';

										if( $dlv_address['title_name'] ) print $dlv_address['title_name'] . ' ';
										if( $dlv_address['lastname'] ) print htmlspecialchars($dlv_address['lastname']);
										if( $dlv_address['firstname'] ) print ', ' . htmlspecialchars($dlv_address['firstname'] ) . '<br />';
										if( $dlv_address['address1'] ) print htmlspecialchars($dlv_address['address1'] ) . '<br />';
										if( $dlv_address['address2'] ) print htmlspecialchars($dlv_address['address2'] ) . '<br />';
										if( $dlv_address['address3'] ) print htmlspecialchars($dlv_address['address3'] ) . '<br />';
										print htmlspecialchars($dlv_address['postal_code'] ) . ' ' . htmlspecialchars($dlv_address['city'] ) . '<br />';
										print htmlspecialchars($dlv_address['country']);
									}
								?>
							</td>
							<td headers="ord-type-pay"><?php print $r['pay_id'] ? '<span class="td-caption td-caption--inline">'._('Moyen de paiement :').'</span>'.htmlspecialchars($ar_payments[$r['pay_id']]['name'] ) : '';?></td>
							<td headers="ord-ht"><?php print '<span class="td-caption td-caption--inline">'._('Total HT :').'</span>'.ria_number_format($r['total_ht'], NumberFormatter::CURRENCY, 2, 'EUR')?></td>
							<td headers="ord-ttc"><?php print '<span class="td-caption td-caption--inline">'._('Total TTC :').'</span>'.ria_number_format($r['total_ttc'], NumberFormatter::CURRENCY, 2, 'EUR')?></td>
						</tr>
					<?php
				}
			}
		?>
		</tbody>
		<tfoot>
			<tr id="pagination" <?php print $pages>1 ? '' : 'style="display: none"'; ?>>
				<td colspan="2" class="align-left">
				<?php
					print 'Page '.$_GET['page'].'/'.$pages;
				?>
				</td>
				<td colspan="5">
				<?php
					if( $pages>1 ){
						$page_start = $_GET['page']>5 ? $_GET['page']-5 : 1;
						$page_stop = $_GET['page']+5<$pages ? $_GET['page']+5 : $pages;

						$links = array();
						if( $_GET['page']>1 ){
							$links[] = '<a href="/orders/invoices/?page='.($_GET['page']-1).'">&laquo; '._('Page précédente').'</a>';
						}
						for( $i=$page_start; $i<=$page_stop; $i++ ){
							if( $i==$_GET['page'] ){
								$links[] = '<b>'.$i.'</b>';
							}else{
								$links[] = '<a href="/orders/invoices/?page='.$i.'">'.$i.'</a>';
							}
						}
						if( $_GET['page']<$pages ){
							$links[] = '<a href="/orders/invoices/?page='.($_GET['page']+1).'">'._('Page suivante').' &raquo;</a>';
						}
						print implode(' | ',$links);
					}
				?>
				</td>
			</tr>
			<tr class="ord-totals">
				<td colspan="5" class="align-right"><?php print _('Totaux :'); ?></th>
				<td class="align-right"><span><?php print number_format( $totals['total_ht'], 2, ',', ' ' ); ?></span> <?php print _('HT'); ?> </td>
				<td class="align-right"><span><?php print number_format( $totals['total_ttc'], 2, ',', ' ' ); ?></span> <?php print _('TTC'); ?> </td>
			</tr>
			<tr class="js-btn-export" style="display: none">
				<td colspan="7" >
					<input class="btn-move" type="submit" name="export" id="export" value="<?php print _('Exporter'); ?>" title="<?php print _('Exporter la liste des factures au format Microsoft Excel'); ?>" />
				</td>
			</tr>
		</tfoot>
	</table>
</form>

<script>
	var riadatepicker_upd_url = '';
	<?php
		view_date_initialized( $invs_count, '/orders/invoices/', false, array('refresh_in_ajax' => false,'load_popup_new_orders'=>false) );
	?>

	$(document).ready(function(){

		// Active le filtre par moyen de paiement
		$('#selectpaytype .selectorview,#selectpaytype .selector').mouseup(function(){
			if($('#selectpaytype .selector').css('display')=='none'){
				$('#selectpaytype .selector').show();
			}else{
				$('#selectpaytype .selector').hide();
			}
		});

		// Active le filtre par représentant
		$('#selectseller .selectorview,#selectseller .selector').mouseup(function(){
			if($('#selectseller .selector').css('display')=='none'){
				$('#selectseller .selector').show();
			}else{
				$('#selectseller .selector').hide();
			}
		});

		// Active le filtre par dépôt de stockage
		$('#selectdeposit .selectorview,#selectdeposit .selector').mouseup(function(){
			if($('#selectdeposit .selector').css('display')=='none'){
				$('#selectdeposit .selector').show();
			}else{
				$('#selectdeposit .selector').hide();
			}
		});

	}).delegate('#selectpaytype .selector a', 'click', function(){
			let pay_id = 'pay='+$(this).attr('name').replace('pay-','');
			window.location = 'index.php?' + pay_id;
		}
	).delegate('#selectseller .selector a', 'click', function(){
			let seller_id = 'seller='+$(this).attr('name').replace('seller-','');
			window.location = 'index.php?' + seller_id;
		}
	).delegate('#selectdeposit .selector a', 'click', function(){
			let dps_id = 'dps='+$(this).attr('name').replace('dps-','');
			window.location = 'index.php?' + dps_id;
		}
	);
</script>
<?php

require_once('admin/skin/footer.inc.php');