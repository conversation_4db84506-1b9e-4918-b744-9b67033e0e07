<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	$state = isset($_GET['state']) && is_numeric($_GET['state']) && $_GET['state'] ? $_GET['state'] : 0;

	if( !isset($orders) ){
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}

	if( !isset($config['export_shipment_active']) || !$config['export_shipment_active'] ){
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}
	
	/*
		A  = 0  = Référence destinataire
		B  = 1  = Nom ou Raison sociale
		C  = 2  = Suite Nom ou Suite Raison sociale ou Prénom ou Contact
		D  = 3  = Suite Nom 2 ou Suite Raison sociale 2 ou Prénom ou Contact
		E  = 4  = Adresse destinataire
		F  = 5  = Adresse destinataire suite
		G  = 6  = Digicode / Etage / Interphone
		H  = 7  = Code Postal destinataire
		I  = 8  = Ville Destinataire
		J  = 9  = Code Pays destinataire
		K  = 10 = Téléphone destinataire
		L  = 11 = Email destinataire
		M  = 12 = Référence envoi
		N  = 13 = Code barres client
		O  = 14 = Produit
		P  = 15 = Compte
		Q  = 16 = Sous-compte
		R  = 17 = Valeur assurée
		S  = 18 = Valeur douane
		T  = 19 = Document / marchandise
		U  = 20 = Description du contenu
		V  = 21 = Livraison le samedi
		W  = 22 = Identifiant Relais
		X  = 23 = Poids
		Y  = 24 = Largueur
		Z  = 25 = Longueur
		AA = 26 = Hauteur
		AB = 27 = Avertir destinataire
		AC = 28 = Nombre de colis
		AD = 29 = date d'envoi
		AE = 30 = A intégrer
		AF = 31 = Avertir expéditeur
	*/
		
	require_once('strings.inc.php');
	require_once('orders.inc.php');

	$ar_ord_ids = array();
	$ar_lines 	= array();

	while( $order = ria_mysql_fetch_assoc($orders) ){
		// Exclusion des commandes livrées en magasin
		if( is_numeric($order['str_id']) && $order['str_id'] > 0 ){
			$ar_ord_ids[] = $order['id'];
			continue;
		}

		// Exclusion des commandes livrées par un autre service que Chronopost
		if( !in_array($order['srv_id'], $config['export_shipment_chronopost']) ){
			continue;
		}
		
		$order = ria_mysql_fetch_assoc(ord_orders_get_with_adresses(0, $order['id']));
		
		$order_format = ord_orders_shipment_formatted( $order, 38 );
		if( !ria_array_key_exists(array('firstname', 'addr1', 'addr2', 'addr3', 'zipcode', 'city', 'ref_order', 'user_id', 'addr3', 'country', 'dlv-notes', 'weight_order', 'assurance', 'phone', 'email', 'society', 'mobile', 'rly_ref', 'rly_country'), $order_format) ){
			continue;
		}
		
		$type_delivery = 1; // Livraison à domicile
		if( trim($order_format['rly_ref']) != '' ){
			$type_delivery = 86; // Livraison en point relais
		}elseif( $order_format['country'] != 'FR' ){
			$type_delivery = 44; // Livraison à l'international
		}

		$user_ref = gu_users_get_ref( $order_format['user_id'], true );
		if( trim($user_ref) == '' ){
			$user_ref = $order_format['user_id'];
		}

		$tmp_line = array();
		
		$tmp_line[0]  = strcut( $user_ref, 50, '' );
		$tmp_line[1]  = strcut( $order_format['lastname'], 50, '' );
		$tmp_line[2]  = strcut( $order_format['firstname'], 50, '' );
		$tmp_line[3]  = '';
		$tmp_line[4]  = strtoupper2( $order_format['addr1'] );
		$tmp_line[5]  = strtoupper2( $order_format['addr2'] );
		$tmp_line[6]  = strtoupper2( $order_format['addr3'] );
		$tmp_line[7]  = strcut( $order_format['zipcode'], 9, '' );
		$tmp_line[8]  = strcut( $order_format['city'], 50, '' );
		$tmp_line[9]  = trim($order_format['rly_ref']) !=  '' && trim($order_format['rly_country']) != '' ? $order_format['rly_country'] : $order_format['country'];
		$tmp_line[10] = strcut( (trim($order_format['mobile']) != '' ? $order_format['mobile'] : $order_format['phone']), 17, '' );
		$tmp_line[11] = strcut( $order_format['email'], 80, '');
		$tmp_line[12] = strcut( $order_format['ref_order'], 35, '' );
		$tmp_line[13] = '';
		$tmp_line[14] = $type_delivery;
		$tmp_line[15] = isset($config['export_shipment_chronopost_id']) ? $config['export_shipment_chronopost_id'] : '';
		$tmp_line[16] = '';
		$tmp_line[17] = $order_format['assurance'];
		$tmp_line[18] = '';
		$tmp_line[19] = 'M';
		$tmp_line[20] = '';
		$tmp_line[21] = 'V';
		$tmp_line[22] = strcut( $order_format['rly_ref'], 5, '' );
		$tmp_line[23] = $order_format['weight_order'] > 0 ? strcut( number_format($order_format['weight_order']/1000, 2), 5, '' ) : '';
		$tmp_line[24] = '';
		$tmp_line[25] = '';
		$tmp_line[26] = '';
		$tmp_line[27] = '1';
		$tmp_line[28] = '1';
		$tmp_line[29] = date('d/m/Y');
		$tmp_line[30] = '';
		$tmp_line[31] = '';

		$ar_lines[] = $tmp_line;
		$ar_ord_ids[] = $order['id'];
	}

	if( sizeof($ar_lines) ){
		header("Content-type: text/csv");
		header("Content-Disposition: attachment; filename=export-chronopost-".date('dmY').".csv");
		header("Pragma: no-cache");
		header("Expires: 0");

		foreach( $ar_lines as $one_line ){
			$line = '';
			$first = true;

			foreach( $one_line as $col ){
				if( !$first ){
					$line .= ';';
				}

				$line .= utf8_decode( str_replace(';', '', $col) );

				$first = false;
			}

			print $line."\n";
		}

		exit;
	}else{
		$_SESSION['export_shipment_no_order'] = "Aucune commande à exporter pour Chronopost";
		header('Location: /admin/orders/orders.php?state='.$state);
		exit;
	}
