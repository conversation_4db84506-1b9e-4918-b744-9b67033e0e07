<?php

	/**	\file order-beta.php
   *  Cette page est une nouvelle version de l'interface affichant le détail d'une commande.
   *  Elle est en version BETA et ne reprendre qu'une partie des fonctionnalités présentes sur la page actuelle jugée trop lourds.
   *  Elle est utilisé par Conforama pour avoir accès à la gestion des colonnes du tableau des lignes de commande.
	 */

	require_once('orders.inc.php');
	require_once('delivery.inc.php');
	require_once('fields.inc.php');
	require_once('strings.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

	// Si impossible de charger la commande
	$no_ord = false;

  if( !isset($_GET['ord']) || !is_numeric($_GET['ord']) || $_GET['ord'] <= 0 ){
    $no_ord = true;
  }else{
    $r_ord = ord_orders_get_with_adresses( 0, $_GET['ord'] );

    if( !$r_ord || !ria_mysql_num_rows($r_ord) ){
      $no_ord = true;
    }

    $ord = ria_mysql_fetch_assoc( $r_ord );
  }

	unset( $error, $no_error );

  // redirection vers la liste des commandes
	if( $no_ord ){
		if( isset($_GET['state']) && is_numeric($_GET['state']) ){
			header('Location: orders.php?state='.$_GET['state']);
    }else{
			header('Location: orders.php');
    }
		exit;
	}

  if( !isset($_GET['state']) ) $_GET['state'] = '';

	// Rattachement à un modèle de saisie
	if( isset($_POST['addmdl']) && isset($_POST['model-pick']) && is_numeric($_POST['model-pick']) && $_POST['model-pick']>0 ){
		$res = fld_object_models_add( $_GET['ord'],$_POST['model-pick'] );
		if( $res===false ){
			$error = sprintf(_("L'ajout du modèle de saisie à la commande %d a échoué pour une raison inconnue."), $_GET['ord']);
		}else{
			header('Location: /admin/orders/order.php?ord='.$_GET['ord']);
			exit;
		}
	}

	// Détachement d'un modèle de saisie
	if( isset($_GET['delmdl']) && is_numeric($_GET['delmdl']) && $_GET['delmdl']>0 ){
		$res = fld_object_models_del( $_GET['ord'],$_GET['delmdl'] );
		if( $res===false ){
			$error = sprintf(_("La suppression du modèle de saisie pour la commande %d a échoué pour une raison inconnue."), $_GET['ord']);
		}else{
			header('Location: /admin/orders/order.php?ord='.$_GET['ord']);
			exit;
		}
	}

	// Sauvegarde des champs avancés
	if( isset($_POST['savefields']) ){
		$fields = fld_fields_get( 0,0,-2,0,0,0,null,array(),false,array(),null,CLS_ORDER );
		$notify_port = false; // Par défaut aucune notification est envoyé
		while( $f = ria_mysql_fetch_array($fields) ){
			if( $f['type_id']==FLD_TYPE_SELECT_MULTIPLE && !isset($_POST['fld'.$f['id']]) )
				$_POST['fld'.$f['id']] = '';
			if( isset($_POST['fld'.$f['id']]) ){
				$value = $_POST['fld'.$f['id']];
				fld_object_values_set( $_GET['ord'], $f['id'], $value );

				//Verification si un champs avancé lié au frais de port est modifier
				if( $f['id'] == FLD_PRODUCT_FDP_1|| $f['id'] == FLD_PRODUCT_FDP_2 ){
					$notify_port = true;
				}
			}
		}
		if(gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_PORT') && $notify_port){
			$res = ord_order_notify_port($_GET['ord']);
			if(!$res){
				$error = sprintf(_("Erreur lors de l'envoi de la notification."), $_GET['ord']);
			}

		}
		header('Location: /admin/orders/order.php?ord='.$_GET['ord']);
		exit;
	}

  // Modification de l'état de la commande
	if( isset($_POST['save-newstate'], $_POST['newstate']) && is_numeric($_POST['newstate']) ){
    $rorder = ord_orders_get( 0, $_GET['ord'] );
    if( !$rorder || !ria_mysql_num_rows($rorder) ){
      $error = _('Le chargement de la commande a échoué pour une raison inconnue.');
    }else{
      $order = ria_mysql_fetch_array($rorder);

      if(isset($config['active_popup_products_invoice']) && $config['active_popup_products_invoice'] && $_POST['newstate'] == _STATE_INVOICE ){
        if( $order['state_id'] != _STATE_INVOICE ){ // Si la commande est déja en état facturée, ne fait rien
          $inv = ord_invoices_add( $order['user'], date("d/m/Y") );

          if( !$inv ){
            $error = _("La création de la facture a échoué pour une raison inconnue.");
          }else{

            $qte_inv = array();

            //Récupère les factures lié à la commande
            $r_inv = ord_invoices_get( 0, 0, 0, false, false, false, false, $_GET['ord'], false, false, true );
            if( $r_inv ){
              while( $ord_inv = ria_mysql_fetch_assoc($r_inv)){
                //Récupère la quantité des produits déja facturés
                $r_inv_product = ord_inv_products_get( $ord_inv['id']);
                if( $r_inv_product ){
                  while( $inv_product = ria_mysql_fetch_assoc($r_inv_product) ){
                    if( isset($qte_inv[$inv_product['id']]) ){
                      $qte_inv[$inv_product['id']] = $qte_inv[$inv_product['id']] + $inv_product['qte'];
                    }else{
                      $qte_inv[$inv_product['id']] = $inv_product['qte'];
                    }
                  }
                }
              }
            }


            $r_product = ord_products_get( $order['id'] );
            if( !$r_product || !ria_mysql_num_rows($r_product) ){
              $error = _("Le chargement des produits de la commande a échoué pour une raison inconnue.");
            }else{
              while( $product = ria_mysql_fetch_assoc($r_product) ){
                $qte = isset($qte_inv[$product['id']])? $product['qte'] - $qte_inv[$product['id']] : $product['qte'];
                if( $qte > 0){
                    ord_inv_products_add( $inv, $product['id'], $product['line'], $product['ref'], $product['name'], $product['price_ht'], $qte, $product['tva_rate'], $order['id']);
                }
              }
            }
          }
        }
      }

      /**
       * Applique les valeurs par défaut pour les champs "_FLD_ORD_SIGN_RATE" et "_FLD_ORD_PIPE_SIGN_DATE" si non renseigné.
       *
       * Valeurs par défaut:
       * 	   - _FLD_ORD_SIGN_RATE       --> 3 (30% de chance de signature)
       *     - _FLD_ORD_PIPE_SIGN_DATE  --> 1 mois
       */
      if( in_array($_POST['newstate'], array(_STATE_DEVIS)) ){
        if( !fld_object_values_get($_GET['ord'], _FLD_ORD_PIPE_SIGN_DATE) ){
          fld_object_values_set($_GET['ord'], _FLD_ORD_PIPE_SIGN_DATE, date('Y-m-d 00:00:00', strtotime('+1 month')));
        }

        if( !fld_object_values_get($_GET['ord'], _FLD_ORD_SIGN_RATE) ){
          fld_object_values_set($_GET['ord'], _FLD_ORD_SIGN_RATE, 3);
        }
      }

      if( $_POST['newstate'] == 25 && (!isset($config['sys_cheque_actived']) || !$config['sys_cheque_actived'] || !in_array($order['pay_id'], array(_PAY_CHEQUE, _PAY_VIREMENT, _PAY_CHEQUE_X))) ){
        $error = _('La modification du statut de la commande a échoué car celle-ci n\'a pas été payée par chèque ou virement.');
      }elseif( !ord_orders_state_update($_GET['ord'], $_POST['newstate'], '', true, $_SESSION['usr_id'], false) ){
        $error = _('La modification du statut de la commande a échoué pour une raison inconnue.');
      }elseif( $_POST['newstate'] == _STATE_DEVIS && isset($_POST['need_sync']) && $_POST['need_sync'] ){
        if( !ord_orders_set_need_sync($_GET['ord']) ){
          $error = _('Une erreur est survenue lors de la modification du statut de commande.');
        }
      }else{

        if( isset($_GET['state']) && is_numeric($_GET['state']) ){
          header('Location: order-beta.php?ord='.$_GET['ord'].'&state='.$_GET['state']);
        }else{
          header('Location: order-beta.php?ord='.$_GET['ord']);
        }
        exit;
      }

      exit;
    }
	}

  $ord_id = $ord['id'];
  if( in_array($config['tnt_id'], [977, 1043, 998]) ){
    // Spé Conforama, l'ID de la commande est potentiellement dans un champ avancé
    $ord_id_original = fld_object_values_get( $ord['id'], 114185 );
    if( is_numeric($ord_id_original) && $ord_id_original > 0 ){
      $ord_id = $ord_id_original;
    }
  }

	$is_sync = $ord['piece'] !== '';
	$can_modify_order = !$is_sync && in_array($ord['state_id'], [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_WAIT_VALIDATION, _STATE_PAY_WAIT_CONFIRM, _STATE_DEVIS, _STATE_BASKET_PAY_CB]);
	$can_modify_comments = $can_modify_order || !$is_sync;

  $num = $ord['piece']!='' ? $ord['piece'] : str_pad( $ord_id, 8, '0', STR_PAD_LEFT );
	$state = '';
	if( isset($_GET['state']) && $_GET['state']>0 ){
		$rst = ord_states_get( $_GET['state'] );
		if( $rst && ria_mysql_num_rows($rst) ){
			$state = ria_mysql_result( $rst, 0, 'name_plural' );
		}
	}elseif( isset($_GET['state']) ){
		$state = _('Toutes les commandes');
	}

	// Détermine le fil d'Ariane
	$breadcrumbs = Breadcrumbs::root( 'Accueil', '/admin/index.php' );
	$breadcrumbs->push( _('Commandes'), '/admin/orders/orders.php' );
	if( is_numeric($_GET['state']) && $_GET['state'] > 0 ){
		$breadcrumbs->push( $state, '/admin/orders/orders.php?state='.$_GET['state'] );
	}else{
		$breadcrumbs->push( _('Toutes les commandes'), '/admin/orders/orders.php?state=0' );
	}
	$breadcrumbs->push( sprintf( _('Commande n°%s'), $ord_id) );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', sprintf(_('Commande %s%s - Commandes'), $num, ($state!='' ? ' - '.$state : '' )));
	require_once('admin/skin/header.inc.php');

?>
<h2><?php
  if( $_GET['state'] == _STATE_BASKET ){
    print view_cart_is_sync( $ord );
  }else{
    print view_ord_is_sync( $ord );
  }

  printf( ' '._('Pièce n°%d du %s'), str_pad( $ord_id, 8, '0', STR_PAD_LEFT ), ria_date_format($ord['date']) );
?></h2>

<?php
	if( isset($error) ){
		print '<div class="error">'.htmlspecialchars($error).'</div>';
	}
	if( isset($no_error) ){
		print '<div class="error-success">'.htmlspecialchars($no_error).'</div>';
	}
?>

<form action="order-beta.php?state=<?php print $_GET['state']; ?>&amp;ord=<?php print $_GET['ord']; ?>" method="post">
  <div id="ajax_ord_prds_filters">
    <input type="hidden" name="id" value="<?php print $ord['id']; ?>" />
    <input type="hidden" name="childid" value="<?php isset($ord_child) ? $ord_child['id'] : 0; ?>" />
  </div>

  <table id="table-une-commande">
    <thead>
      <tr>
        <th colspan="2"><?php print _('Propriétés générales')?></th>
      </tr>
    </thead>
    <tbody>
      <?php
        if( trim($ord['piece']) != '' ){
          print '<tr>'
            .'<td id="td-order-ref">'
              ._('Référence ERP :')
            .'</td>'
            .'<td class="large">'
              .htmlspecialchars( $ord['piece'] )
            .'</td>'
          .'</tr>';
        }
      ?>
      <tr>
        <td id="td-order-ref">
          <div class="bloc-ghost"></div>
          <?php print _('Référence :'); ?>
        </td>
        <td class="large">
        <?php
          if( !trim($ord['piece']) ){
            print '<input type="text" name="ord-reference" class="ord-mini-action" id="ord-reference" value="'.( trim($ord['ref']) ? htmlspecialchars($ord['ref']) : '' ).'"/>';
          }elseif( trim($ord['ref'])!='' ){
            print htmlspecialchars($ord['ref']);
          }
        ?>
        </td>
      </tr>

      <?php if( tnt_tenants_have_websites() ){ // Affiche le site web d'origine (si pertinent) ?>
        <tr>
          <td><?php print _('Source :'); ?></td><td><?php
            if( !$ord['pay_id'] ){
              $source = _('Gestion commerciale');
            }else{
              if( !tnt_tenants_have_websites() ){
                $source = 'Yuto';
              }else{
                if( wst_websites_is_fdv_app($config['tnt_id'], $ord['wst_id']) ){
                  $source = 'Yuto';
                }else{
                  $source = 'Web';
                }
              }
            }

            print htmlspecialchars( $source );
          ?></td>
        </tr>
      <?php }

        if( !in_array($config['tnt_id'], [977, 998, 1043]) ){
          if( !gu_user_is_authorized('_RGH_ADMIN_ORDER_USE_STATE_LINE') ){
            // Active ou non la mise à jour de l'état d'une commande
            $allow_state_update = isset($config['sys_cheque_actived']) && $config['sys_cheque_actived'];
            $allow_state_update = $allow_state_update || $config['allow_orders_update_state'] || $allow_cancel || $allow_delivery;

            $allow_update_state_delivery = isset($config['allow_orders_update_state']) && $config['allow_orders_update_state'];

            $delivery_previous_states = isset($config['delivery_previous_states']) && is_array($config['delivery_previous_states'])
              ? $config['delivery_previous_states']
              : array();

            $delivery_states = isset($config['delivery_states']) && is_array($config['delivery_states'])
              ? $config['delivery_states']
              : array();

            ?><tr>
              <td>
                <?php

                  $states_array = array();
                  if( isset($config['sys_cheque_actived']) && $config['sys_cheque_actived'] && in_array($ord['pay_id'], array(_PAY_CHEQUE, _PAY_VIREMENT, _PAY_CHEQUE_X)) ){
                    if( $ord['state_id']==25 ){
                      $states_array[] = 4;
                    } else {
                      $states_array[] = 25;
                    }
                  }

                  if( !$config['allow_orders_update_state'] || $config['allow_orders_update_state'] && $config['orders_update_state_included'] ){
                    // si annulation autorisée seulement, tous les états ne sont pas dans la liste
                    $states_array = array_merge($states_array, $config['orders_update_state_included']);
                    $states_array[] = 9;
                    $states_array[] = 10;
                    $states_array[] = $ord['state_id'];
                  }

                  if ($allow_update_state_delivery && isset($ord) && in_array($ord['state_id'], $delivery_previous_states)) {
                    if (isset($config['srv_port_store']) && $config['srv_port_store']) {
                      $states_array[] = _STATE_BL_STORE;
                    }
                    $states_array = array_merge($states_array, $delivery_states);
                  }

                  if( in_array($ord['state_id'], array( _STATE_WAIT_PAY, _STATE_PAY_WAIT_CONFIRM )) && in_array($ord['pay_id'], array(_PAY_CB, _PAY_SOFINCO)) ){
                    $states_array[] = _STATE_PAY_CONFIRM;
                    $allow_state_update = true;
                  }

                  if( $ord['pay_id']==_PAY_PAYPAL && $ord['state_id']==_STATE_WAIT_PAY ){
                    $states_array[] = _STATE_PAY_CONFIRM;
                    $allow_state_update = true;
                  }

                  if( $config['tnt_id'] == 14 ){
                    if( $ord['state_id'] == _STATE_BASKET && !in_array(_STATE_WAIT_PAY, $states_array) ){
                      $states_array[] = _STATE_WAIT_PAY;
                    }
                    if( $ord['state_id'] == _STATE_WAIT_PAY && !in_array(_STATE_PAY_CONFIRM, $states_array) ){
                      $states_array[] = _STATE_PAY_CONFIRM;
                    }
                  }

                  $states_array[] = _STATE_WAIT_PAY;
                  $states_array[] = _STATE_PAY_CONFIRM;
                  if (isset($config['ord_products_devis']) && $config['ord_products_devis']){
                    $states_array[] = _STATE_DEVIS;
                  }

                  if (!in_array($ord['state_id'], $states_array)) {
                    $states_array[] = $ord['state_id'];
                  }

                  $states = ord_states_get( $states_array );
                  if( $states && ria_mysql_num_rows($states) ){
                ?>
                <label for="newstate"><?php print _('Etat :'); ?></label>
              </td><td>
                <select name="newstate" id="newstate" <?php print !$allow_state_update ? 'disabled="disabled"' : ''; ?>>
                <?php
                  $print_need_sync = false;
                  while( $r = ria_mysql_fetch_array($states) ){
                    if( $ord['state_id'] == _STATE_INVOICE ){
                      switch ($r['id']){
                        case _STATE_SUPP_PARTIEL_INV:
                        case _STATE_BASKET:
                        case _STATE_BASKET_CANCEL:
                        case _STATE_BASKET_SAVE:
                        case _STATE_BASKET_PAY_CB:
                        case _STATE_CANCEL_MERCHAND:
                        case _STATE_CANCEL_USER:
                        case _STATE_WAIT_PAY:
                        case _STATE_PAY_CONFIRM:
                        case _STATE_DEVIS:
                          continue 2;
                          break;
                      }
                    }

                    if( $r['id']==$ord['state_id'] ){
                      if ($r['id'] == _STATE_DEVIS){
                        if ($ord['nsync'] == 0 && !$ord['piece']){
                          print '	<option id="ord_status_devis" value="'.$r['id'].'">'.htmlspecialchars( _($r['name']) ).'</option>
                              <option id="ord_status_devis_in_progress" value="'.$r['id'].'" selected="selected">'._('Devis en cours').'</option>';
                          $print_need_sync = true;
                        } else {
                          print '<option value="'.$r['id'].'" selected="selected">'.htmlspecialchars( _($r['name']) ).'</option>';
                        }
                      } else {
                        print '<option value="'.$r['id'].'" selected="selected">'.htmlspecialchars( _($r['name']) ).'</option>';
                      }
                    } else {
                      if ($r['id'] == _STATE_DEVIS){
                        if ($ord['state_id'] == _STATE_BASKET || $ord['state_id'] == _STATE_BASKET_SAVE){
                          print '	<option id="ord_status_devis" value="'.$r['id'].'">'.htmlspecialchars( _($r['name']) ).'</option>
                              <option id="ord_status_devis_in_progress" value="'._STATE_DEVIS.'">'._('Devis en cours').'</option>';
                          $print_need_sync = true;
                        } else {
                          print '<option value="'.$r['id'].'">'.htmlspecialchars( _($r['name']) ).'</option>';
                        }
                      } else {
                        print '	<option value="'.$r['id'].'">'.htmlspecialchars( _($r['name']) ).'</option>';
                      }
                    }

                  }
                ?>
                </select>
                <?php
                  if( $allow_state_update ){ ?>
                  <input type="submit" name="save-newstate" id="save-newstate" value="<?php print _('Modifier'); ?>" title="<?php print _('Mettre à jour le statut de cette commande'); ?>" />
                <?php }
                  }
                ?>
              </td>
            </tr>
          <?php }

          $exclude_codes = array();
          $promotions = pmt_codes_get(null, null, true);
          $order_promotions = ord_orders_promotions_get($ord['id']);
          if( ($can_modify_order && ria_mysql_num_rows($promotions)) || !empty($order_promotions) ){ ?>
            <tr>
              <th colspan="2"><?php print _('Codes promotion')?></th>
            </tr>
          <?php }

          if( !empty($order_promotions) ){ ?>
            <tr>
              <td><?php print _('Codes appliqués :'); ?></td>
              <td>
                <?php foreach( $order_promotions as $promotion ){
                  $rpmt = pmt_codes_get($promotion['pmt_id']);
                  if( ria_mysql_num_rows($rpmt) ){
                    $pmt = ria_mysql_fetch_assoc($rpmt);
                    $exclude_codes[] = $pmt['code'];
                ?>
                    <p>
                      <a href="/admin/promotions/codes/edit.php?pmt=<?php print $pmt['id']; ?>" target="_blank"><?php print htmlspecialchars((trim($pmt['name']) ? $pmt['name'].' - ' : '' ).$pmt['code']); ?></a>
                      <?php if( $can_modify_order ){ ?>
                        <input type="submit" class="auto_input" name="detach[<?php print $pmt['id']; ?>]" value="<?php print _('Détacher'); ?>" <?php print $ctr_validation ? 'disabled' : ''; ?>>
                      <?php } ?>
                    </p>
                  <?php }
                } ?>
              </td>
            </tr>
          <?php } if( ria_mysql_num_rows($promotions) && $can_modify_order ){ ?>
            <tr>
            <td>
              <label for="pmt-code"><?php print _('Code promotion :'); ?></label>
            </td>
            <td>
              <p>
                <select class="text" id="pmt-code" name="pmt-code" <?php print $ctr_validation ? 'disabled' : ''; ?>>
                  <option value="">--- <?php print _('Sélectionnez un code promotion'); ?> ---</option>
                  <?php
                    while( $row = ria_mysql_fetch_assoc($promotions) ){
                      if( !is_null($row['code']) && !in_array($row['code'], $exclude_codes) ){
                        print '<option value="'.$row['code'].'">'.htmlspecialchars((trim($row['name']) ? $row['name'].' - ' : '').$row['code']).'</option>' ;
                      }
                    }
                  ?>
                </select>
                <?php if( !$ctr_validation ){ ?>
                  <input class="auto_input" type="submit" name="attach" id="attach" value="<?php print _('Attacher')?>" title="<?php print _('Attacher le code promotion')?>" />
                <?php } ?>
              </p>
            </td>
            </tr>
          <?php }

          // Le bloc Règlement n'apparaîtra que s'il contient des informations à afficher
          $payment_name = ord_payment_types_get_name($ord['pay_id']);

          $nb_couchdb_payments = ord_payment_couchdb_get_count($_GET['ord']);
          if( $payment_name || $ord['comments'] || $ord['card_id'] || $nb_couchdb_payments ){
            ?>
              <tr><th colspan="2"><?php print _('Règlement')?></th></tr>
            <?php
          }
          if( $payment_name || $nb_couchdb_payments ){ ?>
            <tr>
              <td><?php print _('Mode de règlement :'); ?></td>
              <td><?php print $payment_name ? htmlspecialchars( $payment_name ) : ''; ?></td>
            </tr>
          <?php }
        }

        if( $ord['comments'] ){
          $show_comment = false;
          if( json_last_error() === JSON_ERROR_NONE ){
            $json = json_decode( $ord['comments'], true );

            foreach( $json as $k_cmt=>$v_cmt ){
              if( trim($v_cmt) != '' ){
                $show_comment = true;
                break;
              }
            }
          }else{
            $show_comment = true;
          }

          if( $show_comment ){
            print '<tr><th colspan="2">'._('Commentaire').'</th></tr>';

            print '<tr><td colspan="2" class="ord-comments">';
            $json = json_decode( $ord['comments'], true );
            if( json_last_error() === JSON_ERROR_NONE ){
              foreach( $json as $k_cmt=>$v_cmt ){
                print '<div>'
                  .'<strong>'.htmlspecialchars( $k_cmt ).'</strong>';

                  if( $can_modify_comments ){
                    print '<br /><textarea name="comments['.$k_cmt.']">'.htmlspecialchars( $v_cmt ).'</textarea>';
                  }else{
                    print '<br/><span>'.htmlspecialchars( $v_cmt ).'</span>';
                  }

                print '</div>';
              }
            }else{
              print nl2br( $ord['comments'] );
            }
          }

          print '</td></tr>';
        }

        if( $config['tnt_id'] != 171 ){
          $not_edit_data = true;
        }
        include('./order/update-user.php');

        if( !in_array($config['tnt_id'], [977, 998, 1043]) ){ ?>
          <tr><th colspan="2"><?php print _('Livraison')?></th></tr>
          <tr>
            <?php if( $ord['srv_id'] && ( $rsrv = dlv_services_get( $ord['srv_id'] ) ) && ( $srv = ria_mysql_fetch_array($rsrv) ) ){ ?>
            <td><label><?php print _('Livraison assurée par :'); ?></label></td>
            <td>
              <a href="/admin/config/livraison/services/edit.php?srv=<?php print $ord['srv_id']; ?>" target="_blank"><?php print htmlspecialchars($srv['name']); ?></a>
            </td>
            <?php } ?>
          </tr>
        <?php }
      ?>
    </tbody>
  </table>

  <?php
    include('./order/products-beta.php');

    if( !in_array($config['tnt_id'], [977, 998, 1043]) ){
      $total_ht = $total_tva = $total_ttc = [];
			$temps_totals_prd = $temps_totals_cfg = [];
			$temp_total_HT = $temp_total_TVA = $temp_total_TTC = 0;

      $r_ord_prd = ord_products_get( $_GET['ord'], array('line_pos'=>'asc'), 0, '', null, false, 1 );
			while( $prd = ria_mysql_fetch_assoc($r_ord_prd) ){
				if( $prd['id'] != 0 ){
						$currency = trim($prd['currency']) != '' ? $prd['currency'] : $prc['money_code'];

						if( !isset($total_ht[$currency]) ){
							$total_ht[ $currency ] = 0;
							$total_tva[ $currency ] = 0;
							$total_ttc[ $currency ] = 0;
						}

						$total_ht[ $currency ] = $ord['total_ht'];
						$total_ttc[ $currency ] = $ord['total_ttc'];
						$total_tva[ $currency ] = $total_ttc[ $currency ] - $total_ht[ $currency ];

				}
			}
      ?><table>
        <col width="90%"><col width="*">

        <tbody>
          <tr>
            <th class="align-right"><?php print _('Total HT :'); ?></th>
            <td class="align-right bold"><?php
              foreach( $total_ht as $currency=>$one_total ){
                print ria_number_format( $one_total, NumberFormatter::CURRENCY, 2, $currency ).'<br />';
              }
            ?></td>
          </tr>
          <tr>
            <th class="align-right"><?php print _('TVA :'); ?></th>
            <td class="align-right bold"><?php
              foreach( $total_tva as $currency=>$one_total ){
                print ria_number_format( $one_total, NumberFormatter::CURRENCY, 2, $currency ).'<br />';
              }
            ?></td>
          </tr>
          <tr>
            <th class="align-right"><?php print _('Total TTC :'); ?></th>
            <td class="align-right bold"><?php
              foreach( $total_ttc as $currency=>$one_total ){
                print ria_number_format( $one_total, NumberFormatter::CURRENCY, 2, $currency ).'<br />';
              }
            ?></td>
          </tr>
        </tbody>
        <?php if( !in_array($config['tnt_id'], [977, 998, 1043]) ){ ?>
          <tfoot>
            <tr>
              <td colspan="2">
                <div class="ord-history align-right">
                  <?php
                  print '<a onclick="displayPopup(\'Historique des états de la commande\', \'\', \'/admin/orders/popup-order-state-history.php?ord=' . intval($ord['id']) . '\'); return false;" class="button" href="#" title="'
                    . _('Historique des états de la commande')
                    . '">'
                      . _('Voir l\'historique')
                    . '</a>';
                  ?>
                </div>
              </td>
            </tr>
          </tfoot>
        <?php } ?>
      </table>
    <?php }

    $ctr_validation = false;
    include('./order/fields.inc.php');

    print '<input type="hidden" id="ord-id" value="'.htmlspecialchars( $ord['id'] ).'" />';
  ?>
</form>

<?php
  require_once('admin/skin/footer.inc.php');
