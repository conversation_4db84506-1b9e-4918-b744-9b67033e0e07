<?php

	/**	\file bl.php
	 *	Cette page est chargée d'afficher un bon de livraison. Elle a été créée à l'origine pour les besoins de Nutranimal
	 *	qui fait de la livraison directe / vente en laissé sur place avec Yuto.
	 *	Cet écran ne permet que la visualisation du bon de livraison, aucune modification.
	 *	Il a été créé à partir du fichier orders/invoice.php
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	require_once('orders.inc.php');
	require_once('ord/bl.inc.php');
	require_once('delivery.inc.php');
	require_once('promotions.inc.php');
	require_once('fields.inc.php');
	require_once('strings.inc.php');

	// L'identifiant de la pièce est obligatoire et il faut disposer d'un accès à cette page
	if( !isset($_GET['bl']) || !is_numeric($_GET['bl']) ){
		header('Location: /admin/orders/bl/index.php');
		exit;
	}

	// Charge le bon de livraison
	$rbl = ord_bl_get( $_GET['bl'] );
	if( !ria_mysql_num_rows($rbl) ){
		header('Location: /admin/orders/bl/index.php');
		exit;
	}
	$bl = ria_mysql_fetch_array( $rbl );

	// Chargement du commercial
	$seller = false;
	if( $bl['seller_id'] ){
		$r_seller = gu_users_get(0, '', '', PRF_SELLER, '', 0, '', false, false, $bl['seller_id']);
		if ($r_seller && ria_mysql_num_rows($r_seller)) {
			$seller = ria_mysql_fetch_assoc($r_seller);
		}
	}

	unset( $error, $success );

	// Met à jour le dépôt de livraison
	if( isset($_POST['save']) && isset($_POST['dps_id']) ){
		$res = ord_bl_set_deposit( $bl['id'], $_POST['dps_id'] );
		if( $res ){
			$success = _('Vos modifications ont été enregistrées avec succès.');
		}else{
			$error = _('Une erreur inattendue est survenue lors de l\'enregistrement de vos modifications');
		}
	}

	// Bouton Supprimer
	if( isset($_POST['delete']) ){
		if( ord_bl_del( $bl['id'] ) ){
			header('Location: /orders/bl/index.php');
			exit;
		}else{
			$error = 'Une erreur inattendue s\'est produite lors de la suppression du bon de livraison. Veuillez réessayer ou prendre contact avec nous pour nous signaler l\'erreur.';
		}
	}

	// Rattachement à un modèle de saisie
	/*if( !$ctr_validation && isset($_POST['addmdl']) && isset($_POST['model-pick']) && is_numeric($_POST['model-pick']) && $_POST['model-pick']>0 ){
		$res = fld_object_models_add( $_GET['ord'],$_POST['model-pick'] );
		if( $res===false ){
			$error = sprintf(_("L'ajout du modèle de saisie à la commande %d a échoué pour une raison inconnue."), $_GET['ord']);
		}else{
			header('Location: /admin/orders/order.php?ord='.$_GET['ord']);
			exit;
		}
	}*/

	// Détachement d'un modèle de saisie
	/*if( !$ctr_validation && isset($_GET['delmdl']) && is_numeric($_GET['delmdl']) && $_GET['delmdl']>0 ){
		$res = fld_object_models_del( $_GET['ord'],$_GET['delmdl'] );
		if( $res===false ){
			$error = sprintf(_("La suppression du modèle de saisie pour la commande %d a échoué pour une raison inconnue."), $_GET['ord']);
		}else{
			header('Location: /admin/orders/order.php?ord='.$_GET['ord']);
			exit;
		}
	}*/

	// Sauvegarde des champs avancés
	/*if( !$ctr_validation && isset($_POST['savefields']) ){
		$fields = fld_fields_get( 0,0,-2,0,0,0,null,array(),false,array(),null,CLS_ORDER );
		$notify_port = false; // Par défaut aucune notification est envoyé
		while( $f = ria_mysql_fetch_array($fields) ){
			if( $f['type_id']==FLD_TYPE_SELECT_MULTIPLE && !isset($_POST['fld'.$f['id']]) )
				$_POST['fld'.$f['id']] = '';
			if( isset($_POST['fld'.$f['id']]) ){
				$value = $_POST['fld'.$f['id']];
				fld_object_values_set( $_GET['ord'], $f['id'], $value );

				//Verification si un champs avancé lié au frais de port est modifier
				if( $f['id'] == FLD_PRODUCT_FDP_1|| $f['id'] == FLD_PRODUCT_FDP_2 ){
					$notify_port = true;
				}
			}
		}
		if(gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_PORT') && $notify_port){
			$res = ord_order_notify_port($_GET['ord']);
			if(!$res){
				$error = sprintf(_("Erreur lors de l'envoi de la notification."), $_GET['ord']);
			}

		}
		header('Location: /admin/orders/order.php?ord='.$_GET['ord']);
		exit;
	}*/

	// Défini le titre de la page
	$num = $bl['piece']!='' ? $bl['piece'] : str_pad( $bl['id'], 8, '0', STR_PAD_LEFT );
	define('ADMIN_PAGE_TITLE', sprintf(_('Bon de livraison %s - Commandes'), $num ));
	require_once('admin/skin/header.inc.php');

?>
<h2><?php
	print view_bl_is_sync($bl);
	printf( ' '._('Pièce n°%d du %s'), str_pad( $bl['id'], 8, '0', STR_PAD_LEFT ), ria_date_format($bl['date_en']));
?></h2>

<?php
	if( isset($error) ){
		print '<div class="error">'.htmlspecialchars($error).'</div>';
	}
	if( isset($success) ){
		print '<div class="error-success">'.htmlspecialchars($success).'</div>';
	}

	$usr = array();
	$prc = array('money_code' => '');
	$rusr = gu_users_get($bl['usr_id']);
	if( $rusr && ria_mysql_num_rows($rusr) ){
		$usr = ria_mysql_fetch_array($rusr);
	}

?>
<form method="post" action="bl.php?bl=<?php print urlencode($_GET['bl']); ?>">
<table id="table-une-commande" class="tb_invoice">
<thead>
	<tr>
		<th colspan="2" class="multi-colspan"><?php print _('Propriétés générales'); ?></th>
	</tr>
</thead>
<tbody>
	<?php if( trim($bl['piece']) ){ ?>
		<tr>
			<td class="col125px"><?php print _('Numéro de pièce :')?></td>
			<td><?php print view_bl_is_sync( $bl ).'&nbsp;'.htmlspecialchars($bl['piece']); ?></td>
		</tr>
	<?php }	?>
	<?php if( trim($bl['ref']) ){ ?>
		<tr>
			<td class="col125px"><?php print _('Référence :')?></td>
			<td><?php print htmlspecialchars($bl['ref']); ?></td>
		</tr>
	<?php }	?>
	<tr id="ord-adresses-row">
		<td><?php print _('Compte client :'); ?></td>
		<td>
			<div id="ord-addresses">

				<div id="ord-addresses-compte-client">
					<span id="compte-client-name">
						<?php
						if( $bl['usr_id'] ){
							$ref = gu_users_get_ref($bl['usr_id']);
							if( $ref===false ){
								$ref = str_pad( $bl['usr_id'], 8, '0', STR_PAD_LEFT );
								print '<span class="barre" title="'._('Ce compte client a été supprimé').'">'.htmlspecialchars( $ref ).'</span>';
							}else{
								if( !$ref ){
									$ref = str_pad( $bl['usr_id'], 8, '0', STR_PAD_LEFT );
								}
								print '<a href="/admin/customers/edit.php?usr='.$bl['usr_id'].'">';
								print view_usr_is_sync( $usr ).'&nbsp;'.htmlspecialchars( $ref );
								print '</a>';
							}
						}
						?>
					</span>
				</div>

			</div>
		</td>
	</tr>

	<?php if( $seller ){ // Affiche le représentant s'il y en a un ?>
		<tr>
			<td><?php print _('Représentant :'); ?></td>
			<td><?php
				print '<a href="/admin/customers/edit.php?usr='.$seller['id'].'">';
					print view_usr_is_sync( $seller ).'&nbsp;'.htmlspecialchars( $seller['adr_firstname'].' '.$seller['adr_lastname'].' '.$seller['society'] );
				print '</a>';
			?></td>
		</tr>
	<?php } ?>

	<?php if( trim($bl['dps_id']) && $bl['piece']!='' ){ ?>
		<tr>
			<td><?php print _('Dépôt :')?></td>
			<td><?php print htmlspecialchars( prd_deposits_get_name( $bl['dps_id'] ) ); ?></td>
		</tr>
	<?php }elseif( trim($bl['dps_id']) && $bl['piece']=='' ){ ?>
		<tr>
			<td><label for="dps_id"><?php print _('Dépôt :')?></label></td>
			<td><select name="dps_id" id="dps_id">
				<?php
					$deposits = prd_deposits_get();
					while( $dps = ria_mysql_fetch_array($deposits) ){
						print '<option value="'.$dps['id'].'"'.( $dps['id']==$bl['dps_id'] ? ' selected="selected"' : '' ).'>'.htmlspecialchars( $dps['name'] ).'</option>';
					}
				?>
				</select>
				<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
			</td>
		</tr>
	<?php } ?>
	<tr>
		<th colspan="2" class="multi-colspan"><?php print _('Articles'); ?></th>
	</tr>
	<tr class="order-products-row" data-ord="<?php print htmlspecialchars($bl['id']) ?>">

		<td colspan="2">
		<table id="ord-products-articles">
			<thead class="th-head-second thead-none">
				<tr>
					<th class="col125px"><?php print _('Référence'); ?></th>
					<th class="th-prd-comment"><?php print _('Désignation'); ?></th>
					<th class="col100px align-right"><?php print _('Prix Unitaire'); ?></th>
					<th class="col80px align-right"><?php print _('Quantité'); ?></th>
					<th class="col80px align-right"><?php print _('Total'); ?></th>
				</tr>
			</thead>
			<tbody>
			<?php
				// Affiche la liste des produits contenus dans cette pièce
				$rbl_products = ord_bl_products_get( $bl['id'] );
				while( $prd = ria_mysql_fetch_assoc($rbl_products) ){
					print '<tr class="ord-prd-row">';
					if( $prd['id']!=0 ){

						$categories = prd_products_categories_get($prd['id']);
						$is_colisage = prd_colisage_classify_exists($prd['id']);
						$colisage_id = 0;
						if ($is_colisage && $colisage_id = fld_object_values_get(array($bl['id'], $prd['id'], $prd['line']), _FLD_PRD_COL_ORD_PRODUCT) ){
							$r_colisage = prd_colisage_types_get(parseInt($colisage_id));
							$colisage = ria_mysql_fetch_assoc($r_colisage);
						} else {
							$is_colisage = false;
						}

						print '     <td colspan="2" class="multi-colspan">
										<div class="ord-prd-ref-name">
											<div class="ord-prd-nmc-row-main">';

						// Affiche la référence du produit
						print '<span class="td-prd-ref-name-1">';
						if( $cat = ria_mysql_fetch_array($categories) ) {
							print '     <a href="/admin/catalog/product.php?prd='.$prd['id'].'&amp;cat='.$cat['cat'].'">'.htmlspecialchars($prd['ref']).'</a>';
						} else {
							print htmlspecialchars($prd['ref']);
						}
						print '</span>';

						// Désignation du produit et commentaires éventuels
						print '<span id="padding-top-5">
								<span class="ord-prd-name-txt">'.htmlspecialchars($prd['name']).' '.($is_colisage ? ' - '.htmlspecialchars($colisage['name']).' ('.parseInt($colisage['qte']).')' : '') .'</span>';
						print '</span>';

						print ' </div>';

						print '
							</div>
						</td>'; //Fin div ord-prd-ref-name

						// Prix unitaire
						print ' <td class="align-right td-prd-prix-unitaire">
									<span class="ord-prd-price-txt">'.ria_number_format($prd['price_ht'], NumberFormatter::CURRENCY, 2, $prc['money_code']).'</span>';
						print ' </td>';

						// Quantité
						print '<td class="align-right td-prd-quantite">
									<span class="ord-prd-qte-txt">'.ria_number_format( $prd['qte'] ).'</span>';
						print ' </td>';

						// Total HT de la ligne
						print '<td class="align-right td-prd-total">'.ria_number_format($prd['total_ht'], NumberFormatter::CURRENCY, 2, $prc['money_code']).'</td>';

					}else{

						// Commentaires
						print '<td colspan="5">'.nl2br( htmlspecialchars($prd['notes']) ).'</td>';

					}
					print '</tr>';
				}
			?>
			</tbody>
			<tfoot>
				<tr>
					<th colspan="3" class="align-right"><?php print _('Total HT :'); ?></th>
					<td colspan="2" class="align-right"><?php print ria_number_format($bl['total_ht'], NumberFormatter::CURRENCY, 2, $prc['money_code']); ?></td>
				</tr>
				<tr>
					<th colspan="3" class="align-right"><?php print _('TVA :'); ?></th>
					<td colspan="2" class="align-right"><?php print ria_number_format($bl['total_ttc']-$bl['total_ht'], NumberFormatter::CURRENCY, 2, $prc['money_code']); ?></td>
				</tr>
				<tr>
					<th colspan="3" class="align-right"><?php print _('Total TTC :'); ?></th>
					<td colspan="2" class="align-right"><?php print ria_number_format($bl['total_ttc'], NumberFormatter::CURRENCY, 2, $prc['money_code']); ?></td>
				</tr>
			</tfoot>
		</table>
	</td>


	</tr>
</tbody>
<?php if( $bl['piece']=='' ){ ?>
<tfoot>
	<tr>
		<td colspan="2">
			<input type="submit" name="delete" value="<?php print _('Supprimer'); ?>" class="button-del float-left" />
 		</td>
	</tr>
</tfoot>
<?php } ?>
</table>
</form>

<?php

require_once('admin/skin/footer.inc.php');