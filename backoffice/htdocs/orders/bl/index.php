<?php
/**	\file index.php
 *	Cette page affiche une liste de bons de livraison
 */

require_once('fields.inc.php');
require_once('orders.inc.php');
require_once('ord/bl.inc.php');
require_once('view.admin.inc.php');

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

$ar_payments = ord_payment_types_get_array();

if (isset($_GET['seller']) && is_numeric($_GET['seller'])) {
	$_SESSION['ord_seller_id'] = $_GET['seller'];
}

if (isset($_GET['dps']) && is_numeric($_GET['dps'])) {
	$_SESSION['ord_dps_id'] = $_GET['dps'];
}

// Comme c'est pas fait via de l'ajax, il faut ajouter ces fonctions pour bien prendre en compte la date envoyée
// La partie la plus important c'est le view_date_session, parce qu'elle sauvegarde dans une variable la période
// Et l'initialisation du riadatepicker se fait avec la variable
if (isset($_GET['date1'], $_GET['date2'])) {
	$_SESSION['datepicker_date1'] = $_GET['date1'];
	$_SESSION['datepicker_date2'] = $_GET['date2'];
	view_date_in_session($_GET['date1'], $_GET['date2']);
}

if (isset($_GET['period'])) {
	$_SESSION['datepicker_period'] = $_GET['period'];
}


	// Variable pour la mise en place des périodes
$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
$_SESSION['datepicker_period'] = isset($_SESSION['datepicker_period']) ? $_SESSION['datepicker_period'] : _('Aujourd\'hui');
$_SESSION['ord_pay_id'] = isset($_SESSION['ord_pay_id']) ? $_SESSION['ord_pay_id'] : 0;
$_SESSION['ord_dps_id'] = isset($_SESSION['ord_dps_id']) ? $_SESSION['ord_dps_id'] : 0;
$_SESSION['ord_seller_id'] = isset($_SESSION['ord_seller_id']) ? $_SESSION['ord_seller_id'] : 0;

if (isset($_GET["date1"], $_GET["date2"])) {
	$date1 = ria_mysql_escape_string(dateheureparse($_GET["date1"]));
	$date2 = ria_mysql_escape_string(dateheureparse($_GET["date2"]));
}

// Calcule le nombre de bons de livraison
$rbls = false;

if( isset($_GET['ref-bl']) && trim($_GET['ref-bl']) != '' ){
	// Recherche par numéro de pièce (code ERP)
	$rbls = ord_bl_get( 0, 0, false, false, false, [], $_GET['ref-bl'] );

	// Si aucun BL n'est trouvé, on recherche par ID RiaShop si la réf fournie est numérique uniquement
	if( !$rbls || !ria_mysql_num_rows($rbls) ){
		if( is_numeric($_GET['ref-bl']) && $_GET['ref-bl'] > 0 ){
			$rbls = ord_bl_get( $_GET['ref-bl'] );
		}
	}
}else{
	$rbls = ord_bl_get(0, 0, false, false, false, array(), false, array(), $date1, $date2, false, 0, $_SESSION['ord_seller_id'], $_SESSION['ord_dps_id']);
}

$rbls_count = $rbls ? ria_mysql_num_rows($rbls) : 0;

// Calcule le montant total des bons de livraison
$bl_ids = array();
$totals = array('total_ht' => 0, 'total_ttc' => 0);
if ($rbls_count) {
	while ($bl = ria_mysql_fetch_assoc($rbls)) {
		$bl_ids[] = $bl['id'];
		$totals['total_ht']  += $bl['total_ht'];
		$totals['total_ttc'] += $bl['total_ttc'];
	}

	ria_mysql_data_seek($rbls, 0);
}

$key = array(
	'id' => is_array($bl_ids) ? $bl_ids : '',
	'ref' => '',
	'piece' => '',
);

$filter = array(
	'dps_id' 		=> $_SESSION['ord_dps_id'],
	'seller_id' 	=> $_SESSION['ord_seller_id'],
);

$period = array(
	'start' => $date1,
	'end' => $date2,
);

// Récupère les devises utilisées
$currencies = prd_prices_get_all_currencies();

// Export des bons de livraison
$msg = null;
if (isset($_GET['export'])) {
	ria_mysql_data_seek($rbls, 0);

	$file_name = 'bons-de-livraison-' . time() . '.xls';
	$file_csv = $config['doc_dir'] . '/' . $file_name;

	$exp_id = exp_exports_add(CLS_BL, $file_csv, $file_name);
	try {
		// Ajoute l'import dans la file d'attente
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_EXPORT_GENERATE, array(
			'wst_id' => $config['wst_id'],
			'cls_id' => CLS_BL,
			'exp_id' => $exp_id,
			'key' => $key,
			'filter' => $filter,
			'period' => $period,
		));
		$_SESSION['msg']['text'] = _('Votre demande d\'export a bien été prise en compte, vous pouvez suivre son traitement depuis <a href="/admin/tools/exports/index.php" id="link-exports"> Outils > Exports</a>.');
		$_SESSION['msg']['class'] = 'success';
	} catch (Exception $e) {
		$_SESSION['msg']['text'] = _('Une erreur est survenue lors de l\'export');
		$_SESSION['msg']['class'] = 'error';
		error_log(__FILE__ . ':' . __LINE__ . ' ' . $e->getMessage());
	}
	header('Location: /admin/orders/bl/index.php');
	exit;
}

// Compose le titre de la page
$title = _('Tous les bons de livraison');
/*if( $_GET['state']!=0 ){
		switch ($_GET['state'] ) {
			case _STATE_BASKET:
				$title = _('Paniers');
				break;
			default:
				$title = _('Commandes '). mb_strtolower($ar_states[$_GET['state']]['name_pl'], 'UTF-8');
				break;
		}
	}*/

// Défini le titre de la page
define('ADMIN_PAGE_TITLE', $title . ' - ' . _('Commandes'));
require_once('admin/skin/header.inc.php');
?>


<form action="index.php" method="get"><?php
	print '<h2 id="title-state">';
	print '<input class="btn-move float-right hide-mobile" type="submit" name="export" value="' . _('Exporter') . '" title="' . _('Exporter la liste des bons de livraison au format Microsoft Excel') . '" />';
	print htmlspecialchars($title) . ' (' . number_format($rbls_count, 0, ',', ' ') . ')';
	print '</h2>';

	if (isset($_SESSION['msg'])) {
		?><div class="<?php print $_SESSION['msg']['class']; ?>"><?php print $_SESSION['msg']['text']; ?></div><?php
		unset($_SESSION['msg']);
	}

	// Calcul des paramètres de pagination
	$by_page = 25;

	$_GET['page'] = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] ? $_GET['page'] : 1;
	$pages = ceil($rbls_count / $by_page);
	if ($_GET['page'] > $pages) {
		$_GET['page'] = $pages;
	}

	if ($_GET['page'] > 1) {
		ria_mysql_data_seek($rbls, ($_GET['page'] - 1) * $by_page);
	}

	// Dates de début et de fin
	if (isset($_GET['date1'], $_GET['date2'], $_GET['period'])) {
		print '
			<input type="hidden" name="date1" id="date1" value="' . $_GET['date1'] . '"/>
			<input type="hidden" name="date2" id="date2" value="' . $_GET['date2'] . '"/>
			<input type="hidden" name="last" id="last" value="' . $_GET['period'] . '"/>
		';
	}
?>

	<div class="stats-menu">
		<div class="ria-admin-ui-filters">
			<div id="riadatepicker"></div>
			<?php
			// Filtre sur le représentant
			print view_sellers_selector();

			// Filtre sur les dépôts
			print view_deposits_selector($config['tnt_id']);
			?>
		</div>
		<div class="clear"></div>
	</div>

	<div>
		<label for="ref-bl"><?php print _('Recherche par numéro de pièce ou identifiant :'); ?></label>
		<input type="text" name="ref-bl" id="ref-bl" value="<?php print isset($_GET['ref-bl']) ? htmlspecialchars($_GET['ref-bl']) : ''; ?>" />
		<input type="submit" name="sch-ref-bl" value="<?php print _('Rechercher'); ?>" />
	</div>

	<hr />

	<table class="list" id="table-liste-commandes">
		<caption class="thead-title-mobile"><?php print _('Bons de livraison') ?></caption>
		<thead class="thead-none">
			<tr>
				<th id="ord-id"><?php print _('Numéro') ?></th>
				<th id="ord-date"><?php print _('Date') ?></th>
				<th id="ord-inv"><?php print _('Adresse de facturation') ?></th>
				<th id="ord-livr"><?php print _('Adresse de livraison') ?></th>
				<th id="ord-type-pay"><?php print _('Moyen de paiement') ?></th>
				<th id="ord-state"><?php print _('Etat/Statut') ?></th>
				<th id="ord-ht"><?php print _('Total H.T.') ?></th>
				<th id="ord-ttc"><?php print _('Total T.T.C.') ?></th>
			</tr>
		</thead>
		<tbody id="lst_orders">
			<?php
			// Affichage des bons de livraison
			if (!$rbls_count) {
				print '<tr><td colspan="8">' . _('Aucun bon de livraison ne correspond à vos critères de sélection.') . '</td></tr>';
			} else {
				$count = 0;
				while (($r = ria_mysql_fetch_assoc($rbls)) && $count < $by_page) {
					$user = array();
					$prc = array('money_code' => '');
					$r_user = gu_users_get($r['usr_id']);

					if ($r_user && ria_mysql_num_rows($r_user)) {
						$user = ria_mysql_fetch_assoc($r_user);

						$prc = ria_mysql_fetch_assoc(
							prd_prices_categories_get($user['prc_id'])
						);
					}

					$count++;

					if(isset($inv_address) ){
						unset($inv_address);
					}

					$rinv_address = gu_adresses_get($r['usr_id'], $r['adr_invoices']);
					if (ria_mysql_num_rows($rinv_address)) {
						$inv_address = ria_mysql_fetch_array($rinv_address);
					}

					if( isset($dlv_address) ){
						unset($dlv_address);
					}

					$rdlv_address = gu_adresses_get($r['usr_id'], $r['adr_dlv_id']);
					if (ria_mysql_num_rows($rdlv_address)) {
						$dlv_address = ria_mysql_fetch_array($rdlv_address);
					}

			?>
					<tr>
						<td headers="ord-id" id="td-ord-id">
							<?php print view_bl_is_sync($r); ?>
							<a href="bl.php?bl=<?php echo $r['id'] ?>" title="<?php print _('Afficher la fiche de ce bon de livraison') ?>"><?php echo htmlspecialchars($r['piece'] ? $r['piece'] : str_pad($r['id'], 8, '0', STR_PAD_LEFT)); ?></a>
						</td>
						<td headers="ord-date" class="align-right"><?php echo ria_date_format($r['date_en']); ?></td>
						<td headers="ord-inv">
							<?php
							if (isset($inv_address)) {
								print '<span class="td-caption">' . _('Adresse de facturation :') . '</span>';
								if ($inv_address['society']) print htmlspecialchars($inv_address['society']) . '<br />';

								if ($inv_address['title_name']) print $inv_address['title_name'] . ' ';
								if ($inv_address['lastname']) print htmlspecialchars($inv_address['lastname']);
								if ($inv_address['firstname']) print ', ' . htmlspecialchars($inv_address['firstname']) . '<br />';
								if ($inv_address['address1']) print htmlspecialchars($inv_address['address1']) . '<br />';
								if ($inv_address['address2']) print htmlspecialchars($inv_address['address2']) . '<br />';
								if ($inv_address['address3']) print htmlspecialchars($inv_address['address3']) . '<br />';
								print htmlspecialchars($inv_address['postal_code']) . ' ' . htmlspecialchars($inv_address['city']) . '<br />';
								print htmlspecialchars($inv_address['country']) . '<br />';
							}
							if (isset($r['usr_id'])) {
								$email = gu_users_get_email($r['usr_id']);
								if ($email) {
									print '<a href="mailto:' . htmlspecialchars($email) . '" target="_top">' . htmlspecialchars($email) . '</a>';
								}
							}
							?>
						</td>
						<td headers="ord-livr">
							<?php
							print '<span class="td-caption">' . _('Adresse de livraison :') . '</span>';

							if (isset($dlv_address)) {
								if ($dlv_address['society']) print htmlspecialchars($dlv_address['society']) . '<br />';

								if ($dlv_address['title_name']) print $dlv_address['title_name'] . ' ';
								if ($dlv_address['lastname']) print htmlspecialchars($dlv_address['lastname']);
								if ($dlv_address['firstname']) print ', ' . htmlspecialchars($dlv_address['firstname']) . '<br />';
								if ($dlv_address['address1']) print htmlspecialchars($dlv_address['address1']) . '<br />';
								if ($dlv_address['address2']) print htmlspecialchars($dlv_address['address2']) . '<br />';
								if ($dlv_address['address3']) print htmlspecialchars($dlv_address['address3']) . '<br />';
								print htmlspecialchars($dlv_address['postal_code']) . ' ' . htmlspecialchars($dlv_address['city']) . '<br />';
								print htmlspecialchars($dlv_address['country']);
							}
							?>
						</td>
						<td headers="ord-type-pay"><?php print $r['pay_id'] ? '<span class="td-caption td-caption--inline">' . _('Moyen de paiement :') . '</span>' . htmlspecialchars($ar_payments[$r['pay_id']]['name']) : ''; ?></td>
						<td headers="ord-state"><?php print '<span class="td-caption td-caption--inline">' . _('Etat/Statut :') . '</span>' . _($r['state_name']) ?></td>
						<td headers="ord-ht"><?php print '<span class="td-caption td-caption--inline">' . _('Total HT :') . '</span>' . ria_number_format($r['total_ht'], NumberFormatter::CURRENCY, 2, 'EUR') ?></td>
						<td headers="ord-ttc"><?php print '<span class="td-caption td-caption--inline">' . _('Total TTC :') . '</span>' . ria_number_format($r['total_ttc'], NumberFormatter::CURRENCY, 2, 'EUR') ?></td>
					</tr>
			<?php
				}
			}
			?>
		</tbody>
		<tfoot>
			<tr id="pagination" <?php print $pages > 1 ? '' : 'style="display: none"'; ?>>
				<td colspan="2" class="align-left">
					<?php
					print 'Page ' . $_GET['page'] . '/' . $pages;
					?>
				</td>
				<td colspan="6">
					<?php
					if ($pages > 1) {
						$page_start = $_GET['page'] > 5 ? $_GET['page'] - 5 : 1;
						$page_stop = $_GET['page'] + 5 < $pages ? $_GET['page'] + 5 : $pages;

						$links = array();
						if ($_GET['page'] > 1) {
							$links[] = '<a href="/orders/bl/?page=' . ($_GET['page'] - 1) . '">&laquo; ' . _('Page précédente') . '</a>';
						}
						for ($i = $page_start; $i <= $page_stop; $i++) {
							if ($i == $_GET['page']) {
								$links[] = '<b>' . $i . '</b>';
							} else {
								$links[] = '<a href="/orders/bl/?page=' . $i . '">' . $i . '</a>';
							}
						}
						if ($_GET['page'] < $pages) {
							$links[] = '<a href="/orders/bl/?page=' . ($_GET['page'] + 1) . '">' . _('Page suivante') . ' &raquo;</a>';
						}
						print implode(' | ', $links);
					}
					?>
				</td>
			</tr>
			<tr class="ord-totals">
				<td colspan="3"></td>
				<th class="align-right"><?php print _('Totaux :'); ?></th>
				<td colspan="2" class="align-right"><span><?php print number_format($totals['total_ht'], 2, ',', ' '); ?></span> <?php print _('HT'); ?> </td>
				<td colspan="2" class="align-right"><span><?php print number_format($totals['total_ttc'], 2, ',', ' '); ?></span> <?php print _('TTC'); ?> </td>
			</tr>
			<tr class="js-btn-export" style="display: none">
				<td colspan="8">
					<input class="btn-move" type="submit" name="export" id="export" value="<?php print _('Exporter'); ?>" title="<?php print _('Exporter la liste des bons de livraison au format Microsoft Excel'); ?>" />
				</td>
			</tr>
		</tfoot>
	</table>
</form>

<script>
	var riadatepicker_upd_url = '';
	<?php
	view_date_initialized($rbls_count, '/orders/bl/', false, array('refresh_in_ajax' => true, 'load_popup_new_orders' => false));
	?>


	$(document).ready(function() {
		// Active le filtre par représentant
		$('#selectseller .selectorview,#selectseller .selector').mouseup(function() {
			if ($('#selectseller .selector').css('display') == 'none') {
				$('#selectseller .selector').show();
			} else {
				$('#selectseller .selector').hide();
			}
		});

		// Active le filtre par dépôt de stockage
		$('#selectdeposit .selectorview,#selectdeposit .selector').mouseup(function() {
			if ($('#selectdeposit .selector').css('display') == 'none') {
				$('#selectdeposit .selector').show();
			} else {
				$('#selectdeposit .selector').hide();
			}
		});

	}).delegate('#selectseller .selector a', 'click', function() {
		let seller_id = 'seller=' + $(this).attr('name').replace('seller-', '');
		window.location = 'index.php?' + seller_id;
	}).delegate('#selectdeposit .selector a', 'click', function() {
		let dps_id = 'dps=' + $(this).attr('name').replace('dps-', '');
		window.location = 'index.php?' + dps_id;
	});

	// Tableau contenant toutes les étapes d'une commande (version au pluriel).
	//var tstate = [];

	<?php
	/* if( $r_states = ord_states_get() ){
			while( $state = ria_mysql_fetch_assoc($r_states) ){
				print 'tstate['.$state['id'].'] = \''.str_replace("'", "\'", $state['name_plural']).'\';'."\n";
			}
		} */
	?>
</script>
<?php

require_once('admin/skin/footer.inc.php');
