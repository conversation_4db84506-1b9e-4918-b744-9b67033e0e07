<?php
  /**	\file orders.php
	 *	Cette page affiche une liste de commandes, filtrées sur un état passé via le paramètre $_GET['state']
	 */

	require_once('fields.inc.php');
	require_once('orders.inc.php');
	require_once('gu.categories.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	if( !isset($_SESSION['beta-orders-listing-actived']) ){
		header('Location: /admin/orders/orders.php'.(isset($_GET['state']) ? '?state='.$_GET['state'] : ''));
		exit;
	}

	{ // Gestion des paramètres
		$ref = isset($_GET['ref']) ? trim($_GET['ref']) : '';

		$ar_origin = '';
		if( isset($_SESSION['origin']) ){
			$ar_origin = $_SESSION['origin'];
		}
	}

	{ // Gestion des filtres automatique
		{ // Statut des commandes retournées dans le listing
			$ar_state_ids = [];

			if( !isset($_GET['state']) || !ord_states_exists($_GET['state']) ){
				$_GET['state'] = 0; // Par défaut, "Toutes les commandes" = les commandes validées
			}

			if( $_GET['state'] == 0 ){
				$ar_state_ids = array_merge( $ar_state_ids, ord_states_get_ord_valid(), [_STATE_SUPP_PARTIEL_INV] );

				if( in_array($config['tnt_id'], [977, 998, 1043]) ){
					$ar_state_ids[] = _STATE_VALIDATE;
					$ar_state_ids[] = _STATE_SUPP_WAIT_CONFIRM;
				}
			}else{
				$ar_state_ids = is_array($_GET['state']) ? $_GET['state'] : [$_GET['state']];
			}
		}
	}

	// Détermine la date de début de période à partir de l'url et des données de session.
	// La valeur par défaut est la date du jour.
	if( isset($_GET['date1']) ){
		$date1 = dateparse( $_GET['date1'] );
	}elseif( isset($_SESSION['datepicker_date1']) ){
		$date1 = dateparse( $_SESSION['datepicker_date1'] );
	}else{
		$date1 = date('Y-m-d');
	}

	// Détermine la date de fin de période à partir de l'url et des données de session.
	// La valeur par défaut est la date du jour.
	if( isset($_GET['date2']) ){
		$date2 = dateparse( $_GET['date2'] );
	}elseif( isset($_SESSION['datepicker_date2']) ){
		$date2 = dateparse( $_SESSION['datepicker_date2'] );
	}else{
		$date2 = date('Y-m-d');
	}

	require_once('view/view.orders.inc.php');
	$ar_cols = view_admin_orders_get_columns( ['group_id' => SORT_ASC, 'position' => SORT_ASC, 'name' => SORT_ASC] );
	$ar_filters = view_admin_orders_get_filters( $ar_state_ids, ['start' => $date1, 'end' => $date2] );

	// Détermine le fil d'Ariane
	$breadcrumbs = Breadcrumbs::root( 'Accueil', '/admin/index.php' );
	$breadcrumbs->push( _('Commandes'), '/admin/orders/orders.php' );
	if( is_numeric($_GET['state']) && $_GET['state'] > 0 ){
		$breadcrumbs->push( ord_states_get_name($_GET['state']) );
	}else{
		$breadcrumbs->push( _('Toutes les commandes') );
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Toutes les commandes').' - '._('Commandes'));
	require_once('admin/skin/header.inc.php');

	print '<form action="orders.php" method="get">'
		.'<h2 id="title-state">'
			._('Toutes les commandes')
			.'<a class="edit-cat" id="display-filters-options" href="#">'._('Personnaliser les filtres').'</a>'
		.'</h2>';

		{ // Paramètres des filtres appliqués sur le listing
			print '<div id="ajax_orders_filters">'
					.'<input type="hidden" name="table-search" value="'.htmlspecialchars( $ref ).'" />'
					.'<input type="hidden" name="date1" id="date1" value="'.htmlspecialchars( $date1 ).'" />'
					.'<input type="hidden" name="date2" id="date2" value="'.htmlspecialchars( $date2 ).'" />'
					.'<input type="hidden" name="origin" id="origin" value="'.htmlspecialchars( is_array($ar_origin) ? implode(',', $ar_origin) : $ar_origin ).'" />';

					foreach( $ar_state_ids as $one_state ){
						print '<input type="hidden" name="state[]" value="'.htmlspecialchars( $one_state ).'" />';
					}

					foreach( $ar_filters as $one_filter ){
						$value = '';
						if( isset($_GET[ $one_filter['id'] ]) ){
							$value = $_GET[ $one_filter['id'] ];
						}

						print '<input type="hidden" name="'.htmlspecialchars( $one_filter['id'] ).'" value="'.htmlspecialchars( $value ).'" />';
					}

				print '</div>';
		}

		{ // Filtre sur le listing
			print '<div class="stats-menu">'
				.'<div id="riadatepicker"></div>'
				.'<div class="ajax-load-filters"></div>'
				.'<div class="clear"></div>'
			.'</div>';
		}

		// Statisques globaux sur les commandes
		print '<div id="ajax_stats_orders"></div>';

		print '<div id="select-columns-orders" class="with-cols-options hidden">'
			.'<a href="#" id="display-cols-options" class="edit-cat"> '._('Personnaliser colonnes').'</a>'
			.'<span class="menu-cols none">'
				.'<span class="cols">'
					.'<span class="col">'
						.'<input type="checkbox" name="cols-all" id="cols-all" value="all" />'
						.'<label for="cols-all">'._('Toutes les colonnes').'</label>'
					.'</span>'

					.'<span class="col separate"></span>';

					$last_group = ''; $first_group = true;
					foreach( $ar_cols as $one_col ){
						if( $last_group != $one_col['group'] ){
							print '<span class="group '.( !$first_group ? ' next-group' : '' ).'">'
								.'<strong>'.htmlspecialchars( $one_col['group'] ).'</strong>'
							.'</span>';
							$first_group = false;
						}

						print'<span class="col '.( in_array( $one_col['id'], $ar_cols_actived ) ? 'checked' : '' ).'">'
							.'<input '.( in_array( $one_col['id'], $ar_cols_actived ) ? 'checked="checked"' : '' ).' type="checkbox" name="cols-'.$one_col['id'].'" id="cols-'.$one_col['id'].'" value="'.$one_col['id'].'" />'
							.'<label for="cols-'.$one_col['id'].'"> '.htmlspecialchars( $one_col['name'] ).'</label>'
						.'</span>';

						$last_group = $one_col['group'];
					}

				print '</span>'
			.'</span>'
		.'</div>'

		// Tableau vide, celui-ci est rempli avec DataTable + un chargement des données en Ajax
		// Il s'agit de la structure HTML minimal à avoir pour le bon fonctionnement de DataTable
		.'<table style="min-width:100%;" id="ajax_table_orders">'
			.'<caption class="thead-title-mobile">'._('Commandes').'</caption>'
			.'<thead>'

			.'</thead>'
		.'</table>'

		.'<br /><br />'
		.'<div id="notice-show-orders" class="notice hidden">'._('Seules les commandes des 3 dernières années sont retournées').'</div>'
	.'</form>';
?>

<style>
	.selector {
		max-height: 260px;
	}
	[id="riadatepicker"], [id="selectorigins"] {
		float: left;
	}
</style>

<script>
	<?php view_date_initialized( 0, '', false, ['callback' => 'reloadOrdersData']); ?>
	var timer_savePositionCols = false;
	var tableOrdersAjax = false;
	var firstLoad = true;
	var actualCodeColsSelected = [];
	var lastOrigin = '';

	function loadStatsOrders(){
		// Paramètre de chargement des données
		var parameters = $('#ajax_orders_filters').find('input').serializeJSON();
		parameters.loadstats = 'on';

		let newOrigin = getOriginValues();
		parameters.origin = newOrigin;
		lastOrigin = newOrigin.toLocaleString();

		$.ajax({
			url: '/ajax/orders/list-orders.php',
			data: parameters,
			method: 'get',
			dataType: 'json'
		}).done(function( json ){
			if( json.data.synthese.length ){
				$('#ajax_stats_orders').html( json.data.synthese );
			}
		});
	}

	function loadTableOrders(){
		$('.load-ajax-opacity').show();

		// Masque la personnalisation des colonnes et la notice
		$('#select-columns-orders, #notice-show-orders').addClass('hidden');

		// Charge le tableau des commandes
		$.ajax({
			url: '/ajax/orders/list-orders.php?loadconfig=on',
			method: 'get',
			dataType: 'json'
		}).done(function( json ){
			let orderColPos = 0;

			{ // Charge l'entête du tableau
				var headHtml = '<tr>';

				for (let index = 0; index < json.data.header.length; index++) {
					const element = json.data.header[index];
					headHtml += '<th data-key-col="' + element.id + '">' + element.name + '</th>';
				}

				headHtml += '</tr>';

				if (json.data.sortcol) {
					orderColPos = json.data.sortcol;
				}
			}

			// Chargement des statistiques
			loadStatsOrders();

			// Réinitialise le tableau si besoin
			if( tableOrdersAjax !== false ){
				tableOrdersAjax.destroy();
				tableOrdersAjax = false;

				$('#ajax_table_orders thead, #ajax_table_orders tbody').html('');
			}

			{ // Charge le contenu du tableau
				$('.load-ajax-opacity').hide();
				$('#ajax_table_orders').find('thead').html( headHtml );

				// Paramètre de chargement des données
				var parameters = $('#ajax_orders_filters').find('input').serializeJSON();
				parameters.loaddata = 'on'

				let newOrigin = getOriginValues();
				parameters.origin = newOrigin;
				lastOrigin = newOrigin.toLocaleString();

				let nbRows = parseInt( localStorage.getItem('datatable-orders-listing-numberrows') );
				if( isNaN(nbRows) ){
					nbRows = 25;
				}

				tableOrdersAjax = $('#ajax_table_orders').DataTable( {
					ajax: {
						url: "/ajax/orders/list-orders.php",
						data: parameters,
						complete: function(){ // Action(s) lancée(s) une fois les données récupérées
							searchInTableOrders();

						}
					},
					columns: json.data.columns,

					// Traduction française du plugins
					language: {
						url: "/admin/js/datatables/french.json"
					},

					// Scroll horizontale
					scrollX: true,

					// Choix du nombre de ligne par page
					lengthMenu: [ 5, 10, 25, 50, 75, 100, 200 ],

					// Nombre de ligne par défaut
					pageLength: nbRows,

					// Tri par défaut sur la date
					order: [
						[ orderColPos, "desc" ]
					],

					dom: 'lBfrtip',
					buttons: [
						'csv', 'excel'
					]
				});

				// Permet de trier les colonnes comme on le souhaite
				new $.fn.dataTable.ColReorder( tableOrdersAjax, {
					// options
				} );

				tableOrdersAjax.on( 'column-reorder', function ( e, settings, details ) {
						let changeSortCol = '';
						for(let iCol in settings.aoColumns ){
							if( changeSortCol != '' ){
								changeSortCol += ',';
							}

							changeSortCol += settings.aoColumns[ iCol ].keyCol
						}

						clearTimeout( timer_savePositionCols );

						timer_savePositionCols = setTimeout(function(){
							$.ajax({
								url: '/ajax/orders/list-orders.php',
								method: 'post',
								data: {
									savepositioncols: 'on',
									cols: changeSortCol
								},
								dataType: 'json'
							});
						}, 1000);
				} );

				tableOrdersAjax.on( 'length.dt', function ( e, settings, len ) {
					localStorage.setItem('datatable-orders-listing-numberrows', len);
				} );
			}

			$('[id="notice-show-orders"], [id="select-columns-orders"]').removeClass('hidden');

			// Certaines actions sont réalisées lors du premier chargement
			if( firstLoad ){

			}

			firstLoad = false;
		});
	}

	function searchInTableOrders(){
		var search = $('[name="table-search"]').val();
		if( $.trim(search) != '' && tableOrdersAjax !== false ){
			tableOrdersAjax.search( search ).draw();
		}
	}

	function loadFilters(){
		var parameters = $('#ajax_orders_filters').find('input').serializeJSON();
		parameters.loadfilters = 'on';

		let newOrigin = getOriginValues();
		parameters.origin = newOrigin;
		lastOrigin = newOrigin.toLocaleString();

		$.ajax({
			url: '/admin/ajax/orders/list-orders.php',
			method: 'get',
			data: parameters,
			dataType: 'json'
		}).done(function(json){
			$('.ajax-load-filters').html( json.data.content );

			$('.riapicker:not([id="selectorigins"])').each(function(){
				var riapicker = $(this);

				// Gestion de l'affichage de la liste déroulantes des sélecteurs
				riapicker.find('.selectorview').click(function(){
					$('.riapicker').each(function(){
						if( $(this).attr('id') != riapicker.attr('id') ){
							$(this).find('.selector').hide();
						}
					});

					if( riapicker.find('.selector').is(':visible') ){
						riapicker.find('.selector').hide();
					}else{
						riapicker.find('.selector').show();
					}
				});

				// Gestion du choix d'une valeur des sélecteurs
				riapicker.find('.selector a').click(function(){
					let valueHiddenID = riapicker.attr('id').replace('select-', '');
					let typeSearch = riapicker.data('use-for-search');

					let value = $(this).attr('name').replace('val-', '');
					if( value == 'none' ){
						value = '';
					}else{
						if( typeSearch == 'value' ){
							value = $(this).html();
						}
					}

					$('[name="' + valueHiddenID + '"]').val( value );
					riapicker.find('.view').html( $(this).html() );
					riapicker.find('.selector').hide();

					loadTableOrders();
				});
			});

			$('#selectorigins .selectorview').click(function(){
				if($('#selectorigins .selector').css('display')=='none'){
					$('#selectorigins .selector').show();
				}else{
					$('#selectorigins .selector').hide();

					let newOrigin = getOriginValues();
					if( lastOrigin != newOrigin.toLocaleString() ){
						loadTableOrders();
					}
				}
			});

			$('#selectorigins .selector a').click(function(){
				var attrOrigin = $(this).attr('name');

				if( attrOrigin=='all' || attrOrigin=='noorigin' || attrOrigin=='gescom' || attrOrigin=='web' ){
					$('[name="origin[]"]:checked').removeAttr('checked');
					$('#selectorigins .selectorview .left .view').html($(this).html());
					$('#selectorigins .selector').hide();
					$('[name="origin"]').val( attrOrigin );

					loadTableOrders();
				}else{
					$('[name="origin"]').val('');
					reloadSelectorOrigin();
				}
			});
		});
	}

	/** Cette fonction permet de récupérer les origines choisies pour le listing des commandes.
	 * 	@return array Un tableau contenant les origines choisies
	 */
	function getOriginValues(){
		var origin = [];
		var originText = '';

		if( $('[name="origin"]').val() ){
			origin = $('[name="origin"]').val().split(',');
		}else if ( $('[name="origin[]"]:checked').length ){
			$('[name="origin[]"]:checked').each(function(){
				origin.push( $(this).val() );

				let temp = $(this).parent().find('label').html();
				if( $.trim(originText) != '' ){
					originText += ', ';
				}
				originText += temp;
			});

			$('[id="selectorigins"] .view').html( originText );
		}

		return origin;
	}

	/** Active / Désactive les origines utilisées pour filtrer la liste des commandes dans le sélecteur d'origine
	 */
	function reloadSelectorOrigin(){
		$('.selector .parent').each(function(){
			$(this).find('[type=checkbox]').prop("indeterminate", false).removeAttr('checked');

			var total_check = $(this).parent().find('.child [type=checkbox]').length;
			var total_is_checked = $(this).parent().find('.child [type=checkbox]:checked').length

			if( total_is_checked > 0 ){
				if( total_check != total_is_checked ){
					$(this).find('[type=checkbox]').prop( "indeterminate", true );
				}else{
					$(this).find('[type=checkbox]').attr('checked', 'checked');
				}
			}
		});
	}

	function reloadOrdersData(){
		// Ferme toutes les popup
		hidePopup();

		// Recharge les valeurs des filtres
		loadFilters();

		// Recharge le tableau des commandes
		loadTableOrders();
	}

	$(document).ready(function() {
		$.fn.dataTable.moment( 'D/M/YYYY à HH:mm' );

		// Chargement initial des filtres
		loadFilters();

		// Chargement initial du tableau
		loadTableOrders();

		let arrayLastOrigin = getOriginValues();
		lastOrigin = arrayLastOrigin.toLocaleString();
	}).on( // Gestion de l'affichage du menu "Personnaliser mes colonnes"
		'click', '[id="display-cols-options"]', function(){
			if($('.menu-cols').hasClass('none')){
				$('.menu-cols').removeClass('none');

				// Sauvegarde les colonnes actuellement sélectionnées lors de l'ouverture du menu
				actualCodeColsSelected = [];
				$('[id="select-columns-orders"] input[type="checkbox"]:checked').each(function(){
					actualCodeColsSelected.push( $(this).val() );
				});
			}else{
				$('.menu-cols').addClass('none');
			}
			return false;
		}
	).on(
		'click', '[id="select-columns-orders"] [name="cols-all"]', function(){
			if( $(this).is(':checked') ){
				$('[id="select-columns-orders"] [type="checkbox"]').attr('checked', 'checked');
			}else{
				$('[id="select-columns-orders"] [type="checkbox"]').removeAttr('checked')
				$('[id="select-columns-orders"] [name="cols-ref"]').attr('checked', 'checked');
				$('[id="select-columns-orders"] [name="cols-id"]').attr('checked', 'checked');
				$('[id="select-columns-orders"] [name="cols-piece"]').attr('checked', 'checked');
				$('[id="select-columns-orders"] [name="cols-date"]').attr('checked', 'checked');
				$('[id="select-columns-orders"] [name="cols-adr_inv"]').attr('checked', 'checked');
				$('[id="select-columns-orders"] [name="cols-adr_dlv"]').attr('checked', 'checked');
				$('[id="select-columns-orders"] [name="cols-pay_name"]').attr('checked', 'checked');
				$('[id="select-columns-orders"] [name="cols-total_ht"]').attr('checked', 'checked');
				$('[id="select-columns-orders"] [name="cols-total_ttc"]').attr('checked', 'checked');
			}
		}
	).on(
		'click', '[id="display-filters-options"]', function(){
			var parameters = $('#ajax_orders_filters').find('input').serialize();
			displayPopup( 'Personnaliser les filtres', '', '/admin/ajax/orders/popup-choose-filters.php?' + parameters );
		}
	).click(function(e){
		if( !$(e.target).parents('.riapicker').length ){
			$('.riapicker .selector').hide();
			let newOrigin = getOriginValues();
			if( lastOrigin != newOrigin.toLocaleString() ){
				loadTableOrders();
			}
		}

		if( !$('.menu-cols').hasClass('none') ){
			if( $($(e.target).parents('#select-columns-orders')).length == 0 ){
				// Ferme le menu de sélection des colonnes
				$('.menu-cols').addClass('none');

				// Charge la liste des colonnes sélectionnées dans un tableau
				var listCodeColsSelected = [];
				$('[id="select-columns-orders"] input[type="checkbox"]:checked').each(function(){
					listCodeColsSelected.push( $(this).val() );
				});

				// Sauvegarde la sélection et lance la mise à jour du tableau
				// Seulement si des changements ont été apportées aux colonnes sélectionnées
				if( actualCodeColsSelected.toString() != listCodeColsSelected.toString() ){
					$.ajax({
						url: '/ajax/orders/list-orders.php',
						method: 'post',
						data: {
							saveconfigcols: 'on',
							cols: listCodeColsSelected
						},
						dataType: 'json'
					}).done(function( json ){
						loadTableOrders();
					});

					actualCodeColsSelected = listCodeColsSelected;
				}
			}
		}
	}).on(
		'click', '#ajax_table_orders a', function(){
			let saveUrlToBack  = '/admin/orders/orders-beta.php?';
			saveUrlToBack 		+= $('#ajax_orders_filters').find('input').filter(function(){return this.value != '';}).serialize();

			window.history.pushState( {}, '', saveUrlToBack );
		}
	);
</script>

<?php require_once('admin/skin/footer.inc.php');