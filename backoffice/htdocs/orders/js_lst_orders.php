<?php

	/**	\file Ce fichier retourne une liste de commandes au format XML pour affichage dans le back-office
	 *
	 *	\param $state Etat de commande
	 *	\param $date1 Date de début de période
	 *	\param $date2 Date de fin de période
	 *	\param $origin Filtre sur l'origine de commande
	 *
	 *	La réponse de ce fichier est structurée comme suit :
	 *	\code
	 *	<success>
	 *		<synthese><!-- Tableau de synthèse (Nombre de commandes, Marge brute, etc) --></synthese>
	 *		<html_select><!-- Liste des status de commande --></html_select>
	 *		<noorder /> <!-- Optionnel, uniquement présent s'il n'y a aucune commande -->
	 *		<number /> <!-- Pagination -->
	 *		<order /> <!-- Commande numéro 1 -->
	 *		<order /> <!-- Commande numéro 2 -->
	 *		<!-- ... -->
	 *	</success>
	 *	\code
	 *
	 */


	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	$only_stats = isset($_GET['only_stats']) && $_GET['only_stats'] ? true : false;

	require_once('view.admin.inc.php');
	require_once('orders.inc.php');
	require_once('view.admin.inc.php');
	require_once('comparators.inc.php');
	require_once('relays.inc.php');

	// header('Content-type: application/xml');
	$xml = '<?xml version="1.0" encoding="utf-8"?>
			<success>';

	$_SESSION['origin'] = isset($_GET['origin']) ? $_GET['origin'] : array();
	if( isset($_GET['state'], $_GET['date1'], $_GET['date2']) ){
		view_date_in_session($_GET['date1'], $_GET['date2']);

		// Filtre sur l'origine de la commande
		$params = view_origins_get_params();
		$origin = $params['origin'];
		$gescom = $params['gescom'];
		$is_web = $params['is_web'];

		// traitement du paramètre site web
		$wst_id = 0;
		if( isset($_GET['wst']) && is_array($_GET['wst']) && sizeof($_GET['wst']) ){
			$_SESSION['ar_websitepicker'] = $_GET['wst'];
			$_SESSION['websitepicker'] = $_GET['wst'][0];
			$wst_id = $_GET['wst'];
		}

		if( isset($_GET['ord_website']) ){
			$_SESSION['websitepicker'] = $_GET['ord_website'];
			$wst_id = str_replace('w-', '', $_SESSION['websitepicker']);
		}

		$pay = isset($_GET['pay']) && is_numeric($_GET['pay']) && $_GET['pay'] ? $_GET['pay'] : 0;
		$dps_id = isset($_GET['dps']) && is_numeric($_GET['dps']) && $_GET['dps'] ? $_GET['dps'] : 0;
		$seller = isset($_GET['seller']) && is_numeric($_GET['seller']) && $_GET['seller'] ? $_GET['seller'] : 0;
		$state = $_GET['state']==0 ? ord_states_get_ord_invalid() : false;
		$pmt_id = isset($_GET['pmt_id']) && is_numeric($_GET['pmt_id']) && $_GET['pmt_id'] ? $_GET['pmt_id'] : 0;

		$piece = isset($_GET['piece']) && trim($_GET['piece']) != "" ? $_GET['piece'] : '';
		$ref = isset($_GET['ref']) && is_numeric($_GET['ref']) && $_GET['ref'] ? $_GET['ref'] : 0;

		$ord_ids = array();
		$have_mdl_filter = $have_fld_filter = false;

		{ // Filtre sur modèle de saisie / Champs avancé
			if (isset($_GET['mdl']) && is_numeric($_GET['mdl']) && $_GET['mdl'] > 0) {
				if ($rmdl = fld_models_get($_GET['mdl'])) {
					$mdl = ria_mysql_fetch_array($rmdl);
					if ($mdl['cls_id'] == CLS_ORDER) {
						$r_object = fld_models_get_objects($mdl['id']);
						if ($r_object) {
							while ($object = ria_mysql_fetch_assoc($r_object)) {
								$ord_ids[] = $object['obj_id'];
							}
						}

						$have_mdl_filter = true;
					}
				}
			}

			if (!$have_mdl_filter) {
				if (isset($_GET['fld']) && is_numeric($_GET['fld']) && $_GET['fld'] > 0) {
					if ($rfld = fld_fields_get($_GET['fld'])) {
						$fld = ria_mysql_fetch_array($rfld);
						if ($fld['cls_id'] == CLS_ORDER) {
							$r_object = fld_fields_get_objects($fld['id']);
							if ($r_object) {
								while ($object = ria_mysql_fetch_assoc($r_object)) {
									$ord_ids[] = $object['obj_id'];
								}
							}

							$have_fld_filter = true;
						}
					}
				}
			}
			if (!$have_mdl_filter && !$have_fld_filter) {
				$ord_ids = 0;
			}
		}

		$_SESSION['ord_pay_id'] = $pay;
		$_SESSION['ord_dps_id'] = $dps_id;
		$_SESSION['ord_seller_id'] = $seller;


		$period = array();
		if (trim($piece) == '' && !$ref) {
			$period = array(
				'start' => $_GET['date1'],
				'end' => $_GET['date2'],
			);
		}

		// Charge la liste des commandes correspondants aux filtres appliqués
		$rord = false;
		if( !$only_stats ){
			$ar_states = ord_states_get_array();

			if (!is_array($ord_ids) || count($ord_ids)) {
				$key = array(
					'id' => is_array($ord_ids) ? $ord_ids : $ref,
					'ref' => '',
					'piece' => $piece,
				);

				$filter = array(
					'is_web' => ($gescom ? false : $is_web),
					'origin' => trim($piece) == ''  && !$ref ? $origin : false,
					'wst_id' => trim($piece) == ''  && !$ref ? $wst_id : 0,
					'pay_id' => $pay,
					'dps_id' => $dps_id,
					'seller_id' => $seller,
					'pmt_id' => $pmt_id,
					'state_id' => ($_GET['state'] == 0 ? ord_states_get_ord_valid() : $_GET['state']),
				);

				$rord = ord_orders_get_simple($key, $period, $filter);
			}
			$ar_states_used = array();
			if ($rord && ria_mysql_num_rows($rord)) {
				while ($ord = ria_mysql_fetch_assoc($rord)) {
					if ($_GET['state'] == 0) {
						if (!array_key_exists($ord['state_id'], $ar_states_used)) {
							$ar_states_used[$ord['state_id']] = 0;
						}

						$ar_states_used[$ord['state_id']]++;
					}
				}

				ria_mysql_data_seek($rord, 0);
			}
		}

		if( trim($piece) != '' || $ref ){
			$ord_ids = array();

			$key = array(
				'id' => $ref,
				'ref' => '',
				'piece' => $piece,
			);

			$r_ord_ref = ord_orders_get_simple($key, array(), array());
			if ($r_ord_ref && ria_mysql_num_rows($r_ord_ref)) {
				while ($ord_ref = ria_mysql_fetch_assoc($r_ord_ref)) {
					$ord_ids[] = $ord_ref['id'];
				}

				$_GET['date1'] = $_GET['date2'] = false;
				$origin = false;
				$wst_id = 0;
			}

			if( !count($ord_ids) ){
				$ord_ids = -1;
			}
		}

		// Récupère les devises utilisées
		$currencies = prd_prices_get_all_currencies();
		// Pour chaque devise, affiche un tableau de statistiques sur les commandes dans cette devise
		foreach( $currencies as $currency ){

			// Statistiques globales
			$total_volume = ord_orders_get_average_totals( null, ($_GET['state']==0 ? ord_states_get_ord_valid() : $_GET['state']), $_GET['date1'], $_GET['date2'], $state, $ord_ids, false, false, 0, 0, 0, 0, $pmt_id, 0, $currency, $dps_id );
			if( $total_volume['volume'] > 0 ){
				$ord_vol = ord_orders_get_average_totals( $is_web, ($_GET['state']==0 ? ord_states_get_ord_valid() : $_GET['state']), $_GET['date1'], $_GET['date2'], $state, $ord_ids, $origin, false, 0, $wst_id, $pay, $seller, $pmt_id, 0, $currency, $dps_id);

				$htmlSynthese = '<tr>';
				$htmlSynthese .= '<td headers="hd-order-total">';
				$htmlSynthese .= ria_number_format($ord_vol['volume'], NumberFormatter::DECIMAL);
				if( $total_volume['volume']!=$ord_vol['volume'] ){
					$pourcent = $total_volume['volume'] > 0 ? $ord_vol['volume'] / $total_volume['volume'] : 0;
					$htmlSynthese .= '<br /><span class="pourcent-volume">('.ria_number_format($pourcent, NumberFormatter::PERCENT, 2).')</span>';
				}
				$htmlSynthese .= '</td>';

				$htmlSynthese .= '<td headers="hd-order-ht">';
				$htmlSynthese .= ria_number_format($ord_vol['total_ht'], NumberFormatter::CURRENCY, 2, $currency);
				if( $total_volume['volume']!=$ord_vol['volume'] ){
					$pourcent = $total_volume['total_ht'] > 0 ? $ord_vol['total_ht'] / $total_volume['total_ht'] : 0;
					$htmlSynthese .= '<br /><span class="pourcent-volume">('.ria_number_format($pourcent, NumberFormatter::PERCENT, 2).')</span>';
				}
				$htmlSynthese .= '</td>';

				$htmlSynthese .= '<td headers="hd-order-ttc">';
				$htmlSynthese .= ria_number_format($ord_vol['total_ttc'], NumberFormatter::CURRENCY, 2, $currency);
				if( $total_volume['volume']!=$ord_vol['volume'] ){
					$pourcent = $total_volume['total_ttc'] > 0 ? $ord_vol['total_ttc'] / $total_volume['total_ttc'] : 0;
					$htmlSynthese .= '<br /><span class="pourcent-volume">('.ria_number_format($pourcent, NumberFormatter::PERCENT, 2).')</span>';
				}
				$htmlSynthese .= '</td>';

				$htmlSynthese .= '<td headers="hd-order-avg">'.ria_number_format($ord_vol['average_ht'], NumberFormatter::CURRENCY, 2, $currency).'</td>';
				$htmlSynthese .= '<td headers="hd-order-avg-ttc">'.ria_number_format($ord_vol['average_ttc'], NumberFormatter::CURRENCY, 2, $currency).'</td>';

				// La marge n'est chargé qu'aux utilisateurs y ayant accès
				if( gu_user_is_authorized('_RGH_ADMIN_MARGE_SHOW') ){
					$htmlSynthese .= '<td headers="hd-order-margin">';
					$htmlSynthese .= ria_number_format($ord_vol['marge'], NumberFormatter::CURRENCY, 2, $currency);
					if( $total_volume['volume']!=$ord_vol['volume'] ){
						$pourcent = $total_volume['marge'] > 0 ? $ord_vol['marge'] / $total_volume['marge'] : 0;
						$htmlSynthese .= '<br /><span class="pourcent-volume">('.ria_number_format($pourcent, NumberFormatter::PERCENT, 2).')</span>';
					}
					$htmlSynthese .= '</td>';
				}

				$htmlSynthese .= '</tr>';

				// Envoie en sortie
				$xml .= '<synthese currency="'.$currency.'" total_ht="'.ria_number_format($ord_vol['total_ht'], NumberFormatter::CURRENCY, 2, $currency).'" total_ttc="'.ria_number_format($ord_vol['total_ttc'], NumberFormatter::CURRENCY, 2, $currency).'">';
				$xml .= xmlentities($htmlSynthese);
				$xml .= '</synthese>';
			}
		}

		// Liste déroulante des statuts
		if (!$only_stats) {
			$htmlSelect = '<option value="">'._('Toutes les commandes').'</option>';
			if (count($ar_states_used)) {
				foreach ($ar_states_used as $key => $volume) {
					ob_start();
					?><option value="<?php print $key ; ?>"><?php
						print htmlspecialchars($ar_states[ $key ]['name_pl'] ).'('.ria_number_format($volume, NumberFormatter::DECIMAL).')';
					?></option><?php
					$htmlSelect .= ob_get_clean();
				}
			}

			// Envoie le contenu de la balise select située en entête du tableau (en haut à droite),
			// et contenant la listes des status de commande avec pour chacun un indicateur de quantité
			$xml .= '<html_select>';
			$xml .= xmlentities($htmlSelect);
			$xml .= '</html_select>';
		}

		// Choix des origines
		$xml .= '<choose_origin>'.xmlentities( view_origins_selector(true) ).'</choose_origin>';

		// Liste des commandes, ou la balise spéciale <noorder />
		if( $rord ){
			if( !ria_mysql_num_rows($rord) ){ // Pas de commande
				$xml .= '<noorder/>';
				$xml .= '<number count="0" nbpage="1" actual="1"/>';
			} else {
				$p = 1;
				$p =  isset($_GET['page']) ? $_GET['page'] : 1;

				$nbpage = ceil( ria_mysql_num_rows($rord) / 25 );

				if( $p > $nbpage ){
					$p = $nbpage;
				}

				$xml .= '<number count="'.ria_mysql_num_rows($rord).'" nbpage="'.$nbpage.'" actual="'.$p.'"/>';

				ria_mysql_data_seek( $rord, ($p-1)*25 );

				$count=0;

				$ar_payments = ord_payment_types_get_array();
				while( $ord = ria_mysql_fetch_assoc($rord) ){
					if( $count >= 25 ){
						break;
					}

					$user = array();
					$prc = array('money_code' => '');
					$r_user = gu_users_get($ord['usr_id']);

					if( $r_user && ria_mysql_num_rows($r_user) ){
						$user = ria_mysql_fetch_assoc($r_user);

						$prc = ria_mysql_fetch_assoc(
							prd_prices_categories_get($user['prc_id'])
						);
					}

					// false permet de ne pas vérifier l'utilisateur sur les adresses
					$ord_address = ord_orders_address_load($ord,false);

					$adr_inv = $adr_livr = '';
					$adr_inv .= '<span class="td-caption">'._('Adresse de facturation :').'</span>';
					if( $ord_address['invoice']['society'] ) $adr_inv .= htmlspecialchars($ord_address['invoice']['society']).'<br />';

					if( $ord_address['invoice']['title_name'] ) $adr_inv .= $ord_address['invoice']['title_name'].' ';
					if( $ord_address['invoice']['lastname'] ) $adr_inv .= htmlspecialchars($ord_address['invoice']['lastname']);
					if( $ord_address['invoice']['firstname'] ) $adr_inv .= ', '.htmlspecialchars($ord_address['invoice']['firstname']).'<br />';
					if( $ord_address['invoice']['address1'] ) $adr_inv .= htmlspecialchars($ord_address['invoice']['address1']).'<br />';
					if( $ord_address['invoice']['address2'] ) $adr_inv .= htmlspecialchars($ord_address['invoice']['address2']).'<br />';
					if( $ord_address['invoice']['address3'] ) $adr_inv .= htmlspecialchars($ord_address['invoice']['address3']).'<br />';
					$adr_inv .= htmlspecialchars($ord_address['invoice']['postal_code']).' '.htmlspecialchars($ord_address['invoice']['city']).'<br />';
					$adr_inv .= htmlspecialchars( $ord_address['invoice']['country'] ).'<br/>';
					if (isset($ord['usr_id'])) {
						$email = gu_users_get_email($ord['usr_id']);
						if ($email) {
							$adr_inv .= '<a href="mailto:'.htmlspecialchars($email).'" target="_top">'.htmlspecialchars($email).'</a>';
						}
					}
					$adr_livr .= '<span class="td-caption">'._('Adresse de livraison :').'</span>';
					// Charge l'adresse de la commande enfant si elle existe
					$r_child = ord_orders_get_simple(array('parent_id' => $ord['id']));
					if ($r_child && ria_mysql_num_rows($r_child)) {
						$child = ria_mysql_fetch_assoc($r_child);
						$ord_address =  ord_orders_address_load($child);
					}

					if ($ord_address['delivery']['society']) $adr_livr .= htmlspecialchars($ord_address['delivery']['society']) . '<br />';

					if ($ord_address['delivery']['title_name']) $adr_livr .= $ord_address['delivery']['title_name'] . ' ';
					if ($ord_address['delivery']['lastname']) $adr_livr .= htmlspecialchars($ord_address['delivery']['lastname']);
					if ($ord_address['delivery']['firstname']) $adr_livr .= ', ' . htmlspecialchars($ord_address['delivery']['firstname']) . '<br />';
					if ($ord_address['delivery']['address1']) $adr_livr .= htmlspecialchars($ord_address['delivery']['address1']) . '<br />';
					if ($ord_address['delivery']['address2']) $adr_livr .= htmlspecialchars($ord_address['delivery']['address2']) . '<br />';
					if ($ord_address['delivery']['address3']) $adr_livr .= htmlspecialchars($ord_address['delivery']['address3']) . '<br />';
					$adr_livr .= htmlspecialchars($ord_address['delivery']['postal_code']) . ' ' . htmlspecialchars($ord_address['delivery']['city']) . '<br />';
					$adr_livr .= htmlspecialchars($ord_address['delivery']['country']);

					if( isset($config['ord_multi_currency']) && $config['ord_multi_currency'] ){
						$txt_total_ht = '';
						$txt_total_ttc = '';

						$ar_totals = ord_products_get_totals_by_currencies( $ord['id'], $_GET['state'] );
						if( is_array($ar_totals) ){
							foreach( $ar_totals as $one_total_line ){
								if( trim($txt_total_ht) != '' ){
									$txt_total_ht .= ' / ';
								}

								$txt_total_ht .= ria_number_format( $one_total_line['total_ht'], NumberFormatter::CURRENCY, 2, $one_total_line['currency'] );

								if( trim($txt_total_ttc) != '' ){
									$txt_total_ttc .= ' / ';
								}

								$txt_total_ttc .= ria_number_format( $one_total_line['total_ttc'], NumberFormatter::CURRENCY, 2, $one_total_line['currency'] );
							}
						}
					}else{
						$txt_total_ht = ria_number_format( $ord['total_ht'], NumberFormatter::CURRENCY, 2, $ord['currency'] );
						$txt_total_ttc = ria_number_format( $ord['total_ttc'], NumberFormatter::CURRENCY, 2, $ord['currency'] );
					}

					// Informations sur la commande
					$xml.= '<order
						id="'.$ord['id'].'"
						numero="'.xmlentities(ord_orders_name('',$ord['piece'],$ord['id'])).'"
						piece="'.xmlentities($ord['piece']).'"
						pay_name="'.xmlentities($ord['pay_id'] ? '<span class="td-caption td-caption--inline">'._('Moyen de paiement :').'</span>'.$ar_payments[$ord['pay_id']]['name'] : '').'"
						state="'.xmlentities('<span class="td-caption td-caption--inline">'._('Etat/Statut :').'</span>'.$ar_states[$ord['state_id']]['name'] ).'"
						date="'.date('d/m/Y à H:i', strtotime($ord['date'])).'"
						adr_livr="'.xmlentities($adr_livr).'"
						adr_inv="'.xmlentities($adr_inv).'"
						ht="'.xmlentities('<span class="td-caption td-caption--inline">'._('Total HT :').'</span>').$txt_total_ht.'"
						ttc="'.xmlentities('<span class="td-caption td-caption--inline">'._('Total TTC :').'</span>').$txt_total_ttc.'"
					>'.xmlentities( $_GET['state']==1 ? view_cart_is_sync($ord) : view_ord_is_sync($ord) ).'</order>';

					$count++;
				}
			}
		}
	}

	print $xml.'</success>';