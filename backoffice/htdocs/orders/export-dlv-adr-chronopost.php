<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	if( !isset($orders) ){
		header('Location: /admin/orders/orders.php');
		exit;
	}

	if( !isset($config['export_dlv_adr_active']) || !$config['export_dlv_adr_active'] ){
		header('Location: /admin/orders/orders.php');
		exit;
	}

	/*
		A  = 0  = Nom
		B  = 1  = Prénom
		C  = 2  = Adresse
		D  = 3  = Suite adresse
		E  = 4  = Code porte
		F  = 5  = Code pays
		G  = 6  = Code Postal
		H  = 7  = Ville
		I  = 8  = Téléphone
		J  = 9  = E-mail
		K  = 10 = Référence
		L  = 11 = Type
		M  = 12 = Raison sociale
	*/

	require_once('strings.inc.php');
	require_once('orders.inc.php');

	$ar_ord_ids = array();
	$ar_lines 	= array();

	while( $order = ria_mysql_fetch_assoc($orders) ){
		// Exclusion des commandes livrées en magasin
		if( is_numeric($order['str_id']) && $order['str_id'] > 0 ){
			$ar_ord_ids[] = $order['id'];
			continue;
		}

		// Exclusion des commandes livrées par un autre service que Chronopost
		if( !in_array($order['srv_id'], $config['export_dlv_adr_chronopost']) ){
			continue;
		}

		$r_ord_adr = ord_orders_get_with_adresses(0, $order['id']);
		if (!$r_ord_adr || !ria_mysql_num_rows($r_ord_adr)) {
			continue;
		}

		$order_format = ord_orders_shipment_formatted( ria_mysql_fetch_assoc($r_ord_adr), 38 );
		if( !ria_array_key_exists(array('lastname', 'firstname', 'addr1', 'addr2', 'addr3', 'zipcode', 'city', 'ref_order', 'user_id', 'addr3', 'country', 'dlv-notes', 'weight_order', 'assurance', 'phone', 'email', 'society', 'mobile', 'rly_ref', 'rly_country'), $order_format) ){
			continue;
		}

		$type_delivery = 1; // Livraison à domicile
		if( trim($order_format['rly_ref']) != '' ){
			$type_delivery = 86; // Livraison en point relais
		}elseif( $order_format['country'] != 'FR' ){
			$type_delivery = 44; // Livraison à l'international
		}

		$user_ref = gu_users_get_ref( $order_format['user_id'], true );
		if( trim($user_ref) == '' ){
			$user_ref = $order_format['user_id'];
		}
		$user_prf = 'Client particulier';
		$prf_id = gu_users_get_prf( $order_format['user_id'] );
		if ($prf_id) {
			$user_prf =  gu_profiles_get_name( $prf_id );
			if (!strstr($user_prf, 'Client')) {
				$user_prf = 'Client ' . $user_prf;
			}
		}

		$society = '';

		switch ($config['tnt_id']) {
			case 14:
				$society = 'TDV';
				break;
		}

		$tmp_line = array();

		$tmp_line[0]  = $order_format['lastname'];
		$tmp_line[1]  = $order_format['firstname'];
		$tmp_line[2]  = strtoupper2( $order_format['addr1'] );
		$tmp_line[3]  = strtoupper2( $order_format['addr2'] );
		$tmp_line[4]  = '';
		$tmp_line[5]  = trim($order_format['rly_ref']) !=  '' && trim($order_format['rly_country']) != '' ? $order_format['rly_country'] : $order_format['country'];
		$tmp_line[6]  = $order_format['zipcode'];
		$tmp_line[7]  = $order_format['city'];
		$tmp_line[8]  = trim($order_format['mobile']) != '' ? $order_format['mobile'] : $order_format['phone'];
		$tmp_line[9]  = $order_format['email'];
		$tmp_line[10] = $user_ref;
		$tmp_line[11] = $user_prf;
		$tmp_line[12] = $society;

		$ar_lines[] = $tmp_line;
		$ar_ord_ids[] = $order['id'];
	}

	if( sizeof($ar_lines) ){
		header("Content-type: text/csv");
		header("Content-Disposition: attachment; filename=export-carnet-adresses-chronopost-".date('dmY').".csv");
		header("Pragma: no-cache");
		header("Expires: 0");

		foreach( $ar_lines as $one_line ){
			$line = '';
			$first = true;

			foreach( $one_line as $col ){
				if( !$first ){
					$line .= ';';
				}

				$line .= utf8_decode( str_replace(';', '', $col) );

				$first = false;
			}

			print $line."\n";
		}

		exit;
	}else{
		$_SESSION['export_shipment_no_order'] = "Aucune commande à exporter pour Chronopost";
		header('Location: /admin/orders/orders.php');
		exit;
	}
