<?php

    /** \file js_volume_orders.php
     *  Ce fichier est appelé en Ajax pour récupérer le nombre de commandes correspondant à une suite de filtres :
	 * 	- pay : mode de règlement
	 * 	- seller : représentant
	 *  - state : état de commande
	 *  - pmt_id : promotion
	 *  - mdl : modèle de saisie
	 *  - fld : champ avancé
	 *  - date1 / date2 : période
	 *
	 *	La liste des commandes appelle régulièrement cette page pour pouvoir afficher une notification en cas de nouvelle
	 *	commande correspondant aux filtres actifs
     */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	require_once('orders.inc.php');

	// Filtre sur l'origine de la commande
	$params = view_origins_get_params();
	$origin = $params['origin'];
	$gescom = $params['gescom'];
	$is_web = $params['is_web'];

	// traitement du paramètre site web
	$wst_id = 0;
	if( isset($_SESSION['websitepicker']) ){
		$wst_id = str_replace('w-', '', $_SESSION['websitepicker']);
	}

	$pay = isset($_GET['pay']) && is_numeric($_GET['pay']) && $_GET['pay'] ? $_GET['pay'] : 0;
	$dps_id = isset($_GET['dps']) && is_numeric($_GET['dps']) && $_GET['dps'] ? $_GET['dps'] : 0;
	$seller = isset($_GET['seller']) && is_numeric($_GET['seller']) && $_GET['seller'] ? $_GET['seller'] : 0;
	$exclude = isset($_GET['state']) && $_GET['state']==0 ? ord_states_get_ord_invalid() : false;
	$pmt_id = isset($_GET['pmt_id']) && is_numeric($_GET['pmt_id']) && $_GET['pmt_id'] ? $_GET['pmt_id'] : 0;

	$ord_ids = array();
	$have_mdl_filter = $have_fld_filter = false;

	{ // Filtre sur modèle de saisie / Champs avancé
		if (isset($_GET['mdl']) && is_numeric($_GET['mdl']) && $_GET['mdl'] > 0) {
			if ($rmdl = fld_models_get($_GET['mdl'])) {
				$mdl = ria_mysql_fetch_array($rmdl);
				if ($mdl['cls_id'] == CLS_ORDER) {
					$r_object = fld_models_get_objects($mdl['id']);
					if ($r_object) {
						while ($object = ria_mysql_fetch_assoc($r_object)) {
							$ord_ids[] = $object['obj_id'];
						}
					}

					$have_mdl_filter = true;
				}
			}
		}

		if (!$have_mdl_filter) {
			if (isset($_GET['fld']) && is_numeric($_GET['fld']) && $_GET['fld'] > 0) {
				if ($rfld = fld_fields_get($_GET['fld'])) {
					$fld = ria_mysql_fetch_array($rfld);
					if ($fld['cls_id'] == CLS_ORDER) {
						$r_object = fld_fields_get_objects($fld['id']);
						if ($r_object) {
							while ($object = ria_mysql_fetch_assoc($r_object)) {
								$ord_ids[] = $object['obj_id'];
							}
						}

						$have_fld_filter = true;
					}
				}
			}
		}
		if (!$have_mdl_filter && !$have_fld_filter) {
			$ord_ids = 0;
		}
	}

	$_SESSION['ord_pay_id'] = $pay;
	$_SESSION['ord_dps_id'] = $dps_id;
	$_SESSION['ord_seller_id'] = $seller;

	if( isset($_GET['state'], $_GET['date1'], $_GET['date2']) ){
		$ord_vol = ord_orders_get_average_totals(
			$is_web, ($_GET['state']==0 ? ord_states_get_ord_valid() : $_GET['state']), $_GET['date1'], $_GET['date2'],
			$exclude, $ord_ids, $origin, false, 0, $wst_id, $pay, $seller, $pmt_id, $dps_id
		);

		$xml = '<?xml version="1.0" encoding="utf8"?>
				<success>
					<order volume="'.$ord_vol['volume'].'" />
				</success>';

		print $xml;
	}


