<?php

	/**	\file fields.inc.php
	 * 	Affiche les champs avancés associés à la pièce de vente et permet leur modification
	 */

	// Charge la liste des modèles de saisie
	$models = fld_models_get(0, 0, CLS_ORDER);
	$ord_models = fld_models_get(0, $_GET['ord'], CLS_ORDER);
	$fields = fld_fields_get(0, 0, -2, 0, 0, $_GET['ord'], null, array(), false, array(_FLD_ORD_SIGN_RATE, _FLD_ORD_PIPE_SIGN_DATE), null, CLS_ORDER);

	// Affiche le tableau "Champs personnalisés" uniquement s'il y a des modèles de saisie ou bien des champs orphelins
	if( ria_mysql_num_rows($models) || ria_mysql_num_rows($ord_models) || ria_mysql_num_rows($fields) ){
?>
<table id="table-champs-personnalises">
	<caption><?php print _('Champs personnalisés')?></caption>
	<tbody>
		<?php
		$ar_fld_exclude = array();

		if( ria_mysql_num_rows($ord_models) ){
			ria_mysql_data_seek($ord_models, 0);

			// Tableau contenant les champs avancés faisant le lien entre les services de livraison et l'estimation des frais de port d'une commande
			$estim_port_fld_ids = [
				_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_1,
				_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_2,
				_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_3,
				_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_4,
				_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_5,
				_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_6,
				_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_7,
				_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_8,
				_FLD_ORD_SPECIFIC_SHIPPING_CHARGES_9,
			];

			// Si un ou plusieurs modèles sont affectés à la commande, affiche leur liste ainsi que les champs qu'ils contiennent
			while( $m = ria_mysql_fetch_array($ord_models) ){
				print '<tr><th colspan="2" class="tfoot-grey">';
				if( !$ctr_validation ){
					print '<a href="order.php?ord='.$_GET['ord'].'&amp;delmdl='.$m['id'].'" style="float: right;">'._('Supprimer').'</a>';
				}
				print _('Modèle de saisie :').' '.htmlspecialchars($m['name']);
				print '</th></tr>';

				$ord_fields = fld_fields_get(0,0,$m['id'],0,0,$_GET['ord'],null,array(),false,array(),null,CLS_ORDER);
				$last_cat = '';
				while( $f = ria_mysql_fetch_array($ord_fields) ){
					// S'il s'agit d'un champ avancé permettant de faire l'estimation de frais de port son nom est complété avec le service de livraison rattaché
					if( in_array($f['id'], $estim_port_fld_ids) ){
						$dlv_srv_name = '';

						$shipping = dlv_services_get_by_shippingfield( $f['id'] );
						if( is_array($shipping) && isset($shipping['name']) ){
							$dlv_srv_name = $shipping['name'];
						}

						if( trim($dlv_srv_name) == '' ){
							continue;
						}

						$f['name'] .= ' ('.htmlspecialchars( $dlv_srv_name ).')';
					}

					$ar_fld_exclude[] = $f['id'];

					if( $f['cat_name']!=$last_cat ){
						print '
							<tr>
								<th colspan="2">'.htmlspecialchars($f['cat_name']).'</th>
							</tr>';
						$last_cat = $f['cat_name'];
					}
					print '	<tr>
								<td class="th-150"><label for="fld'.$f['id'].'" '.( $f['is-sync'] ? 'title="'._('Ce champ est synchronisé avec votre gestion commerciale.').'"' : '' ).' >'.htmlspecialchars( $f['name'] ).' :</label>';
					/*if( ($f['type_id']==5 || $f['type_id']==6) && !$f['is-sync'] ){
						print '<sub><a href="#" onclick="return fldFieldModifyValues('.$f['id'].')">Editer la liste</a></sub>';
					}*/
					print '		</td>
								<td>';
					print fld_fields_edit( $f );
					print '		</td>
							</tr>';
				}

			}
		}else{
			// Affiche le message d'information uniquement si des modèles peuvent être ajoutés
			if( ria_mysql_num_rows($models)>ria_mysql_num_rows($ord_models) ){
				print '<tr><td colspan="2" class="multi-colspan">';
				print _('Aucun modèle de saisie n\'a été associé à cette commande. Avant de pouvoir continuer, veuillez sélectionner un modèle ci-dessous.');
				print '</td></tr>';
			}
		}

		// Affiche la liste des champs orphelins (n'étant rattachés à aucun modèle affecté à la commande)
		// $fields = fld_fields_get_orphans($_GET['ord'],CLS_ORDER);
		if( $fields && ria_mysql_num_rows($fields) ){
			ria_mysql_data_seek($fields, 0);

			ob_start();

			$last_cat = '';
			while ($f = ria_mysql_fetch_array($fields)) {
				if (in_array($f['id'], $ar_fld_exclude)) {
					continue;
				}

				if ($f['cat_name'] != $last_cat) {
					print '<tr><th colspan="2">' . htmlspecialchars($f['cat_name']) . '</th></tr>';
					$last_cat = $f['cat_name'];
				}

				print '<tr><td class="th-150"><label for="fld' . $f['id'] . '" ' . ($f['is-sync'] ? 'title="'._('Ce champ est synchronisé avec votre gestion commerciale.').'"' : '') . '>' . $f['name'] . ' :</label></td><td>';
				print fld_fields_edit($f);
				print '</td></tr>';
			}

			$orphans = ob_get_clean();

			if (trim($orphans)) {
				print '<tr><th colspan="2" class="tfoot-grey color-red">';
				print _('Champs orphelins');
				print '</th></tr>';
				print $orphans;
			}
		}

		?>
	</tbody>
	<tfoot class="print-none">
		<?php if( !$ord_viewed['piece'] ){ ?>
		<tr>
			<td colspan="2" class="multi-colspan">
				<input type="submit" name="savefields" class="btn-main" value="<?php print _('Enregistrer')?>" title="<?php print _('Enregistrer les modifications'); ?>" <?php print $ctr_validation ? 'disabled="disabled"' : ''; ?> />
				<input type="submit" name="cancel" value="<?php print _('Annuler')?>" title="<?php print _('Annuler les modifications'); ?>" <?php print $ctr_validation ? 'disabled="disabled"' : ''; ?> />
			</td>
		</tr>
		<?php }

			// Construit la liste des modèles déjà appliqués à cette commande
			$exclude = array();
			while( $m = ria_mysql_fetch_array($ord_models) ){
				$exclude[] = $m['id'];
			}

			// Si des modèles supplémentaires peuvent être ajoutés, affiche l'interface correspondante
			if( ria_mysql_num_rows($models)>ria_mysql_num_rows($ord_models) ){
				print '<tr><td colspan="2" class="tfoot-grey multi-colspan">';
				// Affiche la liste des modèles pouvant être ajoutés à cette commande
				print '
					<label for="model-pick">'._('Modèle de saisie :').'</label>
					<select name="model-pick" id="model-pick">
				';
				while( $m = ria_mysql_fetch_array($models) )
					if( array_search( $m['id'], $exclude )===false ){
						print '<option value="'.$m['id'].'">'.htmlspecialchars( $m['name'] ).'</option>';
					}
				print '
					</select>
					<input type="submit" name="addmdl" class="button" value="'._('Ajouter').'" '.( $ctr_validation ? 'disabled="disabled"' : '' ).' />
				';
				print '</td></tr>';
			}
		?>
	</tfoot>
</table>
<?php
	}
