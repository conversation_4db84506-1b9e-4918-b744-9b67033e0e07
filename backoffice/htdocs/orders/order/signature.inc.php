<?php

/**	\file signature.inc.php
 *
 * 	Ce fichier est chargé d'afficher la signature attachée au document (s'il y en a une)
 *
 */

if( ria_mysql_num_rows(ord_orders_signature_get($ord['id']))>0 ){

	$sign_row = ria_mysql_fetch_assoc(ord_orders_signature_get($ord['id']));
	$sign = $sign_row['signature'];

	$ar_sign_pt = explode(',', $sign);
	$min_x = $min_y = null;
	$max_x = $max_y = 0;
	foreach( $ar_sign_pt as &$val ){
		$val = str_replace(['moveTo:', 'lineTo:'], '', $val);
		$v = explode(':', $val);

		// Min
		if( $v[0] < $min_x || $min_x === null ){
			$min_x = $v[0];
		}
		if( $v[1] < $min_y || $min_y === null ){
			$min_y = $v[1];
		}

		// Max
		if( $v[0] > $max_x ){
			$max_x = $v[0];
		}
		if( $v[1] > $max_y ){
			$max_y = $v[1];
		}
	}

	$height = $max_y > $max_x ? 500 : 250;
	$height = $height - ( $min_y * ($height / $max_y));

	$width = $max_x > $max_y ? 500 : 250;
	$width = $width - ( $min_x * ($width / $max_x));


?>
	<script>
		$('document').ready(function(){
			var canvas =  $('#ord_signature').get(0);
			var path = '<?php print $sign; ?>';
			var ratioX = <?php print round( ($width / $max_x), 6 ); ?>;
			var ratioY = <?php print round( ($height / $max_y), 6 ); ?>;

			var ctx = canvas.getContext('2d');
			path = path.split(',');
			ctx.beginPath();

			path.forEach( function(element, index) {

				if (element.indexOf("lineTo:") != -1) {
					var p = element.replace("lineTo:","").split(':');
					ctx.lineTo((p[0] - <?php print $min_x; ?>) * ratioX, (p[1] - <?php print $min_y; ?>) * ratioY);

				}else if (element.indexOf("moveTo:") != -1) {

					var p = element.replace("moveTo:","").split(':');
					ctx.moveTo((p[0] - <?php print $min_x; ?>) * ratioX, (p[1] - <?php print $min_y; ?>) * ratioY);

				}
			});

			ctx.stroke();
		});
	</script>

	<table id="ord-signature">
	<tr>
		<th><?php print _('Bon pour accord par')?>
		<?php
			print $sign_row["lastname"] != NULL ? htmlspecialchars( $sign_row["lastname"] )." ": "";
			print $sign_row["firstname"] != NULL ? htmlspecialchars( $sign_row["firstname"] ) : "" ;
			print $sign_row["function"] != NULL ? " (".htmlspecialchars( $sign_row["function"] ).")" : "" ;
			if( $sign_row['date_created'] ){
				print ', le '.date( 'd/m/Y à H:i', strtotime( $sign_row['date_created'] ) );
			}
		?>
		</th>
	</tr>
	<tr>
		<td class="align-center multi-colspan">
			<canvas id="ord_signature" width="<?php print $width; ?>" height="<?php print $height; ?>">
				<?php print _('Désolé, votre navigateur ne permet pas d\'afficher la signature électronique du document.'); ?>
			</canvas>
		</td>
	</tr>
	</table>
<?php } ?>
