<?php
	/**	\file products-beta.php
	 *	Ce fichier affiche la liste des produits contenus dans une pièce de vente. Il est utilisé
	 *	comme include par le fichier htdocs/admin/orders/order-beta.php.
	*/

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	require_once('view/view.orders.inc.php');
	$ar_cols = view_admin_ord_products_get_columns( ['group_id' => SORT_ASC, 'position' => SORT_ASC, 'name' => SORT_ASC] );

	// La table des informations de la commande est soit issus de $ord (initialisé en amont)
	$ord_viewed = &$ord;

	print '<div class="section-products">'
		.'<div class="section-name">'._('Articles').'</div>'
		.'<div id="select-columns-orders" class="with-cols-options hidden">'
			.'<a href="#" id="display-cols-options" class="edit-cat newoptions"> '._('Personnaliser colonnes').'</a>'
			.'<span class="menu-cols none">'
				.'<span class="cols">'
					.'<span class="col">'
						.'<input type="checkbox" name="cols-all" id="cols-all" value="all" />'
						.'<label for="cols-all">'._('Toutes les colonnes').'</label>'
					.'</span>'

					.'<span class="col separate"></span>';

					$last_group = ''; $first_group = true;
					foreach( $ar_cols as $one_col ){
						if( $last_group != $one_col['group'] ){
							print '<span class="group '.( !$first_group ? ' next-group' : '' ).'">'
								.'<strong>'.htmlspecialchars( $one_col['group'] ).'</strong>'
							.'</span>';
							$first_group = false;
						}

						print'<span class="col '.( in_array( $one_col['id'], $ar_cols_ord_products_actived ) ? 'checked' : '' ).'">'
							.'<input '.( in_array( $one_col['id'], $ar_cols_ord_products_actived ) ? 'checked="checked"' : '' )
								.' type="checkbox" name="cols-'.$one_col['id'].'" id="cols-'.$one_col['id'].'" value="'.$one_col['id'].'" '
								.( in_array($one_col['id'], ['ref', 'name']) ? 'disabled="disabled"' : '' )
							.'/>'
							.'<label for="cols-'.$one_col['id'].'"> '.htmlspecialchars( $one_col['name'] ).'</label>'
						.'</span>';

						$last_group = $one_col['group'];
					}

				print '</span>'
			.'</span>'
		.'</div>'

		.'<table style="min-width:100%;" id="ord-products-articles" class="print-table ajax_table_ord_products">'
			.'<thead>'

			.'</thead>'
			.'<tbody>'

			.'</tbody>'
		.'</table>'
	.'</div>';
?>
<script>//<!--
	var timer_savePositionCols = false;
  var tableOrdersAjax = false;
  var firstLoad = true;
	var actualCodeColsSelected = [];
	var lastValueInput = '';

	function loadTableOrdProducts(){
		$('.load-ajax-opacity').show();

		// Masque la personnalisation des colonnes et la notice
		$('#select-columns-ord-prds, #notice-show-ord-prds').addClass('hidden');

		// Charge le tableau des commandes
		$.ajax({
			url: '/ajax/orders/list-order-products.php?loadconfigprds=on',
			method: 'get',
			dataType: 'json'
		}).done(function( json ){
			let orderColPos = 0;

			{ // Charge l'entête du tableau
				var headHtml = '<tr>';

				for (let index = 0; index < json.data.header.length; index++) {
					const element = json.data.header[index];
					headHtml += '<th data-key-col="' + element.id + '">' + element.name + '</th>';
				}

				headHtml += '</tr>';

				if (json.data.sortcol) {
					orderColPos = json.data.sortcol;
				}
			}

			// Chargement des statistiques
			// loadStatsOrders();

			// Réinitialise le tableau si besoin
			if( tableOrdersAjax !== false ){
				tableOrdersAjax.destroy();
				tableOrdersAjax = false;

				$('.ajax_table_ord_products thead, .ajax_table_ord_products tbody').html('');
			}

			{ // Charge le contenu du tableau
				$('.load-ajax-opacity').hide();
				$('.ajax_table_ord_products').find('thead').html( headHtml );

				// Paramètre de chargement des données
				var parameters = $('#ajax_ord_prds_filters').find('input').serializeJSON();
				parameters.loaddataprds = 'on'

				// $('.ajax_table_ord_products').append('<caption>' + '<?php print _('Articles'); ?>' + '</caption>');

				tableOrdersAjax = $('.ajax_table_ord_products').DataTable( {
					ajax: {
						url: "/ajax/orders/list-order-products.php",
						data: parameters,
						complete: function(){ // Action(s) lancée(s) une fois les données récupérées
							// searchInTableOrders();
						}
					},
					columns: json.data.columns,

					// Traduction française du plugins
					language: {
						url: "/admin/js/datatables/french.json"
					},

					// Scroll horizontale
					scrollX: true,

					// Nombre de ligne par défaut
					pageLength: -1,

					dom: 'fBrt',
					buttons: [
						{
								extend: 'csv',
								text: 'CSV',
								exportOptions: {
									format: {
										body: function ( data, rowIdx, columnIdx, cellNode ) {
											data = data.replace(/<.*?>/ig, ""); // Suppression des balises HTML
											data = data.replace('+ d\'infos', '' );
											return data;
										}
									}
								}
						},
						{
								extend: 'excel',
								text: 'Excel',
								exportOptions: {
									format: {
										body: function ( data, rowIdx, columnIdx, cellNode ) {
											if( $(cellNode).find('[type="number"]').length ){
												data = $(cellNode).find('[type="number"]').val();
											}else if( $(cellNode).find('textarea').length ){
												data = $(cellNode).find('textarea').val();
											}else{
												data = data.replace(/<.*?>/ig, ""); // Suppression des balises HTML
												data = data.replace('+ d\'infos', '' );
											}

											if( $(cellNode).find('.currency').length ){
												data += ' ' + $(cellNode).find('.currency').html();
											}

											return data;
										}
									}
								}
						}
					]
				});

				// Permet de trier les colonnes comme on le souhaite
				new $.fn.dataTable.ColReorder( tableOrdersAjax, {
					// options
				} );

				tableOrdersAjax.on( 'column-reorder', function ( e, settings, details ) {
						let changeSortCol = '';
						for(let iCol in settings.aoColumns ){
							if( changeSortCol != '' ){
								changeSortCol += ',';
							}

							changeSortCol += settings.aoColumns[ iCol ].keyCol
						}

						clearTimeout( timer_savePositionCols );

						timer_savePositionCols = setTimeout(function(){
							$.ajax({
								url: '/ajax/orders/list-order-products.php',
								method: 'post',
								data: {
									savepositioncols: 'on',
									cols: changeSortCol
								},
								dataType: 'json'
							});
						}, 1000);
				} );
			}

			$('[id="notice-show-orders"], [id="select-columns-orders"]').removeClass('hidden');

			// Certaines actions sont réalisées lors du premier chargement
			if( firstLoad ){

			}

			firstLoad = false;
		});
	}

	$(document).ready(function(){
		$("#checkall").click(function(){
			if($(this).is(":checked")){
				$(".checkbox").prop("checked", true);
			} else {
				$(".checkbox").prop("checked", false);
			}
		});

		$(".checkbox").click(function(){
			var all_checked = true;
			$(".checkbox").each(function(){
				if (!$(this).is(":checked")){
					all_checked = false;
					return false;
				}
			});
			if (all_checked == true){
				$("#checkall").prop('checked', true);
			} else {
				$("#checkall").prop('checked', false);
			}
		});

    loadTableOrdProducts();
	}).on(
		'click', '.ord-line-more-infos', function(){
			displayPopup('Plus d\'informations', '', '/admin/orders/popup-data-line-order.php?ord=' + $(this).data('ord') + '&prd=' + $(this).data('prd') + '&line=' + $(this).data('line') + '&edit=' + $(this).data('edit'), 'reloadPrdLines()');
			return false;
		}
	).on( // Gestion de l'affichage du menu "Personnaliser mes colonnes"
		'click', '[id="display-cols-options"]', function(){
			if($('.menu-cols').hasClass('none')){
				$('.menu-cols').removeClass('none');
				$('.menu-cols').show();

				// Sauvegarde les colonnes actuellement sélectionnées lors de l'ouverture du menu
				actualCodeColsSelected = [];
				$('[id="select-columns-orders"] input[type="checkbox"]:checked').each(function(){
					actualCodeColsSelected.push( $(this).val() );
				});
			}else{
				$('.menu-cols').addClass('none');
			}
			return false;
		}
	).on(
		'focus', '#ord-products-articles input, #ord-products-articles textarea', function(){
			lastValueInput = $(this).val();
		}
	).on(
		'blur', '#ord-products-articles input, #ord-products-articles textarea', function(){
			let ordID = $(this).data('ord');
			let prdID = $(this).data('prd');
			let lineID = $(this).data('line');
			let col = $(this).data('col');

			// Mise à jour de la ligne de commande si la valeur a changée
			if( lastValueInput != $(this).val() ){
				let element = $(this);

				let formData = {
					updline: 'on',
					ord: ordID,
					prd: prdID,
					line: lineID,
					col: col,
					value: $(this).val()
				};

				$.ajax({
					url: '/ajax/orders/list-order-products.php',
					data: formData,
					method: 'post',
					dataType: 'json'
				}).done(function( json ){
					if( json.data.success == 'on' ){
						if( col == 'priceht' || col == 'qte' ){
							let qte = parseFloat( element.parents('tr').find('[data-col="qte"]').val() );
							let price = parseFloat( element.parents('tr').find('[data-col="priceht"]').val() );
							if( !isNaN(qte) && !isNaN(price) ){
								element.parents('tr').find('.totalht').html( (price * qte).toFixed(2) );
							}
						}

						element.addClass('input-edit-success');
					}else{
						element.addClass('input-edit-error');
					}

					setTimeout(function(){
						element.removeClass('input-edit-success').removeClass('input-edit-error');
					}, 1000);
				});
			}
		}
	).click(function(e){
		if( !$('.menu-cols').hasClass('none') ){
			if( $($(e.target).parents('#select-columns-orders')).length == 0 ){
				// Ferme le menu de sélection des colonnes
				$('.menu-cols').addClass('none');

				// Charge la liste des colonnes sélectionnées dans un tableau
				var listCodeColsSelected = [];
				$('[id="select-columns-orders"] input[type="checkbox"]:checked').each(function(){
					listCodeColsSelected.push( $(this).val() );
				});

				// Sauvegarde la sélection et lance la mise à jour du tableau
				// Seulement si des changements ont été apportées aux colonnes sélectionnées
				if( actualCodeColsSelected.toString() != listCodeColsSelected.toString() ){
					$.ajax({
						url: '/ajax/orders/list-order-products.php',
						method: 'post',
						data: {
							saveconfigcols: 'on',
							cols: listCodeColsSelected
						},
						dataType: 'json'
					}).done(function( json ){
						loadTableOrdProducts();
					});

					actualCodeColsSelected = listCodeColsSelected;
				}
			}
		}
	}).on(
		'click', '[id="select-columns-orders"] [name="cols-all"]', function(){
			if( $(this).is(':checked') ){
				$('[id="select-columns-orders"] [type="checkbox"]').attr('checked', 'checked');
			}else{
				$('[id="select-columns-orders"] [type="checkbox"]').removeAttr('checked')
				$('[id="select-columns-orders"] [name="cols-ref"]').attr('checked', 'checked');
				$('[id="select-columns-orders"] [name="cols-name"]').attr('checked', 'checked');
			}
		}
	);

	function reloadPrdLines(){
		window.reloadOrdPrds($('.order-products-row'), undefined, false);
		hidePopup();
	}

	function reloadOrdPrds(){
		loadTableOrdProducts();
	}

	$('textarea.ord-prd-input.ord-prd-comment-input').on('input click change', function () {
		this.style.height = 'auto';
		this.style.height = (this.scrollHeight) + 'px';
  });
//--></script>