<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

require_once('dlv_store_plage.inc.php');

// Récupère la commande (si nécessaire).
if( empty($ord) ){
	if( !isset($_GET['ord']) ){
		header('Location: /admin/orders.php');
		exit;
	}

	$ord = ria_mysql_fetch_array(
		ord_orders_get_masked($_GET['ord'], true)
	);
}

if( $ord['user'] ){
	// Récupère l'utilisateur (si nécessaire).
	if( !isset($usr) ){
		$usr = array();
		$r_user = gu_users_get($ord['user']);

		if( $r_user && ria_mysql_num_rows($r_user) ){
			$usr = ria_mysql_fetch_assoc($r_user);
		}
	}

	// Récupère des différentes adresses
	$dlv_address = ord_delivery_address($ord);
	$dlv_third = ord_orders_get_thrid_address( $ord['id'] );
	$dlv_ponton = dsp_plage_get_plage_for_order($ord['id']);

	?>

	<!-- Adresse de facturation -->
	<div class="block-ord-address">
		<div class="th-user-adress-facture">
			<?php print _('Adresse de facturation'); ?>
		</div>
		<div class="adresse-facturation">
			<div class="details-adresse">
				<div class="title-detail">
					<?php print _('Adresse :');
					if( !$ord['piece'] && !isset($not_edit_data) ){ ?>
						<br>
						<a href="/admin/customers/popup-address.php?usr=<?php print $ord['user']; ?>&adr=<?php print $ord['inv_id']; ?>&popup=1&ord=<?php print $ord['id']; ?>&reloadord=1" class="order-address-edit-btn print-none" title="<?php print _('Modifier cette adresse'); ?>" data-ord="<?php print $ord['id']; ?>"><?php print _('Modifier'); ?></a>
					<?php } ?>
				</div>
				<div>
					<?php
						if( $ord['inv_society'] ) print htmlspecialchars($ord['inv_society']).'<br />';

						if( $ord['inv_title_name'] ) print htmlspecialchars( $ord['inv_title_name'] ).' ';
						if( $ord['inv_lastname'] ) print htmlspecialchars($ord['inv_lastname']);
						if( $ord['inv_firstname'] ) print ', '.htmlspecialchars($ord['inv_firstname']).'<br />';
						else print '<br />';

						if( $ord['inv_address1'] ) print htmlspecialchars($ord['inv_address1']).'<br />';
						if( $ord['inv_address2'] ) print htmlspecialchars($ord['inv_address2']).'<br />';
						if( $ord['inv_address3'] ) print htmlspecialchars($ord['inv_address3']).'<br />';
						print htmlspecialchars($ord['inv_postal_code']).' '.htmlspecialchars($ord['inv_city']).'<br />';
						print htmlspecialchars($ord['inv_country']);
					?>
				</div>
			</div>
			<?php if( trim($ord['inv_phone']) ){ ?>
				<div class="details-adresse">
					<div class="title-detail"><?php print _('Téléphone :'); ?></div>
					<div><?php print htmlspecialchars($ord['inv_phone']); ?></div>
				</div>
			<?php }
			if( trim($ord['inv_fax']) ){ ?>
				<div class="details-adresse">
					<div class="title-detail"><?php print _('Fax :'); ?></div>
					<div><?php print htmlspecialchars($ord['inv_fax']); ?></div>
				</div>
			<?php }
			if( trim($ord['inv_mobile']) ){ ?>
				<div class="details-adresse">
					<div class="title-detail"><?php print _('Portable :'); ?></div>
					<div><?php print htmlspecialchars($ord['inv_mobile']); ?></div>
				</div>
			<?php }
			if( trim($ord['inv_phone_work']) ){ ?>
				<div class="details-adresse">
					<div class="title-detail"><?php print _('En journée :'); ?></div>
					<div><?php print htmlspecialchars($ord['inv_phone_work']); ?></div>
				</div>
			<?php } ?>
		</div>
	</div>

	<!-- Adresse de livraison -->
	<div class="block-ord-address">
		<div class="th-user-adress-livraison">
			<?php print _('Adresse de livraison'); ?>
		</div>

		<div class="adresse-livraison">
			<div class="details-adresse">
				<div class="title-detail"> <?php
					print (trim($dlv_address['type']) == "" ? _('Adresse') : htmlspecialchars( $dlv_address['type'] ) ) . ' :' ;
					if (!$ord['piece'] && !isset($not_edit_data)) {
						print '
							<br/>
							<a href="/admin/customers/popup-address.php?usr='.$ord['user'].'&adr='.$dlv_address['id'].'&popup=1&ord='.$ord['id'].'&dlv-adr=1&is-dlv=1&reloadord=1" data-ord="'.$ord['id'].'" class="order-address-edit-btn print-none" title="'._('Modifier cette adresse').'">'._('Modifier').'</a>';
					} ?>
				</div>
				<div>
					<?php if( $dlv_address['name'] ){
						print htmlspecialchars($dlv_address['name']).'<br/>';
					}
					if( $dlv_address['title_name'] ){
						print htmlspecialchars($dlv_address['title_name']).' ';
					}
					if( $dlv_address['firstname'] ){
						print htmlspecialchars($dlv_address['firstname']).' ';
					}
					if( $dlv_address['lastname'] ){
						print htmlspecialchars($dlv_address['lastname']).'<br/>';
					}
					if( $dlv_address['address1'] ){
						print htmlspecialchars($dlv_address['address1']).'<br/>';
					}
					if( $dlv_address['address2'] ){
						print htmlspecialchars($dlv_address['address2']).'<br/>';
					}
					if( $dlv_address['address3'] ){
						print htmlspecialchars($dlv_address['address3']).'<br/>';
					}
					if( empty($ctr_validation) ){
						print htmlspecialchars($dlv_address['zipcode']).' '.htmlspecialchars($dlv_address['city']);
					}else{ ?>
						<input type="text" name="dlv-zipcode" class="text ord-mini-action" placeholder="<?php print _('Code postal...'); ?>" value="<?php print isset($_POST['dlv-zipcode']) ? htmlspecialchars($_POST['dlv-zipcode']) : ''; ?>" maxlength="9">
						<input type="submit" name="save-dlv-zipcode" class="auto_input" title="<?php print _('Valider la commande après avoir saisie le code postal de livraison.'); ?>" value="<?php print _('Enregistrer'); ?>">
						<br>
						<?php print htmlspecialchars($dlv_address['city']);
					} ?>
					<br>
					<?php print htmlspecialchars($dlv_address['country']); ?>
				</div>
			</div>
			<?php if( !empty($dlv_ponton) ){ ?>
				<div class="details-adresse" style="margin: 1rem 0;">
					<div class="title-detail"><?php print _('Livraison sur ponton'); ?></div>
					<div>
						<?php if( !$dlv_ponton['oop_confirmed'] ){ ?>
							<p class="color-red">⚠️ <?php print _('Commande non confirmée'); ?></p>
							<br>
						<?php } ?>
						<?php print _(dateformatcomplet($dlv_ponton['date']->getTimestamp())); ?>
						<br>
						<?php print $dlv_ponton['dsp_debut']; ?> - <?php print $dlv_ponton['dsp_fin']; ?>
						<?php if( $dlv_ponton['ponton_number'] ){ ?>
							<br>
							<?php print _('Ponton n°').$dlv_ponton['ponton_number']; ?>
						<?php } ?>
						<?php if( $dlv_ponton['boat_number'] ){ ?>
							<br>
							<?php print _('Emplacement n°').$dlv_ponton['boat_number']; ?>
						<?php } ?>
					</div>
				</div>
			<?php } ?>
			<?php if( trim($dlv_address['phone']) ){ ?>
				<div class="details-adresse">
					<div class="title-detail"><?php print _('Téléphone :'); ?></div>
					<div><?php print htmlspecialchars($dlv_address['phone']); ?></div>
				</div>
			<?php }
			if( trim($dlv_address['fax']) ){ ?>
				<div class="details-adresse">
					<div class="title-detail"><?php print _('Fax :'); ?></div>
					<div><?php print htmlspecialchars($dlv_address['fax']); ?></div>
				</div>
			<?php }
			if( trim($dlv_address['mobile']) ){ ?>
				<div class="details-adresse">
					<div class="title-detail"><?php print _('Portable :'); ?></div>
					<div><?php print htmlspecialchars($dlv_address['mobile']); ?></div>
				</div>
			<?php }
			if( trim($dlv_address['work']) ){ ?>
				<div class="details-adresse">
					<div class="title-detail"><?php print _('En journée :'); ?></div>
					<div><?php print htmlspecialchars($dlv_address['work']); ?></div>
				</div>
			<?php } ?>
		</div>
	</div>

	<!-- Adresse du client final -->
	<?php if( gu_user_is_authorized('_RGH_ADMIN_ORDER_USE_THIRD_ADR') ){ ?>
		<div class="block-ord-address third">
			<div class="ord-adresses-row">
					<div class="ord-addresses">
						<div class="th-user-adress-livraison">
							<?php print _('Adresse client final'); ?>
						</div>

						<div class="adresse-livraison">
							<div class="details-adresse">
								<div class="title-detail"> <?php
									print (trim($dlv_third['type']) == "" ? _('Adresse') : htmlspecialchars( $dlv_third['type'] ) ) . ' :' ;
									if (!$ord['piece'] && !isset($not_edit_data)) {
										print '
											<br/>
											<a href="/admin/customers/popup-address.php?usr='.$ord['user'].'&adr='.$dlv_third['id'].'&popup=1&ord='.$ord['id'].'&third_adr=1&is-dlv=1&reloadord=1" data-ord="'.$ord['id'].'" class="order-address-edit-btn print-none" title="'._('Modifier cette adresse').'">'._('Modifier').'</a>';
									} ?>
								</div>
								<div>
									<?php if( $dlv_third['name'] ){
										print htmlspecialchars($dlv_third['name']).'<br/>';
									}
									if( $dlv_third['title_name'] ){
										print htmlspecialchars($dlv_third['title_name']).' ';
									}
									if( $dlv_third['firstname'] ){
										print htmlspecialchars($dlv_third['firstname']).' ';
									}
									if( $dlv_third['lastname'] ){
										print htmlspecialchars($dlv_third['lastname']).'<br/>';
									}
									if( $dlv_third['address1'] ){
										print htmlspecialchars($dlv_third['address1']).'<br/>';
									}
									if( $dlv_third['address2'] ){
										print htmlspecialchars($dlv_third['address2']).'<br/>';
									}
									if( $dlv_third['address3'] ){
										print htmlspecialchars($dlv_third['address3']).'<br/>';
									}
									if( empty($ctr_validation) ){
										print htmlspecialchars($dlv_third['zipcode']).' '.htmlspecialchars($dlv_third['city']);
									}else{ ?>
										<input type="text" name="dlv-zipcode" class="text ord-mini-action" placeholder="<?php print _('Code postal...'); ?>" value="<?php print isset($_POST['dlv-zipcode']) ? htmlspecialchars($_POST['dlv-zipcode']) : ''; ?>" maxlength="9">
										<input type="submit" name="save-dlv-zipcode" class="auto_input" title="<?php print _('Valider la commande après avoir saisie le code postal de livraison.'); ?>" value="<?php print _('Enregistrer'); ?>">
										<br>
										<?php print htmlspecialchars($dlv_third['city']);
									} ?>
									<br>
									<?php print htmlspecialchars($dlv_third['country']); ?>
								</div>
							</div>
							<?php if( !empty($dlv_ponton) ){ ?>
								<div class="details-adresse" style="margin: 1rem 0;">
									<div class="title-detail"><?php print _('Livraison sur ponton'); ?></div>
									<div>
										<?php if( !$dlv_ponton['oop_confirmed'] ){ ?>
											<p class="color-red">⚠️ <?php print _('Commande non confirmée'); ?></p>
											<br>
										<?php } ?>
										<?php print _(dateformatcomplet($dlv_ponton['date']->getTimestamp())); ?>
										<br>
										<?php print $dlv_ponton['dsp_debut']; ?> - <?php print $dlv_ponton['dsp_fin']; ?>
										<?php if( $dlv_ponton['ponton_number'] ){ ?>
											<br>
											<?php print _('Ponton n°').$dlv_ponton['ponton_number']; ?>
										<?php } ?>
										<?php if( $dlv_ponton['boat_number'] ){ ?>
											<br>
											<?php print _('Emplacement n°').$dlv_ponton['boat_number']; ?>
										<?php } ?>
									</div>
								</div>
							<?php } ?>
							<?php if( trim($dlv_third['phone']) ){ ?>
								<div class="details-adresse">
									<div class="title-detail"><?php print _('Téléphone :'); ?></div>
									<div><?php print htmlspecialchars($dlv_third['phone']); ?></div>
								</div>
							<?php }
							if( trim($dlv_third['fax']) ){ ?>
								<div class="details-adresse">
									<div class="title-detail"><?php print _('Fax :'); ?></div>
									<div><?php print htmlspecialchars($dlv_third['fax']); ?></div>
								</div>
							<?php }
							if( trim($dlv_third['mobile']) ){ ?>
								<div class="details-adresse">
									<div class="title-detail"><?php print _('Portable :'); ?></div>
									<div><?php print htmlspecialchars($dlv_third['mobile']); ?></div>
								</div>
							<?php }
							if( trim($dlv_third['work']) ){ ?>
								<div class="details-adresse">
									<div class="title-detail"><?php print _('En journée :'); ?></div>
									<div><?php print htmlspecialchars($dlv_third['work']); ?></div>
								</div>
							<?php } ?>
						</div>
					</div>
			</div>
		</div>
	<?php } ?>
<?php } ?>