<?php

// Affichage des retours
if( $config['returns_allowed'] ){
	if( ord_returns_keeped_products_get_count($ord['id']) && in_array($ord['state_id'], ord_returns_orders_states_allowed_get()) ){
	$returns = ord_returns_get( 0, 0, null, null, $ord['id']);
?>
<table  class="ncmd_cart checklist" id="table-retours">
	<caption><?php print _('Retours')?></caption>
	<thead class="thead-none">
		<tr>
			<th id="ord-return-id"><?php print _('Numéro')?></th>
			<th id="ord-date"><?php print _('Date du retour')?></th>
			<th id="ord-state"><?php print _('Etat/Statut')?></th>
			<th id="ord-products"><?php print _('Nombre de produits')?></th>
			<th id="ord-ht"><?php print _('Total H.T.')?></th>
			<th id="ord-ttc"><?php print _('Total T.T.C.')?></th>
		</tr>
	</thead>
	<tbody>
	<?php
		if( ria_mysql_num_rows($returns) ){
			while($r = ria_mysql_fetch_array($returns)){

				print '<tr>
							<td><a href="/admin/orders/returns/return.php?ret='.$r['id'].'">'.str_pad($r['id'], 8, 0, STR_PAD_LEFT).'</a></td>
							<td class="align-center"">'.substr(dateheureunparse($r['date']), 0, 17).'</td>
							<td class="align-center">'.ord_returns_states_get_name($r['states_id']).'</td>
							<td class="align-right">'.ria_number_format($r['products']).'</td>
							<td class="align-right">'.ria_number_format($r['total_ht'], NumberFormatter::CURRENCY, 2, $prc['money_code']).'</td>
							<td class="align-right">'.ria_number_format($r['total_ttc'], NumberFormatter::CURRENCY, 2, $prc['money_code']).'</td>
						</tr>';
			}
		}else{
			print '	<tr><td colspan="6" class="multi-colspan">'._('Il n\'y a pas de retour pour cette commande').'</td></tr>';
		}
	?>
	</tbody>
	<tfoot>
		<tr>
			<th colspan="6" class="align-left">
				<input type="submit" <?php print $ctr_validation ? 'disabled="disabled"' : ''; ?> id="ncmd_new_return" name="createReturn" value="<?php print _('Créer un retour')?>" />
			</th>
		</tr>
	</tfoot>
</table>

<?php
	}
}