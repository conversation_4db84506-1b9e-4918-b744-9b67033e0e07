<?php 
	require_once('orders.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	define('ADMIN_PAGE_TITLE', _('Commandes'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Commandes')?></h2>

<p><?php print _('Cette interface va vous permettre de traiter l\'ensemble des commandées passées par l\'intermédiaire de la boutique. Il est très important de bien valider les différents états de commande, car vos clients seront avertis par email de la progression de leur commande.')?></p>

<form action="orders.php" method="get">
	<p><?php print _('Vous avez la possibilité de rechercher une commande en renseignant son numéro ci-contre :'); ?>
	<input type="text" name="ref" id="ref" maxlength="16" /> <input class="btn-action" type="submit" value="<?php print _('Rechercher')?>" /></p>
</form>


<dl>
	<dt><a href="orders.php"><?php print _('Toutes les commandes').' ('.number_format(ord_states_get_count(0, array(1, 2, 9, 10, 12)),0,',',' ').')'?></a></dt>
	<dd><?php print _('Vous trouverez ici toutes les commandes validées, quel que soit leur état de traitement. Les paniers, commandes annulées et archivées ne sont pas disponibles dans cet écran.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=1"><?php print _('Paniers en préparation').' ('.number_format(ord_states_get_count(1),0,',',' ').')'?></a></dt>
	<dd><?php print _('Vous trouverez ici tous les paniers en cours de préparation par vos clients. Veuillez noter que parmi ces paniers, certains ne seront jamais validés.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=25" title="<?php print _('Afficher les commandes dont le réglement est en attente de validation')?>"><?php print _('Règlement reçu, en attente de validation').' ('.number_format(ord_states_get_count(25),0,',',' ').')'?></a></dt>
	<dd><?php print _('Vous trouverez ici toutes les commandes dont le règlement a bien été reçu, mais dont ce dernier est toujours en cours de contrôle.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=4" title="<?php print _('Afficher les nouvelles commandes à traiter')?>"><?php print _('En attente de traitement').' ('.number_format(ord_states_get_count(4),0,',',' ').')'?></a></dt>
	<dd><?php print _('Vous trouverez ici toutes les commandes validées et payées par vos clients, mais qui n\'ont pas encore été traitées.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=5" title="<?php print _('Visualiser les commandes en cours de traitement')?>"><?php print _('En cours de traitement').' ('.number_format(ord_states_get_count(5),0,',',' ').')'?></a></dt>
	<dd><?php print _('Cette catégorie regroupe toutes les commandes dont vous avez pris connaissance, mais dont les articles n\'ont pas encore été réunis.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=11" title="<?php print _('Visualiser les commandes en cours de préparation')?>"><?php print _('En cours de préparation').' ('.number_format(ord_states_get_count(11),0,',',' ').')'?></a></dt>
	<dd><?php print _('Cette catégorie regroupe toutes les commandes dont les articles sont en train d\'être réunis.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=6" title="<?php print _('Visualiser les commandes prêtes à expédier')?>"><?php print _('Prêtes à expédier').' ('.number_format(ord_states_get_count(6),0,',',' ').')'?></a></dt>
	<dd><?php print _('Toutes les commandes dont le colis a été préparé, et qui sont en attente d\'expédition.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=7" title="<?php print _('Visualiser les commandes expédiées')?>"><?php print _('Expédiées').' ('.number_format(ord_states_get_count(7),0,',',' ').')'?></a></dt>
	<dd><?php print _('Les commandes dont le colis a été remis à la société d\'expédition, et qui sont en cours d\'acheminement.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=24" title="<?php print _('Visualiser les commandes disponibles en magasin')?>"><?php print _('Disponibles en magasin').' ('.number_format(ord_states_get_count(24),0,',',' ').')'?></a></dt>
	<dd><?php print _('Les commandes dont le colis est disponible en magasin.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=<?php print _STATE_INV_STORE; ?>" title="<?php print _('Visualiser les commandes retirées en magasin')?>"><?php print _('Retirées en magasin').' ('.number_format(ord_states_get_count(_STATE_INV_STORE),0,',',' ').')'?></a></dt>
	<dd><?php print _('Les commandes dont le colis a été retiré par le client en magasin.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=8" title="<?php print _('Visualiser les commandes facturées')?>"><?php print _('Facturées').' ('.number_format(ord_states_get_count(8),0,',',' ').')'?></a></dt>
	<dd><?php print _('Vous trouverez ici l\'ensemble des commandes terminées et facturées au client.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=3" title="<?php print _('Visualiser les commandes en attente de règlement')?>"><?php print _('En attente de règlement').' ('.number_format(ord_states_get_count(3),0,',',' ').')'?></a></dt>
	<dd><?php print _('Il s\'agit de toutes les commandes validées par le client mais dont le paiement n\'a pas encore été reçu. Si le paiement n\'est pas reçu au bout d\'une semaine, la commande sera automatiquement annulée.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=10" title="<?php print _('Afficher les commandes annulées par vous même')?>"><?php print _('Annulées par vous même').' ('.number_format(ord_states_get_count(10),0,',',' ').')'?></a></dt>
	<dd><?php print _('Vous trouverez ici toutes les commandes que vous avez vous même annulé. Chaque annulation de commande devra être justifiée auprès de votre client.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=9" title="<?php print _('Afficher les commandes annulées par le client')?>"><?php print _('Annulées par le client').' ('.number_format(ord_states_get_count(9),0,',',' ').')'?></a></dt>
	<dd><?php print _('Jusqu\'à ce que la commande soit prête à expédier, le client peut demander l\'annulation de sa commande. Si cela se produit, vous recevrez une notification par email, et la commande du client viendra se placer dans cette catégorie.')?></dd>
</dl>

<dl>
	<dt><a href="orders.php?state=12" title="<?php print _('Afficher les commandes archivées')?>"><?php print _('Archivées').' ('.number_format(ord_states_get_count(12),0,',',' ').')'?></a></dt>
	<dd><?php print _('Comprend les commandes terminées et archivées. Les commandes non-terminées ne doivent pas être archivées.')?></dd>
</dl>

<dl>
	<dt><a href="returns/index.php" title="<?php print _('Gestion des retours')?>"><?php print _('Gestion des retours')?></a></dt>
	<dd><?php print _('Vous trouverez ici toutes les demandes de retour effectuées par vos clients ainsi qu’un système complet de traitement des retours.')?></dd>
</dl>
<?php
	require_once('admin/skin/footer.inc.php');
?>