<?php
	/**	\file orders.php
	 *	Cette page affiche une liste de commandes, filtrées sur un état passé via le paramètre $_GET['state']
	 */

	if( in_array($config['tnt_id'], [977, 998, 1043, 171]) && !isset($_GET['force_default']) || isset($_GET['force_beta']) ){
		$_SESSION['beta-orders-listing-actived'] = true;
	}

	// Test la nouvelle version de cette interface
	if( isset($_SESSION['beta-orders-listing-actived']) && $_SESSION['beta-orders-listing-actived'] ){
		header('Location: /admin/orders/orders-beta.php'.(isset($_GET['state']) ? '?state='.$_GET['state'] : ''));
		exit;
	}

	require_once('fields.inc.php');
	require_once('orders.inc.php');
	require_once('gu.categories.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

	$orders_count = 0;
	$have_mdl_filter = $have_fld_filter = false;

	// Filtre sur l'état de la commande, ou 0 pour toutes les commandes "actives"
	if( !isset($_GET['state']) || !ord_states_exists($_GET['state']) ){
		$_GET['state'] = 0;
	}

	// Le paramètre ref est utilisé pour les recherches
	if( !isset($_GET['ref']) ){
		$_GET['ref'] = 0;

	}else{ // Réalise un trim sur la référence saisie par l'internaute car la recherche de commande attend une référence exacte
		$_GET['ref'] = trim($_GET['ref']);
	}

	// Filtre sur l'origine de la commande
	$params = view_origins_get_params();

	$origin = false;
	if( isset($_GET['origin']) && is_array($_GET['origin'])){
		$origin = $_GET['origin'];
	}else if( !isset($_GET['origin']) && isset($_GET['state']) && $_GET['state'] !== 0 ){
		$origin = $params['origin'];
	}

	$gescom = $params['gescom'];
	$is_web = $params['is_web'];

	// Filtre sur le site d'origine
	$wst_id = 0;
	if( isset($_SESSION['ar_websitepicker']) && is_array($_SESSION['ar_websitepicker']) && sizeof($_SESSION['ar_websitepicker']) ){
		$wst_id = $_SESSION['ar_websitepicker'];
	}elseif( isset($_SESSION['websitepicker']) ){
		$wst_id = str_replace('w-', '', $_SESSION['websitepicker']);
	}

	// Variable pour la mise en place des périodes
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;

	$_SESSION['datepicker_period'] = isset($_SESSION['datepicker_period']) ? $_SESSION['datepicker_period'] : _('Aujourd\'hui');
	$_SESSION['ord_pay_id'] = isset($_SESSION['ord_pay_id']) ? $_SESSION['ord_pay_id'] : 0;
	$_SESSION['ord_dps_id'] = isset($_SESSION['ord_dps_id']) ? $_SESSION['ord_dps_id'] : 0;
	$_SESSION['ord_seller_id'] = isset($_SESSION['ord_seller_id']) ? $_SESSION['ord_seller_id'] : 0;

	$pmt_id = isset($_GET['pmt']) && is_numeric($_GET['pmt']) && $_GET['pmt'] ? $_GET['pmt'] : 0;

	// Extraction des commandes
	$_GET['piece'] = '';
	if( !is_numeric($_GET['ref']) ){
		$_GET['piece'] = $_GET['ref'];
		$_GET['ref'] = 0;
	}

	$ord_ids = array();

	$ar_payments = ord_payment_types_get_array();

	// Boutons "Créer une commande" / "Créer un devis"
	if( isset($_GET['add']) ){
		header('Location: order.php?ord=new');
		exit;
	}

	{ // Filtre sur modèle de saisie / Champs avancé
		if( isset($_GET['mdl']) && is_numeric($_GET['mdl']) && $_GET['mdl']>0 ){
			if( $rmdl = fld_models_get($_GET['mdl']) ){
				$mdl = ria_mysql_fetch_array($rmdl);
				if( $mdl['cls_id']==CLS_ORDER ){
					$r_object = fld_models_get_objects($mdl['id']);
					if( $r_object ){
						while( $object = ria_mysql_fetch_assoc($r_object) ){
							$ord_ids[] = $object['obj_id'];
						}
					}

					$have_mdl_filter = true;
				}
			}
		}

		if( !$have_mdl_filter ){
			if (isset($_GET['fld']) && is_numeric($_GET['fld']) && $_GET['fld'] > 0) {
				if ($rfld = fld_fields_get($_GET['fld'])) {
					$fld = ria_mysql_fetch_array($rfld);
					if ($fld['cls_id'] == CLS_ORDER) {
						$r_object = fld_fields_get_objects($fld['id']);
						if ($r_object) {
							while ($object = ria_mysql_fetch_assoc($r_object)) {
								$ord_ids[] = $object['obj_id'];
							}
						}

						$have_fld_filter = true;
					}
				}
			}
		}

		if( !$have_mdl_filter && !$have_fld_filter ){
			$ord_ids = 0;
		}
	}

	$ar_states = ord_states_get_array();

	$orders = false;
	if (!is_array($ord_ids) || count($ord_ids)) {
		$key = array(
			'id' => is_array($ord_ids) ? $ord_ids : $_GET['ref'],
			'ref' => '',
			'piece' => $_GET['piece'],
			'need-sync' => isset($_GET['need-sync'])
		);

		//! résolution temporaire du bug de l'export des commandes
		if( isset($origin) && is_array($origin) && in_array('web', $origin) ){
			$origin = false;
		}

		$filter = array(
			'is_web' 		=> ($gescom ? false : $is_web),
			'origin' 		=> trim($_GET['piece']) == "" && !$_GET['ref'] ? $origin : false,
			'wst_id' 		=> trim($_GET['piece']) == "" && !$_GET['ref'] ? $wst_id : 0,
			'pay_id' 		=> $_SESSION['ord_pay_id'],
			'dps_id' 		=> $_SESSION['ord_dps_id'],
			'seller_id' 	=> $_SESSION['ord_seller_id'],
			'pmt_id' 		=> $pmt_id,
			'state_id' 		=> $_GET['state']==0 && !isset($_GET['need-sync']) ? array_merge(ord_states_get_ord_valid(), array(_STATE_SUPP_PARTIEL_INV)) : $_GET['state'],
		);

		$period = array();
		if (trim($_GET['piece']) == "" && !$_GET['ref']) {
			$period = array(
				'start' => $date1,
				'end' => $date2,
			);
		}

		$orders = ord_orders_get_simple($key, $period, $filter);
	}

	// Calcule le nombre de commandes
	$orders_count = $orders ? ria_mysql_num_rows($orders) : 0;

	// Récupère les devises utilisées
	$currencies = prd_prices_get_all_currencies();

	// Détermine si le statut est à ligne de commande et non à la commande
	// Dans ce cas la colonne ne sera pas affiché
	$state_on_line = gu_user_is_authorized('_RGH_ADMIN_ORDER_USE_STATE_LINE');

	// Export des commandes
	$msg = null;
	if( isset($_GET['export']) ){
		ria_mysql_data_seek($orders, 0);

		$file_name = 'commandes-'.time().'.xls';
		$file_csv = $config['doc_dir'].'/'.$file_name;

		// Gestion de l'absence d'un filtre sur l'origin de commande)
		if( is_array($filter['origin']) && count($filter['origin']) == 1 && ($filter['origin'][0] == 'all' || trim($filter['origin'][0]) == '') ){
			unset($filter['origin']);
		}

		try{
			$exp_id = exp_exports_add( CLS_ORDER, $file_csv, $file_name );

			// Ajoute l'import dans la file d'attente
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_EXPORT_GENERATE, array(
				'wst_id' => $config['wst_id'],
				'cls_id' => CLS_ORDER,
				'exp_id' => $exp_id,
				'key' => $key,
				'filter' => $filter,
				'period' =>$period,
			));
			$_SESSION['msg']['text'] = _('Votre demande d\'export a bien été prise en compte, vous pouvez suivre son traitement depuis <a href="/admin/tools/exports/index.php" id="link-exports"> Outils > Exports</a>.');
			$_SESSION['msg']['class'] = 'success';
		}catch(Exception $e){
			$_SESSION['msg']['text'] = _('Une erreur est survenue lors de l\'export');
			$_SESSION['msg']['class'] = 'error';
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
		header('Location: /admin/orders/orders.php?state='.$_GET['state']);
		exit;
	}

	// Export Collissimo
	if( isset($_GET['export-colissimo']) ){
		require_once('export-colissimo.php');
		exit;
	}

	// Export Mondial Relay
	if( isset($_GET['export-mondial-relay']) ){
		require_once('export-mondial-relay.php');
		exit;
	}

	// Export Chronopost
	if( isset($_GET['export-chronopost']) ){
		require_once('export-chronopost.php');
		exit;
	}

	// Bouton Export lettres de suivies
	if( isset($_GET['export-suivies']) ){
		require_once('export-suivies.php');
		exit;
	}

	// Export des adresses de livraison au format Chronopost
	if( isset($_GET['export-dlv-adr-chronopost']) ){
		require_once('export-dlv-adr-chronopost.php');
		exit;
	}

	// Compose le titre de la page
	$title = _('Toutes les commandes');
	if( $_GET['state']!=0 ){
		switch ($_GET['state']) {
			case _STATE_BASKET:
				$title = _('Paniers');
				break;
			default:
				$title = _('Commandes '). mb_strtolower($ar_states[$_GET['state']]['name_pl'], 'UTF-8');
				break;
		}
	}


	if( isset($_GET['need-sync']) ){
		$title = _('En attente de synchronisation');
		$title .= ' ('.ria_number_format( $orders_count ).')';
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', $title.' - '._('Commandes'));
	require_once('admin/skin/header.inc.php');

	print '<form action="orders.php" method="get">';

	print '<h2 id="title-state">';
	print htmlspecialchars( $title );
	if( !isset($_GET['need-sync']) ){
		print '<input class="btn-move float-right hide-mobile" type="submit" name="export" value="'._('Exporter').'" title="'._('Exporter la liste des commandes au format Microsoft Excel').'" />';
		switch( $_GET['state'] ){
			case 0:
				if( gu_user_is_authorized('_RGH_ADMIN_ORDER_CREATE') ){
					print '<input type="submit" name="add" value="'._('Créer une commande').'" class="btn-main float-right" style="margin-right: 5px" />';
				}
			break;
			case 28:
				print '<input type="submit" name="add" value="'._('Créer un devis').'" class="btn-main float-right" style="margin-right: 5px" />';
			break;
		}
	}
	print '</h2>';

	if( isset($_SESSION['msg']) ){ ?>
		<div class="<?php print $_SESSION['msg']['class']; ?>"><?php print $_SESSION['msg']['text']; ?></div><?php
		unset($_SESSION['msg']);
	}

	// Calcul des paramètres de pagination
	$by_page = 25;

	$_GET['page'] = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] ? $_GET['page'] : 1;
	$pages = ceil( $orders_count / $by_page );
	if( $_GET['page']>$pages ){
		$_GET['page'] = $pages;
	}
	if( $_GET['page']>1 ){
		ria_mysql_data_seek( $orders, ($_GET['page']-1)*$by_page );
	}

	// Dates de début et de fin
	if( isset($_GET['date1'], $_GET['date2'], $_GET['last']) ){
		print '
			<input type="hidden" name="date1" id="date1" value="'.$_GET['date1'].'"/>
			<input type="hidden" name="date2" id="date2" value="'.$_GET['date2'].'"/>
			<input type="hidden" name="last" id="last" value="'.$_GET['last'].'"/>
		';
	}
	if( isset($_GET['state']) ){
		print '<input type="hidden" name="state-h" id="state-h" value="'.$_GET['state'].'"/>';
	}
	if( isset($_GET['pmt']) ){
		print '<input type="hidden" name="pmt_id" id="pmt_id" value="'.$_GET['pmt'].'"/>';
	}
?>

<?php if( !isset($_GET['need-sync']) ){ ?>
<div class="stats-menu">
	<div class="ria-admin-ui-filters">
		<div id="riadatepicker"></div>
		<?php
			print view_websites_selector( $wst_id, true, 'riapicker', true, _('Tous les sites'), false, true );

			print view_origins_selector();

			// Filtre sur le moyen de paiement utilisé
			print view_pay_selector();

			// Filtre sur le représentant
			print view_sellers_selector();

			// Filtre sur les dépôts
			print view_deposits_selector($config['tnt_id']);
		?>
	</div>
	<div>
		<?php foreach( $currencies as $currency ){ ?>
		<table class="orders table-synthese-order" id="table-synthese-order" data-currency="<?php print $currency; ?>">
			<thead class="thead-none">
				<tr>
					<th id="hd-order-total"><?php print _('Commandes')?></th>
					<th id="hd-order-ht"><?php print _('Total')?> <abbr title="<?php print _('Hors Taxes')?>"><?php print _('HT')?></abbr></th>
					<th id="hd-order-ttc"><?php print _('Total')?> <abbr title="<?php print _('Toutes Taxes Comprises')?>"><?php print _('TTC')?></abbr></th>
					<th id="hd-order-avg-ht"><?php print _('Panier moyen')?> <abbr title="<?php print _('Hors Taxes')?>"><?php print _('HT')?></abbr></th>
					<th id="hd-order-avg-ttc"><?php print _('Panier moyen')?> <abbr title="<?php print _('Toutes Taxes Comprises')?>"><?php print _('TTC')?></abbr></th>
					<?php
						// Contrôle que l'utilisateur à accès à la marge
						if( gu_user_is_authorized('_RGH_ADMIN_MARGE_SHOW') ){
							print '<th id="hd-order-margin">'._('Marge brute').'</th>';
						}
					?>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td headers="hd-order-total"><img src="/admin/images/loader2.gif" class="loader" title="<?php print _('Chargement en cours, veuillez patienter.')?>" /></td>
					<td headers="hd-order-ht"><img src="/admin/images/loader2.gif" class="loader" title="<?php print _('Chargement en cours, veuillez patienter.')?>" /></td>
					<td headers="hd-order-ttc"><img src="/admin/images/loader2.gif" class="loader" title="<?php print _('Chargement en cours, veuillez patienter.')?>" /></td>
					<td headers="hd-order-avg-ht"><img src="/admin/images/loader2.gif" class="loader" title="<?php print _('Chargement en cours, veuillez patienter.')?>" /></td>
					<td headers="hd-order-avg-ttc"><img src="/admin/images/loader2.gif" class="loader" title="<?php print _('Chargement en cours, veuillez patienter.')?>" /></td>
					<?php
						// Contrôle que l'utilisateur à accès à la marge
						if( gu_user_is_authorized('_RGH_ADMIN_MARGE_SHOW') ){
							print '<td headers="hd-order-margin">'
								.'<img src="/admin/images/loader2.gif" class="loader" title="'._('Chargement en cours, veuillez patienter.').'" />'
							.'</td>';
						}
					?>
				</tr>
			</tbody>
		</table>
		<?php } ?>
	</div>
	<div class="clear"></div>
</div>
<?php } ?>

<?php
	if( isset($_SESSION['export_shipment_no_order']) ){
		print '<div class="notice">'.htmlspecialchars( $_SESSION['export_shipment_no_order'] ).'</div>';
		unset( $_SESSION['export_shipment_no_order'] );
	}
?>

	<?php
		if( isset($_SESSION['origin']) && is_array($_SESSION['origin']) && sizeof($_SESSION['origin']) ){
			foreach( $_SESSION['origin'] as $o ){
				print '<input type="hidden" name="origin[]" value="'.htmlspecialchars( $o ).'" />';
			}
		}else{
			print '<input type="hidden" name="origin[]" value="" />';
		}
	?>

	<input type="hidden" name="state" id="state" value="<?php print isset($_GET['state']) ? $_GET['state'] : '0'; ?>" />
	<input type="hidden" name="ord_website" id="ord_website" value="<?php print isset($_SESSION['websitepicker']) ? $_SESSION['websitepicker'] : 'w-0'; ?>" />
	<input type="hidden" name="ord_pay_id" id="ord_pay_id" value="<?php print $_SESSION['ord_pay_id']; ?>" />
	<input type="hidden" name="ord_dps_id" id="ord_dps_id" value="<?php print $_SESSION['ord_dps_id']; ?>" />
	<input type="hidden" name="ord_seller_id" id="ord_seller_id" value="<?php print $_SESSION['ord_seller_id']; ?>" />
	<input type="hidden" name="ord_mdl_id" id="ord_mdl_id" value="<?php print isset($_GET['mdl']) && is_numeric($_GET['mdl']) && $_GET['mdl'] ? $_GET['mdl'] : 0; ?>" />
	<input type="hidden" name="ord_fld_id" id="ord_fld_id" value="<?php print isset($_GET['fld']) && is_numeric($_GET['fld']) && $_GET['fld'] ? $_GET['fld'] : 0; ?>" />
	<input type="hidden" name="ord_sch_ref" id="ord_sch_ref" value="<?php print isset($_GET['ref']) && trim($_GET['ref']) != "" ? $_GET['ref'] : ""; ?>" />
	<input type="hidden" name="ord_sch_piece" id="ord_sch_piece" value="<?php print isset($_GET['piece']) && trim($_GET['piece']) != "" ? $_GET['piece'] : ""; ?>" />

	<table class="list" id="table-liste-commandes">
		<caption class="thead-title-mobile"><?php print _('Commandes')?></caption>
		<thead class="thead-none">
			<tr>
				<th id="ord-id"><?php print _('Numéro')?></th>
				<th id="ord-date"><?php print _('Date')?></th>
				<th id="ord-inv"><?php print _('Adresse de facturation')?></th>
				<th id="ord-livr"><?php print _('Adresse de livraison')?></th>
				<th id="ord-type-pay"><?php print _('Moyen de paiement')?></th>
				<?php if (!$state_on_line) { ?>
					<th id="ord-state"><?php print _('Etat/Statut')?></th>
				<?php } ?>
				<th id="ord-ht"><?php print _('Total H.T.')?></th>
				<th id="ord-ttc"><?php print _('Total T.T.C.')?></th>
			</tr>
		</thead>
		<tbody id="lst_orders">
		<?php
			// Affichage des commandes
			if( !$orders_count ){
				print '<tr><td colspan="8">'._('Aucune commande ne correspond à vos critères de sélection.').'</td></tr>';
			}else{
				$count = 0;
				while( ($r = ria_mysql_fetch_assoc($orders)) && $count<$by_page ){
					$user = array();
					$prc = array('money_code' => '');
					$r_user = gu_users_get($r['usr_id']);

					if( $r_user && ria_mysql_num_rows($r_user) ){
						$user = ria_mysql_fetch_assoc($r_user);

						$prc = ria_mysql_fetch_assoc(
							prd_prices_categories_get($user['prc_id'])
						);
					}

					$count++;
						$id=ord_orders_name('','',$r['id']);

						if( $id == $r['piece'] ){
							$ord_id = $r['piece'];
						}elseif( $r['piece'] !== "" ){
							$ord_id = $r['piece'].' ('.$r['id'].')';
						}else{
							$ord_id = $r['id'];
						}

						// false permet de ne pas vérifier l'utilisateur sur les adresses
						$ord_address = ord_orders_address_load($r, false);

						if( isset($config['ord_multi_currency']) && $config['ord_multi_currency'] ){
							$txt_total_ht = '';
							$txt_total_ttc = '';

							$ar_totals = ord_products_get_totals_by_currencies( $r['id'], $_GET['state'] );
							if( is_array($ar_totals) ){
								foreach( $ar_totals as $one_total_line ){
									if( trim($txt_total_ht) != '' ){
										$txt_total_ht .= ' / ';
									}

									$txt_total_ht .= ria_number_format( $one_total_line['total_ht'], NumberFormatter::CURRENCY, 2, $one_total_line['currency'] );

									if( trim($txt_total_ttc) != '' ){
										$txt_total_ttc .= ' / ';
									}

									$txt_total_ttc .= ria_number_format( $one_total_line['total_ttc'], NumberFormatter::CURRENCY, 2, $one_total_line['currency'] );
								}
							}
						}else{
							$txt_total_ht = ria_number_format( $r['total_ht'], NumberFormatter::CURRENCY, 2, $r['currency'] );
							$txt_total_ttc = ria_number_format( $r['total_ttc'], NumberFormatter::CURRENCY, 2, $r['currency'] );
						}

					?>
						<tr>
							<td headers="ord-id" id="td-ord-id">
								<?php echo ( $_GET['state']==1 ? view_cart_is_sync($r) : view_ord_is_sync($r));
								if( gu_user_is_authorized('_RGH_ADMIN_ORDER_EDIT') ){ ?>
								<a href="order.php?ord=<?php echo $r['id']?>&amp;state=<?php echo $_GET['state']?>" title="<?php print _('Afficher la fiche de cette commande')?>"><?php echo $ord_id; ?></a>
								<?php }else{
									echo $ord_id;
									} ?>
								<br />
								<ul class="ord-actions">
									<li>
										<a id="ord-duplicate" class="button"><?php print _('Dupliquer'); ?></a>
										<input type="hidden" name="ord-id" value="<?php echo $r['id']; ?>" />
									</li>
								</ul>
							</td>
							<td headers="ord-date" class="align-right"><?php echo ria_date_format($r['date']); ?></td>
							<td headers="ord-inv">
								<?php
									print '<span class="td-caption">'._('Adresse de facturation :').'</span>';
									if( $ord_address['invoice']['society'] ) print htmlspecialchars($ord_address['invoice']['society']).'<br />';

									if( $ord_address['invoice']['title_name'] ) print $ord_address['invoice']['title_name'].' ';
									if( $ord_address['invoice']['lastname'] ) print htmlspecialchars($ord_address['invoice']['lastname']);
									if( $ord_address['invoice']['firstname'] ) print ', '.htmlspecialchars($ord_address['invoice']['firstname']).'<br />';
									if( $ord_address['invoice']['address1'] ) print htmlspecialchars($ord_address['invoice']['address1']).'<br />';
									if( $ord_address['invoice']['address2'] ) print htmlspecialchars($ord_address['invoice']['address2']).'<br />';
									if( $ord_address['invoice']['address3'] ) print htmlspecialchars($ord_address['invoice']['address3']).'<br />';
									print htmlspecialchars($ord_address['invoice']['postal_code']).' '.htmlspecialchars($ord_address['invoice']['city']).'<br />';
									print htmlspecialchars( $ord_address['invoice']['country'] ).'<br />';
									if (isset($r['usr_id'])) {
										$email = gu_users_get_email($r['usr_id']);
										if ($email) {
											print '<a href="mailto:'.htmlspecialchars($email).'" target="_top">'.htmlspecialchars($email).'</a>';
										}
									}
								?>
							</td>
							<td headers="ord-livr">
								<?php
									print '<span class="td-caption">'._('Adresse de livraison :').'</span>';
									// Charge l'adresse de la commande enfant si elle existe
									$r_child = ord_orders_get_simple(array('parent_id' => $r['id']));
									if ($r_child && ria_mysql_num_rows($r_child)) {
										$child = ria_mysql_fetch_assoc($r_child);
										$ord_address =  ord_orders_address_load($child);
									}

									if ($ord_address['delivery']['society']) print htmlspecialchars($ord_address['delivery']['society']) . '<br />';

									if ($ord_address['delivery']['title_name']) print $ord_address['delivery']['title_name'] . ' ';
									if ($ord_address['delivery']['lastname']) print htmlspecialchars($ord_address['delivery']['lastname']);
									if ($ord_address['delivery']['firstname']) print ', ' . htmlspecialchars($ord_address['delivery']['firstname']) . '<br />';
									if ($ord_address['delivery']['address1']) print htmlspecialchars($ord_address['delivery']['address1']) . '<br />';
									if ($ord_address['delivery']['address2']) print htmlspecialchars($ord_address['delivery']['address2']) . '<br />';
									if ($ord_address['delivery']['address3']) print htmlspecialchars($ord_address['delivery']['address3']) . '<br />';
									print htmlspecialchars($ord_address['delivery']['postal_code']) . ' ' . htmlspecialchars($ord_address['delivery']['city']) . '<br />';
									print htmlspecialchars($ord_address['delivery']['country']);
								?>
							</td>
							<td headers="ord-type-pay"><?php print $r['pay_id'] ? '<span class="td-caption td-caption--inline">'._('Moyen de paiement :').'</span>'.htmlspecialchars($ar_payments[$r['pay_id']]['name']) : '';?></td>
							<?php if (!$state_on_line) { ?>
								<td headers="ord-state"><?php print '<span class="td-caption td-caption--inline">'._('Etat/Statut :').'</span>'._( $ar_states[$r['state_id']]['name'] )?></td>
							<?php } ?>
							<td headers="ord-ht"><?php
								print '<span class="td-caption td-caption--inline">'._('Total HT :').'</span>'.$txt_total_ht;
							?></td>
							<td headers="ord-ttc"><?php
								print '<span class="td-caption td-caption--inline">'._('Total TTC :').'</span>'.$txt_total_ttc;
							?></td>
						</tr>
					<?php
				}
			}
		?>
		</tbody>
		<?php if( !isset($_GET['need-sync']) ){ ?>
		<tfoot>
			<tr id="pagination" <?php print $pages>1 ? '' : 'style="display: none"'; ?>>
				<td colspan="2" class="align-left">
				<?php
					print sprintf( _('Page %d/%d'), $_GET['page'], $pages );
				?>
				</td>
				<td colspan="6">
				<?php
					if( $pages>1 ){
						$page_start = $_GET['page']>5 ? $_GET['page']-5 : 1;
						$page_stop = $_GET['page']+5<$pages ? $_GET['page']+5 : $pages;

						$links = array();
						if( $_GET['page']>1 ){
							$links[] = '<a onclick="return reload_lst_orders( '.($_GET['page']-1).' )" href="#">&laquo; '._('Page précédente').'</a>';
						}
						for( $i=$page_start; $i<=$page_stop; $i++ ){
							if( $i==$_GET['page'] ){
								$links[] = '<b>'.$i.'</b>';
							}else{
								$links[] = '<a onclick="return reload_lst_orders( '.$i.' )" href="#">'.$i.'</a>';
							}
						}
						if( $_GET['page']<$pages ){
							$links[] = '<a onclick="return reload_lst_orders( '.($_GET['page']+1).' )" href="#">'._('Page suivante').' &raquo;</a>';
						}
						print implode(' | ',$links);
					}
				?>
				</td>
			</tr>
			<?php foreach( $currencies as $key => $currency ){ ?>
			<tr class="ord-totals">
				<?php if( $key == 0 ){ ?>
					<td rowspan="<?php print count($currencies); ?>" colspan="3"></td>
					<th rowspan="<?php print count($currencies); ?>" class="align-right"><?php print _('Totaux :'); ?></th>
				<?php  } ?>
					<td colspan="2" class="total-loader align-right" id="orders-total-ht-<?php print $currency; ?>" ><span><img src="/admin/images/loader_join_file.gif" class="loader" title="<?php print _('Chargement en cours, veuillez patienter.')?>" /></span> <?php print _('HT'); ?> </td>
					<td colspan="2" class="total-loader align-right" id="orders-total-ttc-<?php print $currency; ?>" ><span><img src="/admin/images/loader_join_file.gif" class="loader" title="<?php print _('Chargement en cours, veuillez patienter.')?>" /></span> <?php print _('TTC'); ?> </td>
			<?php } ?>
			</tr>
			<tr class="js-btn-export" style="display: none">
				<td colspan="8" >
					<input class="btn-move" type="submit" name="export" id="export" value="<?php print _('Exporter'); ?>" title="<?php print _('Exporter la liste des commandes au format Microsoft Excel'); ?>" />
					<?php
					if( isset($config['export_shipment_active']) && $config['export_shipment_active'] ){
						if( (is_numeric($_GET['state']) && $_GET['state'] == 0) || in_array($_GET['state'], ord_states_get_ord_valid()) ){

							if( isset($config['export_shipment_colissimo']) && !empty($config['export_shipment_colissimo']) ){
								print '<input class="btn-move float-right" type="submit" name="export-colissimo" id="export-colissimo" value="'._('Export Colissimo').'" title="'._('Exporter les étiquettes Colissimo au format TXT').'" />';
							}

							if( isset($config['export_shipment_mondial_relay']) && !empty($config['export_shipment_mondial_relay']) ){
								print '<input class="btn-move float-right" type="submit" name="export-mondial-relay" id="export-mondial-relay" value="'._('Export Mondial Relay').'" title="'._('Exporter les étiquettes Mondial Relay au format CSV').'" />';
							}

							if( isset($config['export_shipment_chronopost']) && !empty($config['export_shipment_chronopost']) ){
								print '<input class="btn-move float-right" type="submit" name="export-chronopost" id="export-chronopost" value="'._('Export Chronopost').'" title="'._('Exporter les étiquettes Chronopost au format CSV').'" />';
							}
							if( isset($config['export_shipment_suivies']) && !empty($config['export_shipment_suivies']) ){
								print '<input class="btn-move float-right" type="submit" name="export-suivies" id="export-suivies" value="'._('Export Lettres de suivies').'" title="'._('Exporter les étiquettes de lettre de suivies au format CSV').'" />';
							}
						}
					}

					if (isset($config['export_dlv_adr_active']) && $config['export_dlv_adr_active']) {
						if (isset($config['export_dlv_adr_chronopost']) && $config['export_dlv_adr_chronopost']) { ?>
						<input class="btn-move" type="submit" name="export-dlv-adr-chronopost" id="export-dlv-adr-chronopost" value="<?php print _('Export carnet d\'adresse Chronopost')?>" title="<?php print _('Exporter les adresses de livraison pour Chronopost au format TXT')?>" />
							<?php
						}
					} ;?>
				</td>
			</tr>
			<?php } ?>
		</tfoot>
	</table>
</form>

<?php if( !isset($_GET['need-sync']) ){ ?>
<script>

	<?php
		// Si un recherche de commande est effectuée, on ne charge pas la popup indiquant le nombre de nouvelle(s) commande(s)
		$popup_new_orders = ( isset($_GET['ref']) && $_GET['ref']!= 0 ) ? false : true;
		view_date_initialized($orders_count, '/admin/orders/orders.php', false, array('refresh_in_ajax' => true,'load_popup_new_orders'=>$popup_new_orders));
	?>

	// Tableau contenant toutes les étapes d'une commande (version au pluriel).
	var tstate = [];
	<?php
		if( $r_states = ord_states_get() ){
			while( $state = ria_mysql_fetch_assoc($r_states) ){
				print 'tstate['.$state['id'].'] = \''.str_replace("'", "\'", $state['name_plural']).'\';'."\n";
			}
		}
	?>
</script>
<?php } ?>
<?php

require_once('admin/skin/footer.inc.php');
