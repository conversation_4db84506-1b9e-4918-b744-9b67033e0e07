<?php

  define('ADMIN_PAGE_TITLE', _('Plus d\'informations'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'popup-iframe');
	require_once('admin/skin/header.inc.php');

  $g_error = $r_line = false;
  if( isset($_GET['ord'], $_GET['prd'], $_GET['line']) ){
    $r_line = ord_products_get( $_GET['ord'], false, $_GET['prd'], $price='', $_GET['line'] );
  }

  if( !$r_line || !ria_mysql_num_rows($r_line) ){
    $g_error = _('La ligne de commande n\'a pas été identifiée.');
  }

  if( $g_error ){
    print '<div class="error">'.nl2br( $g_error ).'</div>';
  }else{
    $line = ria_mysql_fetch_assoc( $r_line );

    // Action sur l'onglet "Général"
    if( isset($_POST['save']) ){
      if( isset($_POST['date_livr']) ){ // Sauvegarde la date de livraison
        $date_livr = '';
        if( isdate($_POST['date_livr']) ){
          $date_livr = $_POST['date_livr'];
        }

        ord_products_set_date( $_GET['ord'], $_GET['prd'], $_GET['line'], $date_livr );
      }

      if( isset($_POST['purchase_avg']) ){ // Sauvegarde le prix d'achat
        $purchase_avg = null;
        if( is_numeric($_POST['purchase_avg']) && $_POST['purchase_avg'] >= 0 ){
          $purchase_avg = $_POST['purchase_avg'];
        }

        ord_products_update_purchase_avg( $_GET['ord'], $_GET['prd'], $_GET['line'], $purchase_avg );
      }

      { // Sauvegarde du statut sur la commande
        if( isset($_POST['state']) && is_numeric($_POST['state']) && $_POST['state'] > 0 && $_POST['state'] != $line['state_line'] ){
          ord_products_set_state( $_GET['ord'], $_GET['prd'], $_GET['line'], $_POST['state'] );
        }
      }

      // Pour Conforama, la ligne est marquée comme à synchroniser suite à la mise à jour de ses informations
			if( in_array($config['tnt_id'], [977, 998, 1043]) ){
				ord_products_set_need_sync( $_GET['ord'], $_GET['prd'], $_GET['line'] );
			}

      $_SESSION['message-success'] = _('Les informations ont bien été mise à jour.');
      header('Location: /admin/orders/popup-data-line-order.php?ord='.$_GET['ord'].'&prd='.$_GET['prd'].'&line='.$_GET['line'].'&edit='.$_GET['edit']);
      exit;
    }

    // Spé CONFORAMA : Annulation d'une commande
    if( isset($_GET['cancelconfo']) && in_array($config['tnt_id'], [977, 998, 1043]) ){
      if( ord_products_update_sage_qte($_GET['ord'], $_GET['prd'], $_GET['line'], 0) ){
        if( ord_products_set_state($_GET['ord'], $_GET['prd'], $_GET['line'], _STATE_CANCEL_MERCHAND) && ord_products_set_is_sync($_GET['ord'], $_GET['prd'], $_GET['line']) ){
          $_SESSION['message-success'] = _('La commande a bien été annulée.');
          header('Location: /admin/orders/popup-data-line-order.php?ord='.$_GET['ord'].'&prd='.$_GET['prd'].'&line='.$_GET['line'].'&edit='.$_GET['edit']);
          exit;
        }
      }
    }

    // Action sur l'onglet "Avancés"
  	view_admin_tab_fields_actions( CLS_ORD_PRODUCT, [$_GET['ord'], $_GET['prd'], $_GET['line']], $config['i18n_lng'] );

    $tab = 'general';
    if( isset($_GET['tab']) && in_array($_GET['tab'], ['general', 'fields']) ){
      $tab = $_GET['tab'];
    }

    if(isset($_POST['tabFields']) ){
      $tab = 'fields';
    }

    $can_edit = isset($_GET['edit']) && $_GET['edit'] == '1';

    if( gu_user_is_authorized('_RGH_ADMIN_ORDER_USE_STATE_LINE') && $can_edit ){
      $state_line = ord_products_get_state( $_GET['ord'], $_GET['prd'], $_GET['line'] );
      if(
        isset($config['admin_ord_states_editable'])
        && is_array($config['admin_ord_states_editable'])
        && in_array($state_line['id'], $config['admin_ord_states_editable'])
      ){
        $can_edit = true;
      }else{
        $can_edit = false;
      }
    }

    $content_infos = $content_fields = '';

    if( isset($_SESSION['message-success']) ){
      print '<div class="success">'.nl2br( $_SESSION['message-success'] ).'</div>';
      unset( $_SESSION['message-success'] );
    }

    print '<form id="cat-form" action="popup-data-line-order.php?ord='.$_GET['ord'].'&prd='.$_GET['prd'].'&line='.$_GET['line'].'&edit='.($can_edit ? '1' : '0').'" method="post" enctype="multipart/form-data">'
      .'<ul class="tabstrip">'
        .'<li><input type="submit" name="tabGeneral" value="'._('Général').'" '.( $tab == 'general' ? 'class="selected"' : '' ).' /></li>'
        .'<li><input type="submit" name="tabFields" value="'._('Avancé').'" '.( $tab == 'fields' ? 'class="selected"' : '' ).' /></li>'
      .'</ul>'

      .'<div id="tabpanel">';


        if( $tab == 'general' ){
          // Information sur la ligne de commande
          ob_start();

          if( is_numeric($line['line_dps_id']) && $line['line_dps_id'] > 0 ){
            print '<tr>'
              .'<td>'._('Dépôt').'</td>'
              .'<td>'
                .'<a target="_blank" href="/admin/config/livraison/deposits/edit.php?dps='.$line['line_dps_id'].'">'.htmlspecialchars( prd_deposits_get_name( $line['line_dps_id'] ) ).'</a>'
              .'</td>'
            .'</tr>';
          }

          if( $can_edit || trim($line['date_livr_en']) != '' ){
            $val_date_livr = '';

            if( trim($line['date_livr_en']) != '' ){
              $date_livr = new DateTime( $line['date_livr_en'] );
              $val_date_livr = $date_livr->format('Y-m-d');
            }

            print '<tr>'
              .'<td>'._('Date de livraison').'</td>'
              .'<td>';

                if( $can_edit ){
                  print '<input type="date" name="date_livr" value="'.$val_date_livr.'" />';
                }else{
                  print ria_date_format( $val_date_livr );
                }

              print '</td>'
            .'</tr>';
          }

          print '<tr>'
            .'<td>'._('Prix d\'achat').'</td>'
            .'<td>';

              if( $can_edit ){
                print '<input type="text" name="purchase_avg" value="'.htmlspecialchars( $line['purchase_avg'] ).'" />';
              }else{
                print htmlspecialchars( $line['purchase_avg'] );
              }

            print '</td>'
          .'</tr>';

          if( gu_user_is_authorized('_RGH_ADMIN_ORDER_USE_STATE_LINE') ){
            $state_line = ord_products_get_state( $_GET['ord'], $_GET['prd'], $_GET['line'] );

            if( is_array($state_line) && ria_array_key_exists(['id', 'name'], $state_line) ){
              $ar_state_access = [];

              $access_state_OK = $config['orders_update_state_included'];

              // Hack Conforama - les status accessible en édition dépende de l'état de la commande
              if( in_array($config['tnt_id'], [977, 998, 1043]) ){
                // Charge le workflow sur la ligne de commande
                $workflow = fld_object_values_get( [$_GET['ord'], $_GET['prd'], $_GET['line']], 114248, '', false, true );

                // Le statut de la ligne est toujours inclus
                $access_state_OK = [ $state_line['id'], _STATE_CANCEL_MERCHAND ];

                switch( $state_line['id'] ){
                  case _STATE_WAIT_PAY : // 3 - Attente de confirmation Conforama
                    $access_state_OK[] = _STATE_ORD_WAIT_CONFIRM; // 4 - En attente de validation franchisé

                    if( in_array($workflow, ['DDS', 'dds']) ){
                      // $access_state_OK[] = _STATE_BL_READY; // 8 - En attente de livraison
                    }else{
                      $access_state_OK[] = _STATE_VALIDATE; // 7 - Confirmées
                    }

                    break;
                  case _STATE_SUPP_CANCEL : // 6 - À annuler au fournisseur
                    $access_state_OK[] = _STATE_CANCEL_MERCHAND; // 0 - Annulée
                    $can_edit = true;
                    break;
                  case _STATE_SUPP_WAIT_CONFIRM : // 5 - A confirmer au fournisseur
                    if( !in_array($workflow, ['DDS', 'dds']) ){
                      $access_state_OK[] = _STATE_VALIDATE; // 7 - Confirmée
                      $can_edit = true; // Pour le workflow VPO, il est encore possible de changer le statut
                    }
                    break;
                }
              }

              $r_state = ord_states_get();
              if( $r_state ){
                while( $state = ria_mysql_fetch_assoc($r_state) ){
                  if( in_array($state['id'], $access_state_OK) ){
                    $ar_state_access[ $state['id'] ] = $state['name'];
                  }
                }
              }

              asort( $ar_state_access );

              if( count($ar_state_access) ){
                print '<tr>'
                  .'<td>'._('Etat').'</td>'
                  .'<td>';

                    if( $can_edit ){
                      print '<select name="state">';
                      foreach( $ar_state_access as $state_id=>$state_name ){
                          $selected = '';
                          if( $state_id == $state_line['id'] ){
                            $selected = 'selected="selected"';
                          }

                          print '<option value="'.$state_id.'" '.$selected.'>'.htmlspecialchars( $state_name ).'</option>';
                        }
                      print '</select>';
                    }else{
                      print htmlspecialchars( $state_line['name'] );

                      // Spé CONFORAMA : Annulation d'une commande
                      if( in_array($config['tnt_id'], [977, 998, 1043]) ){
                        if( ord_orders_get_piece($line['ord_id']) == '' && $state_line['id'] >= 4 && $state_line['id'] != 10 ){
                          print '<br /><a style="color:red" href="/admin/orders/popup-data-line-order.php?ord='.$_GET['ord'].'&prd='.$_GET['prd'].'&line='.$_GET['line'].'&edit='.$_GET['edit'].'&cancelconfo=1">Annuler la commande (réservé au support)';
                        }
                      }
                    }

                  print '</td>'
                .'</tr>';
              }
            }
          }

          $content_infos = ob_get_clean();

          print '<table class="checklist">'
            .'<caption>'._('Informations').'</caption>';

            if( $can_edit ){
              print '<tfoot>'
                .'<tr>'
                  .'<td colspan="2">'
                    .'<input type="submit" name="save" value="'._('Enregistrer').'" class="btn-main" />'
                  .'</td>'
                .'</tr>'
              .'</tfoot>';
            }

            print '<tbody>';

              if( trim($content_infos) != '' ){
                print $content_infos;
              }else{
                print '<tr><td>'._('Aucun information supplémentaire.').'</td></tr>';
              }

            print '</tbody>'
          .'</table>';

          $r_state_line = ord_products_states_get( $line['ord_id'], $line['id'], $line['line'] );
          if( $r_state_line && ria_mysql_num_rows($r_state_line) ){
            print '<table class="checklist">'
              .'<caption>'._('Historique des changements de statut').'</caption>'
              .'<thead>'
                .'<tr>'
                  .'<th id="line-state-date">'._('Date').'</th>'
                  .'<th id="line-state-name">'._('Statut').'</th>'
                  .'<th id="line-state-author">'._('Auteur').'</th>'
                .'</tr>'
              .'</thead>'
              .'<tbody>';

              while( $state_line = ria_mysql_fetch_assoc($r_state_line) ){
                $author = trim( $state_line['firstname'].' '.$state_line['lastname'].' '.$state_line['society'] );
                if( $author == '' ){
                  $author = $state_line['email'];
                }

                print '<tr>'
                  .'<td headers="line-state-date">'.ria_date_format( $state_line['date'] ).'</td>'
                  .'<td headers="line-state-name">'.htmlspecialchars( $state_line['state_name'] ).'</td>'
                  .'<td headers="line-state-author">'
                    .'<a href="/admin/customers/edit.php?usr='.$state_line['usr_id'].'" target="_blank">'
                      .htmlspecialchars( $author )
                    .'</a>'
                  .'</td>'
                .'</tr>';
              }

              print '</tbody>'
            .'</table>';
          }
        }elseif( $tab == 'fields' ){
          print view_admin_tab_fields( CLS_ORD_PRODUCT, [$line['ord_id'], $line['id'], $line['line']], $config['i18n_lng'] );
        }

      print '</div>'
    .'</form>';
  }

  // print '<pre>';
  // print_r($line);
  // print '</pre>';