<?php
    /** \file popup-order-state-history.php
     *  Ce fichier affiche la liste des états par lequel une commande est passée.
     *  \param $_GET['ord'] Identifiant d'une commande
     */

    // Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER');

    require_once('orders.inc.php');

    $ord_id = isset($_GET['ord']) ? intval($_GET['ord']) : 0;

    if ($ord_id > 0) {
        $states = ord_orders_states_get($ord_id, 0, array('datetime' => 'asc'));
    }

    define('ADMIN_HEAD_POPUP', true);
    define('ADMIN_ID_BODY', 'popup-content');
    define('ADMIN_CLASS_BODY', 'popup-iframe popup-order-state-history');
    require_once('admin/skin/header.inc.php');
?>
<form>
    <div class="popup-content">
        <h2><?php print _('Etats de la commande'); ?></h2>
        <table width="100%">
            <col width="180" /><col width="*" />
            <thead>
                <tr>
                    <th><?php print _('Date de modification')?></th>
                    <th><?php print _('Etat')?></th>
                    <th><?php print _('Utilisateur')?></th>
                </tr>
            </thead>
            <tbody>
            <?php
            while ($states && $ord_state = ria_mysql_fetch_assoc($states)) {
                ?>
                <tr>
                    <td><?php print htmlspecialchars(ria_date_format($ord_state['date'])) ?></td>
                    <td><?php print htmlspecialchars($ord_state['state_name']) ?></td>
                    <td><?php
                        $usr_id = !$ord_state['raw_usr_id'] && $ord_state['state_id'] == _STATE_BASKET ? $ord_state['usr_id'] : $ord_state['raw_usr_id'];
                        if (!$usr_id) {
                            print 'Aucun compte client';
                        } else {
                            $ref = gu_users_get_ref($usr_id);
                            if (!$ref) {
                                $ref = str_pad($usr_id, 8, '0', STR_PAD_LEFT);
                            }

                            $label = $ref;
                            $r_user = gu_users_get($usr_id);
                            if ($r_user && ria_mysql_num_rows($r_user)) {
                                $user = ria_mysql_fetch_assoc($r_user);
                                if ($user['adr_firstname'] && $user['adr_lastname']) {
                                    $label = ucwords($user['adr_firstname']) . ' ' . strtoupper($user['adr_lastname']);
                                    if ($user['society']) {
                                        $label .= ' ' . trim($user['society']);
                                    }
                                } elseif ($user['email']) {
                                    $label = $user['email'];
                                }
                            }

                            print '<a class="usr-link" href="/admin/customers/edit.php?usr=' . intval($usr_id) . '">';
                            print view_usr_is_sync($user) . htmlspecialchars(trim($label));
                            print '</a>';
                        }
                        ?>
                    </td>
                </tr>

                <?php
            }
            ?>
            </tbody>
        </table>
    </div>

    <div id="pagination" class="popup-content">
        <div class="linked"><input type="button" name="cancel" value="<?php echo _("Annuler"); ?>" onclick="parent.hidePopup();" class="btn-cancel" /></div>
    </div>
</form>
<script>
    $(document)
        .ready(function(){
            $('#pagination').scrollToFixed({
                left: 0,
                bottom: 0,
                offsets: true
            });
        })
        .delegate('.usr-link', 'click', function(event){
            event.preventDefault();
            window.parent.location = $(event.target).attr('href');
        });
</script>

<?php
    require_once('admin/skin/footer.inc.php');