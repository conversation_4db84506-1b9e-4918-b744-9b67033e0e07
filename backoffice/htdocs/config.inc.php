<?php
// Numéro de version des fichiers JS et CSS pour forcer le rechargement
define('ADMIN_ASSET', 'p-004');

// numéro de version des images
define('ADMIN_ASSET_IMGS', 'v-2');

// Ajout d'une constante permettant de savoir qu'on est dans l'administration
define('CONTEXT_IS_ADMIN', true);

require_once('db.inc.php');

$config = [];

// Initialise le tenant et le website par défaut
$config['tnt_id'] = getenv('ENVRIA_TNT_ID');


// Réalise la connexion à la base de données
require_once('cfg.variables.inc.php');
require_once('tenants.inc.php');

$config['wst_id'] = tnt_tenants_get_website_default($config['tnt_id']);

// Charge les variables de configuration pour le site par défaut
cfg_variables_load($config);
cfg_images_load($config);
cfg_products_load($config);

$config['use_catalog_restrictions'] = false;

require_once('users.inc.php');
require_once('http.inc.php');

// Tableau contenant des URLs pouvant être exclut du contrôle du referer (appelée par des outils externe)
$exclude_ctrl_referer = ['^\/simplesaml\/', '^\/saml2\/', '^\/saml2-asc\/', '^\/saml2-metadata\/'];

// On regarde si l'URL appelé est exclue du contrôle de referer
$ctrl_referer = true;
foreach ($exclude_ctrl_referer as $one_regex) {
	if (preg_match('/' . $one_regex . '/', $_SERVER['PHP_SELF'])) {
		$ctrl_referer = false;
		break;
	}
}

// Protection contre les attaques de type CSRF
if ($ctrl_referer) {
	http_check_referrer();
}

$_SERVER['SCRIPT_URL'] = isset($_SERVER['SCRIPT_URL']) ? $_SERVER['SCRIPT_URL'] : $_SERVER['SCRIPT_NAME'];

{ // Initialisation de l'environnement pour la gestion de l'internationalisation
	$accepted_languages = array('fr_FR', 'en_GB', 'de_DE');

	// Le choix de la langue se fait dans cette priorité :
	//		1 - Si l'administrateur est connecté on essaye de récupérer l'information dans usr_lng_code ($_SESSION['lang']);
	//		2 - Si non renseigné on récupère la langue du navigateur
	//		3 - Si la langue du navigateur n'est pas supportée, on prendra alors l'anglais

	$lang = 'en_GB';

	if (isset($_GET['lang']) && in_array($_GET['lang'], $accepted_languages)) {
		$lang = $_SESSION['lang'] = $_GET['lang'];
	} elseif (isset($_SESSION['lang']) && !empty($_SESSION['lang']) && in_array($_SESSION['lang'], $accepted_languages)) {
		$lang = $_SESSION['lang'];
	} elseif (!empty($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
		// Version longue de la langue du navigateur
		$lang_browser_long = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 5);

		if (in_array($lang_browser_long, $accepted_languages)) {
			$lang = $lang_browser_long;
		} else {
			// Version courte de la langue du navigateur
			$lang_browser = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 2);

			switch ($lang_browser) {
				case 'fr':
					$lang = 'fr_FR';
					break;
				case 'en':
					$lang = 'en_GB';
					break;
				case 'de':
					$lang = 'de_DE';
					break;
			}
		}
		$_SESSION['lang'] = $lang;
	}

	putenv('LC_MESSAGES=' . $lang . '.utf8');

	setlocale(LC_MESSAGES, $lang . '.utf8');

	bindtextdomain('riashop', __DIR__ . '/_locale');
	textdomain('riashop');
	bind_textdomain_codeset('riashop', 'UTF-8');
}

if (!isset($_SERVER['PHP_SELF'])) {
	$_SERVER['PHP_SELF'] = '';
}

// URL accessible sans connexion
$ar_nologin_urls = [

	// Connexion
	'/login.php', '/admin/login.php',
	// Déconnexion
	'/exit.php', '/admin/exit.php',

	// Permet le choix du locataire sur lequel on va travailler (réservé aux utilisateurs de RiaStudio)
	'/popup-select-tenant.php', '/admin/popup-select-tenant.php',

	// Connexion Okta de DEV (via SAML2)
	'/simplesaml/module.php/saml/sp/saml2-acs.php/622888e04d5b9',
	'/simplesaml/module.php/saml/sp/metadata.php/622888e04d5b9',
	'/saml/622888e04d5b9-saml.php',
	'/simplesaml/module.php/saml/sp/saml2-acs.php/94at35hkmg',
	'/simplesaml/module.php/saml/sp/metadata.php/94at35hkmg',
	'/saml/94at35hkmg-okta.php',
	'/admin/js/translates.php',
	'/js/translates.php',

	// SVN Tool - Maintenance interface
	'/svn-tool/',
	'/svn-tool/index.php',
	'/svn-tool/api.php'
];

if (!in_array($_SERVER['PHP_SELF'], $ar_nologin_urls)) {
	// Si l'utilisateur n'est pas connecté :
	if( !gu_users_is_connected() ){
		if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] == 'XMLHttpRequest') {
			// Si la requête est de type Ajax, la redirection vers la page de connexion ne servira à rien.
			// Envoie plutôt une réponse qui permettra d'informer l'absence de connexion
			header('HTTP/1.0 401 Unauthorized');
			header('Content-Type: application/xml');
			echo '<?xml version="1.0" encoding="utf8"?>';
			echo '<error code="401" msg="' . _('Votre session est terminée, vous avez été déconnecté(e). Veuillez vous authentifier à nouveau.') . '" />';
			exit;
		} else {
			gu_users_disconnect();
			// Si l'utilisateur n'est pas connecté, il est redirigé vers la page de connexion
			header('Location: /login.php?dest=' . urlencode($_SERVER['REQUEST_URI']));
			exit;
		}
	}

	// Informe RiaShop qu'un tenant a bien été choisi
	$_SESSION['success_init_tenant'] = true;

	// Identification des super-admins
	$config['USER_RIASTUDIO'] = false;
	if (strstr($_SESSION['usr_email'], '@riastudio.fr') ||  strstr($_SESSION['usr_email'], '@kontinuum.fr') || strstr($_SESSION['usr_email'], '@yuto.com') || strstr($_SESSION['usr_email'], '@lundimatin.fr') ) {
		$config['USER_RIASTUDIO'] = true;
	}

	if (ria_array_get($config, 'seller_admin_access', false) && $_SESSION['usr_prf_id'] == PRF_SELLER) {
		$_SESSION['ord_seller_id'] = $_SESSION['usr_seller_id'];
		if (isset($_GET['sellers'])) {
			$_GET['sellers'] = $_SESSION['ord_seller_id'];
		}
	}

	if (isset($config['msg_types_used']) && (is_array($config['msg_types_used']) && sizeof($config['msg_types_used']))) {
		$config['msg_types_used'][] = 'DIRECT_CONTACT';
	}

	$config['admin_submenu_state'] = 1; // Par défaut le sous-menu est ouvert

	// Chargement de configuration lié au compte administrateur (bdd)
	if (isset($_SESSION['usr_id'])) {
		if (gu_users_is_tenant_linked($_SESSION['usr_id'], $_SESSION['usr_email']) && !in_array($config['tnt_id'], array(16, 1))) {
			$config['price_watching_active'] = false;
		}

		// On récupère les variables de config 'admin-list-prds-cols' et 'admin_submenu_paneling_state'
		$code_override = array('admin-list-prds-cols', 'admin_submenu_paneling_state');

		$r_val = cfg_overrides_get(0, array(), $code_override, $_SESSION['usr_id']);
		$nb_overrides = $r_val ? ria_mysql_num_rows($r_val) : 0;
		if ($nb_overrides) {
			while ($val = ria_mysql_fetch_assoc($r_val)) {
				if ($val['code'] == 'admin-list-prds-cols') {
					$_SESSION['usr-admin-list-prds-cols'] = explode(',', $val['value']);
				} else if ($val['code'] == 'admin_submenu_paneling_state') {
					// Si on a un état de sous-menu enregistré, on le récupère
					$config['admin_submenu_state'] = $val['value'];
				}
			}
			ria_mysql_data_seek($r_val, 0);
		}

		load_list_cols_products();
	}

	// Initialise les variables de période
	require_once('view.admin.inc.php');
	view_admin_datepicker_init();

	// Définit la devise monétaire du client à partir de la catégorie tarifaire par défaut.
	if ($prc_id = $config['default_prc_id']) {
		$prc = ria_mysql_fetch_assoc(
			prd_prices_categories_get($prc_id)
		);

		$_SESSION['admin_currency'] = $prc['money_code'];
	}
}else{
	if( in_array($config['tnt_id'], [977, 1043, 998]) && in_array($_SERVER['PHP_SELF'], ['/admin/exit.php', '/exit.php']) ){
		if( isset($_SESSION['admin_datepicker_init']) ){
			unset( $_SESSION['admin_datepicker_init'] );
		}
	}
}
