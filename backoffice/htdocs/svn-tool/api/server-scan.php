<?php
/**
 * Server Scanner API Endpoint
 */

require_once '../config/config.php';
require_once '../models/ServerScanner.php';

// Set proper headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
    exit;
}

// Validate request
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Invalid request']);
    exit;
}

try {
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON data');
    }
    
    $scanPath = isset($input['path']) ? trim($input['path']) : '/var/www';
    $maxDepth = isset($input['depth']) ? intval($input['depth']) : 3;
    
    // Validate scan path
    if (empty($scanPath) || !is_dir($scanPath)) {
        throw new Exception('Invalid scan path: ' . $scanPath);
    }
    
    // Security check - prevent scanning sensitive directories
    $restrictedPaths = ['/etc', '/usr', '/bin', '/sbin', '/boot', '/root'];
    foreach ($restrictedPaths as $restricted) {
        if (strpos($scanPath, $restricted) === 0) {
            throw new Exception('Access denied to restricted path: ' . $scanPath);
        }
    }
    
    // Initialize scanner
    $scanner = new ServerScanner();
    $scanner->setMaxDepth($maxDepth);
    
    // Log scan start
    error_log("ServerScan: Starting scan of {$scanPath} with depth {$maxDepth}");
    
    // Perform scan
    $repositories = $scanner->scanForSvnRepositories($scanPath);
    
    // Format response
    $response = [
        'status' => 'success',
        'data' => $repositories,
        'meta' => [
            'scan_path' => $scanPath,
            'max_depth' => $maxDepth,
            'total_found' => count($repositories),
            'scan_time' => date('Y-m-d H:i:s')
        ]
    ];
    
    error_log("ServerScan: Completed successfully. Found " . count($repositories) . " repositories");
    
} catch (Exception $e) {
    error_log("ServerScan: Error - " . $e->getMessage());
    
    $response = [
        'status' => 'error',
        'message' => $e->getMessage(),
        'code' => $e->getCode() ?: 500
    ];
    
    http_response_code(500);
}

echo json_encode($response);
?>
