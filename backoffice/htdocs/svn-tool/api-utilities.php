<?php
/**
 * SVN Tool API Utility Functions
 * 
 * Utility functions for SVN operations
 */

if (!function_exists('commitFiles')) {
    function commitFiles($repoPath, $files, $message) {
        // Check if SVN is available
        $svnPath = trim(shell_exec('which svn 2>/dev/null'));
        if (empty($svnPath)) {
            throw new Exception('SVN command not found on system');
        }
        
        // Change to repository directory
        $oldCwd = getcwd();
        if (!chdir($repoPath)) {
            throw new Exception('Cannot change to repository directory');
        }
        
        try {
            // Add files to SVN
            foreach ($files as $file) {
                $escapedFile = escapeshellarg($file);
                $output = array();
                $returnCode = 0;
                exec("svn add {$escapedFile} 2>&1", $output, $returnCode);

                // Check for specific errors
                $outputStr = implode(' ', $output);
                if ($returnCode !== 0 && strpos($outputStr, 'already under version control') === false) {
                    // Handle database lock errors
                    if (strpos($outputStr, 'readonly database') !== false ||
                        strpos($outputStr, 'E200031') !== false ||
                        strpos($outputStr, 'E155004') !== false) {

                        // Try to fix permissions and cleanup
                        exec('find . -name ".svn" -type d -exec chmod -R 775 {} \\; 2>&1');
                        exec('find . -name "*.db" -exec chmod 664 {} \\; 2>&1');
                        exec('svn cleanup 2>&1');

                        // Retry the add operation
                        $retryOutput = array();
                        $retryReturnCode = 0;
                        exec("svn add {$escapedFile} 2>&1", $retryOutput, $retryReturnCode);

                        if ($retryReturnCode !== 0 && strpos(implode(' ', $retryOutput), 'already under version control') === false) {
                            throw new Exception('SVN add failed for ' . $file . ' even after cleanup: ' . implode(' ', $retryOutput));
                        }
                    } else {
                        throw new Exception('SVN add failed for ' . $file . ': ' . $outputStr);
                    }
                }
            }

            // Commit files with enhanced error handling
            $escapedMessage = escapeshellarg($message);
            $fileList = implode(' ', array_map('escapeshellarg', $files));
            $output = array();
            $returnCode = 0;

            // Build SVN command with credentials if available
            $svnCommand = "svn commit --non-interactive -m {$escapedMessage}";

            // Add credentials if stored
            $credentials = getSvnCredentials();
            if ($credentials && isset($credentials['username']) && isset($credentials['password'])) {
                $svnCommand .= " --username " . escapeshellarg($credentials['username']);
                $svnCommand .= " --password " . escapeshellarg($credentials['password']);
                $svnCommand .= " --trust-server-cert";
            }

            $svnCommand .= " {$fileList} 2>&1";

            exec($svnCommand, $output, $returnCode);

            if ($returnCode !== 0) {
                $outputStr = implode(' ', $output);

                // Handle various SVN errors
                if (strpos($outputStr, 'readonly database') !== false ||
                    strpos($outputStr, 'E200031') !== false ||
                    strpos($outputStr, 'E155004') !== false ||
                    strpos($outputStr, 'locked') !== false) {

                    // Try comprehensive cleanup and permission fix
                    exec('svn cleanup 2>&1');
                    exec('find . -name ".svn" -type d -exec chmod -R 775 {} \\; 2>&1');
                    exec('find . -name "*.db" -exec chmod 664 {} \\; 2>&1');

                    // Wait a moment for filesystem to settle
                    sleep(1);

                    // Retry the commit operation with credentials
                    $retryOutput = array();
                    $retryReturnCode = 0;

                    $retrySvnCommand = "svn commit --non-interactive -m {$escapedMessage}";
                    if ($credentials && isset($credentials['username']) && isset($credentials['password'])) {
                        $retrySvnCommand .= " --username " . escapeshellarg($credentials['username']);
                        $retrySvnCommand .= " --password " . escapeshellarg($credentials['password']);
                        $retrySvnCommand .= " --trust-server-cert";
                    }
                    $retrySvnCommand .= " {$fileList} 2>&1";

                    exec($retrySvnCommand, $retryOutput, $retryReturnCode);

                    if ($retryReturnCode !== 0) {
                        $retryOutputStr = implode(' ', $retryOutput);

                        // Check for authentication issues
                        if (strpos($retryOutputStr, 'Authentication') !== false ||
                            strpos($retryOutputStr, 'E170013') !== false ||
                            strpos($retryOutputStr, 'E215004') !== false) {
                            throw new Exception('SVN commit failed: Authentication required. Please configure SVN credentials.');
                        } else {
                            throw new Exception('SVN commit failed even after cleanup and permission fixes: ' . $retryOutputStr);
                        }
                    }

                    $output = $retryOutput;
                } elseif (strpos($outputStr, 'Authentication') !== false ||
                          strpos($outputStr, 'E170013') !== false ||
                          strpos($outputStr, 'E215004') !== false) {
                    throw new Exception('SVN commit failed: Authentication required. Please configure SVN credentials or check network connectivity.');
                } else {
                    throw new Exception('SVN commit failed: ' . $outputStr);
                }
            }
            
            return array(
                'committed_files' => $files,
                'message' => $message,
                'output' => $output
            );
            
        } finally {
            chdir($oldCwd);
        }
    }
}

if (!function_exists('cleanupRepository')) {
    function cleanupRepository($repoPath) {
        // Check if SVN is available
        $svnPath = trim(shell_exec('which svn 2>/dev/null'));
        if (empty($svnPath)) {
            throw new Exception('SVN command not found on system');
        }

        // Change to repository directory
        $oldCwd = getcwd();
        if (!chdir($repoPath)) {
            throw new Exception('Cannot change to repository directory');
        }

        try {
            $allOutput = array();

            // First, try to fix permissions if we have readonly database issues
            $permOutput = array();
            $permReturnCode = 0;
            exec('find . -name ".svn" -type d -exec chmod 755 {} \\; 2>&1', $permOutput, $permReturnCode);
            if ($permReturnCode === 0) {
                $allOutput[] = 'Fixed .svn directory permissions';
            }

            // Try to fix SQLite database permissions specifically
            $dbOutput = array();
            $dbReturnCode = 0;
            exec('find . -name "wc.db" -exec chmod 664 {} \\; 2>&1', $dbOutput, $dbReturnCode);
            if ($dbReturnCode === 0) {
                $allOutput[] = 'Fixed SVN database permissions';
            }

            // Run SVN cleanup command
            $output = array();
            $returnCode = 0;
            exec('svn cleanup 2>&1', $output, $returnCode);

            if ($returnCode !== 0) {
                $errorOutput = implode(' ', $output);

                // Handle specific readonly database error
                if (strpos($errorOutput, 'readonly database') !== false || strpos($errorOutput, 'E200031') !== false) {
                    // Try more aggressive permission fixing
                    exec('find . -name ".svn" -type d -exec chmod -R 775 {} \\; 2>&1', $permOutput2, $permReturnCode2);
                    exec('find . -name "*.db" -exec chmod 664 {} \\; 2>&1', $dbOutput2, $dbReturnCode2);

                    // Try cleanup again
                    $output2 = array();
                    $returnCode2 = 0;
                    exec('svn cleanup 2>&1', $output2, $returnCode2);

                    if ($returnCode2 !== 0) {
                        throw new Exception('SVN cleanup failed even after permission fixes: ' . implode(' ', $output2));
                    }

                    $allOutput[] = 'Applied aggressive permission fixes';
                    $allOutput = array_merge($allOutput, $output2);
                } else {
                    throw new Exception('SVN cleanup failed: ' . $errorOutput);
                }
            } else {
                $allOutput = array_merge($allOutput, $output);
            }

            return array(
                'output' => $allOutput,
                'message' => 'Repository cleanup completed successfully'
            );

        } finally {
            chdir($oldCwd);
        }
    }
}

if (!function_exists('fixSvnPermissions')) {
    function fixSvnPermissions($repoPath) {
        // Change to repository directory
        $oldCwd = getcwd();
        if (!chdir($repoPath)) {
            throw new Exception('Cannot change to repository directory');
        }

        try {
            $output = array();
            $allCommands = array();

            // Fix .svn directory permissions
            $cmd1 = 'find . -name ".svn" -type d -exec chmod 755 {} \\; 2>&1';
            exec($cmd1, $output1, $returnCode1);
            $allCommands[] = array('command' => $cmd1, 'output' => $output1, 'return_code' => $returnCode1);

            // Fix SVN database file permissions
            $cmd2 = 'find . -name "wc.db" -exec chmod 664 {} \\; 2>&1';
            exec($cmd2, $output2, $returnCode2);
            $allCommands[] = array('command' => $cmd2, 'output' => $output2, 'return_code' => $returnCode2);

            // Fix all files in .svn directories
            $cmd3 = 'find . -path "*/.svn/*" -type f -exec chmod 644 {} \\; 2>&1';
            exec($cmd3, $output3, $returnCode3);
            $allCommands[] = array('command' => $cmd3, 'output' => $output3, 'return_code' => $returnCode3);

            // Try to change ownership if possible (may fail in Docker without proper privileges)
            $cmd4 = 'find . -name ".svn" -type d -exec chown www-data:www-data {} \\; 2>/dev/null';
            exec($cmd4, $output4, $returnCode4);
            $allCommands[] = array('command' => $cmd4, 'output' => $output4, 'return_code' => $returnCode4);

            return array(
                'message' => 'SVN permissions fixed',
                'commands_executed' => $allCommands,
                'success' => true
            );

        } finally {
            chdir($oldCwd);
        }
    }
}

if (!function_exists('checkRepositoryPermissions')) {
    function checkRepositoryPermissions($repoPath) {
        $permissions = array();
        
        // Check directory permissions
        $permissions['directory'] = array(
            'path' => $repoPath,
            'readable' => is_readable($repoPath),
            'writable' => is_writable($repoPath),
            'executable' => is_executable($repoPath),
            'owner' => function_exists('posix_getpwuid') ? posix_getpwuid(fileowner($repoPath)) : 'unknown',
            'group' => function_exists('posix_getgrgid') ? posix_getgrgid(filegroup($repoPath)) : 'unknown',
            'perms' => substr(sprintf('%o', fileperms($repoPath)), -4)
        );
        
        // Check .svn directory if it exists
        $svnDir = $repoPath . '/.svn';
        if (is_dir($svnDir)) {
            $permissions['svn_directory'] = array(
                'path' => $svnDir,
                'readable' => is_readable($svnDir),
                'writable' => is_writable($svnDir),
                'executable' => is_executable($svnDir),
                'perms' => substr(sprintf('%o', fileperms($svnDir)), -4)
            );
        }
        
        return $permissions;
    }
}

if (!function_exists('getFileDiff')) {
    function getFileDiff($repoPath, $file) {
        // Check if SVN is available
        $svnPath = trim(shell_exec('which svn 2>/dev/null'));
        if (empty($svnPath)) {
            throw new Exception('SVN command not found on system');
        }
        
        // Change to repository directory
        $oldCwd = getcwd();
        if (!chdir($repoPath)) {
            throw new Exception('Cannot change to repository directory');
        }
        
        try {
            // Build SVN diff command with credentials if available
            $escapedFile = escapeshellarg($file);
            $diffCommand = "svn diff {$escapedFile}";

            $credentials = getSvnCredentials();
            if ($credentials && isset($credentials['username']) && isset($credentials['password'])) {
                $diffCommand .= " --username " . escapeshellarg($credentials['username']);
                $diffCommand .= " --password " . escapeshellarg($credentials['password']);
                $diffCommand .= " --trust-server-cert";
            }
            $diffCommand .= ' 2>&1';

            // Run SVN diff command
            $output = array();
            $returnCode = 0;
            exec($diffCommand, $output, $returnCode);

            if ($returnCode !== 0 && !empty($output) && strpos(implode(' ', $output), 'svn: E') !== false) {
                $outputStr = implode(' ', $output);
                // Check for authentication errors
                if (strpos($outputStr, 'Authentication') !== false ||
                    strpos($outputStr, 'E170013') !== false ||
                    strpos($outputStr, 'E215004') !== false) {
                    throw new Exception('SVN diff failed: Authentication required. Please configure SVN credentials.');
                }
                throw new Exception('SVN diff failed: ' . $outputStr);
            }
            
            return array(
                'file' => $file,
                'diff' => implode("\n", $output),
                'has_changes' => !empty($output)
            );
            
        } finally {
            chdir($oldCwd);
        }
    }
}

if (!function_exists('checkSvnConnectivity')) {
    function checkSvnConnectivity($repoPath) {
        // Change to repository directory
        $oldCwd = getcwd();
        if (!chdir($repoPath)) {
            throw new Exception('Cannot change to repository directory');
        }

        try {
            $result = array(
                'local_status' => 'unknown',
                'remote_connectivity' => 'unknown',
                'authentication_status' => 'unknown',
                'repository_info' => array(),
                'recommendations' => array()
            );

            // Check local SVN status
            $statusOutput = array();
            $statusReturnCode = 0;
            exec('svn status 2>&1', $statusOutput, $statusReturnCode);

            if ($statusReturnCode === 0) {
                $result['local_status'] = 'ok';
            } else {
                $result['local_status'] = 'error';
                $result['recommendations'][] = 'Local SVN working copy has issues. Try running cleanup.';
            }

            // Check repository info (this will test connectivity)
            $infoOutput = array();
            $infoReturnCode = 0;

            // Build SVN info command with credentials if available
            $infoCommand = 'svn info --non-interactive';
            $credentials = getSvnCredentials();
            if ($credentials && isset($credentials['username']) && isset($credentials['password'])) {
                $infoCommand .= " --username " . escapeshellarg($credentials['username']);
                $infoCommand .= " --password " . escapeshellarg($credentials['password']);
                $infoCommand .= " --trust-server-cert";
            }
            $infoCommand .= ' 2>&1';

            exec($infoCommand, $infoOutput, $infoReturnCode);

            if ($infoReturnCode === 0) {
                $result['remote_connectivity'] = 'ok';
                $result['authentication_status'] = 'ok';

                // Parse repository info
                foreach ($infoOutput as $line) {
                    if (strpos($line, 'URL:') === 0) {
                        $result['repository_info']['url'] = trim(substr($line, 4));
                    } elseif (strpos($line, 'Revision:') === 0) {
                        $result['repository_info']['revision'] = trim(substr($line, 9));
                    } elseif (strpos($line, 'Last Changed Author:') === 0) {
                        $result['repository_info']['last_author'] = trim(substr($line, 20));
                    }
                }
            } else {
                $infoOutputStr = implode(' ', $infoOutput);

                if (strpos($infoOutputStr, 'Authentication') !== false ||
                    strpos($infoOutputStr, 'E215004') !== false) {
                    $result['remote_connectivity'] = 'ok';
                    $result['authentication_status'] = 'failed';
                    $result['recommendations'][] = 'SVN server is reachable but authentication failed. Configure SVN credentials.';
                } elseif (strpos($infoOutputStr, 'E170013') !== false ||
                          strpos($infoOutputStr, 'Unable to connect') !== false) {
                    $result['remote_connectivity'] = 'failed';
                    $result['authentication_status'] = 'unknown';
                    $result['recommendations'][] = 'Cannot connect to SVN server. Check network connectivity.';
                } else {
                    $result['remote_connectivity'] = 'error';
                    $result['authentication_status'] = 'unknown';
                    $result['recommendations'][] = 'Unknown SVN connectivity issue: ' . $infoOutputStr;
                }
            }

            return $result;

        } finally {
            chdir($oldCwd);
        }
    }
}

/**
 * SVN Credential Management Functions
 * PHP 5.6 compatible secure credential storage
 */

if (!function_exists('storeSvnCredentials')) {
    function storeSvnCredentials($username, $password, $remember = true) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Validate inputs
        if (empty($username) || empty($password)) {
            throw new Exception('Username and password cannot be empty');
        }

        // Store credentials in session (encrypted)
        $credentials = array(
            'username' => $username,
            'password' => base64_encode($password), // Basic encoding for PHP 5.6
            'timestamp' => time(),
            'remember' => $remember
        );

        $_SESSION['svn_credentials'] = $credentials;

        return array(
            'stored' => true,
            'username' => $username,
            'remember' => $remember,
            'timestamp' => $credentials['timestamp']
        );
    }
}

if (!function_exists('getSvnCredentials')) {
    function getSvnCredentials() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        if (!isset($_SESSION['svn_credentials'])) {
            return null;
        }

        $credentials = $_SESSION['svn_credentials'];

        // Check if credentials are expired (1 hour for session-only, 24 hours for remembered)
        $maxAge = $credentials['remember'] ? 86400 : 3600; // 24h or 1h
        if (time() - $credentials['timestamp'] > $maxAge) {
            unset($_SESSION['svn_credentials']);
            return null;
        }

        return array(
            'username' => $credentials['username'],
            'password' => base64_decode($credentials['password']),
            'timestamp' => $credentials['timestamp'],
            'remember' => $credentials['remember']
        );
    }
}

if (!function_exists('hasSvnCredentials')) {
    function hasSvnCredentials() {
        $credentials = getSvnCredentials();
        return $credentials !== null;
    }
}

if (!function_exists('clearSvnCredentials')) {
    function clearSvnCredentials() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        if (isset($_SESSION['svn_credentials'])) {
            unset($_SESSION['svn_credentials']);
        }

        return array(
            'cleared' => true,
            'timestamp' => time()
        );
    }
}
?>
