# SVN Authentication Modal System

## Overview

This document describes the SVN authentication modal system implemented for the SVN Tool project. The system provides automatic detection of SVN authentication failures and presents a secure credential input modal following the existing glass morphism design aesthetic.

## Features

### 1. Authentication Error Detection
- **Automatic detection** of SVN authentication failures in API responses
- **Pattern matching** for common SVN authentication error codes:
  - `E215004` - Authentication failed
  - `E170013` - Unable to connect to repository
  - `Authentication required`
  - `Authentication failed`
  - `No more credentials`

### 2. Glass Morphism Authentication Modal
- **Consistent design** following the existing SVN tool glass morphism aesthetic
- **Neutral colors** with transparency effects (rgba(255,255,255,0.25-0.4))
- **Backdrop blur effects** for professional appearance
- **Responsive design** that works on all screen sizes

### 3. Secure Credential Management
- **Session-based storage** with PHP 5.6 compatibility
- **Base64 encoding** for basic password protection
- **Configurable expiration** (1 hour for session-only, 24 hours for remembered)
- **CSRF token protection** following existing RiaShop patterns
- **Automatic cleanup** of expired credentials

### 4. Integration with SVN Operations
- **Automatic credential injection** into SVN commands
- **Retry mechanism** for failed operations after authentication
- **Support for all SVN operations**: commit, cleanup, status, diff, info
- **Trust server certificate** option for self-signed certificates

## Implementation Details

### Files Modified/Created

1. **CSS Styling** (`assets/css/svn-tool.css`)
   - Added `.auth-modal` styles with glass morphism effects
   - Credential status indicators
   - Loading states for authentication buttons

2. **JavaScript Authentication Manager** (`assets/js/svn-tool.js`)
   - `AuthManager` object with complete authentication workflow
   - Error detection and modal management
   - Credential storage and retrieval via API
   - Integration with existing API request system

3. **HTML Modal** (`index.php`)
   - Authentication modal with secure form
   - CSRF token integration
   - Credential status indicators
   - Management buttons for stored credentials

4. **API Endpoints** (`api.php`, `api-functions.php`)
   - `store_credentials` - Store user credentials securely
   - `check_credentials` - Check if credentials are stored
   - `clear_credentials` - Clear stored credentials

5. **Credential Utilities** (`api-utilities.php`)
   - `storeSvnCredentials()` - Secure credential storage
   - `getSvnCredentials()` - Retrieve stored credentials
   - `hasSvnCredentials()` - Check credential availability
   - `clearSvnCredentials()` - Clear stored credentials

6. **SVN Command Integration**
   - Modified all SVN command executions to use stored credentials
   - Added `--username`, `--password`, and `--trust-server-cert` options
   - Enhanced error handling for authentication failures

### Security Features

1. **CSRF Protection**
   - All authentication requests include CSRF tokens
   - Token validation on server side
   - Follows existing RiaShop CSRF patterns

2. **Session Security**
   - Credentials stored only in PHP sessions
   - Automatic expiration based on user preference
   - No persistent storage on disk

3. **Input Validation**
   - Server-side validation of username/password
   - XSS protection with proper escaping
   - SQL injection prevention (no database storage)

4. **Password Handling**
   - Base64 encoding for basic protection (PHP 5.6 compatible)
   - No plaintext storage in logs or debug output
   - Automatic masking in command logs

## Usage Workflow

### 1. Automatic Authentication
1. User performs SVN operation (commit, cleanup, etc.)
2. System detects authentication error in SVN output
3. Authentication modal appears automatically
4. User enters credentials
5. Operation retries with stored credentials

### 2. Manual Credential Management
1. User clicks "Gérer les identifiants" button
2. Authentication modal opens
3. User enters/updates credentials
4. Credentials stored for future operations
5. "Effacer les identifiants" button becomes available

### 3. Credential Status
- **Green indicator**: Credentials are stored and valid
- **Orange indicator**: No credentials stored
- **Automatic updates**: Status updates based on credential availability

## Error Handling

### Authentication Errors
- **E215004**: Invalid username/password
- **E170013**: Cannot connect to repository
- **Network errors**: Connection timeouts, DNS failures
- **Certificate errors**: Self-signed certificate issues

### Recovery Mechanisms
- **Automatic retry** after successful authentication
- **Clear error messages** in modal
- **Fallback to manual credential entry**
- **Session cleanup** on repeated failures

## PHP 5.6 Compatibility

### Features Used
- `openssl_random_pseudo_bytes()` for token generation
- `base64_encode()/decode()` for password encoding
- Session-based storage (no modern encryption)
- Traditional array syntax
- No type declarations or return types

### Limitations
- Basic password encoding (not modern encryption)
- Session-only storage (no persistent credential store)
- Limited to HTTP authentication methods

## Docker Environment Support

### Container Integration
- **Path detection**: Works with Docker container file paths
- **Permission handling**: Respects container user permissions
- **Network access**: Supports SVN over HTTP/HTTPS in containers
- **Log integration**: Compatible with container logging

### Common Docker Paths
- `/var/www/sites/front` - Frontend repositories
- `/var/www/sites` - General site repositories
- `/var/www/riashop` - RiaShop engine
- `/var/www/engine` - Engine repositories

## Testing

### Test File
Use `test-auth-system.php` to verify:
- Credential storage functions
- Authentication error detection
- Modal display and interaction
- CSRF token handling
- Session management

### Manual Testing
1. Configure repository path
2. Attempt SVN operation without credentials
3. Verify modal appears
4. Enter test credentials
5. Verify operation retries
6. Test credential management buttons

## Troubleshooting

### Common Issues
1. **Modal doesn't appear**: Check JavaScript console for errors
2. **Credentials not stored**: Verify session is working
3. **SVN still fails**: Check network connectivity and repository URL
4. **CSRF errors**: Ensure token is properly included in requests

### Debug Steps
1. Check browser console for JavaScript errors
2. Verify PHP session is active
3. Test SVN commands manually in container
4. Check repository permissions and connectivity
5. Verify CSRF token generation and validation

## Future Enhancements

### Potential Improvements
1. **Modern encryption** when upgrading to PHP 7+
2. **Persistent credential storage** with proper encryption
3. **Multiple credential profiles** for different repositories
4. **SSH key authentication** support
5. **Integration with system credential stores**

### Security Enhancements
1. **Password strength validation**
2. **Credential rotation policies**
3. **Audit logging** for authentication events
4. **Two-factor authentication** support
5. **OAuth integration** for modern SVN servers
