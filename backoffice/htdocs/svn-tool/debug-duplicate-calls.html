<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Duplicate API Calls</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log-entry {
            padding: 5px;
            margin: 2px 0;
            border-left: 3px solid #007bff;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .duplicate-entry {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        .warning-entry {
            border-left-color: #ffc107;
            background: #fff3cd;
            color: #856404;
        }
        .success-entry {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        #debug-log {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            padding: 10px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🐛 Debug Duplicate API Calls</h1>
        <p>This page helps identify and debug duplicate API call issues in the SVN tool.</p>
        
        <div class="debug-section">
            <h3>📊 Initialization Monitor</h3>
            <div id="init-log"></div>
        </div>
        
        <div class="debug-section">
            <h3>📡 API Call Monitor</h3>
            <div id="debug-log"></div>
            <button onclick="clearDebugLog()">Clear Log</button>
            <button onclick="showApiCallStats()">Show Stats</button>
        </div>
        
        <div class="debug-section">
            <h3>🧪 Test Controls</h3>
            <p>Click these buttons to test for duplicate API calls:</p>
            
            <!-- Hidden elements needed for SVN Tool -->
            <input type="hidden" id="repo_base_path" value="/var/www/sites">
            <input type="hidden" id="csrf_token" value="test-token">
            <div id="repo-list" style="display: none;"></div>
            <div id="file-list" style="display: none;"></div>
            <div id="notification-container" style="display: none;"></div>
            
            <button onclick="testRefreshRepos()">Test Refresh Repositories</button>
            <button onclick="testSelectRepo()">Test Select Repository</button>
            <button onclick="testCheckPermissions()">Test Check Permissions</button>
            <button onclick="testFixPermissions()">Test Fix Permissions</button>
            <button onclick="testCheckConnectivity()">Test Check Connectivity</button>
            <button onclick="testCleanup()">Test Cleanup</button>
        </div>
        
        <div class="debug-section">
            <h3>📈 Statistics</h3>
            <div id="stats-display"></div>
        </div>
    </div>

    <!-- Load SVN Tool JavaScript -->
    <script src="assets/js/svn-tool.js"></script>
    
    <script>
        // Debug logging
        let debugLog = [];
        let initializationCount = 0;
        let apiCallStats = {};
        
        function addDebugEntry(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const entry = `[${timestamp}] ${message}`;
            debugLog.push({entry, type, timestamp});
            
            const logDiv = document.getElementById('debug-log');
            const entryDiv = document.createElement('div');
            entryDiv.className = `log-entry ${type}-entry`;
            entryDiv.textContent = entry;
            logDiv.appendChild(entryDiv);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function addInitEntry(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const entry = `[${timestamp}] ${message}`;
            
            const logDiv = document.getElementById('init-log');
            const entryDiv = document.createElement('div');
            entryDiv.className = `log-entry ${type}-entry`;
            entryDiv.textContent = entry;
            logDiv.appendChild(entryDiv);
        }
        
        // Override console methods to capture initialization
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;
        
        console.log = function(...args) {
            const message = args.join(' ');
            if (message.includes('Initializing SVN Tool') || message.includes('SVN Tool')) {
                initializationCount++;
                addInitEntry(`INIT #${initializationCount}: ${message}`, initializationCount > 1 ? 'duplicate' : 'success');
            }
            if (message.includes('Binding SVN Tool events')) {
                addInitEntry(`EVENT BINDING: ${message}`, 'info');
            }
            if (message.includes('Making API request') || message.includes('DUPLICATE API CALL')) {
                const type = message.includes('DUPLICATE') ? 'duplicate' : 'info';
                addDebugEntry(message, type);
            }
            originalConsoleLog.apply(console, args);
        };
        
        console.warn = function(...args) {
            const message = args.join(' ');
            if (message.includes('already initialized') || message.includes('DUPLICATE')) {
                addInitEntry(`WARNING: ${message}`, 'warning');
            }
            addDebugEntry(`WARNING: ${message}`, 'warning');
            originalConsoleWarn.apply(console, args);
        };
        
        // Test functions
        function testRefreshRepos() {
            addDebugEntry('🧪 Testing refresh repositories...', 'info');
            if (typeof SvnTool !== 'undefined' && SvnTool.loadRepositories) {
                SvnTool.loadRepositories();
            } else {
                addDebugEntry('❌ SvnTool.loadRepositories not available', 'duplicate');
            }
        }
        
        function testSelectRepo() {
            addDebugEntry('🧪 Testing select repository...', 'info');
            if (typeof SvnTool !== 'undefined' && SvnTool.selectRepository) {
                SvnTool.state.currentRepo = '/var/www/sites/front';
                SvnTool.selectRepository('/var/www/sites/front');
            } else {
                addDebugEntry('❌ SvnTool.selectRepository not available', 'duplicate');
            }
        }
        
        function testCheckPermissions() {
            addDebugEntry('🧪 Testing check permissions...', 'info');
            if (typeof SvnTool !== 'undefined' && SvnTool.checkPermissions) {
                SvnTool.state.currentRepo = '/var/www/sites/front';
                SvnTool.checkPermissions();
            } else {
                addDebugEntry('❌ SvnTool.checkPermissions not available', 'duplicate');
            }
        }
        
        function testFixPermissions() {
            addDebugEntry('🧪 Testing fix permissions...', 'info');
            if (typeof SvnTool !== 'undefined' && SvnTool.fixPermissions) {
                SvnTool.state.currentRepo = '/var/www/sites/front';
                SvnTool.fixPermissions();
            } else {
                addDebugEntry('❌ SvnTool.fixPermissions not available', 'duplicate');
            }
        }
        
        function testCheckConnectivity() {
            addDebugEntry('🧪 Testing check connectivity...', 'info');
            if (typeof SvnTool !== 'undefined' && SvnTool.checkConnectivity) {
                SvnTool.state.currentRepo = '/var/www/sites/front';
                SvnTool.checkConnectivity();
            } else {
                addDebugEntry('❌ SvnTool.checkConnectivity not available', 'duplicate');
            }
        }
        
        function testCleanup() {
            addDebugEntry('🧪 Testing cleanup...', 'info');
            if (typeof SvnTool !== 'undefined' && SvnTool.cleanupRepository) {
                SvnTool.state.currentRepo = '/var/www/sites/front';
                SvnTool.cleanupRepository();
            } else {
                addDebugEntry('❌ SvnTool.cleanupRepository not available', 'duplicate');
            }
        }
        
        function clearDebugLog() {
            document.getElementById('debug-log').innerHTML = '';
            document.getElementById('init-log').innerHTML = '';
            debugLog = [];
            initializationCount = 0;
            apiCallStats = {};
            if (typeof SvnTool !== 'undefined' && SvnTool.state) {
                SvnTool.state.apiCallCount = {};
            }
        }
        
        function showApiCallStats() {
            const statsDiv = document.getElementById('stats-display');
            let statsHtml = '<h4>API Call Statistics:</h4>';
            
            if (typeof SvnTool !== 'undefined' && SvnTool.state && SvnTool.state.apiCallCount) {
                const stats = SvnTool.state.apiCallCount;
                for (const [endpoint, count] of Object.entries(stats)) {
                    const status = count > 1 ? '❌ DUPLICATE' : '✅ OK';
                    statsHtml += `<div>${endpoint}: ${count} calls ${status}</div>`;
                }
            } else {
                statsHtml += '<div>No API call statistics available</div>';
            }
            
            statsHtml += `<div><strong>Total initializations: ${initializationCount}</strong></div>`;
            statsDiv.innerHTML = statsHtml;
        }
        
        // Monitor page load
        document.addEventListener('DOMContentLoaded', function() {
            addInitEntry('🌐 DOM Content Loaded', 'success');
            
            // Check what objects are available
            setTimeout(function() {
                if (typeof SvnTool !== 'undefined') {
                    addInitEntry('✅ SvnTool object is available', 'success');
                } else {
                    addInitEntry('❌ SvnTool object is NOT available', 'duplicate');
                }
                
                if (typeof SvnApp !== 'undefined') {
                    addInitEntry('⚠️ SvnApp object is available (should be disabled)', 'warning');
                } else {
                    addInitEntry('✅ SvnApp object is NOT available (correctly disabled)', 'success');
                }
                
                showApiCallStats();
            }, 1000);
        });
    </script>
</body>
</html>
