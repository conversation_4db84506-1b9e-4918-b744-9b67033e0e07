<?php
/**
 * SVN Tool API Functions
 * 
 * All handler and utility functions for the API
 */

if (!function_exists('handleTest')) {
    function handleTest() {
        echo json_encode(array(
            'status' => 'success',
            'message' => 'API is working',
            'timestamp' => date('Y-m-d H:i:s'),
            'php_version' => PHP_VERSION
        ));
    }
}

if (!function_exists('handleListRepositories')) {
    function handleListRepositories() {
        try {
            // First check for user-provided path from POST/GET
            $basePath = '';
            
            // Check POST data first (from form submission)
            if (isset($_POST['repo_base_path']) && !empty($_POST['repo_base_path'])) {
                $basePath = trim($_POST['repo_base_path']);
            }
            // Check GET parameter
            elseif (isset($_GET['repo_base_path']) && !empty($_GET['repo_base_path'])) {
                $basePath = trim($_GET['repo_base_path']);
            }
            // Check session data
            elseif (isset($_SESSION['repo_base_path']) && !empty($_SESSION['repo_base_path'])) {
                $basePath = $_SESSION['repo_base_path'];
            }
            // Fall back to config file
            else {
                $basePath = config('repository.base_path', '');
            }
            
            if (empty($basePath)) {
                throw new Exception('Repository base path not configured');
            }

            // Normalize path
            $basePath = rtrim($basePath, '/');

            if (!is_dir($basePath)) {
                // Provide helpful suggestions for Docker container environment
                $suggestions = array();
                $commonPaths = array(
                    '/var/www/sites/front',
                    '/var/www/sites',
                    '/var/www/riashop',
                    '/var/www/engine',
                    '/var/www'
                );

                foreach ($commonPaths as $path) {
                    if (is_dir($path) && is_readable($path)) {
                        $suggestions[] = $path;
                    }
                }

                $message = 'Repository base path does not exist: ' . $basePath;
                if (!empty($suggestions)) {
                    $message .= '. Suggested paths: ' . implode(', ', $suggestions);
                }

                throw new Exception($message);
            }

            if (!is_readable($basePath)) {
                throw new Exception('Repository base path is not readable: ' . $basePath);
            }

            // Simple repository scanning
            $repositories = scanForRepositories($basePath);
            
            echo json_encode(array(
                'status' => 'success',
                'data' => $repositories,
                'count' => count($repositories),
                'base_path' => $basePath
            ));
            
        } catch (Exception $e) {
            throw new Exception('Failed to list repositories: ' . $e->getMessage());
        }
    }
}

if (!function_exists('handleGetStatus')) {
    function handleGetStatus() {
        // PHP 5.6 compatible way to get path parameter
        $path = '';
        if (isset($_GET['path'])) {
            $path = $_GET['path'];
        } elseif (isset($_POST['path'])) {
            $path = $_POST['path'];
        }
        
        if (empty($path)) {
            throw new Exception('Repository path is required');
        }
        
        if (!is_dir($path)) {
            throw new Exception('Repository path does not exist: ' . $path);
        }
        
        try {
            // Simple SVN status check
            $files = getSvnStatus($path);
            
            echo json_encode(array(
                'status' => 'success',
                'data' => $files,
                'repository_path' => $path
            ));
            
        } catch (Exception $e) {
            throw new Exception('Failed to get repository status: ' . $e->getMessage());
        }
    }
}

if (!function_exists('handleCommit')) {
    function handleCommit() {
        // Get JSON input
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON data');
        }
        
        $path = isset($data['path']) ? $data['path'] : '';
        $files = isset($data['files']) ? $data['files'] : array();
        $message = isset($data['message']) ? $data['message'] : '';
        
        if (empty($path) || empty($files) || empty($message)) {
            throw new Exception('Missing required parameters: path, files, and message');
        }
        
        if (!is_dir($path)) {
            throw new Exception('Repository path does not exist: ' . $path);
        }
        
        try {
            $result = commitFiles($path, $files, $message);
            
            echo json_encode(array(
                'status' => 'success',
                'message' => 'Files committed successfully',
                'data' => $result
            ));
            
        } catch (Exception $e) {
            throw new Exception('Failed to commit files: ' . $e->getMessage());
        }
    }
}

if (!function_exists('handleCleanup')) {
    function handleCleanup() {
        // Get JSON input
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON data');
        }
        
        $path = isset($data['path']) ? $data['path'] : '';
        
        if (empty($path)) {
            throw new Exception('Repository path is required');
        }
        
        if (!is_dir($path)) {
            throw new Exception('Repository path does not exist: ' . $path);
        }
        
        try {
            $result = cleanupRepository($path);
            
            echo json_encode(array(
                'status' => 'success',
                'message' => 'Repository cleaned up successfully',
                'data' => $result
            ));
            
        } catch (Exception $e) {
            throw new Exception('Failed to cleanup repository: ' . $e->getMessage());
        }
    }
}

if (!function_exists('handleCheckPermissions')) {
    function handleCheckPermissions() {
        // Get JSON input
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON data');
        }
        
        $path = isset($data['path']) ? $data['path'] : '';
        
        if (empty($path)) {
            throw new Exception('Repository path is required');
        }
        
        if (!is_dir($path)) {
            throw new Exception('Repository path does not exist: ' . $path);
        }
        
        try {
            $result = checkRepositoryPermissions($path);
            
            echo json_encode(array(
                'status' => 'success',
                'message' => 'Permissions checked successfully',
                'data' => $result
            ));
            
        } catch (Exception $e) {
            throw new Exception('Failed to check permissions: ' . $e->getMessage());
        }
    }
}

if (!function_exists('handleGetDiff')) {
    function handleGetDiff() {
        $path = isset($_GET['path']) ? $_GET['path'] : '';
        $file = isset($_GET['file']) ? $_GET['file'] : '';
        
        if (empty($path) || empty($file)) {
            throw new Exception('Repository path and file are required');
        }
        
        if (!is_dir($path)) {
            throw new Exception('Repository path does not exist: ' . $path);
        }
        
        try {
            $diff = getFileDiff($path, $file);
            
            echo json_encode(array(
                'status' => 'success',
                'data' => $diff,
                'file' => $file
            ));
            
        } catch (Exception $e) {
            throw new Exception('Failed to get diff: ' . $e->getMessage());
        }
    }
}

if (!function_exists('scanForRepositories')) {
    function scanForRepositories($basePath, $maxDepth = 2) {
        $repositories = array();
        $currentDepth = 0;
        
        try {
            scanDirectory($basePath, $repositories, $currentDepth, $maxDepth);
            
            // Sort by name
            usort($repositories, function($a, $b) {
                return strcmp($a['name'], $b['name']);
            });
            
        } catch (Exception $e) {
            error_log('Repository scan error: ' . $e->getMessage());
        }
        
        return $repositories;
    }
}

if (!function_exists('scanDirectory')) {
    function scanDirectory($path, &$repositories, $currentDepth, $maxDepth) {
        if ($currentDepth > $maxDepth) {
            return;
        }
        
        $items = scandir($path);
        if ($items === false) {
            return;
        }
        
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') {
                continue;
            }
            
            $itemPath = $path . '/' . $item;
            
            if (!is_dir($itemPath)) {
                continue;
            }
            
            // Check if it's an SVN working copy
            if (is_dir($itemPath . '/.svn')) {
                $repositories[] = array(
                    'name' => $item,
                    'path' => $itemPath,
                    'type' => 'working_copy',
                    'last_modified' => date('Y-m-d H:i:s', filemtime($itemPath))
                );
                continue; // Don't scan inside working copies
            }
            
            // Check if it's an SVN repository
            if (is_dir($itemPath . '/conf') && is_dir($itemPath . '/db') && is_file($itemPath . '/format')) {
                $repositories[] = array(
                    'name' => $item,
                    'path' => $itemPath,
                    'type' => 'repository',
                    'last_modified' => date('Y-m-d H:i:s', filemtime($itemPath))
                );
                continue; // Don't scan inside repositories
            }
            
            // Recursively scan subdirectories
            scanDirectory($itemPath, $repositories, $currentDepth + 1, $maxDepth);
        }
    }
}

if (!function_exists('getSvnStatus')) {
    function getSvnStatus($path) {
        $files = array();
        
        // Check if SVN is available
        $svnPath = trim(shell_exec('which svn 2>/dev/null'));
        if (empty($svnPath)) {
            throw new Exception('SVN command not found on system');
        }
        
        // Change to repository directory
        $oldCwd = getcwd();
        if (!chdir($path)) {
            throw new Exception('Cannot change to repository directory');
        }
        
        try {
            // Build SVN status command with credentials if available
            $statusCommand = 'svn status';
            $credentials = getSvnCredentials();
            if ($credentials && isset($credentials['username']) && isset($credentials['password'])) {
                $statusCommand .= " --username " . escapeshellarg($credentials['username']);
                $statusCommand .= " --password " . escapeshellarg($credentials['password']);
                $statusCommand .= " --trust-server-cert";
            }
            $statusCommand .= ' 2>&1';

            // Run SVN status command
            $output = array();
            $returnCode = 0;
            exec($statusCommand, $output, $returnCode);

            if ($returnCode !== 0) {
                $outputStr = implode(' ', $output);
                // Check for authentication errors
                if (strpos($outputStr, 'Authentication') !== false ||
                    strpos($outputStr, 'E170013') !== false ||
                    strpos($outputStr, 'E215004') !== false) {
                    throw new Exception('SVN status failed: Authentication required. Please configure SVN credentials.');
                }
                throw new Exception('SVN status failed: ' . $outputStr);
            }
            
            // Parse SVN status output
            foreach ($output as $line) {
                $line = trim($line);
                if (empty($line)) continue;
                
                $status = substr($line, 0, 1);
                $filePath = trim(substr($line, 8));
                
                if (!empty($filePath)) {
                    $files[] = array(
                        'name' => basename($filePath),
                        'path' => $filePath,
                        'status' => getSvnStatusName($status),
                        'full_path' => $path . '/' . $filePath
                    );
                }
            }
            
        } finally {
            chdir($oldCwd);
        }
        
        return $files;
    }
}

if (!function_exists('getSvnStatusName')) {
    function getSvnStatusName($statusCode) {
        switch ($statusCode) {
            case 'M': return 'modified';
            case 'A': return 'added';
            case 'D': return 'deleted';
            case 'R': return 'replaced';
            case 'C': return 'conflicted';
            case 'X': return 'external';
            case 'I': return 'ignored';
            case '?': return 'unversioned';
            case '!': return 'missing';
            case '~': return 'obstructed';
            default: return 'unknown';
        }
    }
}

if (!function_exists('handleServerScan')) {
    function handleServerScan() {
        // Get JSON input or fallback to GET parameters for testing
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        // Fallback to GET parameters if JSON parsing fails
        if (!$data) {
            $data = array(
                'path' => isset($_GET['path']) ? $_GET['path'] : '/var/www',
                'depth' => isset($_GET['depth']) ? intval($_GET['depth']) : 3
            );
        }

        if (empty($data['path'])) {
            throw new Exception('Invalid scan path');
        }

        $scanPath = isset($data['path']) ? trim($data['path']) : '/var/www';
        $maxDepth = isset($data['depth']) ? intval($data['depth']) : 3;

        // Validate scan path
        if (empty($scanPath) || !is_dir($scanPath)) {
            throw new Exception('Invalid scan path: ' . $scanPath);
        }

        // Security check - prevent scanning sensitive directories
        $restrictedPaths = array('/etc', '/usr', '/bin', '/sbin', '/boot', '/root');
        foreach ($restrictedPaths as $restricted) {
            if (strpos($scanPath, $restricted) === 0) {
                throw new Exception('Access denied to restricted path: ' . $scanPath);
            }
        }

        try {
            // Include ServerScanner model
            if (file_exists('models/ServerScanner.php')) {
                require_once 'models/ServerScanner.php';

                // Initialize scanner
                $scanner = new ServerScanner();
                $scanner->setMaxDepth($maxDepth);

                // Perform scan
                $repositories = $scanner->scanForSvnRepositories($scanPath);

                echo json_encode(array(
                    'status' => 'success',
                    'data' => $repositories,
                    'meta' => array(
                        'scan_path' => $scanPath,
                        'max_depth' => $maxDepth,
                        'total_found' => count($repositories),
                        'scan_time' => date('Y-m-d H:i:s')
                    )
                ));
            } else {
                // Fallback to simple scanning
                $repositories = scanForRepositories($scanPath, $maxDepth);

                echo json_encode(array(
                    'status' => 'success',
                    'data' => $repositories,
                    'meta' => array(
                        'scan_path' => $scanPath,
                        'max_depth' => $maxDepth,
                        'total_found' => count($repositories),
                        'scan_time' => date('Y-m-d H:i:s')
                    )
                ));
            }

        } catch (Exception $e) {
            throw new Exception('Failed to scan for repositories: ' . $e->getMessage());
        }
    }
}

if (!function_exists('handleFixPermissions')) {
    function handleFixPermissions() {
        // Get JSON input
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if (!$data) {
            throw new Exception('Invalid JSON data');
        }

        $path = isset($data['path']) ? $data['path'] : '';

        if (empty($path)) {
            throw new Exception('Repository path is required');
        }

        if (!is_dir($path)) {
            throw new Exception('Repository path does not exist: ' . $path);
        }

        try {
            $result = fixSvnPermissions($path);

            echo json_encode(array(
                'status' => 'success',
                'message' => 'SVN permissions fixed successfully',
                'data' => $result
            ));

        } catch (Exception $e) {
            throw new Exception('Failed to fix permissions: ' . $e->getMessage());
        }
    }
}

if (!function_exists('handleCheckConnectivity')) {
    function handleCheckConnectivity() {
        // Get JSON input
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if (!$data) {
            throw new Exception('Invalid JSON data');
        }

        $path = isset($data['path']) ? $data['path'] : '';

        if (empty($path)) {
            throw new Exception('Repository path is required');
        }

        if (!is_dir($path)) {
            throw new Exception('Repository path does not exist: ' . $path);
        }

        try {
            $result = checkSvnConnectivity($path);

            echo json_encode(array(
                'status' => 'success',
                'message' => 'SVN connectivity check completed',
                'data' => $result
            ));

        } catch (Exception $e) {
            throw new Exception('Failed to check SVN connectivity: ' . $e->getMessage());
        }
    }
}

if (!function_exists('handleStoreCredentials')) {
    function handleStoreCredentials() {
        // Get JSON input
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if (!$data) {
            throw new Exception('Invalid JSON data');
        }

        $username = isset($data['username']) ? trim($data['username']) : '';
        $password = isset($data['password']) ? $data['password'] : '';
        $remember = isset($data['remember']) ? (bool)$data['remember'] : false;

        if (empty($username) || empty($password)) {
            throw new Exception('Username and password are required');
        }

        try {
            $result = storeSvnCredentials($username, $password, $remember);

            echo json_encode(array(
                'status' => 'success',
                'message' => 'Credentials stored successfully',
                'data' => $result
            ));

        } catch (Exception $e) {
            throw new Exception('Failed to store credentials: ' . $e->getMessage());
        }
    }
}

if (!function_exists('handleCheckCredentials')) {
    function handleCheckCredentials() {
        try {
            $hasCredentials = hasSvnCredentials();

            echo json_encode(array(
                'status' => 'success',
                'has_credentials' => $hasCredentials,
                'message' => $hasCredentials ? 'Credentials are stored' : 'No credentials stored'
            ));

        } catch (Exception $e) {
            throw new Exception('Failed to check credentials: ' . $e->getMessage());
        }
    }
}

if (!function_exists('handleClearCredentials')) {
    function handleClearCredentials() {
        try {
            $result = clearSvnCredentials();

            echo json_encode(array(
                'status' => 'success',
                'message' => 'Credentials cleared successfully',
                'data' => $result
            ));

        } catch (Exception $e) {
            throw new Exception('Failed to clear credentials: ' . $e->getMessage());
        }
    }
}
?>
