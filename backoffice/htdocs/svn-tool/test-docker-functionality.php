<?php
/**
 * Test script to verify SVN tool functionality in Docker environment
 */

// Initialize session and constants
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

define('SVN_TOOL_ROOT', __DIR__);
define('SVN_TOOL_ACCESS', true);

// Include required files
require_once 'lib/helpers.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'api-utilities.php';
require_once 'api-functions.php';

echo "=== Docker Environment SVN Tool Test ===\n\n";

// Test repository path
$testRepo = '/var/www/sites/front';

echo "Testing with repository: {$testRepo}\n\n";

// Test 1: Repository existence and SVN status
echo "1. Testing repository access...\n";

if (!is_dir($testRepo)) {
    echo "   ✗ Repository directory not found: {$testRepo}\n";
    echo "   This test requires running inside the Docker container.\n";
    exit(1);
}

echo "   ✓ Repository directory exists\n";

if (!is_dir($testRepo . '/.svn')) {
    echo "   ✗ Not an SVN working copy\n";
    exit(1);
}

echo "   ✓ SVN working copy detected\n";

// Test 2: Permission checking
echo "\n2. Testing permission checking...\n";

try {
    $permissions = checkRepositoryPermissions($testRepo);
    echo "   ✓ Permission check completed\n";
    echo "   - Repository readable: " . ($permissions['readable'] ? 'Yes' : 'No') . "\n";
    echo "   - Repository writable: " . ($permissions['writable'] ? 'Yes' : 'No') . "\n";
    
    if (isset($permissions['svn_directory'])) {
        echo "   - .svn directory readable: " . ($permissions['svn_directory']['readable'] ? 'Yes' : 'No') . "\n";
        echo "   - .svn directory writable: " . ($permissions['svn_directory']['writable'] ? 'Yes' : 'No') . "\n";
        echo "   - .svn permissions: " . $permissions['svn_directory']['perms'] . "\n";
    }
} catch (Exception $e) {
    echo "   ✗ Permission check failed: " . $e->getMessage() . "\n";
}

// Test 3: SVN connectivity check
echo "\n3. Testing SVN connectivity...\n";

try {
    $connectivity = checkSvnConnectivity($testRepo);
    echo "   ✓ Connectivity check completed\n";
    echo "   - Local status: " . $connectivity['local_status'] . "\n";
    echo "   - Remote connectivity: " . $connectivity['remote_connectivity'] . "\n";
    echo "   - Authentication status: " . $connectivity['authentication_status'] . "\n";
    
    if (!empty($connectivity['repository_info']['url'])) {
        echo "   - Repository URL: " . $connectivity['repository_info']['url'] . "\n";
    }
    
    if (!empty($connectivity['recommendations'])) {
        echo "   Recommendations:\n";
        foreach ($connectivity['recommendations'] as $rec) {
            echo "     • " . $rec . "\n";
        }
    }
} catch (Exception $e) {
    echo "   ✗ Connectivity check failed: " . $e->getMessage() . "\n";
}

// Test 4: Permission fixing
echo "\n4. Testing permission fixing...\n";

try {
    $fixResult = fixSvnPermissions($testRepo);
    echo "   ✓ Permission fix completed\n";
    echo "   - Message: " . $fixResult['message'] . "\n";
    
    if (isset($fixResult['commands_executed'])) {
        echo "   Commands executed:\n";
        foreach ($fixResult['commands_executed'] as $cmd) {
            echo "     • Return code: " . $cmd['return_code'] . "\n";
        }
    }
} catch (Exception $e) {
    echo "   ✗ Permission fix failed: " . $e->getMessage() . "\n";
}

// Test 5: SVN cleanup
echo "\n5. Testing SVN cleanup...\n";

try {
    $cleanupResult = cleanupRepository($testRepo);
    echo "   ✓ SVN cleanup completed\n";
    echo "   - Message: " . $cleanupResult['message'] . "\n";
    
    if (!empty($cleanupResult['output'])) {
        echo "   Output:\n";
        foreach ($cleanupResult['output'] as $line) {
            echo "     " . $line . "\n";
        }
    }
} catch (Exception $e) {
    echo "   ✗ SVN cleanup failed: " . $e->getMessage() . "\n";
}

// Test 6: SVN status check
echo "\n6. Testing SVN status...\n";

try {
    $files = getSvnStatus($testRepo);
    echo "   ✓ SVN status check completed\n";
    echo "   - Modified files found: " . count($files) . "\n";
    
    if (count($files) > 0) {
        echo "   Modified files:\n";
        foreach (array_slice($files, 0, 5) as $file) { // Show first 5 files
            echo "     • " . $file['name'] . " (" . $file['status'] . ")\n";
        }
        if (count($files) > 5) {
            echo "     ... and " . (count($files) - 5) . " more files\n";
        }
    }
} catch (Exception $e) {
    echo "   ✗ SVN status check failed: " . $e->getMessage() . "\n";
}

// Test 7: API endpoint simulation
echo "\n7. Testing API endpoint simulation...\n";

// Simulate fix_permissions API call
echo "   Testing fix_permissions API...\n";
try {
    // Mock the php://input stream
    $jsonData = json_encode(array('path' => $testRepo));

    // Create a temporary file to simulate php://input
    $tempFile = tempnam(sys_get_temp_dir(), 'svn_test');
    file_put_contents($tempFile, $jsonData);

    // Override file_get_contents for php://input
    $originalInput = 'php://input';

    // Directly test the function logic
    $result = fixSvnPermissions($testRepo);
    if ($result && $result['success']) {
        echo "     ✓ fix_permissions function works\n";
    } else {
        echo "     ✗ fix_permissions function failed\n";
    }

    unlink($tempFile);
} catch (Exception $e) {
    echo "     ✗ fix_permissions test error: " . $e->getMessage() . "\n";
}

// Simulate check_connectivity API call
echo "   Testing check_connectivity API...\n";
try {
    // Directly test the function logic
    $result = checkSvnConnectivity($testRepo);
    if ($result && isset($result['local_status'])) {
        echo "     ✓ check_connectivity function works\n";
        echo "     - Status: " . $result['local_status'] . "/" . $result['remote_connectivity'] . "/" . $result['authentication_status'] . "\n";
    } else {
        echo "     ✗ check_connectivity function failed\n";
    }
} catch (Exception $e) {
    echo "     ✗ check_connectivity test error: " . $e->getMessage() . "\n";
}

echo "\n=== Docker Test Complete ===\n";
echo "This test verifies that all SVN tool functionality works in the Docker environment.\n";
echo "If you see any ✗ symbols, those indicate issues that may need attention.\n";
echo "\nTo test the web interface:\n";
echo "1. Open the SVN tool in your browser\n";
echo "2. Configure the repository path to: {$testRepo}\n";
echo "3. Test the new buttons: 'Corriger permissions' and 'Test connectivité'\n";
echo "4. Verify that API calls only happen once (no duplicate notifications)\n";
echo "5. Try committing files to test the enhanced error handling\n";
?>
