<?php
/**
 * Test script for repository detection
 */

// Define access constant
define('SVN_TOOL_ACCESS', true);

// Include required files
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'api-functions.php';
require_once 'api-utilities.php';

echo "Testing SVN Repository Detection\n";
echo "================================\n\n";

// Test paths
$testPaths = array(
    '/home/<USER>/RIASHOP/LOCALHOST/test_vibe/sites',
    '/home/<USER>/RIASHOP/LOCALHOST/test_vibe',
    '/var/www/sites/front'
);

foreach ($testPaths as $testPath) {
    echo "Testing path: $testPath\n";
    echo "Path exists: " . (is_dir($testPath) ? 'YES' : 'NO') . "\n";
    echo "Path readable: " . (is_readable($testPath) ? 'YES' : 'NO') . "\n";
    
    if (is_dir($testPath) && is_readable($testPath)) {
        try {
            $repositories = scanForRepositories($testPath);
            echo "Repositories found: " . count($repositories) . "\n";
            
            if (count($repositories) > 0) {
                echo "Repository details:\n";
                foreach ($repositories as $repo) {
                    echo "  - Name: {$repo['name']}\n";
                    echo "    Path: {$repo['path']}\n";
                    echo "    Type: {$repo['type']}\n";
                    echo "    Modified: {$repo['last_modified']}\n\n";
                }
            }
        } catch (Exception $e) {
            echo "Error scanning: " . $e->getMessage() . "\n";
        }
    }
    
    echo "---\n\n";
}

// Test the scanDirectory function directly
echo "Direct directory scan test:\n";
echo "===========================\n";

$testPath = '/home/<USER>/RIASHOP/LOCALHOST/test_vibe/sites';
if (is_dir($testPath)) {
    $repositories = array();
    try {
        scanDirectory($testPath, $repositories, 0, 2);
        echo "Found " . count($repositories) . " repositories:\n";
        foreach ($repositories as $repo) {
            echo "  - {$repo['name']} ({$repo['type']}) at {$repo['path']}\n";
        }
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
    }
}
?>
