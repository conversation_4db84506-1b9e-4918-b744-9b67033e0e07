<?php
/**
 * SVN Tool Functionality Test
 * 
 * Tests all major functionality to ensure the refactoring worked
 */

// Start session
session_start();

echo "=== SVN Tool Functionality Test ===\n\n";

// Test 1: Configuration loading
echo "1. Testing configuration loading...\n";
try {
    require_once 'config/config.php';
    echo "   ✓ Configuration loaded successfully\n";
    echo "   ✓ Repository base path: " . config('repository.base_path') . "\n";
} catch (Exception $e) {
    echo "   ✗ Configuration failed: " . $e->getMessage() . "\n";
}

// Test 2: Helper functions
echo "\n2. Testing helper functions...\n";
try {
    require_once 'lib/helpers.php';
    require_once 'includes/functions.php';
    echo "   ✓ Helper functions loaded\n";
    
    // Test CSRF token generation
    $token = generateCsrfToken();
    echo "   ✓ CSRF token generated: " . substr($token, 0, 10) . "...\n";
    
    // Test config function
    $appName = config('app.name');
    echo "   ✓ Config function works: " . $appName . "\n";
    
} catch (Exception $e) {
    echo "   ✗ Helper functions failed: " . $e->getMessage() . "\n";
}

// Test 3: API endpoints
echo "\n3. Testing API endpoints...\n";

// Test basic API
echo "   Testing basic API...\n";
$_GET['action'] = 'test';
ob_start();
try {
    include 'api.php';
    $output = ob_get_clean();
    $data = json_decode($output, true);
    if ($data && $data['status'] === 'success') {
        echo "   ✓ Basic API test passed\n";
    } else {
        echo "   ✗ Basic API test failed\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "   ✗ Basic API error: " . $e->getMessage() . "\n";
}

// Test repository listing
echo "   Testing repository listing API...\n";
$_GET['action'] = 'list_repos';
$_GET['repo_base_path'] = '/var/www/html';
ob_start();
try {
    include 'api.php';
    $output = ob_get_clean();
    $data = json_decode($output, true);
    if ($data && isset($data['status'])) {
        echo "   ✓ Repository listing API responded\n";
        echo "   ✓ Status: " . $data['status'] . "\n";
        if (isset($data['count'])) {
            echo "   ✓ Found " . $data['count'] . " repositories\n";
        }
    } else {
        echo "   ✗ Repository listing API failed\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "   ✗ Repository listing error: " . $e->getMessage() . "\n";
}

// Test 4: Models
echo "\n4. Testing models...\n";
try {
    require_once 'models/SvnRepository.php';
    $svnRepo = new SvnRepository();
    echo "   ✓ SvnRepository model loaded\n";
    
    // Test repository finding
    $repos = $svnRepo->findRepositories('/var/www/html');
    echo "   ✓ Repository scanning works, found " . count($repos) . " repositories\n";
    
} catch (Exception $e) {
    echo "   ✗ Models failed: " . $e->getMessage() . "\n";
}

// Test 5: Controllers
echo "\n5. Testing controllers...\n";
try {
    require_once 'controllers/BaseController.php';
    $controller = new BaseController();
    echo "   ✓ BaseController loaded\n";
    
} catch (Exception $e) {
    echo "   ✗ Controllers failed: " . $e->getMessage() . "\n";
}

// Test 6: File syntax
echo "\n6. Testing file syntax...\n";
$files = array(
    'index.php',
    'api.php',
    'config/config.php',
    'lib/helpers.php',
    'includes/functions.php',
    'controllers/BaseController.php',
    'models/SvnRepository.php'
);

foreach ($files as $file) {
    if (file_exists($file)) {
        $output = shell_exec("php -l $file 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "   ✓ $file syntax OK\n";
        } else {
            echo "   ✗ $file syntax error: $output\n";
        }
    } else {
        echo "   ✗ $file not found\n";
    }
}

// Test 7: SVN command availability
echo "\n7. Testing SVN availability...\n";
$svnPath = trim(shell_exec('which svn 2>/dev/null'));
if (!empty($svnPath)) {
    echo "   ✓ SVN command found at: $svnPath\n";
    
    // Test SVN version
    $version = trim(shell_exec('svn --version --quiet 2>/dev/null'));
    if (!empty($version)) {
        echo "   ✓ SVN version: $version\n";
    }
} else {
    echo "   ✗ SVN command not found\n";
}

// Test 8: Directory permissions
echo "\n8. Testing directory permissions...\n";
$testDirs = array(
    '.' => 'SVN Tool root',
    'assets' => 'Assets directory',
    'assets/css' => 'CSS directory',
    'assets/js' => 'JS directory',
    'config' => 'Config directory'
);

foreach ($testDirs as $dir => $description) {
    if (is_dir($dir)) {
        $readable = is_readable($dir) ? '✓' : '✗';
        $writable = is_writable($dir) ? '✓' : '✗';
        echo "   $description: Read $readable Write $writable\n";
    } else {
        echo "   ✗ $description not found\n";
    }
}

echo "\n=== Test Complete ===\n";
echo "If all tests show ✓, the SVN Tool refactoring was successful!\n";
?>
