<?php
/**
 * Final test to verify duplicate API call issue is completely resolved
 */

echo "=== Final Duplicate API Call Fix Test ===\n\n";

// Test 1: Check all files for JavaScript loading
echo "1. Comprehensive JavaScript loading check...\n";

$allFiles = array();
$iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator('.'));
foreach ($iterator as $file) {
    if ($file->isFile() && (
        $file->getExtension() === 'php' || 
        $file->getExtension() === 'html' ||
        $file->getExtension() === 'htm'
    )) {
        $allFiles[] = $file->getPathname();
    }
}

$totalAppJsLoads = 0;
$totalSvnToolJsLoads = 0;
$filesWithJsLoads = array();

foreach ($allFiles as $file) {
    $content = file_get_contents($file);
    $appJsCount = substr_count($content, 'app.js');
    $svnToolJsCount = substr_count($content, 'svn-tool.js');
    
    if ($appJsCount > 0 || $svnToolJsCount > 0) {
        $filesWithJsLoads[] = array(
            'file' => $file,
            'app_js' => $appJsCount,
            'svn_tool_js' => $svnToolJsCount
        );
        $totalAppJsLoads += $appJsCount;
        $totalSvnToolJsLoads += $svnToolJsCount;
    }
}

echo "   Files loading JavaScript:\n";
foreach ($filesWithJsLoads as $fileInfo) {
    echo "   - {$fileInfo['file']}: app.js({$fileInfo['app_js']}) svn-tool.js({$fileInfo['svn_tool_js']})\n";
}

echo "\n   Total JavaScript loads:\n";
echo "   - app.js: {$totalAppJsLoads} times\n";
echo "   - svn-tool.js: {$totalSvnToolJsLoads} times\n";

if ($totalAppJsLoads === 0 && $totalSvnToolJsLoads === 1) {
    echo "   ✅ PERFECT: Only svn-tool.js loads once\n";
} elseif ($totalAppJsLoads > 0) {
    echo "   ❌ PROBLEM: app.js still loads {$totalAppJsLoads} times\n";
} elseif ($totalSvnToolJsLoads > 1) {
    echo "   ❌ PROBLEM: svn-tool.js loads {$totalSvnToolJsLoads} times\n";
} else {
    echo "   ⚠️  WARNING: No JavaScript files loading\n";
}

// Test 2: Check JavaScript initialization patterns
echo "\n2. JavaScript initialization patterns...\n";

if (file_exists('assets/js/app.js')) {
    $appJsContent = file_get_contents('assets/js/app.js');
    
    if (strpos($appJsContent, 'SVN_TOOL_APP_JS_DISABLED') !== false) {
        echo "   ✅ app.js is properly disabled\n";
    } else {
        echo "   ❌ app.js is not disabled\n";
    }
    
    $appInitCount = substr_count($appJsContent, '.init()');
    echo "   - app.js init() calls: {$appInitCount}\n";
}

if (file_exists('assets/js/svn-tool.js')) {
    $svnToolContent = file_get_contents('assets/js/svn-tool.js');
    
    if (strpos($svnToolContent, 'initialized') !== false) {
        echo "   ✅ svn-tool.js has initialization guard\n";
    } else {
        echo "   ❌ svn-tool.js missing initialization guard\n";
    }
    
    $svnInitCount = substr_count($svnToolContent, '.init()');
    $domReadyCount = substr_count($svnToolContent, 'DOMContentLoaded');
    echo "   - svn-tool.js init() calls: {$svnInitCount}\n";
    echo "   - svn-tool.js DOM ready listeners: {$domReadyCount}\n";
}

// Test 3: Check for inline initialization
echo "\n3. Inline initialization check...\n";

$inlineInitCount = 0;
foreach ($allFiles as $file) {
    $content = file_get_contents($file);
    $matches = array();
    
    // Look for inline SvnTool.init() or SvnApp.init() calls
    if (preg_match_all('/SvnTool\.init\(\)|SvnApp\.init\(\)/', $content, $matches)) {
        $count = count($matches[0]);
        $inlineInitCount += $count;
        echo "   - {$file}: {$count} inline init calls\n";
    }
}

if ($inlineInitCount === 0) {
    echo "   ✅ No inline initialization calls found\n";
} else {
    echo "   ❌ Found {$inlineInitCount} inline initialization calls\n";
}

// Test 4: Check event binding patterns
echo "\n4. Event binding patterns...\n";

$eventBindingIssues = array();

if (file_exists('assets/js/svn-tool.js')) {
    $svnToolContent = file_get_contents('assets/js/svn-tool.js');
    
    // Check for addEventListener calls
    $addEventListenerCount = substr_count($svnToolContent, 'addEventListener');
    echo "   - svn-tool.js addEventListener calls: {$addEventListenerCount}\n";
    
    // Check for bindEvents method
    if (strpos($svnToolContent, 'bindEvents:') !== false) {
        echo "   ✅ svn-tool.js has bindEvents method\n";
    } else {
        echo "   ❌ svn-tool.js missing bindEvents method\n";
    }
}

// Test 5: Summary and recommendations
echo "\n=== SUMMARY ===\n";

$duplicateIssueFixed = ($totalAppJsLoads === 0 && $totalSvnToolJsLoads === 1 && $inlineInitCount === 0);

if ($duplicateIssueFixed) {
    echo "🎉 DUPLICATE API CALL ISSUE COMPLETELY FIXED!\n\n";
    echo "✅ Only svn-tool.js loads once\n";
    echo "✅ app.js is disabled\n";
    echo "✅ No duplicate initialization\n";
    echo "✅ No inline init calls\n";
} else {
    echo "⚠️  DUPLICATE API CALL ISSUE STILL EXISTS\n\n";
    echo "Issues found:\n";
    
    if ($totalAppJsLoads > 0) {
        echo "❌ app.js still loads {$totalAppJsLoads} times\n";
    }
    if ($totalSvnToolJsLoads > 1) {
        echo "❌ svn-tool.js loads {$totalSvnToolJsLoads} times\n";
    }
    if ($inlineInitCount > 0) {
        echo "❌ {$inlineInitCount} inline initialization calls found\n";
    }
    
    echo "\nRecommendations:\n";
    if ($totalAppJsLoads > 0) {
        echo "• Remove all app.js script tags\n";
        echo "• Ensure app.js is properly disabled\n";
    }
    if ($totalSvnToolJsLoads > 1) {
        echo "• Remove duplicate svn-tool.js script tags\n";
    }
    if ($inlineInitCount > 0) {
        echo "• Remove inline SvnTool.init() calls\n";
    }
}

echo "\n=== Next Steps ===\n";
echo "1. Test the web interface by opening it in a browser\n";
echo "2. Open browser developer tools (F12)\n";
echo "3. Go to Console tab\n";
echo "4. Click any button (Refresh, Commit, etc.)\n";
echo "5. Verify you see only ONE API request in Network tab\n";
echo "6. Verify you see only ONE notification message\n";
echo "\nIf you still see duplicate calls, check the browser console for errors.\n";
?>
