<?php
/**
 * Test file diff functionality by simulating the JavaScript workflow
 */

// Define access constant
define('SVN_TOOL_ACCESS', true);

echo "Testing File Diff Functionality\n";
echo "===============================\n\n";

// Test 1: Get repository status to find modified files
echo "1. Getting Repository Status:\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/api.php?action=get_status&path=/var/www/sites/front');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$modifiedFiles = array();

if ($httpCode == 200) {
    $data = json_decode($response, true);
    if ($data && $data['status'] === 'success') {
        echo "   ✓ Repository status loaded: " . count($data['data']) . " files\n";
        
        foreach ($data['data'] as $file) {
            echo "   - {$file['name']} ({$file['status']})\n";
            if ($file['status'] === 'modified') {
                $modifiedFiles[] = $file;
            }
        }
        
        echo "   ✓ Found " . count($modifiedFiles) . " modified files for diff testing\n";
    } else {
        echo "   ✗ Failed to get repository status\n";
        exit(1);
    }
} else {
    echo "   ✗ HTTP Error: $httpCode\n";
    exit(1);
}

echo "\n";

// Test 2: Test diff API for each modified file
echo "2. Testing File Diff API:\n";

foreach ($modifiedFiles as $file) {
    echo "   Testing diff for: {$file['name']}\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/api.php?action=get_diff&path=/var/www/sites/front&file=' . urlencode($file['path']));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode == 200) {
        $diffData = json_decode($response, true);
        if ($diffData && $diffData['status'] === 'success') {
            echo "     ✓ Diff loaded successfully\n";
            echo "     ✓ File: {$diffData['data']['file']}\n";
            echo "     ✓ Has changes: " . ($diffData['data']['has_changes'] ? 'YES' : 'NO') . "\n";
            
            if ($diffData['data']['has_changes'] && isset($diffData['data']['diff'])) {
                $diffLines = explode("\n", $diffData['data']['diff']);
                echo "     ✓ Diff lines: " . count($diffLines) . "\n";
                
                // Analyze diff content
                $addedLines = 0;
                $removedLines = 0;
                $contextLines = 0;
                
                foreach ($diffLines as $line) {
                    if (strpos($line, '+') === 0 && strpos($line, '+++') !== 0) {
                        $addedLines++;
                    } elseif (strpos($line, '-') === 0 && strpos($line, '---') !== 0) {
                        $removedLines++;
                    } elseif (strpos($line, ' ') === 0) {
                        $contextLines++;
                    }
                }
                
                echo "     ✓ Added lines: $addedLines\n";
                echo "     ✓ Removed lines: $removedLines\n";
                echo "     ✓ Context lines: $contextLines\n";
                
                // Show first few lines of diff
                echo "     ✓ First 3 diff lines:\n";
                for ($i = 0; $i < min(3, count($diffLines)); $i++) {
                    echo "       " . substr($diffLines[$i], 0, 60) . "\n";
                }
                
            } else {
                echo "     ⚠ No diff content available\n";
            }
        } else {
            echo "     ✗ Diff API failed: " . (isset($diffData['message']) ? $diffData['message'] : 'Unknown error') . "\n";
        }
    } else {
        echo "     ✗ HTTP Error: $httpCode\n";
    }
    
    echo "\n";
}

// Test 3: Verify web interface has diff viewer elements
echo "3. Testing Diff Viewer Interface Elements:\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    $requiredElements = array(
        'diff-viewer' => 'Diff viewer container',
        'diff-filename' => 'Diff filename display',
        'diff-stats' => 'Diff statistics',
        'diff-content' => 'Diff content area',
        'diff-placeholder' => 'Diff placeholder text',
        'copy-diff-btn' => 'Copy diff button'
    );
    
    foreach ($requiredElements as $elementId => $description) {
        if (strpos($response, 'id="' . $elementId . '"') !== false) {
            echo "   ✓ $description found\n";
        } else {
            echo "   ✗ $description missing\n";
        }
    }
    
    // Check for diff button in file list
    if (strpos($response, 'btn-diff') !== false) {
        echo "   ✓ File diff buttons found\n";
    } else {
        echo "   ✗ File diff buttons missing\n";
    }
    
    // Check for showFileDiff function call
    if (strpos($response, 'SvnTool.showFileDiff') !== false) {
        echo "   ✓ showFileDiff function call found\n";
    } else {
        echo "   ✗ showFileDiff function call missing\n";
    }
    
} else {
    echo "   ✗ Web interface not accessible: HTTP $httpCode\n";
}

echo "\n===============================\n";
echo "File Diff Functionality Test Complete\n";

// Summary
echo "\nSUMMARY:\n";
echo "- Repository Status: " . (count($modifiedFiles) > 0 ? "✓ " . count($modifiedFiles) . " MODIFIED FILES FOUND" : "✗ NO MODIFIED FILES") . "\n";
echo "- Diff API: " . (isset($diffData) && $diffData['status'] === 'success' ? "✓ WORKING" : "✗ FAILED") . "\n";
echo "- Diff Content: " . (isset($diffLines) && count($diffLines) > 0 ? "✓ " . count($diffLines) . " LINES" : "✗ NO CONTENT") . "\n";
echo "- Web Interface: " . (strpos($response, 'diff-viewer') !== false ? "✓ ELEMENTS PRESENT" : "✗ ELEMENTS MISSING") . "\n";

if (count($modifiedFiles) > 0) {
    echo "\n🎯 EXPECTED BEHAVIOR:\n";
    echo "When user clicks 'Voir diff' button on a modified file:\n";
    echo "1. API request should be made to: /svn-tool/api.php?action=get_diff&path=/var/www/sites/front&file=FILE_PATH\n";
    echo "2. Diff viewer should become visible and show:\n";
    echo "   - Filename: " . $modifiedFiles[0]['name'] . "\n";
    echo "   - Status: File modified\n";
    echo "   - Diff content with syntax highlighting\n";
    echo "3. Copy diff button should become visible\n";
    echo "4. User should be able to copy diff to clipboard\n";
}
?>
