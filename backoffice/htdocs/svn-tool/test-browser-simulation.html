<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVN Tool Browser Simulation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 8px 15px; }
        #test-results { margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 5px; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); }
        .modal-dialog { position: relative; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 5px; width: 500px; }
    </style>
</head>
<body>
    <h1>SVN Tool Browser Simulation Test</h1>
    
    <!-- Simulate the key elements from the main interface -->
    <div class="test-section">
        <h3>Simulated SVN Tool Elements</h3>
        
        <!-- Server Scanner Button -->
        <button type="button" class="button button-secondary" onclick="SvnTool.ServerScanner.openModal()">
            Scanner le serveur
        </button>
        
        <!-- Repository list -->
        <div id="repo-list">
            <div class="list-group-item" onclick="SvnTool.selectRepository('/var/www/sites/front')" data-repo-path="/var/www/sites/front">
                front - /var/www/sites/front
            </div>
        </div>
        
        <!-- File list -->
        <div id="file-list">
            <div class="file-item" data-file-path="htdocs/include/actions.site.inc.php">
                <div class="file-header">
                    <input type="checkbox" class="file-checkbox">
                    <strong>actions.site.inc.php</strong>
                    <span class="badge status-modified">modified</span>
                </div>
                <div class="file-details">
                    <small>htdocs/include/actions.site.inc.php</small>
                    <button class="btn-diff" onclick="SvnTool.showFileDiff('htdocs/include/actions.site.inc.php'); event.stopPropagation();">Voir diff</button>
                </div>
            </div>
        </div>
        
        <!-- Diff viewer -->
        <div id="diff-viewer" style="display: none;">
            <div id="diff-header">
                <div id="diff-filename"></div>
                <div id="diff-stats"></div>
            </div>
            <div id="diff-content"></div>
        </div>
        <div id="diff-placeholder">
            <p>Cliquez sur un fichier pour voir ses différences</p>
        </div>
        
        <!-- Copy diff button -->
        <button id="copy-diff-btn" style="display: none;">Copier le diff</button>
    </div>
    
    <!-- Server Scanner Modal -->
    <div id="server-scanner-modal" class="modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Scanner de dépôts SVN</h4>
                    <button type="button" class="close" onclick="SvnTool.ServerScanner.closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="scanner-controls">
                        <div class="form-group">
                            <label for="scan-path">Chemin à scanner</label>
                            <input type="text" id="scan-path" class="form-control" value="/var/www" placeholder="/var/www">
                        </div>
                        <div class="form-group">
                            <label for="scan-depth">Profondeur maximale</label>
                            <select id="scan-depth" class="form-control">
                                <option value="3">3 niveaux (Recommandé)</option>
                                <option value="5">5 niveaux</option>
                            </select>
                        </div>
                        <button id="start-scan-btn" class="button button-primary">
                            Démarrer le scan
                        </button>
                    </div>
                    
                    <div id="scan-progress" style="display: none;">
                        <div id="scan-status">Scanning...</div>
                    </div>
                    
                    <div id="scan-results" style="display: none;">
                        <h5>Dépôts SVN trouvés <span id="found-count" class="badge badge-success">0</span></h5>
                        <div id="found-repos"></div>
                    </div>
                </div>
                <div id="modal-footer" class="modal-footer" style="display: none;">
                    <button id="cancel-scan-btn" class="button button-secondary">Annuler</button>
                    <button id="add-selected-repos" class="button button-success">Ajouter les dépôts sélectionnés</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Notification Container -->
    <div id="notification-container"></div>
    
    <div class="test-section">
        <h3>Manual Tests</h3>
        <button onclick="testServerScannerButton()">Test Server Scanner Button</button>
        <button onclick="testFileDiffButton()">Test File Diff Button</button>
        <button onclick="testInitialization()">Test Initialization</button>
        <div id="manual-test-results"></div>
    </div>
    
    <div id="test-results">
        <h3>Test Results:</h3>
        <div id="results-content">Loading SVN Tool...</div>
    </div>

    <!-- Load the actual SVN Tool JavaScript -->
    <script src="assets/js/svn-tool.js"></script>
    <script>
        // Test functions
        function logResult(message, type) {
            var resultsContent = document.getElementById('results-content');
            var className = type === 'success' ? 'success' : (type === 'error' ? 'error' : 'info');
            resultsContent.innerHTML += '<div class="' + className + '">[' + new Date().toLocaleTimeString() + '] ' + message + '</div>';
        }
        
        function testServerScannerButton() {
            logResult('Testing server scanner button click...', 'info');
            try {
                var button = document.getElementById('start-scan-btn');
                if (button) {
                    logResult('✓ Start scan button found', 'success');
                    // Simulate click
                    button.click();
                    logResult('✓ Button click simulated', 'success');
                } else {
                    logResult('✗ Start scan button not found', 'error');
                }
            } catch (error) {
                logResult('✗ Error testing server scanner: ' + error.message, 'error');
            }
        }
        
        function testFileDiffButton() {
            logResult('Testing file diff button click...', 'info');
            try {
                var button = document.querySelector('.btn-diff');
                if (button) {
                    logResult('✓ Diff button found', 'success');
                    // Simulate click
                    button.click();
                    logResult('✓ Diff button click simulated', 'success');
                } else {
                    logResult('✗ Diff button not found', 'error');
                }
            } catch (error) {
                logResult('✗ Error testing file diff: ' + error.message, 'error');
            }
        }
        
        function testInitialization() {
            logResult('Testing SvnTool initialization...', 'info');
            try {
                if (typeof SvnTool !== 'undefined') {
                    logResult('✓ SvnTool object exists', 'success');
                    
                    if (typeof SvnTool.init === 'function') {
                        logResult('✓ SvnTool.init function exists', 'success');
                    } else {
                        logResult('✗ SvnTool.init function missing', 'error');
                    }
                    
                    if (typeof SvnTool.ServerScanner === 'object') {
                        logResult('✓ SvnTool.ServerScanner exists', 'success');
                        
                        if (typeof SvnTool.ServerScanner.openModal === 'function') {
                            logResult('✓ ServerScanner.openModal function exists', 'success');
                        } else {
                            logResult('✗ ServerScanner.openModal function missing', 'error');
                        }
                    } else {
                        logResult('✗ SvnTool.ServerScanner missing', 'error');
                    }
                    
                    if (typeof SvnTool.showFileDiff === 'function') {
                        logResult('✓ SvnTool.showFileDiff function exists', 'success');
                    } else {
                        logResult('✗ SvnTool.showFileDiff function missing', 'error');
                    }
                    
                } else {
                    logResult('✗ SvnTool object not found', 'error');
                }
            } catch (error) {
                logResult('✗ Error testing initialization: ' + error.message, 'error');
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            logResult('DOM loaded, initializing SVN Tool...', 'info');
            
            try {
                if (typeof SvnTool !== 'undefined') {
                    SvnTool.init();
                    logResult('✓ SvnTool initialized successfully', 'success');
                } else {
                    logResult('✗ SvnTool not available for initialization', 'error');
                }
            } catch (error) {
                logResult('✗ Error during initialization: ' + error.message, 'error');
            }
            
            // Run automatic tests
            setTimeout(function() {
                testInitialization();
            }, 1000);
        });
    </script>
</body>
</html>
