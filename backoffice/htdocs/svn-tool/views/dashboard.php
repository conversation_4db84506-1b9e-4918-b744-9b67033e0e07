<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo config('app.name'); ?> - Professional SVN Management</title>
    <link rel="stylesheet" href="assets/css/modern.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚡</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title"><?php echo config('app.name'); ?></h1>
                <div class="header-actions">
                    <span class="version-badge">v<?php echo config('app.version'); ?></span>
                    <button id="server-scan-btn" class="btn btn-primary">
                        <span>🔍</span>
                        <span>Server Scan</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Configuration Section -->
            <div class="card mb-6">
                <div class="card-header">
                    <h2 class="card-title">
                        <span>📁</span>
                        Repository Configuration
                    </h2>
                </div>
                <div class="card-body">
                    <form method="post" action="<?php echo url(); ?>" id="config-form">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <div class="form-group">
                            <label for="repo_base_path" class="form-label">Repository Base Path</label>
                            <input type="text" 
                                   id="repo_base_path" 
                                   name="repo_base_path" 
                                   class="form-input" 
                                   value="<?php echo sanitizeOutput($repoBasePath ? $repoBasePath : ''); ?>" 
                                   placeholder="/var/www/repositories"
                                   required>
                            <div class="form-help">Enter the absolute path to your SVN repositories directory</div>
                        </div>
                        <button type="submit" class="btn btn-primary btn-lg">
                            <span>💾</span>
                            <span>Save Configuration</span>
                        </button>
                    </form>
                </div>
            </div>

            <?php if (!$repoBasePath): ?>
                <div class="alert alert-warning">
                    <div class="alert-icon">⚠️</div>
                    <div>
                        <strong>Configuration Required</strong><br>
                        Please set your repository base path above to begin using the SVN client.
                    </div>
                </div>
            <?php else: ?>
                <!-- SVN Client Interface -->
                <div id="svn-interface">
                    <div class="grid grid-cols-3">
                        <!-- Repositories Panel -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <span>📂</span>
                                    Repositories
                                </h3>
                                <div class="card-actions">
                                    <button id="refresh-repos" class="btn btn-secondary btn-sm">
                                        <span>🔄</span>
                                        <span>Refresh</span>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body" style="padding: 0;">
                                <div class="list-container">
                                    <div class="list-header">Available Repositories</div>
                                    <div id="repo-list" class="list-content">
                                        <div class="list-empty">
                                            <div class="list-empty-icon">📁</div>
                                            <div>Loading repositories...</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Files Panel -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <span>📄</span>
                                    File Status
                                </h3>
                                <div class="card-actions">
                                    <button id="check-permissions" class="btn btn-secondary btn-sm">
                                        <span>🔐</span>
                                        <span>Permissions</span>
                                    </button>
                                    <button id="cleanup-btn" class="btn btn-warning btn-sm">
                                        <span>🧹</span>
                                        <span>Cleanup</span>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body" style="padding: 0;">
                                <div class="list-container">
                                    <div class="list-header">Modified Files</div>
                                    <div id="file-list" class="list-content">
                                        <div class="list-empty">
                                            <div class="list-empty-icon">📋</div>
                                            <div>Select a repository to view files</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions Panel -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <span>⚡</span>
                                    Actions
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="commit-message" class="form-label">Commit Message</label>
                                    <textarea id="commit-message" 
                                             class="form-textarea" 
                                             placeholder="Enter your commit message..."
                                             required></textarea>
                                </div>
                                <div style="display: flex; flex-direction: column; gap: var(--space-3);">
                                    <button id="commit-btn" class="btn btn-success" disabled>
                                        <span>✅</span>
                                        <span>Commit Selected</span>
                                    </button>
                                    <div style="display: flex; gap: var(--space-2);">
                                        <button id="select-all-btn" class="btn btn-secondary btn-sm" style="flex: 1;">
                                            <span>☑️</span>
                                            <span>Select All</span>
                                        </button>
                                        <button id="deselect-all-btn" class="btn btn-secondary btn-sm" style="flex: 1;">
                                            <span>⬜</span>
                                            <span>Deselect All</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Diff Viewer -->
                    <div class="card mt-6">
                        <div class="card-header">
                            <h3 class="card-title">
                                <span>📊</span>
                                Diff Viewer
                            </h3>
                            <div class="card-actions">
                                <button id="copy-diff-btn" class="btn btn-secondary btn-sm" style="display: none;">
                                    <span>📋</span>
                                    <span>Copy Diff</span>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="diff-viewer" class="hidden">
                                <div class="diff-header">
                                    <div class="diff-filename" id="diff-filename"></div>
                                    <div class="diff-stats" id="diff-stats"></div>
                                </div>
                                <div class="diff-content" id="diff-content"></div>
                            </div>
                            <div id="diff-placeholder" class="list-empty">
                                <div class="list-empty-icon">📄</div>
                                <div style="font-size: 1.125rem; color: var(--gray-600); margin-bottom: var(--space-2);">
                                    Click on a file to view its diff
                                </div>
                                <div class="text-sm" style="color: var(--gray-500);">
                                    Changes will be highlighted with syntax coloring
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </main>
    </div>

    <!-- Server Scanner Modal -->
    <div id="server-scanner-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <span>🔍</span>
                    Server-wide SVN Scanner
                </h3>
                <button class="modal-close" aria-label="Close">&times;</button>
            </div>
            <div class="modal-body">
                <div id="scanner-controls">
                    <div class="form-group">
                        <label for="scan-path" class="form-label">Scan Path</label>
                        <input type="text" 
                               id="scan-path" 
                               class="form-input" 
                               value="/var/www" 
                               placeholder="/var/www">
                        <div class="form-help">Enter the root path to scan for SVN repositories</div>
                    </div>
                    <div class="form-group">
                        <label for="scan-depth" class="form-label">Maximum Depth</label>
                        <select id="scan-depth" class="form-select">
                            <option value="3">3 levels (Recommended)</option>
                            <option value="5">5 levels</option>
                            <option value="7">7 levels</option>
                            <option value="10">10 levels (Slow)</option>
                        </select>
                        <div class="form-help">Deeper scans take longer but find more repositories</div>
                    </div>
                    <button id="start-scan-btn" class="btn btn-primary btn-lg" style="width: 100%;">
                        <span>🚀</span>
                        <span>Start Scan</span>
                    </button>
                </div>
                
                <div id="scan-progress" class="hidden" style="margin-top: var(--space-6);">
                    <div class="form-group">
                        <label class="form-label">Scan Progress</label>
                        <div class="progress-bar">
                            <div id="progress-fill" class="progress-fill progress-indeterminate" style="width: 100%;"></div>
                        </div>
                        <div id="scan-status" class="form-help" style="margin-top: var(--space-2);">
                            Initializing scan...
                        </div>
                    </div>
                </div>

                <div id="scan-results" class="hidden" style="margin-top: var(--space-6);">
                    <h4 style="margin-bottom: var(--space-4); display: flex; align-items: center; gap: var(--space-2);">
                        <span>📋</span>
                        <span>Found SVN Repositories</span>
                        <span id="found-count" class="status-badge" style="background: var(--success-50); color: var(--success-600);">0</span>
                    </h4>
                    <div class="list-container" style="max-height: 300px;">
                        <div class="list-header">
                            <label style="display: flex; align-items: center; gap: var(--space-2); margin: 0;">
                                <input type="checkbox" id="select-all-found">
                                <span>Select All Found Repositories</span>
                            </label>
                        </div>
                        <div id="found-repos" class="list-content"></div>
                    </div>
                </div>
            </div>
            <div id="modal-footer" class="modal-footer hidden">
                <button id="cancel-scan-btn" class="btn btn-secondary">Cancel</button>
                <button id="add-selected-repos" class="btn btn-success">
                    <span>✅</span>
                    <span>Add Selected Repositories</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notification-container" class="notification-container"></div>

    <!-- JavaScript is loaded in index.php to avoid duplicates -->
</body>
</html>