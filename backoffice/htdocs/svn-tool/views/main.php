<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVN Web Tool</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="header">
        <h1>SVN Web Tool</h1>
        <button id="refresh-btn" class="btn btn-primary">🔄 Refresh</button>
    </div>

    <div class="main-content">
        <!-- Loading indicator -->
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            <p>Loading repositories...</p>
        </div>

        <!-- Error message -->
        <div id="error-message" class="error-message" style="display: none;">
            <div class="error-content">
                <h3>Error</h3>
                <p id="error-text"></p>
                <button id="retry-btn" class="btn btn-secondary">Try Again</button>
            </div>
        </div>

        <!-- Repositories list -->
        <div id="repositories-container" class="repositories-container" style="display: none;">
            <div class="repositories-header">
                <h2>Repositories</h2>
                <div class="repositories-stats">
                    <span id="repo-count">0 repositories</span>
                </div>
            </div>
            <div id="repositories-grid" class="repositories-grid">
                <!-- Repository cards will be inserted here -->
            </div>
        </div>

        <!-- Repository details -->
        <div id="repository-details" class="repository-details" style="display: none;">
            <div class="details-header">
                <button id="back-btn" class="btn btn-secondary">← Back</button>
                <h2 id="repo-name">Repository</h2>
            </div>
            
            <div class="files-container">
                <div class="files-header">
                    <h3>Modified Files</h3>
                    <div class="files-stats">
                        <span id="files-count">0 files</span>
                    </div>
                </div>
                <div id="files-list" class="files-list">
                    <!-- File items will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript is loaded in index.php to avoid duplicates -->
</body>
</html>