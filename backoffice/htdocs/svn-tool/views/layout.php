<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= sanitizeOutput($title) ?></title>
    <link rel="stylesheet" href="<?= assetVersioned('css/app.css') ?>">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <h1><?= config('app.name') ?></h1>
            <p>Professional SVN repository management with modern UI/UX</p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container">
        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-error">
                <?= sanitizeOutput($_SESSION['error']) ?>
                <?php unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <?= sanitizeOutput($_SESSION['success']) ?>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>
        
        <?= $content ?>
    </main>

    <!-- Notification Container -->
    <div id="notification-container" class="notification-container"></div>

    <!-- JavaScript is loaded in index.php to avoid duplicates -->
</body>
</html>