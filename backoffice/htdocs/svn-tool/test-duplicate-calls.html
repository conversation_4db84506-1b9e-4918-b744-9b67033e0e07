<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Duplicate API Calls Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        #api-call-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .call-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-left: 3px solid #007bff;
        }
        .duplicate-call {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test Duplicate API Calls Fix</h1>
        <p>This page tests whether the duplicate API call issue has been resolved.</p>
        
        <div class="test-section">
            <h3>API Call Monitor</h3>
            <p>This monitor tracks all API calls made by the SVN tool. Each call should appear only once.</p>
            <div id="api-call-log"></div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="test-section">
            <h3>Test Controls</h3>
            <p>Use these buttons to test various API calls and verify they only execute once:</p>
            
            <input type="hidden" id="repo_base_path" value="/var/www/sites">
            <div id="repo-list" style="display: none;">
                <div class="list-group-item" onclick="SvnTool.selectRepository('/var/www/sites/front')" data-repo-path="/var/www/sites/front">
                    Test Repository
                </div>
            </div>
            
            <button onclick="testLoadRepositories()">Test Load Repositories</button>
            <button onclick="testSelectRepository()">Test Select Repository</button>
            <button onclick="testCheckPermissions()">Test Check Permissions</button>
            <button onclick="testFixPermissions()">Test Fix Permissions</button>
            <button onclick="testCheckConnectivity()">Test Check Connectivity</button>
            <button onclick="testCleanup()">Test Cleanup</button>
        </div>
        
        <div class="test-section">
            <h3>Test Results</h3>
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>Notification Test</h3>
            <p>Check if notifications appear only once:</p>
            <div id="notification-container"></div>
        </div>
    </div>

    <!-- Load the SVN Tool JavaScript -->
    <script src="assets/js/svn-tool.js"></script>
    
    <script>
        // API call monitoring
        let apiCallCount = {};
        let originalApiRequest = SvnTool.apiRequest;
        
        // Override the apiRequest method to monitor calls
        SvnTool.apiRequest = function(endpoint, options, callback) {
            const callKey = endpoint + (options.method || 'GET');
            
            if (!apiCallCount[callKey]) {
                apiCallCount[callKey] = 0;
            }
            apiCallCount[callKey]++;
            
            const logEntry = document.createElement('div');
            logEntry.className = 'call-entry' + (apiCallCount[callKey] > 1 ? ' duplicate-call' : '');
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${callKey} (Call #${apiCallCount[callKey]})`;
            
            const log = document.getElementById('api-call-log');
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
            
            // Call the original method
            return originalApiRequest.call(this, endpoint, options, callback);
        };
        
        // Test functions
        function testLoadRepositories() {
            logTest('Testing loadRepositories...');
            SvnTool.loadRepositories();
        }
        
        function testSelectRepository() {
            logTest('Testing selectRepository...');
            SvnTool.state.currentRepo = '/var/www/sites/front';
            SvnTool.selectRepository('/var/www/sites/front');
        }
        
        function testCheckPermissions() {
            logTest('Testing checkPermissions...');
            SvnTool.state.currentRepo = '/var/www/sites/front';
            SvnTool.checkPermissions();
        }
        
        function testFixPermissions() {
            logTest('Testing fixPermissions...');
            SvnTool.state.currentRepo = '/var/www/sites/front';
            SvnTool.fixPermissions();
        }
        
        function testCheckConnectivity() {
            logTest('Testing checkConnectivity...');
            SvnTool.state.currentRepo = '/var/www/sites/front';
            SvnTool.checkConnectivity();
        }
        
        function testCleanup() {
            logTest('Testing cleanupRepository...');
            SvnTool.state.currentRepo = '/var/www/sites/front';
            SvnTool.cleanupRepository();
        }
        
        function logTest(message) {
            const results = document.getElementById('test-results');
            const entry = document.createElement('div');
            entry.className = 'test-result info';
            entry.textContent = message;
            results.appendChild(entry);
        }
        
        function clearLog() {
            document.getElementById('api-call-log').innerHTML = '';
            document.getElementById('test-results').innerHTML = '';
            apiCallCount = {};
        }
        
        // Initialize and log
        document.addEventListener('DOMContentLoaded', function() {
            logTest('Page loaded and SvnTool initialized');
            
            // Check for duplicate initialization
            setTimeout(function() {
                const duplicates = Object.values(apiCallCount).filter(count => count > 1);
                if (duplicates.length > 0) {
                    logTest('⚠️ DUPLICATE API CALLS DETECTED!');
                } else {
                    logTest('✅ No duplicate API calls detected so far');
                }
            }, 2000);
        });
    </script>
</body>
</html>
