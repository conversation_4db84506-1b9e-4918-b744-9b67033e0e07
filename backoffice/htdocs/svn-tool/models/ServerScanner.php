<?php
/**
 * Enhanced Server-wide SVN Scanner Model
 */

class ServerScanner
{
    private $maxDepth = 5;
    private $excludePaths = [
        '/proc', '/sys', '/dev', '/tmp', '/var/tmp', '/var/cache',
        '/var/log', '/var/run', '/var/lock', '/run', '/boot',
        '/usr/src', '/usr/include', '/usr/share/doc'
    ];
    private $timeLimit = 300; // 5 minutes
    private $startTime;
    private $scannedDirs = 0;
    private $foundRepos = 0;
    
    public function setMaxDepth($depth)
    {
        $this->maxDepth = max(1, min(10, intval($depth)));
    }
    
    public function scanForSvnRepositories($basePath = '/')
    {
        set_time_limit($this->timeLimit);
        ini_set('memory_limit', '256M');
        
        $repositories = array();
        $this->startTime = time();
        $this->scannedDirs = 0;
        $this->foundRepos = 0;
        
        error_log("ServerScanner: Starting comprehensive scan from: {$basePath}");
        
        if (!is_dir($basePath) || !is_readable($basePath)) {
            throw new Exception("Base path is not accessible: {$basePath}");
        }
        
        // Normalize path
        $basePath = rtrim(realpath($basePath), '/');
        
        try {
            $this->recursiveScan($basePath, $repositories, 0);
            
            // Sort by path for consistent display
            usort($repositories, function($a, $b) {
                return strcmp($a['path'], $b['path']);
            });
            
            $scanTime = time() - $this->startTime;
            error_log("ServerScanner: Scan completed in {$scanTime}s. Scanned {$this->scannedDirs} directories, found {$this->foundRepos} repositories");
            
        } catch (Exception $e) {
            error_log("ServerScanner: Scan error: " . $e->getMessage());
            throw $e;
        }
        
        return $repositories;
    }
    
    private function recursiveScan($path, &$repositories, $currentDepth)
    {
        // Check time limit
        if (time() - $this->startTime > $this->timeLimit - 30) {
            throw new Exception("Scan time limit reached after scanning {$this->scannedDirs} directories");
        }
        
        // Check depth limit
        if ($currentDepth > $this->maxDepth) {
            return;
        }
        
        // Skip excluded paths
        if ($this->isExcludedPath($path)) {
            return;
        }
        
        $this->scannedDirs++;
        
        // Log progress every 100 directories
        if ($this->scannedDirs % 100 === 0) {
            error_log("ServerScanner: Progress - scanned {$this->scannedDirs} directories, found {$this->foundRepos} repositories");
        }
        
        try {
            $items = $this->getSafeDirectoryListing($path);
            if (empty($items)) {
                return;
            }
            
            foreach ($items as $item) {
                if ($item === '.' || $item === '..' || $item[0] === '.') {
                    continue;
                }
                
                $itemPath = $path . '/' . $item;
                
                if (!is_dir($itemPath) || !is_readable($itemPath)) {
                    continue;
                }
                
                // Check if this is an SVN working copy
                if ($this->isSvnWorkingCopy($itemPath)) {
                    $repoInfo = $this->buildRepositoryInfo($itemPath, 'working_copy');
                    if ($repoInfo) {
                        $repositories[] = $repoInfo;
                        $this->foundRepos++;
                        error_log("ServerScanner: Found SVN working copy: {$itemPath}");
                    }
                    // Don't scan inside SVN working copies
                    continue;
                }
                
                // Check if this is an SVN repository
                if ($this->isSvnRepository($itemPath)) {
                    $repoInfo = $this->buildRepositoryInfo($itemPath, 'repository');
                    if ($repoInfo) {
                        $repositories[] = $repoInfo;
                        $this->foundRepos++;
                        error_log("ServerScanner: Found SVN repository: {$itemPath}");
                    }
                    // Don't scan inside SVN repositories
                    continue;
                }
                
                // Recursively scan subdirectories
                $this->recursiveScan($itemPath, $repositories, $currentDepth + 1);
            }
            
        } catch (Exception $e) {
            // Log but continue scanning
            error_log("ServerScanner: Error scanning {$path}: " . $e->getMessage());
        }
    }
    
    private function isExcludedPath($path)
    {
        foreach ($this->excludePaths as $excludePath) {
            if (strpos($path, $excludePath) === 0) {
                return true;
            }
        }
        
        // Skip hidden directories at root level
        $basename = basename($path);
        if ($basename[0] === '.' && strlen($basename) > 1) {
            return true;
        }
        
        // Skip common non-repository directories
        $skipDirs = ['node_modules', 'vendor', '.git', '.hg', 'build', 'dist', 'target'];
        if (in_array($basename, $skipDirs)) {
            return true;
        }
        
        return false;
    }
    
    private function getSafeDirectoryListing($path)
    {
        try {
            $items = scandir($path);
            return $items !== false ? $items : [];
        } catch (Exception $e) {
            error_log("ServerScanner: Failed to list directory {$path}: " . $e->getMessage());
            return [];
        }
    }
    
    private function isSvnWorkingCopy($path)
    {
        $svnDir = $path . '/.svn';
        return is_dir($svnDir) && is_readable($svnDir);
    }
    
    private function isSvnRepository($path)
    {
        // Check for SVN repository structure
        $requiredDirs = ['conf', 'db', 'hooks'];
        foreach ($requiredDirs as $dir) {
            if (!is_dir($path . '/' . $dir)) {
                return false;
            }
        }
        
        // Check for format file
        $formatFile = $path . '/format';
        if (!is_file($formatFile) || !is_readable($formatFile)) {
            return false;
        }
        
        // Verify it's actually an SVN repository by checking format
        $format = trim(file_get_contents($formatFile));
        return is_numeric($format) && intval($format) >= 1;
    }
    
    private function buildRepositoryInfo($path, $type)
    {
        try {
            $info = [
                'name' => basename($path),
                'path' => $path,
                'type' => $type,
                'size' => $this->getDirectorySize($path),
                'last_modified' => $this->getLastModified($path),
                'permissions' => $this->getPermissionsInfo($path)
            ];
            
            if ($type === 'working_copy') {
                $svnInfo = $this->getSvnInfo($path);
                $info['url'] = $svnInfo['url'];
                $info['revision'] = $svnInfo['revision'];
                $info['status'] = $this->getWorkingCopyStatus($path);
            } else {
                $info['url'] = 'file://' . $path;
                $info['revision'] = 'N/A';
                $info['status'] = 'repository';
            }
            
            return $info;
            
        } catch (Exception $e) {
            error_log("ServerScanner: Error building repo info for {$path}: " . $e->getMessage());
            return null;
        }
    }
    
    private function getSvnInfo($path)
    {
        $info = ['url' => '', 'revision' => ''];
        
        try {
            $command = 'svn info ' . escapeshellarg($path) . ' 2>/dev/null';
            $output = shell_exec($command);
            
            if ($output) {
                if (preg_match('/^URL: (.+)$/m', $output, $matches)) {
                    $info['url'] = trim($matches[1]);
                }
                if (preg_match('/^Revision: (\d+)$/m', $output, $matches)) {
                    $info['revision'] = trim($matches[1]);
                }
            }
        } catch (Exception $e) {
            error_log("ServerScanner: Error getting SVN info for {$path}: " . $e->getMessage());
        }
        
        return $info;
    }
    
    private function getWorkingCopyStatus($path)
    {
        try {
            $command = 'svn status ' . escapeshellarg($path) . ' 2>/dev/null | wc -l';
            $output = shell_exec($command);
            $modifiedFiles = intval(trim($output));
            
            if ($modifiedFiles > 0) {
                return "modified ({$modifiedFiles} files)";
            }
            return 'clean';
        } catch (Exception $e) {
            return 'unknown';
        }
    }
    
    private function getDirectorySize($path)
    {
        try {
            // Use du command for accurate size calculation
            $command = 'du -sb ' . escapeshellarg($path) . ' 2>/dev/null | cut -f1';
            $output = shell_exec($command);
            
            if ($output) {
                $bytes = intval(trim($output));
                return $this->formatBytes($bytes);
            }
        } catch (Exception $e) {
            error_log("ServerScanner: Error getting directory size for {$path}: " . $e->getMessage());
        }
        
        return 'Unknown';
    }
    
    private function getLastModified($path)
    {
        try {
            $timestamp = filemtime($path);
            return $timestamp ? date('Y-m-d H:i:s', $timestamp) : 'Unknown';
        } catch (Exception $e) {
            return 'Unknown';
        }
    }
    
    private function getPermissionsInfo($path)
    {
        try {
            $perms = fileperms($path);
            $owner = function_exists('posix_getpwuid') ? posix_getpwuid(fileowner($path)) : null;
            $group = function_exists('posix_getgrgid') ? posix_getgrgid(filegroup($path)) : null;
            
            return [
                'octal' => substr(sprintf('%o', $perms), -4),
                'owner' => $owner ? $owner['name'] : 'unknown',
                'group' => $group ? $group['name'] : 'unknown',
                'readable' => is_readable($path),
                'writable' => is_writable($path)
            ];
        } catch (Exception $e) {
            return [
                'octal' => '0000',
                'owner' => 'unknown',
                'group' => 'unknown',
                'readable' => false,
                'writable' => false
            ];
        }
    }
    
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
?>
