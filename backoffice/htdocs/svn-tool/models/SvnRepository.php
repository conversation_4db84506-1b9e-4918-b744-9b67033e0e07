<?php
/**
 * SVN Repository Model
 */

class SvnRepository
{
    public function findRepositories($basePath)
    {
        error_log("SvnRepository: Searching for repositories in: {$basePath}");
        
        $repositories = array();
        
        if (!is_dir($basePath)) {
            error_log("SvnRepository: Base path is not a directory: {$basePath}");
            return $repositories;
        }
        
        if (!is_readable($basePath)) {
            error_log("SvnRepository: Base path is not readable: {$basePath}");
            return $repositories;
        }
        
        try {
            $this->scanForRepositories($basePath, $repositories, 0, 3); // Max 3 levels deep
            
            // Sort repositories by name for consistent display
            usort($repositories, function($a, $b) {
                return strcmp($a['name'], $b['name']);
            });
            
            error_log("SvnRepository: Total repositories found: " . count($repositories));
            
        } catch (Exception $e) {
            error_log("SvnRepository: Exception while scanning: " . $e->getMessage());
        }
        
        return $repositories;
    }

    private function scanForRepositories($path, &$repositories, $currentDepth, $maxDepth)
    {
        if ($currentDepth > $maxDepth) {
            return;
        }
        
        $items = scandir($path);
        if ($items === false) {
            error_log("SvnRepository: Failed to scan directory: {$path}");
            return;
        }
        
        foreach ($items as $item) {
            if ($item === '.' || $item === '..' || $item[0] === '.') {
                continue;
            }
            
            $itemPath = rtrim($path, '/') . '/' . $item;
            
            if (!is_dir($itemPath) || !is_readable($itemPath)) {
                continue;
            }
            
            // Check if this directory is an SVN working copy
            if ($this->isSvnWorkingCopy($itemPath)) {
                $relativeName = $this->getRelativeName($itemPath, dirname($path));
                $repositories[] = array(
                    'name' => $relativeName,
                    'path' => $itemPath,
                    'type' => 'working_copy',
                    'depth' => $currentDepth
                );
                error_log("SvnRepository: Found SVN working copy: {$itemPath}");
            }
            // Check if this directory contains SVN repositories (bare repos)
            elseif ($this->containsSvnRepository($itemPath)) {
                $relativeName = $this->getRelativeName($itemPath, dirname($path));
                $repositories[] = array(
                    'name' => $relativeName,
                    'path' => $itemPath,
                    'type' => 'repository',
                    'depth' => $currentDepth
                );
                error_log("SvnRepository: Found SVN repository: {$itemPath}");
            }
            // Recursively scan subdirectories
            else {
                $this->scanForRepositories($itemPath, $repositories, $currentDepth + 1, $maxDepth);
            }
        }
    }

    private function isSvnWorkingCopy($path)
    {
        $svnDir = $path . '/.svn';
        return is_dir($svnDir) && is_readable($svnDir);
    }

    private function containsSvnRepository($path)
    {
        // Check for SVN repository structure (conf, db, hooks directories)
        $requiredDirs = ['conf', 'db', 'hooks'];
        foreach ($requiredDirs as $dir) {
            if (!is_dir($path . '/' . $dir)) {
                return false;
            }
        }
        return true;
    }

    private function getRelativeName($fullPath, $basePath)
    {
        $relativePath = str_replace($basePath . '/', '', $fullPath);
        return $relativePath ?: basename($fullPath);
    }
    
    public function getStatus($repoPath)
    {
        $command = 'svn status --xml ' . escapeshellarg($repoPath) . ' 2>&1';
        $xmlOutput = shell_exec($command);
        
        if (strpos($xmlOutput, 'svn: E') !== false) {
            $this->handleSvnError($xmlOutput);
        }
        
        $files = array();
        if (strpos($xmlOutput, '<?xml') !== false) {
            $xml = simplexml_load_string($xmlOutput);
            if ($xml && isset($xml->target->entry)) {
                foreach ($xml->target->entry as $entry) {
                    $status = (string)$entry->{'wc-status'}['item'];
                    if ($status !== 'normal' && $status !== 'unversioned') {
                        $files[] = array(
                            'path' => (string)$entry['path'],
                            'status' => $status,
                            'name' => basename((string)$entry['path'])
                        );
                    }
                }
            }
        }
        
        return $files;
    }
    
    public function cleanup($repoPath)
    {
        $command = 'svn cleanup ' . escapeshellarg($repoPath) . ' 2>&1';
        $output = shell_exec($command);
        
        if (strpos($output, 'svn: E') !== false) {
            $this->handleSvnError($output);
        }
        
        return $output;
    }
    
    public function testCommand($repoPath)
    {
        $command = 'svn info ' . escapeshellarg($repoPath) . ' 2>&1';
        $output = shell_exec($command);
        
        return array(
            'command' => $command,
            'success' => strpos($output, 'svn: E') === false,
            'output' => $output
        );
    }
    
    private function handleSvnError($output)
    {
        $analysis = $this->analyzeSvnError($output);
        throw new Exception($analysis['user_message']);
    }
    
    private function analyzeSvnError($output)
    {
        $analysis = array(
            'error_code' => '',
            'error_type' => 'unknown',
            'user_message' => 'An unexpected SVN error occurred.',
            'suggested_action' => 'Please try again or contact support.',
            'is_recoverable' => false
        );

        if (strpos($output, 'E215004') !== false || strpos($output, 'Authentication failed') !== false) {
            $analysis['error_code'] = 'E215004';
            $analysis['error_type'] = 'authentication';
            $analysis['user_message'] = 'SVN authentication failed. Your username or password may be incorrect.';
            $analysis['suggested_action'] = 'Please check your SVN credentials and try again.';
            $analysis['is_recoverable'] = true;
        } elseif (strpos($output, 'E155004') !== false || strpos($output, 'locked') !== false) {
            $analysis['error_code'] = 'E155004';
            $analysis['error_type'] = 'locked';
            $analysis['user_message'] = 'The working copy is locked from a previous operation.';
            $analysis['suggested_action'] = 'Try running SVN cleanup to resolve the lock.';
            $analysis['is_recoverable'] = true;
        } elseif (strpos($output, 'E170013') !== false) {
            $analysis['error_code'] = 'E170013';
            $analysis['error_type'] = 'connectivity';
            $analysis['user_message'] = 'Unable to connect to the SVN server.';
            $analysis['suggested_action'] = 'Check your network connection and server availability.';
            $analysis['is_recoverable'] = true;
        } elseif (strpos($output, 'Permission denied') !== false) {
            $analysis['error_type'] = 'permission';
            $analysis['user_message'] = 'Permission denied. The web server lacks necessary permissions.';
            $analysis['suggested_action'] = 'Check file permissions and ownership.';
            $analysis['is_recoverable'] = true;
        }

        return $analysis;
    }
}