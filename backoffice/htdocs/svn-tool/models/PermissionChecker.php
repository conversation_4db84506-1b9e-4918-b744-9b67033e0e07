<?php
/**
 * Permission Checker Model
 */

class PermissionChecker
{
    public function checkPermissions($repoPath)
    {
        $permissions = array(
            'repo_readable' => is_readable($repoPath),
            'repo_writable' => is_writable($repoPath),
            'svn_dir_readable' => false,
            'svn_dir_writable' => false,
            'repo_owner' => 'unknown',
            'current_user' => get_current_user(),
            'repo_permissions' => 'unknown',
            'svn_permissions' => 'unknown'
        );
        
        $svnDir = $repoPath . '/.svn';
        if (is_dir($svnDir)) {
            $permissions['svn_dir_readable'] = is_readable($svnDir);
            $permissions['svn_dir_writable'] = is_writable($svnDir);
            $permissions['svn_permissions'] = substr(sprintf('%o', fileperms($svnDir)), -4);
        }
        
        if (function_exists('posix_getpwuid') && function_exists('fileowner')) {
            $owner = posix_getpwuid(fileowner($repoPath));
            $permissions['repo_owner'] = $owner ? $owner['name'] : 'unknown';
        }
        
        $permissions['repo_permissions'] = substr(sprintf('%o', fileperms($repoPath)), -4);
        
        return $permissions;
    }
    
    public function diagnoseIssues($permissions)
    {
        $issues = array();
        
        if (!$permissions['repo_writable']) {
            $issues[] = 'Repository directory is not writable by web server';
        }
        
        if (!$permissions['svn_dir_writable']) {
            $issues[] = '.svn directory is not writable by web server';
        }
        
        if ($permissions['current_user'] !== $permissions['repo_owner']) {
            $issues[] = 'Web server user differs from repository owner';
        }
        
        return $issues;
    }
    
    public function generateFixCommands($repoPath, $permissions)
    {
        $commands = array();
        
        if ($permissions['repo_owner'] === 'unknown' || !$permissions['repo_writable']) {
            $commands[] = array(
                'description' => 'Fix repository ownership',
                'command' => 'sudo chown -R www-data:www-data ' . escapeshellarg($repoPath),
                'explanation' => 'Changes ownership of the entire repository to www-data user and group'
            );
            
            $commands[] = array(
                'description' => 'Set proper directory permissions',
                'command' => 'sudo chmod -R 775 ' . escapeshellarg($repoPath),
                'explanation' => 'Sets read/write/execute for owner and group, read/execute for others'
            );
            
            $commands[] = array(
                'description' => 'Ensure .svn directories are writable',
                'command' => 'sudo find ' . escapeshellarg($repoPath) . ' -name ".svn" -type d -exec chmod 775 {} \\;',
                'explanation' => 'Ensures all .svn directories have proper permissions'
            );
        }
        
        return $commands;
    }
}