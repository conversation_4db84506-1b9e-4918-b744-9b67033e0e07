<?php
/**
 * SVN File Model
 */

class SvnFile
{
    public function getDiff($filePath)
    {
        $command = 'svn diff ' . escapeshellarg($filePath) . ' 2>&1';
        $diffOutput = shell_exec($command);
        
        if (strpos($diffOutput, 'svn: E') !== false) {
            throw new Exception('Failed to get diff: SVN error occurred');
        }
        
        return $this->formatDiffOutput($diffOutput, $filePath);
    }
    
    public function commit($files, $message)
    {
        $fileArgs = '';
        foreach ($files as $file) {
            $fileArgs .= escapeshellarg($file) . ' ';
        }
        
        $command = 'svn commit -m ' . escapeshellarg($message) . ' ' . $fileArgs . ' --non-interactive 2>&1';
        $output = shell_exec($command);
        
        if (strpos($output, 'svn: E') !== false) {
            throw new Exception('Commit failed: ' . $this->extractSvnError($output));
        }
        
        return $output;
    }
    
    private function formatDiffOutput($diffOutput, $filePath)
    {
        if (empty($diffOutput) || strpos($diffOutput, 'svn: E') !== false) {
            return array(
                'html' => '<div class="diff-empty">No differences found or error occurred.</div>',
                'filename' => basename($filePath),
                'stats' => array('added' => 0, 'removed' => 0)
            );
        }

        $lines = explode("\n", $diffOutput);
        $html = '';
        $stats = array('added' => 0, 'removed' => 0);
        
        foreach ($lines as $line) {
            $line = sanitizeOutput($line);
            $class = '';
            
            if (strpos($line, '+++') === 0 || strpos($line, '---') === 0) {
                $class = 'diff-file-header';
            } elseif (strpos($line, '@@') === 0) {
                $class = 'diff-hunk-header';
            } elseif (strpos($line, '+') === 0) {
                $class = 'diff-added';
                $stats['added']++;
            } elseif (strpos($line, '-') === 0) {
                $class = 'diff-removed';
                $stats['removed']++;
            } else {
                $class = 'diff-context';
            }
            
            $html .= '<div class="diff-line ' . $class . '">' . $line . '</div>';
        }
        
        return array(
            'html' => $html,
            'filename' => basename($filePath),
            'stats' => $stats
        );
    }
    
    private function extractSvnError($output)
    {
        $lines = explode("\n", $output);
        foreach ($lines as $line) {
            if (strpos($line, 'svn: E') !== false) {
                return trim($line);
            }
        }
        return 'Unknown SVN error';
    }
}