<!DOCTYPE html>
<html>
<head>
    <title>SVN Tool Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>SVN Tool Debug Test</h1>
    
    <button onclick="testAPI()">Test API</button>
    <button onclick="testRepositories()">Test Repository Loading</button>
    
    <div id="results"></div>

    <script>
        async function testAPI() {
            const results = document.getElementById('results');
            results.innerHTML = '<div>Testing API...</div>';
            
            try {
                const response = await fetch('api.php?action=test');
                const data = await response.json();
                
                if (data.status === 'success') {
                    results.innerHTML += '<div class="test-result success">✅ API is working</div>';
                } else {
                    results.innerHTML += '<div class="test-result error">❌ API failed: ' + data.message + '</div>';
                }
                results.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                results.innerHTML += '<div class="test-result error">❌ API Error: ' + error.message + '</div>';
            }
        }

        async function testRepositories() {
            const results = document.getElementById('results');
            results.innerHTML = '<div>Testing repository loading...</div>';
            
            try {
                const response = await fetch('api.php?action=list_repos');
                const data = await response.json();
                
                if (data.status === 'success') {
                    results.innerHTML += '<div class="test-result success">✅ Found ' + data.count + ' repositories</div>';
                } else {
                    results.innerHTML += '<div class="test-result error">❌ Repository loading failed: ' + data.message + '</div>';
                }
                results.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                results.innerHTML += '<div class="test-result error">❌ Repository Error: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>