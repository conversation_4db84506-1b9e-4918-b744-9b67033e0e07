<?php
/**
 * Test script for API endpoints
 */

// Define access constant
define('SVN_TOOL_ACCESS', true);

echo "Testing SVN Tool API Endpoints in Container\n";
echo "===========================================\n\n";

// Test 1: Server Scanner API
echo "1. Testing Server Scanner API:\n";
echo "   GET: /svn-tool/api.php?action=server_scan&path=/var/www&depth=2\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/api.php?action=server_scan&path=/var/www&depth=2');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "   HTTP Code: $httpCode\n";
if ($httpCode == 200) {
    $data = json_decode($response, true);
    if ($data && $data['status'] === 'success') {
        echo "   ✓ SUCCESS: Found " . count($data['data']) . " repositories\n";
        foreach ($data['data'] as $repo) {
            echo "     - {$repo['name']} at {$repo['path']} ({$repo['status']})\n";
        }
    } else {
        echo "   ✗ FAILED: " . (isset($data['message']) ? $data['message'] : 'Unknown error') . "\n";
    }
} else {
    echo "   ✗ HTTP ERROR: $httpCode\n";
    echo "   Response: " . substr($response, 0, 200) . "\n";
}

echo "\n";

// Test 2: Repository Status API
echo "2. Testing Repository Status API:\n";
echo "   GET: /svn-tool/api.php?action=get_status&path=/var/www/sites/front\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/api.php?action=get_status&path=/var/www/sites/front');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "   HTTP Code: $httpCode\n";
if ($httpCode == 200) {
    $data = json_decode($response, true);
    if ($data && $data['status'] === 'success') {
        echo "   ✓ SUCCESS: Found " . count($data['data']) . " modified files\n";
        $testFile = null;
        foreach ($data['data'] as $file) {
            echo "     - {$file['name']} ({$file['status']})\n";
            if ($file['status'] === 'modified' && !$testFile) {
                $testFile = $file['path'];
            }
        }
        
        // Test 3: File Diff API
        if ($testFile) {
            echo "\n3. Testing File Diff API:\n";
            echo "   GET: /svn-tool/api.php?action=get_diff&path=/var/www/sites/front&file=$testFile\n";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/api.php?action=get_diff&path=/var/www/sites/front&file=' . urlencode($testFile));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            echo "   HTTP Code: $httpCode\n";
            if ($httpCode == 200) {
                $data = json_decode($response, true);
                if ($data && $data['status'] === 'success') {
                    echo "   ✓ SUCCESS: Diff loaded for {$data['data']['file']}\n";
                    echo "   Has changes: " . ($data['data']['has_changes'] ? 'YES' : 'NO') . "\n";
                    if ($data['data']['has_changes']) {
                        $diffLines = explode("\n", $data['data']['diff']);
                        echo "   Diff lines: " . count($diffLines) . "\n";
                        echo "   First few lines:\n";
                        for ($i = 0; $i < min(3, count($diffLines)); $i++) {
                            echo "     " . substr($diffLines[$i], 0, 80) . "\n";
                        }
                    }
                } else {
                    echo "   ✗ FAILED: " . (isset($data['message']) ? $data['message'] : 'Unknown error') . "\n";
                }
            } else {
                echo "   ✗ HTTP ERROR: $httpCode\n";
            }
        }
    } else {
        echo "   ✗ FAILED: " . (isset($data['message']) ? $data['message'] : 'Unknown error') . "\n";
    }
} else {
    echo "   ✗ HTTP ERROR: $httpCode\n";
}

echo "\n";

// Test 4: Web Interface Access
echo "4. Testing Web Interface Access:\n";
echo "   GET: /svn-tool/\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "   HTTP Code: $httpCode\n";
if ($httpCode == 200) {
    echo "   ✓ SUCCESS: Web interface accessible\n";
    if (strpos($response, 'SVN Web Tool') !== false) {
        echo "   ✓ Page title found\n";
    }
    if (strpos($response, 'start-scan-btn') !== false) {
        echo "   ✓ Server scanner button found\n";
    }
    if (strpos($response, 'diff-viewer') !== false) {
        echo "   ✓ Diff viewer found\n";
    }
} else {
    echo "   ✗ HTTP ERROR: $httpCode\n";
}

echo "\n===========================================\n";
echo "API Endpoint Testing Completed\n";
?>
