<?php
/**
 * Test server scanner functionality by simulating the JavaScript workflow
 */

// Define access constant
define('SVN_TOOL_ACCESS', true);

echo "Testing Server Scanner Functionality\n";
echo "====================================\n\n";

// Test 1: Verify the server scanner API works
echo "1. Testing Server Scanner API directly:\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/api.php?action=server_scan&path=/var/www&depth=2');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "   HTTP Code: $httpCode\n";
if ($httpCode == 200) {
    $data = json_decode($response, true);
    if ($data && $data['status'] === 'success') {
        echo "   ✓ API SUCCESS: Found " . count($data['data']) . " repositories\n";
        
        echo "\n   Repository Details:\n";
        foreach ($data['data'] as $repo) {
            echo "   - Name: {$repo['name']}\n";
            echo "     Path: {$repo['path']}\n";
            echo "     Type: {$repo['type']}\n";
            echo "     Status: {$repo['status']}\n";
            if (isset($repo['url'])) {
                echo "     URL: {$repo['url']}\n";
            }
            if (isset($repo['revision'])) {
                echo "     Revision: {$repo['revision']}\n";
            }
            echo "\n";
        }
        
        // Test 2: Verify the response format matches what JavaScript expects
        echo "2. Testing Response Format for JavaScript:\n";
        
        $expectedFields = array('status', 'data', 'meta');
        $allFieldsPresent = true;
        
        foreach ($expectedFields as $field) {
            if (isset($data[$field])) {
                echo "   ✓ Field '$field' present\n";
            } else {
                echo "   ✗ Field '$field' missing\n";
                $allFieldsPresent = false;
            }
        }
        
        if ($allFieldsPresent && isset($data['meta']['total_found'])) {
            echo "   ✓ Meta information complete\n";
            echo "   ✓ Total found: {$data['meta']['total_found']}\n";
            echo "   ✓ Scan path: {$data['meta']['scan_path']}\n";
            echo "   ✓ Scan time: {$data['meta']['scan_time']}\n";
        }
        
        // Test 3: Verify each repository has required fields
        echo "\n3. Testing Repository Data Structure:\n";
        
        $requiredRepoFields = array('name', 'path', 'type', 'status');
        $allReposValid = true;
        
        foreach ($data['data'] as $index => $repo) {
            echo "   Repository $index ({$repo['name']}):\n";
            foreach ($requiredRepoFields as $field) {
                if (isset($repo[$field])) {
                    echo "     ✓ $field: {$repo[$field]}\n";
                } else {
                    echo "     ✗ $field: MISSING\n";
                    $allReposValid = false;
                }
            }
        }
        
        if ($allReposValid) {
            echo "   ✓ All repositories have required fields\n";
        } else {
            echo "   ✗ Some repositories missing required fields\n";
        }
        
    } else {
        echo "   ✗ API FAILED: " . (isset($data['message']) ? $data['message'] : 'Unknown error') . "\n";
    }
} else {
    echo "   ✗ HTTP ERROR: $httpCode\n";
    echo "   Response: " . substr($response, 0, 200) . "\n";
}

echo "\n====================================\n";

// Test 4: Verify the web interface has the required elements
echo "4. Testing Web Interface Elements:\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    $requiredElements = array(
        'start-scan-btn' => 'Server scanner start button',
        'server-scanner-modal' => 'Server scanner modal',
        'scan-path' => 'Scan path input',
        'scan-depth' => 'Scan depth select',
        'scan-progress' => 'Scan progress indicator',
        'scan-results' => 'Scan results container',
        'found-repos' => 'Found repositories container'
    );
    
    foreach ($requiredElements as $elementId => $description) {
        if (strpos($response, 'id="' . $elementId . '"') !== false) {
            echo "   ✓ $description found\n";
        } else {
            echo "   ✗ $description missing\n";
        }
    }
    
    // Check for JavaScript initialization
    if (strpos($response, 'SvnTool.init()') !== false) {
        echo "   ✓ JavaScript initialization found\n";
    } else {
        echo "   ✗ JavaScript initialization missing\n";
    }
    
    // Check for event handlers
    if (strpos($response, 'SvnTool.ServerScanner.openModal()') !== false) {
        echo "   ✓ Server scanner event handler found\n";
    } else {
        echo "   ✗ Server scanner event handler missing\n";
    }
    
} else {
    echo "   ✗ Web interface not accessible: HTTP $httpCode\n";
}

echo "\n====================================\n";
echo "Server Scanner Functionality Test Complete\n";

// Summary
echo "\nSUMMARY:\n";
echo "- API Endpoint: " . ($httpCode == 200 && isset($data) && $data['status'] === 'success' ? "✓ WORKING" : "✗ FAILED") . "\n";
echo "- Response Format: " . (isset($allFieldsPresent) && $allFieldsPresent ? "✓ CORRECT" : "✗ INCORRECT") . "\n";
echo "- Repository Data: " . (isset($allReposValid) && $allReposValid ? "✓ VALID" : "✗ INVALID") . "\n";
echo "- Web Interface: " . (strpos($response, 'start-scan-btn') !== false ? "✓ ELEMENTS PRESENT" : "✗ ELEMENTS MISSING") . "\n";

if (isset($data) && $data['status'] === 'success' && count($data['data']) > 0) {
    echo "\n🎯 EXPECTED BEHAVIOR:\n";
    echo "When user clicks 'Démarrer le scan' button:\n";
    echo "1. Modal should show 'Scanning...' progress\n";
    echo "2. API request should be made to: /svn-tool/api.php?action=server_scan&path=/var/www&depth=3\n";
    echo "3. Results should show " . count($data['data']) . " repositories:\n";
    foreach ($data['data'] as $repo) {
        echo "   - {$repo['name']} at {$repo['path']}\n";
    }
    echo "4. User should be able to select repositories and add them to base path\n";
}
?>
