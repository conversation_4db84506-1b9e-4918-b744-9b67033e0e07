<?php
/**
 * SVN Authentication System Test
 * 
 * Test script to verify the authentication modal system works correctly
 */

// Initialize session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Define access constant
define('SVN_TOOL_ACCESS', true);

// Include required files
require_once 'config/config.php';
require_once 'api-utilities.php';

// Generate CSRF token
function generateTestCsrfToken() {
    if (!isset($_SESSION['svn_csrf_token'])) {
        $_SESSION['svn_csrf_token'] = bin2hex(openssl_random_pseudo_bytes(32));
    }
    return $_SESSION['svn_csrf_token'];
}

$csrfToken = generateTestCsrfToken();

// Test credential functions
echo "<h1>SVN Authentication System Test</h1>";

echo "<h2>1. Testing Credential Storage Functions</h2>";

// Test storing credentials
try {
    $result = storeSvnCredentials('testuser', 'testpass', true);
    echo "<p>✅ Store credentials: " . json_encode($result) . "</p>";
} catch (Exception $e) {
    echo "<p>❌ Store credentials failed: " . $e->getMessage() . "</p>";
}

// Test checking credentials
try {
    $hasCredentials = hasSvnCredentials();
    echo "<p>✅ Has credentials: " . ($hasCredentials ? 'Yes' : 'No') . "</p>";
} catch (Exception $e) {
    echo "<p>❌ Check credentials failed: " . $e->getMessage() . "</p>";
}

// Test getting credentials
try {
    $credentials = getSvnCredentials();
    if ($credentials) {
        echo "<p>✅ Get credentials: Username = " . $credentials['username'] . ", Password length = " . strlen($credentials['password']) . "</p>";
    } else {
        echo "<p>⚠️ No credentials found</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Get credentials failed: " . $e->getMessage() . "</p>";
}

echo "<h2>2. Testing Authentication Error Detection</h2>";

// Test authentication error patterns
$testErrors = array(
    'svn: E215004: Authentication failed',
    'svn: E170013: Unable to connect to a repository',
    'Authentication required',
    'No more credentials',
    'Some other error'
);

echo "<script>
// Test JavaScript authentication error detection
var authPatterns = [
    'Authentication required',
    'Authentication failed',
    'E170013',
    'E215004',
    'No more credentials',
    'svn: E170013',
    'svn: E215004'
];

function isAuthenticationError(message) {
    for (var i = 0; i < authPatterns.length; i++) {
        if (message.indexOf(authPatterns[i]) !== -1) {
            return true;
        }
    }
    return false;
}

var testErrors = " . json_encode($testErrors) . ";
console.log('Testing authentication error detection:');
testErrors.forEach(function(error) {
    var isAuth = isAuthenticationError(error);
    console.log('Error: \"' + error + '\" -> Authentication error: ' + isAuth);
});
</script>";

foreach ($testErrors as $error) {
    $isAuthError = (
        strpos($error, 'Authentication') !== false ||
        strpos($error, 'E170013') !== false ||
        strpos($error, 'E215004') !== false ||
        strpos($error, 'No more credentials') !== false
    );
    echo "<p>" . ($isAuthError ? "✅" : "❌") . " \"$error\" -> " . ($isAuthError ? "Authentication error" : "Not authentication error") . "</p>";
}

echo "<h2>3. Testing Clear Credentials</h2>";

// Test clearing credentials
try {
    $result = clearSvnCredentials();
    echo "<p>✅ Clear credentials: " . json_encode($result) . "</p>";
} catch (Exception $e) {
    echo "<p>❌ Clear credentials failed: " . $e->getMessage() . "</p>";
}

// Verify credentials are cleared
try {
    $hasCredentials = hasSvnCredentials();
    echo "<p>✅ Has credentials after clear: " . ($hasCredentials ? 'Yes' : 'No') . "</p>";
} catch (Exception $e) {
    echo "<p>❌ Check credentials after clear failed: " . $e->getMessage() . "</p>";
}

echo "<h2>4. Session Information</h2>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>CSRF Token: " . htmlspecialchars($csrfToken) . "</p>";
echo "<p>Session data: " . json_encode($_SESSION) . "</p>";

echo "<h2>5. Test Authentication Modal (Visual Test)</h2>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Authentication Modal Test</title>
    <link rel="stylesheet" href="assets/css/svn-tool.css">
</head>
<body>
    <div class="container">
        <button id="test-auth-modal" class="button button-primary">Test Authentication Modal</button>
        <button id="test-auth-error" class="button button-danger">Test Authentication Error</button>
        <div id="test-results" style="margin-top: 20px;"></div>
    </div>

    <!-- Authentication Modal -->
    <div id="svn-auth-modal" class="auth-modal">
        <div class="auth-modal-dialog">
            <div class="auth-modal-content">
                <div class="auth-modal-header">
                    <h4 class="auth-modal-title">
                        <span>🔐</span>
                        Authentification SVN requise
                    </h4>
                    <button type="button" class="close" onclick="closeTestModal()">&times;</button>
                </div>
                <div class="auth-modal-body">
                    <div id="auth-error" class="auth-error">
                        <strong>Erreur d'authentification</strong><br>
                        <span id="auth-error-message">Test error message</span>
                    </div>
                    
                    <div class="auth-info">
                        <strong>Authentification requise</strong><br>
                        Cette opération SVN nécessite une authentification. Veuillez entrer vos identifiants SVN.
                    </div>

                    <form id="auth-form" class="auth-form">
                        <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrfToken); ?>">
                        
                        <div class="form-group">
                            <label for="svn-username">Nom d'utilisateur SVN</label>
                            <input type="text" id="svn-username" name="username" class="form-control" placeholder="Votre nom d'utilisateur SVN" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="svn-password">Mot de passe SVN</label>
                            <input type="password" id="svn-password" name="password" class="form-control" placeholder="Votre mot de passe SVN" required>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="remember-credentials" name="remember" value="1" checked>
                                Mémoriser les identifiants pour cette session
                            </label>
                        </div>
                    </form>
                    
                    <div id="credential-status" class="credential-status missing">
                        <div class="credential-status-icon"></div>
                        <span>Aucun identifiant stocké</span>
                    </div>
                </div>
                <div class="auth-modal-footer">
                    <button type="button" class="button button-secondary" onclick="closeTestModal()">Annuler</button>
                    <button type="button" id="auth-submit-btn" class="button button-primary" onclick="testSubmit()">Authentifier</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTestModal() {
            document.getElementById('svn-auth-modal').classList.add('show');
        }
        
        function closeTestModal() {
            document.getElementById('svn-auth-modal').classList.remove('show');
        }
        
        function testSubmit() {
            var username = document.getElementById('svn-username').value;
            var password = document.getElementById('svn-password').value;
            
            document.getElementById('test-results').innerHTML = 
                '<p>✅ Form submitted with username: ' + username + ' and password length: ' + password.length + '</p>';
            
            closeTestModal();
        }
        
        function showAuthError() {
            document.getElementById('auth-error').classList.add('show');
            document.getElementById('auth-error-message').textContent = 'Test authentication error message';
            showTestModal();
        }
        
        document.getElementById('test-auth-modal').addEventListener('click', showTestModal);
        document.getElementById('test-auth-error').addEventListener('click', showAuthError);
    </script>
</body>
</html>
