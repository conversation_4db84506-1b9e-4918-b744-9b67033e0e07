<?php
/**
 * Test API Endpoint - Debug Repository Loading
 */

// Set JSON header immediately
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Enable error reporting but capture errors
error_reporting(E_ALL);
ini_set('display_errors', 0);

try {
    // Test basic functionality
    $response = array(
        'status' => 'success',
        'message' => 'Test API is working',
        'timestamp' => date('Y-m-d H:i:s'),
        'php_version' => PHP_VERSION,
        'server_info' => isset($_SERVER['SERVER_SOFTWARE']) ? $_SERVER['SERVER_SOFTWARE'] : 'Unknown'
    );

    // Test if we can include config
    if (file_exists('config/config.php')) {
        require_once 'config/config.php';
        $response['config_loaded'] = true;
        $response['repo_base_path'] = config('repository.base_path', 'Not set');
        
        // Test if base path exists
        $basePath = config('repository.base_path');
        if (!empty($basePath)) {
            $response['base_path_exists'] = is_dir($basePath);
            $response['base_path_readable'] = is_readable($basePath);
            
            if (is_dir($basePath) && is_readable($basePath)) {
                $items = scandir($basePath);
                $response['base_path_items'] = $items !== false ? count($items) - 2 : 0; // -2 for . and ..
            }
        }
    } else {
        $response['config_loaded'] = false;
        $response['error'] = 'Config file not found';
    }

    // Test main API endpoint
    if (isset($_GET['action']) && $_GET['action'] === 'list_repos') {
        // Redirect to main API
        $apiUrl = 'api.php?action=list_repos';
        $context = stream_context_create(array(
            'http' => array(
                'method' => 'GET',
                'header' => 'Content-Type: application/json'
            )
        ));
        
        $apiResponse = file_get_contents($apiUrl, false, $context);
        if ($apiResponse !== false) {
            $apiData = json_decode($apiResponse, true);
            if ($apiData) {
                echo $apiResponse;
                exit;
            }
        }
        
        $response = array(
            'status' => 'error',
            'message' => 'Failed to call main API endpoint'
        );
    }

    // Test repository scanning directly
    if (isset($_GET['test_repos']) && $_GET['test_repos'] === '1') {
        if (file_exists('config/config.php')) {
            require_once 'config/config.php';
            
            $basePath = config('repository.base_path');
            
            if (empty($basePath)) {
                $response['repo_test'] = 'No base path configured';
            } elseif (!is_dir($basePath)) {
                $response['repo_test'] = 'Base path does not exist: ' . $basePath;
            } elseif (!is_readable($basePath)) {
                $response['repo_test'] = 'Base path is not readable: ' . $basePath;
            } else {
                // Try to scan for repositories
                $repos = array();
                $items = scandir($basePath);
                
                if ($items !== false) {
                    foreach ($items as $item) {
                        if ($item === '.' || $item === '..') continue;
                        
                        $itemPath = $basePath . '/' . $item;
                        if (is_dir($itemPath)) {
                            // Check if it's an SVN working copy
                            if (is_dir($itemPath . '/.svn')) {
                                $repos[] = array(
                                    'name' => $item,
                                    'path' => $itemPath,
                                    'type' => 'working_copy'
                                );
                            }
                            // Check if it's an SVN repository
                            elseif (is_dir($itemPath . '/conf') && is_dir($itemPath . '/db') && is_file($itemPath . '/format')) {
                                $repos[] = array(
                                    'name' => $item,
                                    'path' => $itemPath,
                                    'type' => 'repository'
                                );
                            }
                        }
                    }
                }
                
                $response['repo_test'] = 'Found ' . count($repos) . ' repositories';
                $response['repositories'] = $repos;
            }
        }
    }

} catch (Exception $e) {
    $response = array(
        'status' => 'error',
        'message' => $e->getMessage(),
        'file' => basename($e->getFile()),
        'line' => $e->getLine()
    );
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>