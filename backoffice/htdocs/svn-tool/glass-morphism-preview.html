<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVN Tool - Glass Morphism Design Preview</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;800&family=Rubik&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/svn-tool.css">
    <style>
        body {
            font-family: 'Montserrat', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        .standalone-header {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.18);
            color: #1e293b;
            padding: 2rem;
            margin: 0;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
        }
        .standalone-header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            letter-spacing: -0.025em;
            color: #1e293b;
        }
    </style>
</head>
<body>
    <div class="standalone-header">
        <h1>SVN Tool - Glass Morphism Design Preview</h1>
    </div>

    <div class="container">
        <!-- Workflow Container -->
        <div class="workflow-container">
            
            <!-- Step 1: Configuration -->
            <div class="workflow-step">
                <div class="step-indicator">
                    <div class="step-number">1</div>
                    <div class="step-title">Configuration</div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h2>Configuration du dépôt</h2>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="form-group">
                                <label for="repo_base_path">Chemin de base des dépôts SVN</label>
                                <input type="text"
                                       id="repo_base_path"
                                       name="repo_base_path"
                                       class="form-control"
                                       value="/var/www/sites"
                                       placeholder="/var/www/repositories">
                                <small class="form-text">
                                    Entrez le chemin absolu vers votre répertoire de dépôts SVN<br>
                                    <strong>Suggestions pour Docker:</strong> /var/www/sites/front, /var/www/sites, /var/www/riashop, /var/www/engine
                                </small>
                            </div>
                            <div class="button-group">
                                <button type="submit" class="button button-primary">
                                    Sauvegarder la configuration
                                </button>
                                <button type="button" class="button button-secondary">
                                    Scanner le serveur
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Step 2: Repository Selection -->
            <div class="workflow-step">
                <div class="step-indicator">
                    <div class="step-number">2</div>
                    <div class="step-title">Sélection du dépôt</div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h3>Dépôts disponibles</h3>
                        <div class="button-group">
                            <button class="button button-secondary button-sm">
                                Actualiser
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <div class="list-group-item active">
                                <div class="list-item-content">
                                    <span>engine</span>
                                </div>
                                <div class="list-item-actions">
                                    <span class="badge status-modified">modifié</span>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="list-item-content">
                                    <span>front</span>
                                </div>
                                <div class="list-item-actions">
                                    <span class="badge status-added">ajouté</span>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="list-item-content">
                                    <span>riashop</span>
                                </div>
                                <div class="list-item-actions">
                                    <span class="badge badge-success">à jour</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: File Management -->
            <div class="workflow-step">
                <div class="step-indicator">
                    <div class="step-number">3</div>
                    <div class="step-title">Gestion des fichiers</div>
                </div>
                <div class="grid grid-cols-2">
                    <!-- File Status Panel -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Statut des fichiers</h3>
                            <div class="button-group">
                                <button class="button button-secondary button-sm">
                                    Permissions
                                </button>
                                <button class="button button-warning button-sm">
                                    Nettoyage
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="list-group">
                                <div class="list-group-item">
                                    <div class="list-item-content">
                                        <input type="checkbox" class="file-checkbox" checked>
                                        <span>index.php</span>
                                    </div>
                                    <div class="list-item-actions">
                                        <span class="badge status-modified">M</span>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="list-item-content">
                                        <input type="checkbox" class="file-checkbox">
                                        <span>config.php</span>
                                    </div>
                                    <div class="list-item-actions">
                                        <span class="badge status-added">A</span>
                                    </div>
                                </div>
                                <div class="list-group-item">
                                    <div class="list-item-content">
                                        <input type="checkbox" class="file-checkbox">
                                        <span>test.php</span>
                                    </div>
                                    <div class="list-item-actions">
                                        <span class="badge status-unversioned">?</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- File Actions Panel -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Actions sur les fichiers</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="commit-message">Message de commit</label>
                                <textarea id="commit-message"
                                         class="form-control"
                                         placeholder="Entrez votre message de commit..."></textarea>
                            </div>
                            
                            <div class="button-group button-group-vertical">
                                <button class="button button-success">
                                    Commiter les fichiers sélectionnés
                                </button>
                                <div class="button-group">
                                    <button class="button button-secondary button-sm">
                                        Tout sélectionner
                                    </button>
                                    <button class="button button-secondary button-sm">
                                        Tout désélectionner
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Diff Viewer -->
            <div class="workflow-step">
                <div class="step-indicator">
                    <div class="step-number">4</div>
                    <div class="step-title">Visualisation des différences</div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h3>Visualiseur de différences</h3>
                        <div class="button-group">
                            <button class="button button-secondary button-sm">
                                Copier le diff
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="diff-placeholder">
                            <p>Cliquez sur un fichier pour voir ses différences</p>
                            <small>Les modifications seront surlignées avec coloration syntaxique</small>
                        </div>
                    </div>
                </div>
            </div>
            
        </div> <!-- End workflow-container -->
    </div>

    <!-- Notification Examples -->
    <div id="notification-container">
        <div class="notification notification-success">
            Configuration sauvegardée avec succès
        </div>
        <div class="notification notification-info">
            Scan du serveur en cours...
        </div>
    </div>
</body>
</html>
