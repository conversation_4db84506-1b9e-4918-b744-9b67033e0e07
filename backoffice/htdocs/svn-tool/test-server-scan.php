<?php
/**
 * Test script for server scanning functionality
 */

// Define access constant
define('SVN_TOOL_ACCESS', true);

// Start session
session_start();

// Set up fake POST data for testing
$_POST['csrf_token'] = 'test_token';
$_SESSION['svn_csrf_token'] = 'test_token';

// Include required files
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'api-functions.php';
require_once 'api-utilities.php';

echo "Testing Server Scan API\n";
echo "=======================\n\n";

// Test the server scan function directly
try {
    // Simulate JSON input
    $testData = array(
        'path' => '/home/<USER>/RIASHOP/LOCALHOST/test_vibe/sites',
        'depth' => 2
    );
    
    // Temporarily override php://input
    $originalInput = file_get_contents('php://input');
    
    // Create a temporary file to simulate JSON input
    $tempFile = tempnam(sys_get_temp_dir(), 'svn_test');
    file_put_contents($tempFile, json_encode($testData));
    
    echo "Testing handleServerScan function...\n";
    
    // Capture output
    ob_start();
    
    // Mock the input stream
    $GLOBALS['test_input'] = json_encode($testData);
    
    // Override file_get_contents for php://input
    function file_get_contents_override($filename) {
        if ($filename === 'php://input') {
            return $GLOBALS['test_input'];
        }
        return file_get_contents($filename);
    }
    
    // Test with direct function call
    if (function_exists('handleServerScan')) {
        // Temporarily replace file_get_contents
        $originalFunction = 'file_get_contents';
        
        // Call the function
        try {
            // Simulate the input manually
            $_POST = array(); // Clear POST to force JSON input reading
            
            // We'll test the scanning logic directly instead
            if (file_exists('models/ServerScanner.php')) {
                require_once 'models/ServerScanner.php';
                
                $scanner = new ServerScanner();
                $scanner->setMaxDepth(2);
                
                echo "Scanning path: {$testData['path']}\n";
                $repositories = $scanner->scanForSvnRepositories($testData['path']);
                
                echo "Found " . count($repositories) . " repositories:\n";
                foreach ($repositories as $repo) {
                    echo "  - {$repo['name']} ({$repo['type']}) at {$repo['path']}\n";
                    if (isset($repo['url'])) {
                        echo "    URL: {$repo['url']}\n";
                    }
                    if (isset($repo['revision'])) {
                        echo "    Revision: {$repo['revision']}\n";
                    }
                    if (isset($repo['status'])) {
                        echo "    Status: {$repo['status']}\n";
                    }
                    echo "\n";
                }
                
            } else {
                echo "ServerScanner model not found, using fallback...\n";
                $repositories = scanForRepositories($testData['path'], $testData['depth']);
                
                echo "Found " . count($repositories) . " repositories:\n";
                foreach ($repositories as $repo) {
                    echo "  - {$repo['name']} ({$repo['type']}) at {$repo['path']}\n";
                }
            }
            
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage() . "\n";
        }
    } else {
        echo "handleServerScan function not found\n";
    }
    
    $output = ob_get_clean();
    echo $output;
    
    // Clean up
    if (file_exists($tempFile)) {
        unlink($tempFile);
    }
    
} catch (Exception $e) {
    echo "Test failed: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
?>
