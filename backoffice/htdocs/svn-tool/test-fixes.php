<?php
/**
 * Test script to verify the fixes for duplicate API calls and SVN permission issues
 */

// Initialize session and constants
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

define('SVN_TOOL_ROOT', __DIR__);
define('SVN_TOOL_ACCESS', true);

// Include required files
require_once 'lib/helpers.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'api-utilities.php';

echo "=== SVN Tool Fixes Test ===\n\n";

// Test 1: Check JavaScript file structure
echo "1. Testing JavaScript file structure...\n";

$jsFiles = array(
    'assets/js/app.js' => 'Modern ES6+ version (should not be loaded)',
    'assets/js/svn-tool.js' => 'PHP 5.6 compatible version (should be loaded)'
);

foreach ($jsFiles as $file => $description) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        echo "   ✓ {$file} exists - {$description}\n";
        
        // Check for initialization patterns
        if (strpos($content, 'document.addEventListener(\'DOMContentLoaded\'') !== false) {
            echo "     - Has DOM ready listener\n";
        }
        if (strpos($content, '.init()') !== false) {
            echo "     - Has init() method\n";
        }
    } else {
        echo "   ✗ {$file} missing\n";
    }
}

// Test 2: Check view files for duplicate script loading
echo "\n2. Testing view files for script loading...\n";

$viewFiles = array(
    'views/layout.php',
    'views/main.php',
    'index.php'
);

foreach ($viewFiles as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        echo "   Checking {$file}:\n";
        
        $appJsCount = substr_count($content, 'app.js');
        $svnToolJsCount = substr_count($content, 'svn-tool.js');
        
        echo "     - app.js references: {$appJsCount}\n";
        echo "     - svn-tool.js references: {$svnToolJsCount}\n";
        
        if ($appJsCount > 1 || $svnToolJsCount > 1) {
            echo "     ⚠️  Potential duplicate loading detected\n";
        }
    }
}

// Test 3: Test SVN permission functions
echo "\n3. Testing SVN permission functions...\n";

if (function_exists('fixSvnPermissions')) {
    echo "   ✓ fixSvnPermissions() function exists\n";
} else {
    echo "   ✗ fixSvnPermissions() function missing\n";
}

if (function_exists('cleanupRepository')) {
    echo "   ✓ cleanupRepository() function exists\n";
} else {
    echo "   ✗ cleanupRepository() function missing\n";
}

// Test 4: Check API endpoints
echo "\n4. Testing API endpoints...\n";

$apiFile = 'api.php';
if (file_exists($apiFile)) {
    $content = file_get_contents($apiFile);
    
    $endpoints = array(
        'fix_permissions' => 'Fix permissions endpoint',
        'cleanup' => 'Cleanup endpoint',
        'commit' => 'Commit endpoint'
    );
    
    foreach ($endpoints as $endpoint => $description) {
        if (strpos($content, "'{$endpoint}'") !== false) {
            echo "   ✓ {$description} found\n";
        } else {
            echo "   ✗ {$description} missing\n";
        }
    }
}

// Test 5: Check for enhanced error handling
echo "\n5. Testing enhanced error handling...\n";

$utilitiesFile = 'api-utilities.php';
if (file_exists($utilitiesFile)) {
    $content = file_get_contents($utilitiesFile);
    
    $errorHandling = array(
        'readonly database' => 'Readonly database error handling',
        'E200031' => 'E200031 error code handling',
        'E155004' => 'E155004 error code handling',
        'chmod 664' => 'Database permission fixing',
        'svn cleanup' => 'SVN cleanup retry logic'
    );
    
    foreach ($errorHandling as $pattern => $description) {
        if (strpos($content, $pattern) !== false) {
            echo "   ✓ {$description} found\n";
        } else {
            echo "   ✗ {$description} missing\n";
        }
    }
}

// Test 6: Test actual SVN repository if available
echo "\n6. Testing SVN repository access...\n";

$testRepo = '/var/www/sites/front';
if (is_dir($testRepo)) {
    echo "   ✓ Test repository exists: {$testRepo}\n";
    
    if (is_dir($testRepo . '/.svn')) {
        echo "   ✓ SVN working copy detected\n";
        
        // Test permissions
        $svnDir = $testRepo . '/.svn';
        $readable = is_readable($svnDir);
        $writable = is_writable($svnDir);
        
        echo "   - .svn directory readable: " . ($readable ? 'Yes' : 'No') . "\n";
        echo "   - .svn directory writable: " . ($writable ? 'Yes' : 'No') . "\n";
        
        if (!$writable) {
            echo "   ⚠️  SVN directory not writable - this may cause commit issues\n";
        }
    } else {
        echo "   ✗ Not an SVN working copy\n";
    }
} else {
    echo "   ✗ Test repository not found: {$testRepo}\n";
}

echo "\n=== Test Complete ===\n";
echo "If you see any ✗ or ⚠️  symbols above, those indicate issues that need attention.\n";
?>
