<?php
/**
 * File Controller
 */

class FileController extends BaseController
{
    private $svnFile;
    
    public function __construct()
    {
        parent::__construct();
        $this->svnFile = new SvnFile();
    }
    
    public function getDiff()
    {
        try {
            $basePath = $this->requireRepoPath();
            $filePath = isset($_GET['path']) ? $_GET['path'] : '';
            
            if (empty($filePath) || !isPathSafe($filePath, $basePath)) {
                throw new Exception('Invalid or unsafe file path.');
            }
            
            $diff = $this->svnFile->getDiff($filePath);
            
            jsonResponse(array(
                'status' => 'success',
                'data' => $diff
            ));
        } catch (Exception $e) {
            jsonResponse($this->handleException($e, 'get_diff'), 500);
        }
    }
    
    public function commit()
    {
        try {
            $basePath = $this->requireRepoPath();
            
            $input = json_decode(file_get_contents('php://input'), true);
            $files = isset($input['files']) ? $input['files'] : array();
            $message = isset($input['message']) ? $input['message'] : '';
            
            if (empty($files)) {
                throw new Exception('No files selected for commit.');
            }
            
            if (empty($message)) {
                throw new Exception('Commit message is required.');
            }
            
            // Validate all file paths
            $safeFiles = array();
            foreach ($files as $file) {
                if (isPathSafe($file, $basePath)) {
                    $safeFiles[] = $file;
                }
            }
            
            if (empty($safeFiles)) {
                throw new Exception('No valid files selected for commit.');
            }
            
            $result = $this->svnFile->commit($safeFiles, $message);
            
            jsonResponse(array(
                'status' => 'success',
                'message' => 'Commit completed successfully.',
                'data' => $result
            ));
        } catch (Exception $e) {
            jsonResponse($this->handleException($e, 'commit'), 500);
        }
    }
}