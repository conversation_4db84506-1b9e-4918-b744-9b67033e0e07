<?php
/**
 * Repository Controller
 */

class RepositoryController extends BaseController
{
    private $svnRepo;
    private $permissionChecker;
    
    public function __construct()
    {
        parent::__construct();
        $this->svnRepo = new SvnRepository();
        $this->permissionChecker = new PermissionChecker();
    }
    
    public function index()
    {
        $repoBasePath = $this->getRepoBasePath();
        
        echo view('layout', array(
            'title' => config('app.name'),
            'content' => view('dashboard', array(
                'repoBasePath' => $repoBasePath,
                'csrfToken' => generateCsrfToken()
            ))
        ));
    }
    
    public function updateConfig()
    {
        try {
            $this->validateCsrf();
            $this->getRepoBasePath(); // This will update the session
            
            header('Location: ' . url());
            exit;
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
            header('Location: ' . url());
            exit;
        }
    }
    
    public function listRepositories()
    {
        error_log('RepositoryController: listRepositories called');
        
        try {
            $basePath = $this->requireRepoPath();
            error_log("RepositoryController: Using base path: {$basePath}");
            
            $repositories = $this->svnRepo->findRepositories($basePath);
            error_log('RepositoryController: Found ' . count($repositories) . ' repositories');
            
            $response = array(
                'status' => 'success',
                'data' => $repositories
            );
            
            error_log('RepositoryController: Sending response: ' . json_encode($response));
            jsonResponse($response);
            
        } catch (Exception $e) {
            error_log('RepositoryController: Error in listRepositories: ' . $e->getMessage());
            
            $errorResponse = $this->handleException($e, 'list_repositories');
            
            // If no base path is configured, provide a more helpful message
            if (strpos($e->getMessage(), 'not configured') !== false) {
                $errorResponse['message'] = 'Repository base path is not configured. Please set it in the configuration section above.';
                $errorResponse['error_type'] = 'configuration';
            }
            
            jsonResponse($errorResponse, 500);
        }
    }
    
    public function getStatus()
    {
        try {
            $basePath = $this->requireRepoPath();
            $repoPath = isset($_GET['path']) ? $_GET['path'] : '';
            
            if (empty($repoPath) || !isPathSafe($repoPath, $basePath)) {
                throw new Exception('Invalid or unsafe repository path.');
            }
            
            $files = $this->svnRepo->getStatus($repoPath);
            
            jsonResponse(array(
                'status' => 'success',
                'data' => $files
            ));
        } catch (Exception $e) {
            jsonResponse($this->handleException($e, 'get_status'), 500);
        }
    }
    
    public function cleanup()
    {
        try {
            $basePath = $this->requireRepoPath();
            $repoPath = isset($_GET['current_repo']) ? $_GET['current_repo'] : $basePath;
            
            if (empty($repoPath) || !isPathSafe($repoPath, $basePath)) {
                throw new Exception('Invalid repository path for cleanup.');
            }
            
            $result = $this->svnRepo->cleanup($repoPath);
            
            jsonResponse(array(
                'status' => 'success',
                'message' => 'SVN cleanup completed successfully.',
                'data' => $result
            ));
        } catch (Exception $e) {
            jsonResponse($this->handleException($e, 'cleanup'), 500);
        }
    }
    
    public function checkPermissions()
    {
        try {
            $basePath = $this->requireRepoPath();
            $repoPath = isset($_GET['path']) ? $_GET['path'] : (isset($_GET['current_repo']) ? $_GET['current_repo'] : $basePath);
            
            if (empty($repoPath) || !isPathSafe($repoPath, $basePath)) {
                throw new Exception('Invalid repository path for permission check.');
            }
            
            $permissions = $this->permissionChecker->checkPermissions($repoPath);
            $svnTest = $this->svnRepo->testCommand($repoPath);
            $diagnosis = $this->permissionChecker->diagnoseIssues($permissions);
            
            jsonResponse(array(
                'status' => 'success',
                'data' => array(
                    'repo_path' => $repoPath,
                    'permissions' => $permissions,
                    'svn_test' => $svnTest,
                    'diagnosis' => $diagnosis
                )
            ));
        } catch (Exception $e) {
            jsonResponse($this->handleException($e, 'check_permissions'), 500);
        }
    }
    
    public function getPermissionFix()
    {
        try {
            $basePath = $this->requireRepoPath();
            $repoPath = isset($_GET['current_repo']) ? $_GET['current_repo'] : $basePath;
            
            if (empty($repoPath) || !isPathSafe($repoPath, $basePath)) {
                throw new Exception('Invalid repository path for permission fix.');
            }
            
            $permissions = $this->permissionChecker->checkPermissions($repoPath);
            $fixCommands = $this->permissionChecker->generateFixCommands($repoPath, $permissions);
            
            jsonResponse(array(
                'status' => 'success',
                'data' => array(
                    'repo_path' => $repoPath,
                    'current_permissions' => $permissions,
                    'fix_commands' => $fixCommands,
                    'summary' => array(
                        'issue' => 'Web server lacks write permissions to SVN repository',
                        'cause' => $permissions['repo_owner'] === 'unknown' ? 
                                  'Repository ownership is not properly set' : 
                                  'Web server user is not in repository owner group',
                        'solution' => 'Run the provided commands as system administrator'
                    )
                )
            ));
        } catch (Exception $e) {
            jsonResponse($this->handleException($e, 'get_permission_fix'), 500);
        }
    }
}