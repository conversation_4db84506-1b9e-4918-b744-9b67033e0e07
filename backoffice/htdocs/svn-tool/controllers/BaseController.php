<?php
/**
 * Base Controller Class
 */

class BaseController
{
    protected $config;
    
    public function __construct()
    {
        // Load the entire config array
        $this->config = config(null) ?: array();
    }
    
    protected function validateCsrf()
    {
        if (config('security.csrf_protection')) {
            $token = '';
            if (isset($_POST['csrf_token'])) {
                $token = $_POST['csrf_token'];
            } elseif (isset($_SERVER['HTTP_X_CSRF_TOKEN'])) {
                $token = $_SERVER['HTTP_X_CSRF_TOKEN'];
            }
            
            if (!validateCsrfToken($token)) {
                throw new Exception('CSRF token validation failed');
            }
        }
    }
    
    protected function getRepoBasePath()
    {
        if (isset($_POST['repo_base_path']) && !empty($_POST['repo_base_path'])) {
            $path = rtrim($_POST['repo_base_path'], '/') . '/';
            
            if (!is_dir($path) || !is_readable($path)) {
                return null;
            }
            
            $_SESSION['repo_base_path'] = $path;
            return $path;
        }
        
        if (isset($_SESSION['repo_base_path']) && !empty($_SESSION['repo_base_path'])) {
            $path = $_SESSION['repo_base_path'];
            
            if (!is_dir($path)) {
                unset($_SESSION['repo_base_path']);
                return null;
            }
            
            return $path;
        }
        
        return null;
    }
    
    protected function requireRepoPath()
    {
        $path = $this->getRepoBasePath();
        if (!$path) {
            throw new Exception('Repository base path not configured or invalid.');
        }
        return $path;
    }
    
    protected function handleException(Exception $e, $operation = 'operation')
    {
        error_log("SVN Tool Error [{$operation}]: " . $e->getMessage());
        
        return array(
            'status' => 'error',
            'message' => $e->getMessage(),
            'error_type' => 'general',
            'is_recoverable' => true,
            'operation' => $operation
        );
    }
}