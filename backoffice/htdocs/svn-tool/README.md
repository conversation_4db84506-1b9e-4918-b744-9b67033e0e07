# SVN Tool - Refactored Version

## Overview

The SVN Tool has been completely refactored to fix all existing issues and provide a robust, maintainable codebase that follows best practices and integrates properly with the RiaShop environment.

## What Was Fixed

### 1. **Non-functional Buttons**
- ✅ Fixed JavaScript event binding issues
- ✅ Implemented proper API communication
- ✅ Added comprehensive error handling
- ✅ Fixed AJAX request handling

### 2. **PHP 5.6 Compatibility**
- ✅ Removed array short syntax `[]` → `array()`
- ✅ Fixed `random_bytes()` → `openssl_random_pseudo_bytes()`
- ✅ Removed type declarations and return types
- ✅ Fixed function compatibility issues

### 3. **Code Architecture**
- ✅ Organized code into proper MVC structure
- ✅ Separated API functions into dedicated files
- ✅ Implemented consistent error handling
- ✅ Added proper function existence checks

### 4. **RiaShop Integration**
- ✅ Added support for admin skin integration
- ✅ Implemented fallback to standalone layout
- ✅ Fixed path references for Docker environment
- ✅ Added proper CSRF token handling

### 5. **Security Improvements**
- ✅ Implemented CSRF protection
- ✅ Added path safety validation
- ✅ Secured file access controls
- ✅ Added proper input validation

## File Structure

```
svn-tool/
├── index.php                 # Main entry point
├── api.php                   # API endpoint router
├── api-functions.php         # API handler functions
├── api-utilities.php         # SVN utility functions
├── config/
│   └── config.php           # Configuration settings
├── lib/
│   └── helpers.php          # Helper functions
├── includes/
│   └── functions.php        # Core functions
├── controllers/
│   └── BaseController.php   # Base controller class
├── models/
│   └── SvnRepository.php    # SVN repository model
├── assets/
│   ├── css/
│   │   └── svn-tool.css     # Styling (RiaShop compatible)
│   └── js/
│       └── svn-tool.js      # JavaScript functionality
├── .htaccess                # URL rewriting rules
└── README.md               # This documentation
```

## Features

### Core Functionality
- **Repository Management**: Scan and list SVN repositories
- **File Status**: View modified, added, deleted files
- **Commit Operations**: Commit selected files with messages
- **Repository Cleanup**: SVN cleanup operations
- **Permission Checking**: Verify repository permissions
- **Diff Viewer**: View file differences (planned)

### User Interface
- **Responsive Design**: Works on desktop and mobile
- **Professional Styling**: Clean, modern interface
- **Real-time Feedback**: Progress indicators and notifications
- **Error Handling**: User-friendly error messages
- **CSRF Protection**: Secure form submissions

### Technical Features
- **PHP 5.6 Compatible**: Works in legacy environments
- **Docker Ready**: Optimized for container deployment
- **API-Driven**: RESTful API architecture
- **Modular Design**: Easy to extend and maintain
- **Security Focused**: Input validation and path safety

## Usage

### Access URLs
- **Main Interface**: `http://localhost/svn-tool/`
- **Direct Access**: `http://localhost/svn-tool/index.php`
- **API Test**: `http://localhost/svn-tool/api.php?action=test`

### Configuration
1. Set repository base path in the interface
2. The path is saved in session for convenience
3. Default configuration in `config/config.php`

### API Endpoints
- `GET /api.php?action=test` - API health check
- `GET /api.php?action=list_repos&repo_base_path=/path` - List repositories
- `GET /api.php?action=get_status&path=/repo/path` - Get repository status
- `POST /api.php?action=commit` - Commit files
- `POST /api.php?action=cleanup` - Cleanup repository
- `POST /api.php?action=check_permissions` - Check permissions

## Troubleshooting

### URL Redirection Issues
If you experience redirection to localhost root:
1. Clear browser cache
2. Try incognito/private browsing mode
3. Access directly: `http://localhost/svn-tool/index.php`
4. Check that Docker container is running
5. Verify Apache virtual host configuration

### Common Issues
- **"SVN command not found"**: Install SVN on the system
- **"Permission denied"**: Check directory permissions
- **"Repository not found"**: Verify repository path
- **"CSRF token invalid"**: Refresh the page

## Development

### Testing
Run the comprehensive test suite:
```bash
cd /path/to/svn-tool
php test-functionality.php
php test-web-access.php
```

### Adding Features
1. Add new API handlers in `api-functions.php`
2. Add corresponding JavaScript functions in `assets/js/svn-tool.js`
3. Update the API router in `api.php`
4. Add UI elements in `index.php`

### Debugging
- Enable debug mode in `config/config.php`
- Check browser console for JavaScript errors
- Review Apache error logs for PHP errors
- Use the API test endpoint for connectivity issues

## Compatibility

- **PHP**: 5.6+ (optimized for 5.6)
- **SVN**: 1.7+ (tested with 1.14.2)
- **Browsers**: Modern browsers with JavaScript enabled
- **Docker**: Compatible with container environments
- **RiaShop**: Integrates with existing admin interface

## Security Notes

- CSRF tokens protect against cross-site request forgery
- Path validation prevents directory traversal attacks
- Input sanitization prevents injection attacks
- Session-based authentication (when integrated with RiaShop)

## Maintenance

### Regular Tasks
- Monitor error logs for issues
- Update SVN binary as needed
- Review and update repository paths
- Test functionality after system updates

### Backup Considerations
- Configuration settings are in `config/config.php`
- Session data includes repository paths
- No persistent data storage (stateless design)

---

**Refactoring completed successfully!** 🎉

All original issues have been resolved, and the SVN Tool now provides a robust, maintainable solution for SVN repository management in the RiaShop environment.
