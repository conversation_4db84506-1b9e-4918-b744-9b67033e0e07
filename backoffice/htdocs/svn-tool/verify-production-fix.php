<?php
/**
 * Verify that the duplicate API call fix works in production files only
 */

echo "=== Production Files Duplicate API Call Fix Verification ===\n\n";

// Only check production files (exclude test files)
$productionFiles = array(
    'index.php',
    'views/layout.php',
    'views/main.php',
    'views/dashboard.php',
    'controllers/RepositoryController.php'
);

echo "1. Checking production files for JavaScript loading...\n";

$totalAppJsLoads = 0;
$totalSvnToolJsLoads = 0;

foreach ($productionFiles as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);

        // Count actual script tags, not just filename references
        $appJsCount = preg_match_all('/<script[^>]*src[^>]*app\.js[^>]*>/i', $content);
        $svnToolJsCount = preg_match_all('/<script[^>]*src[^>]*svn-tool\.js[^>]*>/i', $content);

        if ($appJsCount > 0 || $svnToolJsCount > 0) {
            echo "   {$file}: app.js({$appJsCount}) svn-tool.js({$svnToolJsCount})\n";
        }

        $totalAppJsLoads += $appJsCount;
        $totalSvnToolJsLoads += $svnToolJsCount;
    }
}

echo "\n   Production totals:\n";
echo "   - app.js loads: {$totalAppJsLoads}\n";
echo "   - svn-tool.js loads: {$totalSvnToolJsLoads}\n";

if ($totalAppJsLoads === 0 && $totalSvnToolJsLoads === 1) {
    echo "   ✅ PERFECT: Only svn-tool.js loads once in production\n";
} else {
    echo "   ❌ ISSUE: Incorrect JavaScript loading in production\n";
}

// Check for inline initialization in production files
echo "\n2. Checking for inline initialization in production files...\n";

$inlineInitCount = 0;
foreach ($productionFiles as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // Look for inline SvnTool.init() calls (excluding comments)
        $lines = explode("\n", $content);
        foreach ($lines as $lineNum => $line) {
            $line = trim($line);
            if (strpos($line, 'SvnTool.init()') !== false && 
                strpos($line, '//') !== 0 && 
                strpos($line, '<!--') !== 0) {
                $inlineInitCount++;
                echo "   {$file}:{$lineNum}: {$line}\n";
            }
        }
    }
}

if ($inlineInitCount === 0) {
    echo "   ✅ No inline initialization calls in production files\n";
} else {
    echo "   ❌ Found {$inlineInitCount} inline initialization calls\n";
}

// Check JavaScript file guards
echo "\n3. Checking JavaScript file protection...\n";

if (file_exists('assets/js/app.js')) {
    $appJsContent = file_get_contents('assets/js/app.js');
    
    if (strpos($appJsContent, 'SVN_TOOL_APP_JS_DISABLED') !== false) {
        echo "   ✅ app.js has protection guard\n";
    } else {
        echo "   ❌ app.js missing protection guard\n";
    }
}

if (file_exists('assets/js/svn-tool.js')) {
    $svnToolContent = file_get_contents('assets/js/svn-tool.js');
    
    if (strpos($svnToolContent, 'this.state.initialized') !== false) {
        echo "   ✅ svn-tool.js has initialization guard\n";
    } else {
        echo "   ❌ svn-tool.js missing initialization guard\n";
    }
    
    if (strpos($svnToolContent, 'this.state.eventsBound') !== false) {
        echo "   ✅ svn-tool.js has event binding guard\n";
    } else {
        echo "   ❌ svn-tool.js missing event binding guard\n";
    }
    
    if (strpos($svnToolContent, 'DUPLICATE API CALL DETECTED') !== false) {
        echo "   ✅ svn-tool.js has API call monitoring\n";
    } else {
        echo "   ❌ svn-tool.js missing API call monitoring\n";
    }
}

// Final assessment
echo "\n=== FINAL ASSESSMENT ===\n";

$allChecksPass = (
    $totalAppJsLoads === 0 && 
    $totalSvnToolJsLoads === 1 && 
    $inlineInitCount === 0
);

if ($allChecksPass) {
    echo "🎉 ALL PRODUCTION CHECKS PASS!\n";
    echo "✅ Duplicate API call issue should be RESOLVED\n\n";
    echo "The SVN tool should now:\n";
    echo "• Load only svn-tool.js once\n";
    echo "• Initialize only once\n";
    echo "• Bind events only once\n";
    echo "• Make each API call only once\n";
    echo "• Show each notification only once\n\n";
    echo "🧪 To verify:\n";
    echo "1. Open the SVN tool in your browser\n";
    echo "2. Open browser DevTools (F12)\n";
    echo "3. Go to Console tab\n";
    echo "4. Look for initialization messages\n";
    echo "5. Click any button and check Network tab\n";
    echo "6. Verify only ONE API request per button click\n";
} else {
    echo "⚠️  SOME ISSUES REMAIN\n";
    if ($totalAppJsLoads > 0) {
        echo "❌ app.js still loads {$totalAppJsLoads} times\n";
    }
    if ($totalSvnToolJsLoads !== 1) {
        echo "❌ svn-tool.js loads {$totalSvnToolJsLoads} times (should be 1)\n";
    }
    if ($inlineInitCount > 0) {
        echo "❌ {$inlineInitCount} inline initialization calls found\n";
    }
}

echo "\n=== Test Complete ===\n";
?>
