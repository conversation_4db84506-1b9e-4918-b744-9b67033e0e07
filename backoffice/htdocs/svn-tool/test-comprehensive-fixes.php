<?php
/**
 * Comprehensive test script to verify all SVN tool fixes
 */

// Initialize session and constants
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

define('SVN_TOOL_ROOT', __DIR__);
define('SVN_TOOL_ACCESS', true);

// Include required files
require_once 'lib/helpers.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'api-utilities.php';
require_once 'api-functions.php';

echo "=== Comprehensive SVN Tool Fixes Test ===\n\n";

// Test 1: JavaScript Duplicate Loading Fix
echo "1. Testing JavaScript duplicate loading fix...\n";

$duplicateLoadingFixed = true;
$jsLoadCount = 0;

// Check index.php for script loading
if (file_exists('index.php')) {
    $indexContent = file_get_contents('index.php');
    $jsLoadCount += substr_count($indexContent, 'svn-tool.js');
    $jsLoadCount += substr_count($indexContent, 'app.js');
    echo "   - index.php script loads: " . substr_count($indexContent, '.js') . "\n";
}

// Check layout.php for script loading
if (file_exists('views/layout.php')) {
    $layoutContent = file_get_contents('views/layout.php');
    $layoutJsCount = substr_count($layoutContent, '.js');
    $jsLoadCount += $layoutJsCount;
    echo "   - layout.php script loads: {$layoutJsCount}\n";
}

// Check main.php for script loading
if (file_exists('views/main.php')) {
    $mainContent = file_get_contents('views/main.php');
    $mainJsCount = substr_count($mainContent, '.js');
    $jsLoadCount += $mainJsCount;
    echo "   - main.php script loads: {$mainJsCount}\n";
}

if ($jsLoadCount <= 1) {
    echo "   ✓ JavaScript duplicate loading issue FIXED\n";
} else {
    echo "   ✗ JavaScript still loaded multiple times ({$jsLoadCount} total)\n";
    $duplicateLoadingFixed = false;
}

// Test 2: Enhanced SVN Error Handling
echo "\n2. Testing enhanced SVN error handling...\n";

$errorHandlingTests = array(
    'readonly database' => false,
    'E200031' => false,
    'E155004' => false,
    'Authentication' => false,
    'E170013' => false,
    '--non-interactive' => false,
    'chmod 664' => false,
    'svn cleanup' => false
);

if (file_exists('api-utilities.php')) {
    $utilitiesContent = file_get_contents('api-utilities.php');
    
    foreach ($errorHandlingTests as $pattern => $found) {
        if (strpos($utilitiesContent, $pattern) !== false) {
            $errorHandlingTests[$pattern] = true;
            echo "   ✓ {$pattern} error handling found\n";
        } else {
            echo "   ✗ {$pattern} error handling missing\n";
        }
    }
}

$errorHandlingFixed = !in_array(false, $errorHandlingTests);

// Test 3: New API Endpoints
echo "\n3. Testing new API endpoints...\n";

$newEndpoints = array(
    'fix_permissions' => false,
    'check_connectivity' => false
);

if (file_exists('api.php')) {
    $apiContent = file_get_contents('api.php');
    
    foreach ($newEndpoints as $endpoint => $found) {
        if (strpos($apiContent, "'{$endpoint}'") !== false) {
            $newEndpoints[$endpoint] = true;
            echo "   ✓ {$endpoint} endpoint found\n";
        } else {
            echo "   ✗ {$endpoint} endpoint missing\n";
        }
    }
}

$endpointsAdded = !in_array(false, $newEndpoints);

// Test 4: New Functions
echo "\n4. Testing new utility functions...\n";

$newFunctions = array(
    'fixSvnPermissions' => function_exists('fixSvnPermissions'),
    'checkSvnConnectivity' => function_exists('checkSvnConnectivity'),
    'handleFixPermissions' => function_exists('handleFixPermissions'),
    'handleCheckConnectivity' => function_exists('handleCheckConnectivity')
);

foreach ($newFunctions as $func => $exists) {
    if ($exists) {
        echo "   ✓ {$func}() function exists\n";
    } else {
        echo "   ✗ {$func}() function missing\n";
    }
}

$functionsAdded = !in_array(false, $newFunctions);

// Test 5: Interface Updates
echo "\n5. Testing interface updates...\n";

$interfaceUpdates = array(
    'fix-permissions-btn' => false,
    'check-connectivity-btn' => false,
    'button-info' => false
);

if (file_exists('index.php')) {
    $indexContent = file_get_contents('index.php');
    
    if (strpos($indexContent, 'fix-permissions-btn') !== false) {
        $interfaceUpdates['fix-permissions-btn'] = true;
        echo "   ✓ Fix permissions button found\n";
    } else {
        echo "   ✗ Fix permissions button missing\n";
    }
    
    if (strpos($indexContent, 'check-connectivity-btn') !== false) {
        $interfaceUpdates['check-connectivity-btn'] = true;
        echo "   ✓ Check connectivity button found\n";
    } else {
        echo "   ✗ Check connectivity button missing\n";
    }
}

if (file_exists('assets/css/svn-tool.css')) {
    $cssContent = file_get_contents('assets/css/svn-tool.css');
    
    if (strpos($cssContent, 'button-info') !== false) {
        $interfaceUpdates['button-info'] = true;
        echo "   ✓ Button-info CSS style found\n";
    } else {
        echo "   ✗ Button-info CSS style missing\n";
    }
}

$interfaceUpdated = !in_array(false, $interfaceUpdates);

// Test 6: JavaScript Function Updates
echo "\n6. Testing JavaScript function updates...\n";

$jsFunctionUpdates = array(
    'fixPermissions' => false,
    'checkConnectivity' => false
);

if (file_exists('assets/js/svn-tool.js')) {
    $jsContent = file_get_contents('assets/js/svn-tool.js');
    
    foreach ($jsFunctionUpdates as $func => $found) {
        if (strpos($jsContent, $func . ':') !== false) {
            $jsFunctionUpdates[$func] = true;
            echo "   ✓ {$func}() JavaScript function found\n";
        } else {
            echo "   ✗ {$func}() JavaScript function missing\n";
        }
    }
}

$jsFunctionsUpdated = !in_array(false, $jsFunctionUpdates);

// Summary
echo "\n=== SUMMARY ===\n";
echo "1. JavaScript duplicate loading fix: " . ($duplicateLoadingFixed ? "✅ FIXED" : "❌ NEEDS WORK") . "\n";
echo "2. Enhanced SVN error handling: " . ($errorHandlingFixed ? "✅ IMPLEMENTED" : "❌ INCOMPLETE") . "\n";
echo "3. New API endpoints: " . ($endpointsAdded ? "✅ ADDED" : "❌ MISSING") . "\n";
echo "4. New utility functions: " . ($functionsAdded ? "✅ IMPLEMENTED" : "❌ INCOMPLETE") . "\n";
echo "5. Interface updates: " . ($interfaceUpdated ? "✅ UPDATED" : "❌ INCOMPLETE") . "\n";
echo "6. JavaScript functions: " . ($jsFunctionsUpdated ? "✅ UPDATED" : "❌ INCOMPLETE") . "\n";

$allFixed = $duplicateLoadingFixed && $errorHandlingFixed && $endpointsAdded && 
            $functionsAdded && $interfaceUpdated && $jsFunctionsUpdated;

echo "\nOverall status: " . ($allFixed ? "🎉 ALL FIXES IMPLEMENTED" : "⚠️  SOME ISSUES REMAIN") . "\n";

if (!$allFixed) {
    echo "\nNext steps:\n";
    if (!$duplicateLoadingFixed) echo "- Fix JavaScript duplicate loading\n";
    if (!$errorHandlingFixed) echo "- Complete SVN error handling implementation\n";
    if (!$endpointsAdded) echo "- Add missing API endpoints\n";
    if (!$functionsAdded) echo "- Implement missing utility functions\n";
    if (!$interfaceUpdated) echo "- Complete interface updates\n";
    if (!$jsFunctionsUpdated) echo "- Add missing JavaScript functions\n";
}

echo "\n=== Test Complete ===\n";
?>
