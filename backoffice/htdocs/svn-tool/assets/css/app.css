/* Modern CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --success-color: #059669;
    --warning-color: #d97706;
    --error-color: #dc2626;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --border-radius: 8px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--gray-900);
    background-color: var(--gray-50);
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    padding: 2rem 0;
    box-shadow: var(--shadow-md);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.header p {
    font-size: 1.125rem;
    opacity: 0.9;
}

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.card-body {
    padding: 1.5rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    gap: 0.5rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-hover);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--error-color);
    color: white;
}

.btn-secondary {
    background: var(--gray-200);
    color: var(--gray-700);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--gray-300);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-loading {
    position: relative;
    color: transparent;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Grid Layout */
.grid {
    display: grid;
    gap: 2rem;
}

.grid-cols-1 {
    grid-template-columns: 1fr;
}

.grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
}

@media (max-width: 768px) {
    .grid-cols-2,
    .grid-cols-3 {
        grid-template-columns: 1fr;
    }
}

/* Alert Styles */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid;
}

.alert-info {
    background: #dbeafe;
    border-color: #93c5fd;
    color: #1e40af;
}

.alert-success {
    background: #d1fae5;
    border-color: #6ee7b7;
    color: #065f46;
}

.alert-warning {
    background: #fef3c7;
    border-color: #fcd34d;
    color: #92400e;
}

.alert-error {
    background: #fee2e2;
    border-color: #fca5a5;
    color: #991b1b;
}

/* Repository List */
.repo-list {
    display: grid;
    gap: 0.5rem;
}

.repo-item {
    padding: 1rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s;
    background: white;
}

.repo-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.repo-item.active {
    border-color: var(--primary-color);
    background: #eff6ff;
}

/* File List */
.file-list {
    display: grid;
    gap: 0.5rem;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    background: white;
    gap: 1rem;
}

.file-checkbox {
    width: 18px;
    height: 18px;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: var(--gray-900);
}

.file-path {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin-top: 0.25rem;
}

.file-status {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-modified {
    background: #fef3c7;
    color: #92400e;
}

.status-added {
    background: #d1fae5;
    color: #065f46;
}

.status-deleted {
    background: #fee2e2;
    color: #991b1b;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
}

/* Diff Viewer */
.diff-viewer {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    overflow: hidden;
    background: white;
}

.diff-header {
    padding: 1rem;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.diff-filename {
    font-weight: 600;
    color: var(--gray-900);
}

.diff-stats {
    display: flex;
    gap: 1rem;
}

.diff-stats-added {
    color: var(--success-color);
    font-weight: 500;
}

.diff-stats-removed {
    color: var(--error-color);
    font-weight: 500;
}

.diff-content {
    max-height: 500px;
    overflow-y: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

.diff-line {
    padding: 0.25rem 1rem;
    white-space: pre-wrap;
    word-break: break-all;
}

.diff-added {
    background: #f0fdf4;
    color: #166534;
}

.diff-removed {
    background: #fef2f2;
    color: #991b1b;
}

.diff-context {
    background: white;
    color: var(--gray-700);
}

.diff-file-header {
    background: var(--gray-100);
    color: var(--gray-600);
    font-weight: 600;
}

.diff-hunk-header {
    background: #eff6ff;
    color: var(--primary-color);
    font-weight: 500;
}

.diff-empty {
    padding: 2rem;
    text-align: center;
    color: var(--gray-500);
    font-style: italic;
}

.diff-placeholder {
    padding: 3rem;
    text-align: center;
    color: var(--gray-400);
    background: var(--gray-50);
    border: 2px dashed var(--gray-200);
    border-radius: var(--border-radius);
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
    max-width: 400px;
}

.notification {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    margin-bottom: 0.5rem;
    overflow: hidden;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification-header {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-title {
    font-weight: 600;
    color: var(--gray-900);
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--gray-400);
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-body {
    padding: 0 1rem 1rem;
    color: var(--gray-600);
}

.notification-actions {
    padding: 0 1rem 1rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.notification.success .notification-header {
    background: var(--success-color);
    color: white;
}

.notification.error .notification-header {
    background: var(--error-color);
    color: white;
}

.notification.warning .notification-header {
    background: var(--warning-color);
    color: white;
}

.notification.info .notification-header {
    background: var(--primary-color);
    color: white;
}

/* Utility Classes */
.hidden { display: none !important; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-gray-500 { color: var(--gray-500); }
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.relative { position: relative; }

/* Responsive Design */
@media (max-width: 768px) {
    .header h1 {
        font-size: 2rem;
    }
    
    .container {
        padding: 1rem;
    }
    
    .card-header,
    .card-body {
        padding: 1rem;
    }
    
    .notification-container {
        left: 1rem;
        right: 1rem;
        max-width: none;
    }
    
    .diff-content {
        font-size: 0.75rem;
    }
}

/* Add debugging styles and loading states */
.btn-loading {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-loading::after {
    content: " ⟳";
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.repo-item {
    padding: 12px;
    border: 1px solid #e5e7eb;
    margin-bottom: 8px;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.repo-item:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
}

.repo-item.active {
    background-color: #eff6ff;
    border-color: #3b82f6;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #f3f4f6;
}

.file-checkbox {
    margin-right: 12px;
}

.file-info {
    flex: 1;
    cursor: pointer;
}

.file-name {
    font-weight: 500;
    color: #111827;
}

.file-status {
    font-size: 0.875rem;
    padding: 2px 8px;
    border-radius: 4px;
    margin-top: 4px;
    display: inline-block;
}

.status-modified {
    background-color: #fef3c7;
    color: #92400e;
}

.status-added {
    background-color: #d1fae5;
    color: #065f46;
}

.status-deleted {
    background-color: #fee2e2;
    color: #991b1b;
}

.text-red-500 {
    color: #ef4444;
}

.text-gray-500 {
    color: #6b7280;
}

.text-center {
    text-align: center;
}

/* Notification styles */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
}

.notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 12px;
    overflow: hidden;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    border-left: 4px solid #10b981;
}

.notification.error {
    border-left: 4px solid #ef4444;
}

.notification.warning {
    border-left: 4px solid #f59e0b;
}

.notification.info {
    border-left: 4px solid #3b82f6;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px 8px;
}

.notification-title {
    font-weight: 600;
    color: #111827;
}

.notification-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    color: #374151;
}

.notification-body {
    padding: 0 16px 12px;
    color: #4b5563;
}

.notification-actions {
    padding: 8px 16px 12px;
    display: flex;
    gap: 8px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}
