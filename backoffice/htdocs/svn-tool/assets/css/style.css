* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.header {
    background: #fff;
    border-bottom: 1px solid #e1e5e9;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.main-content {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.loading {
    text-align: center;
    padding: 3rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    background: #e74c3c;
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    margin-bottom: 2rem;
}

.error-content h3 {
    margin-bottom: 0.5rem;
}

.repositories-container {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.repositories-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e1e5e9;
}

.repositories-header h2 {
    color: #2c3e50;
    font-size: 1.25rem;
}

.repositories-stats {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.repositories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.repository-card {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 1rem;
    background: #fafafa;
    transition: all 0.2s;
}

.repository-card:hover {
    border-color: #3498db;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.repo-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.repo-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
}

.repo-info h3 {
    color: #2c3e50;
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.repo-type {
    color: #7f8c8d;
    font-size: 0.8rem;
    text-transform: uppercase;
    font-weight: 500;
}

.repo-details {
    margin-bottom: 1rem;
}

.repo-path {
    color: #7f8c8d;
    font-size: 0.8rem;
    font-family: monospace;
    margin-bottom: 0.25rem;
    word-break: break-all;
}

.repo-modified {
    color: #95a5a6;
    font-size: 0.8rem;
}

.repo-actions {
    text-align: right;
}

.no-repositories {
    text-align: center;
    color: #7f8c8d;
    padding: 3rem;
    font-style: italic;
}

.repository-details {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.details-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e1e5e9;
}

.details-header h2 {
    color: #2c3e50;
    font-size: 1.25rem;
}

.files-container {
    margin-top: 1rem;
}

.files-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.files-header h3 {
    color: #2c3e50;
    font-size: 1.1rem;
}

.files-stats {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.files-list {
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    overflow: hidden;
}

.file-item {
    padding: 0.75rem;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
}

.file-item:last-child {
    border-bottom: none;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    width: 100%;
}

.file-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 100px;
}

.status-icon {
    font-size: 1rem;
}

.status-text {
    font-size: 0.8rem;
    text-transform: uppercase;
    font-weight: 500;
}

.status-modified { color: #f39c12; }
.status-added { color: #27ae60; }
.status-deleted { color: #e74c3c; }
.status-conflicted { color: #e67e22; }
.status-unversioned { color: #95a5a6; }
.status-missing { color: #c0392b; }

.file-details h4 {
    color: #2c3e50;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.file-path {
    color: #7f8c8d;
    font-size: 0.8rem;
    font-family: monospace;
}

.no-files {
    text-align: center;
    color: #7f8c8d;
    padding: 2rem;
    font-style: italic;
}

@media (max-width: 768px) {
    .header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .repositories-grid {
        grid-template-columns: 1fr;
    }
    
    .repositories-header,
    .files-header,
    .details-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}