/* Modern SVN Web Client - Professional Design System */
:root {
    /* Primary Colors */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-900: #1e3a8a;
    
    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Semantic Colors */
    --success-50: #ecfdf5;
    --success-500: #10b981;
    --success-600: #059669;
    --warning-50: #fffbeb;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --error-50: #fef2f2;
    --error-500: #ef4444;
    --error-600: #dc2626;
    
    /* Typography */
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-12: 3rem;
    --space-16: 4rem;
    
    /* Borders & Shadows */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset & Base Styles */
*, *::before, *::after {
    box-sizing: border-box;
}

* {
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-sans);
    color: var(--gray-900);
    background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 100%);
    min-height: 100vh;
}

/* Layout */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-6);
    width: 100%;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    color: white;
    padding: var(--space-6) 0;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.app-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.app-title {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: 1.875rem;
    font-weight: 700;
    margin: 0;
}

.app-title::before {
    content: '⚡';
    font-size: 2rem;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.version-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

/* Cards */
.card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;
}

.card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-6);
    position: relative;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.card-body {
    padding: var(--space-6);
}

.card-actions {
    display: flex;
    gap: var(--space-2);
    align-items: center;
}

/* Grid System */
.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }

@media (max-width: 1024px) {
    .grid-cols-3 { grid-template-columns: 1fr; }
    .grid-cols-2 { grid-template-columns: 1fr; }
}

@media (max-width: 768px) {
    .main-content { padding: var(--space-4); }
    .header-content { 
        flex-direction: column; 
        gap: var(--space-4); 
        text-align: center; 
    }
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-5);
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1;
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-success:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-secondary {
    background: white;
    color: var(--gray-700);
    border-color: var(--gray-300);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--gray-400);
    transform: translateY(-1px);
}

.btn-sm {
    padding: var(--space-2) var(--space-3);
    font-size: 0.75rem;
}

.btn-lg {
    padding: var(--space-4) var(--space-6);
    font-size: 1rem;
}

/* Loading States */
.btn.loading {
    color: transparent !important;
    pointer-events: none;
    position: relative;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    color: white;
}

.btn-secondary.loading::after {
    color: var(--gray-600);
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Forms */
.form-group {
    margin-bottom: var(--space-5);
}

.form-label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--space-2);
    font-size: 0.875rem;
}

.form-input, .form-textarea, .form-select {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    background: white;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
    font-family: var(--font-mono);
}

.form-help {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: var(--space-1);
}

/* Lists */
.list-container {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: white;
    max-height: 400px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.list-header {
    background: var(--gray-50);
    padding: var(--space-3) var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 500;
    font-size: 0.875rem;
    color: var(--gray-700);
}

.list-content {
    flex: 1;
    overflow-y: auto;
}

.list-item {
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-100);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.list-item:hover {
    background: var(--gray-50);
    transform: translateX(2px);
}

.list-item:last-child {
    border-bottom: none;
}

.list-item.active {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    border-left: 4px solid var(--primary-500);
    transform: translateX(4px);
}

.list-item.active:hover {
    background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-200) 100%);
}

.list-item-icon {
    font-size: 1.25rem;
    opacity: 0.7;
}

.list-item-content {
    flex: 1;
    min-width: 0;
}

.list-item-title {
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.list-item-subtitle {
    font-size: 0.75rem;
    color: var(--gray-500);
    word-break: break-all;
}

.list-empty {
    padding: var(--space-12);
    text-align: center;
    color: var(--gray-500);
}

.list-empty-icon {
    font-size: 3rem;
    margin-bottom: var(--space-4);
    opacity: 0.5;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-modified {
    background: var(--warning-50);
    color: var(--warning-600);
}

.status-added {
    background: var(--success-50);
    color: var(--success-600);
}

.status-deleted {
    background: var(--error-50);
    color: var(--error-600);
}

/* Modals */
.modal {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: var(--space-4);
    backdrop-filter: blur(4px);
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: modalSlideIn var(--transition-slow);
}

.modal-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-400);
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: var(--space-6);
    flex: 1;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: var(--space-3);
    background: var(--gray-50);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Notifications */
.notification-container {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    z-index: 1100;
    max-width: 400px;
    pointer-events: none;
}

.notification {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    margin-bottom: var(--space-3);
    overflow: hidden;
    animation: notificationSlideIn var(--transition-slow);
    border-left: 4px solid var(--gray-300);
    pointer-events: auto;
}

.notification.success { border-left-color: var(--success-500); }
.notification.error { border-left-color: var(--error-500); }
.notification.warning { border-left-color: var(--warning-500); }
.notification.info { border-left-color: var(--primary-500); }

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--space-4) var(--space-4) var(--space-2);
}

.notification-title {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.875rem;
}

.notification-close {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--gray-400);
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.notification-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.notification-body {
    padding: 0 var(--space-4) var(--space-4);
    color: var(--gray-600);
    font-size: 0.875rem;
    line-height: 1.5;
}

@keyframes notificationSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes notificationSlideOut {
    from {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateX(100%) scale(0.9);
    }
}

/* Progress Indicators */
.progress-bar {
    position: relative;
    overflow: hidden;
    background: var(--gray-200);
    border-radius: var(--radius-sm);
    height: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    transition: width var(--transition-normal);
    border-radius: var(--radius-sm);
}

.progress-indeterminate {
    background: linear-gradient(90deg, 
        transparent 0%, 
        var(--primary-500) 25%, 
        var(--primary-600) 50%, 
        var(--primary-500) 75%, 
        transparent 100%
    );
    background-size: 200% 100%;
    animation: progressSlide 2s infinite;
    width: 100% !important;
}

@keyframes progressSlide {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Alerts */
.alert {
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-6);
    border: 1px solid;
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
}

.alert-warning {
    background: var(--warning-50);
    border-color: var(--warning-200);
    color: var(--warning-800);
}

.alert-icon {
    font-size: 1.25rem;
    margin-top: 0.125rem;
}

/* Utilities */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-sm { font-size: 0.875rem; }
.text-xs { font-size: 0.75rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.hidden { display: none !important; }
.sr-only { 
    position: absolute; 
    width: 1px; 
    height: 1px; 
    padding: 0; 
    margin: -1px; 
    overflow: hidden; 
    clip: rect(0, 0, 0, 0); 
    white-space: nowrap; 
    border: 0; 
}

/* Spacing utilities */
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }

/* Debug styles for development */
.debug-info {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-family: monospace;
    font-size: 12px;
    z-index: 9999;
    max-width: 300px;
    word-wrap: break-word;
}
