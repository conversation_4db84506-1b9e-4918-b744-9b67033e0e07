/**
 * SVN Tool CSS - True Glass Morphism Design
 *
 * Clean, professional glass morphism interface with neutral colors
 * Workflow-oriented design with proper spacing and visual hierarchy
 * PHP 5.6 Compatible
 */

/* CSS Variables - True Glass Morphism Design System */
:root {
    /* Neutral Glass Palette */
    --glass-white: rgba(255, 255, 255, 0.25);
    --glass-white-strong: rgba(255, 255, 255, 0.4);
    --glass-white-subtle: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-border-strong: rgba(255, 255, 255, 0.3);

    /* Background System */
    --bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --bg-glass: rgba(255, 255, 255, 0.25);
    --bg-glass-hover: rgba(255, 255, 255, 0.35);
    --bg-glass-active: rgba(255, 255, 255, 0.45);

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-white: #ffffff;

    /* Accent Colors (Minimal Usage) */
    --accent-primary: #3b82f6;
    --accent-success: #10b981;
    --accent-warning: #f59e0b;
    --accent-danger: #ef4444;

    /* Glass Effects */
    --blur-sm: blur(8px);
    --blur-md: blur(16px);
    --blur-lg: blur(24px);

    /* Shadows */
    --shadow-glass: 0 8px 32px rgba(31, 38, 135, 0.15);
    --shadow-glass-hover: 0 16px 40px rgba(31, 38, 135, 0.2);
    --shadow-subtle: 0 4px 16px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 8px 24px rgba(0, 0, 0, 0.08);

    /* Spacing System */
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;
    --space-8: 32px;
    --space-10: 40px;
    --space-12: 48px;
    --space-16: 64px;

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;

    /* Transitions */
    --transition-fast: all 0.15s ease-out;
    --transition-normal: all 0.25s ease-out;
    --transition-slow: all 0.35s ease-out;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    color: var(--text-primary);
    line-height: 1.6;
    font-weight: 400;
}

/* Layout System - Workflow Oriented */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-6);
    position: relative;
}

/* Workflow Steps Layout */
.workflow-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-8);
}

.workflow-step {
    position: relative;
}

.workflow-step:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: calc(-1 * var(--space-8) / 2);
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: var(--space-4);
    background: linear-gradient(180deg, var(--glass-border), transparent);
}

/* Grid System for Multi-Column Layouts */
.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }

/* Glass Morphism Cards */
.card {
    background: var(--bg-glass);
    backdrop-filter: var(--blur-md);
    -webkit-backdrop-filter: var(--blur-md);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-glass);
    overflow: hidden;
    transition: var(--transition-normal);
    position: relative;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-glass-hover);
    background: var(--bg-glass-hover);
}

/* Card Headers - Clean and Minimal */
.card-header {
    padding: var(--space-6);
    background: var(--glass-white-subtle);
    border-bottom: 1px solid var(--glass-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.card-header h2,
.card-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.card-body {
    padding: var(--space-6);
    background: transparent;
}

/* Step Indicators */
.step-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--glass-white-strong);
    border: 1px solid var(--glass-border-strong);
    border-radius: 50%;
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
    backdrop-filter: var(--blur-sm);
    -webkit-backdrop-filter: var(--blur-sm);
}

.step-title {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Glass Morphism Button System */
.button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    font-family: 'Montserrat', sans-serif;
    font-weight: 500;
    font-size: 0.875rem;
    line-height: 1.5;
    text-decoration: none;
    cursor: pointer;
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    padding: var(--space-3) var(--space-5);
    transition: var(--transition-normal);
    position: relative;
    backdrop-filter: var(--blur-sm);
    -webkit-backdrop-filter: var(--blur-sm);
    white-space: nowrap;
}

.button:hover {
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.button:active {
    transform: translateY(0);
}

/* Primary Button - Minimal Accent */
.button-primary {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    color: var(--text-white);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.25);
}

.button-primary:hover {
    background: #2563eb;
    border-color: #2563eb;
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.35);
}

/* Secondary Button - Pure Glass */
.button-secondary {
    background: var(--bg-glass);
    border-color: var(--glass-border-strong);
    color: var(--text-primary);
}

.button-secondary:hover {
    background: var(--bg-glass-hover);
    border-color: var(--glass-border-strong);
}

/* Success Button */
.button-success {
    background: var(--accent-success);
    border-color: var(--accent-success);
    color: var(--text-white);
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.25);
}

.button-success:hover {
    background: #059669;
    border-color: #059669;
    box-shadow: 0 8px 24px rgba(16, 185, 129, 0.35);
}

/* Warning Button */
.button-warning {
    background: var(--accent-warning);
    border-color: var(--accent-warning);
    color: var(--text-white);
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.25);
}

.button-warning:hover {
    background: #d97706;
    border-color: #d97706;
    box-shadow: 0 8px 24px rgba(245, 158, 11, 0.35);
}

/* Info Button */
.button-info {
    background: #3b82f6;
    border-color: #3b82f6;
    color: var(--text-white);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.25);
}

.button-info:hover {
    background: #2563eb;
    border-color: #2563eb;
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.35);
}

/* Danger Button */
.button-danger {
    background: var(--accent-danger);
    border-color: var(--accent-danger);
    color: var(--text-white);
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.25);
}

.button-danger:hover {
    background: #dc2626;
    border-color: #dc2626;
    box-shadow: 0 8px 24px rgba(239, 68, 68, 0.35);
}

/* Button Sizes */
.button-sm {
    padding: var(--space-2) var(--space-3);
    font-size: 0.75rem;
    border-radius: var(--radius-sm);
}

.button-lg {
    padding: var(--space-4) var(--space-6);
    font-size: 1rem;
    border-radius: var(--radius-lg);
}

/* Button Groups */
.button-group {
    display: flex;
    gap: var(--space-3);
    flex-wrap: wrap;
    align-items: center;
}

.button-group-vertical {
    flex-direction: column;
    align-items: stretch;
}

.button-group-vertical .button {
    width: 100%;
}

/* Disabled State */
.button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    background: var(--glass-white-subtle);
    color: var(--text-muted);
    border-color: var(--glass-border);
    box-shadow: none;
}

.button:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Glass Morphism Form Elements */
.form-group {
    margin-bottom: var(--space-6);
    position: relative;
}

.form-control {
    display: block;
    width: 100%;
    min-height: 48px;
    padding: var(--space-3) var(--space-4);
    font-family: 'Montserrat', sans-serif;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--text-primary);
    background: var(--bg-glass);
    backdrop-filter: var(--blur-sm);
    -webkit-backdrop-filter: var(--blur-sm);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-subtle);
}

.form-control:focus {
    outline: none;
    background: var(--bg-glass-hover);
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-medium);
    transform: translateY(-1px);
}

.form-control:hover:not(:focus) {
    background: var(--bg-glass-hover);
    border-color: var(--glass-border-strong);
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 1;
}

/* Enhanced Labels */
label {
    display: block;
    margin-bottom: var(--space-2);
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
    letter-spacing: -0.025em;
}

.form-text {
    display: block;
    margin-top: var(--space-2);
    font-size: 0.75rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* Textarea Specific */
textarea.form-control {
    min-height: 96px;
    resize: vertical;
    font-family: inherit;
}

/* Select Elements */
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2394a3b8' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-3) center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: var(--space-10);
    cursor: pointer;
}

/* Input Groups */
.input-group {
    display: flex;
    align-items: stretch;
    gap: var(--space-2);
}

.input-group .form-control {
    flex: 1;
}

.input-group .button {
    border-radius: var(--radius-md);
}

/* Glass Morphism List Groups */
.list-group {
    display: flex;
    flex-direction: column;
    padding: 0;
    margin: 0;
    border-radius: var(--radius-md);
    overflow: hidden;
    background: var(--glass-white-subtle);
    backdrop-filter: var(--blur-sm);
    -webkit-backdrop-filter: var(--blur-sm);
    border: 1px solid var(--glass-border);
}

.list-group-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) var(--space-5);
    background: transparent;
    border: none;
    border-bottom: 1px solid var(--glass-border);
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: 0.875rem;
    color: var(--text-primary);
    min-height: 56px;
}

.list-group-item:last-child {
    border-bottom: none;
}

.list-group-item:hover {
    background: var(--bg-glass-hover);
    transform: translateX(2px);
    box-shadow: var(--shadow-subtle);
}

.list-group-item.active {
    background: var(--bg-glass-active);
    border-left: 3px solid var(--accent-primary);
    padding-left: calc(var(--space-5) - 3px);
    box-shadow: var(--shadow-medium);
}

.list-group-item.active:hover {
    background: var(--bg-glass-active);
}

/* List Item Content */
.list-item-content {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    flex: 1;
}

.list-item-actions {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.file-checkbox {
    width: 18px;
    height: 18px;
    accent-color: var(--accent-primary);
    cursor: pointer;
}

/* Empty State */
.list-group-empty {
    padding: var(--space-8);
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    background: var(--glass-white-subtle);
    border-radius: var(--radius-md);
}

/* Glass Morphism Alerts */
.alert {
    position: relative;
    padding: var(--space-4) var(--space-5);
    margin-bottom: var(--space-6);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    background: var(--bg-glass);
    backdrop-filter: var(--blur-sm);
    -webkit-backdrop-filter: var(--blur-sm);
    box-shadow: var(--shadow-subtle);
    font-weight: 500;
}

.alert-warning {
    border-left: 4px solid var(--accent-warning);
    background: rgba(245, 158, 11, 0.05);
}

.alert-info {
    border-left: 4px solid var(--accent-primary);
    background: rgba(59, 130, 246, 0.05);
}

.alert-success {
    border-left: 4px solid var(--accent-success);
    background: rgba(16, 185, 129, 0.05);
}

.alert-danger {
    border-left: 4px solid var(--accent-danger);
    background: rgba(239, 68, 68, 0.05);
}

/* Minimal Status Badges */
.badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-2);
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    border-radius: var(--radius-sm);
    background: var(--bg-glass);
    border: 1px solid var(--glass-border);
    backdrop-filter: var(--blur-sm);
    -webkit-backdrop-filter: var(--blur-sm);
    transition: var(--transition-fast);
}

.badge-success {
    color: var(--accent-success);
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
}

.status-modified {
    color: var(--accent-warning);
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
}

.status-added {
    color: var(--accent-success);
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
}

.status-deleted {
    color: var(--accent-danger);
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
}

.status-conflicted {
    color: #ea580c;
    background: rgba(234, 88, 12, 0.1);
    border-color: rgba(234, 88, 12, 0.2);
}

.status-unversioned {
    color: var(--text-muted);
    background: rgba(148, 163, 184, 0.1);
    border-color: rgba(148, 163, 184, 0.2);
}

/* Glass Morphism Modal System */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: rgba(30, 41, 59, 0.4);
    backdrop-filter: var(--blur-md);
    -webkit-backdrop-filter: var(--blur-md);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.modal[style*="block"] {
    opacity: 1;
    visibility: visible;
}

.modal-dialog {
    position: relative;
    width: 90%;
    max-width: 600px;
    margin: var(--space-6);
    pointer-events: auto;
    transform: scale(0.95) translateY(20px);
    transition: var(--transition-normal);
}

.modal[style*="block"] .modal-dialog {
    transform: scale(1) translateY(0);
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    background: var(--bg-glass-strong);
    backdrop-filter: var(--blur-lg);
    -webkit-backdrop-filter: var(--blur-lg);
    border: 1px solid var(--glass-border-strong);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-glass-hover);
    overflow: hidden;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-6);
    background: var(--glass-white-subtle);
    border-bottom: 1px solid var(--glass-border);
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.modal-body {
    position: relative;
    flex: 1;
    padding: var(--space-6);
    background: transparent;
}

.modal-footer {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    gap: var(--space-3);
    padding: var(--space-6);
    background: var(--glass-white-subtle);
    border-top: 1px solid var(--glass-border);
}

.close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    font-size: 1.125rem;
    font-weight: 400;
    line-height: 1;
    color: var(--text-secondary);
    background: var(--bg-glass);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition-fast);
    backdrop-filter: var(--blur-sm);
    -webkit-backdrop-filter: var(--blur-sm);
}

.close:hover {
    background: var(--bg-glass-hover);
    color: var(--text-primary);
    transform: scale(1.05);
}

/* Glass Morphism Progress Bar */
.progress {
    display: flex;
    height: 8px;
    overflow: hidden;
    background: var(--glass-white-subtle);
    border-radius: var(--radius-sm);
    backdrop-filter: var(--blur-sm);
    -webkit-backdrop-filter: var(--blur-sm);
    border: 1px solid var(--glass-border);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    color: var(--text-white);
    text-align: center;
    white-space: nowrap;
    background: var(--accent-primary);
    border-radius: var(--radius-sm);
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
    border-radius: var(--radius-sm) var(--radius-sm) 0 0;
}

.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.1) 75%, transparent 75%, transparent);
    background-size: 16px 16px;
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 16px 0;
    }
    100% {
        background-position: 0 0;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-muted {
    color: var(--text-muted);
}

.text-secondary {
    color: var(--text-secondary);
}

.d-none {
    display: none;
}

.d-block {
    display: block;
}

.d-flex {
    display: flex;
}

.d-grid {
    display: grid;
}

/* Glass Morphism Notification System */
#notification-container {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    z-index: 1060;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.notification {
    padding: var(--space-4) var(--space-5);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    background: var(--bg-glass-strong);
    backdrop-filter: var(--blur-md);
    -webkit-backdrop-filter: var(--blur-md);
    box-shadow: var(--shadow-glass);
    font-weight: 500;
    position: relative;
    overflow: hidden;
    transform: translateX(100%);
    opacity: 0;
    animation: slideInNotification 0.3s ease-out forwards;
}

@keyframes slideInNotification {
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: currentColor;
}

.notification-success {
    color: var(--accent-success);
    border-left-color: var(--accent-success);
}

.notification-error {
    color: var(--accent-danger);
    border-left-color: var(--accent-danger);
}

.notification-warning {
    color: var(--accent-warning);
    border-left-color: var(--accent-warning);
}

.notification-info {
    color: var(--accent-primary);
    border-left-color: var(--accent-primary);
}

/* Glass Morphism Diff Viewer */
.diff-content {
    background: var(--bg-glass);
    backdrop-filter: var(--blur-sm);
    -webkit-backdrop-filter: var(--blur-sm);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    padding: var(--space-5);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    overflow-x: auto;
    white-space: pre;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.diff-file {
    color: var(--accent-primary);
    font-weight: 600;
}

.diff-hunk {
    color: var(--text-secondary);
    background: var(--glass-white-subtle);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.diff-added {
    color: var(--accent-success);
    background: rgba(16, 185, 129, 0.1);
    padding: 1px var(--space-1);
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.diff-removed {
    color: var(--accent-danger);
    background: rgba(239, 68, 68, 0.1);
    padding: 1px var(--space-1);
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.diff-context {
    color: var(--text-muted);
}

.no-changes,
.loading,
.error {
    text-align: center;
    padding: var(--space-12);
    background: var(--glass-white-subtle);
    border-radius: var(--radius-md);
    border: 2px dashed var(--glass-border);
}

.no-changes {
    color: var(--text-muted);
    font-style: italic;
}

.loading {
    color: var(--accent-primary);
    font-weight: 500;
}

.error {
    color: var(--accent-danger);
    font-weight: 500;
}

/* Glass Morphism File Items */
.file-item {
    border: 1px solid var(--glass-border);
    margin-bottom: var(--space-3);
    border-radius: var(--radius-md);
    background: var(--bg-glass);
    backdrop-filter: var(--blur-sm);
    -webkit-backdrop-filter: var(--blur-sm);
    overflow: hidden;
    transition: var(--transition-normal);
}

.file-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
    background: var(--bg-glass-hover);
}

.file-header {
    padding: var(--space-4);
    cursor: pointer;
    background: transparent;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.file-header:hover {
    background: var(--glass-white-subtle);
}

.file-details {
    padding: var(--space-3) var(--space-4);
    background: var(--glass-white-subtle);
    border-top: 1px solid var(--glass-border);
}

.btn-diff {
    background: var(--accent-primary);
    color: var(--text-white);
    border: none;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
}

.btn-diff:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35);
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .grid-cols-3 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .container {
        padding: var(--space-4);
    }

    .workflow-container {
        gap: var(--space-6);
    }

    .grid-cols-2,
    .grid-cols-3 {
        grid-template-columns: 1fr;
    }

    .modal-dialog {
        margin: var(--space-3);
        width: calc(100% - 2 * var(--space-3));
    }

    .card-header {
        padding: var(--space-4);
        flex-direction: column;
        gap: var(--space-3);
        align-items: flex-start;
    }

    .card-body {
        padding: var(--space-4);
    }

    .button-group {
        flex-direction: column;
        align-items: stretch;
    }

    .button-group .button {
        width: 100%;
    }

    #notification-container {
        top: var(--space-3);
        right: var(--space-3);
        left: var(--space-3);
        max-width: none;
    }

    .step-indicator {
        justify-content: center;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: var(--space-3);
    }

    .workflow-container {
        gap: var(--space-4);
    }

    .card-header h2,
    .card-header h3 {
        font-size: 1rem;
    }

    .step-number {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }

    .step-title {
        font-size: 0.75rem;
    }
}

/* Additional Glass Morphism Enhancements */

/* Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, var(--glass-white-subtle) 25%, var(--glass-white) 50%, var(--glass-white-subtle) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Enhanced Focus States */
.button:focus,
.form-control:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* Smooth Scrollbars */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--glass-white-subtle);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--glass-border-strong);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Enhanced Card Animations */
.card {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(16px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary);
    font-weight: 600;
    line-height: 1.3;
    letter-spacing: -0.025em;
}

/* Diff Viewer Enhancements */
#diff-viewer {
    border-radius: var(--radius-md);
    overflow: hidden;
}

#diff-header {
    background: var(--glass-white-subtle);
    padding: var(--space-4);
    border-bottom: 1px solid var(--glass-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#diff-filename {
    font-weight: 600;
    color: var(--text-primary);
    font-family: 'SF Mono', 'Monaco', monospace;
    font-size: 0.875rem;
}

#diff-stats {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

#diff-placeholder {
    padding: var(--space-12);
    background: var(--glass-white-subtle);
    border-radius: var(--radius-md);
    border: 2px dashed var(--glass-border);
    text-align: center;
}

#diff-placeholder p {
    margin: 0 0 var(--space-2) 0;
    color: var(--text-secondary);
    font-weight: 500;
}

#diff-placeholder small {
    color: var(--text-muted);
}

/* Improved Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* SVN Authentication Modal Styles */
.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1060;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: rgba(30, 41, 59, 0.5);
    backdrop-filter: var(--blur-lg);
    -webkit-backdrop-filter: var(--blur-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.auth-modal.show {
    opacity: 1;
    visibility: visible;
}

.auth-modal-dialog {
    position: relative;
    width: 90%;
    max-width: 500px;
    margin: var(--space-6);
    pointer-events: auto;
    transform: scale(0.9) translateY(30px);
    transition: var(--transition-normal);
}

.auth-modal.show .auth-modal-dialog {
    transform: scale(1) translateY(0);
}

.auth-modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    background: var(--glass-white-strong);
    backdrop-filter: var(--blur-lg);
    -webkit-backdrop-filter: var(--blur-lg);
    border: 1px solid var(--glass-border-strong);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-glass-hover);
    overflow: hidden;
}

.auth-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-6);
    background: var(--glass-white-subtle);
    border-bottom: 1px solid var(--glass-border);
}

.auth-modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: -0.025em;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.auth-modal-body {
    position: relative;
    flex: 1;
    padding: var(--space-6);
    background: transparent;
}

.auth-modal-footer {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    gap: var(--space-3);
    padding: var(--space-6);
    background: var(--glass-white-subtle);
    border-top: 1px solid var(--glass-border);
}

/* Authentication Form Styles */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-5);
}

.auth-form .form-group {
    margin-bottom: 0;
}

.auth-form .form-control {
    background: var(--glass-white);
    border: 1px solid var(--glass-border-strong);
}

.auth-form .form-control:focus {
    background: var(--glass-white-strong);
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15), var(--shadow-medium);
}

.auth-error {
    padding: var(--space-3) var(--space-4);
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: var(--radius-md);
    color: var(--accent-danger);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: var(--space-4);
    display: none;
}

.auth-error.show {
    display: block;
}

.auth-info {
    padding: var(--space-3) var(--space-4);
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--radius-md);
    color: var(--accent-primary);
    font-size: 0.875rem;
    margin-bottom: var(--space-4);
}

/* Credential Status Indicator */
.credential-status {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: var(--glass-white-subtle);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: var(--space-2);
}

.credential-status.stored {
    color: var(--accent-success);
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
}

.credential-status.missing {
    color: var(--accent-warning);
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
}

.credential-status-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: currentColor;
}

/* Loading State for Auth Modal */
.auth-modal .button.loading {
    position: relative;
    color: transparent;
}

.auth-modal .button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid currentColor;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    color: var(--text-white);
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Print Styles */
@media print {
    .modal,
    .auth-modal,
    .notification,
    .button {
        display: none !important;
    }

    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
        background: white !important;
    }

    .workflow-step::after {
        display: none;
    }
}
