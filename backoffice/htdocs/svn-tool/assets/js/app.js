/**
 * Modern SVN Web Client - Enhanced JavaScript Application
 *
 * DISABLED: This file is not used to prevent duplicate API calls.
 * The svn-tool.js file is used instead for PHP 5.6 compatibility.
 */

// Prevent this file from running to avoid duplicate API calls
if (typeof window.SVN_TOOL_APP_JS_DISABLED === 'undefined') {
    window.SVN_TOOL_APP_JS_DISABLED = true;
    console.warn('app.js is disabled to prevent duplicate API calls. Using svn-tool.js instead.');
}

// Exit early if this file is accidentally loaded
if (window.SVN_TOOL_APP_JS_DISABLED) {
    // Do not define SvnApp to prevent conflicts
} else {

const SvnApp = {
    // Configuration
    config: {
        apiBaseUrl: '',
        pollInterval: 1000,
        maxRetries: 3,
        timeout: 30000
    },

    // State management
    state: {
        currentRepo: null,
        selectedFiles: new Set(),
        isScanning: false,
        scanAbortController: null
    },

    // Initialize application
    init() {
        console.log('🚀 Initializing Modern SVN Web Client...');
        
        this.detectBaseUrl();
        this.NotificationManager.init();
        this.ServerScanner.init();
        this.bindEvents();
        
        // Load initial data
        setTimeout(() => {
            this.loadRepositories();
        }, 100);
        
        console.log('✅ SVN Web Client initialized successfully');
    },

    // Detect base URL for API calls
    detectBaseUrl() {
        // Get current script location to determine base URL
        const scripts = document.getElementsByTagName('script');
        for (let script of scripts) {
            if (script.src && script.src.includes('app.js')) {
                const scriptPath = script.src;
                this.config.apiBaseUrl = scriptPath.replace('/assets/js/app.js', '/');
                break;
            }
        }
        
        // Fallback to current directory
        if (!this.config.apiBaseUrl) {
            this.config.apiBaseUrl = window.location.pathname.replace(/\/[^\/]*$/, '/');
        }
        
        console.log('🔗 API Base URL:', this.config.apiBaseUrl);
    },

    // Bind event listeners
    bindEvents() {
        // Repository refresh
        const refreshBtn = document.getElementById('refresh-repos');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadRepositories());
        }

        // File actions
        const commitBtn = document.getElementById('commit-btn');
        const selectAllBtn = document.getElementById('select-all-btn');
        const deselectAllBtn = document.getElementById('deselect-all-btn');

        if (commitBtn) {
            commitBtn.addEventListener('click', () => this.commitSelectedFiles());
        }
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => this.selectAllFiles());
        }
        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', () => this.deselectAllFiles());
        }

        // Utility actions
        const cleanupBtn = document.getElementById('cleanup-btn');
        const permissionsBtn = document.getElementById('check-permissions');

        if (cleanupBtn) {
            cleanupBtn.addEventListener('click', () => this.cleanupRepository());
        }
        if (permissionsBtn) {
            permissionsBtn.addEventListener('click', () => this.checkPermissions());
        }
    },

    // API request helper
    async apiRequest(endpoint, options = {}) {
        const url = this.config.apiBaseUrl + 'api.php' + endpoint;
        
        try {
            const response = await fetch(url, {
                method: options.method || 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    ...options.headers
                },
                signal: options.signal,
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    },

    // Load repositories from API
    async loadRepositories() {
        this.setButtonLoading('refresh-repos', true);
        
        try {
            // Get the current repository base path from the form
            const repoPathInput = document.getElementById('repo_base_path');
            const repoPath = repoPathInput ? repoPathInput.value.trim() : '';
            
            // Build API URL with repository path parameter
            let apiUrl = '?action=list_repos';
            if (repoPath) {
                apiUrl += '&repo_base_path=' + encodeURIComponent(repoPath);
            }
            
            const data = await this.apiRequest(apiUrl);
            
            if (data.status === 'success') {
                this.renderRepositories(data.data);
                this.NotificationManager.success('Success', `Found ${data.count} repositories in ${data.base_path}`);
            } else {
                throw new Error(data.message || 'Failed to load repositories');
            }
            
        } catch (error) {
            this.NotificationManager.error('Load Failed', 'Failed to load repositories: ' + error.message);
            console.error('Load repositories error:', error);
        } finally {
            this.setButtonLoading('refresh-repos', false);
        }
    },

    // Render repositories list
    renderRepositories(repositories) {
        const repoList = document.getElementById('repo-list');
        if (!repoList) return;

        if (!repositories || repositories.length === 0) {
            repoList.innerHTML = `
                <div class="list-empty">
                    <div class="list-empty-icon">📁</div>
                    <div>No SVN repositories found</div>
                    <div class="text-sm" style="margin-top: var(--space-2); color: var(--gray-500);">
                        Check your repository base path configuration
                    </div>
                </div>
            `;
            return;
        }

        const html = repositories.map(repo => `
            <div class="list-item" onclick="SvnApp.selectRepository('${repo.path}')" data-repo-path="${repo.path}">
                <div class="list-item-icon">📂</div>
                <div class="list-item-content">
                    <div class="list-item-title">${this.escapeHtml(repo.name)}</div>
                    <div class="list-item-subtitle">${this.escapeHtml(repo.path)}</div>
                </div>
            </div>
        `).join('');

        repoList.innerHTML = html;
    },

    // Select repository
    async selectRepository(repoPath) {
        try {
            // Update UI state
            document.querySelectorAll('.list-item').forEach(item => {
                item.classList.remove('active');
            });
            
            const selectedItem = document.querySelector(`[data-repo-path="${repoPath}"]`);
            if (selectedItem) {
                selectedItem.classList.add('active');
            }

            this.state.currentRepo = repoPath;
            this.state.selectedFiles.clear();

            // Load repository status
            const data = await this.apiRequest(`?action=get_status&path=${encodeURIComponent(repoPath)}`);
            
            if (data.status === 'success') {
                this.renderFiles(data.data);
                this.updateCommitButton();
            } else {
                throw new Error(data.message || 'Failed to load repository status');
            }

        } catch (error) {
            console.error('Failed to select repository:', error);
            this.NotificationManager.error('Error', 'Failed to load repository: ' + error.message);
            
            const fileList = document.getElementById('file-list');
            if (fileList) {
                fileList.innerHTML = `
                    <div class="list-empty">
                        <div class="list-empty-icon">❌</div>
                        <div>Failed to load repository status</div>
                    </div>
                `;
            }
        }
    },

    // Render files list
    renderFiles(files) {
        const fileList = document.getElementById('file-list');
        if (!fileList) return;

        if (!files || files.length === 0) {
            fileList.innerHTML = `
                <div class="list-empty">
                    <div class="list-empty-icon">✅</div>
                    <div>No modified files</div>
                    <div class="text-sm" style="margin-top: var(--space-2); color: var(--gray-500);">
                        All files are up to date
                    </div>
                </div>
            `;
            return;
        }

        const html = files.map(file => `
            <div class="list-item" onclick="SvnApp.toggleFileSelection('${file.path}')" data-file-path="${file.path}">
                <input type="checkbox" class="file-checkbox" onchange="SvnApp.handleFileCheckbox('${file.path}', this.checked)">
                <div class="list-item-content">
                    <div class="list-item-title">${this.escapeHtml(file.name)}</div>
                    <div class="list-item-subtitle">${this.escapeHtml(file.path)}</div>
                </div>
                <div class="status-badge status-${file.status}">${file.status}</div>
            </div>
        `).join('');

        fileList.innerHTML = html;
    },

    // Toggle file selection
    toggleFileSelection(filePath) {
        const checkbox = document.querySelector(`[data-file-path="${filePath}"] .file-checkbox`);
        if (checkbox) {
            checkbox.checked = !checkbox.checked;
            this.handleFileCheckbox(filePath, checkbox.checked);
        }
    },

    // Handle file checkbox change
    handleFileCheckbox(filePath, checked) {
        const fileCheckbox = document.querySelector(`[data-file-path="${filePath}"] .file-checkbox`);
        if (fileCheckbox) {
            fileCheckbox.checked = checked;
        }

        if (checked) {
            this.state.selectedFiles.add(filePath);
        } else {
            this.state.selectedFiles.delete(filePath);
        }

        this.updateCommitButton();
    },

    // Select all files
    selectAllFiles() {
        const fileCheckboxes = document.querySelectorAll('.file-checkbox');
        fileCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
            this.state.selectedFiles.add(checkbox.getAttribute('data-file-path'));
        });
        this.updateCommitButton();
    },

    // Deselect all files
    deselectAllFiles() {
        const fileCheckboxes = document.querySelectorAll('.file-checkbox');
        fileCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
            this.state.selectedFiles.delete(checkbox.getAttribute('data-file-path'));
        });
        this.updateCommitButton();
    },

    // Update commit button state
    updateCommitButton() {
        const commitBtn = document.getElementById('commit-btn');
        if (!commitBtn) return;
        
        const selectedCount = this.state.selectedFiles.size;
        commitBtn.disabled = selectedCount === 0;
        
        const span = commitBtn.querySelector('span');
        if (span) {
            span.textContent = selectedCount > 0 ? 
                `Commit Selected (${selectedCount})` : 
                'Commit Selected';
        }
    },

    // Commit selected files
    async commitSelectedFiles() {
        const commitBtn = document.getElementById('commit-btn');
        if (commitBtn) {
            commitBtn.disabled = true;
        }

        const commitMessage = prompt('Enter commit message:');
        if (!commitMessage) {
            if (commitBtn) {
                commitBtn.disabled = false;
            }
            return;
        }

        try {
            const data = await this.apiRequest('?action=commit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    path: this.state.currentRepo,
                    files: Array.from(this.state.selectedFiles),
                    message: commitMessage
                })
            });

            if (data.status === 'success') {
                this.NotificationManager.success('Success', 'Files committed successfully');
                this.loadRepositories();
            } else {
                throw new Error(data.message || 'Failed to commit files');
            }
        } catch (error) {
            console.error('Failed to commit files:', error);
            this.NotificationManager.error('Error', 'Failed to commit files: ' + error.message);
        } finally {
            if (commitBtn) {
                commitBtn.disabled = false;
            }
        }
    },

    // Cleanup repository
    async cleanupRepository() {
        const cleanupBtn = document.getElementById('cleanup-btn');
        if (cleanupBtn) {
            cleanupBtn.disabled = true;
        }

        try {
            const data = await this.apiRequest('?action=cleanup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    path: this.state.currentRepo
                })
            });

            if (data.status === 'success') {
                this.NotificationManager.success('Success', 'Repository cleaned up successfully');
                this.loadRepositories();
            } else {
                throw new Error(data.message || 'Failed to cleanup repository');
            }
        } catch (error) {
            console.error('Failed to cleanup repository:', error);
            this.NotificationManager.error('Error', 'Failed to cleanup repository: ' + error.message);
        } finally {
            if (cleanupBtn) {
                cleanupBtn.disabled = false;
            }
        }
    },

    // Check permissions
    async checkPermissions() {
        const permissionsBtn = document.getElementById('check-permissions');
        if (permissionsBtn) {
            permissionsBtn.disabled = true;
        }

        try {
            const data = await this.apiRequest('?action=check_permissions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    path: this.state.currentRepo
                })
            });

            if (data.status === 'success') {
                this.NotificationManager.success('Success', 'Permissions checked successfully');
                this.loadRepositories();
            } else {
                throw new Error(data.message || 'Failed to check permissions');
            }
        } catch (error) {
            console.error('Failed to check permissions:', error);
            this.NotificationManager.error('Error', 'Failed to check permissions: ' + error.message);
        } finally {
            if (permissionsBtn) {
                permissionsBtn.disabled = false;
            }
        }
    },

    // Set button loading state
    setButtonLoading(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (!button) return;
        
        // Store original text if not already stored
        if (!button.dataset.originalText) {
            button.dataset.originalText = button.textContent.trim();
        }
        
        if (loading) {
            button.disabled = true;
            button.classList.add('loading');
            button.textContent = 'Loading...';
        } else {
            button.disabled = false;
            button.classList.remove('loading');
            button.textContent = button.dataset.originalText || 'Refresh';
        }
    },

    // Escape HTML
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    // Notification Manager
    NotificationManager: {
        init() {
            console.log('📢 NotificationManager initialized');
        },
        success(title, message) {
            console.log(`✅ ${title}: ${message}`);
            // You can enhance this with actual UI notifications later
        },
        error(title, message) {
            console.error(`❌ ${title}: ${message}`);
            alert(`${title}: ${message}`);
        },
        info(title, message) {
            console.log(`ℹ️ ${title}: ${message}`);
        }
    },

    // Server Scanner
    ServerScanner: {
        init() {
            console.log('🔍 ServerScanner initialized');
        }
    }
}

// Initialize the application when DOM is loaded (single initialization)
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📄 DOM loaded, initializing SVN App...');
        SvnApp.init();
    });
} else {
    console.log('📄 DOM already loaded, initializing SVN App immediately...');
    SvnApp.init();
}

} // End of disabled app.js conditional block