/**
 * SVN Tool JavaScript - Refactored for PHP 5.6 and RiaShop Integration
 * 
 * Handles all client-side functionality for the SVN tool
 */

var SvnTool = {
    // Configuration
    config: {
        apiBaseUrl: './',
        pollInterval: 1000,
        maxRetries: 3,
        timeout: 30000
    },

    // State management
    state: {
        currentRepo: null,
        selectedFiles: [],
        isScanning: false,
        csrfToken: null,
        currentDiff: null,
        initialized: false,
        eventsBound: false,
        apiCallCount: {},
        pendingOperation: null,
        hasStoredCredentials: false
    },

    // Initialize application
    init: function() {
        // Prevent multiple initializations
        if (this.state.initialized) {
            console.warn('⚠️ SVN Tool already initialized, skipping duplicate initialization...');
            return;
        }

        console.log('🚀 Initializing SVN Tool...');
        this.state.initialized = true;

        this.detectCsrfToken();
        this.NotificationManager.init();
        this.ServerScanner.init();
        this.AuthManager.init();
        this.bindEvents();

        // Test if elements exist
        var startScanBtn = document.getElementById('start-scan-btn');
        console.log('Start scan button found:', !!startScanBtn);

        var serverScannerModal = document.getElementById('server-scanner-modal');
        console.log('Server scanner modal found:', !!serverScannerModal);
        
        // Load initial data if repository path is configured
        var repoPathInput = document.getElementById('repo_base_path');
        if (repoPathInput && repoPathInput.value.trim()) {
            setTimeout(function() {
                SvnTool.loadRepositories();
            }, 500);
        }

        // Check credential status on initialization
        setTimeout(function() {
            SvnTool.AuthManager.checkStoredCredentials();
        }, 1000);
        
        console.log('SVN Tool initialized successfully');
    },

    // Detect CSRF token from page
    detectCsrfToken: function() {
        var tokenInput = document.querySelector('input[name="csrf_token"]');
        if (tokenInput) {
            this.state.csrfToken = tokenInput.value;
        }
    },

    // Bind event listeners
    bindEvents: function() {
        console.log('🔗 Binding SVN Tool events...');
        var self = this;

        // Prevent duplicate event binding
        if (this.state.eventsBound) {
            console.log('⚠️ Events already bound, skipping...');
            return;
        }
        this.state.eventsBound = true;

        // Repository refresh
        var refreshBtn = document.getElementById('refresh-repos');
        if (refreshBtn) {
            // Remove any existing listeners (clone and replace)
            var newRefreshBtn = refreshBtn.cloneNode(true);
            refreshBtn.parentNode.replaceChild(newRefreshBtn, refreshBtn);

            newRefreshBtn.addEventListener('click', function() {
                self.loadRepositories();
            });
        }

        // File actions
        var commitBtn = document.getElementById('commit-btn');
        var selectAllBtn = document.getElementById('select-all-btn');
        var deselectAllBtn = document.getElementById('deselect-all-btn');

        if (commitBtn) {
            // Remove any existing listeners
            var newCommitBtn = commitBtn.cloneNode(true);
            commitBtn.parentNode.replaceChild(newCommitBtn, commitBtn);

            newCommitBtn.addEventListener('click', function() {
                self.commitSelectedFiles();
            });
        }
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', function() {
                self.selectAllFiles();
            });
        }
        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', function() {
                self.deselectAllFiles();
            });
        }

        // Utility actions
        var cleanupBtn = document.getElementById('cleanup-btn');
        var permissionsBtn = document.getElementById('check-permissions');
        var fixPermissionsBtn = document.getElementById('fix-permissions-btn');
        var checkConnectivityBtn = document.getElementById('check-connectivity-btn');

        if (cleanupBtn) {
            var newCleanupBtn = cleanupBtn.cloneNode(true);
            cleanupBtn.parentNode.replaceChild(newCleanupBtn, cleanupBtn);
            newCleanupBtn.addEventListener('click', function() {
                self.cleanupRepository();
            });
        }
        if (permissionsBtn) {
            var newPermissionsBtn = permissionsBtn.cloneNode(true);
            permissionsBtn.parentNode.replaceChild(newPermissionsBtn, permissionsBtn);
            newPermissionsBtn.addEventListener('click', function() {
                self.checkPermissions();
            });
        }
        if (fixPermissionsBtn) {
            var newFixPermissionsBtn = fixPermissionsBtn.cloneNode(true);
            fixPermissionsBtn.parentNode.replaceChild(newFixPermissionsBtn, fixPermissionsBtn);
            newFixPermissionsBtn.addEventListener('click', function() {
                self.fixPermissions();
            });
        }
        if (checkConnectivityBtn) {
            var newCheckConnectivityBtn = checkConnectivityBtn.cloneNode(true);
            checkConnectivityBtn.parentNode.replaceChild(newCheckConnectivityBtn, checkConnectivityBtn);
            newCheckConnectivityBtn.addEventListener('click', function() {
                self.checkConnectivity();
            });
        }

        // Server scanner button
        var serverScanBtn = document.getElementById('server-scan-btn');
        if (serverScanBtn) {
            serverScanBtn.addEventListener('click', function() {
                self.ServerScanner.openModal();
            });
        }

        // Copy diff button
        var copyDiffBtn = document.getElementById('copy-diff-btn');
        if (copyDiffBtn) {
            copyDiffBtn.addEventListener('click', function() {
                self.copyDiffToClipboard();
            });
        }

        // Credential management buttons
        var manageCredentialsBtn = document.getElementById('manage-credentials-btn');
        var clearCredentialsBtn = document.getElementById('clear-credentials-btn');

        if (manageCredentialsBtn) {
            manageCredentialsBtn.addEventListener('click', function() {
                self.AuthManager.showModal();
            });
        }

        if (clearCredentialsBtn) {
            clearCredentialsBtn.addEventListener('click', function() {
                self.AuthManager.clearCredentials();
            });
        }
    },

    // API request helper
    apiRequest: function(endpoint, options, callback) {
        // Track API calls to detect duplicates
        if (!this.state.apiCallCount) {
            this.state.apiCallCount = {};
        }
        var callKey = endpoint + (options.method || 'GET');
        this.state.apiCallCount[callKey] = (this.state.apiCallCount[callKey] || 0) + 1;

        if (this.state.apiCallCount[callKey] > 1) {
            console.warn('🚨 DUPLICATE API CALL DETECTED:', callKey, 'Call #' + this.state.apiCallCount[callKey]);
        }

        var url = this.config.apiBaseUrl + 'api.php' + endpoint;
        var xhr = new XMLHttpRequest();
        var method = options.method || 'GET';

        console.log('📡 Making API request:', method, url, 'Call #' + this.state.apiCallCount[callKey]);

        xhr.open(method, url, true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

        if (this.state.csrfToken) {
            xhr.setRequestHeader('X-CSRF-Token', this.state.csrfToken);
        }

        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                console.log('API response received:', xhr.status, xhr.responseText);

                try {
                    var data = JSON.parse(xhr.responseText);
                    if (xhr.status === 200) {
                        callback(null, data);
                    } else {
                        // Check for authentication errors
                        if (data.message && SvnTool.AuthManager.isAuthenticationError(data.message)) {
                            SvnTool.AuthManager.handleAuthenticationError(data.message, endpoint, options, callback);
                            return;
                        }
                        callback(new Error(data.message || 'Request failed'), null);
                    }
                } catch (e) {
                    console.error('JSON parse error:', e, 'Response:', xhr.responseText);
                    callback(new Error('Invalid JSON response'), null);
                }
            }
        };

        xhr.onerror = function() {
            console.error('Network error for request:', url);
            callback(new Error('Network error'), null);
        };

        if (options.body) {
            var requestBody = JSON.stringify(options.body);
            console.log('Sending POST body:', requestBody);
            xhr.send(requestBody);
        } else {
            xhr.send();
        }
    },

    // Load repositories from API
    loadRepositories: function() {
        var self = this;
        this.setButtonLoading('refresh-repos', true);
        
        // Get the current repository base path from the form
        var repoPathInput = document.getElementById('repo_base_path');
        var repoPath = repoPathInput ? repoPathInput.value.trim() : '';
        
        // Build API URL with repository path parameter
        var apiUrl = '?action=list_repos';
        if (repoPath) {
            apiUrl += '&repo_base_path=' + encodeURIComponent(repoPath);
        }
        
        this.apiRequest(apiUrl, {}, function(error, data) {
            self.setButtonLoading('refresh-repos', false);
            
            if (error) {
                self.NotificationManager.error('Erreur de chargement', 'Impossible de charger les dépôts: ' + error.message);
                console.error('Load repositories error:', error);
                return;
            }
            
            if (data.status === 'success') {
                self.renderRepositories(data.data);
                self.NotificationManager.success('Succès', 'Trouvé ' + data.count + ' dépôts dans ' + data.base_path);
            } else {
                self.NotificationManager.error('Erreur', data.message || 'Échec du chargement des dépôts');
            }
        });
    },

    // Render repositories list
    renderRepositories: function(repositories) {
        var repoList = document.getElementById('repo-list');
        if (!repoList) return;

        if (!repositories || repositories.length === 0) {
            repoList.innerHTML = '<div class="list-group-item text-center">Aucun dépôt SVN trouvé</div>';
            return;
        }

        var html = '';
        for (var i = 0; i < repositories.length; i++) {
            var repo = repositories[i];
            html += '<div class="list-group-item" onclick="SvnTool.selectRepository(\'' + 
                    this.escapeHtml(repo.path) + '\')" data-repo-path="' + 
                    this.escapeHtml(repo.path) + '">' +
                    '<strong>' + this.escapeHtml(repo.name) + '</strong><br>' +
                    '<small>' + this.escapeHtml(repo.path) + '</small>' +
                    '</div>';
        }

        repoList.innerHTML = html;
    },

    // Select repository
    selectRepository: function(repoPath) {
        var self = this;
        
        try {
            // Update UI state
            var listItems = document.querySelectorAll('#repo-list .list-group-item');
            for (var i = 0; i < listItems.length; i++) {
                listItems[i].classList.remove('active');
            }
            
            var selectedItem = document.querySelector('[data-repo-path="' + repoPath + '"]');
            if (selectedItem) {
                selectedItem.classList.add('active');
            }

            this.state.currentRepo = repoPath;
            this.state.selectedFiles = [];

            // Load repository status
            this.apiRequest('?action=get_status&path=' + encodeURIComponent(repoPath), {}, function(error, data) {
                if (error) {
                    console.error('Failed to select repository:', error);
                    self.NotificationManager.error('Erreur', 'Impossible de charger le dépôt: ' + error.message);
                    
                    var fileList = document.getElementById('file-list');
                    if (fileList) {
                        fileList.innerHTML = '<div class="list-group-item text-center">Échec du chargement du statut du dépôt</div>';
                    }
                    return;
                }
                
                if (data.status === 'success') {
                    self.renderFiles(data.data);
                    self.updateCommitButton();
                } else {
                    self.NotificationManager.error('Erreur', data.message || 'Échec du chargement du statut du dépôt');
                }
            });

        } catch (error) {
            console.error('Failed to select repository:', error);
            this.NotificationManager.error('Erreur', 'Erreur lors de la sélection du dépôt');
        }
    },

    // Render files list
    renderFiles: function(files) {
        var fileList = document.getElementById('file-list');
        if (!fileList) return;

        if (!files || files.length === 0) {
            fileList.innerHTML = '<div class="list-group-item text-center">Aucun fichier modifié</div>';
            return;
        }

        var html = '';
        for (var i = 0; i < files.length; i++) {
            var file = files[i];
            html += '<div class="list-group-item file-item" data-file-path="' +
                    this.escapeHtml(file.path) + '">' +
                    '<div class="file-header" onclick="SvnTool.toggleFileSelection(\'' +
                    this.escapeHtml(file.path) + '\')">' +
                    '<input type="checkbox" class="file-checkbox" onchange="SvnTool.handleFileCheckbox(\'' +
                    this.escapeHtml(file.path) + '\', this.checked)" onclick="event.stopPropagation();">' +
                    '<strong>' + this.escapeHtml(file.name) + '</strong>' +
                    '<span class="badge status-' + file.status + '" style="float: right;">' + file.status + '</span>' +
                    '</div>' +
                    '<div class="file-details">' +
                    '<small>' + this.escapeHtml(file.path) + '</small>' +
                    '<button class="btn-diff" onclick="SvnTool.showFileDiff(\'' +
                    this.escapeHtml(file.path) + '\'); event.stopPropagation();" style="float: right; margin-left: 10px;">Voir diff</button>' +
                    '</div>' +
                    '</div>';
        }

        fileList.innerHTML = html;
    },

    // Toggle file selection
    toggleFileSelection: function(filePath) {
        var checkbox = document.querySelector('[data-file-path="' + filePath + '"] .file-checkbox');
        if (checkbox) {
            checkbox.checked = !checkbox.checked;
            this.handleFileCheckbox(filePath, checkbox.checked);
        }
    },

    // Handle file checkbox change
    handleFileCheckbox: function(filePath, checked) {
        var fileCheckbox = document.querySelector('[data-file-path="' + filePath + '"] .file-checkbox');
        if (fileCheckbox) {
            fileCheckbox.checked = checked;
        }

        if (checked) {
            if (this.state.selectedFiles.indexOf(filePath) === -1) {
                this.state.selectedFiles.push(filePath);
            }
        } else {
            var index = this.state.selectedFiles.indexOf(filePath);
            if (index > -1) {
                this.state.selectedFiles.splice(index, 1);
            }
        }

        this.updateCommitButton();
    },

    // Show file diff
    showFileDiff: function(filePath) {
        var self = this;

        if (!this.state.currentRepo) {
            this.NotificationManager.error('Erreur', 'Aucun dépôt sélectionné');
            return;
        }

        // Show loading in diff viewer
        var diffViewer = document.getElementById('diff-viewer');
        var diffPlaceholder = document.getElementById('diff-placeholder');
        var diffContent = document.getElementById('diff-content');
        var diffFilename = document.getElementById('diff-filename');
        var copyDiffBtn = document.getElementById('copy-diff-btn');

        if (diffViewer) diffViewer.style.display = 'block';
        if (diffPlaceholder) diffPlaceholder.style.display = 'none';
        if (copyDiffBtn) copyDiffBtn.style.display = 'inline-block';

        if (diffFilename) {
            diffFilename.textContent = 'Chargement du diff pour: ' + filePath;
        }
        if (diffContent) {
            diffContent.innerHTML = '<div class="loading">Chargement du diff...</div>';
        }

        // Load diff from API
        this.apiRequest('?action=get_diff&path=' + encodeURIComponent(this.state.currentRepo) +
                       '&file=' + encodeURIComponent(filePath), {}, function(error, data) {
            if (error) {
                console.error('Failed to load diff:', error);
                self.NotificationManager.error('Erreur', 'Impossible de charger le diff: ' + error.message);

                if (diffContent) {
                    diffContent.innerHTML = '<div class="error">Erreur lors du chargement du diff</div>';
                }
                return;
            }

            if (data.status === 'success') {
                self.renderDiff(data.data, filePath);
            } else {
                self.NotificationManager.error('Erreur', data.message || 'Échec du chargement du diff');
                if (diffContent) {
                    diffContent.innerHTML = '<div class="error">Échec du chargement du diff</div>';
                }
            }
        });
    },

    // Render diff content
    renderDiff: function(diffData, filePath) {
        var diffFilename = document.getElementById('diff-filename');
        var diffContent = document.getElementById('diff-content');
        var diffStats = document.getElementById('diff-stats');

        if (diffFilename) {
            diffFilename.textContent = filePath;
        }

        if (diffStats) {
            diffStats.textContent = diffData.has_changes ? 'Fichier modifié' : 'Aucune modification';
        }

        if (diffContent) {
            if (diffData.has_changes && diffData.diff) {
                // Format diff with basic syntax highlighting
                var formattedDiff = this.formatDiff(diffData.diff);
                diffContent.innerHTML = '<pre class="diff-content">' + formattedDiff + '</pre>';
            } else {
                diffContent.innerHTML = '<div class="no-changes">Aucune modification détectée pour ce fichier</div>';
            }
        }

        // Store diff data for copying
        this.state.currentDiff = diffData.diff || '';
    },

    // Format diff output with basic highlighting
    formatDiff: function(diffText) {
        if (!diffText) return 'Aucun diff disponible';

        var lines = diffText.split('\n');
        var formattedLines = [];

        for (var i = 0; i < lines.length; i++) {
            var line = this.escapeHtml(lines[i]);

            if (line.startsWith('+++') || line.startsWith('---')) {
                formattedLines.push('<span class="diff-file">' + line + '</span>');
            } else if (line.startsWith('@@')) {
                formattedLines.push('<span class="diff-hunk">' + line + '</span>');
            } else if (line.startsWith('+')) {
                formattedLines.push('<span class="diff-added">' + line + '</span>');
            } else if (line.startsWith('-')) {
                formattedLines.push('<span class="diff-removed">' + line + '</span>');
            } else {
                formattedLines.push('<span class="diff-context">' + line + '</span>');
            }
        }

        return formattedLines.join('\n');
    },

    // Select all files
    selectAllFiles: function() {
        var fileCheckboxes = document.querySelectorAll('.file-checkbox');
        this.state.selectedFiles = [];
        
        for (var i = 0; i < fileCheckboxes.length; i++) {
            var checkbox = fileCheckboxes[i];
            checkbox.checked = true;
            var filePath = checkbox.closest('[data-file-path]').getAttribute('data-file-path');
            if (filePath && this.state.selectedFiles.indexOf(filePath) === -1) {
                this.state.selectedFiles.push(filePath);
            }
        }
        this.updateCommitButton();
    },

    // Deselect all files
    deselectAllFiles: function() {
        var fileCheckboxes = document.querySelectorAll('.file-checkbox');
        this.state.selectedFiles = [];
        
        for (var i = 0; i < fileCheckboxes.length; i++) {
            fileCheckboxes[i].checked = false;
        }
        this.updateCommitButton();
    },

    // Update commit button state
    updateCommitButton: function() {
        var commitBtn = document.getElementById('commit-btn');
        if (!commitBtn) return;

        var selectedCount = this.state.selectedFiles.length;
        commitBtn.disabled = selectedCount === 0;

        commitBtn.textContent = selectedCount > 0 ?
            'Commiter les fichiers sélectionnés (' + selectedCount + ')' :
            'Commiter les fichiers sélectionnés';
    },

    // Copy diff to clipboard
    copyDiffToClipboard: function() {
        if (!this.state.currentDiff) {
            this.NotificationManager.warning('Attention', 'Aucun diff à copier');
            return;
        }

        try {
            // Create a temporary textarea to copy the diff
            var tempTextarea = document.createElement('textarea');
            tempTextarea.value = this.state.currentDiff;
            document.body.appendChild(tempTextarea);
            tempTextarea.select();
            document.execCommand('copy');
            document.body.removeChild(tempTextarea);

            this.NotificationManager.success('Succès', 'Diff copié dans le presse-papiers');
        } catch (error) {
            console.error('Failed to copy diff:', error);
            this.NotificationManager.error('Erreur', 'Impossible de copier le diff');
        }
    },

    // Commit selected files
    commitSelectedFiles: function() {
        var self = this;
        var commitBtn = document.getElementById('commit-btn');

        if (!this.state.currentRepo) {
            this.NotificationManager.error('Erreur', 'Aucun dépôt sélectionné');
            return;
        }

        if (this.state.selectedFiles.length === 0) {
            this.NotificationManager.error('Erreur', 'Aucun fichier sélectionné');
            return;
        }

        var commitMessage = document.getElementById('commit-message').value.trim();
        if (!commitMessage) {
            this.NotificationManager.error('Erreur', 'Veuillez entrer un message de commit');
            return;
        }

        if (commitBtn) {
            commitBtn.disabled = true;
            commitBtn.textContent = 'Commit en cours...';
        }

        this.apiRequest('?action=commit', {
            method: 'POST',
            body: {
                path: this.state.currentRepo,
                files: this.state.selectedFiles,
                message: commitMessage
            }
        }, function(error, data) {
            if (commitBtn) {
                commitBtn.disabled = false;
                commitBtn.textContent = 'Commiter les fichiers sélectionnés';
            }

            if (error) {
                console.error('Failed to commit files:', error);
                self.NotificationManager.error('Erreur', 'Échec du commit: ' + error.message);
                return;
            }

            if (data.status === 'success') {
                self.NotificationManager.success('Succès', 'Fichiers commités avec succès');
                document.getElementById('commit-message').value = '';
                self.state.selectedFiles = [];
                self.selectRepository(self.state.currentRepo); // Refresh file list
            } else {
                self.NotificationManager.error('Erreur', data.message || 'Échec du commit');
            }
        });
    },

    // Cleanup repository
    cleanupRepository: function() {
        var self = this;
        var cleanupBtn = document.getElementById('cleanup-btn');

        if (!this.state.currentRepo) {
            this.NotificationManager.error('Erreur', 'Aucun dépôt sélectionné');
            return;
        }

        if (cleanupBtn) {
            cleanupBtn.disabled = true;
            cleanupBtn.textContent = 'Nettoyage...';
        }

        this.apiRequest('?action=cleanup', {
            method: 'POST',
            body: {
                path: this.state.currentRepo
            }
        }, function(error, data) {
            if (cleanupBtn) {
                cleanupBtn.disabled = false;
                cleanupBtn.textContent = 'Nettoyage';
            }

            if (error) {
                console.error('Failed to cleanup repository:', error);
                self.NotificationManager.error('Erreur', 'Échec du nettoyage: ' + error.message);
                return;
            }

            if (data.status === 'success') {
                self.NotificationManager.success('Succès', 'Dépôt nettoyé avec succès');
                self.selectRepository(self.state.currentRepo); // Refresh file list
            } else {
                self.NotificationManager.error('Erreur', data.message || 'Échec du nettoyage');
            }
        });
    },

    // Check permissions
    checkPermissions: function() {
        var self = this;
        var permissionsBtn = document.getElementById('check-permissions');

        if (!this.state.currentRepo) {
            this.NotificationManager.error('Erreur', 'Aucun dépôt sélectionné');
            return;
        }

        if (permissionsBtn) {
            permissionsBtn.disabled = true;
            permissionsBtn.textContent = 'Vérification...';
        }

        this.apiRequest('?action=check_permissions', {
            method: 'POST',
            body: {
                path: this.state.currentRepo
            }
        }, function(error, data) {
            if (permissionsBtn) {
                permissionsBtn.disabled = false;
                permissionsBtn.textContent = 'Permissions';
            }

            if (error) {
                console.error('Failed to check permissions:', error);
                self.NotificationManager.error('Erreur', 'Échec de la vérification: ' + error.message);
                return;
            }

            if (data.status === 'success') {
                self.NotificationManager.success('Succès', 'Permissions vérifiées avec succès');
                console.log('Permissions data:', data.data);
            } else {
                self.NotificationManager.error('Erreur', data.message || 'Échec de la vérification des permissions');
            }
        });
    },

    // Fix permissions
    fixPermissions: function() {
        var self = this;
        var fixPermissionsBtn = document.getElementById('fix-permissions-btn');

        if (!this.state.currentRepo) {
            this.NotificationManager.error('Erreur', 'Aucun dépôt sélectionné');
            return;
        }

        if (fixPermissionsBtn) {
            fixPermissionsBtn.disabled = true;
            fixPermissionsBtn.textContent = 'Correction...';
        }

        this.apiRequest('?action=fix_permissions', {
            method: 'POST',
            body: {
                path: this.state.currentRepo
            }
        }, function(error, data) {
            if (fixPermissionsBtn) {
                fixPermissionsBtn.disabled = false;
                fixPermissionsBtn.textContent = 'Corriger permissions';
            }

            if (error) {
                console.error('Failed to fix permissions:', error);
                self.NotificationManager.error('Erreur', 'Échec de la correction: ' + error.message);
                return;
            }

            if (data.status === 'success') {
                self.NotificationManager.success('Succès', 'Permissions corrigées avec succès');
                console.log('Fix permissions result:', data.data);
            } else {
                self.NotificationManager.error('Erreur', data.message || 'Échec de la correction des permissions');
            }
        });
    },

    // Check SVN connectivity
    checkConnectivity: function() {
        var self = this;
        var connectivityBtn = document.getElementById('check-connectivity-btn');

        if (!this.state.currentRepo) {
            this.NotificationManager.error('Erreur', 'Aucun dépôt sélectionné');
            return;
        }

        if (connectivityBtn) {
            connectivityBtn.disabled = true;
            connectivityBtn.textContent = 'Test en cours...';
        }

        this.apiRequest('?action=check_connectivity', {
            method: 'POST',
            body: {
                path: this.state.currentRepo
            }
        }, function(error, data) {
            if (connectivityBtn) {
                connectivityBtn.disabled = false;
                connectivityBtn.textContent = 'Test connectivité';
            }

            if (error) {
                console.error('Failed to check connectivity:', error);
                self.NotificationManager.error('Erreur', 'Échec du test de connectivité: ' + error.message);
                return;
            }

            if (data.status === 'success') {
                var result = data.data;
                var message = 'Test de connectivité terminé:\n';
                message += '• Statut local: ' + result.local_status + '\n';
                message += '• Connectivité distante: ' + result.remote_connectivity + '\n';
                message += '• Authentification: ' + result.authentication_status + '\n';

                if (result.repository_info && result.repository_info.url) {
                    message += '• URL: ' + result.repository_info.url + '\n';
                }

                if (result.recommendations && result.recommendations.length > 0) {
                    message += '\nRecommandations:\n';
                    for (var i = 0; i < result.recommendations.length; i++) {
                        message += '• ' + result.recommendations[i] + '\n';
                    }
                }

                self.NotificationManager.info('Test de connectivité', message);
                console.log('Connectivity check result:', result);
            } else {
                self.NotificationManager.error('Erreur', data.message || 'Échec du test de connectivité');
            }
        });
    },

    // Set button loading state
    setButtonLoading: function(buttonId, loading) {
        var button = document.getElementById(buttonId);
        if (!button) return;
        
        if (loading) {
            button.disabled = true;
            button.textContent = 'Chargement...';
        } else {
            button.disabled = false;
            button.textContent = button.getAttribute('data-original-text') || 'Actualiser';
        }
    },

    // Escape HTML
    escapeHtml: function(text) {
        var div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    // Notification Manager
    NotificationManager: {
        init: function() {
            console.log('NotificationManager initialized');
        },
        
        success: function(title, message) {
            console.log('SUCCESS: ' + title + ': ' + message);
            this.showNotification('success', title, message);
        },
        
        error: function(title, message) {
            console.error('ERROR: ' + title + ': ' + message);
            this.showNotification('error', title, message);
            alert(title + ': ' + message);
        },
        
        warning: function(title, message) {
            console.warn('WARNING: ' + title + ': ' + message);
            this.showNotification('warning', title, message);
        },
        
        info: function(title, message) {
            console.log('INFO: ' + title + ': ' + message);
            this.showNotification('info', title, message);
        },
        
        showNotification: function(type, title, message) {
            var container = document.getElementById('notification-container');
            if (!container) return;
            
            var notification = document.createElement('div');
            notification.className = 'notification notification-' + type;
            notification.innerHTML = '<strong>' + title + '</strong><br>' + message;
            
            container.appendChild(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(function() {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }
    },

    // Authentication Manager
    AuthManager: {
        init: function() {
            console.log('AuthManager initialized');
            this.checkStoredCredentials();
        },

        // Check if error message indicates authentication failure
        isAuthenticationError: function(message) {
            var authPatterns = [
                'Authentication required',
                'Authentication failed',
                'E170013',
                'E215004',
                'No more credentials',
                'svn: E170013',
                'svn: E215004'
            ];

            for (var i = 0; i < authPatterns.length; i++) {
                if (message.indexOf(authPatterns[i]) !== -1) {
                    return true;
                }
            }
            return false;
        },

        // Handle authentication error by showing modal
        handleAuthenticationError: function(errorMessage, endpoint, options, callback) {
            console.log('Authentication error detected:', errorMessage);

            // Store the pending operation for retry after authentication
            SvnTool.state.pendingOperation = {
                endpoint: endpoint,
                options: options,
                callback: callback
            };

            this.showModal(errorMessage);
        },

        // Show authentication modal
        showModal: function(errorMessage) {
            var modal = document.getElementById('svn-auth-modal');
            var errorDiv = document.getElementById('auth-error');
            var errorMessageSpan = document.getElementById('auth-error-message');

            if (modal) {
                // Update error message if provided
                if (errorMessage && errorDiv && errorMessageSpan) {
                    errorMessageSpan.textContent = errorMessage;
                    errorDiv.classList.add('show');
                } else if (errorDiv) {
                    errorDiv.classList.remove('show');
                }

                // Reset form
                this.resetForm();

                // Show modal
                modal.classList.add('show');

                // Focus on username field
                var usernameField = document.getElementById('svn-username');
                if (usernameField) {
                    setTimeout(function() {
                        usernameField.focus();
                    }, 300);
                }
            }
        },

        // Close authentication modal
        closeModal: function() {
            var modal = document.getElementById('svn-auth-modal');
            if (modal) {
                modal.classList.remove('show');

                // Clear pending operation
                SvnTool.state.pendingOperation = null;

                // Reset form
                this.resetForm();
            }
        },

        // Reset authentication form
        resetForm: function() {
            var form = document.getElementById('auth-form');
            var errorDiv = document.getElementById('auth-error');
            var submitBtn = document.getElementById('auth-submit-btn');

            if (form) {
                form.reset();
            }
            if (errorDiv) {
                errorDiv.classList.remove('show');
            }
            if (submitBtn) {
                submitBtn.classList.remove('loading');
                submitBtn.textContent = 'Authentifier';
            }
        },

        // Submit credentials
        submitCredentials: function() {
            var form = document.getElementById('auth-form');
            var submitBtn = document.getElementById('auth-submit-btn');
            var errorDiv = document.getElementById('auth-error');

            if (!form) return;

            var username = document.getElementById('svn-username').value.trim();
            var password = document.getElementById('svn-password').value;
            var remember = document.getElementById('remember-credentials').checked;

            if (!username || !password) {
                this.showError('Veuillez entrer votre nom d\'utilisateur et mot de passe');
                return;
            }

            // Show loading state
            if (submitBtn) {
                submitBtn.classList.add('loading');
            }
            if (errorDiv) {
                errorDiv.classList.remove('show');
            }

            // Store credentials via API
            SvnTool.apiRequest('?action=store_credentials', {
                method: 'POST',
                body: {
                    username: username,
                    password: password,
                    remember: remember
                }
            }, this.handleCredentialResponse.bind(this));
        },

        // Handle credential storage response
        handleCredentialResponse: function(error, data) {
            var submitBtn = document.getElementById('auth-submit-btn');

            if (submitBtn) {
                submitBtn.classList.remove('loading');
            }

            if (error) {
                console.error('Failed to store credentials:', error);
                this.showError('Erreur lors de la sauvegarde des identifiants: ' + error.message);
                return;
            }

            if (data.status === 'success') {
                SvnTool.state.hasStoredCredentials = true;
                this.updateCredentialStatus(true);

                // Close modal
                this.closeModal();

                // Retry the pending operation
                this.retryPendingOperation();

                SvnTool.NotificationManager.success('Succès', 'Identifiants sauvegardés avec succès');
            } else {
                this.showError(data.message || 'Échec de la sauvegarde des identifiants');
            }
        },

        // Show error in authentication modal
        showError: function(message) {
            var errorDiv = document.getElementById('auth-error');
            var errorMessageSpan = document.getElementById('auth-error-message');

            if (errorDiv && errorMessageSpan) {
                errorMessageSpan.textContent = message;
                errorDiv.classList.add('show');
            }
        },

        // Retry pending operation after authentication
        retryPendingOperation: function() {
            if (SvnTool.state.pendingOperation) {
                var operation = SvnTool.state.pendingOperation;
                SvnTool.state.pendingOperation = null;

                console.log('Retrying operation after authentication:', operation.endpoint);

                // Retry the original API call
                SvnTool.apiRequest(operation.endpoint, operation.options, operation.callback);
            }
        },

        // Check if credentials are stored
        checkStoredCredentials: function() {
            SvnTool.apiRequest('?action=check_credentials', {}, function(error, data) {
                if (!error && data.status === 'success') {
                    SvnTool.state.hasStoredCredentials = data.has_credentials;
                    SvnTool.AuthManager.updateCredentialStatus(data.has_credentials);
                }
            });
        },

        // Update credential status indicator
        updateCredentialStatus: function(hasCredentials) {
            var statusDiv = document.getElementById('credential-status');
            var clearBtn = document.getElementById('clear-credentials-btn');
            var manageBtn = document.getElementById('manage-credentials-btn');

            if (statusDiv) {
                if (hasCredentials) {
                    statusDiv.className = 'credential-status stored';
                    statusDiv.innerHTML = '<div class="credential-status-icon"></div><span>Identifiants stockés</span>';
                } else {
                    statusDiv.className = 'credential-status missing';
                    statusDiv.innerHTML = '<div class="credential-status-icon"></div><span>Aucun identifiant stocké</span>';
                }
            }

            // Show/hide clear button based on credential status
            if (clearBtn) {
                clearBtn.style.display = hasCredentials ? 'inline-flex' : 'none';
            }

            // Update manage button text
            if (manageBtn) {
                manageBtn.innerHTML = hasCredentials ?
                    '🔐 Modifier les identifiants' :
                    '🔐 Gérer les identifiants';
            }
        },

        // Clear stored credentials
        clearCredentials: function() {
            if (!confirm('Êtes-vous sûr de vouloir effacer les identifiants stockés ?')) {
                return;
            }

            SvnTool.apiRequest('?action=clear_credentials', {
                method: 'POST',
                body: {}
            }, function(error, data) {
                if (error) {
                    console.error('Failed to clear credentials:', error);
                    SvnTool.NotificationManager.error('Erreur', 'Impossible d\'effacer les identifiants: ' + error.message);
                    return;
                }

                if (data.status === 'success') {
                    SvnTool.state.hasStoredCredentials = false;
                    SvnTool.AuthManager.updateCredentialStatus(false);
                    SvnTool.NotificationManager.success('Succès', 'Identifiants effacés avec succès');
                } else {
                    SvnTool.NotificationManager.error('Erreur', data.message || 'Échec de l\'effacement des identifiants');
                }
            });
        }
    },

    // Server Scanner
    ServerScanner: {
        init: function() {
            console.log('ServerScanner initialized');
            this.bindEvents();
        },

        bindEvents: function() {
            var self = this;

            // Start scan button
            var startScanBtn = document.getElementById('start-scan-btn');
            if (startScanBtn) {
                startScanBtn.addEventListener('click', function() {
                    self.startScan();
                });
            }

            // Cancel scan button
            var cancelScanBtn = document.getElementById('cancel-scan-btn');
            if (cancelScanBtn) {
                cancelScanBtn.addEventListener('click', function() {
                    self.closeModal();
                });
            }

            // Add selected repos button
            var addSelectedBtn = document.getElementById('add-selected-repos');
            if (addSelectedBtn) {
                addSelectedBtn.addEventListener('click', function() {
                    self.addSelectedRepositories();
                });
            }

            // Select all found repos checkbox
            var selectAllCheckbox = document.getElementById('select-all-found');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    self.toggleAllFoundRepos(this.checked);
                });
            }
        },

        openModal: function() {
            var modal = document.getElementById('server-scanner-modal');
            if (modal) {
                modal.style.display = 'block';
                this.resetModal();
            }
        },

        closeModal: function() {
            var modal = document.getElementById('server-scanner-modal');
            if (modal) {
                modal.style.display = 'none';
                this.resetModal();
            }
        },

        resetModal: function() {
            // Reset to initial state
            document.getElementById('scanner-controls').style.display = 'block';
            document.getElementById('scan-progress').style.display = 'none';
            document.getElementById('scan-results').style.display = 'none';
            document.getElementById('modal-footer').style.display = 'none';

            // Reset form values
            var scanPath = document.getElementById('scan-path');
            if (scanPath && !scanPath.value.trim()) {
                scanPath.value = '/var/www';
            }

            // Clear results
            document.getElementById('found-repos').innerHTML = '';
            document.getElementById('found-count').textContent = '0';
        },

        startScan: function() {
            var scanPath = document.getElementById('scan-path').value.trim();
            var scanDepth = document.getElementById('scan-depth').value;

            if (!scanPath) {
                SvnTool.NotificationManager.error('Erreur', 'Veuillez entrer un chemin à scanner');
                return;
            }

            console.log('Starting scan with path:', scanPath, 'depth:', scanDepth);

            // Show progress
            document.getElementById('scanner-controls').style.display = 'none';
            document.getElementById('scan-progress').style.display = 'block';
            document.getElementById('scan-status').textContent = 'Démarrage du scan...';

            // Start scan
            console.log('Making API request to server_scan...');
            var apiUrl = '?action=server_scan&path=' + encodeURIComponent(scanPath) + '&depth=' + parseInt(scanDepth);
            SvnTool.apiRequest(apiUrl, {}, this.handleScanResponse.bind(this));
        },

        handleScanResponse: function(error, data) {
            console.log('Scan response received:', error, data);

            if (error) {
                console.error('Scan failed:', error);
                SvnTool.NotificationManager.error('Erreur de scan', error.message);
                this.resetModal();
                return;
            }

            if (data.status === 'success') {
                console.log('Scan successful, found', data.data.length, 'repositories');
                this.displayScanResults(data.data, data.meta);
            } else {
                console.error('Scan failed with status:', data.status, 'message:', data.message);
                SvnTool.NotificationManager.error('Erreur de scan', data.message || 'Échec du scan');
                this.resetModal();
            }
        },

        displayScanResults: function(repositories, meta) {
            // Hide progress, show results
            document.getElementById('scan-progress').style.display = 'none';
            document.getElementById('scan-results').style.display = 'block';
            document.getElementById('modal-footer').style.display = 'block';

            // Update count
            document.getElementById('found-count').textContent = repositories.length;

            // Display repositories
            var foundReposContainer = document.getElementById('found-repos');
            if (repositories.length === 0) {
                foundReposContainer.innerHTML = '<div class="alert alert-info">Aucun dépôt SVN trouvé dans ' + meta.scan_path + '</div>';
                return;
            }

            var html = '';
            for (var i = 0; i < repositories.length; i++) {
                var repo = repositories[i];
                html += '<div class="form-check" style="margin: 10px 0;">' +
                        '<input type="checkbox" class="form-check-input repo-checkbox" ' +
                        'id="repo-' + i + '" value="' + SvnTool.escapeHtml(repo.path) + '">' +
                        '<label class="form-check-label" for="repo-' + i + '">' +
                        '<strong>' + SvnTool.escapeHtml(repo.name) + '</strong> (' + repo.type + ')<br>' +
                        '<small>' + SvnTool.escapeHtml(repo.path) + '</small>' +
                        '</label>' +
                        '</div>';
            }

            foundReposContainer.innerHTML = html;

            SvnTool.NotificationManager.success('Scan terminé',
                'Trouvé ' + repositories.length + ' dépôts dans ' + meta.scan_path);
        },

        toggleAllFoundRepos: function(checked) {
            var checkboxes = document.querySelectorAll('.repo-checkbox');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = checked;
            }
        },

        addSelectedRepositories: function() {
            var checkboxes = document.querySelectorAll('.repo-checkbox:checked');
            if (checkboxes.length === 0) {
                SvnTool.NotificationManager.warning('Attention', 'Aucun dépôt sélectionné');
                return;
            }

            // Get the first selected repository path and set it as base path
            var firstRepoPath = checkboxes[0].value;
            var basePath = firstRepoPath.substring(0, firstRepoPath.lastIndexOf('/'));

            // Update the base path input
            var repoPathInput = document.getElementById('repo_base_path');
            if (repoPathInput) {
                repoPathInput.value = basePath;
            }

            // Close modal and refresh repositories
            this.closeModal();
            SvnTool.loadRepositories();

            SvnTool.NotificationManager.success('Succès',
                'Chemin de base mis à jour: ' + basePath);
        }
    }
};

// Initialize the application when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        SvnTool.init();
    });
} else {
    SvnTool.init();
}
