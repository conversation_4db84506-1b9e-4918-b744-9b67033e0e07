<?php
/**
 * SVN Tool Web Access Test
 * 
 * Tests the SVN tool as if accessed via web browser
 */

echo "=== SVN Tool Web Access Test ===\n\n";

// Test 1: Test index.php output
echo "1. Testing index.php web output...\n";
ob_start();
try {
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['HTTP_HOST'] = 'localhost';
    $_SERVER['REQUEST_URI'] = '/svn-tool/';
    
    include 'index.php';
    $output = ob_get_clean();
    
    if (strpos($output, 'SVN Tool - Gestion des dépôts') !== false) {
        echo "   ✓ Index page loads successfully\n";
        echo "   ✓ Page title found\n";
    } else {
        echo "   ✗ Index page failed to load properly\n";
    }
    
    if (strpos($output, 'assets/css/svn-tool.css') !== false) {
        echo "   ✓ CSS file reference found\n";
    } else {
        echo "   ✗ CSS file reference missing\n";
    }
    
    if (strpos($output, 'assets/js/svn-tool.js') !== false) {
        echo "   ✓ JavaScript file reference found\n";
    } else {
        echo "   ✗ JavaScript file reference missing\n";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "   ✗ Index page error: " . $e->getMessage() . "\n";
}

// Test 2: Test API endpoints via HTTP simulation
echo "\n2. Testing API endpoints via HTTP simulation...\n";

// Test basic API
echo "   Testing API test endpoint...\n";
$_GET = array('action' => 'test');
$_SERVER['REQUEST_METHOD'] = 'GET';
ob_start();
try {
    include 'api.php';
    $apiOutput = ob_get_clean();
    $data = json_decode($apiOutput, true);
    
    if ($data && $data['status'] === 'success') {
        echo "   ✓ API test endpoint works\n";
        echo "   ✓ API returns valid JSON\n";
    } else {
        echo "   ✗ API test endpoint failed\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "   ✗ API test error: " . $e->getMessage() . "\n";
}

// Test repository listing API
echo "   Testing repository listing API...\n";
$_GET = array('action' => 'list_repos', 'repo_base_path' => '/var/www/html');
$_SERVER['REQUEST_METHOD'] = 'GET';
ob_start();
try {
    include 'api.php';
    $apiOutput = ob_get_clean();
    $data = json_decode($apiOutput, true);
    
    if ($data && isset($data['status'])) {
        echo "   ✓ Repository listing API responds\n";
        echo "   ✓ Status: " . $data['status'] . "\n";
        if (isset($data['count'])) {
            echo "   ✓ Found " . $data['count'] . " repositories\n";
        }
    } else {
        echo "   ✗ Repository listing API failed\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "   ✗ Repository listing error: " . $e->getMessage() . "\n";
}

// Test 3: Check static assets
echo "\n3. Testing static assets...\n";

$assets = array(
    'assets/css/svn-tool.css' => 'CSS file',
    'assets/js/svn-tool.js' => 'JavaScript file'
);

foreach ($assets as $asset => $description) {
    if (file_exists($asset)) {
        $size = filesize($asset);
        echo "   ✓ $description exists ($size bytes)\n";
        
        // Check if file is readable
        if (is_readable($asset)) {
            echo "   ✓ $description is readable\n";
        } else {
            echo "   ✗ $description is not readable\n";
        }
    } else {
        echo "   ✗ $description not found\n";
    }
}

// Test 4: Test CSRF token functionality
echo "\n4. Testing CSRF token functionality...\n";
session_start();
try {
    // Test CSRF token generation
    require_once 'lib/helpers.php';
    $token1 = generateCsrfToken();
    $token2 = generateCsrfToken();
    
    if ($token1 === $token2) {
        echo "   ✓ CSRF token consistency works\n";
    } else {
        echo "   ✗ CSRF token inconsistency detected\n";
    }
    
    if (strlen($token1) === 64) {
        echo "   ✓ CSRF token has correct length\n";
    } else {
        echo "   ✗ CSRF token has incorrect length: " . strlen($token1) . "\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ CSRF token error: " . $e->getMessage() . "\n";
}

// Test 5: Test configuration access
echo "\n5. Testing configuration access...\n";
try {
    require_once 'config/config.php';
    
    $appName = config('app.name');
    $repoPath = config('repository.base_path');
    $svnBinary = config('svn.binary_path');
    
    if (!empty($appName)) {
        echo "   ✓ App name configuration: $appName\n";
    } else {
        echo "   ✗ App name configuration missing\n";
    }
    
    if (!empty($repoPath)) {
        echo "   ✓ Repository path configuration: $repoPath\n";
    } else {
        echo "   ✗ Repository path configuration missing\n";
    }
    
    if (!empty($svnBinary)) {
        echo "   ✓ SVN binary path configuration: $svnBinary\n";
    } else {
        echo "   ✗ SVN binary path configuration missing\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ Configuration error: " . $e->getMessage() . "\n";
}

// Test 6: Test URL routing compatibility
echo "\n6. Testing URL routing compatibility...\n";

// Simulate different URL patterns
$urlTests = array(
    '/svn-tool/' => 'Root directory access',
    '/svn-tool/index.php' => 'Direct index.php access',
    '/svn-tool/api.php?action=test' => 'API endpoint access'
);

foreach ($urlTests as $url => $description) {
    echo "   Testing $description ($url)...\n";
    
    // Check if the URL pattern would work with current .htaccess
    if (strpos($url, 'api.php') !== false) {
        echo "   ✓ API URL should work directly\n";
    } elseif (strpos($url, 'index.php') !== false) {
        echo "   ✓ Index URL should work directly\n";
    } else {
        echo "   ✓ Root URL should be handled by .htaccess rewrite\n";
    }
}

echo "\n=== Web Access Test Complete ===\n";
echo "The SVN Tool should now be accessible via web browser!\n";
echo "\nAccess URLs:\n";
echo "- Main interface: http://localhost/svn-tool/\n";
echo "- Direct access: http://localhost/svn-tool/index.php\n";
echo "- API test: http://localhost/svn-tool/api.php?action=test\n";
echo "\nIf you're still experiencing URL redirection issues, check:\n";
echo "1. Apache virtual host configuration\n";
echo "2. Parent directory .htaccess files\n";
echo "3. Docker container port mapping\n";
echo "4. Browser cache (try incognito/private mode)\n";
?>
