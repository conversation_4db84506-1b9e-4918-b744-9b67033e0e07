<?php
/**
 * SVN Tool - Main Entry Point
 *
 * Refactored for PHP 5.6 compatibility and RiaShop integration
 */

// Initialize session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Define access constant and root path
define('SVN_TOOL_ROOT', __DIR__);
define('SVN_TOOL_ACCESS', true);

// Include SVN tool specific files
require_once 'lib/helpers.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Generate CSRF token (PHP 5.6 compatible)
function generateSvnCsrfToken() {
    if (!isset($_SESSION['svn_csrf_token'])) {
        // PHP 5.6 compatible random token generation
        $_SESSION['svn_csrf_token'] = bin2hex(openssl_random_pseudo_bytes(32));
    }
    return $_SESSION['svn_csrf_token'];
}

$csrfToken = generateSvnCsrfToken();

// Get repository base path
$repoBasePath = '';
if (isset($_POST['repo_base_path']) && !empty($_POST['repo_base_path'])) {
    $repoBasePath = trim($_POST['repo_base_path']);
    $_SESSION['repo_base_path'] = $repoBasePath;
} elseif (isset($_SESSION['repo_base_path'])) {
    $repoBasePath = $_SESSION['repo_base_path'];
} else {
    // Set default repository path for Docker container
    $repoBasePath = '/var/www/sites';
    $_SESSION['repo_base_path'] = $repoBasePath;
}

// Use standalone layout by default for better compatibility
$useAdminSkin = false;

// Only try admin skin if explicitly requested and all dependencies exist
if (isset($_GET['admin_skin']) && $_GET['admin_skin'] === '1') {
    if (file_exists('../../include/admin/skin/header.inc.php') &&
        file_exists('../../view/admin/header.inc.php') &&
        file_exists('../../view/admin/Notices.inc.php')) {
        try {
            // Set page title for admin skin
            define('ADMIN_PAGE_TITLE', 'SVN Tool - Gestion des dépôts');

            // Try to include required RiaShop files
            if (file_exists('../../include/http.inc.php')) {
                require_once('../../include/http.inc.php');
            }
            if (file_exists('../../include/tenants.inc.php')) {
                require_once('../../include/tenants.inc.php');
            }
            if (file_exists('../../include/websites.inc.php')) {
                require_once('../../include/websites.inc.php');
            }
            if (file_exists('../../include/users.inc.php')) {
                require_once('../../include/users.inc.php');
            }

            require_once('../../include/admin/skin/header.inc.php');
            $useAdminSkin = true;
        } catch (Exception $e) {
            // Fall back to standalone layout
            $useAdminSkin = false;
        }
    }
}

if (!$useAdminSkin) {
    // Standalone HTML layout
    ?>
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>SVN Tool - Gestion des dépôts</title>
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;800&family=Rubik&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Montserrat', sans-serif;
                margin: 0;
                padding: 0;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                min-height: 100vh;
            }
            .standalone-header {
                background: rgba(255, 255, 255, 0.25);
                backdrop-filter: blur(16px);
                -webkit-backdrop-filter: blur(16px);
                border-bottom: 1px solid rgba(255, 255, 255, 0.18);
                color: #1e293b;
                padding: 2rem;
                margin: 0;
                box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
            }
            .standalone-header h1 {
                margin: 0;
                font-size: 1.5rem;
                font-weight: 600;
                letter-spacing: -0.025em;
                color: #1e293b;
            }
        </style>
    </head>
    <body>
        <div class="standalone-header">
            <h1>SVN Tool - Gestion des dépôts</h1>
        </div>
    <?php
}
?>

<link rel="stylesheet" href="assets/css/svn-tool.css">

<div class="container">
    <?php if ($useAdminSkin): ?>
    <div style="margin-bottom: 20px;">
        <button class="button button-primary" onclick="window.location.href='../maintenance/'">
            Retour au dashboard
        </button>
    </div>
    <?php endif; ?>

    <?php if (!$useAdminSkin): ?>
    <h1>SVN Tool - Gestion des dépôts</h1>
    <?php endif; ?>

    <!-- Workflow Container -->
    <div class="workflow-container">

        <!-- Step 1: Configuration -->
        <div class="workflow-step">
            <div class="step-indicator">
                <div class="step-number">1</div>
                <div class="step-title">Configuration</div>
            </div>
            <div class="card">
                <div class="card-header">
                    <h2>Configuration du dépôt</h2>
                </div>
                <div class="card-body">
                    <form method="post" action="" id="config-form">
                        <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrfToken); ?>">
                        <div class="form-group">
                            <label for="repo_base_path">Chemin de base des dépôts SVN</label>
                            <input type="text"
                                   id="repo_base_path"
                                   name="repo_base_path"
                                   class="form-control"
                                   value="<?php echo htmlspecialchars($repoBasePath); ?>"
                                   placeholder="/var/www/repositories"
                                   required>
                            <small class="form-text">
                                Entrez le chemin absolu vers votre répertoire de dépôts SVN<br>
                                <strong>Suggestions pour Docker:</strong> /var/www/sites/front, /var/www/sites, /var/www/riashop, /var/www/engine
                            </small>
                        </div>
                        <div class="button-group">
                            <button type="submit" class="button button-primary">
                                Sauvegarder la configuration
                            </button>
                            <button type="button" class="button button-secondary" onclick="SvnTool.ServerScanner.openModal()">
                                Scanner le serveur
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <?php if (empty($repoBasePath)): ?>
            <div class="alert alert-warning">
                <strong>Configuration requise</strong><br>
                Veuillez définir le chemin de base de vos dépôts ci-dessus pour commencer à utiliser l'outil SVN.
            </div>
        <?php else: ?>

            <!-- Step 2: Repository Selection -->
            <div class="workflow-step">
                <div class="step-indicator">
                    <div class="step-number">2</div>
                    <div class="step-title">Sélection du dépôt</div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h3>Dépôts disponibles</h3>
                        <div class="button-group">
                            <button id="refresh-repos" class="button button-secondary button-sm">
                                Actualiser
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="repo-list" class="list-group">
                            <div class="list-group-empty">
                                Chargement des dépôts...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: File Management -->
            <div class="workflow-step">
                <div class="step-indicator">
                    <div class="step-number">3</div>
                    <div class="step-title">Gestion des fichiers</div>
                </div>
                <div class="grid grid-cols-2">
                    <!-- File Status Panel -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Statut des fichiers</h3>
                            <div class="button-group">
                                <button id="check-permissions" class="button button-secondary button-sm">
                                    Permissions
                                </button>
                                <button id="fix-permissions-btn" class="button button-info button-sm">
                                    Corriger permissions
                                </button>
                                <button id="check-connectivity-btn" class="button button-primary button-sm">
                                    Test connectivité
                                </button>
                                <button id="cleanup-btn" class="button button-warning button-sm">
                                    Nettoyage
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="file-list" class="list-group">
                                <div class="list-group-empty">
                                    Sélectionnez un dépôt pour voir les fichiers
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- File Actions Panel -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Actions sur les fichiers</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="commit-message">Message de commit</label>
                                <textarea id="commit-message"
                                         class="form-control"
                                         placeholder="Entrez votre message de commit..."
                                         required></textarea>
                            </div>

                            <div class="button-group button-group-vertical">
                                <button id="commit-btn" class="button button-success" disabled>
                                    Commiter les fichiers sélectionnés
                                </button>
                                <div class="button-group">
                                    <button id="select-all-btn" class="button button-secondary button-sm">
                                        Tout sélectionner
                                    </button>
                                    <button id="deselect-all-btn" class="button button-secondary button-sm">
                                        Tout désélectionner
                                    </button>
                                </div>
                                <div class="button-group" style="margin-top: var(--space-4);">
                                    <button id="manage-credentials-btn" class="button button-info button-sm">
                                        🔐 Gérer les identifiants
                                    </button>
                                    <button id="clear-credentials-btn" class="button button-warning button-sm" style="display: none;">
                                        🗑️ Effacer les identifiants
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Diff Viewer -->
            <div class="workflow-step">
                <div class="step-indicator">
                    <div class="step-number">4</div>
                    <div class="step-title">Visualisation des différences</div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h3>Visualiseur de différences</h3>
                        <div class="button-group">
                            <button id="copy-diff-btn" class="button button-secondary button-sm" style="display: none;">
                                Copier le diff
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="diff-viewer" style="display: none;">
                            <div id="diff-header">
                                <div id="diff-filename"></div>
                                <div id="diff-stats"></div>
                            </div>
                            <div id="diff-content"></div>
                        </div>
                        <div id="diff-placeholder">
                            <p>Cliquez sur un fichier pour voir ses différences</p>
                            <small>Les modifications seront surlignées avec coloration syntaxique</small>
                        </div>
                    </div>
                </div>
            </div>

        </div> <!-- End workflow-container -->
        <?php endif; ?>
</div>

<!-- Server Scanner Modal -->
<div id="server-scanner-modal" class="modal" style="display: none;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Scanner de dépôts SVN</h4>
                <button type="button" class="close" onclick="SvnTool.ServerScanner.closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="scanner-controls">
                    <div class="form-group">
                        <label for="scan-path">Chemin à scanner</label>
                        <input type="text"
                               id="scan-path"
                               class="form-control"
                               value="/var/www"
                               placeholder="/var/www">
                        <small class="form-text">Entrez le chemin racine à scanner pour les dépôts SVN</small>
                    </div>
                    <div class="form-group">
                        <label for="scan-depth">Profondeur maximale</label>
                        <select id="scan-depth" class="form-control">
                            <option value="3">3 niveaux (Recommandé)</option>
                            <option value="5">5 niveaux</option>
                            <option value="7">7 niveaux</option>
                            <option value="10">10 niveaux (Lent)</option>
                        </select>
                        <small class="form-text">Les scans plus profonds prennent plus de temps mais trouvent plus de dépôts</small>
                    </div>
                    <div class="button-group">
                        <button id="start-scan-btn" class="button button-primary button-lg">
                            Démarrer le scan
                        </button>
                    </div>
                </div>

                <div id="scan-progress" style="display: none; margin-top: 20px;">
                    <div class="form-group">
                        <label>Progression du scan</label>
                        <div class="progress">
                            <div id="progress-fill" class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%;"></div>
                        </div>
                        <div id="scan-status" style="margin-top: 10px;">
                            Initialisation du scan...
                        </div>
                    </div>
                </div>

                <div id="scan-results" style="display: none; margin-top: 20px;">
                    <h5>Dépôts SVN trouvés <span id="found-count" class="badge badge-success">0</span></h5>
                    <div style="max-height: 300px; overflow-y: auto;">
                        <div>
                            <label>
                                <input type="checkbox" id="select-all-found">
                                Sélectionner tous les dépôts trouvés
                            </label>
                        </div>
                        <div id="found-repos"></div>
                    </div>
                </div>
            </div>
            <div id="modal-footer" class="modal-footer" style="display: none;">
                <button id="cancel-scan-btn" class="button button-secondary">Annuler</button>
                <button id="add-selected-repos" class="button button-success">
                    Ajouter les dépôts sélectionnés
                </button>
            </div>
        </div>
    </div>
</div>

<!-- SVN Authentication Modal -->
<div id="svn-auth-modal" class="auth-modal">
    <div class="auth-modal-dialog">
        <div class="auth-modal-content">
            <div class="auth-modal-header">
                <h4 class="auth-modal-title">
                    <span>🔐</span>
                    Authentification SVN requise
                </h4>
                <button type="button" class="close" onclick="SvnTool.AuthManager.closeModal()">&times;</button>
            </div>
            <div class="auth-modal-body">
                <div id="auth-error" class="auth-error">
                    <strong>Erreur d'authentification</strong><br>
                    <span id="auth-error-message">Nom d'utilisateur ou mot de passe incorrect.</span>
                </div>

                <div class="auth-info">
                    <strong>Authentification requise</strong><br>
                    Cette opération SVN nécessite une authentification. Veuillez entrer vos identifiants SVN.
                </div>

                <form id="auth-form" class="auth-form">
                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrfToken); ?>">

                    <div class="form-group">
                        <label for="svn-username">Nom d'utilisateur SVN</label>
                        <input type="text"
                               id="svn-username"
                               name="username"
                               class="form-control"
                               placeholder="Votre nom d'utilisateur SVN"
                               required
                               autocomplete="username">
                        <small class="form-text">Nom d'utilisateur pour l'accès au dépôt SVN</small>
                    </div>

                    <div class="form-group">
                        <label for="svn-password">Mot de passe SVN</label>
                        <input type="password"
                               id="svn-password"
                               name="password"
                               class="form-control"
                               placeholder="Votre mot de passe SVN"
                               required
                               autocomplete="current-password">
                        <small class="form-text">Mot de passe pour l'accès au dépôt SVN</small>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="remember-credentials" name="remember" value="1" checked>
                            Mémoriser les identifiants pour cette session
                        </label>
                        <small class="form-text">Les identifiants seront stockés de manière sécurisée pour cette session uniquement</small>
                    </div>
                </form>

                <div id="credential-status" class="credential-status missing">
                    <div class="credential-status-icon"></div>
                    <span>Aucun identifiant stocké</span>
                </div>
            </div>
            <div class="auth-modal-footer">
                <button type="button" class="button button-secondary" onclick="SvnTool.AuthManager.closeModal()">
                    Annuler
                </button>
                <button type="button" id="auth-submit-btn" class="button button-primary" onclick="SvnTool.AuthManager.submitCredentials()">
                    Authentifier
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Notification Container -->
<div id="notification-container"></div>

<script src="assets/js/svn-tool.js"></script>
<!-- SVN Tool initialization is handled within svn-tool.js itself -->

<?php
if ($useAdminSkin && file_exists('../../include/admin/skin/footer.inc.php')) {
    // Include admin footer
    require_once('../../include/admin/skin/footer.inc.php');
} else {
    // Standalone footer
    ?>
    </body>
    </html>
    <?php
}
?>
