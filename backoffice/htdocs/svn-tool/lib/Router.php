<?php
/**
 * Simple Router Class with proper base path handling
 */

class Router
{
    private $routes = array();
    private $basePath = '';
    
    public function __construct()
    {
        // Auto-detect base path from the current script location
        $this->basePath = $this->detectBasePath();
    }
    
    public function addRoute($method, $path, $handler)
    {
        $this->routes[] = array(
            'method' => strtoupper($method),
            'path' => $path,
            'handler' => $handler
        );
    }
    
    public function handleRequest()
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $this->getCurrentPath();
        
        // Debug logging
        error_log("Router: Base path detected as: {$this->basePath}");
        error_log("Router: Handling {$method} request for path: {$path}");
        error_log("Router: Original REQUEST_URI: {$_SERVER['REQUEST_URI']}");
        
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && $this->matchPath($route['path'], $path)) {
                error_log("Router: Matched route {$route['path']} -> {$route['handler']}");
                return $this->executeHandler($route['handler']);
            }
        }
        
        // 404 Not Found
        error_log("Router: No route found for {$method} {$path}");
        error_log("Router: Available routes: " . print_r(array_map(function($r) { 
            return $r['method'] . ' ' . $r['path']; 
        }, $this->routes), true));
        
        http_response_code(404);
        if (isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode(array(
                'status' => 'error', 
                'message' => 'Endpoint not found',
                'debug' => array(
                    'requested_path' => $path,
                    'method' => $method,
                    'base_path' => $this->basePath
                )
            ));
        } else {
            echo '404 - Page Not Found';
        }
    }
    
    private function detectBasePath()
    {
        // Get the directory where index.php is located
        $scriptName = $_SERVER['SCRIPT_NAME'];
        $basePath = dirname($scriptName);
        
        // Normalize the path
        $basePath = str_replace('\\', '/', $basePath);
        $basePath = rtrim($basePath, '/');
        
        // If we're at document root, base path should be empty
        if ($basePath === '.' || $basePath === '') {
            return '';
        }
        
        return $basePath;
    }
    
    private function getCurrentPath()
    {
        $requestUri = $_SERVER['REQUEST_URI'];
        
        // Remove query string
        $path = parse_url($requestUri, PHP_URL_PATH);
        
        // Remove base path if it exists
        if (!empty($this->basePath)) {
            // Check if the path starts with our base path
            if (strpos($path, $this->basePath) === 0) {
                $path = substr($path, strlen($this->basePath));
            }
        }
        
        // Ensure path starts with /
        if (empty($path) || $path === '') {
            return '/';
        }
        
        if ($path[0] !== '/') {
            $path = '/' . $path;
        }
        
        // Remove trailing slash except for root
        if ($path !== '/') {
            $path = rtrim($path, '/');
        }
        
        return $path;
    }
    
    private function matchPath($routePath, $currentPath)
    {
        return $routePath === $currentPath;
    }
    
    private function executeHandler($handler)
    {
        if (is_string($handler) && strpos($handler, '@') !== false) {
            $parts = explode('@', $handler);
            $controllerName = $parts[0];
            $methodName = $parts[1];
            
            if (!class_exists($controllerName)) {
                throw new Exception("Controller class {$controllerName} not found");
            }
            
            $controller = new $controllerName();
            
            if (!method_exists($controller, $methodName)) {
                throw new Exception("Method {$methodName} not found in {$controllerName}");
            }
            
            return $controller->$methodName();
        }
        
        throw new Exception('Invalid route handler: ' . $handler);
    }
    
    /**
     * Get the base URL for the application
     */
    public function getBaseUrl()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        return $protocol . '://' . $host . $this->basePath;
    }
    
    /**
     * Generate a URL relative to the application base
     */
    public function url($path = '')
    {
        $path = ltrim($path, '/');
        return $this->basePath . ($path ? '/' . $path : '');
    }
}