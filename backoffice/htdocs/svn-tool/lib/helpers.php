<?php
/**
 * Helper Functions
 */

/**
 * Generate URL relative to application base
 */
function url($path = '') {
    global $router;
    if (isset($router) && method_exists($router, 'url')) {
        return $router->url($path);
    }
    
    // Fallback URL generation
    $basePath = dirname($_SERVER['SCRIPT_NAME']);
    $basePath = str_replace('\\', '/', $basePath);
    $basePath = rtrim($basePath, '/');
    
    $path = ltrim($path, '/');
    return $basePath . ($path ? '/' . $path : '');
}

/**
 * Get base URL for the application
 */
function baseUrl() {
    global $router;
    if (isset($router) && method_exists($router, 'getBaseUrl')) {
        return $router->getBaseUrl();
    }
    
    // Fallback base URL generation
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $basePath = dirname($_SERVER['SCRIPT_NAME']);
    $basePath = str_replace('\\', '/', $basePath);
    $basePath = rtrim($basePath, '/');
    
    return $protocol . '://' . $host . $basePath;
}

/**
 * Check if request is AJAX
 */
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Send JSON response (only if not already defined)
 */
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

/**
 * Load configuration (only if not already defined)
 */
if (!function_exists('config')) {
    function config($key = null) {
        static $config = null;

        if ($config === null) {
            $configFile = SVN_TOOL_ROOT . '/config/config.php';
            if (file_exists($configFile)) {
                $config = require $configFile;
            } else {
                $config = array();
            }
        }

        if ($key === null) {
            return $config;
        }

        $keys = explode('.', $key);
        $value = $config;

        foreach ($keys as $k) {
            if (isset($value[$k])) {
                $value = $value[$k];
            } else {
                return null;
            }
        }

        return $value;
    }
}

/**
 * Load view template
 */
function view($template, $data = array()) {
    $viewFile = SVN_TOOL_ROOT . '/views/' . $template . '.php';
    
    if (!file_exists($viewFile)) {
        throw new Exception("View template not found: {$template}");
    }
    
    // Extract data to variables
    extract($data);
    
    // Start output buffering
    ob_start();
    
    // Include the view file
    include $viewFile;
    
    // Return the buffered content
    return ob_get_clean();
}

/**
 * Sanitize output for HTML
 */
function sanitizeOutput($value) {
    return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
}

/**
 * Generate CSRF token (PHP 5.6 compatible)
 */
function generateCsrfToken() {
    if (!isset($_SESSION['csrf_token'])) {
        // PHP 5.6 compatible random token generation
        $_SESSION['csrf_token'] = bin2hex(openssl_random_pseudo_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Validate CSRF token
 */
function validateCsrfToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Check if path is safe (within allowed directory) (only if not already defined)
 */
if (!function_exists('isPathSafe')) {
    function isPathSafe($path, $allowedBasePath) {
    $realPath = realpath($path);
    $realBasePath = realpath($allowedBasePath);
    
    if ($realPath === false || $realBasePath === false) {
        return false;
    }
    
    return strpos($realPath, $realBasePath) === 0;
    }
}

/**
 * Format file size (only if not already defined)
 */
if (!function_exists('formatFileSize')) {
    function formatFileSize($bytes) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}

/**
 * Get current timestamp for logging
 */
function getTimestamp() {
    return date('Y-m-d H:i:s');
}

/**
 * Log debug message
 */
function debugLog($message, $context = array()) {
    if (config('app.debug')) {
        $contextStr = empty($context) ? '' : ' ' . json_encode($context);
        error_log('[SVN Tool Debug] ' . getTimestamp() . ': ' . $message . $contextStr);
    }
}

/**
 * Generate URL for static assets (CSS, JS, images)
 */
function asset($path = '') {
    global $router;
    
    // Remove leading slash if present
    $path = ltrim($path, '/');
    
    if (isset($router) && method_exists($router, 'url')) {
        return $router->url('assets/' . $path);
    }
    
    // Fallback asset URL generation
    $basePath = dirname($_SERVER['SCRIPT_NAME']);
    $basePath = str_replace('\\', '/', $basePath);
    $basePath = rtrim($basePath, '/');
    
    return $basePath . '/assets/' . $path;
}

/**
 * Generate versioned asset URL with cache busting
 */
function assetVersioned($path = '', $version = null) {
    $assetUrl = asset($path);
    
    if ($version === null) {
        // Try to get version from config
        $version = config('app.version');
        if (!$version) {
            // Fallback to file modification time for cache busting
            $filePath = SVN_TOOL_ROOT . '/assets/' . ltrim($path, '/');
            if (file_exists($filePath)) {
                $version = filemtime($filePath);
            } else {
                $version = '1.0.0';
            }
        }
    }
    
    $separator = strpos($assetUrl, '?') !== false ? '&' : '?';
    return $assetUrl . $separator . 'v=' . $version;
}
