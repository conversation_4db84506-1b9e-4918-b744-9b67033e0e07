<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test SVN Tool JavaScript</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 8px 15px; }
        #test-results { margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>SVN Tool JavaScript Functionality Test</h1>
    
    <div class="test-section">
        <h3>1. Test Server Scanner API</h3>
        <button onclick="testServerScanner()">Test Server Scanner</button>
        <div id="scanner-result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Test File Diff API</h3>
        <button onclick="testFileDiff()">Test File Diff</button>
        <div id="diff-result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Test Repository Status API</h3>
        <button onclick="testRepoStatus()">Test Repository Status</button>
        <div id="status-result"></div>
    </div>
    
    <div id="test-results">
        <h3>Test Results:</h3>
        <div id="results-content">Click buttons above to run tests...</div>
    </div>

    <script>
        // Simple API request function for testing
        function makeApiRequest(url, callback) {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var data = JSON.parse(xhr.responseText);
                            callback(null, data);
                        } catch (e) {
                            callback(new Error('Invalid JSON: ' + xhr.responseText), null);
                        }
                    } else {
                        callback(new Error('HTTP ' + xhr.status + ': ' + xhr.statusText), null);
                    }
                }
            };
            
            xhr.onerror = function() {
                callback(new Error('Network error'), null);
            };
            
            xhr.send();
        }
        
        function logResult(elementId, message, type) {
            var element = document.getElementById(elementId);
            var className = type === 'success' ? 'success' : (type === 'error' ? 'error' : 'info');
            element.innerHTML = '<div class="' + className + '">' + message + '</div>';
            
            // Also log to results
            var resultsContent = document.getElementById('results-content');
            resultsContent.innerHTML += '<div class="' + className + '">[' + new Date().toLocaleTimeString() + '] ' + message + '</div>';
        }
        
        function testServerScanner() {
            logResult('scanner-result', 'Testing server scanner...', 'info');
            
            makeApiRequest('api.php?action=server_scan&path=/var/www&depth=2', function(error, data) {
                if (error) {
                    logResult('scanner-result', 'Server Scanner FAILED: ' + error.message, 'error');
                } else if (data.status === 'success') {
                    logResult('scanner-result', 'Server Scanner SUCCESS: Found ' + data.data.length + ' repositories', 'success');
                } else {
                    logResult('scanner-result', 'Server Scanner FAILED: ' + (data.message || 'Unknown error'), 'error');
                }
            });
        }
        
        function testFileDiff() {
            logResult('diff-result', 'Testing file diff...', 'info');
            
            makeApiRequest('api.php?action=get_diff&path=/var/www/sites/front&file=htdocs/include/actions.site.inc.php', function(error, data) {
                if (error) {
                    logResult('diff-result', 'File Diff FAILED: ' + error.message, 'error');
                } else if (data.status === 'success') {
                    var hasChanges = data.data.has_changes ? 'YES' : 'NO';
                    logResult('diff-result', 'File Diff SUCCESS: Has changes: ' + hasChanges, 'success');
                } else {
                    logResult('diff-result', 'File Diff FAILED: ' + (data.message || 'Unknown error'), 'error');
                }
            });
        }
        
        function testRepoStatus() {
            logResult('status-result', 'Testing repository status...', 'info');
            
            makeApiRequest('api.php?action=get_status&path=/var/www/sites/front', function(error, data) {
                if (error) {
                    logResult('status-result', 'Repository Status FAILED: ' + error.message, 'error');
                } else if (data.status === 'success') {
                    logResult('status-result', 'Repository Status SUCCESS: Found ' + data.data.length + ' files', 'success');
                } else {
                    logResult('status-result', 'Repository Status FAILED: ' + (data.message || 'Unknown error'), 'error');
                }
            });
        }
        
        // Auto-run tests on page load
        window.onload = function() {
            logResult('results-content', 'Page loaded, ready for testing...', 'info');
        };
    </script>
</body>
</html>
