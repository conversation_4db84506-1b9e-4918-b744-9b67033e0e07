<?php
/**
 * SVN Tool Configuration
 */

// Prevent direct access
if (!defined('SVN_TOOL_ACCESS')) {
    define('SVN_TOOL_ACCESS', true);
}

// Configuration array (PHP 5.6 compatible)
$config = array(
    'repository' => array(
        'base_path' => '/var/www/sites', // Default SVN repositories path for Docker container
    ),
    'svn' => array(
        'binary_path' => '/usr/bin/svn',
        'timeout' => 30
    ),
    'app' => array(
        'name' => 'SVN Web Tool',
        'version' => '1.0.0',
        'debug' => true
    ),
    'security' => array(
        'csrf_protection' => true
    )
);

/**
 * Get configuration value
 */
if (!function_exists('config')) {
    function config($key = null, $default = null) {
        global $config;

        // If no key provided, return entire config
        if ($key === null) {
            return $config;
        }

        $keys = explode('.', $key);
        $current = $config;
        
        foreach ($keys as $k) {
            if (!isset($current[$k])) {
                return $default;
            }
            $current = $current[$k];
        }
        
        return $current;
    }
}

/**
 * Set configuration value
 */
if (!function_exists('setConfig')) {
    function setConfig($key, $value) {
        global $config;
        
        $keys = explode('.', $key);
        $current = &$config;
        
        foreach ($keys as $k) {
            if (!isset($current[$k])) {
                $current[$k] = array();
            }
            $current = &$current[$k];
        }
        
        $current = $value;
    }
}

return $config;
?>
