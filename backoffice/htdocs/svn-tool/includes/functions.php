<?php
/**
 * SVN Tool Helper Functions
 */

if (!defined('SVN_TOOL_ACCESS')) {
    define('SVN_TOOL_ACCESS', true);
}

if (!function_exists('config')) {
    function config($key, $default = null) {
        global $config;

        $keys = explode('.', $key);
        $current = $config;

        foreach ($keys as $k) {
            if (!isset($current[$k])) {
                return $default;
            }
            $current = $current[$k];
        }

        return $current;
    }
}

/**
 * Send JSON response and exit (only if not already defined)
 */
if (!function_exists('jsonResponse')) {
    function jsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}

/**
 * Log message to error log
 */
function logMessage($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    error_log("[{$timestamp}] [{$level}] SVN Tool: {$message}");
}

/**
 * Sanitize file path
 */
function sanitizePath($path) {
    // Remove any dangerous characters
    $path = str_replace(['../', '..\\', '~'], '', $path);
    return trim($path);
}

/**
 * Check if path is safe (only if not already defined)
 */
if (!function_exists('isPathSafe')) {
    function isPathSafe($path, $basePath) {
        $realPath = realpath($path);
        $realBasePath = realpath($basePath);

        if ($realPath === false || $realBasePath === false) {
            return false;
        }

        return strpos($realPath, $realBasePath) === 0;
    }
}

/**
 * Format file size (only if not already defined)
 */
if (!function_exists('formatFileSize')) {
    function formatFileSize($bytes) {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
}
?>
