<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVN Tool Debug</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #f5f5f5; }
        .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>SVN Tool Debug Console</h1>
    
    <div class="test-section">
        <h2>API Connectivity Tests</h2>
        <button class="btn-primary" onclick="testBasicAPI()">Test Basic API</button>
        <button class="btn-success" onclick="testRepositoryAPI()">Test Repository API</button>
        <button class="btn-warning" onclick="testServerScan()">Test Server Scanner</button>
        <div id="api-results"></div>
    </div>

    <div class="test-section">
        <h2>JavaScript Functionality Tests</h2>
        <button class="btn-primary" onclick="testEventBinding()">Test Event Binding</button>
        <button class="btn-success" onclick="testNotifications()">Test Notifications</button>
        <button class="btn-warning" onclick="testModalFunctions()">Test Modal Functions</button>
        <div id="js-results"></div>
    </div>

    <div class="test-section">
        <h2>Configuration Check</h2>
        <button class="btn-primary" onclick="checkConfiguration()">Check Configuration</button>
        <div id="config-results"></div>
    </div>

    <script>
        async function testBasicAPI() {
            const results = document.getElementById('api-results');
            results.innerHTML = '<div class="test-result info">Testing basic API connectivity...</div>';
            
            try {
                const response = await fetch('test-api.php');
                const data = await response.json();
                
                if (data.status === 'success') {
                    results.innerHTML += `<div class="test-result success">✅ Basic API: ${data.message}</div>`;
                    results.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    results.innerHTML += `<div class="test-result error">❌ Basic API failed: ${data.message}</div>`;
                }
            } catch (error) {
                results.innerHTML += `<div class="test-result error">❌ API Error: ${error.message}</div>`;
            }
        }

        async function testRepositoryAPI() {
            const results = document.getElementById('api-results');
            results.innerHTML += '<div class="test-result info">Testing repository API...</div>';
            
            try {
                const response = await fetch('test-api.php?action=list_repos');
                const data = await response.json();
                
                if (data.status === 'success') {
                    results.innerHTML += `<div class="test-result success">✅ Repository API: Found ${data.count} repositories</div>`;
                    results.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    results.innerHTML += `<div class="test-result error">❌ Repository API failed: ${data.message}</div>`;
                }
            } catch (error) {
                results.innerHTML += `<div class="test-result error">❌ Repository API Error: ${error.message}</div>`;
            }
        }

        async function testServerScan() {
            const results = document.getElementById('api-results');
            results.innerHTML += '<div class="test-result info">Testing server scanner...</div>';
            
            try {
                const response = await fetch('api/server-scan.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        path: '/tmp',
                        depth: 1
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    results.innerHTML += `<div class="test-result success">✅ Server Scanner: Found ${data.data.length} repositories</div>`;
                } else {
                    results.innerHTML += `<div class="test-result error">❌ Server Scanner failed: ${data.message}</div>`;
                }
                results.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                results.innerHTML += `<div class="test-result error">❌ Server Scanner Error: ${error.message}</div>`;
            }
        }

        function testEventBinding() {
            const results = document.getElementById('js-results');
            results.innerHTML = '<div class="test-result info">Testing event binding...</div>';
            
            // Test if SvnApp is loaded
            if (typeof SvnApp !== 'undefined') {
                results.innerHTML += '<div class="test-result success">✅ SvnApp object is loaded</div>';
                
                // Test if methods exist
                const methods = ['init', 'loadRepositories', 'bindEvents', 'apiRequest'];
                methods.forEach(method => {
                    if (typeof SvnApp[method] === 'function') {
                        results.innerHTML += `<div class="test-result success">✅ Method ${method} exists</div>`;
                    } else {
                        results.innerHTML += `<div class="test-result error">❌ Method ${method} missing</div>`;
                    }
                });
                
                // Test if components exist
                const components = ['NotificationManager', 'ServerScanner'];
                components.forEach(component => {
                    if (SvnApp[component]) {
                        results.innerHTML += `<div class="test-result success">✅ Component ${component} exists</div>`;
                    } else {
                        results.innerHTML += `<div class="test-result error">❌ Component ${component} missing</div>`;
                    }
                });
                
            } else {
                results.innerHTML += '<div class="test-result error">❌ SvnApp object not found</div>';
            }
        }

        function testNotifications() {
            const results = document.getElementById('js-results');
            
            if (typeof SvnApp !== 'undefined' && SvnApp.NotificationManager) {
                SvnApp.NotificationManager.success('Test', 'This is a test success notification');
                SvnApp.NotificationManager.error('Test', 'This is a test error notification');
                SvnApp.NotificationManager.warning('Test', 'This is a test warning notification');
                SvnApp.NotificationManager.info('Test', 'This is a test info notification');
                
                results.innerHTML += '<div class="test-result success">✅ Notification tests triggered</div>';
            } else {
                results.innerHTML += '<div class="test-result error">❌ NotificationManager not available</div>';
            }
        }

        function testModalFunctions() {
            const results = document.getElementById('js-results');
            
            if (typeof SvnApp !== 'undefined' && SvnApp.ServerScanner) {
                // Test modal opening
                setTimeout(() => {
                    SvnApp.ServerScanner.openModal();
                    results.innerHTML += '<div class="test-result success">✅ Server scanner modal opened</div>';
                    
                    // Close it after 2 seconds
                    setTimeout(() => {
                        SvnApp.ServerScanner.closeModal();
                        results.innerHTML += '<div class="test-result success">✅ Server scanner modal closed</div>';
                    }, 2000);
                }, 500);
            } else {
                results.innerHTML += '<div class="test-result error">❌ ServerScanner not available</div>';
            }
        }

        async function checkConfiguration() {
            const results = document.getElementById('config-results');
            results.innerHTML = '<div class="test-result info">Checking configuration...</div>';
            
            try {
                const response = await fetch('test-api.php?test_repos=1');
                const data = await response.json();
                
                if (data.config_loaded) {
                    results.innerHTML += '<div class="test-result success">✅ Configuration loaded</div>';
                    results.innerHTML += `<div class="test-result info">Repository base path: ${data.repo_base_path}</div>`;
                    
                    if (data.repo_test) {
                        results.innerHTML += `<div class="test-result info">Repository test: ${data.repo_test}</div>`;
                    }
                    
                    if (data.repositories) {
                        results.innerHTML += `<pre>${JSON.stringify(data.repositories, null, 2)}</pre>`;
                    }
                } else {
                    results.innerHTML += '<div class="test-result error">❌ Configuration not loaded</div>';
                }
                
            } catch (error) {
                results.innerHTML += `<div class="test-result error">❌ Configuration check failed: ${error.message}</div>`;
            }
        }

        // Auto-run basic tests on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testBasicAPI();
                testEventBinding();
            }, 1000);
        });
    </script>
</body>
</html>