<?php
/**
 * SVN Tool API - Clean Version
 * 
 * Refactored for PHP 5.6 compatibility and proper function organization
 */

// Define access constant (only if not already defined)
if (!defined('SVN_TOOL_ACCESS')) {
    define('SVN_TOOL_ACCESS', true);
}

// Start session to access user configuration (only if not already started)
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set JSON header immediately (only if headers not already sent)
if (!headers_sent()) {
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');
}

// <PERSON>le preflight requests
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Load configuration and functions first
if (!file_exists('config/config.php')) {
    echo json_encode(array('status' => 'error', 'message' => 'Configuration file not found'));
    exit;
}

$config = require_once 'config/config.php';

// Check if functions file exists before including
if (file_exists('includes/functions.php')) {
    require_once 'includes/functions.php';
}

// Include API function files
require_once 'api-functions.php';
require_once 'api-utilities.php';

// Helper function to get config values (only if not already defined)
if (!function_exists('config')) {
    function config($key, $default = null) {
        global $config;
        
        $keys = explode('.', $key);
        $current = $config;
        
        foreach ($keys as $k) {
            if (!isset($current[$k])) {
                return $default;
            }
            $current = $current[$k];
        }
        
        return $current;
    }
}

try {
    // Get action parameter
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    if (empty($action)) {
        throw new Exception('Action parameter is required');
    }

    // Validate CSRF token for POST requests
    if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = isset($_SERVER['HTTP_X_CSRF_TOKEN']) ? $_SERVER['HTTP_X_CSRF_TOKEN'] : '';
        if (empty($token) || !isset($_SESSION['svn_csrf_token']) || $token !== $_SESSION['svn_csrf_token']) {
            throw new Exception('Invalid CSRF token');
        }
    }

    // Route to appropriate handler
    switch ($action) {
        case 'list_repos':
            handleListRepositories();
            break;
            
        case 'get_status':
            handleGetStatus();
            break;
            
        case 'commit':
            handleCommit();
            break;
            
        case 'cleanup':
            handleCleanup();
            break;
            
        case 'check_permissions':
            handleCheckPermissions();
            break;
            
        case 'get_diff':
            handleGetDiff();
            break;
            
        case 'test':
            handleTest();
            break;

        case 'server_scan':
            handleServerScan();
            break;

        case 'fix_permissions':
            handleFixPermissions();
            break;

        case 'check_connectivity':
            handleCheckConnectivity();
            break;

        case 'store_credentials':
            handleStoreCredentials();
            break;

        case 'check_credentials':
            handleCheckCredentials();
            break;

        case 'clear_credentials':
            handleClearCredentials();
            break;

        default:
            throw new Exception('Unknown action: ' . $action);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(array(
        'status' => 'error',
        'message' => $e->getMessage(),
        'debug' => array(
            'file' => basename($e->getFile()),
            'line' => $e->getLine(),
            'timestamp' => date('Y-m-d H:i:s')
        )
    ));
}
?>
