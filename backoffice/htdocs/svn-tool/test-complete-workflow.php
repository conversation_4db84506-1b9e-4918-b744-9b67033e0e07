<?php
/**
 * Test complete SVN tool workflow - both server scanner and file diff functionality
 */

// Define access constant
define('SVN_TOOL_ACCESS', true);

echo "Testing Complete SVN Tool Workflow\n";
echo "==================================\n\n";

// Test 1: Verify web interface is accessible and properly configured
echo "1. Testing Web Interface Access:\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    echo "   ✓ Web interface accessible (HTTP 200)\n";
    
    // Check for key interface elements
    $interfaceElements = array(
        'svn-interface' => 'Main SVN interface',
        'repo-list' => 'Repository list',
        'file-list' => 'File list',
        'diff-viewer' => 'Diff viewer',
        'server-scanner-modal' => 'Server scanner modal',
        'start-scan-btn' => 'Start scan button'
    );
    
    $allElementsPresent = true;
    foreach ($interfaceElements as $elementId => $description) {
        if (strpos($response, 'id="' . $elementId . '"') !== false) {
            echo "   ✓ $description found\n";
        } else {
            echo "   ✗ $description missing\n";
            $allElementsPresent = false;
        }
    }
    
    // Check for JavaScript initialization
    if (strpos($response, 'SvnTool.init()') !== false) {
        echo "   ✓ JavaScript initialization found\n";
    } else {
        echo "   ✗ JavaScript initialization missing\n";
        $allElementsPresent = false;
    }
    
    if ($allElementsPresent) {
        echo "   ✓ All required interface elements present\n";
    }
    
} else {
    echo "   ✗ Web interface not accessible: HTTP $httpCode\n";
    exit(1);
}

echo "\n";

// Test 2: Test Server Scanner Workflow
echo "2. Testing Server Scanner Workflow:\n";

echo "   Step 1: Test server scanner API\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/api.php?action=server_scan&path=/var/www&depth=2');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
$scanResponse = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    $scanData = json_decode($scanResponse, true);
    if ($scanData && $scanData['status'] === 'success') {
        echo "     ✓ Server scanner API working\n";
        echo "     ✓ Found " . count($scanData['data']) . " repositories\n";
        
        $foundRepos = array();
        foreach ($scanData['data'] as $repo) {
            $foundRepos[] = $repo['name'];
            echo "       - {$repo['name']} at {$repo['path']} ({$repo['status']})\n";
        }
        
        // Verify expected repositories are found
        $expectedRepos = array('engine', 'riashop', 'front');
        $allExpectedFound = true;
        foreach ($expectedRepos as $expected) {
            if (in_array($expected, $foundRepos)) {
                echo "     ✓ Expected repository '$expected' found\n";
            } else {
                echo "     ✗ Expected repository '$expected' missing\n";
                $allExpectedFound = false;
            }
        }
        
        if ($allExpectedFound) {
            echo "     ✓ All expected repositories found\n";
        }
        
    } else {
        echo "     ✗ Server scanner API failed\n";
        exit(1);
    }
} else {
    echo "     ✗ Server scanner API error: HTTP $httpCode\n";
    exit(1);
}

echo "\n";

// Test 3: Test Repository Listing Workflow
echo "3. Testing Repository Listing Workflow:\n";

echo "   Step 1: Test repository listing API\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/api.php?action=list_repos&repo_base_path=/var/www/sites');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
$repoResponse = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    $repoData = json_decode($repoResponse, true);
    if ($repoData && $repoData['status'] === 'success') {
        echo "     ✓ Repository listing API working\n";
        echo "     ✓ Found " . count($repoData['data']) . " repositories in /var/www/sites\n";
        
        $testRepo = null;
        foreach ($repoData['data'] as $repo) {
            echo "       - {$repo['name']} at {$repo['path']}\n";
            if ($repo['name'] === 'front') {
                $testRepo = $repo['path'];
            }
        }
        
        if ($testRepo) {
            echo "     ✓ Test repository 'front' found at $testRepo\n";
            
            // Test 4: Test File Status Workflow
            echo "\n4. Testing File Status Workflow:\n";
            
            echo "   Step 1: Test file status API\n";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/api.php?action=get_status&path=' . urlencode($testRepo));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            $statusResponse = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode == 200) {
                $statusData = json_decode($statusResponse, true);
                if ($statusData && $statusData['status'] === 'success') {
                    echo "     ✓ File status API working\n";
                    echo "     ✓ Found " . count($statusData['data']) . " files\n";
                    
                    $modifiedFiles = array();
                    foreach ($statusData['data'] as $file) {
                        echo "       - {$file['name']} ({$file['status']})\n";
                        if ($file['status'] === 'modified') {
                            $modifiedFiles[] = $file;
                        }
                    }
                    
                    if (count($modifiedFiles) > 0) {
                        echo "     ✓ Found " . count($modifiedFiles) . " modified files for diff testing\n";
                        
                        // Test 5: Test File Diff Workflow
                        echo "\n5. Testing File Diff Workflow:\n";
                        
                        $testFile = $modifiedFiles[0];
                        echo "   Step 1: Test diff API for {$testFile['name']}\n";
                        
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, 'http://localhost/svn-tool/api.php?action=get_diff&path=' . urlencode($testRepo) . '&file=' . urlencode($testFile['path']));
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                        $diffResponse = curl_exec($ch);
                        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        curl_close($ch);
                        
                        if ($httpCode == 200) {
                            $diffData = json_decode($diffResponse, true);
                            if ($diffData && $diffData['status'] === 'success') {
                                echo "     ✓ File diff API working\n";
                                echo "     ✓ File: {$diffData['data']['file']}\n";
                                echo "     ✓ Has changes: " . ($diffData['data']['has_changes'] ? 'YES' : 'NO') . "\n";
                                
                                if ($diffData['data']['has_changes']) {
                                    $diffLines = explode("\n", $diffData['data']['diff']);
                                    echo "     ✓ Diff content: " . count($diffLines) . " lines\n";
                                    echo "     ✓ Sample diff line: " . substr($diffLines[0], 0, 50) . "...\n";
                                }
                                
                            } else {
                                echo "     ✗ File diff API failed\n";
                            }
                        } else {
                            echo "     ✗ File diff API error: HTTP $httpCode\n";
                        }
                        
                    } else {
                        echo "     ⚠ No modified files found for diff testing\n";
                    }
                    
                } else {
                    echo "     ✗ File status API failed\n";
                }
            } else {
                echo "     ✗ File status API error: HTTP $httpCode\n";
            }
            
        } else {
            echo "     ✗ Test repository 'front' not found\n";
        }
        
    } else {
        echo "     ✗ Repository listing API failed\n";
    }
} else {
    echo "     ✗ Repository listing API error: HTTP $httpCode\n";
}

echo "\n==================================\n";
echo "Complete Workflow Test Summary\n";
echo "==================================\n";

// Final summary
$webInterfaceWorking = ($httpCode == 200 && isset($allElementsPresent) && $allElementsPresent);
$serverScannerWorking = (isset($scanData) && $scanData['status'] === 'success' && isset($allExpectedFound) && $allExpectedFound);
$repoListingWorking = (isset($repoData) && $repoData['status'] === 'success');
$fileStatusWorking = (isset($statusData) && $statusData['status'] === 'success');
$fileDiffWorking = (isset($diffData) && $diffData['status'] === 'success');

echo "✓ Web Interface: " . ($webInterfaceWorking ? "WORKING" : "FAILED") . "\n";
echo "✓ Server Scanner: " . ($serverScannerWorking ? "WORKING" : "FAILED") . "\n";
echo "✓ Repository Listing: " . ($repoListingWorking ? "WORKING" : "FAILED") . "\n";
echo "✓ File Status: " . ($fileStatusWorking ? "WORKING" : "FAILED") . "\n";
echo "✓ File Diff: " . ($fileDiffWorking ? "WORKING" : "FAILED") . "\n";

if ($webInterfaceWorking && $serverScannerWorking && $repoListingWorking && $fileStatusWorking && $fileDiffWorking) {
    echo "\n🎉 ALL SYSTEMS WORKING! Both critical issues should be resolved.\n";
    echo "\n📋 USER WORKFLOW:\n";
    echo "1. Visit http://localhost/svn-tool/\n";
    echo "2. Click 'Scanner le serveur' to open server scanner modal\n";
    echo "3. Click 'Démarrer le scan' to find repositories\n";
    echo "4. Select repositories from scan results\n";
    echo "5. Click on repository to view files\n";
    echo "6. Click 'Voir diff' button on modified files to view differences\n";
    echo "7. Use 'Copier le diff' to copy diff content\n";
} else {
    echo "\n⚠ SOME ISSUES REMAIN - Check failed components above\n";
}
?>
