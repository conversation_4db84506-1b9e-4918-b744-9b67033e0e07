<?php

/**	\file popup-select-tenant.php
 *	Fichier de la popup de choix de tenant sur l'interface d'administration mutualisée (app.riashop.fr).
 *	Cette popup est réservée aux utilisateurs ayant accès à plusieurs tenants. Elle leur permet de choisir
 *	sur quel tenant ils souhaitent travailler.
 */

require_once('http.inc.php');
require_once('strings.inc.php');

if (!isset($admin_account) || !is_a($admin_account, 'Administrator') || !$admin_account->isConnected()) {
	http_403();
}

$no_translates = true;

define('ADMIN_PAGE_TITLE', _('Choisissez une instance'));
define('ADMIN_HEAD_POPUP', true);
define('ADMIN_ID_BODY', 'popup-content');
define('ADMIN_CLASS_BODY', 'popup-iframe');
require_once('admin/skin/header.inc.php');

?>

<div class="ria-admin-ui-intro">
	<div class="app-searchbar">
		<span class="app-searchbar-icon"></span>
		<input id="select-tenant-input" class="app-searchbar-input" type="text" placeholder="<?php echo _('Rechercher une entreprise') ?>" aria-label="Votre recherche" name="tenant" autocomplete="off">
	</div>
	<div class="ria-admin-ui-intro-caption select-tenant">
		<?php
		// récupère la liste des tenants
		$monitoring = new Monitoring();
		$ar_tenants = $monitoring->getTenants();

		// Affiche la liste des tenants pouvant être choisis
		print '<ul id="select-tenant-list">';
		foreach ($ar_tenants as $tnt_id => $data) {
			if (!$admin_account->hasAccess($tnt_id)) {
				continue;
			}

			$selected = $tnt_id == $admin_account->getTenantSelected();

			print '<li js-tnt-id="' . $tnt_id . '"' . ($selected ? 'class="selected"' : '') . '>';
			print htmlspecialchars($data['name']);
			print '</li>';
		}
		print '</ul>';

		?></div>
</div>

<style>
	.popup_ria_drag {
		height: 0;
		padding: 0;
	}

	/** Surligne dans les résultats ce qui a été tapé dans le champ de recherche */
	#select-tenant-list .highlight {
		background-color: yellow;
	}

	#select-tenant-list .selected .highlight,
	#select-tenant-list .hover .highlight,
	#select-tenant-list li:hover .highlight,
	#select-tenant-list li:focus .highlight {
		color: #232E63;
	}

	.content {
		top: 0;
		border-radius: 10px;
		padding: 0;
		overflow: auto;
	}

	#select-tenant-list {
		border-top: 1px solid #232E63;
	}

	#select-tenant-list li {
		margin: 0;
		list-style: none;

		border: 1px solid #232E63;
		border-top: 0px;

		color: #232E63;
		display: block;
		padding: 10px 0;
		font-size: 1.2em;
		font-weight: 600;

		cursor: pointer;

		overflow: hidden;
	}

	#select-tenant-list li:hover,
	#select-tenant-list li:focus,
	#select-tenant-list li.hover,
	#select-tenant-list li.selected {
		color: #fff;
		background-color: #5377FB;
		text-decoration: none;
	}

	#select-tenant-list li.selected {
		cursor: default !important;
		background-color: #34428d;
	}
</style>

<script>
	function openTenant(e) {
		if (e.type == 'keypress' && e.which != 13) {
			return;
		}

		var tnt_id = $(this).attr('js-tnt-id');

		var href = window.parent.location.href;
		href = href.replace(/#.*/, '');
		href = href.replace(/[&?]?get-tenant-admin=[0-9]+/, '');

		var separator = '?';
		if( href.indexOf('?') != -1 ){
			separator = '&';
		}

		window.parent.location.href = href + separator + 'get-tenant-admin=' + tnt_id;
	}

	$(document).ready(function() {

		var delay = false;
		var $list = $('#select-tenant-list');

		$('#select-tenant-list li:not(.selected)').attr('tabindex', '0').keypress(openTenant).click(openTenant);

		// Filtre côté client la liste des tenants
		$('#select-tenant-input').on('keyup', function(e) {
			clearTimeout(delay);

			var value = strtoupper($(this).val());
			value = value.trim().replace(/\s+/g, " ");

			if (value == '') {
				$('li', $list).show();
			}

			// Retire la classe hover ajoutée pour gérer la sélection au clavier si un seul tenant
			$('#select-tenant-list li:visible').removeClass('hover');

			// Si l'utilisateur cliquer sur la touche Entrée et qu'un seul tenant est dans la liste, ouvre le tenant
			if (e.which == 13 && $('#select-tenant-list li:visible').length == 1) {
				$('#select-tenant-list li:visible').click();
			}

			// Filtre la liste en fonction de la saisie de l'utilisateur
			delay = setTimeout(function() {

				// Filtre la liste en fonction de la recherche
				$('li', $list).each(function(i, li) {
					var txtValue = strtoupper($(this).text());
					if (txtValue.indexOf(value) > -1) { // Recherche par nom de tenant
						$(this).show();

						// Surligne la recherche de l'utilisateur
						var searchExp = new RegExp(value, 'i');
						$(this).html($(this).text().replace(searchExp, function(f) {
							return '<span class="highlight">' + f + '</span>'
						}));
					} else if ($(this).attr('js-tnt-id') == value) { // Recherche par numéro de tenant
						$(this).show();
					} else {
						$(this).hide();
					}
				});

				// Indique à l'utilisateur que le seul tenant de la liste sera ouvert s'il clique sur Enter
				if ($('#select-tenant-list li:visible').length == 1) {
					$('#select-tenant-list li:visible').addClass('hover');
				}

			}, 100);
		});

		// Place le focus dans le champ input pour économiser un clic
		$('#select-tenant-input').focus();

	});

	// Fonction perso permettant de désactiver le premier tenant "---supprimer---"
	$('li[js-tnt-id="308"]').remove();


	// Cette fonction étend la méthode toUpperCase en lui permettant de mieux traiter les caractères accentués
	function strtoupper(str) {
		str = str.replace(/[àáâãäåæ]/g, 'A');
		str = str.replace(/[ç]/g, 'C');
		str = str.replace(/[èéêë]/g, 'E');
		str = str.replace(/[ìíîï]/g, 'I');
		str = str.replace(/[ð]/g, 'D');
		str = str.replace(/[ñ]/g, 'N');
		str = str.replace(/[òóôõöœø]/g, 'O');
		str = str.replace(/[š]/g, 'S');
		str = str.replace(/[ùúûü]/g, 'U');
		str = str.replace(/[ý]/g, 'Y');
		str = str.replace(/[ž]/g, 'Z');
		return str.toUpperCase();
	}
</script>

<?php
require_once('admin/skin/footer.inc.php');
