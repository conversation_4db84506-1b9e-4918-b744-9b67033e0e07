<?php

	/**	\file index.php
	 *	Cette page affiche la liste des messages classés comme indésirables. Elle permet également
	 *	de sortir un message des spams.
	 */

	require_once('antispam.inc.php');
	require_once('messages.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_MOD_SPAM');

	// Retire un message de la liste des indésirables
	if( isset($_POST['unblock-mess']) ){
		if( isset($_POST['rvw']) && is_array($_POST['rvw']) ){
			foreach( $_POST['rvw'] as $msg ){
				if( !gu_messages_set_spam($msg, false) ){
					$error = _("Une erreur inattendue s'est produite lors de la suppression des messages des indésirables.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
				}else{
					$success = _('Le ou les messages demandés ont été correctement retirés des spams. Merci de votre participation.');
				}
			}
		}

		/*if( !isset($error) ){
			header('Location: /admin/moderation/spam/index.php');
			exit;
		}*/
	}

	if( !isset($_GET['page']) ){
		$_GET['page'] = 0;
	}

	// Récupère les messages qui sont notés comme indésirables
	$count = messages_count( 0, '', null, false, -1 );

	// Calcule la pagination
	$page = $pages = 1;
	$limit = 25;
	if( $count ){
		$pages = ceil($count / $limit);
		$pages = $pages>0 ? $pages : 1;

		if( !isset($_GET['page']) ){
			$page = 1;
		}else{
			if( !is_numeric($_GET['page']) || $_GET['page']<0 ){
				$page = 1;
			}elseif( $_GET['page']>$pages ){
				$page = $pages;
			}else{
				$page = $_GET['page'];
			}
		}
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Modération'), '/admin/moderation/index.php' )
		->push( _('Messages indésirables') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Messages indésirables') . ' - ' . _('Modération'));
	require_once('admin/skin/header.inc.php');

	// Chargement des messages indésirables
	$rmessages = messages_get_spams( false, false, false, ($page-1)*$limit, $limit );
?>
	<h2><?php echo _("Messages indésirables"); ?><?php print isset($_GET['spam']) ? ' : '.$as['value'] : ''; ?></h2>
	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}elseif( isset($success) ){
			print '<div class="success">'.nl2br( htmlspecialchars($success) ).'</div>';
		}
	?>
    <form method="post" action="index.php?page=<?php print $_GET['page']; ?><?php print isset($_GET['spam']) ? '&amp;spam='.$_GET['spam'] : ''; ?>" >
	<table id="cntcontact" class="tb-spam checklist">
        <tbody>
            <tr>
            	<th id="checkbox" data-label="<?php print _('Tout cocher :'); ?> "><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
            	<th id="commentaire" class="thead-none col-big-content"><?php echo _("Contenu"); ?></th>
                <th id="type" class="align-center thead-none"><?php echo _("Type"); ?></th>
            </tr>
            <?php if( $count==0 ){ ?>
                    <tr><td colspan="3"><?php echo _("Aucun message indésirable"); ?></td></tr>
            <?php }else{

					while( $m = ria_mysql_fetch_array( $rmessages ) ){
				?>
						<tr id="message-<?php print $m['id']; ?>">
							<td headers="checkbox" class="align-center"><input type="checkbox" value="<?php print $m[ 'id' ]; ?>" name="rvw[]" class="checkbox" /></td>
							<td headers="commentaire" class="commentaire" data-label="<?php print _('Contenu :'); ?> ">
								<p><?php echo _('Auteur :'); ?>
									<?php
										if( $m[ 'usr_id' ] > 0 && ($usr = ria_mysql_fetch_array( gu_users_get($m['usr_id']) )) ){
											print '<a target="_blank" href="/admin/customers/edit.php?usr='. $m[ 'usr_id' ] .'" >';
											print view_usr_is_sync($usr).' '.htmlspecialchars( $usr['adr_firstname'] . ' ' . $usr[ 'adr_lastname' ] .' - ' . $usr[ 'society' ] );
											print '</a>';
										}else{
											print view_usr_is_sync($usr['is_sync']=0).' '.htmlspecialchars( $m[ 'firstname' ] . ' ' . $m[ 'lastname' ] .' - ' . $m[ 'society' ] );
										}
										print '&nbsp; ' . _('le ') .ria_date_format($m[ 'date' ]).'<br />' . _('Sujet :').' '.htmlspecialchars( $m[ 'subject' ] ); ?>
								</p>
								<br /><?php echo _('Message :'); ?><br />
								<?php print nl2br(htmlspecialchars($m[ 'body' ])); ?>
							</td>
							<td headers="type" class="align-center" data-label="<?php print _('Type :'); ?> "><?php print ucfirst($m[ 'type_text' ]); ?></td>
						</tr>
					<?php
					}
				}
			?>
		</tbody>
		<?php if( ria_mysql_num_rows($rmessages) ){ ?>
		<tfoot>
            <tr>
                <td colspan="3">
					<input class="show-form-spam float-left" type="submit" name="unblock-mess" value="<?php echo _("Retirer des spams"); ?>" />
                </td>
            </tr>
			<?php if( $pages>1 ){ ?>
			<tr id="pagination" class="align-left"><?php
				$file = 'index.php';
				print '	<td colspan="3"><div class="float-left">' . _("Page ").$page.' / '.$pages.'</div>
					<div class="float-right">';
					$links = array();
					if( $page>1 ){
						$links[] = '<a href="'.view_admin_construct_url($file,array_merge($_GET, array('page' => ($page-1)))).'">&laquo; ' . _("Page précédente") . '</a>';
					}
					for( $i=$page-5; $i<$page+5; $i++ ){
						if( $i>=1 && $i<=$pages ){
							if( $i==$page ){
								$links[] = '<b>'.$i.'</b>';
							}else{
								$links[] = '<a href="'.view_admin_construct_url($file,array_merge($_GET, array('page' => ($i)))).'">'.$i.'</a>';
							}
						}
					}
					if( $page<$pages ){
						$links[] = '<a href="'.view_admin_construct_url($file,array_merge($_GET, array('page' => ($page+1)))).'">' . _("Page suivante") . ' &raquo;</a>';
					}

					print implode(' | ',$links).
						'	</div></td>';

			?></tr>
			<?php } ?>
        </tfoot>
		<?php } ?>
	</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>