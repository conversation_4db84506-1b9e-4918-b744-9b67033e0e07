<?php

	/**	\file index.php
	 *	Cette page sert d'accueil à la section Modération du back-office
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_MOD');

	// Signaler un message comme Spam
	if( isset($_GET['cnt'], $_GET['action']) && $_GET['action']=='signal-spam' && ($msg = ria_mysql_fetch_array( messages_get(0, '', 0, $_GET['cnt']) )) ){
		$b_error = false;

		if( !gu_messages_set_spam($_GET['cnt']) ){
			$b_error = true;
		}
		if( trim($msg['ip'])!='' && !ats_ips_add($msg['ip']) ){
			$b_error = true;
		}

		if( !$b_error ){
			$success = _("Le message a bien été signalé comme spam");
		}
		else{
			$error = _("Une erreur inattendue s'est produite lors du classement du message comme spam.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
		}
	}

	// passer une message en tant que avis sur le site
	if( isset($_GET['cnt'], $_GET['action']) && $_GET['action']=='new-review-site' && ($msg = ria_mysql_fetch_array( messages_get(0, '', 0, $_GET['cnt']) )) ){

		if( $msg['type']=='RVW_SITE' ){
			header('Location: /admin/moderation/index.php');
			exit;
		}

		if( !gu_messages_update_type($_GET['cnt'], 'RVW_SITE') ){
			$error = _("Une erreur inattendue s'est produite lors du classement du message comme \"Avis sur le site\".\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
		}
		else{
			$success = _("Le message a bien été transformé en \"Avis sur le site\".");
		}
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Modération'), '/admin/moderation/index.php' );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Modération'));
	require_once('admin/skin/header.inc.php');

	if( isset($error) ){
		print '<div class="error">'.nl2br($error).'</div>';
	}
	elseif( isset($success) ){
		print '<div class="error-success">'.nl2br($success).'</div>';
	}
?>
	<h2><?php print _('Modération'); ?></h2>
	<p><?php print _('La modération permet de filtrer les messages laissés par les utilisateurs de votre boutique en ligne.'); ?></p>

<?php
	print view_index_menu('_MDL_MOD');

	require_once('admin/skin/footer.inc.php');
?>