<?php

	/**	\file view_contact.php
	 *	Cette page est utilisée en include par le fichier moderation.php. Elle affiche une liste de messages
	 *	en provenance du ou des sites de l'instance.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_MOD');

$page = $pages = 1;
$limit = 25;
if( $messages ){
	$pages = ceil(sizeof($messages) / $limit);
	$pages = $pages>0 ? $pages : 1;

	if( !isset($_GET['page']) ){
		$page = 1;
	}else{
		if( !is_numeric($_GET['page']) || $_GET['page']<0 ){
			$page = 1;
		}elseif( $_GET['page']>$pages ){
			$page = $pages;
		}else{
			$page = $_GET['page'];
		}
	}

}
?>
<table id="cntcontact" class="tb-contact-moderation">
	<thead>
	<?php if( !isset($_GET['usr']) && sizeof($messages)>0 ){ ?>
		<tr>
			<th colspan="2" class="align-left"><input class="btn-export" type="button" name="exporter" id="exporter" value="<?php echo _("Exporter"); ?>" /></th>
		</tr>
	<?php } ?>
		<tr>
			<th id="th-author"><?php echo _("Auteur"); ?></th>
			<th id="th-message"><?php echo _("Message"); ?></th>
		</tr>
	</thead>
	<tbody><?php
		if( !$messages || !sizeof($messages) ){
			print '<tr><td colspan="2">' . _("Aucun message ne correspond aux critères de sélection.") . '</td></tr>';
		} else {
			$count = 0;
			//Limite le nombre de messages affichés
			$messages_to_show = array_slice($messages, ($page-1)*$limit, $limit);
			while( isset($messages_to_show[$count]) ){
				// Information sur le compte rattaché, s'il en existe un
				$usr['is_sync'] = 0;
				if(  $messages_to_show[$count]['usr_id']>0 ){
					$rusr = gu_users_get( $messages_to_show[$count]['usr_id']);
					if( $rusr && ria_mysql_num_rows($rusr) )
						$usr = ria_mysql_fetch_assoc($rusr);
				}

				print view_admin_msg_moderation( $type, $messages_to_show[$count] );

				$count++;
			}
		}
	?></tbody>
	<?php if( $pages>1 ){ ?>
	<tfoot>
		<tr id="pagination"><?php
			print '<td class="align-left">Page '.$page.' / '.$pages.'</td>';
			print '<td>';
				if( isset($_GET['usr']) ){
					$queries['tab'] = 'contacts';
				}else{
					$queries = array('type' => $type['code'], 'have-rep' => $have_rep, 'have-usr' => $have_usr, 'sort-by' => $dir);
				}
				$links = array();
				if( $page>1 ){
					$links[] = '<a onclick="return loadMessages('.($page-1).')" href="'.view_admin_construct_url($file,array_merge($_GET, array('page' => ($page-1)), $queries)).'">&laquo; ' . _("Page précédente") . '</a>';
				}
				for( $i=$page-5; $i<$page+5; $i++ ){
					if( $i>=1 && $i<=$pages ){
						if( $i==$page ){
							$links[] = '<b>'.$i.'</b>';
						}else{
							$links[] = '<a onclick="return loadMessages('.$i.')" href="'.view_admin_construct_url($file,array_merge($_GET, array('page' => ($i)), $queries)).'">'.$i.'</a>';
						}
					}
				}
				if( $page<$pages ){
					$links[] = '<a onclick="return loadMessages('.($page+1).')" href="'.view_admin_construct_url($file,array_merge($_GET, array('page' => ($page+1)), $queries)).'">' . _("Page suivante") . ' &raquo;</a>';
				}

				print implode(' | ',$links);
			print '	</td>';

		?></tr>
	</tfoot>
	<?php } ?>
</table>