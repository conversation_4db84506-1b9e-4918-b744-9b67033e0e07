<?php

	/**	\file prd_moderation.php
	 *	Cette page est dédiée à la modération des demandes de modification effectuées par des fournisseurs
	 *	sur les produits du catalogue.
	 */

	require_once('fields.inc.php');
	// require_once('view.site.inc.php');
	require_once('prd/suppliers.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_MOD_PRD');

	// Charge la liste des champs que l'utilisateur en cours à choisi de modérer
	$fields = array();
	$rfields = gu_moderate_fields_get($_SESSION['usr_id']);
	while( $f = ria_mysql_fetch_array($rfields) ){
		$fields[] = $f['fld_id'];
	}

	// Bouton Refuser
	if( isset($_POST['refuse']) ){
		if( isset($_POST['fur']) && is_array($_POST['fur']) ){
			foreach( $_POST['fur'] as $r ){
				fld_update_requests_refuse( $r );
			}
		}
	}

	// Bouton Accepter
	if( isset($_POST['accept']) ){
		if( isset($_POST['fur']) && is_array($_POST['fur']) ){
			foreach( $_POST['fur'] as $r ){
				fld_update_requests_accept( $r );
			}
		}
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Modération'), '/admin/moderation/index.php' )
		->push( _('Demandes de modification') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Demandes de modification') . ' - '  . _('Modération'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Modération des demandes de modification'); ?></h2>

<?php
	if( sizeof($fields)==0 ){
		$error = str_replace("#param[redirection_configuration_champ]#", "<a href=\"../options/moderation.php\">", str_replace("#param[redirection_moderation]#", "<a href=\"../options/\">", str_replace("#param[fermeture_redirection]#", "</a>", _("Avant de continuer, veuillez #param[redirection_configuration_champ]#configurer les champs</a> que vous souhaitez modérer.\nCette configuration s'effectue dans la section #param[redirection_moderation]#Mes Options#param[fermeture_redirection]# de l'interface d'administration."))));
	}
	if( isset($error) ){
		print '<div class="error">'.nl2br($error).'</div>';
	}
	if( (!isset($_GET['prd']) || !is_numeric($_GET['prd'])) && !isset($error) ){
		$products = fld_update_products_get();
?>

	<table id="tb-prd-a-moderer" class="checklist">
		<caption><?php echo _("Liste des produits à modérer"); ?> (<?php print ria_mysql_num_rows($products).' produits'; ?>)</caption>
		<thead>
			<tr>
				<th id="prd-ref"><?php echo _("Référence"); ?></th>
				<th id="prd-name"><?php echo _("Désignation"); ?></th>
				<th id="prd-requests"><abbr title="<?php echo _("Demandes de modification en attente de validation"); ?>"><?php echo _("Demandes"); ?></abbr></th>
			</tr>
		</thead>
		<tbody>
			<?php
				if( $products===false || ria_mysql_num_rows($products)==0 ){
					print '<tr><td colspan="3">' . _("Aucun produit à modérer") . '</td></tr>';
				}else{
					while( $p = ria_mysql_fetch_array($products) ){
						print '	<tr>
									<td><a href="?prd='.$p['id'].'">'.$p['ref'].'</a></td>
									<td>'.htmlspecialchars($p['name']).'</td>
									<td class="numeric">'.number_format($p['requests'],0,',',' ').'</td>
								</tr>';
					}
				}
			?>
		</tbody>
	</table>
<?php
	}

	if(	isset($_GET['prd']) && prd_products_exists($_GET['prd']) ){
		$prd = ria_mysql_fetch_array(prd_products_get( $_GET['prd'] ));

		$prdsup = ria_mysql_fetch_array(prd_suppliers_get($prd['id']));
?>

	<table id="td-mod-proprietes-prd">
    	<caption><?php echo _("Propriétés du produit"); ?></caption>
		<tbody>
			<tr>
				<td><?php echo _("Référence :"); ?></td>
				<td><?php
					$categories = prd_products_categories_get($prd['id']);
					if( $cat = ria_mysql_fetch_array($categories) ){
						print '<a href="/admin/catalog/product.php?prd='.$prd['id'].'&amp;cat='.$cat['cat'].'">'.htmlspecialchars($prd['ref']).'</a>';
					}else{
						print htmlspecialchars($prd['ref']);
					}
				?></td>
			</tr>
			<tr>
				<td><?php echo _('Désignation :'); ?></td>
				<td><?php print htmlspecialchars($prd['name']); ?></td>
			</tr>
			<tr>
				<td><?php echo _("Référence(s) fournisseur :"); ?></td>
				<td><?php
					$suppliers = array();
					$rsuppliers = prd_suppliers_get( $prd['id'] );
					while( $sup = ria_mysql_fetch_array($rsuppliers) ){
						$suppliers[] = $sup['ref'];
					}
					print implode(',',$suppliers);
				?></td>
			</tr>
			<tr>
				<td><?php echo _("Publié :"); ?></td>
				<td><?php print $prd['publish'] ? _('Oui') : _('Non'); ?></td>
			</tr>
		</tbody>
    </table>

	<form action="prd_moderation.php?prd=<?php print $prd['id']; ?>" method="post">
	<table id="td-mod-demandes-modifications" class="checklist">
		<caption><?php echo _("Liste des demandes de modification à modérer"); ?></caption>
		<thead>
			<tr>
				<th id="fur-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
				<th id="fur-from"><?php echo _("Demandeur"); ?></th>
				<th id="fur-field"><?php echo _("Champ"); ?></th>
				<th id="fur-previous"><?php echo _("Valeur précédente"); ?></th>
				<th id="fur-next"><?php echo _("Valeur proposée"); ?></th>
				<th id="fur-date"><?php echo _("Date"); ?></th>
			</tr>
		</thead>
		<tbody>
	<?php
		$requests = fld_update_requests_get(false,false,$fields,$_GET['prd']);
		if( !ria_mysql_num_rows($requests) ){
			print '	<tr><td colspan="6">'.
						_('Aucune demande de modification à valider').'
					</td></tr>';
		}else{
			while( $r = ria_mysql_fetch_array($requests) ){
				print '	<tr>
							<td headers="fur-sel"><input type="checkbox" class="checkbox" name="fur[]" value="'.$r['id'].'" /></td>
							<td headers="fur-from" nowrap="nowrap"><a href="../customers/edit.php?usr='.$r['usr_id'].'">';
				switch( $r['adr_type_id'] ){
					case 1:
						print htmlspecialchars( $r['adr_firstname'].' '.$r['adr_lastname'] );
						break;
					case 2:
						print htmlspecialchars( $r['adr_society'] );
						break;
					case 3:
						print htmlspecialchars( $r['adr_firstname'].' '.$r['adr_lastname'].' ('.$r['adr_society'].')' );
						break;
				}
				print '	</a></td>
						<td headers="fur-field" nowrap="nowrap">'.
						htmlspecialchars($r['fld_name']);
				if( $r['fld_id']==FLD_PRD_STOCK_MINI || $r['fld_id']==FLD_PRD_STOCK_MAXI ){ // Précise le dépôt
					$rdps = prd_deposits_get( $r['obj2_id'] );
					if( ria_mysql_num_rows($rdps) ){
						$dps = ria_mysql_fetch_array($rdps);
						print '<br />Dépôt '.htmlspecialchars( $dps['name'] );
					}
				}
				print '</td>';

				// Valeur précédente
				print '<td headers="fur-previous">' ;
				switch( $r['fld_id'] ){
					case FLD_PRD_NAME:
						print htmlspecialchars($prd['name']);
						break;
					case FLD_PRD_DESC:
						print view_site_format_desc($prd['desc']);
						break;
					case FLD_PRD_DESC_LONG:
						print view_site_format_desc($prd['desc-long']);
						break;
					case FLD_PRD_IMAGE:
						// On vérifie s'il y a une image, sinon on affiche une image par défaut
						$size = $config['img_sizes']['medium'];
						$url_img = false;
						if( is_numeric($prd['img_id']) && $prd['img_id'] > 0 ){
							$url_img = $config['img_url'].'/'.$size['dir'].'/'.$prd['img_id'].'.'.$size['format'];
						}
						if( $url_img == false ){
							$url_img = view_admin_get_img_default('medium');
						}
						print '<img src="'.$url_img.'" width="'.$size['width'].'" height="'.$size['height'].'" alt="Image produit Modération" title="Image produit Modération" />';
						break;
					case FLD_PRD_BRAND:
						print htmlspecialchars($prd['brd_title']);
						break;
					case FLD_PRD_PRICE_TTC:
						print number_format($prd['price_ttc'],2,',',' ').'&nbsp;&euro;';
						break;
					case FLD_PRD_TVA_RATE:
						print number_format(($prd['tva_rate']-1)*100,1,',',' ').'&nbsp;%';
						break;
					case FLD_PRD_PRICE_ADH:
						$rp = prd_products_get_price( $prd['id'], 0, 1 );
						if( $p = ria_mysql_fetch_array($rp) )
							print number_format( $p['price_ht'], 2, ',', ' ' ).'&nbsp;&euro;';
						break;
					case FLD_PRD_PRICE_BS:
						$rps = prd_suppliers_get( $prd['id'], $r['obj2_id'] );
						if( ria_mysql_num_rows($rps) ){
							$ps = ria_mysql_fetch_array($rps);
							print number_format($ps['price'], 2, ',', ' ' ).'&nbsp;&euro';
						}
						break;
					case FLD_PRD_TAXCODE:
						print htmlspecialchars($prd['taxcode']);
						break;
					case FLD_PRD_WEIGHT_NET:
						if( !isset($prd['weight_net']) ) $prd['weight_net'] = '';
						print number_format( $prd['weight_net'], 0, ',', ' ' ).'<abbr title="grammes">g</abbr>';
						break;
					case FLD_PRD_WEIGHT_BRUT:
						print number_format( $prd['weight'], 0, ',', ' ' ).'<abbr title="grammes">g</abbr>';
						break;
					case FLD_PRD_STOCK_MINI:
					case FLD_PRD_STOCK_MAXI:
						$rstock = prd_dps_stocks_get( $prd['id'], $r['obj2_id'] );
						if( ria_mysql_num_rows($rstock) ){
							$stock = ria_mysql_fetch_array($rstock);
							if( $r['fld_id']==FLD_PRD_STOCK_MINI )
								print $stock['mini'];
							else if( $r['fld_id']==FLD_PRD_STOCK_MAXI )
								print $stock['maxi'];
						}
						break;
					case FLD_SUPPLIER_REF:
						print $prdsup['ref'];
						break;
					case FLD_SUPPLIER_DELAY:
						print $prdsup['delay'];
						break;
					case FLD_SUPPLIER_BARCODE:
						print $prdsup['barcode'];
						break;
					case FLD_PRD_SUPPLIER_CONVERSION:
						print number_format($prdsup['conversion']);
						break;

					case FLD_PRD_HIERARCHY:
					case FLD_PRD_ACCESSOIRE:
					case FLD_PRD_OPTION:
					case FLD_PRD_PIECE:
						if($r['fld_value']==1){
							$prod = ria_mysql_fetch_array(prd_products_get( $r['obj2_id'] ));
							print $prod['name'];
						}
						else{
							print ' Ajouter';
						}
						break;

					default:
						$fields = ria_mysql_fetch_array(fld_fields_get($r['fld_id'], 0, 0, 0, 0, $r['obj1_id']));
						switch($fields['type_id']){
							case FLD_TYPE_INT:
							case FLD_TYPE_TEXT:
							case FLD_TYPE_TEXTAREA:
							case FLD_TYPE_FLOAT:
							case FLD_TYPE_BOOLEAN_YES_NO:
							case FLD_TYPE_SELECT:
								if( isset($fields['obj_value']) ){
									print $fields['obj_value'];
								}else{
									print _('Champ vide');
								}
								break;
							case FLD_TYPE_SELECT_MULTIPLE:
								$selected = array();
								if( isset($fields['obj_value']) ){
									$values = explode( ',', $fields['obj_value'] );
									foreach( $values as $v ){
										$selected[] = fld_restricted_values_get_id( $fields['id'], trim($v) );
									}
								}
								$restric = fld_restricted_values_get(0,$r['fld_id']);
								if(ria_mysql_num_rows($restric)){

									while( $res = ria_mysql_fetch_array($restric) ){
										print '<div><input type="checkbox" disabled="disabled" class="checkbox" name="fld'.$fields['id'].'[]" id="fld'.$fields['id'].'-'.$res['id'].'" value="'.$res['id'].'" '.( array_search($res['id'],$selected)!==false ? 'checked="checked"':'' ).' />
												<label for="fld'.$fields['id'].'-'.$res['id'].'">'.htmlspecialchars($res['name']).'</label></div>';
									}

								}
								break;
							default:
						}

				}
				print '</td>';

				// Valeur proposée
				print '		<td headers="fur-next">';
				$f = array( 'tenant'=>$config['tnt_id'], 'id'=>$r['fld_id'], 'type_id'=>$r['fld_type_id'], 'obj_value'=>$r['fld_value'], 'unit_name'=>$r['fld_unit_name'], 'unit_symbol'=>$r['fld_unit_name']);
				print fld_fields_view($f, true);
				print '		</td>
									<td headers="fur-date" nowrap="nowrap">'.ria_date_format($r['date_created']).'</td>
						</tr>';
			}
		}
	?>
		</tbody>
		<tfoot>
			<tr><td colspan="6">
				<input type="submit" name="accept" value="<?php echo _("Accepter"); ?>" title="<?php echo _("Accepter les demandes de modification sélectionnées"); ?>" />
				<input type="submit" name="refuse" value="<?php echo _("Refuser"); ?>" title="<?php echo _("Refuser les demandes de modification sélectionnées"); ?>" />
			</td></tr>
		</tfoot>
	</table>
	</form>
<?php
	}

	require_once('admin/skin/footer.inc.php');
?>