<?php

	/**	\file export_devis.php
	 * 
	 * 	Ce fichier exporte les demandes de devis collectées en ligne. Il s'agit d'un développement spécifique pour les Cheminées de Chazelles.
	 * 
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_MOD');

	header( 'Content-type: text/csv;' );
	header( 'Content-disposition: attachment; filename="exportDevis_'.date('dmY').'.csv"'); 
	
	print utf8_decode('"Nom";"Prénom";"Adresse";"Code Postal";"Ville";"Téléphone";"Email";"Commentaire";"Profil";"Type de bien";"Année de construction";"Type de projet";"Modèle de cheminées";"Modèle d\'inserts";"Modèle de poêles";"Surface";"Hauteur sous le plafond";"Conduit existant";"Hauteur du conduit";"Conduit à créer";"Nature du mur d\'adossement";"Budget minimum";"Budget maximum";"Distribution d\'air chaud à prévoir";"Délai de réalisation du projet";"Intervention souhaitée"')."\n";

	while( $dv = ria_mysql_fetch_array($devis) ){
		// Information contenu dans les champs avancés
		$adr = fld_object_values_get( $dv['id'], $config['fld_quote_address'] );				// Adresse
		$zipcode = fld_object_values_get( $dv['id'], $config['fld_quote_zipcode'] );			// Code postal
		$city = fld_object_values_get( $dv['id'], $config['fld_quote_city'] );					// Ville
		$profil = fld_object_values_get( $dv['id'], $config['fld_quote_type'] );				// Profil
		$property = fld_object_values_get( $dv['id'], $config['fld_quote_property'] );			// Type de bien immobilier
		$year_build = fld_object_values_get( $dv['id'], $config['fld_quote_year_build'] );		// Année de construction
		$project = fld_object_values_get( $dv['id'], $config['fld_quote_project'] );			// Projet
		$mdl_ch = fld_object_values_get( $dv['id'], $config['fld_quote_mdl_ch'] );				// Modèle de cheminée
		$mdl_in = fld_object_values_get( $dv['id'], $config['fld_quote_mdl_in'] );				// Modèle d'insert
		$mdl_po = fld_object_values_get( $dv['id'], $config['fld_quote_mdl_po'] );				// Modèle de poêle
		$surface = fld_object_values_get( $dv['id'], $config['fld_quote_surface'] );			// Surface
		$ht_ceiling = fld_object_values_get( $dv['id'], $config['fld_quote_ceiling_height'] );	// Hauteur sous plafond
		$ht_ceiling = $ht_ceiling!='Hauteursousplafond' ? $ht_ceiling : '';
		$c_lead = fld_object_values_get( $dv['id'], $config['fld_quote_duct_exists'] );			// Conduit existant
		$create_lead = fld_object_values_get( $dv['id'], $config['fld_quote_duct_create'] );	// Conduit à créer
		$ht_lead = fld_object_values_get( $dv['id'], $config['fld_quote_duct_height'] );		// Hauteur du conduit
		$ht_lead = $ht_lead!='Hauteurduconduit' ? $ht_lead : '';
		$material = fld_object_values_get( $dv['id'], $config['fld_quote_wall'] );				// Nature du mur
		$material = $material!='Nature du mur d\'adossement' ? $material : '';
		$hot_air = fld_object_values_get( $dv['id'], $config['fld_quote_distr_air'] );			// Distribution d'air chaud
		$bg_min = fld_object_values_get( $dv['id'], $config['fld_quote_budget_min'] );			// Budget minimum
		$bg_max = fld_object_values_get( $dv['id'], $config['fld_quote_budget_max'] );			// Budget maximum
		$time = fld_object_values_get( $dv['id'], $config['fld_quote_time'] );					// Délai du projet
		$intv = fld_object_values_get( $dv['id'], $config['fld_quote_interv'] );				// Intervention
		
		print utf8_decode('"'.str_replace('"','\"',$dv['lastname']).'";"'.str_replace('"','\"',$dv['firstname']).'";"'.str_replace('"','\"',$adr).'";"'.str_replace('"','\"',$zipcode).'";"'.str_replace('"','\"',$city).'";"'.str_replace('"','\"',$dv['phone']).'";"'.str_replace('"','\"',$dv['email']).'";"'.( $dv['body']!='Inscrivez ici tout commentaire utile à la compréhension du projet' ? str_replace('"','\"',$dv['body']) : '' ).'";"'.str_replace('"','\"',$profil).'";"'.str_replace('"','\"',$property).'";"'.$year_build.'";"'.str_replace('"','\"',$project).'";"'.str_replace('"','\"',$mdl_ch).'";"'.str_replace('"','\"',$mdl_in).'";"'.str_replace('"','\"',$mdl_po).'";"'.$surface.'";"'.$ht_ceiling.'";"'.$c_lead.'";"'.$ht_lead.'";"'.$create_lead.'";"'.str_replace('"','\"',$material).'";"'.$bg_min.'";"'.$bg_max.'";"'.$hot_air.'";"'.str_replace('"','\"',$time).'";"'.str_replace('"','\"',$intv).'"')."\n";
	}
