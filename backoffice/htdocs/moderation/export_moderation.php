<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_MOD');

	global $config;
	require_once( 'excel/PHPExcel.php' );
	$alphabet = array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z');
	
	// Création de l'objet PHPExcel
	$objPHPExcel = new PHPExcel();

	// Déterminé les propriétés de la feuille Excel
	$objPHPExcel->getProperties()->setCreator("riaStudio")
								 ->setLastModifiedBy("riaStudio")
								 ->setTitle( _("Export des messages") )
								 ->setSubject( _("Export des messages") )
								 ->setDescription( _("Export des messages") )
								 ->setKeywords("export messages")
								 ->setCategory("");
	
	// Création du fichier
	$objPHPExcel->getActiveSheet()->setSheetState(PHPExcel_Worksheet::SHEETSTATE_VERYHIDDEN);
	$objWorksheet = $objPHPExcel->createSheet();
	$objWorksheet->setTitle('Messages');

	// Détermine le nom des colonnes
	$i = isset($_GET['type']) && $_GET['type']=='CONTACT' && $config['tnt_id']==8 ? 1 : 0; 
	// print $i; exit;
	if( isset($_GET['type']) && $_GET['type']=='CONTACT' && $config['tnt_id']==8 )
		$objWorksheet->setCellValue('A1', 'Type');
	
	$end_col = '';
	foreach( $_POST['col-filter'] as $col=>$name ){
		$objWorksheet->setCellValue($alphabet[$i].'1', $name);
		
		// Largeur de la colonne
		$objWorksheet->getColumnDimension( $alphabet[$i] )->setWidth( $_POST['col-width'][ $col ] );
		
		$end_col = $alphabet[$i];
		$i++;
	}
	
	if( isset($_POST['fld-filter'])){
		foreach( $_POST['fld-filter'] as $col=>$name ){
			$letter = $i<26 ? $alphabet[$i] : 'A'.$alphabet[$i-26];
			$objWorksheet->setCellValue($letter.'1', $name);
			$end_col = $letter;
			$i++;
		}
	}
	
	$origin = array( 'source' => _('Source'), 'first_visit' => _('Premiere visite'), 'nb_visit' => _('Nombre de visites'), 'ip' => _('Adresse IP') );
	foreach( $origin as $o ){
		$letter = $i<26 ? $alphabet[$i] : 'A'.$alphabet[$i-26];
		$objWorksheet->setCellValue($letter.'1', utf8_decode($o));
		$objWorksheet->getColumnDimension( $alphabet[$i] )->setWidth( 20 );
		$end_col = $letter;
		$i++;
	}

	// Police de l'entête du tableau
	$objWorksheet->getStyle('A1:'.$end_col.'1')->getFont()->setBold(true);
	$objWorksheet->getStyle('A1:'.$end_col.'1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
	$objWorksheet->getStyle('A1:'.$end_col.'1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');

	$ligne=2;
	if( $rmsg && ria_mysql_num_rows($rmsg) ){
		while( $msg = ria_mysql_fetch_array($rmsg) ){
			$i = 0; 
			if( isset($_GET['type']) && $_GET['type']=='CONTACT' && $config['tnt_id']==8 ){
				$i=1;
				if($config['tnt_id'] == 8) {
					$body = explode("\n",$msg['body']);
					$body2 = array();
					$t = '';
					foreach($body as $key => $b) {
						if(strpos($b,'* Type') !== false) {
							$body2 = explode(' : ', $b);
							$t = trim($body2[1]);
						}
					}
				}
				$objWorksheet->setCellValue( 'A'.$ligne, $t );
			}
			
			foreach( $_POST['col-filter'] as $col=>$name ){
				if( $col=='body' ){
					$objWorksheet->setCellValue( $alphabet[$i].$ligne, $msg[$col]!='' ? str_replace( array("\n",'*', "	"),'',$msg['body'] ) : '' );
					$objWorksheet->getStyle( $alphabet[$i].$ligne )->getAlignment()->setWrapText(true);
				} elseif( isset($msg[$col]) ) {
					$objWorksheet->setCellValue( $alphabet[$i].$ligne, $msg[$col]!='' ? $msg[$col] : '' );
				}
				$i++;
			}
			
			// Export des champs avancés
			if( isset($_POST['fld-filter']) ){
				foreach( $_POST['fld-filter'] as $fld=>$name ){
					$letter = $i<26 ? $alphabet[$i] : 'A'.$alphabet[$i-26];
					$val = fld_object_values_get( $msg['id'], $fld);
					$objWorksheet->setCellValue( $letter.$ligne, $val!='' ? $val : '' );
					$i++;
				}
			}

			$rstats = stats_origins_get( $msg['id'], CLS_MESSAGE, $wst_id);
			if( $rstats != false){
				$stat = ria_mysql_fetch_array( $rstats );

				foreach( $origin as $k=>$o ){
					$letter = $i<26 ? $alphabet[$i] : 'A'.$alphabet[$i-26];
					$val = '';
					switch( $k ){
						case 'source': {
							$val = view_source_origin( $stat, 'showsource' );
							break;
						}
						case 'first_visit': {
							$val = date( _('d/m/Y à H:i'), strtotime( $stat['first_visit'] ));
							break;
						}
						case 'nb_visit': {
							$val = $stat['times_visited'];
							break;
						}
						case 'ip': {
							$val = $stat['ip'];
							break;
						}
					}

					$objWorksheet->setCellValue( $letter.$ligne, $val );
					$i++;
				}
			}

			$ligne++;
		}
	}
	
	// Met des bordures en place
	$objWorksheet->getStyle('A1:'.$end_col.($ligne-1))->getBorders()->applyFromArray(
		array(
			'allborders' => array(
				'style' => PHPExcel_Style_Border::BORDER_THIN,
			)
		)
	);
	
	// Applique un alignement sur le haut à toutes les cellules
	$objWorksheet->getStyle('A1:'.$end_col.($ligne-1))->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
	
	// Confirgure l'ouverture d'Excel sur la case A1
	$objPHPExcel->setActiveSheetIndex(1);
	
	// Redirect output to a client web browser (Excel5)
	header('Content-Type: application/vnd.ms-excel');
	if( $_POST['extension']=='xlsx' ){
		header('Content-Disposition: attachment;filename="messages.xlsx"');
	}else{
		header('Content-Disposition: attachment;filename="messages.xls"');
	}
	header('Cache-Control: max-age=0');

	// Ecrit le fichier et le sauvegarde
	if( $_POST['extension']=='xlsx' ){
		$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
	}else{
		$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
	}

	$objWriter->save('php://output');
	exit;
