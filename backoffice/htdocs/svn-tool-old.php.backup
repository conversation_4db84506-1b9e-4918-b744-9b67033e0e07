<?php
// =============================================================================
//               MODERN SVN WEB CLIENT - PROFESSIONAL EDITION
// =============================================================================
//
// Author: AI Assistant
// Version: 2.0
// Description: A modern, responsive web-based SVN client with professional UI/UX
//
// =============================================================================

session_start();

// Configuration
$repoBasePath = isset($_POST['repo_base_path']) ? trim($_POST['repo_base_path']) : 
                (isset($_SESSION['repo_base_path']) ? $_SESSION['repo_base_path'] : '');

if (!empty($repoBasePath)) {
    $_SESSION['repo_base_path'] = $repoBasePath;
}

$currentPath = $repoBasePath;

// Security Functions
function is_path_safe($path, $basePath) {
    if (empty($basePath)) return false;
    $realPath = realpath($path);
    $realBasePath = realpath($basePath);
    return $realPath !== false && $realBasePath !== false && 
           strpos($realPath, $realBasePath) === 0;
}

function sanitizeOutput($output) {
    return htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
}

// SVN Functions
function checkSvnPermissions($repoPath) {
    $permissions = array(
        'repo_readable' => is_readable($repoPath),
        'repo_writable' => is_writable($repoPath),
        'svn_dir_readable' => false,
        'svn_dir_writable' => false,
        'repo_owner' => 'unknown',
        'current_user' => get_current_user(),
        'repo_permissions' => 'unknown',
        'svn_permissions' => 'unknown'
    );
    
    $svnDir = $repoPath . '/.svn';
    if (is_dir($svnDir)) {
        $permissions['svn_dir_readable'] = is_readable($svnDir);
        $permissions['svn_dir_writable'] = is_writable($svnDir);
        $permissions['svn_permissions'] = substr(sprintf('%o', fileperms($svnDir)), -4);
    }
    
    if (function_exists('posix_getpwuid') && function_exists('fileowner')) {
        $owner = posix_getpwuid(fileowner($repoPath));
        $permissions['repo_owner'] = $owner ? $owner['name'] : 'unknown';
    }
    
    $permissions['repo_permissions'] = substr(sprintf('%o', fileperms($repoPath)), -4);
    
    return $permissions;
}

function testSvnCommand($repoPath) {
    $command = 'svn info ' . escapeshellarg($repoPath) . ' 2>&1';
    $output = shell_exec($command);
    $returnCode = 0;
    
    return array(
        'command' => $command,
        'success' => strpos($output, 'svn: E') === false,
        'return_code' => $returnCode,
        'output' => $output
    );
}

function analyzeSvnError($output) {
    $analysis = array(
        'error_code' => '',
        'error_type' => 'unknown',
        'user_message' => 'An unexpected error occurred.',
        'suggested_action' => 'Please try again or contact support.',
        'is_recoverable' => false
    );

    if (strpos($output, 'E215004') !== false || strpos($output, 'Authentication failed') !== false) {
        $analysis['error_code'] = 'E215004';
        $analysis['error_type'] = 'authentication';
        $analysis['user_message'] = 'SVN authentication failed. Your username or password may be incorrect.';
        $analysis['suggested_action'] = 'Please check your SVN credentials and try again.';
        $analysis['is_recoverable'] = true;
    } elseif (strpos($output, 'E155004') !== false || strpos($output, 'locked') !== false) {
        $analysis['error_code'] = 'E155004';
        $analysis['error_type'] = 'locked';
        $analysis['user_message'] = 'The working copy is locked from a previous operation.';
        $analysis['suggested_action'] = 'Try running SVN cleanup to resolve the lock.';
        $analysis['is_recoverable'] = true;
    } elseif (strpos($output, 'E170013') !== false) {
        $analysis['error_code'] = 'E170013';
        $analysis['error_type'] = 'connectivity';
        $analysis['user_message'] = 'Unable to connect to the SVN server.';
        $analysis['suggested_action'] = 'Check your network connection and server availability.';
        $analysis['is_recoverable'] = true;
    } elseif (strpos($output, 'Permission denied') !== false || strpos($output, 'Operation not permitted') !== false) {
        $analysis['error_type'] = 'permission';
        $analysis['user_message'] = 'Permission denied. The web server lacks necessary permissions.';
        $analysis['suggested_action'] = 'Check file permissions and ownership.';
        $analysis['is_recoverable'] = true;
    }

    return $analysis;
}

function formatDiffOutput($diffOutput, $filePath) {
    if (empty($diffOutput) || strpos($diffOutput, 'svn: E') !== false) {
        return array(
            'html' => '<div class="diff-empty">No differences found or error occurred.</div>',
            'filename' => basename($filePath),
            'stats' => array('added' => 0, 'removed' => 0)
        );
    }

    $lines = explode("\n", $diffOutput);
    $html = '';
    $stats = array('added' => 0, 'removed' => 0);
    
    foreach ($lines as $line) {
        $line = sanitizeOutput($line);
        $class = '';
        
        if (strpos($line, '+++') === 0 || strpos($line, '---') === 0) {
            $class = 'diff-file-header';
        } elseif (strpos($line, '@@') === 0) {
            $class = 'diff-hunk-header';
        } elseif (strpos($line, '+') === 0) {
            $class = 'diff-added';
            $stats['added']++;
        } elseif (strpos($line, '-') === 0) {
            $class = 'diff-removed';
            $stats['removed']++;
        } else {
            $class = 'diff-context';
        }
        
        $html .= '<div class="diff-line ' . $class . '">' . $line . '</div>';
    }
    
    return array(
        'html' => $html,
        'filename' => basename($filePath),
        'stats' => $stats
    );
}

function generatePermissionFixCommands($repoPath, $permissions) {
    $commands = array();
    
    if ($permissions['repo_owner'] === 'unknown' || !$permissions['repo_writable']) {
        $commands[] = array(
            'description' => 'Fix repository ownership',
            'command' => 'sudo chown -R www-data:www-data ' . escapeshellarg($repoPath),
            'explanation' => 'Changes ownership of the entire repository to www-data user and group'
        );
        
        $commands[] = array(
            'description' => 'Set proper directory permissions',
            'command' => 'sudo chmod -R 775 ' . escapeshellarg($repoPath),
            'explanation' => 'Sets read/write/execute for owner and group, read/execute for others'
        );
        
        $commands[] = array(
            'description' => 'Ensure .svn directories are writable',
            'command' => 'sudo find ' . escapeshellarg($repoPath) . ' -name ".svn" -type d -exec chmod 775 {} \\;',
            'explanation' => 'Ensures all .svn directories have proper permissions'
        );
    }
    
    return $commands;
}

// API Handler
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    try {
        if (empty($repoBasePath) || !is_dir($repoBasePath)) {
            throw new Exception('Repository base path not configured or invalid.');
        }

        $action = $_GET['action'];
        $response = array('status' => 'error', 'message' => 'Invalid action.');

        switch ($action) {
            case 'list_repos':
                $repos = array();
                $items = scandir($repoBasePath);
                foreach ($items as $item) {
                    if ($item !== '.' && $item !== '..' && is_dir($repoBasePath . '/' . $item)) {
                        $svnDir = $repoBasePath . '/' . $item . '/.svn';
                        if (is_dir($svnDir)) {
                            $repos[] = array(
                                'name' => $item,
                                'path' => $repoBasePath . '/' . $item
                            );
                        }
                    }
                }
                $response = array('status' => 'success', 'data' => $repos);
                break;

            case 'get_status':
                $repoPath = isset($_GET['path']) ? $_GET['path'] : '';
                if (empty($repoPath) || !is_path_safe($repoPath, $repoBasePath)) {
                    throw new Exception('Invalid or unsafe repository path.');
                }
                
                $command = 'svn status --xml ' . escapeshellarg($repoPath) . ' 2>&1';
                $xmlOutput = shell_exec($command);
                
                if (strpos($xmlOutput, 'svn: E') !== false) {
                    $errorAnalysis = analyzeSvnError($xmlOutput);
                    throw new Exception($errorAnalysis['user_message']);
                }
                
                $files = array();
                if (strpos($xmlOutput, '<?xml') !== false) {
                    $xml = simplexml_load_string($xmlOutput);
                    if ($xml && isset($xml->target->entry)) {
                        foreach ($xml->target->entry as $entry) {
                            $status = (string)$entry->{'wc-status'}['item'];
                            if ($status !== 'normal' && $status !== 'unversioned') {
                                $files[] = array(
                                    'path' => (string)$entry['path'],
                                    'status' => $status,
                                    'name' => basename((string)$entry['path'])
                                );
                            }
                        }
                    }
                }
                
                $response = array('status' => 'success', 'data' => $files);
                break;

            case 'get_diff':
                $filePath = isset($_GET['path']) ? $_GET['path'] : '';
                if (empty($filePath) || !is_path_safe($filePath, $repoBasePath)) {
                    throw new Exception('Invalid or unsafe file path.');
                }
                
                $command = 'svn diff ' . escapeshellarg($filePath) . ' 2>&1';
                $diffOutput = shell_exec($command);
                
                if (strpos($diffOutput, 'svn: E') !== false) {
                    $errorAnalysis = analyzeSvnError($diffOutput);
                    throw new Exception($errorAnalysis['user_message']);
                }
                
                $formattedDiff = formatDiffOutput($diffOutput, $filePath);
                $response = array('status' => 'success', 'data' => $formattedDiff);
                break;

            case 'commit':
                $data = json_decode(file_get_contents('php://input'), true);
                $files = isset($data['files']) ? $data['files'] : array();
                $message = isset($data['message']) ? $data['message'] : '';

                if (empty($files)) {
                    throw new Exception('No files selected for commit.');
                }
                if (empty($message)) {
                    throw new Exception('Commit message is required.');
                }

                $fileArgs = '';
                foreach ($files as $file) {
                    if (is_path_safe($file, $repoBasePath)) {
                        $fileArgs .= escapeshellarg($file) . ' ';
                    }
                }

                if (empty($fileArgs)) {
                    throw new Exception('No valid files selected for commit.');
                }

                $command = 'svn commit -m ' . escapeshellarg($message) . ' ' . $fileArgs . ' --non-interactive 2>&1';
                $commitOutput = shell_exec($command);
                
                if (strpos($commitOutput, 'svn: E') !== false) {
                    $errorAnalysis = analyzeSvnError($commitOutput);
                    throw new Exception($errorAnalysis['user_message']);
                }
                
                $response = array('status' => 'success', 'message' => 'Commit completed successfully.', 'data' => $commitOutput);
                break;

            case 'svn_cleanup':
                $repoPath = isset($_GET['current_repo']) ? $_GET['current_repo'] : $currentPath;
                if (empty($repoPath) || !is_path_safe($repoPath, $repoBasePath)) {
                    throw new Exception('Invalid repository path for cleanup.');
                }
                
                $command = 'svn cleanup ' . escapeshellarg($repoPath) . ' 2>&1';
                $output = shell_exec($command);
                
                if (strpos($output, 'svn: E') !== false) {
                    $errorAnalysis = analyzeSvnError($output);
                    throw new Exception($errorAnalysis['user_message']);
                }
                
                $response = array('status' => 'success', 'message' => 'SVN cleanup completed successfully.');
                break;

            case 'check_permissions':
                $repoPath = isset($_GET['path']) ? $_GET['path'] : $currentPath;
                if (empty($repoPath) || !is_path_safe($repoPath, $repoBasePath)) {
                    throw new Exception('Invalid repository path for permission check.');
                }
                
                $permissions = checkSvnPermissions($repoPath);
                $svnTest = testSvnCommand($repoPath);
                
                $diagnosis = array();
                if (!$permissions['repo_writable']) {
                    $diagnosis[] = 'Repository directory is not writable by web server';
                }
                if (!$permissions['svn_dir_writable']) {
                    $diagnosis[] = '.svn directory is not writable by web server';
                }
                if ($permissions['current_user'] !== $permissions['repo_owner']) {
                    $diagnosis[] = 'Web server user differs from repository owner';
                }
                
                $response = array(
                    'status' => 'success',
                    'data' => array(
                        'repo_path' => $repoPath,
                        'permissions' => $permissions,
                        'svn_test' => $svnTest,
                        'diagnosis' => $diagnosis
                    )
                );
                break;

            case 'get_permission_fix':
                $repoPath = isset($_GET['current_repo']) ? $_GET['current_repo'] : $currentPath;
                if (empty($repoPath) || !is_path_safe($repoPath, $repoBasePath)) {
                    throw new Exception('Invalid repository path for permission fix.');
                }
                
                $permissions = checkSvnPermissions($repoPath);
                $fixCommands = generatePermissionFixCommands($repoPath, $permissions);
                
                $response = array(
                    'status' => 'success',
                    'data' => array(
                        'repo_path' => $repoPath,
                        'current_permissions' => $permissions,
                        'fix_commands' => $fixCommands,
                        'summary' => array(
                            'issue' => 'Web server lacks write permissions to SVN repository',
                            'cause' => $permissions['repo_owner'] === 'unknown' ? 
                                      'Repository ownership is not properly set' : 
                                      'Web server user is not in repository owner group',
                            'solution' => 'Run the provided commands as system administrator'
                        )
                    )
                );
                break;
        }
        
        echo json_encode($response);
        
    } catch (Exception $e) {
        echo json_encode(array(
            'status' => 'error',
            'message' => $e->getMessage(),
            'error_type' => 'general',
            'is_recoverable' => true
        ));
    }
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern SVN Web Client</title>
    <style>
        /* Modern CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --success-color: #059669;
            --warning-color: #d97706;
            --error-color: #dc2626;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --border-radius: 8px;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--gray-900);
            background-color: var(--gray-50);
        }

        /* Header Styles */
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            padding: 2rem 0;
            box-shadow: var(--shadow-md);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.125rem;
            opacity: 0.9;
        }

        /* Container and Layout */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-900);
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            gap: 0.5rem;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: var(--primary-hover);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--gray-700);
        }

        .btn-secondary:hover:not(:disabled) {
            background: var(--gray-300);
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .btn-loading {
            position: relative;
            color: transparent;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Grid Layout */
        .grid {
            display: grid;
            gap: 2rem;
        }

        .grid-cols-1 {
            grid-template-columns: 1fr;
        }

        .grid-cols-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        .grid-cols-3 {
            grid-template-columns: repeat(3, 1fr);
        }

        @media (max-width: 768px) {
            .grid-cols-2,
            .grid-cols-3 {
                grid-template-columns: 1fr;
            }
        }

        /* Alert Styles */
        .alert {
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            border: 1px solid;
        }

        .alert-info {
            background: #dbeafe;
            border-color: #93c5fd;
            color: #1e40af;
        }

        .alert-success {
            background: #d1fae5;
            border-color: #6ee7b7;
            color: #065f46;
        }

        .alert-warning {
            background: #fef3c7;
            border-color: #fcd34d;
            color: #92400e;
        }

        .alert-error {
            background: #fee2e2;
            border-color: #fca5a5;
            color: #991b1b;
        }

        /* Repository List */
        .repo-list {
            display: grid;
            gap: 0.5rem;
        }

        .repo-item {
            padding: 1rem;
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }

        .repo-item:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-sm);
        }

        .repo-item.active {
            border-color: var(--primary-color);
            background: #eff6ff;
        }

        /* File List */
        .file-list {
            display: grid;
            gap: 0.5rem;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            background: white;
            gap: 1rem;
        }

        .file-checkbox {
            width: 18px;
            height: 18px;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 500;
            color: var(--gray-900);
        }

        .file-path {
            font-size: 0.875rem;
            color: var(--gray-500);
            margin-top: 0.25rem;
        }

        .file-status {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-modified {
            background: #fef3c7;
            color: #92400e;
        }

        .status-added {
            background: #d1fae5;
            color: #065f46;
        }

        .status-deleted {
            background: #fee2e2;
            color: #991b1b;
        }

        .file-actions {
            display: flex;
            gap: 0.5rem;
        }

        /* Diff Viewer */
        .diff-viewer {
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            overflow: hidden;
            background: white;
        }

        .diff-header {
            padding: 1rem;
            background: var(--gray-50);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .diff-filename {
            font-weight: 600;
            color: var(--gray-900);
        }

        .diff-stats {
            display: flex;
            gap: 1rem;
        }

        .diff-stats-added {
            color: var(--success-color);
            font-weight: 500;
        }

        .diff-stats-removed {
            color: var(--error-color);
            font-weight: 500;
        }

        .diff-content {
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .diff-line {
            padding: 0.25rem 1rem;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .diff-added {
            background: #f0fdf4;
            color: #166534;
        }

        .diff-removed {
            background: #fef2f2;
            color: #991b1b;
        }

        .diff-context {
            background: white;
            color: var(--gray-700);
        }

        .diff-file-header {
            background: var(--gray-100);
            color: var(--gray-600);
            font-weight: 600;
        }

        .diff-hunk-header {
            background: #eff6ff;
            color: var(--primary-color);
            font-weight: 500;
        }

        .diff-empty {
            padding: 2rem;
            text-align: center;
            color: var(--gray-500);
            font-style: italic;
        }

        .diff-placeholder {
            padding: 3rem;
            text-align: center;
            color: var(--gray-400);
            background: var(--gray-50);
            border: 2px dashed var(--gray-200);
            border-radius: var(--border-radius);
        }

        /* Notification System */
        .notification-container {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1000;
            max-width: 400px;
        }

        .notification {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
            margin-bottom: 0.5rem;
            overflow: hidden;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .notification-header {
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-title {
            font-weight: 600;
            color: var(--gray-900);
        }

        .notification-close {
            background: none;
            border: none;
            font-size: 1.25rem;
            cursor: pointer;
            color: var(--gray-400);
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-body {
            padding: 0 1rem 1rem;
            color: var(--gray-600);
        }

        .notification-actions {
            padding: 0 1rem 1rem;
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .notification.success .notification-header {
            background: var(--success-color);
            color: white;
        }

        .notification.error .notification-header {
            background: var(--error-color);
            color: white;
        }

        .notification.warning .notification-header {
            background: var(--warning-color);
            color: white;
        }

        .notification.info .notification-header {
            background: var(--primary-color);
            color: white;
        }

        /* Loading States */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--gray-200);
            border-top-color: var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 1rem;
            }
            
            .card-header,
            .card-body {
                padding: 1rem;
            }
            
            .notification-container {
                left: 1rem;
                right: 1rem;
                max-width: none;
            }
            
            .diff-content {
                font-size: 0.75rem;
            }
        }

        /* Utility Classes */
        .hidden { display: none !important; }
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .mb-0 { margin-bottom: 0 !important; }
        .mb-1 { margin-bottom: 0.5rem; }
        .mb-2 { margin-bottom: 1rem; }
        .mb-3 { margin-bottom: 1.5rem; }
        .mt-2 { margin-top: 1rem; }
        .mt-3 { margin-top: 1.5rem; }
        .relative { position: relative; }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <h1>Modern SVN Web Client</h1>
            <p>Professional SVN repository management with modern UI/UX</p>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <!-- Configuration Section -->
        <div class="card mb-3">
            <div class="card-header">
                <h2 class="card-title">Repository Configuration</h2>
            </div>
            <div class="card-body">
                <form method="post" id="config-form">
                    <div class="form-group">
                        <label for="repo_base_path" class="form-label">Repository Base Path</label>
                        <input type="text" 
                               id="repo_base_path" 
                               name="repo_base_path" 
                               class="form-input" 
                               value="<?php echo sanitizeOutput($currentPath); ?>" 
                               placeholder="/var/www/repositories"
                               required>
                        <small class="text-gray-500">Enter the absolute path to your SVN repositories directory</small>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <span>Update Configuration</span>
                    </button>
                </form>
            </div>
        </div>

        <?php if (!$currentPath): ?>
            <div class="alert alert-warning">
                <strong>Notice:</strong> Please set your repository base path above to begin using the SVN client.
            </div>
        <?php else: ?>
            <!-- SVN Client Interface -->
            <div id="svn-interface">
                <div class="grid grid-cols-3">
                    <!-- Repositories Panel -->
                    <div class="card">
                        <div class="card-header">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <h3 class="card-title">Repositories</h3>
                                <button id="refresh-repos" class="btn btn-secondary btn-sm">
                                    <span>Refresh</span>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="repo-list" class="repo-list">
                                <div class="text-center text-gray-500">Loading repositories...</div>
                            </div>
                        </div>
                    </div>

                    <!-- Files Panel -->
                    <div class="card">
                        <div class="card-header">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <h3 class="card-title">File Status</h3>
                                <div style="display: flex; gap: 0.5rem;">
                                    <button id="check-permissions" class="btn btn-secondary btn-sm">
                                        <span>Check Permissions</span>
                                    </button>
                                    <button id="cleanup-btn" class="btn btn-warning btn-sm">
                                        <span>Cleanup</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="file-list" class="file-list">
                                <div class="text-center text-gray-500">Select a repository to view files</div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions Panel -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Actions</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label for="commit-message" class="form-label">Commit Message</label>
                                <textarea id="commit-message" 
                                         class="form-input form-textarea" 
                                         placeholder="Enter your commit message..."
                                         required></textarea>
                            </div>
                            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                <button id="commit-btn" class="btn btn-success">
                                    <span>Commit Selected</span>
                                </button>
                                <button id="select-all-btn" class="btn btn-secondary">
                                    <span>Select All</span>
                                </button>
                                <button id="deselect-all-btn" class="btn btn-secondary">
                                    <span>Deselect All</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Diff Viewer -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h3 class="card-title">Diff Viewer</h3>
                    </div>
                    <div class="card-body">
                        <div id="diff-viewer" class="diff-viewer hidden">
                            <div class="diff-header">
                                <div class="diff-filename" id="diff-filename"></div>
                                <div class="diff-stats" id="diff-stats"></div>
                            </div>
                            <div class="diff-content" id="diff-content"></div>
                            <div style="padding: 1rem; border-top: 1px solid var(--gray-200); text-align: right;">
                                <button class="btn btn-secondary btn-sm" onclick="copyDiffToClipboard()">
                                    <span>Copy Diff</span>
                                </button>
                            </div>
                        </div>
                        <div id="diff-placeholder" class="diff-placeholder">
                            <div style="font-size: 1.125rem; margin-bottom: 0.5rem;">📄</div>
                            <div>Click on a file to view its diff</div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Notification Container -->
    <div id="notification-container" class="notification-container"></div>

    <script>
        /**
         * Modern SVN Web Client JavaScript
         * Professional-grade implementation with proper error handling
         */

        // Global State Management
        const AppState = {
            selectedFiles: new Set(),
            currentRepo: null,
            currentRepoForCleanup: null,
            isLoading: false
        };

        // Notification System
        class NotificationManager {
            static container = null;
            static notifications = new Map();
            static idCounter = 0;

            static init() {
                this.container = document.getElementById('notification-container');
            }

            static show(type, title, message, options = {}) {
                const id = ++this.idCounter;
                const notification = this.createNotification(id, type, title, message, options);
                
                this.container.appendChild(notification);
                this.notifications.set(id, notification);

                // Auto-close if specified
                if (options.autoClose !== false) {
                    setTimeout(() => this.close(id), options.duration || 5000);
                }

                return id;
            }

            static createNotification(id, type, title, message, options) {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.dataset.id = id;

                const header = document.createElement('div');
                header.className = 'notification-header';
                
                const titleEl = document.createElement('div');
                titleEl.className = 'notification-title';
                titleEl.textContent = title;
                
                const closeBtn = document.createElement('button');
                closeBtn.className = 'notification-close';
                closeBtn.innerHTML = '×';
                closeBtn.onclick = () => this.close(id);
                
                header.appendChild(titleEl);
                header.appendChild(closeBtn);

                const body = document.createElement('div');
                body.className = 'notification-body';
                body.textContent = message;

                notification.appendChild(header);
                notification.appendChild(body);

                // Add action buttons if provided
                if (options.actions && options.actions.length > 0) {
                    const actionsEl = document.createElement('div');
                    actionsEl.className = 'notification-actions';
                    
                    options.actions.forEach(action => {
                        const btn = document.createElement('button');
                        btn.className = 'btn btn-sm btn-secondary';
                        btn.textContent = action.text;
                        btn.onclick = () => {
                            if (typeof action.onclick === 'string') {
                                eval(action.onclick);
                            } else if (typeof action.onclick === 'function') {
                                action.onclick();
                            }
                            if (action.closeOnClick !== false) {
                                this.close(id);
                            }
                        };
                        actionsEl.appendChild(btn);
                    });
                    
                    notification.appendChild(actionsEl);
                }

                return notification;
            }

            static close(id) {
                const notification = this.notifications.get(id);
                if (notification) {
                    notification.style.animation = 'slideOut 0.3s ease-in forwards';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                        this.notifications.delete(id);
                    }, 300);
                }
            }

            static success(title, message, options = {}) {
                return this.show('success', title, message, options);
            }

            static error(title, message, options = {}) {
                return this.show('error', title, message, { autoClose: false, ...options });
            }

            static warning(title, message, options = {}) {
                return this.show('warning', title, message, options);
            }

            static info(title, message, options = {}) {
                return this.show('info', title, message, options);
            }
        }

        // Add slideOut animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideOut {
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Utility Functions
        function setButtonLoading(buttonId, loading) {
            const button = document.getElementById(buttonId);
            if (!button) return;
            
            if (loading) {
                button.classList.add('btn-loading');
                button.disabled = true;
            } else {
                button.classList.remove('btn-loading');
                button.disabled = false;
            }
        }

        function showElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) element.classList.remove('hidden');
        }

        function hideElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) element.classList.add('hidden');
        }

        // API Communication
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error('API request failed:', error);
                throw error;
            }
        }

        function handleApiResponse(data, successCallback, operation) {
            if (data.status === 'success') {
                if (successCallback) successCallback(data);
            } else {
                handleApiError(data, operation);
            }
        }

        function handleApiError(data, operation) {
            const title = data.error_type === 'permission' ? 'Permission Error' : 'Operation Failed';
            const message = data.message || 'An unexpected error occurred.';
            const actions = [];

            if (data.error_type === 'permission') {
                actions.push({
                    text: 'Show Fix Guide',
                    onclick: 'showPermissionFix()'
                });
                actions.push({
                    text: 'Check Permissions',
                    onclick: 'checkPermissions()'
                });
            } else {
                if (data.is_recoverable) {
                    actions.push({
                        text: 'Retry',
                        onclick: `retry${operation}()`
                    });
                }
                actions.push({
                    text: 'Check Permissions',
                    onclick: 'checkPermissions()'
                });
            }

            if (data.raw_output) {
                actions.push({
                    text: 'View Details',
                    onclick: `showErrorDetails("${encodeURIComponent(data.raw_output)}")`
                });
            }

            NotificationManager.error(title, message, { actions });
        }

        // Repository Management
        async function loadRepositories() {
            setButtonLoading('refresh-repos', true);
            
            try {
                const data = await apiRequest('?action=list_repos');
                handleApiResponse(data, (data) => {
                    renderRepositories(data.data);
                    NotificationManager.success('Success', 'Repositories loaded successfully.');
                }, 'LoadRepositories');
            } catch (error) {
                NotificationManager.error('Load Failed', 'Failed to load repositories. Please try again.');
                console.error('Load repositories error:', error);
            } finally {
                setButtonLoading('refresh-repos', false);
            }
        }

        function renderRepositories(repos) {
            const repoList = document.getElementById('repo-list');
            
            if (repos.length === 0) {
                repoList.innerHTML = '<div class="text-center text-gray-500">No SVN repositories found in the specified path.</div>';
                return;
            }

            repoList.innerHTML = repos.map(repo => `
                <div class="repo-item" onclick="selectRepo('${repo.path}')" data-path="${repo.path}">
                    <div class="file-name">${repo.name}</div>
                    <div class="file-path">${repo.path}</div>
                </div>
            `).join('');
        }

        async function selectRepo(repoPath) {
            // Update UI state
            document.querySelectorAll('.repo-item').forEach(item => {
                item.classList.toggle('active', item.dataset.path === repoPath);
            });

            AppState.currentRepo = repoPath;
            AppState.currentRepoForCleanup = repoPath;
            
            const fileList = document.getElementById('file-list');
            fileList.innerHTML = '<div class="text-center text-gray-500">Loading files...</div>';
            
            try {
                const data = await apiRequest(`?action=get_status&path=${encodeURIComponent(repoPath)}`);
                handleApiResponse(data, (data) => {
                    renderFiles(data.data, repoPath);
                }, 'SelectRepo');
            } catch (error) {
                const errorMessage = error.message && (error.message.includes('locked') || error.message.includes('E155004'))
                    ? 'Repository appears to be locked from a previous operation.'
                    : 'Failed to load repository status. The repository may be corrupted or inaccessible.';
                
                const actions = [{
                    text: 'Retry',
                    onclick: `selectRepo("${repoPath}")`
                }];
                
                if (error.message && (error.message.includes('locked') || error.message.includes('E155004'))) {
                    actions.unshift({
                        text: 'Force Cleanup',
                        onclick: 'forceCleanup()'
                    });
                }
                
                NotificationManager.error('Repository Error', errorMessage, { actions });
                console.error('Select repo error:', error);
            }
        }

        function renderFiles(files, repoPath) {
            const fileList = document.getElementById('file-list');
            
            if (files.length === 0) {
                fileList.innerHTML = '<div class="text-center text-gray-500">No modified files found.</div>';
                return;
            }

            fileList.innerHTML = files.map(file => {
                const statusClass = {
                    'modified': 'status-modified',
                    'added': 'status-added',
                    'deleted': 'status-deleted'
                }[file.status] || 'status-modified';

                return `
                    <div class="file-item">
                        <input type="checkbox" 
                               class="file-checkbox" 
                               onchange="toggleFile('${file.path}')"
                               data-path="${file.path}">
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-path">${file.path}</div>
                        </div>
                        <div class="file-status ${statusClass}">${file.status}</div>
                        <div class="file-actions">
                            <button class="btn btn-secondary btn-sm" onclick="showDiff('${file.path}')">
                                <span>View Diff</span>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // File Selection Management
        function toggleFile(filePath) {
            if (AppState.selectedFiles.has(filePath)) {
                AppState.selectedFiles.delete(filePath);
            } else {
                AppState.selectedFiles.add(filePath);
            }
            updateFileSelection();
        }

        function selectAllFiles() {
            document.querySelectorAll('.file-checkbox').forEach(checkbox => {
                checkbox.checked = true;
                AppState.selectedFiles.add(checkbox.dataset.path);
            });
            updateFileSelection();
        }

        function deselectAllFiles() {
            document.querySelectorAll('.file-checkbox').forEach(checkbox => {
                checkbox.checked = false;
                AppState.selectedFiles.delete(checkbox.dataset.path);
            });
            AppState.selectedFiles.clear();
            updateFileSelection();
        }

        function updateFileSelection() {
            const commitBtn = document.getElementById('commit-btn');
            const selectedCount = AppState.selectedFiles.size;
            
            if (selectedCount > 0) {
                commitBtn.innerHTML = `<span>Commit Selected (${selectedCount})</span>`;
                commitBtn.disabled = false;
            } else {
                commitBtn.innerHTML = '<span>Commit Selected</span>';
                commitBtn.disabled = true;
            }
        }

        // Diff Viewer
        async function showDiff(filePath) {
            const diffViewer = document.getElementById('diff-viewer');
            const diffPlaceholder = document.getElementById('diff-placeholder');
            const diffContent = document.getElementById('diff-content');
            
            // Show loading state
            diffContent.innerHTML = '<div class="diff-empty">Loading diff...</div>';
            showElement('diff-viewer');
            hideElement('diff-placeholder');
            
            try {
                const data = await apiRequest(`?action=get_diff&path=${encodeURIComponent(filePath)}`);
                handleApiResponse(data, (data) => {
                    renderDiff(data.data, filePath);
                }, 'ShowDiff');
            } catch (error) {
                diffContent.innerHTML = '<div class="diff-empty">Failed to load diff. Please try again.</div>';
                NotificationManager.error('Diff Error', 'Failed to load file differences.', {
                    actions: [{
                        text: 'Retry',
                        onclick: `showDiff("${filePath}")`
                    }]
                });
                console.error('Show diff error:', error);
            }
        }

        function renderDiff(diffData, filePath) {
            const diffFilename = document.getElementById('diff-filename');
            const diffStats = document.getElementById('diff-stats');
            const diffContent = document.getElementById('diff-content');
            
            if (diffData.html) {
                diffContent.innerHTML = diffData.html;
                diffFilename.textContent = diffData.filename || 'Unknown file';
                
                const stats = diffData.stats || { added: 0, removed: 0 };
                let statsText = '';
                if (stats.added > 0) {
                    statsText += `<span class="diff-stats-added">+${stats.added}</span>`;
                }
                if (stats.removed > 0) {
                    if (statsText) statsText += ' ';
                    statsText += `<span class="diff-stats-removed">-${stats.removed}</span>`;
                }
                if (!statsText) {
                    statsText = 'No changes';
                }
                diffStats.innerHTML = statsText;
            } else {
                diffContent.innerHTML = `<div class="diff-empty">${diffData || 'No differences found.'}</div>`;
                diffFilename.textContent = filePath.split('/').pop();
                diffStats.innerHTML = 'No changes';
            }
        }

        function copyDiffToClipboard() {
            const diffContent = document.getElementById('diff-content');
            const lines = diffContent.querySelectorAll('.diff-line');
            let textContent = '';
            
            lines.forEach(line => {
                textContent += line.textContent + '\n';
            });
            
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(textContent).then(() => {
                    NotificationManager.success('Copied', 'Diff content copied to clipboard.');
                }).catch(() => {
                    fallbackCopyToClipboard(textContent);
                });
            } else {
                fallbackCopyToClipboard(textContent);
            }
        }

        function fallbackCopyToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                NotificationManager.success('Copied', 'Diff content copied to clipboard.');
            } catch (err) {
                NotificationManager.error('Copy Failed', 'Failed to copy diff to clipboard. Please copy manually.');
            }
            document.body.removeChild(textArea);
        }

        // SVN Operations
        async function commitFiles() {
            if (AppState.selectedFiles.size === 0) {
                NotificationManager.warning('No Files Selected', 'Please select at least one file to commit.');
                return;
            }
            
            const message = document.getElementById('commit-message').value.trim();
            if (!message) {
                NotificationManager.warning('Missing Commit Message', 'Please enter a commit message describing your changes.');
                return;
            }

            // Show confirmation dialog
            if (!confirm(`Are you sure you want to commit ${AppState.selectedFiles.size} file(s)?\n\nCommit message: "${message}"`)) {
                return;
            }

            setButtonLoading('commit-btn', true);

            try {
                const data = await apiRequest('?action=commit', {
                    method: 'POST',
                    body: JSON.stringify({
                        files: Array.from(AppState.selectedFiles),
                        message: message
                    })
                });

                handleApiResponse(data, (data) => {
                    NotificationManager.success('Commit Successful', 'Your changes have been committed successfully.');
                    AppState.selectedFiles.clear();
                    document.getElementById('commit-message').value = '';
                    if (AppState.currentRepo) selectRepo(AppState.currentRepo);
                }, 'CommitFiles');
            } catch (error) {
                NotificationManager.error('Commit Failed', 'Failed to commit changes. Please try again.', {
                    actions: [{
                        text: 'Retry',
                        onclick: 'commitFiles()'
                    }]
                });
                console.error('Commit error:', error);
            } finally {
                setButtonLoading('commit-btn', false);
            }
        }

        async function forceCleanup() {
            const repoPath = AppState.currentRepoForCleanup || AppState.currentRepo;
            if (!repoPath) {
                NotificationManager.error('Cleanup Error', 'No repository selected for cleanup.');
                return;
            }
            
            if (!confirm(`Are you sure you want to run SVN cleanup on:\n${repoPath.split('/').pop()}?\n\nThis will resolve any locks from previous operations.`)) {
                return;
            }
            
            const loadingNotification = NotificationManager.info('Cleaning Up', `Running SVN cleanup on ${repoPath.split('/').pop()}...`, {
                autoClose: false
            });
            
            try {
                const url = repoPath !== AppState.currentRepo 
                    ? `?action=svn_cleanup&current_repo=${encodeURIComponent(repoPath)}`
                    : '?action=svn_cleanup';
                
                const data = await apiRequest(url);
                
                NotificationManager.close(loadingNotification);
                
                handleApiResponse(data, (data) => {
                    NotificationManager.success('Cleanup Successful', 'SVN cleanup completed successfully.', {
                        actions: [{
                            text: 'Refresh Repository',
                            onclick: 'refreshAfterCleanup()'
                        }]
                    });
                }, 'ForceCleanup');
            } catch (error) {
                NotificationManager.close(loadingNotification);
                NotificationManager.error('Cleanup Error', 'Failed to execute SVN cleanup. Please check your connection and try again.', {
                    actions: [
                        {
                            text: 'Check Permissions',
                            onclick: 'checkPermissions()'
                        },
                        {
                            text: 'Retry',
                            onclick: 'forceCleanup()'
                        }
                    ]
                });
                console.error('Force cleanup error:', error);
            }
        }

        async function checkPermissions() {
            const repoPath = AppState.currentRepoForCleanup || AppState.currentRepo;
            if (!repoPath) {
                NotificationManager.error('Permission Check Error', 'No repository selected.');
                return;
            }
            
            const loadingNotification = NotificationManager.info('Checking Permissions', 'Analyzing repository permissions...', {
                autoClose: false
            });
            
            try {
                const url = repoPath !== AppState.currentRepo 
                    ? `?action=check_permissions&current_repo=${encodeURIComponent(repoPath)}`
                    : '?action=check_permissions';
                
                const data = await apiRequest(url);
                
                NotificationManager.close(loadingNotification);
                
                handleApiResponse(data, (data) => {
                    showPermissionReport(data.data);
                }, 'CheckPermissions');
            } catch (error) {
                NotificationManager.close(loadingNotification);
                NotificationManager.error('Permission Check Failed', 'Failed to check repository permissions.');
                console.error('Check permissions error:', error);
            }
        }

        function showPermissionReport(data) {
            const report = `
                <div style="font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa;">
                    <h2 style="color: #dc3545; border-bottom: 2px solid #dc3545; padding-bottom: 10px;">
                        Permission Analysis for: ${data.repo_path}
                    </h2>
                    
                    <h3>File System Permissions:</h3>
                    <ul>
                        <li>Repository Readable: ${data.permissions.repo_readable ? '✓ Yes' : '✗ No'}</li>
                        <li>Repository Writable: ${data.permissions.repo_writable ? '✓ Yes' : '✗ No'}</li>
                        <li>.svn Directory Readable: ${data.permissions.svn_dir_readable ? '✓ Yes' : '✗ No'}</li>
                        <li>.svn Directory Writable: ${data.permissions.svn_dir_writable ? '✓ Yes' : '✗ No'}</li>
                        <li>Repository Owner: ${data.permissions.repo_owner}</li>
                        <li>Web Server User: ${data.permissions.current_user}</li>
                        <li>Repository Permissions: ${data.permissions.repo_permissions}</li>
                        <li>.svn Permissions: ${data.permissions.svn_permissions}</li>
                    </ul>
                    
                    <h3>SVN Command Test:</h3>
                    <p><strong>Command:</strong> <code>${data.svn_test.command}</code></p>
                    <p><strong>Success:</strong> ${data.svn_test.success ? '✓ Yes' : '✗ No'}</p>
                    <p><strong>Return Code:</strong> ${data.svn_test.return_code}</p>
                    <p><strong>Output:</strong></p>
                    <pre style="background: white; padding: 10px; border: 1px solid #ddd; border-radius: 4px; overflow-x: auto;">${data.svn_test.output}</pre>
                    
                    ${data.diagnosis.length > 0 ? `
                        <h3>Issues Detected:</h3>
                        <ul style="color: #dc3545;">
                            ${data.diagnosis.map(issue => `<li>${issue}</li>`).join('')}
                        </ul>
                    ` : ''}
                </div>
            `;
            NotificationManager.info('Permission Report', report, { duration: 0, autoClose: false });
        }
    </script>
</body>
</html>
            'suggested_action' => 'Enter the absolute path to your SVN repositories directory.',
            'is_recoverable' => true,
            'debug' => $errorDetails
        ));
        exit();
    }

    $action = $_GET['action'];
    $response = array('status' => 'error', 'message' => 'Invalid action.');

    try {
        switch ($action) {
            case 'list_repos':
                $repos = array();
                $iterator = new RecursiveIteratorIterator(
                    new RecursiveDirectoryIterator($repoBasePath, RecursiveDirectoryIterator::SKIP_DOTS),
                    RecursiveIteratorIterator::SELF_FIRST
                );
                foreach ($iterator as $file) {
                    if ($file->getFilename() === '.svn' && $file->isDir()) {
                        $repoPath = $file->getPath();
                        if (is_path_safe($repoPath, $repoBasePath)) {
                            $repos[] = array(
                                'name' => basename($repoPath),
                                'path' => $repoPath
                            );
                        }
                    }
                }
                $response = array('status' => 'success', 'data' => array_unique($repos, SORT_REGULAR));
                break;

            case 'get_status':
                $repoPath = isset($_GET['path']) ? $_GET['path'] : '';
                if (empty($repoPath) || !is_path_safe($repoPath, $repoBasePath)) {
                    throw new Exception('Invalid or unsafe repository path.');
                }
                $command = 'svn status --xml ' . escapeshellarg($repoPath) . ' 2>&1';
                $xmlOutput = shell_exec($command);

                $files = array();
                if ($xmlOutput && strpos($xmlOutput, '<?xml') !== false) {
                    $xml = simplexml_load_string($xmlOutput);
                    if ($xml && $xml->target) {
                        foreach ($xml->target->entry as $entry) {
                            $status = (string)$entry->{'wc-status'}['item'];
                            if (in_array($status, array('modified', 'added', 'deleted', 'unversioned'))) {
                                $files[] = array(
                                    'path' => (string)$entry['path'],
                                    'status' => $status
                                );
                            }
                        }
                    }
                } else {
                    // Handle SVN errors in status command
                    $errorAnalysis = analyzeSvnError($xmlOutput);
                    logSvnError('get_status', 'SVN status command failed', array(
                        'command' => $command,
                        'output' => $xmlOutput,
                        'repo_path' => $repoPath,
                        'analysis' => $errorAnalysis
                    ));
                    
                    throw new Exception($errorAnalysis['user_message']);
                }
                $response = array('status' => 'success', 'data' => $files);
                break;

            case 'get_diff':
                $filePath = isset($_GET['path']) ? $_GET['path'] : '';
                if (empty($filePath) || !is_path_safe($filePath, $repoBasePath)) {
                    throw new Exception('Invalid or unsafe file path.');
                }
                $command = 'svn diff ' . escapeshellarg($filePath) . ' 2>&1';
                $diffOutput = shell_exec($command);
                
                // Check for SVN errors in diff output
                if (strpos($diffOutput, 'svn: E') !== false) {
                    $errorAnalysis = analyzeSvnError($diffOutput);
                    logSvnError('get_diff', 'SVN diff command failed', array(
                        'command' => $command,
                        'output' => $diffOutput,
                        'file_path' => $filePath,
                        'analysis' => $errorAnalysis
                    ));
                    
                    throw new Exception($errorAnalysis['user_message']);
                }
                
                // Parse and format the diff
                $formattedDiff = formatDiffOutput($diffOutput, $filePath);
                $response = array('status' => 'success', 'data' => $formattedDiff);
                break;

            case 'svn_add':
                $data = json_decode(file_get_contents('php://input'), true);
                $files = isset($data['files']) ? $data['files'] : array();
                if (empty($files)) {
                    throw new Exception('No files selected for adding.');
                }

                $safeFiles = array();
                foreach ($files as $file) {
                    if (is_path_safe($file, $repoBasePath)) {
                        $safeFiles[] = escapeshellarg($file);
                    }
                }
                if (empty($safeFiles)) {
                    throw new Exception('No valid files provided for adding.');
                }

                $command = 'svn add ' . implode(' ', $safeFiles) . ' 2>&1';
                $output = shell_exec($command);

                // Check for errors
                if (strpos($output, 'svn: E') !== false) {
                    $errorAnalysis = analyzeSvnError($output);
                    logSvnError('svn_add', 'SVN add command failed', array(
                        'command' => $command,
                        'output' => $output,
                        'files' => $files,
                        'analysis' => $errorAnalysis
                    ));

                    $response = array(
                        'status' => 'error',
                        'error_type' => $errorAnalysis['error_type'],
                        'message' => $errorAnalysis['user_message'],
                        'suggested_action' => $errorAnalysis['suggested_action'],
                        'is_recoverable' => $errorAnalysis['is_recoverable']
                    );
                } else {
                    $response = array('status' => 'success', 'message' => 'Files added successfully.', 'data' => $output);
                }
                break;

            case 'commit':
                $data = json_decode(file_get_contents('php://input'), true);
                $files = isset($data['files']) ? $data['files'] : array();
                $message = isset($data['message']) ? $data['message'] : '';

                if (empty($files)) {
                    throw new Exception('No files selected for commit.');
                }
                if (empty($message)) {
                    throw new Exception('Commit message is required.');
                }

                $fileArgs = '';
                foreach ($files as $file) {
                    if (is_path_safe($file, $repoBasePath)) {
                        $fileArgs .= escapeshellarg($file) . ' ';
                    }
                }

                if (empty($fileArgs)) {
                    throw new Exception('No valid files selected for commit.');
                }

                $command = 'svn commit -m ' . escapeshellarg($message) . ' ' . $fileArgs . ' --non-interactive 2>&1';
                $commitOutput = shell_exec($command);

                // Check for errors
                if (strpos($commitOutput, 'svn: E') !== false) {
                    $errorAnalysis = analyzeSvnError($commitOutput);
                    logSvnError('commit', 'SVN commit command failed', array(
                        'command' => $command,
                        'output' => $commitOutput,
                        'files' => $files,
                        'message' => $message,
                        'analysis' => $errorAnalysis
                    ));

                    $response = array(
                        'status' => 'error',
                        'error_type' => $errorAnalysis['error_type'],
                        'message' => $errorAnalysis['user_message'],
                        'suggested_action' => $errorAnalysis['suggested_action'],
                        'is_recoverable' => $errorAnalysis['is_recoverable'],
                        'raw_output' => $commitOutput
                    );
                } else {
                    $response = array('status' => 'success', 'message' => 'Commit completed successfully.', 'data' => $commitOutput);
                }
                break;

            case 'check_permissions':
                $repoPath = isset($_GET['path']) ? $_GET['path'] : '';
                if (empty($repoPath)) {
                    $repoPath = isset($_GET['current_repo']) ? $_GET['current_repo'] : $repoBasePath;
                }

                if (empty($repoPath) || !is_path_safe($repoPath, $repoBasePath)) {
                    throw new Exception('Invalid or unsafe repository path for permission check.');
                }

                $permissions = checkSvnPermissions($repoPath);
                $svnTest = testSvnCommand($repoPath);

                $response = array(
                    'status' => 'success',
                    'data' => array(
                        'repo_path' => $repoPath,
                        'permissions' => $permissions,
                        'svn_test' => $svnTest,
                        'diagnosis' => array()
                    )
                );

                // Add diagnostic messages
                $diagnosis = array();
                if (!$permissions['repo_writable']) {
                    $diagnosis[] = 'Repository directory is not writable by web server';
                }
                if (!$permissions['svn_dir_writable']) {
                    $diagnosis[] = '.svn directory is not writable by web server';
                }
                if (!empty($permissions['lock_files'])) {
                    $diagnosis[] = 'Found ' . count($permissions['lock_files']) . ' potential lock file(s)';
                }
                if (!$svnTest['success']) {
                    $diagnosis[] = 'SVN info command failed - may indicate SVN client issues';
                }
                if ($permissions['current_user'] !== $permissions['repo_owner']) {
                    $diagnosis[] = 'Web server user differs from repository owner';
                }

                $response['data']['diagnosis'] = $diagnosis;
                break;

            case 'svn_cleanup':
                $repoPath = isset($_GET['path']) ? $_GET['path'] : '';
                if (empty($repoPath)) {
                    $repoPath = isset($_GET['current_repo']) ? $_GET['current_repo'] : $repoBasePath;
                }

                if (empty($repoPath) || !is_path_safe($repoPath, $repoBasePath)) {
                    throw new Exception('Invalid or unsafe repository path for cleanup.');
                }

                // Pre-cleanup permission check
                $permissions = checkSvnPermissions($repoPath);
                $preCleanupLog = array(
                    'repo_path' => $repoPath,
                    'permissions' => $permissions,
                    'timestamp' => date('Y-m-d H:i:s')
                );

                logSvnError('svn_cleanup_pre_check', 'Pre-cleanup permission analysis', $preCleanupLog);

                // Check basic requirements
                if (!is_dir($repoPath)) {
                    throw new Exception('Repository path does not exist: ' . basename($repoPath));
                }

                if (!is_dir($repoPath . '/.svn')) {
                    throw new Exception('Path is not an SVN working copy: ' . basename($repoPath));
                }

                // Check if we have necessary permissions
                if (!$permissions['repo_writable'] || !$permissions['svn_dir_writable']) {
                    $permissionError = array(
                        'status' => 'error',
                        'error_type' => 'permission',
                        'message' => 'Insufficient file system permissions for SVN cleanup.',
                        'details' => array(
                            'repo_writable' => $permissions['repo_writable'],
                            'svn_dir_writable' => $permissions['svn_dir_writable'],
                            'current_user' => $permissions['current_user'],
                            'repo_owner' => $permissions['repo_owner'],
                            'repo_perms' => $permissions['repo_perms'],
                            'svn_perms' => $permissions['svn_perms']
                        ),
                        'suggested_actions' => array()
                    );

                    // Provide specific suggestions based on the permission issue
                    if (!$permissions['repo_writable']) {
                        $permissionError['suggested_actions'][] = 'Repository directory needs write permissions for ' . $permissions['current_user'];
                    }
                    if (!$permissions['svn_dir_writable']) {
                        $permissionError['suggested_actions'][] = '.svn directory needs write permissions for ' . $permissions['current_user'];
                    }
                    if ($permissions['current_user'] !== $permissions['repo_owner']) {
                        $permissionError['suggested_actions'][] = 'Consider changing repository ownership to ' . $permissions['current_user'];
                        $permissionError['suggested_actions'][] = 'Or add ' . $permissions['current_user'] . ' to the same group as ' . $permissions['repo_owner'];
                    }

                    $response = $permissionError;
                    break;
                }

                // Execute cleanup with detailed logging
                $command = 'svn cleanup ' . escapeshellarg($repoPath) . ' 2>&1';
                $startTime = microtime(true);

                // Use exec to get both output and return code
                $output = array();
                $returnCode = 0;
                exec($command, $output, $returnCode);

                $endTime = microtime(true);
                $executionTime = round(($endTime - $startTime) * 1000, 2);
                $outputString = implode("\n", $output);

                // Detailed logging
                $cleanupLog = array(
                    'command' => $command,
                    'output' => $outputString,
                    'return_code' => $returnCode,
                    'execution_time_ms' => $executionTime,
                    'repo_path' => $repoPath,
                    'pre_permissions' => $permissions
                );

                logSvnError('svn_cleanup_execution', 'SVN cleanup command executed', $cleanupLog);

                if ($returnCode === 0) {
                    // Post-cleanup verification
                    $postPermissions = checkSvnPermissions($repoPath);

                    $response = array(
                        'status' => 'success',
                        'message' => 'SVN cleanup completed successfully.',
                        'data' => array(
                            'output' => $outputString,
                            'repo_path' => basename($repoPath),
                            'execution_time_ms' => $executionTime,
                            'return_code' => $returnCode,
                            'locks_removed' => count($permissions['lock_files']) - count($postPermissions['lock_files'])
                        )
                    );
                } else {
                    // Enhanced error analysis for cleanup failures
                    $errorAnalysis = analyzeSvnError($outputString);

                    // Add permission-specific error details
                    if (strpos($outputString, 'Permission denied') !== false) {
                        $errorAnalysis['error_type'] = 'permission';
                        $errorAnalysis['user_message'] = 'Permission denied during SVN cleanup. The web server lacks necessary file system permissions.';
                        $errorAnalysis['suggested_action'] = 'Contact your system administrator to fix file permissions.';
                        $errorAnalysis['is_recoverable'] = false;
                        $errorAnalysis['permission_details'] = $permissions;
                    }

                    logSvnError('svn_cleanup_failed', 'SVN cleanup failed', array_merge($cleanupLog, array(
                        'analysis' => $errorAnalysis
                    )));

                    $response = array(
                        'status' => 'error',
                        'error_type' => $errorAnalysis['error_type'],
                        'message' => $errorAnalysis['user_message'],
                        'suggested_action' => $errorAnalysis['suggested_action'],
                        'is_recoverable' => $errorAnalysis['is_recoverable'],
                        'raw_output' => $outputString,
                        'repo_path' => basename($repoPath),
                        'execution_time_ms' => $executionTime,
                        'return_code' => $returnCode,
                        'permission_details' => $errorAnalysis['error_type'] === 'permission' ? $permissions : null
                    );
                }
                break;

            case 'get_permission_fix':
                $repoPath = isset($_GET['path']) ? $_GET['path'] : '';
                if (empty($repoPath)) {
                    $repoPath = isset($_GET['current_repo']) ? $_GET['current_repo'] : $repoBasePath;
                }

                if (empty($repoPath) || !is_path_safe($repoPath, $repoBasePath)) {
                    throw new Exception('Invalid or unsafe repository path for permission fix.');
                }

                $permissions = checkSvnPermissions($repoPath);
                $fixCommands = generatePermissionFixCommands($repoPath, $permissions);

                $response = array(
                    'status' => 'success',
                    'data' => array(
                        'repo_path' => $repoPath,
                        'current_permissions' => $permissions,
                        'fix_commands' => $fixCommands,
                        'summary' => array(
                            'issue' => 'Web server lacks write permissions to SVN repository',
                            'cause' => $permissions['repo_owner'] === 'null' ?
                                      'Repository ownership is not properly set' :
                                      'Web server user is not in repository owner group',
                            'solution' => 'Run the provided commands as system administrator'
                        )
                    )
                );
                break;
        }
    } catch (Exception $e) {
        logSvnError($action, $e->getMessage(), array(
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ));

        $response = array(
            'status' => 'error',
            'error_type' => 'exception',
            'message' => $e->getMessage(),
            'suggested_action' => 'Please try again or contact support if the problem persists.',
            'is_recoverable' => true
        );
    }

    echo json_encode($response);
    exit();
}

// =============================================================================
//                           ** HTML FRONTEND **
// =============================================================================

$currentPath = getRepoBasePath();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple SVN Web Client</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            background-color: #f8f9fa;
            color: #212529;
        }

        .container {
            max-width: 1300px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .path-selector {
            background: #e9ecef;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .path-selector h3 {
            margin-top: 0;
            color: #495057;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .current-path {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }

        .alert {
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        /* Diff Viewer Styles */
        .diff-viewer {
            border: 1px solid #d1d5da;
            border-radius: 6px;
            background: #fff;
            margin-top: 15px;
        }

        .diff-header {
            background: #f6f8fa;
            border-bottom: 1px solid #d1d5da;
            padding: 10px 16px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, monospace;
            font-size: 14px;
        }

        .diff-filename {
            font-weight: 600;
            color: #24292e;
        }

        .diff-stats {
            margin-top: 5px;
            font-size: 12px;
        }

        .diff-stats-added {
            color: #28a745;
            font-weight: 600;
        }

        .diff-stats-removed {
            color: #d73a49;
            font-weight: 600;
        }

        .diff-content {
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 12px;
            line-height: 1.45;
            overflow-x: auto;
        }

        .diff-line {
            display: flex;
            min-height: 20px;
        }

        .diff-line:hover {
            background-color: #f8f9fa;
        }

        .diff-line-num {
            width: 50px;
            padding: 0 8px;
            text-align: right;
            color: #586069;
            background: #f6f8fa;
            border-right: 1px solid #e1e4e8;
            user-select: none;
            flex-shrink: 0;
        }

        .diff-line-num-old {
            border-right: none;
        }

        .diff-line-num-new {
            border-right: 1px solid #e1e4e8;
        }

        .diff-line-prefix {
            width: 20px;
            text-align: center;
            padding: 0 4px;
            user-select: none;
            flex-shrink: 0;
        }

        .diff-line-content {
            padding: 0 8px;
            white-space: pre;
            flex: 1;
            min-width: 0;
        }

        .diff-line-added {
            background-color: #e6ffed;
        }

        .diff-line-added .diff-line-num {
            background-color: #cdffd8;
        }

        .diff-line-added .diff-line-prefix {
            color: #28a745;
            font-weight: bold;
        }

        .diff-line-removed {
            background-color: #ffeef0;
        }

        .diff-line-removed .diff-line-num {
            background-color: #ffdce0;
        }

        .diff-line-removed .diff-line-prefix {
            color: #d73a49;
            font-weight: bold;
        }

        .diff-line-context .diff-line-prefix {
            color: #586069;
        }

        .diff-hunk-header {
            background: #f1f8ff;
            color: #0366d6;
            padding: 4px 8px;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 12px;
            border-top: 1px solid #c8e1ff;
            border-bottom: 1px solid #c8e1ff;
        }

        .diff-empty {
            padding: 20px;
            text-align: center;
            color: #586069;
            font-style: italic;
        }

        .diff-actions {
            padding: 8px 16px;
            background: #f6f8fa;
            border-top: 1px solid #d1d5da;
            text-align: right;
        }

        .btn-copy {
            background: #f6f8fa;
            border: 1px solid #d1d5da;
            color: #24292e;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            border-radius: 3px;
        }

        .btn-copy:hover {
            background: #e1e4e8;
        }

        .btn-copy:active {
            background: #d1d5da;
        }

        @media (max-width: 768px) {
            .diff-line-num {
                width: 35px;
                font-size: 10px;
            }

            .diff-content {
                font-size: 11px;
            }

            .diff-line-content {
                padding: 0 4px;
            }
        }

        /* Notification System Styles */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
        }

        .notification {
            padding: 12px 16px;
            margin-bottom: 10px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: flex;
            align-items: flex-start;
            animation: slideIn 0.3s ease-out;
            position: relative;
            font-size: 14px;
            line-height: 1.4;
        }

        .notification-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .notification-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .notification-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .notification-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .notification-icon {
            margin-right: 10px;
            font-weight: bold;
            flex-shrink: 0;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .notification-message {
            margin-bottom: 8px;
        }

        .notification-actions {
            margin-top: 8px;
        }

        .notification-btn {
            background: none;
            border: 1px solid currentColor;
            color: inherit;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 8px;
        }

        .notification-btn:hover {
            background: rgba(0,0,0,0.1);
        }

        .notification-close {
            position: absolute;
            top: 8px;
            right: 8px;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: inherit;
            opacity: 0.7;
        }

        .notification-close:hover {
            opacity: 1;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .notification.slide-out {
            animation: slideOut 0.3s ease-in forwards;
        }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .btn-loading {
            position: relative;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Notification Container -->
        <div id="notification-container" class="notification-container"></div>

        <h1>Simple SVN Web Client</h1>

        <div class="path-selector">
            <h3>Repository Base Path Configuration</h3>

            <?php if (isset($_POST['repo_base_path'])): ?>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-bottom: 15px;">
                    <strong>Debug Info:</strong><br>
                    Submitted path: <?php echo htmlspecialchars($_POST['repo_base_path'], ENT_QUOTES, 'UTF-8'); ?><br>
                    Normalized path: <?php echo htmlspecialchars(rtrim($_POST['repo_base_path'], '/') . '/', ENT_QUOTES, 'UTF-8'); ?><br>
                    Directory exists: <?php echo is_dir($_POST['repo_base_path']) ? 'Yes' : 'No'; ?><br>
                    Directory readable: <?php echo is_readable($_POST['repo_base_path']) ? 'Yes' : 'No'; ?><br>
                    Current working directory: <?php echo getcwd(); ?><br>
                </div>
            <?php endif; ?>

            <form method="post" action="">
                <div class="form-group">
                    <label for="repo_base_path">Repository Base Path:</label>
                    <input type="text"
                           id="repo_base_path"
                           name="repo_base_path"
                           class="form-control"
                           value="<?php echo htmlspecialchars($currentPath ? $currentPath : '', ENT_QUOTES, 'UTF-8'); ?>"
                           placeholder="/path/to/your/repositories/"
                           required>
                    <small style="color: #6c757d;">
                        Enter the absolute path to the directory containing your SVN working copies.<br>
                        <strong>Expected structure:</strong> Your path should contain subdirectories with .svn folders.<br>
                        <strong>Example:</strong> If you have /path/to/repos/project1/.svn, enter /path/to/repos/
                    </small>
                </div>
                <button type="submit" class="btn btn-primary">Set Repository Path</button>
            </form>

            <?php if ($currentPath): ?>
                <div class="current-path">
                    <strong>Current Path:</strong> <?php echo htmlspecialchars($currentPath, ENT_QUOTES, 'UTF-8'); ?>
                    <br><strong>SVN repositories found:</strong>
                    <?php
                    $svnCount = 0;
                    if (is_dir($currentPath)) {
                        $iterator = new RecursiveIteratorIterator(
                            new RecursiveDirectoryIterator($currentPath, RecursiveDirectoryIterator::SKIP_DOTS),
                            RecursiveIteratorIterator::SELF_FIRST
                        );
                        foreach ($iterator as $file) {
                            if ($file->getFilename() === '.svn' && $file->isDir()) {
                                $svnCount++;
                            }
                        }
                    }
                    echo $svnCount . ' SVN working copies detected';
                    ?>
                </div>
            <?php endif; ?>
        </div>

        <?php if (!$currentPath): ?>
            <div class="alert alert-warning">
                <strong>Notice:</strong> Please set your repository base path above to begin using the SVN client.
            </div>
        <?php else: ?>
            <!-- SVN Client Interface -->
            <div id="svn-interface">
                <div style="display: flex; gap: 20px;">
                    <div style="flex: 1;">
                        <h2>Repositories</h2>
                        <button id="refresh-repos" class="btn btn-primary">Refresh</button>
                        <div id="repo-list" style="margin-top: 15px;"></div>
                    </div>

                    <div style="flex: 2;">
                        <h2>Files Status</h2>
                        <div id="file-list"></div>
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <h2>Diff Viewer</h2>
                    <div id="diff-viewer" class="diff-viewer" style="display: none;">
                        <div class="diff-header">
                            <div class="diff-filename" id="diff-filename"></div>
                            <div class="diff-stats" id="diff-stats"></div>
                        </div>
                        <div class="diff-content" id="diff-content"></div>
                        <div class="diff-actions">
                            <button class="btn-copy" onclick="copyDiffToClipboard()">Copy Diff</button>
                        </div>
                    </div>
                    <div id="diff-placeholder" style="background: #f8f9fa; padding: 20px; border-radius: 4px; text-align: center; color: #586069; font-style: italic;">
                        Click on a file to view its diff
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <h2>Commit</h2>
                    <div style="margin-bottom: 15px;">
                        <label for="commit-message">Commit Message:</label>
                        <textarea id="commit-message" class="form-control" rows="3" placeholder="Enter your commit message..."></textarea>
                    </div>
                    <button id="commit-btn" class="btn btn-primary">Commit Selected Files</button>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <?php if ($currentPath): ?>
    <script>
        // Enhanced Notification System
        var NotificationManager = {
            container: null,

            init: function() {
                this.container = document.getElementById('notification-container');
            },

            show: function(type, title, message, options) {
                options = options || {};
                var notification = document.createElement('div');
                notification.className = 'notification notification-' + type;

                var icon = this.getIcon(type);
                var actions = options.actions ? this.renderActions(options.actions) : '';

                notification.innerHTML =
                    '<span class="notification-icon">' + icon + '</span>' +
                    '<div class="notification-content">' +
                        '<div class="notification-title">' + title + '</div>' +
                        '<div class="notification-message">' + message + '</div>' +
                        actions +
                    '</div>' +
                    '<button class="notification-close" onclick="NotificationManager.close(this.parentElement)">&times;</button>';

                this.container.appendChild(notification);

                // Auto-close after delay
                var autoClose = options.autoClose !== false;
                if (autoClose) {
                    var delay = options.delay || (type === 'error' ? 8000 : 5000);
                    setTimeout(function() {
                        NotificationManager.close(notification);
                    }, delay);
                }

                return notification;
            },

            getIcon: function(type) {
                switch(type) {
                    case 'success': return '✓';
                    case 'error': return '✗';
                    case 'warning': return '⚠';
                    case 'info': return 'ℹ';
                    default: return '•';
                }
            },

            renderActions: function(actions) {
                var html = '<div class="notification-actions">';
                for (var i = 0; i < actions.length; i++) {
                    var action = actions[i];
                    html += '<button class="notification-btn" onclick="' + action.onclick + '">' + action.text + '</button>';
                }
                html += '</div>';
                return html;
            },

            close: function(notification) {
                notification.classList.add('slide-out');
                setTimeout(function() {
                    if (notification.parentElement) {
                        notification.parentElement.removeChild(notification);
                    }
                }, 300);
            },

            success: function(title, message, options) {
                return this.show('success', title, message, options);
            },

            error: function(title, message, options) {
                return this.show('error', title, message, options);
            },

            warning: function(title, message, options) {
                return this.show('warning', title, message, options);
            },

            info: function(title, message, options) {
                return this.show('info', title, message, options);
            }
        };

        // Enhanced error handling for AJAX requests
        function handleApiResponse(data, successCallback, operation) {
            if (data.status === 'success') {
                if (successCallback) successCallback(data);
            } else {
                var title = 'Operation Failed';
                var message = data.message || 'An unexpected error occurred.';
                var actions = [];

                if (data.error_type === 'permission') {
                    title = 'Permission Error';
                    actions.push({
                        text: 'Show Fix Guide',
                        onclick: 'showPermissionFix()'
                    });
                    actions.push({
                        text: 'Check Permissions',
                        onclick: 'checkPermissions()'
                    });
                } else {
                    if (data.is_recoverable) {
                        actions.push({
                            text: 'Retry',
                            onclick: 'retry' + operation + '()'
                        });
                    }

                    // Always offer permission check for failed operations
                    actions.push({
                        text: 'Check Permissions',
                        onclick: 'checkPermissions()'
                    });
                }

                if (data.raw_output) {
                    actions.push({
                        text: 'View Details',
                        onclick: 'showCleanupDetails("' + encodeURIComponent(data.raw_output) + '")'
                    });
                }

                NotificationManager.error(title, message, {
                    actions: actions,
                    autoClose: false
                });

                console.error('SVN Tool Error:', {
                    operation: operation,
                    response: data,
                    timestamp: new Date().toISOString()
                });
            }
        }

        // JavaScript code for SVN operations with enhanced error handling
        let selectedFiles = [];
        let currentRepo = '';

        function setButtonLoading(buttonId, loading) {
            var button = document.getElementById(buttonId);
            if (loading) {
                button.classList.add('btn-loading');
                button.disabled = true;
            } else {
                button.classList.remove('btn-loading');
                button.disabled = false;
            }
        }

        function loadRepositories() {
            setButtonLoading('refresh-repos', true);

            fetch('?action=list_repos')
                .then(function(response) {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(function(data) {
                    setButtonLoading('refresh-repos', false);
                    handleApiResponse(data, function(data) {
                        var repoList = document.getElementById('repo-list');
                        var html = '';
                        if (data.data.length === 0) {
                            html = '<div style="color: #6c757d; font-style: italic;">No SVN repositories found in the specified path.</div>';
                        } else {
                            data.data.forEach(function(repo) {
                                html += '<div style="padding: 8px; border: 1px solid #dee2e6; margin-bottom: 5px; cursor: pointer; border-radius: 4px;" onclick="selectRepo(\'' + repo.path + '\')">' + repo.name + '</div>';
                            });
                        }
                        repoList.innerHTML = html;
                        NotificationManager.success('Success', 'Repositories loaded successfully.');
                    }, 'LoadRepositories');
                })
                .catch(function(error) {
                    setButtonLoading('refresh-repos', false);
                    NotificationManager.error('Network Error', 'Failed to load repositories. Please check your connection and try again.', {
                        actions: [{
                            text: 'Retry',
                            onclick: 'loadRepositories()'
                        }]
                    });
                    console.error('Load repositories error:', error);
                });
        }

        // Global variable to track current repository for cleanup
        var currentRepoForCleanup = '';

        function setCurrentRepoForCleanup(repoPath) {
            currentRepoForCleanup = repoPath;
        }

        function checkPermissions() {
            var repoPath = currentRepoForCleanup || currentRepo;
            if (!repoPath) {
                NotificationManager.error('Permission Check Error', 'No repository selected for permission check.');
                return;
            }

            var loadingNotification = NotificationManager.info('Checking Permissions', 'Analyzing file system permissions for ' + repoPath.split('/').pop() + '...', {
                autoClose: false
            });

            var url = '?action=check_permissions';
            if (repoPath !== currentRepo) {
                url += '&current_repo=' + encodeURIComponent(repoPath);
            }

            fetch(url)
                .then(function(response) { return response.json(); })
                .then(function(data) {
                    NotificationManager.close(loadingNotification);

                    if (data.status === 'success') {
                        showPermissionReport(data.data);
                    } else {
                        NotificationManager.error('Permission Check Failed', data.message || 'Unable to check permissions.');
                    }
                })
                .catch(function(error) {
                    NotificationManager.close(loadingNotification);
                    NotificationManager.error('Permission Check Error', 'Failed to check permissions. Please try again.');
                    console.error('Permission check error:', error);
                });
        }

        function showPermissionReport(data) {
            var report = '<div style="font-family: monospace; font-size: 12px; line-height: 1.4;">';
            report += '<h3>Permission Analysis for: ' + data.repo_path.split('/').pop() + '</h3>';

            // File System Permissions
            report += '<h4>File System Permissions:</h4>';
            report += '<table style="border-collapse: collapse; width: 100%;">';
            report += '<tr><td style="padding: 4px; border: 1px solid #ddd;"><strong>Repository Readable:</strong></td><td style="padding: 4px; border: 1px solid #ddd;">' + (data.permissions.repo_readable ? '✓ Yes' : '✗ No') + '</td></tr>';
            report += '<tr><td style="padding: 4px; border: 1px solid #ddd;"><strong>Repository Writable:</strong></td><td style="padding: 4px; border: 1px solid #ddd;">' + (data.permissions.repo_writable ? '✓ Yes' : '✗ No') + '</td></tr>';
            report += '<tr><td style="padding: 4px; border: 1px solid #ddd;"><strong>.svn Directory Readable:</strong></td><td style="padding: 4px; border: 1px solid #ddd;">' + (data.permissions.svn_dir_readable ? '✓ Yes' : '✗ No') + '</td></tr>';
            report += '<tr><td style="padding: 4px; border: 1px solid #ddd;"><strong>.svn Directory Writable:</strong></td><td style="padding: 4px; border: 1px solid #ddd;">' + (data.permissions.svn_dir_writable ? '✓ Yes' : '✗ No') + '</td></tr>';
            report += '<tr><td style="padding: 4px; border: 1px solid #ddd;"><strong>Repository Owner:</strong></td><td style="padding: 4px; border: 1px solid #ddd;">' + data.permissions.repo_owner + '</td></tr>';
            report += '<tr><td style="padding: 4px; border: 1px solid #ddd;"><strong>Web Server User:</strong></td><td style="padding: 4px; border: 1px solid #ddd;">' + data.permissions.current_user + '</td></tr>';
            report += '<tr><td style="padding: 4px; border: 1px solid #ddd;"><strong>Repository Permissions:</strong></td><td style="padding: 4px; border: 1px solid #ddd;">' + data.permissions.repo_perms + '</td></tr>';
            report += '<tr><td style="padding: 4px; border: 1px solid #ddd;"><strong>.svn Permissions:</strong></td><td style="padding: 4px; border: 1px solid #ddd;">' + data.permissions.svn_perms + '</td></tr>';
            report += '</table>';

            // Lock Files
            if (data.permissions.lock_files.length > 0) {
                report += '<h4>Lock Files Found:</h4>';
                report += '<ul>';
                data.permissions.lock_files.forEach(function(lock) {
                    report += '<li>' + lock.file + ' (' + lock.size + ' bytes) - ' + (lock.writable ? 'Writable' : 'Not writable') + '</li>';
                });
                report += '</ul>';
            } else {
                report += '<h4>Lock Files: None found</h4>';
            }

            // SVN Test
            report += '<h4>SVN Command Test:</h4>';
            report += '<p><strong>Command:</strong> ' + data.svn_test.command + '</p>';
            report += '<p><strong>Success:</strong> ' + (data.svn_test.success ? '✓ Yes' : '✗ No') + '</p>';
            report += '<p><strong>Return Code:</strong> ' + data.svn_test.return_code + '</p>';
            if (data.svn_test.output) {
                report += '<p><strong>Output:</strong></p><pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">' + data.svn_test.output + '</pre>';
            }

            // Diagnosis
            if (data.diagnosis.length > 0) {
                report += '<h4>Issues Detected:</h4>';
                report += '<ul style="color: #dc3545;">';
                data.diagnosis.forEach(function(issue) {
                    report += '<li>' + issue + '</li>';
                });
                report += '</ul>';
            } else {
                report += '<h4 style="color: #28a745;">No Issues Detected</h4>';
            }

            report += '</div>';

            var reportWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes');
            reportWindow.document.write(
                '<html><head><title>SVN Permission Report</title>' +
                '<style>body{font-family:Arial,sans-serif;padding:20px;background:#f8f9fa;}</style>' +
                '</head><body>' + report + '</body></html>'
            );
        }

        // Enhanced forceCleanup with permission checking
        function forceCleanup() {
            var repoPath = currentRepoForCleanup || currentRepo;
            if (!repoPath) {
                NotificationManager.error('Cleanup Error', 'No repository selected for cleanup.');
                return;
            }

            // Show loading notification
            var loadingNotification = NotificationManager.info('Cleaning Up', 'Running SVN cleanup on ' + repoPath.split('/').pop() + '...', {
                autoClose: false
            });

            var url = '?action=svn_cleanup';
            if (repoPath !== currentRepo) {
                url += '&current_repo=' + encodeURIComponent(repoPath);
            }

            fetch(url)
                .then(function(response) {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(function(data) {
                    NotificationManager.close(loadingNotification);

                    if (data.status === 'success') {
                        var message = 'SVN cleanup completed successfully for ' + data.data.repo_path + '.';
                        if (data.data.locks_removed > 0) {
                            message += ' Removed ' + data.data.locks_removed + ' lock file(s).';
                        }
                        message += ' (Execution time: ' + data.data.execution_time_ms + 'ms)';

                        NotificationManager.success('Cleanup Successful', message, {
                            actions: [{
                                text: 'Refresh Repository',
                                onclick: 'refreshAfterCleanup()'
                            }]
                        });

                        setTimeout(function() {
                            if (currentRepo) {
                                selectRepo(currentRepo);
                            }
                        }, 1000);

                    } else {
                        var title = 'Cleanup Failed';
                        var message = data.message || 'SVN cleanup operation failed.';
                        var actions = [];

                        // Enhanced error handling for permission issues
                        if (data.error_type === 'permission') {
                            title = 'Permission Error';
                            actions.push({
                                text: 'Check Permissions',
                                onclick: 'checkPermissions()'
                            });

                            if (data.permission_details) {
                                actions.push({
                                    text: 'View Permission Details',
                                    onclick: 'showPermissionDetails(' + JSON.stringify(data.permission_details) + ')'
                                });
                            }

                            // Add system admin contact if available
                            actions.push({
                                text: 'Contact Admin',
                                onclick: 'window.open("mailto:<EMAIL>?subject=SVN Permission Issue&body=Repository: ' + encodeURIComponent(repoPath) + '%0A%0AError: ' + encodeURIComponent(message) + '", "_blank")'
                            });
                        } else {
                            if (data.is_recoverable !== false) {
                                actions.push({
                                    text: 'Try Again',
                                    onclick: 'forceCleanup()'
                                });
                            }

                            actions.push({
                                text: 'Check Permissions',
                                onclick: 'checkPermissions()'
                            });
                        }

                        if (data.raw_output) {
                            actions.push({
                                text: 'View Raw Output',
                                onclick: 'showCleanupDetails("' + encodeURIComponent(data.raw_output) + '")'
                            });
                        }

                        NotificationManager.error(title, message, {
                            actions: actions,
                            autoClose: false
                        });
                    }
                })
                .catch(function(error) {
                    NotificationManager.close(loadingNotification);

                    NotificationManager.error('Cleanup Error', 'Failed to execute SVN cleanup. Please check your connection and try again.', {
                        actions: [
                            {
                                text: 'Check Permissions',
                                onclick: 'checkPermissions()'
                            },
                            {
                                text: 'Retry',
                                onclick: 'forceCleanup()'
                            }
                        ]
                    });
                    console.error('Force cleanup error:', error);
                });
        }

        function showPermissionDetails(permissionData) {
            var details = '<h3>Permission Details</h3>';
            details += '<p><strong>Current User:</strong> ' + permissionData.current_user + '</p>';
            details += '<p><strong>Repository Owner:</strong> ' + permissionData.repo_owner + '</p>';
            details += '<p><strong>Repository Permissions:</strong> ' + permissionData.repo_perms + '</p>';
            details += '<p><strong>.svn Permissions:</strong> ' + permissionData.svn_perms + '</p>';

            if (permissionData.lock_files.length > 0) {
                details += '<h4>Lock Files:</h4><ul>';
                permissionData.lock_files.forEach(function(lock) {
                    details += '<li>' + lock.file + ' - ' + (lock.writable ? 'Writable' : 'Not writable') + '</li>';
                });
                details += '</ul>';
            }

            var detailsWindow = window.open('', '_blank', 'width=600,height=400,scrollbars=yes');
            detailsWindow.document.write(
                '<html><head><title>Permission Details</title>' +
                '<style>body{font-family:Arial,sans-serif;padding:20px;background:#f8f9fa;}</style>' +
                '</head><body>' + details + '</body></html>'
            );
        }

        function showCleanupDetails(encodedOutput) {
            var output = decodeURIComponent(encodedOutput);
            var detailsWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
            detailsWindow.document.write(
                '<html><head><title>SVN Cleanup Details</title>' +
                '<style>body{font-family:monospace;padding:20px;background:#f8f9fa;}pre{background:white;padding:15px;border:1px solid #ddd;border-radius:4px;}</style>' +
                '</head><body>' +
                '<h2>SVN Cleanup Output</h2>' +
                '<pre>' + output.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '</pre>' +
                '</body></html>'
            );
        }

        function refreshAfterCleanup() {
            if (currentRepo) {
                NotificationManager.info('Refreshing', 'Reloading repository status...');
                selectRepo(currentRepo);
            } else {
                loadRepositories();
            }
        }

        function selectRepo(repoPath) {
            currentRepo = repoPath;
            setCurrentRepoForCleanup(repoPath); // Set for potential cleanup operations

            var fileList = document.getElementById('file-list');
            fileList.innerHTML = '<div style="color: #6c757d;">Loading files...</div>';

            fetch('?action=get_status&path=' + encodeURIComponent(repoPath))
                .then(function(response) {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(function(data) {
                    handleApiResponse(data, function(data) {
                        var html = '<h3>Repository: ' + repoPath + '</h3>';
                        if (data.data.length === 0) {
                            html += '<p style="color: #6c757d; font-style: italic;">No modified files found.</p>';
                        } else {
                            data.data.forEach(function(file) {
                                var statusColor = file.status === 'modified' ? '#ffc107' :
                                                file.status === 'added' ? '#28a745' :
                                                file.status === 'deleted' ? '#dc3545' : '#6c757d';
                                html += '<div style="padding: 8px; border: 1px solid #dee2e6; margin-bottom: 5px; border-radius: 4px;">';
                                html += '<input type="checkbox" onchange="toggleFile(\'' + file.path + '\')" style="margin-right: 8px;">';
                                html += '<span style="background: ' + statusColor + '; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px; margin-right: 8px;">' + file.status + '</span>';
                                html += '<span onclick="showDiff(\'' + file.path + '\')" style="cursor: pointer; text-decoration: underline;">' + file.path + '</span>';
                                html += '</div>';
                            });
                        }
                        fileList.innerHTML = html;
                    }, 'SelectRepo');
                })
                .catch(function(error) {
                    // Enhanced error handling for locked repositories
                    var errorMessage = 'Failed to load repository status. The repository may be corrupted or inaccessible.';
                    var actions = [{
                        text: 'Retry',
                        onclick: 'selectRepo("' + repoPath + '")'
                    }];

                    // If this might be a lock issue, offer cleanup
                    if (error.message && (error.message.includes('locked') || error.message.includes('E155004'))) {
                        errorMessage = 'Repository appears to be locked from a previous operation.';
                        actions.unshift({
                            text: 'Force Cleanup',
                            onclick: 'forceCleanup()'
                        });
                    }

                    NotificationManager.error('Repository Error', errorMessage, {
                        actions: actions
                    });
                    console.error('Select repo error:', error);
                });
        }

        function toggleFile(filePath) {
            var index = selectedFiles.indexOf(filePath);
            if (index > -1) {
                selectedFiles.splice(index, 1);
            } else {
                selectedFiles.push(filePath);
            }
        }

        function showDiff(filePath) {
            var diffViewer = document.getElementById('diff-viewer');
            var diffPlaceholder = document.getElementById('diff-placeholder');
            var diffContent = document.getElementById('diff-content');

            // Show loading state
            diffContent.innerHTML = '<div style="padding: 20px; text-align: center; color: #6c757d;">Loading diff...</div>';
            diffViewer.style.display = 'block';
            diffPlaceholder.style.display = 'none';

            fetch('?action=get_diff&path=' + encodeURIComponent(filePath))
                .then(function(response) {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(function(data) {
                    handleApiResponse(data, function(data) {
                        var diffFilename = document.getElementById('diff-filename');
                        var diffStats = document.getElementById('diff-stats');

                        if (data.data.html) {
                            diffContent.innerHTML = data.data.html;
                            diffFilename.textContent = data.data.filename || 'Unknown file';

                            var stats = data.data.stats || {added: 0, removed: 0};
                            var statsText = '';
                            if (stats.added > 0) {
                                statsText += '<span class="diff-stats-added">+' + stats.added + '</span>';
                            }
                            if (stats.removed > 0) {
                                if (statsText) statsText += ' ';
                                statsText += '<span class="diff-stats-removed">-' + stats.removed + '</span>';
                            }
                            if (!statsText) {
                                statsText = 'No changes';
                            }
                            diffStats.innerHTML = statsText;
                        } else {
                            diffContent.innerHTML = '<div class="diff-empty">' + (data.data || 'No differences found.') + '</div>';
                            diffFilename.textContent = filePath.split('/').pop();
                            diffStats.innerHTML = 'No changes';
                        }
                    }, 'ShowDiff');
                })
                .catch(function(error) {
                    diffContent.innerHTML = '<div class="diff-empty">Failed to load diff. Please try again.</div>';
                    NotificationManager.error('Diff Error', 'Failed to load file differences.', {
                        actions: [{
                            text: 'Retry',
                            onclick: 'showDiff("' + filePath + '")'
                        }]
                    });
                    console.error('Show diff error:', error);
                });
        }

        function commitFiles() {
            if (selectedFiles.length === 0) {
                NotificationManager.warning('No Files Selected', 'Please select at least one file to commit.');
                return;
            }

            var message = document.getElementById('commit-message').value.trim();
            if (!message) {
                NotificationManager.warning('Missing Commit Message', 'Please enter a commit message describing your changes.');
            }

            setButtonLoading('commit-btn', true);

            fetch('?action=commit', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ files: selectedFiles, message: message })
            })
            .then(function(response) {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(function(data) {
                setButtonLoading('commit-btn', false);
                handleApiResponse(data, function(data) {
                    NotificationManager.success('Commit Successful', 'Your changes have been committed successfully.');
                    selectedFiles = [];
                    document.getElementById('commit-message').value = '';
                    if (currentRepo) selectRepo(currentRepo);
                }, 'CommitFiles');
            })
            .catch(function(error) {
                setButtonLoading('commit-btn', false);
                NotificationManager.error('Commit Failed', 'Failed to commit changes. Please try again.', {
                    actions: [{
                        text: 'Retry',
                    }]
                });
                console.error('Commit error:', error);
            });
        }

        function copyDiffToClipboard() {
            var diffContent = document.getElementById('diff-content');
            var textContent = '';

            lines.forEach(function(line) {
                var prefix = line.querySelector('.diff-line-prefix').textContent;
                var content = line.querySelector('.diff-line-content').textContent;
                textContent += prefix + content + '\n';
            });

            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(textContent).then(function() {
                    NotificationManager.success('Copied', 'Diff content copied to clipboard.');
                }).catch(function() {
                    fallbackCopyToClipboard(textContent);
                });
            } else {
                fallbackCopyToClipboard(textContent);
            }
        }

        function fallbackCopyToClipboard(text) {
            var textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                NotificationManager.success('Copied', 'Diff content copied to clipboard.');
            } catch (err) {
                NotificationManager.error('Copy Failed', 'Failed to copy diff to clipboard. Please copy manually.');
            }
            document.body.removeChild(textArea);
        }

        // Retry functions for recoverable errors
        function retryLoadRepositories() {
            loadRepositories();
        }

        function retrySelectRepo() {
            if (currentRepo) {
                selectRepo(currentRepo);
            }
        }

        function retryCommitFiles() {
            commitFiles();
        }

        function showPermissionFix() {
            var repoPath = currentRepoForCleanup || currentRepo;
            if (!repoPath) {
                NotificationManager.error('Permission Fix Error', 'No repository selected.');
                return;
            }

            var loadingNotification = NotificationManager.info('Generating Fix', 'Analyzing permission issues and generating fix commands...', {
                autoClose: false
            });

            var url = '?action=get_permission_fix';
            if (repoPath !== currentRepo) {
                url += '&current_repo=' + encodeURIComponent(repoPath);
            }

            fetch(url)
                .then(function(response) { return response.json(); })
                .then(function(data) {
                    NotificationManager.close(loadingNotification);

                    if (data.status === 'success') {
                        showPermissionFixWindow(data.data);
                    } else {
                        NotificationManager.error('Permission Fix Failed', data.message || 'Unable to generate permission fix commands.');
                    }
                })
                .catch(function(error) {
                    NotificationManager.close(loadingNotification);
                    NotificationManager.error('Permission Fix Error', 'Failed to generate permission fix. Please try again.');
                    console.error('Permission fix error:', error);
                });
        }

        function showPermissionFixWindow(data) {
            var content = '<div style="font-family: Arial, sans-serif; line-height: 1.6; max-width: 800px;">';

            // Header
            content += '<h2 style="color: #dc3545; border-bottom: 2px solid #dc3545; padding-bottom: 10px;">SVN Permission Fix Guide</h2>';
            content += '<p><strong>Repository:</strong> ' + data.repo_path + '</p>';

            // Problem Summary
            content += '<div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin: 15px 0;">';
            content += '<h3 style="color: #721c24; margin-top: 0;">Problem Summary</h3>';
            content += '<p><strong>Issue:</strong> ' + data.summary.issue + '</p>';
            content += '<p><strong>Cause:</strong> ' + data.summary.cause + '</p>';
            content += '<p><strong>Solution:</strong> ' + data.summary.solution + '</p>';
            content += '</div>';

            // Current Status
            content += '<h3>Current Permissions Status</h3>';
            content += '<ul>';
            content += '<li>Repository Owner: <code>' + (data.current_permissions.repo_owner || 'null') + '</code></li>';
            content += '<li>Web Server User: <code>' + data.current_permissions.current_user + '</code></li>';
            content += '<li>Repository Writable: ' + (data.current_permissions.repo_writable ? '✓ Yes' : '✗ No') + '</li>';
            content += '<li>.svn Directory Writable: ' + (data.current_permissions.svn_dir_writable ? '✓ Yes' : '✗ No') + '</li>';
            content += '</ul>';

            // Fix Commands
            content += '<h3>Fix Commands</h3>';
            content += '<div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; padding: 15px; margin: 15px 0;">';
            content += '<p><strong>⚠️ Important:</strong> These commands must be run as system administrator (root) on the server.</p>';
            content += '</div>';

            data.fix_commands.forEach(function(cmd, index) {
                content += '<div style="margin: 20px 0; border: 1px solid #ddd; border-radius: 4px; overflow: hidden;">';
                content += '<div style="background: #f8f9fa; padding: 10px; border-bottom: 1px solid #ddd;">';
                content += '<h4 style="margin: 0; color: #495057;">Step ' + (index + 1) + ': ' + cmd.description + '</h4>';
                content += '</div>';
                content += '<div style="padding: 15px;">';
                content += '<p style="margin: 0 0 10px 0;"><strong>Command:</strong></p>';
                content += '<div style="background: #2d3748; color: #e2e8f0; padding: 12px; border-radius: 4px; font-family: monospace; font-size: 14px; overflow-x: auto; margin: 10px 0;">';
                content += '<code>' + cmd.command + '</code>';
                content += '<button onclick="copyCommand(\'' + cmd.command.replace(/'/g, "&#39;") + '\')" style="float: right; background: #4a5568; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px;">Copy</button>';
                content += '</div>';
                content += '<p style="margin: 10px 0 0 0; color: #6c757d; font-style: italic;">' + cmd.explanation + '</p>';
                content += '</div>';
                content += '</div>';
            });

            // Verification Steps
            content += '<h3>Verification Steps</h3>';
            content += '<div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; padding: 15px; margin: 15px 0;">';
            content += '<p>After running the fix commands, verify the solution:</p>';
            content += '<ol>';
            content += '<li>Refresh this page and run the permission check again</li>';
            content += '<li>Try the SVN cleanup operation</li>';
            content += '<li>Test other SVN operations (status, commit, update)</li>';
            content += '</ol>';
            content += '</div>';

            // Alternative Solutions
            content += '<h3>Alternative Solutions</h3>';
            content += '<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 15px 0;">';
            content += '<p>If the above commands don\'t work, consider these alternatives:</p>';
            content += '<ul>';
            content += '<li><strong>Docker/Container:</strong> If running in a container, ensure volume mounts have correct permissions</li>';
            content += '<li><strong>SELinux:</strong> Check SELinux contexts if enabled: <code>ls -Z ' + data.repo_path + '</code></li>';
            content += '<li><strong>NFS/Network Storage:</strong> Network mounted directories may need special handling</li>';
            content += '<li><strong>Backup &amp; Restore:</strong> As last resort, backup repository and restore with correct permissions</li>';
            content += '</ul>';
            content += '</div>';

            content += '</div>';

            // Create the complete HTML document
            var htmlDoc = '<!DOCTYPE html>' +
                '<html>' +
                '<head>' +
                    '<title>SVN Permission Fix Guide</title>' +
                    '<style>' +
                        'body { padding: 20px; background: #f8f9fa; font-family: Arial, sans-serif; }' +
                        'code { background: #f1f3f4; padding: 2px 4px; border-radius: 3px; }' +
                    '</style>' +
                '</head>' +
                '<body>' +
                    content +
                    '<script>' +
                        'function copyCommand(text) {' +
                            'text = text.replace(/&#39;/g, "\'");' +
                            'if (navigator.clipboard) {' +
                                'navigator.clipboard.writeText(text).then(function() {' +
                                    'alert("Command copied to clipboard!");' +
                                '}).catch(function(err) {' +
                                    'console.error("Failed to copy: ", err);' +
                                    'fallbackCopy(text);' +
                                '});' +
                            '} else {' +
                                'fallbackCopy(text);' +
                            '}' +
                        '}' +
                        'function fallbackCopy(text) {' +
                            'var textArea = document.createElement("textarea");' +
                            'textArea.value = text;' +
                            'document.body.appendChild(textArea);' +
                            'textArea.select();' +
                            'try {' +
                                'document.execCommand("copy");' +
                                'alert("Command copied to clipboard!");' +
                            '} catch (err) {' +
                                'prompt("Copy this command manually:", text);' +
                            '}' +
                            'document.body.removeChild(textArea);' +
                        '}' +
                    '</script>' +
                '</body>' +
                '</html>';

            var fixWindow = window.open('', '_blank', 'width=900,height=800,scrollbars=yes');
            if (fixWindow) {
                fixWindow.document.write(htmlDoc);
                fixWindow.document.close();
            } else {
                NotificationManager.error('Popup Blocked', 'Please allow popups for this site to view the permission fix guide.');
            }
        }

        // Initialize notification system and load repositories
        document.addEventListener('DOMContentLoaded', function() {
            NotificationManager.init();

            // Event listeners
            document.getElementById('refresh-repos').addEventListener('click', loadRepositories);
            document.getElementById('commit-btn').addEventListener('click', commitFiles);

            // Load repositories on page load
            loadRepositories();
        });
    </script>
    <?php endif; ?>
</body>
</html>



