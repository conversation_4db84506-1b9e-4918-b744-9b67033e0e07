<?php

	/**	\file edit.php
	 *	Cet écran permet la création et la mise à jour d'une promotion spéciale.
	 */

	require_once('promotions.inc.php');

	// Vérifie que l'utilisateur à bien accès à la page et qu'il à le droit de modifier une promotion
	if( $_GET['type'] == _PMT_TYPE_SOLDES ){
		if( $_GET['id']==0 ){
			gu_if_authorized_else_403('_RGH_ADMIN_PROMO_SOLDE_ADD');
		}elseif( $_GET['id'] != 0 ){
			gu_if_authorized_else_403('_RGH_ADMIN_PROMO_SOLDE_VIEW');
		}
	}elseif( $_GET['type'] == _PMT_TYPE_REWARD ){
		if( $_GET['id'] == 0 ){
			gu_if_authorized_else_403('_RGH_ADMIN_PROMO_REWARD_ADD');
		}elseif( $_GET['id'] != 0 ){
			gu_if_authorized_else_403('_RGH_ADMIN_PROMO_REWARD_VIEW');
		}
	}else{
		if( $_GET['id'] == 0 ){
			gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD_ADD');
		}elseif( $_GET['id'] != 0 ){
			gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD_VIEW');
		}
	}

	// Vérifie le paramètre type (obligatoire)
	if( !isset($_GET['type']) || !is_numeric($_GET['type']) || $_GET['type']<=0 ){
		header('Location: /admin/promotions/index.php');
		exit;
	}

	$rtype = pmt_types_get( $_GET['type'], false );
	if( !$rtype || !ria_mysql_num_rows($rtype) ){
		header('Location: /admin/promotions/index.php');
		exit;
	}

	$type = ria_mysql_fetch_array( $rtype );

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: /admin/promotions/specials/index.php?type='.$type['id']);
		exit;
	}

	$_GET['id'] = isset($_GET['id']) ? $_GET['id'] : 0;
	if( $_GET['id']>0 && !pmt_codes_exists($_GET['id']) ){
		header('Location: /admin/promotions/specials/index.php?type='.$type['id']);
		exit;
	}

	$tab = '';
	$pmt = array(
		'id'=>0, 'code'=>'', 'name'=>'', 'desc'=>'', 'active'=>false,
		'date_start_en'=>'', 'date_start'=>'', 'date_stop_en'=>'', 'date_stop'=>'', 'hour_stop'=>'', 'hour_start'=>'',
		'used'=>0, 'used_max'=>'', 'reusable'=>1, 'first_order'=>0, 'include_pmt'=>0, 'only_destock'=>0,
		'all-customers'=>0, 'all-catalog'=>0, 'available_stocks'=>0,
		'prd_in_cart'=>0, 'one_by_cart'=>1, 'same_qty'=>0
	);

	if( $_GET['id']>0 ){
		$rpmt = pmt_codes_get( $_GET['id'] );
		if( $rpmt && ria_mysql_num_rows($rpmt) ){
			$pmt = ria_mysql_fetch_array( $rpmt );
			$pmt['active'] = pmt_codes_is_active( $pmt['code'] );
		}
	}

	// Action sur l'onglet "Avancés"
	view_admin_tab_fields_actions( CLS_PMT_CODE, $pmt['id'], $config['i18n_lng'] );

	$rgrp = $_GET['id'] ? pmt_code_groups_get( $_GET['id'] ) : false;
	$count_grp = $rgrp ? ria_mysql_num_rows( $rgrp ) : 0;

	$roff = $_GET['id'] ? pmt_offers_get( $_GET['id'] ) : false;
	$count_reduct_prd = $roff ? ria_mysql_num_rows($roff) : 1;

	// Bouton Annuler sur la liste des produits
	if( isset($_POST['cancel-products']) ){
		header('Location: edit.php?id='.$_GET['id'].'&type='.$_GET['type'].'&tab=general');
		exit;
	}

	// Ajout d'une exception sur la liste des comptes clients
	if( isset($_POST['pmt-usr-include']) || isset($_POST['pmt-usr-exclude']) ){
		if( $_POST['pmt-add-rule']=='prf' ){
			pmt_profiles_add($_GET['id'],$_POST['pmt-usr-type'],isset($_POST['pmt-usr-include']));
		}elseif( $_POST['pmt-add-rule']=='seg' ){
			pmt_segments_add($_GET['id'],$_POST['pmt-usr-type'],isset($_POST['pmt-usr-include']));
		}elseif( $_POST['pmt-add-rule']=='usr' ){
			pmt_users_add($_GET['id'],$_POST['pmt-usr-id'],isset($_POST['pmt-usr-include'])); //
		}

		if( !isset($error) ){
			header('Location: edit.php?id='.$_GET['id'].'&type='.$_GET['type'].'&tab=customers');
			exit;
		}
	}

	// Bouton Supprimer sur la liste des exceptions clients
	if( isset($_POST['pmt-del-usr-rule']) ){
		if( isset($_POST['pmt-customers']) ){
			foreach( $_POST['pmt-customers'] as $p ){
				$type = substr($p,0,3);
				$id = substr($p,4);
				switch( $type ){
					case 'prf':
						pmt_profiles_del($_GET['id'],$id);
						break;
					case 'seg':
						pmt_segments_del($_GET['id'],$id);
						break;
					case 'usr':
						pmt_users_del($_GET['id'],$id);
						break;
				}
			}
		}

		if( !isset($error) ){
			header('Location: edit.php?id='.$_GET['id'].'&type='.$_GET['type'].'&tab=customers');
			exit;
		}
	}

	// Bouton Enregistrer sur la listes des comptes clients
	if( isset($_POST['save-customers']) ){
		if( isset($_POST['pmt-all-customers']) ){
			pmt_codes_set_all_customers($_GET['id'],$_POST['pmt-all-customers']);
		}

		if( !isset($error) ){
			header('Location: edit.php?id='.$_GET['id'].'&type='.$_GET['type'].'&tab=customers');
			exit;
		}
	}

	// Bouton Annuler sur la liste des comptes clients
	if( isset($_POST['cancel-customers']) ){
		header('Location: edit.php?id='.$_GET['id'].'&type='.$_GET['type'].'&tab=general');
		exit;
	}

	// Bouton Supprimer sur la liste des promotions contenues dans la souche
	if( isset($_POST['delete']) ){
		if( isset($_POST['pmt']) && is_array($_POST['pmt']) && sizeof($_POST['pmt']) ){
			foreach( $_POST['pmt'] as $pmt ){
				if( !pmt_codes_del($pmt) ){
					$error = _("Une erreur inattendue s'est produite lors de la suppression.") . " <br />" . _("Veuillez réessayer ou prendre contact pour signaler le problème.");
				}
			}
		}

		if( !isset($error) ){
			$_SESSION['save-pmt-ok'] = true;
			header('Location: /admin/promotions/specials/edit.php?id='.$_GET['id'].'&type='.$_GET['type'].'#tabSerie');
			exit;
		}
	}

	$ar_pmt = null;
	if( isset($_POST['pmt']) && is_array($_POST['pmt']) && sizeof($_POST['pmt']) ){
		$ar_pmt = $_POST['pmt'];
	}

	$rcode_serie = pmt_codes_get( null, null, false, $_GET['type'], false, $pmt['id'] );
	$s_export = 'all';
	if( isset($_POST['s-export']) && is_array($_POST['s-export']) && sizeof($_POST['s-export']) ){
		if( in_array('0', $_POST['s-export']) ){
			$s_export = '0';
		} elseif( in_array('1', $_POST['s-export']) ){
			$s_export = '1';
		}
	}

	// Limite d'affichage des bons d'achat
	$limit_BA = 50;

	// Gestion de la pagination
	$page = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page']>0 ? $_GET['page'] : 1;
	$pages = $rcode_serie && ria_mysql_num_rows($rcode_serie) ? ceil( ria_mysql_num_rows($rcode_serie) / $limit_BA ) : 1;
	if( $page>$pages ){
		$page = $pages;
	}

	$pmin = $page - 2;
	$pmin = $pmin < 1 ? 1 : $pmin;
	$pmax = $pmin + 4;
	$pmax = $pmax>$pages ? $pages : $pmax;

	// Bouton Exporter
	if( isset($_POST['export-serie']) ){
		if( $rcode_serie && ria_mysql_num_rows($rcode_serie) ){
			$rows[] = 'Code'.( $s_export=='all' ? ';Status' : '' ).( $s_export=='all' || $s_export=='1' ? ';Commande(s)' : '' );
			while( $c = ria_mysql_fetch_array($rcode_serie) ){
				if( is_array($ar_pmt) && !in_array($c['id'], $ar_pmt) ){
					continue;
				}

				$status = '';
				if( $s_export=='all' ){
					if( $c['used']>0 ){
						$status = 'UTILISE';
					} else {
						$status = 'EN COURS';
					}
				} else {
					if( $s_export=='1' ){
						if( $c['used']<=0 ){
							continue;
						}
					} elseif( $s_export=='0' ){
						if( $c['used']>0 ){
							continue;
						}
					}
				}

				$orders = '';
				$ar_orders = pmt_codes_get_orders( $c['id'] );
				foreach( $ar_orders as $o ){
					if( trim($orders)!='' ){
						$orders .= ', ';
					}

					$orders .= $o;
				}

				$rows[] = $c['code'].( $s_export=='all' ? ';'.$status : '' ).';'.$orders;
			}

			$path = '/tmp/fichier_temp_supprimable.csv';
			file_put_contents( $path, implode("\r", $rows) );
			header('Content-Type: application/x-force-download; charset=utf-8');
			header('Content-Disposition: attachment; filename="'.urlalias( 'pmt-'.$pmt['name'] ).'.csv"');
			header('Content-Length: '.filesize($path));
			readfile($path);
			exit;
		} else {
			header('Location: /admin/promotions/specials/edit.php?id='.$pmt['id'].'&type='.$_GET['type'].'#tabSerie');
			exit;
		}
	}

	// S'il s'agit de la création d'un chéquier, on créer une variable de session pour la création
	unset($_SESSION['admin_create_promo']);
	if( $pmt['id']<=0 && $_GET['type']==_PMT_TYPE_CHEEKBOOK ){
		$_SESSION['admin_create_promo'] = array(
			'products' => array(
				'include' => 1,
				'rules' => array(),
				'promo' => 0
			),
			'customers' => array(
				'include' => 0,
				'rules' => array()
			)
		);
	}

	$r_promotions = pmt_codes_get(null, null, false, $type['id'], false, null);
	$has_other_promotions = $r_promotions && ria_mysql_num_rows($r_promotions) > 1;

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Promotions'), '/admin/promotions/index.php' );

	if( isset($_GET['type']) && ($type = ria_mysql_fetch_array( pmt_types_get($_GET['type'], false) )) ){
		if( isset($_GET['id']) ){
			Breadcrumbs::add( $type['name'], '/admin/promotions/specials/index.php?type='.$type['id'] );
			if( !is_numeric($_GET['id']) || $_GET['id']<=0 ){
				Breadcrumbs::add( _('Nouvelle promotion') );
			} elseif( ($cod = ria_mysql_fetch_array( pmt_codes_get($_GET['id']) )) ){
				$page_title = trim($cod['name'])!='' ?
					( $cod['type']==_PMT_TYPE_CHEEKBOOK ? _('Chéquier n°') : '' ).htmlspecialchars($cod['name'])
					: (trim($cod['code'])!='' ? $cod['code'] : _('Promotion n°').$cod['id'])
				;
				Breadcrumbs::add( $page_title );
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Edition') . ' - '.htmlspecialchars( $type['name'] ).' - ' ._('Promotions'));
	require_once('admin/skin/header.inc.php');

	// contenu du h2 en fonction de la page
	if ( isset($_GET['type']) && is_numeric($_GET['type']) ) {
		switch ($_GET['type']) {
			case 1:
				$nom_singulier = 'Code promotion';
				break;
			case 2:
				$nom_singulier = 'Bon d\'achat';
				break;
			case 3:
				$nom_singulier = 'Produit offert';
				break;
			case 4:
				$nom_singulier = 'Réduction dégressive';
				break;
			case 6:
				$nom_singulier = 'Code de parrainage';
				break;
			case 7:
				$nom_singulier = 'Chèque cadeau';
				break;
			case 8:
				$nom_singulier = 'Carte cadeau';
				break;
			case 9:
				$nom_singulier = 'Remise';
				break;
			case 12:
				$nom_singulier = 'Offre spéciale';
				break;

			default:
				$nom_singulier = htmlspecialchars($type['name']);
		}
		if ( isset($pmt['name']) && trim($pmt['name']) != '' ) {
			$pmt_name = htmlspecialchars($pmt['name']);
		}
		if ( isset($pmt['code']) && trim($pmt['code']) != '' ) {
			$pmt_name = htmlspecialchars($pmt['code']);
		}

		if ( $_GET['type'] == 4 || $_GET['type'] == 8 || $_GET['type'] == 9 || $_GET['type'] == 12 ) {
			$edit_new = 'Nouvelle ';
		} else if ( $_GET['type'] == 10 ){
			$edit_new = 'Nouvelles ';
		} else if ( $_GET['type'] == 11 ){
			$edit_new = 'Nouvel ';
		} else {
			$edit_new = 'Nouveau ';
		}

		if ( isset($pmt_name) && $pmt_name != ''){
			//on vérifie le type
			if($_GET['type'] == 7) {
				$h2_content = $nom_singulier . (isset($pmt_name) && $pmt_name != '' ? ' n°' . $pmt_name : '');
			} else {
				$h2_content = $nom_singulier . (isset($pmt_name) && $pmt_name != '' ? ' : ' . $pmt_name : '');
			}
		} else {
			// sinon c'est un nouveau
			$h2_content = $edit_new . $nom_singulier;

		}
	} else {
		$h2_content = $type['name'];
	}

	echo '<h2>' . htmlspecialchars($h2_content) . '</h2>';

	if( $pmt['active'] && $pmt['used_max']>0 && $pmt['used']>=$pmt['used_max'] ){
		print '<div class="notice">'._('Le <a href="#used-max">nombre maximum d\'utilisations</a> a été atteint, ce code ne peut plus être utilisé.').'</div>';
	}elseif( $pmt['used_max']==1 ){
		print '<div class="notice">'._('Ce code est à usage unique, son <a href="#used-max">nombre maximum d\'utilisations</a> est limité à 1.').'</div>';
	}

?>

<form action="/admin/promotions/specials/edit.php<?php print '?id='.$_GET['id'].'&amp;type='.$_GET['type']; ?>" method="post" enctype="multipart/form-data">
	<input type="hidden" name="type-promotion" id="type-promotion" value="<?php print $type['id']; ?>" />
	<input type="hidden" name="id-promotion" id="id-promotion" value="<?php print $_GET['id']; ?>" />

	<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="Général" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
		<?php
			if( $_GET['type']==_PMT_TYPE_CHEEKBOOK && $pmt['id']<=0 ){
				if( view_admin_show_tab_fields( CLS_PMT_CODE, $pmt['id'] ) ){
					print '<li><input type="submit" name="tabFields" value="' . _("Avancé") . '" '.( $tab=='fields' ? 'class="selected"' : '' ).' /></li>';
				}
				print '
					<li><input type="submit" name="tabProducts" value="' . _("Produits") . '" '.( $tab=='products' ? 'class="selected"' : '' ).' /></li>
					<li><input type="submit" name="tabCustomers" value="' . _("Clients") . '" '.( $tab=='customers' ? 'class="selected"' : '' ).' /></li>
				';
			}elseif( $pmt['id']>0 ){
				if( view_admin_show_tab_fields( CLS_PMT_CODE, $pmt['id'] ) ){
					print '<li><input type="submit" name="tabFields" value="' . _("Avancé") . '" '.( $tab=='fields' ? 'class="selected"' : '' ).' /></li>';
				}
				print '
					<li><input type="submit" name="tabProducts" value="' . _("Produits") . '" '.( $tab=='products' ? 'class="selected"' : '' ).' /></li>
					<li><input type="submit" name="tabCustomers" value="' . _("Clients") . '" '.( $tab=='customers' ? 'class="selected"' : '' ).' /></li>
				';

				if( in_array($_GET['type'], array(_PMT_TYPE_BA, _PMT_TYPE_CHEEKBOOK)) ){
					print '
						<li><input type="submit" name="tabSerie" value="' . _("Série") . '" '.( $tab=='serie' ? 'class="selected"' : '' ).' /></li>
					';
				}elseif( in_array($_GET['type'], array(_PMT_TYPE_CODE, _PMT_TYPE_CREDIT)) ){
					print '
						<li><input type="submit" name="tabVariations" value="' . _("Variante") . 's" '.( $tab=='variations' ? 'class="selected"' : '' ).' /></li>
					';
				}

				print '
					<li><input type="submit" name="tabStats" value="' . _("Statistiques") . '" '.( $tab=='stats' ? 'class="selected"' : '' ).' /></li>
				';
			}
		?>
	</ul>
	<div id="tabpanel">
		<?php
			if( isset($_SESSION['save-pmt-ok']) && $_SESSION['save-pmt-ok'] ){
				print '<div class="success">' ._('L\'enregistrement des informations s\'est correctement déroulé.') . '</div>';
				unset( $_SESSION['save-pmt-ok'] );
			}
		?>

		<table id="pmt-special" class="pmt-special">
			<tbody>
				<?php
					if( !in_array($_GET['type'], array(_PMT_TYPE_CODE, _PMT_TYPE_CREDIT, _PMT_TYPE_REWARD, _PMT_TYPE_CHEEKBOOK, _PMT_TYPE_GIFTS)) ){
						print '
							<tr>
								<td><span class="mandatory">*</span> <label for="pmt-label">' . _('Libellé :') . '</label></td>
								<td><input type="text" name="pmt-label" id="pmt-label" value="'.htmlspecialchars( $pmt['name'] ).'" /></td>
							</tr>
						';
					}

					if( !in_array($_GET['type'], array(_PMT_TYPE_BA, _PMT_TYPE_CHEEKBOOK, _PMT_TYPE_REMISE, _PMT_TYPE_SOLDES)) ){
						print '
							<tr>
								<td>
									<label for="code">'.( in_array($_GET['type'], array(_PMT_TYPE_CODE, _PMT_TYPE_CREDIT, _PMT_TYPE_REWARD, _PMT_TYPE_GIFTS)) ? '<span class="mandatory">*</span> ' : '' ). _('Code :').'</label>
								</td>
								<td>
									<input type="text" class="code" name="code" id="code" value="'.$pmt['code'].'" maxlength="16" />
									<input type="button" value="' . _("Générer") . '" id="gen-code" name="gen-code" class="btn-action-small btn-main" />
									<a class="upd-pattern edit" href="#">' . _("Modifier le pattern") . '</a>
						';

						if( !in_array($_GET['type'], array(_PMT_TYPE_CODE, _PMT_TYPE_CREDIT, _PMT_TYPE_REWARD, _PMT_TYPE_GIFTS)) ){
							print '
									<sub>
										' . _('L\'utilisation d\'un code promotion n\'est pas obligatoire. Dans le cas où ce dernier n\'est pas renseigné, l\'offre commerciale sera automatiquement appliquée lorsque les conditions seront réunies. En renseignant un code, l\'offre sera alors appliquée seulement dans le cas de l\'utilisation du code et toujours si toutes les conditions sont réunies.') . '
									</sub>
							';
						}

						print '
								</td>
							</tr>
						';
					}

					if( $_GET['type']==_PMT_TYPE_CHEEKBOOK  && $pmt['id']<=0 ){
						print '
							<tr>
								<td><span class="mandatory">*</span> <label for="pmt-nb-cheekbook">' . _('Nombre de chéquiers :') . '</label></td>
								<td>
									<input class="price" type="text" name="pmt-nb-cheekbook" id="pmt-nb-cheekbook" value="1" />
									<sub>' . _("Précisez le nombre de chéquiers à générer.") . '</sub>
								</td>
							</tr>
						';
					}

					if( in_array($_GET['type'], array(_PMT_TYPE_BA, _PMT_TYPE_CHEEKBOOK)) ){
						print '
							<tr>
								<td>
						';

						switch( $_GET['type'] ){
							case _PMT_TYPE_BA:
								print '
									'.( $pmt['id']<=0 ? '<span class="mandatory">*</span> ' : '' ).'<label for="pmt-qte-gen">'._('Quantité :').'</label>
								';
								break;
							case _PMT_TYPE_CHEEKBOOK:
								print '
									'.( $pmt['id']<=0 ? '<span class="mandatory">*</span> ' : '' ).'<label for="pmt-qte-gen">' . _('Nombre de chèques :') . '</label>
								';
								break;
						}

						print '
								</td>
								<td>
									<div class="pmt-zone-gen">
										<input type="text" class="price" name="pmt-qte-gen" id="pmt-qte-gen" value="" />
						';

						if( $pmt['id']>0 ){
							print '
										<input class="btn-action-small" type="button" name="gen-new-code" id="gen-new-code" value="' . _("Générer") . '" title="' . _("Générer de nouveaux bons d'achat") . '" />
							';
						}

						print '
										<a class="upd-pattern" href="#">' . _("Modifier le pattern") . '</a>
										<div class="clear"></div>
						';

						switch( $_GET['type'] ){
							case _PMT_TYPE_BA:
								print '
										<sub>' . _("Précisez la quantité de bons d'achat à générer.") . '</sub>
								';
								break;
							case _PMT_TYPE_CHEEKBOOK:
								print '
										<sub>' . _("Précisez la quantité de chèques cadeaux à générer par chéquiers.") . '</sub>
								';
								break;
						}

						print '
									</div>
								</td>
							</tr>
						';
					}
				?>

				<tr>
					<th colspan="2"><?php echo _("Dates de validité"); ?></th>
				</tr>
				<tr>
					<td class="td-pmt-spec"><label for="date-start"><?php echo _('Débuter le :'); ?></label></td>
					<td>
						<input type="text" name="date-start" id="date-start" class="date datepicker" maxlength="10" value="<?php print $pmt['date_start']; ?>" autocomplete="off" />
						<label for="hour-start"><?php echo _("à"); ?></label>
						<input type="text" name="hour-start" id="hour-start" class="hour" maxlength="5" value="<?php print $pmt['hour_start']; ?>" autocomplete="off" />
				   </td>
				</tr>
				<tr>
					<td><label for="date-stop"><?php echo _('Terminer le :'); ?></label></td>
					<td>
						<input type="text" name="date-stop" id="date-stop" class="date datepicker" maxlength="10" value="<?php print $pmt['date_stop']; ?>" autocomplete="off" />
						<label for="hour-stop"><?php echo _("à"); ?></label>
						<input type="text" name="hour-stop" id="hour-stop" class="hour" maxlength="5" value="<?php print $pmt['hour_stop']; ?>" autocomplete="off" />
						<?php
							if( $pmt['id']>0 && trim($pmt['date_stop'])=='' ){
								print '<sub>' . _('La promotion n\'est pas limitée dans le temps') . '</sub>';
							} elseif( $pmt['id']==0 ){
								print '<sub>' . _('Laissez vide pour ne pas limiter la promotion dans le temps') . '</sub>';
							}
						?>
					</td>
				</tr>
				<tr>
					<th colspan="2"><?php echo _("Bénéfices apportés par l'offre"); ?></th>
				</tr>
				<?php
					print pmt_offers_get_html( $_GET['type'], $roff );
				?>
				<tr>
					<td class="pmt-off-srv"><?php print _('Frais de ports offerts pour :'); ?></td>
					<td class="pmt-off-srv"><a href="#" class="check-all-srv edit"><?php echo _("Cocher tout"); ?></a> | <a href="#" class="uncheck-all-srv edit"><?php echo _("Décocher tout"); ?></a></td>
				</tr>
				<tr>
					<td></td>
					<td class="pmt-list-choose">
						<ul class="pmt-list-service">
						<?php
							$cod_service = pmt_codes_get_services( $_GET['id'] );
							$rsrv = dlv_services_get( 0, true );

							print '
									<li>
										<label><input '.( in_array(-1, $cod_service) ? 'checked="checked"' : '' ).' type="checkbox" name="chk_srv[]" value="-1" /> ' . _("Livraison en magasin (si activé)") . '</label>
									</li>
							';

							if( $rsrv && ria_mysql_num_rows($rsrv) ){
								while( $srv = ria_mysql_fetch_array($rsrv) ){
									print '	<li>
												<label>
													<input '.( in_array($srv['id'], $cod_service) ? 'checked="checked"' : '' ).' type="checkbox" name="chk_srv[]" id="chk_srv_'.$srv['id'].'" value="'.$srv['id'].'" />
													'.htmlspecialchars( $srv['name'] ). '
												</label>
											</li>
									';
								}
							}
						?></ul>
					</td>
				</tr>
				<tr>
					<th colspan="2"><?php echo _("Limites d'applications"); ?></th>
				</tr>
				<tr>
					<td colspan="2">
						<input <?php print $pmt['used_max']>0 ? 'checked="checked"' : ''; ?> type="checkbox" value="1" id="used-max-apply" name="used-max-apply" class="checkbox" />
						<label for="used-max"><?php echo _('Nombre maximum d\'utilisations (global) :'); ?></label>
						<input type="text" maxlength="8" value="<?php print $pmt['used_max']; ?>" id="used-max" name="used-max" class="price" />
					</td>
				</tr>
				<tr>
					<td colspan="2">
						<input <?php print !$pmt['reusable'] ? 'checked="checked"' : ''; ?> type="checkbox" value="0" id="reusable" name="reusable" class="checkbox" />
						<label for="reusable"><?php echo _("Limiter à une seule utilisation par compte client"); ?></label>
					</td>
				</tr>
				<tr>
					<td colspan="2">
						<input <?php print $pmt['first_order'] ? 'checked="checked"' : ''; ?> type="checkbox" value="1" id="first_order" name="first_order" class="checkbox" />
						<label for="first_order"><?php echo _("Limiter à la première commande du compte client"); ?></label>
					</td>
				</tr>

				<?php
					$type_limit_stock = array(_PMT_TYPE_PRD, _PMT_TYPE_REDUC, _PMT_TYPE_BUY_X_FREE_Y);
					if( isset($config['pmt_active_remise_outstock']) && $config['pmt_active_remise_outstock'] ){
						$type_limit_stock[] = _PMT_TYPE_REMISE;
						$type_limit_stock[] = _PMT_TYPE_SOLDES;
					}
				?>

				<tr <?php print in_array($_GET['type'], $type_limit_stock) ? '' : 'class="none"'; ?>>
					<td colspan="2">
						<input <?php print $pmt['available_stocks'] ? 'checked="checked"' : ''; ?> type="checkbox" value="1" id="available_stocks" name="available_stocks" />
						<label for="available_stocks"><?php echo _("Dans la limite des stocks disponibles"); ?></label>
					</td>
				</tr>
				<tr <?php print in_array($_GET['type'], array(_PMT_TYPE_PRD)) ? '' : 'class="none"'; ?>>
					<td colspan="2">
						<input <?php print $pmt['prd_in_cart'] ? 'checked="checked"' : ''; ?> type="checkbox" value="1" id="prd_in_cart" name="prd_in_cart" />
						<label for="prd_in_cart" title="<?php echo _("En cochant cette case, seuls les produits offerts non présents dans le panier en cours seront proposés à l'internaute."); ?>">Exclure les produits du panier</label>
					</td>
				</tr>
				<tr <?php print in_array($_GET['type'], array(_PMT_TYPE_PRD)) ? '' : 'class="none"'; ?>>
					<td colspan="2">
						<input <?php print $pmt['one_by_cart'] ? 'checked="checked"' : ''; ?> type="checkbox" value="1" id="one_by_cart" name="one_by_cart" />
						<label for="one_by_cart" title="<?php echo _("En décochant cette case, le ou les produits seront offerts (selon la quantité définie) autant de fois que les conditions sont remplies. S'il n'existe aucune condition, alors cette option n'est pas prise en compte."); ?>"><?php echo _("Ne s'applique qu'une seule fois"); ?></label>
					</td>
				</tr>
				<?php
					$rwst = wst_websites_get();
					if( $rwst && ria_mysql_num_rows($rwst)>1 ){
						print '
							<tr>
								<td class="pmt-off-srv">' . _('Applicable sur les sites :') . '</td>
								<td class="pmt-off-srv"><a href="#" class="check-all-wst">' . _("Cocher tout") . '</a> | <a href="#" class="uncheck-all-wst">' . _("Décocher tout") . '</a></td>
							</tr>
							<tr>
								<td></td>
								<td class="pmt-list-choose pmt-list-wst">
						';

						$in_wst = pmt_websites_get_array( $pmt['id'] );
						while( $wst = ria_mysql_fetch_array($rwst) ){
							$checked = in_array( $wst['id'], $in_wst ) ? 'checked="checked"' : '';

							print '
									<div>
										<input '.$checked.' type="checkbox" value="'.$wst['id'].'" id="wst-'.$wst['id'].'" name="wst[]" />
										<label for="wst-'.$wst['id'].'">'.htmlspecialchars( $wst['name'] ).'</label>
									</div>
							';
						}

						print '
								</td>
							</tr>
						';
					}
				?>
				<tr>
					<td colspan="2">
						<div class="add-pmt-cdt"><?php
							if( $_GET['id']>0 ){
								print pmt_groups_get_html( $rgrp, $_GET['type'] );
							}

							print '<input class="btn-action-small" type="button" name="cdt-add-grp" id="cdt-add-grp" value="' . _("Ajouter un groupe de conditions") . '" />';
						?></div>
					</td>
				</tr>
				<tr>
					<th colspan="2"><?php echo _("Commentaire"); ?></th>
				</tr>
				<tr>
					<td colspan="2">
						<textarea name="pmt-cmt" id="pmt-cmt" rows="10" cols="50"><?php print htmlspecialchars( $pmt['desc'] ); ?></textarea>
					</td>
				</tr>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="2"><?php
						if( $pmt['id']<=0 && $_GET['type']==_PMT_TYPE_CHEEKBOOK ){
							print '
								<input class="saveAll" type="submit" name="save" value="' . _("Enregistrer") . '" />
							';
						}else{
							print '
								<input type="submit" name="save" id="save" value="' . _("Enregistrer") . '" />
							';
						} ?>
						<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" />
					</td>
				</tr>
			</tfoot>
		</table>

		<?php if( $pmt['id']>0 || $_GET['type']==_PMT_TYPE_CHEEKBOOK ){ ?>
			<table id="tb-tabProducts" class="pmt-special tb_rewards checklist">
				<caption><?php echo _("Règles d'inclusion / d'exclusion"); ?></caption>
				<tbody>
					<tr><td colspan="2" class="pmt-spe-rules pmt-td-all-catalog">
						<input type="radio" <?php print $pmt['all-catalog'] ? 'checked="checked"' : ''; ?> value="1" id="pmt-all-catalog-1" name="pmt-all-catalog" class="radio" />
						<label for="pmt-all-catalog-1"><?php print _('Inclure tous les produits, sauf exceptions ci-dessous :'); ?></label><br />
						<input type="radio" <?php print !$pmt['all-catalog'] ? 'checked="checked"' : ''; ?> value="0" id="pmt-all-catalog-0" name="pmt-all-catalog" class="radio" />
						<label for="pmt-all-catalog-0"><?php print _('Exclure tous les produits, sauf exceptions ci-dessous :'); ?></label>
					</td></tr>
					<tr>
						<td colspan="2" class="pmt-spe-rules">
							<fieldset id="pmt-add-rule">
								<legend><?php echo _("Ajouter une exception"); ?></legend>

								<input type="hidden" name="elem-type" id="elem-type-prd" value="" />
								<input type="hidden" name="elem-id" id="elem-id-prd" value="" />

								<div>
									<input disabled="disabled" type="radio" id="pmt-add-rule-prd" value="prd" name="pmt-add-rule" class="radio" />
									<label class="inline rwd-add-rule-label" for="pmt-prd-name"><?php print _('Une référence de produit :'); ?></label>
									<input type="text" class="ref" maxlength="16" id="pmt-prd-name" name="pmt-prd-name" />
									<input type="button" name="pmt-ref-select" id="pmt-ref-select" class="button" value="<?php echo _("Choisir"); ?>" />
								</div>

								<div>
									<input disabled="disabled" type="radio" id="pmt-add-rule-set" class="pmt-add-rule-set" value="prd" name="pmt-add-rule" class="radio" />
									<label class="inline rwd-add-rule-label" for="pmt-prd-name"><?php print _('Une plage de références :'); ?></label>
									<input type="text" class="ref" maxlength="16" id="pmt-set1" name="pmt-set1" />
									<label id="pmt-label-ref2" for="pmt-set2"> <?php echo _("à"); ?> </label>
									<input type="text" class="ref" maxlength="16" id="pmt-set2" name="pmt-set2" />
								</div>

								<div>
									<input disabled="disabled" type="radio" id="pmt-add-rule-cat" value="cat" name="pmt-add-rule" class="radio" />
									<label class="inline rwd-add-rule-label" for="pmt-add-rule-cat"><?php print _('Une catégorie de produits :'); ?></label>
									<input class="text" type="text" readonly="readonly" id="pmt-cat-name" name="pmt-cat-name" />
									<input type="button" name="pmt-cat-select" id="pmt-cat-select" class="button" value="<?php echo _("Choisir"); ?>" />
								</div>

								<div>
									<input disabled="disabled" type="radio" id="pmt-add-rule-brd" value="brd" name="pmt-add-rule" class="radio" />
									<label class="inline rwd-add-rule-label" for="pmt-add-rule-brd"><?php print _('Une marque de produits :'); ?></label>
									<input class="text" type="text" readonly="readonly" id="pmt-brd-name" name="pmt-brd-name" />
									<input type="button" name="pmt-brd-select" id="pmt-brd-select" class="button" value="<?php echo _("Choisir"); ?>" />
								</div>

								<div class="pmt-rules-buttons">
									<?php if( $_GET['id']!=0 && $has_other_promotions ){ ?>
									<input title="<?php print _('Importer depuis une autre promotion') ?>" type="button" value="<?php print _('Importer depuis une autre promotion') ?>" name="pmt-prd-include" class="button pmt-rules-prd-import" />
									<?php } ?>
									<?php print '<input type="button" value="'._('Ajout rapide').'" name="quick-ref" data-cod-id="'.$_GET['id'].'"
										title="'._('Ajouter une liste de référence articles').'" class="button pmt-rules-prd-quick" />'; ?>
									<input title="<?php echo _("Inclure dans la promotion"); ?>" onclick="return addRulesProducts( true );" type="submit" value="<?php echo _("Inclure dans la promotion"); ?>" name="pmt-prd-include" class="button"  />
									<input title="<?php echo _("Exclure de la promotion"); ?>" onclick="return addRulesProducts( false );" type="submit" value="<?php echo _("Exclure de la promotion"); ?>" name="pmt-prd-exclude" class="button" />
								</div>

							</fieldset>

						</td>
					</tr>
					<tr><td colspan="2" id="pmt-list-rules-prd" class="pmt-spe-rules">
						<div class="notice"><?php echo _("Aucune exception n'est enregistrée pour le moment."); ?></div>
					</td></tr>
					<tr><td colspan="2" class="pmt-spe-rules with-border-top">
						<fieldset>
							<legend><?php echo _("Règles supplémentaires"); ?></legend>
							<input type="checkbox" value="1" id="include_pmt" name="include_pmt" />
							<label for="include_pmt"><?php echo _("Inclure les articles bénéficiant déjà d'une promotion individuelle."); ?></label>

							<div class="clear"></div>

							<input type="checkbox" value="1" id="only_destock" name="only_destock" />
							<label for="only_destock"><?php echo _("Inclure uniquement les articles en déstockage."); ?></label>
						</fieldset>
					</td></tr>
				</tbody>
				<tfoot>
					<tr><td colspan="2"><?php
						if( $pmt['id']<=0 && $_GET['type']==_PMT_TYPE_CHEEKBOOK ){
							print '
								<input class="saveAll" type="submit" name="save" value="' . _("Enregistrer") . '" />
							';
						}else{
							print '
								<input onclick="saveRulesProducts();" type="button" value="' . _("Enregistrer") . '" name="save-products" />
							';
						}

						print '
							<input type="submit" value="' . _("Annuler") . '" name="cancel-products" />
						';
					?></td></tr>
				</tfoot>
			</table>

			<table id="tb-tabCustomers" class="pmt-special">
				<caption><?php echo _("Règles d'inclusion / d'exclusion"); ?></caption>
				<tbody>
					<tr><td colspan="2" class="pmt-spe-rules">
						<input type="radio" class="radio" name="pmt-all-customers" id="all-customers-1" value="1" <?php if($pmt['all-customers']) print 'checked="checked"'; ?> />
						<label for="all-customers-1"><?php echo _("Tous les comptes clients, à l'exception des comptes ci-dessous :"); ?></label><br />
						<input type="radio" class="radio" name="pmt-all-customers" id="all-customers-0" value="0" <?php if(!$pmt['all-customers']) print 'checked="checked"'; ?> />
						<label for="all-customers-0"><?php echo _("Aucun comptes clients, à l'exception des comptes ci-dessous :"); ?></label><br />
					</td></tr>
					<tr><td colspan="2" class="pmt-spe-rules">

						<fieldset id="pmt-add-rule-user">
							<legend><?php echo _("Ajouter une exception"); ?></legend>

							<input type="hidden" name="elem-type" id="elem-type-user" value="" />
							<input type="hidden" name="elem-id" id="elem-id-user" value="" />

							<div>
								<input disabled="disabled" type="radio" id="pmt-add-rule-prf" value="prf" name="pmt-add-rule" class="radio" />
								<label class="inline rwd-add-rule-label" for="pmt-add-rule-prf"><?php print _('Type de compte :'); ?></label>
								<select name="pmt-prf-name" id="pmt-prf-name">
									<option value="-1"></option>
									<?php
										require_once('users.inc.php');
										$profiles = gu_profiles_get();
										while( $p = ria_mysql_fetch_array($profiles) )
											print '<option value="'.$p['id'].'">'.htmlspecialchars($p['name']).'</option>';
									?>
								</select>
							</div>

							<div>
								<input disabled="disabled" type="radio" id="pmt-add-rule-seg" value="seg" name="pmt-add-rule" class="radio" />
								<label class="inline rwd-add-rule-label" for="pmt-add-rule-seg"><?php print _('Segment de clients :'); ?></label>
								<select name="pmt-seg-name" id="pmt-seg-name">
									<option value="-1"></option>
									<?php
										require_once('segments.inc.php');
										$segments = seg_segments_get( 0, CLS_USER );
										while( $s = ria_mysql_fetch_array($segments) )
											print '<option value="'.$s['id'].'">'.htmlspecialchars($s['name']).'</option>';
									?>
								</select>
							</div>

							<div>
								<input disabled="disabled" type="radio" id="pmt-add-rule-usr" value="usr" name="pmt-add-rule" class="radio" />
								<label class="inline rwd-add-rule-label" for="pmt-add-rule-usr"><?php print _('Compte client :'); ?></label>
								<input class="text" type="text" readonly="readonly" id="pmt-usr-name" name="pmt-usr-name" />
								<input type="button" name="pmt-usr-select" id="pmt-usr-select" class="button" value="<?php echo _("Choisir"); ?>" />
							</div>
							<div class="pmt-rules-buttons">
								<?php if( $_GET['id']!=0 && $has_other_promotions ){ ?>
								<input title="<?php print _('Importer depuis une autre promotion') ?>" type="button" value="<?php print _('Importer depuis une autre promotion') ?>" name="pmt-usr-include" class="button pmt-rules-usr-import" />
								<?php } ?>
								<input type="submit" class="button" name="pmt-usr-include" value="<?php echo _("Inclure dans la promotion"); ?>" onclick="return addRulesCustomers(1);" />
								<input type="submit" class="button" name="pmt-usr-exclude" value="<?php echo _("Exclure de la promotion"); ?>" onclick="return addRulesCustomers(0);" />
							</div>
						</fieldset>
					</td></tr>
					<tr>
						<td id="pmt-list-rules-user" class="pmt-spe-rules" colspan="2">
							<div class="notice"><?php print _('Aucune exception n\'est enregistrée pour le moment.'); ?></div>
						</td>
					</tr>
					<?php $count = pmt_codes_get_users_count($pmt['id'], ($pmt['all-customers'] !== '0')); ?>
					<tr>
						<td class="pmt-spe-rules" colspan="2">
							<?php print _('Nombre de comptes clients inclus dans la promotion : '); ?>
							<span class="customers-count-pmt"><?php print ria_number_format($count); ?></span>
						</td>
					</tr>
				</tbody>
				<tfoot>
					<tr><td colspan="2"><?php
						if( $pmt['id']<=0 && $_GET['type']==_PMT_TYPE_CHEEKBOOK ){
							print '
								<input class="saveAll" type="submit" name="save" value="' . _("Enregistrer") . '" />
							';
						}else{
							print '
								<input onclick="return saveRulesCustomers();" type="submit" name="save-customers" value="' . _("Enregistrer") . '" />
							';
						}

						print '
							<input type="submit" name="cancel-customers" value="' . _("Annuler") . '" />
						';
					?></td></tr>
				</tfoot>
			</table>

			<?php
				print view_admin_tab_fields( CLS_PMT_CODE, $pmt['id'], $config['i18n_lng'], '', 'tb-tabFields', 'pmt-special' );
			?>

			<div id="tb-tabVariations" class="pmt-special">
				<div class="notice"><?php echo _("Les internautes peuvent se tromper lorsqu’ils renseignent un code promotion existant. Afin de faciliter son utilisation vous pouvez renseigner ici une ou des orthographes approchantes qui seront acceptées."); ?></div>
				<table class="checklist">
					<thead>
						<tr>
							<th id="var-del">
								<input type="checkbox" onclick="checkAllClick(this)" class="checkbox" />
							</th>
							<th id="var-code"><?php echo _('Variantes'); ?></th>
						</tr>
					</thead>
					<tbody><?php
						$rvar = pmt_codes_variations_get( $pmt['id'] );
						if( !$rvar || !ria_mysql_num_rows($rvar) ){
							print '
								<tr>
									<td colspan="2">' . _("Aucune orthographe approchée n'est encore définie pour le code promotion.") . '</td>
								</tr>
							';
						}else{
							while( $var = ria_mysql_fetch_array($rvar) ){
								print '
									<tr>
										<td headers="var-del">
											<input type="checkbox" name="del-var[]" id="del-var-'.$var['cvt_id'].'" value="'.$var['cvt_id'].'" />
										</td>
										<td headers="var-code">
											<label for="del-var-'.$var['cvt_id'].'">'.htmlspecialchars( $var['code'] ).'</label>
										</td>
									</tr>
								';
							}
						}
					?></tbody>
					<tfoot>
						<tr>
							<td colspan="2">
								<?php if( ria_mysql_num_rows($rvar) ){ ?>
								<input type="submit" class="btn-del" value="<?php echo _("Supprimer"); ?>" name="del-variations" onclick="return delVariations();" />
								<?php } ?>
								<input type="text" name="new-variation" id="new-variation" value="" />
								<input type="submit" value="<?php echo _("Ajouter"); ?>" name="save-variations" onclick="return saveVariations();" />
							</td>
						</tr>
					</tfoot>
				</table>
			</div>
		<?php } if( $pmt['id']>0 ){
			$type_promo =  array(_PMT_TYPE_CODE, _PMT_TYPE_CREDIT, _PMT_TYPE_BA, _PMT_TYPE_REWARD, _PMT_TYPE_CHEEKBOOK, _PMT_TYPE_GIFTS);
			if( in_array($_GET['type'], $type_promo)){
				$states = pmt_codes_get_statistics( $_GET['id'],true);
			}else{
				$states = pmt_codes_get_statistics( $_GET['id'] );
			}

			$r_pmt_products = pmt_products_get($pmt['id']);
			$arr_products_id = array();

			if($r_pmt_products){
				while($product = ria_mysql_fetch_assoc($r_pmt_products)){
					array_push($arr_products_id,$product["id"]);
				}
			}

			$states_table = array(
				array( // Statuts de commande pris en compte pour le calcul du chiffre d'affaires potentiel
					'name' => 'Chiffre d\'affaires potentiel',
					'codes' => array( _STATE_BASKET, _STATE_WAIT_PAY ),
					'desc' => 'Statistiques sur le chiffre d\'affaires pouvant être généré par le code promotion',
					'headers' => 'poteniel'
				),
				array( // Statuts de commande pris en compte pour le calcul du chiffre d'affaires confirmé
					'name' => 'Chiffre d\'affaires confirmé',
					'codes' => array( _STATE_PAY_WAIT_CONFIRM, _STATE_PAY_CONFIRM, _STATE_IN_PROCESS, _STATE_PREPARATION, _STATE_BL_READY, _STATE_BL_EXP, _STATE_BL_STORE, _STATE_INVOICE, _STATE_ARCHIVE, _STATE_INV_STORE ),
					'desc' => 'Statistiques sur le chiffre d\'affaires effectivement généré par le code promotion',
					'headers' => 'confirm'
				),
				array( // Status de commande pris en compte pour le calcul du chiffre d'affaires annulé
					'name' => 'Chiffre d\'affaires annulé',
					'codes' => array( _STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND ),
					'desc' => 'Statistiques sur le chiffre d\'affaires annulé généré par le code promotion',
					'headers' => 'cancel'
				)
			);
		?>

			<div class="stats-menu export-button">
				<button id="export" name="export" class="btn-move btn-export" type="button">
					Exporter
				</button>
				<div class="clear"></div>
			</div>

			<?php
				foreach( $states_table as $t ){
					ria_mysql_data_seek( $states, 0 );
			?>

				<table class="tb-tabStats pmt-special">
					<caption><?php print htmlspecialchars($t['name']); ?></caption>
					<tbody>
						<tr id="head-tabStats">
							<th id="state-name-<?php print $t['headers']; ?>"><?php echo _("Statut de commande"); ?></th>
							<th id="state-usage-<?php print $t['headers']; ?>" class="align-right"><?php echo _("Nombre d'utilisations"); ?></th>
							<?php if( !in_array($_GET['type'], array(_PMT_TYPE_CODE, _PMT_TYPE_CREDIT)) ){?>
								<th id="state-ventes-<?php print $t['headers']; ?>" class="align-right"><?php echo _("Ventes"); ?></th>
							<?php } ?>
							<th id="state-ca-ht-<?php print $t['headers']; ?>" class="align-right"><?php echo _("CA HT"); ?></th>
							<th id="state-ca-ht-<?php print $t['headers']; ?>" class="align-right"><?php echo _("CA TTC"); ?></th>
							<th id="state-total-ht-<?php print $t['headers']; ?>" class="align-right"><?php echo _("Total HT"); ?></th>
							<th id="state-total-ttc-<?php print $t['headers']; ?>" class="align-right"><?php echo _("Total TTC"); ?></th>
						</tr>
						<?php
							$usages = $sold = $ca_ht =  $total_ht = $total_ttc = $ca_ttc = 0;
							while( $s = ria_mysql_fetch_array($states) ){
								if( array_search( $s['state_id'], $t['codes'] )!==false ){

									$r_products = "";
									$usages += $s['usages'];
									$sold += $s['sold'];
									$ca_ht += $s['ca_ht'];
									$ca_ttc += $s['ca_ttc'];
									$total_ht += $s['total_ht'];
									$total_ttc += $s['total_ttc'];

									print '
									<tr>
										<td headers="state-name-'.$t['headers'].'" data-label="'._('Statut de commande :').' "><a href="/admin/orders/orders.php?state='.$s['state_id'].'&pmt='.$_GET['id'].'">'.htmlspecialchars( $s['state_name'] ).'</a></td>
										<td headers="state-usage-'.$t['headers'].'" class="numeric" data-label="'._('Nombre d\'utilisations :').' ">'.ria_number_format($s['usages']).'</td>';
									if($_GET['type'] != _PMT_TYPE_CODE && $_GET['type'] != _PMT_TYPE_CREDIT){
										print '<td headers="state-total-ht-'.$t['headers'].'" class="numeric" data-label="'._('Ventes :').' ">'.ria_number_format($s['sold']).'</td>';
									}
									print '
										<td headers="state-usage-'.$t['headers'].'" class="numeric" data-label="'._('CA HT :').' ">'.ria_number_format($s['ca_ht'], NumberFormatter::CURRENCY, 2).'</td>
										<td headers="state-usage-'.$t['headers'].'" class="numeric" data-label="'._('CA TTC :').' ">'.ria_number_format($s['ca_ttc'], NumberFormatter::CURRENCY, 2).'</td>
										<td headers="state-total-ht-'.$t['headers'].'" class="numeric" data-label="'._('Total HT :').' ">'.ria_number_format($s['total_ht'], NumberFormatter::CURRENCY, 2).'</td>
										<td headers="state-total-ttc-'.$t['headers'].'" class="numeric" data-label="'._('Total TTC :').' ">'.ria_number_format($s['total_ttc'], NumberFormatter::CURRENCY, 2).'</td>
									</tr>
									';
								}
							}
							?>

					</tbody>
					<tfoot>
						<tr>
							<th class="align-right"><?php print _('Totaux :'); ?></th>
							<th class="numeric" <?php print 'data-label="'._('Nombre d\'utilisations :').'" '?>><?php print ria_number_format($usages); ?></th>
							<?php if( !in_array($_GET['type'], array(_PMT_TYPE_CODE, _PMT_TYPE_CREDIT)) ){ ?>
								<th class="numeric" <?php print 'data-label="'._('Ventes:').'" '?>><?php print ria_number_format($sold); ?></th>
							<?php } ?>
							<th class="numeric" <?php print 'data-label="'._('CA HT :').'" '?>><?php print ria_number_format($ca_ht, NumberFormatter::CURRENCY, 2); ?></th>
							<th class="numeric" <?php print 'data-label="'._('CA TTC :').'" '?>><?php print ria_number_format($ca_ttc, NumberFormatter::CURRENCY, 2); ?></th>
							<th class="numeric" <?php print 'data-label="'._('Total HT :').'" '?>><?php print ria_number_format($total_ht, NumberFormatter::CURRENCY, 2); ?></th>
							<th class="numeric" <?php print 'data-label="'._('Total TTC :').'" '?>><?php print ria_number_format($total_ttc, NumberFormatter::CURRENCY, 2); ?></th>
						</tr>
					</tfoot>
				</table>

			<?php } ?>

			<table id="tb-tabSerie" class="checklist pmt-special">
				<caption><?php echo _("Liste des Codes promotion"); ?></caption>
				<thead>
					<tr>
						<th colspan="4">
							<div class="align-right">
					   <label><?php print _('Exporter les codes :'); ?></label>
								<select name="s-export[]" id="s-export">
									<option value="all"><?php echo _("Tous"); ?></option>
									<option value="0"><?php echo _("En cours"); ?></option>
									<option value="1"><?php echo _("Utilisés"); ?></option>
								</select>
								<input type="submit" name="export-serie" value="<?php echo _("Exporter"); ?>" />
							</div>
						</th>
					</tr>
					<tr class="th-head-second">
						<th id="pmt-select"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
						<th id="pmt-serie-code"><?php echo _("Code"); ?> </th>
						<th id="pmt-serie-etat"><?php echo _("Etat"); ?></th>
						<th id="pmt-serie-cmd"><?php echo _("Commande"); ?></th>
					</tr>
				</thead>
				<tbody><?php
					if( $rcode_serie && ria_mysql_num_rows($rcode_serie) ){
						ria_mysql_data_seek( $rcode_serie, ($page-1)*$limit_BA );

						$i = 1;
						while( $code = ria_mysql_fetch_array($rcode_serie) ){
							if( $i>$limit_BA ){
								break;
							}

							$status = '';
							if( $code['used']>0 ){
								$status = '<span class="pmt-used">' . _("Utilisé") . '</span>';
							} else {
								$status = '<span class="pmt-not-used">' . _("En cours") . '</span>';
							}

							$orders = '';
							$ar_orders = pmt_codes_get_orders( $code['id'] );
							foreach( $ar_orders as $o ){
								if( trim($orders)!='' ){
									$orders .= '<br />';
								}

								$orders .= '<a href="/admin/orders/order.php?ord='.$o.'">N°'.$o.'</a>';
							}

							print '
								<tr>
									<td headers="pmt-select"><input type="checkbox" class="checkbox" name="pmt[]" value="'.$code['id'].'" /></td>
									<td>'.$code['code'].'</td>
									<td>'.$status.'</td>
									<td>'.$orders.'</td>
								</tr>
							';

							$i++;
						}
					}else{
						print '<tr><td colspan="4"></td></tr>';
					}
				?></tbody>

				<tfoot>
					<tr>
						<td colspan="4" class="align-left">
							<input class="float-left" type="submit" name="delete" value="<?php echo _("Supprimer"); ?>" />
							<div class="align-right">
								<label><?php print _('Exporter les codes :'); ?></label>
								<select name="s-export[]" id="fs-export">
									<option value="all"><?php echo _("Tous"); ?></option>
									<option value="0"><?php echo _("En cours"); ?></option>
									<option value="1"><?php echo _("Utilisés"); ?></option>
								</select>
								<input type="submit" name="export-serie" value="<?php echo _("Exporter"); ?>" />
							</div>
						</td>
					</tr>
					<tr>
						<td id="pagination" colspan="4">
							<?php
								$link = '/admin/promotions/specials/edit.php?id='.$_GET['id'].'&type='.$_GET['type'];

								if( $pages>1 ){
									if( $page>1 ){
										print '<a onclick="return reloadPromotionsSerie('.($page-1).');" href="'.$link.'&amp;page='.($page-1).'#tabSerie">&laquo; ' . _("Page précédente") . '</a> | ';
									}

									for( $i=$pmin; $i<=$pmax; $i++ ){
										if( $i==$page ){
											print '<b>'.$page.'</b>';
										}else{
											print '<a onclick="return reloadPromotionsSerie('.($i).');" href="'.$link.'&amp;page='.($i).'#tabSerie">'.$i.'</a>';
										}

										if( $i<$pmax ){
											print ' | ';
										}
									}

									if( $page<$pages ){
										print ' | <a onclick="return reloadPromotionsSerie('.($page+1).');" href="'.$link.'&amp;page='.($page+1).'#tabSerie">' . _("Page suivante") . ' &raquo;</a>';
									}
								}

							?>
						</td>
					</tr>
				</tfoot>
			</table>
		<?php } ?>
	</div>
</form>
<div class="page-load-block"></div>

<script><!--
	<?php

	if( $_GET['id'] != 0){
		$can_edit = false;
		if ( $_GET['type']== _PMT_TYPE_SOLDES ){
			if( gu_user_is_authorized('_RGH_ADMIN_PROMO_SOLDE_EDIT') ){
				$can_edit = true;
			}
		}elseif( $_GET['type'] == _PMT_TYPE_REWARD ){
			if( gu_user_is_authorized('_RGH_ADMIN_PROMO_REWARD_EDIT') ){
				$can_edit = true;
			}
		}else{
			if( gu_user_is_authorized('_RGH_ADMIN_PROMO_PRD_EDIT') ){
				$can_edit = true;
			}
		}

		if( !$can_edit ){ ?>
			$(document).ready(function(){
				$('table').find('input, select, textarea').attr('disabled', 'disabled')
				$('table a.edit').remove();
				$('input[onclick]').attr('onclick','').unbind('click');
				$('#save').remove();
			});
	<?php
		}
	} ?>
//--></script>

<script><!--
	var displayColisage = <?php print gu_user_is_authorized('_RGH_ADMIN_PROMO_COLISAGE') ? '1' : '0'; ?>;
	var typePromotion = <?php print $_GET['type']; ?>;
	var idCod = <?php print $_GET['id']; ?>;
	var nbReducProduct = <?php print $count_reduct_prd; ?>;
	var nbGroups = <?php print $count_grp; ?>;
	var nbGroupCdts = {};
--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>