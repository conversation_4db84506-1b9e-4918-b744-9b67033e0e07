<?php

	/**	\file export-stats-promotions.php
	 *	Ce fichier réalise l'exportation de données statistiques sur les promotions au format Microsoft Excel
	 */

	require_once( $_SERVER['DOCUMENT_ROOT'].'/config-old.inc.php' );
	require_once( 'excel/PHPExcel.php' );

	// Vérifie que l'utilisateur en cours à accès peut intervenir sur les promotions
	gu_if_authorized_else_403('_MDL_PROMO');

	$cod = ria_mysql_fetch_array( pmt_codes_get($_GET['cod']) );
	
	// Création de l'objet PHPExcel
	$objPHPExcel = new PHPExcel();
	
	// Détermine les propriétés de la feuille Excel
	$objPHPExcel->getProperties()->setCreator("riaStudio")
								 ->setLastModifiedBy("riaStudio")
								 ->setTitle( _("Statistiques de promotion") )
								 ->setSubject( _("Statistiques de promotion") )
								 ->setDescription( _("Statistiques de promotion") )
								 ->setKeywords("stats promotion")
								 ->setCategory("");
	
	
	// Création du document
	$objPHPExcel->getActiveSheet()->setSheetState(PHPExcel_Worksheet::SHEETSTATE_VERYHIDDEN);
	
	// Création de la feuille Excel
	$objWorksheet = $objPHPExcel->createSheet();
	$objWorksheet->setTitle('Statistiques');
	
	$letter = array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z');
	
	$states = pmt_codes_get_statistics( $_GET['cod'] );
			
	$states_table = array(
		array( // Statuts de commande pris en compte pour le calcul du chiffre d'affaires potentiel
			'name' => _('Chiffre d\'affaires potentiel'),
			'codes' => array( 1, 3 ),
			'desc' => _('Statistiques sur le chiffre d\'affaires pouvant être généré par le code promotion'),
			'headers' => 'poteniel'
		),
		array( // Statuts de commande pris en compte pour le calcul du chiffre d'affaires confirmé
			'name' => _('Chiffre d\'affaires confirmé'),
			'codes' => array( 25, 4, 5, 6, 7, 24, 8, 12, _STATE_INV_STORE ),
			'desc' => _('Statistiques sur le chiffre d\'affaires effectivement généré par le code promotion'),
			'headers' => 'confirm'
		),
		array( // Status de commande pris en compte pour le calcul du chiffre d'affaires annulé
			'name' => _('Chiffre d\'affaires annulé'),
			'codes' => array( 9, 10 ),
			'desc' => _('Statistiques sur le chiffre d\'affaires annulé généré par le code promotion'),
			'headers' => 'cancel'
		)
	);
	
	$i = 0; $line = 1; $last = 1;
	foreach( $states_table as $t ){
		ria_mysql_data_seek( $states, 0 );
		
		// entête de chaque tableau
		$objWorksheet->mergeCells( 'A'.$line.':D'.$line );
		$objWorksheet->setCellValue( 'A'.$line, $t['name'] );
		
		// style sur l'entête de chaque tableau
		$objWorksheet->getStyle('A'.$line.':D'.$line)->getFont()->setBold(true);
		$objWorksheet->getStyle('A'.$line.':D'.$line)->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
		$objWorksheet->getStyle('A'.$line.':D'.$line)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
		
		$line++;
		
		// titre des colonnes
		$objWorksheet->setCellValue( 'A'.$line, _('Statut de commande') );
		$objWorksheet->setCellValue( 'B'.$line, _('Nombre d\'utilisations') );
		$objWorksheet->setCellValue( 'C'.$line, _('Total HT') );
		$objWorksheet->setCellValue( 'D'.$line, _('Total TTC') );

		// style sur le titres des colonnes
		$objWorksheet->getStyle('A'.$line.':D'.$line)->getFont()->setBold(true);
		$objWorksheet->getStyle('A'.$line.':D'.$line)->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_BLACK);
		$objWorksheet->getStyle('A'.$line.':D'.$line)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('E3E3E3');
		$objWorksheet->getStyle('A'.$line.':D'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		
		$line++.
		
		$usages = $total_ht = $total_ttc = 0;
		while( $s = ria_mysql_fetch_array($states) ){
			if( array_search( $s['state_id'], $t['codes'] )!==false ){
				$usages += $s['usages'];
				$total_ht += $s['total_ht'];
				$total_ttc += $s['total_ttc'];
				
				// ligne de statistiques de chaque statut
				$objWorksheet->setCellValue( 'A'.$line, $s['state_name'] );
				$objWorksheet->setCellValue( 'B'.$line, number_format( $s['usages'], 0, ',', ' ' ) );
				$objWorksheet->setCellValue( 'C'.$line, number_format( $s['total_ht'], 2, ',', ' ' ) );
				$objWorksheet->setCellValue( 'D'.$line, number_format( $s['total_ttc'], 2, ',', ' ' ) );
				
				$line++;
			}
		}
		
		// pieds du tableau
		$objWorksheet->setCellValue( 'A'.$line, _('Total :') );
		$objWorksheet->setCellValue( 'B'.$line, number_format( $usages, 0, ',', ' ' ) );
		$objWorksheet->setCellValue( 'C'.$line, number_format( $total_ht, 2, ',', ' ' ) );
		$objWorksheet->setCellValue( 'D'.$line, number_format( $total_ttc, 2, ',', ' ' ) );
		
		// style sur le pieds du tableau
		$objWorksheet->getStyle('A'.$line.':D'.$line)->getFont()->setBold(true);
		$objWorksheet->getStyle('A'.$line.':D'.$line)->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_BLACK);
		$objWorksheet->getStyle('A'.$line.':D'.$line)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('E3E3E3');
		$objWorksheet->getStyle('B'.($last+2).':D'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
		
	
		// met des bordures en place
		$objWorksheet->getStyle('A'.$last.':D'.$line)->getBorders()->applyFromArray(
			array(
				'allborders' => array(
					'style' => PHPExcel_Style_Border::BORDER_THIN,
				)
			)
		);
		
		$line = $line+2;
		$last = $line;
	}
	
	// largeur des colonnes
	$objWorksheet->getColumnDimension('A')->setWidth(50);
	$objWorksheet->getColumnDimension('B')->setWidth(20);
	$objWorksheet->getColumnDimension('C')->setWidth(10);
	$objWorksheet->getColumnDimension('D')->setWidth(10);

	// Confirgure l'ouverture d'Excel sur la case A1
	$objPHPExcel->setActiveSheetIndex(1);


	// Redirect output to a client web browser (Excel5)
	header('Content-Type: application/vnd.ms-excel');
	header('Content-Disposition: attachment;filename="stats-pmt-'.strtolower(trim($cod['name']) ? $cod['name'] : $cod['code']).'.xls"');
	header('Cache-Control: max-age=0');

	// Ecrit le fichier et le sauvegarde
	$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
	$objWriter->save('php://output');
	exit;