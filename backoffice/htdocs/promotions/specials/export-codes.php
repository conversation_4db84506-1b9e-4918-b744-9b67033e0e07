<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD');

	require_once('promotions.inc.php');
	
	header('Content-disposition: attachment; filename="export-produits.csv"');
	header('Content-type: application/octetstream');
	header('Pragma: no-cache');
	header('Expires: 0');
	
	$type = false;
	
	if( isset($_GET['type']) && pmt_types_exists($_GET['type']) ){
		$rtype = pmt_types_get( $_GET['type'] );
		if( $rtype && ria_mysql_num_rows($rtype) ){
			$type = ria_mysql_fetch_array( $rtype );
		}
	}
	
	if( !$type ){
		return;
	}
	
	print utf8_decode('"N° de carnet";"n° ticket";"Code Ticket"');
	$rfld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), false, array(), null, CLS_PMT_CODE );
	
	$ar_fields = array();
	if( $rfld && ria_mysql_num_rows($rfld) ){
		while( $fld = ria_mysql_fetch_array($rfld) ){
			$ar_fields[] = $fld['id'];
			print utf8_decode( ';"'.$fld['name'].'"' );
		}
	}
	
	print "\n";
	$ar_parent = isset($_GET['pmt']) ? (is_array($_GET['pmt']) ? $_GET['pmt'] : array($_GET['pmt'])) : false;
	if( is_array($ar_parent) && sizeof($ar_parent) ){
		foreach( $ar_parent as $p ){
			$rcod = pmt_codes_get( $p );
			if( $rcod && ria_mysql_num_rows($rcod) ){
				$cod = ria_mysql_fetch_array( $rcod );
				
				$rchild = pmt_codes_get( null, null, false, false, false, $cod['id'] );
				if( $rchild && ria_mysql_num_rows($rchild) ){
					$c = 1;
					while( $child = ria_mysql_fetch_array($rchild) ){
						print utf8_decode( '"'.$cod['name'].'";"'.(($cod['name']*100)+$c).'";"'.$child['code'].'"' );
						
						foreach( $ar_fields as $f ){
							$val = fld_object_values_get( $p, $f );
							print utf8_decode( ';"'.$val.'"' );
						}
						
						print "\n";
						$c++;
					}
				}
			}
		}
	}
?>