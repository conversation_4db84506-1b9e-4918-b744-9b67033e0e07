<?php
	/**	\file popup-quick-products.php
	 *	Cette popup est utilisée pour ajouter une liste de références à une promotion.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD_ADD');

	if (!isset($_GET['cod'])){
		$g_error = _('Il manque des paramètres');
	}

	$ref_can_add = [];
	$products_can_add = [];

	// Analyse des références
	// Pour chaque référence, on l'a recherche en base
	// Si elle existe, elle est ajoutée au tableau $ref_can_add
	// Sinon un message d'erreur prévient l'utilisateur que la référence n'a pas été trouvé
	if( isset($_POST['quick-order-analyse']) ){
		if( !isset($_POST['references']) || trim($_POST['references']) == '' ){
			$error[] = "Aucune référence trouvée.";
		}else{
			// Récupère chaque référence sur chaque ligne
			$refs = explode( "\n", trim( str_replace(' ', '', $_POST['references'])) );
			foreach($refs as $k=>$d){
				$refs[$k] = str_replace("\n", '' ,trim($d));
			}

			$exists = [];

			$r_prd = prd_products_get_simple( 0, $refs );
			if( $r_prd ){
				while( $prd = ria_mysql_fetch_assoc($r_prd) ){
					$exists[] = $prd['ref'];
					$products_can_add[] = $prd['id'];
				}
			}

			$not_exists = array_diff( $refs, $exists );

			if( count($not_exists) > 1 ){
				$error[] = 'Les références suivantes n\'ont pas été trouvées : ';
				foreach( $not_exists as $r ){
					$error[] = ' - '.$r;
				}
			}elseif( count($not_exists) ){
				$error[] = str_replace( '#param[ref]#', array_shift($not_exists), _('La référence "#param[ref]#" n\'a pas été trouvée.') );
			}

			$ref_can_add = $exists;
		}
	}

	// Ajout des articles à la promotion
	if( isset($_POST['quick-order-validate']) ){
		if( !isset($_POST['quick-order-product-ids']) || trim($_POST['quick-order-product-ids']) == '' ){
			$error = "Aucune référence n'a pu être ajoutée.";
		}else{
			$prd_ids = explode(',', $_POST['quick-order-product-ids']);

			foreach( $prd_ids as $id ){
				if( isset($_POST['exclude']) && $_POST['exclude'] ){
					if( !pmt_products_add($_GET['cod'], $id, 0) ){
						$error[] = _('La référence "'.htmlspecialchars(prd_products_get_ref($id)).'" n\'a pas pu être exclue de la promotion.');
					}
				}else{
					if( !pmt_products_add($_GET['cod'], $id, 1) ){
						$error[] = _('La référence "'.htmlspecialchars(prd_products_get_ref($id)).'" n\'a pas pu être inclue de la promotion.');
					}
				}
			};
		}
	}

	// Annule après analyse des références
	if( isset($_POST['quick-order-cancel']) ){
		header('Location: /admin/promotions/specials/popup-quick-products.php?cod='.$_GET['cod']);
		exit;
	}

	define('ADMIN_PAGE_TITLE', _('Ajout rapide d\'articles'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_CLASS_BODY', 'popup-iframe');

	require_once('admin/skin/header.inc.php');

	if (isset($g_error)) {
		print '<div class="error">'.nl2br($g_error).'</div>';
		exit;
	}

	if( !count($ref_can_add) ){
		print '<div id="quick-order-instructions" class="notice">'
			.'<p>'._('Indiquez sur des lignes différentes la référence des articles que vous souhaitez ajouter à la promotion. Par exemple :').'</p>'
			.'<ul>';

		$r_prd = prd_products_get_simple();
		if( $r_prd ){
			$i = 0;
			while( $prd = ria_mysql_fetch_assoc($r_prd) ){
				if( prd_products_is_port($prd['ref']) ){
					continue;
				}

				if( ($i++) > 5 ){
					break;
				}

				print '<li>'.htmlspecialchars( $prd['ref'] ).'</li>';
			}
		}

			print '</ul>'
		.'</div>';
	}

	if( isset($error) ){
		print '<div class="error">'.nl2br( implode("\n", $error) ).'</div>';
	}

	$label = _('Liste des références :');
	if( count($ref_can_add) ){
		if( count($ref_can_add) > 1 ){
			$label = _('Les références suivantes seront ajoutées à la promotion :');
		}else{
			$label = _('La référence suivante sera ajoutée à la promotion :');
		}
	}

	print '<form method="post" action="/admin/promotions/specials/popup-quick-products.php?cod='.$_GET['cod'].'">'
		.'<div class="quick-order-div" id="quick-order-ref">'
				.'<label class="quick-order-label" for="quick-order-references">'.$label.'</label><br />'
				.'<textarea name="references" id="quick-order-references" col="80" rows="10"
					'.( count($ref_can_add) ? 'disabled="disabled"' : '' ).'>';

					if( count($ref_can_add)  ){
						print implode( "\n", $ref_can_add );
					}

				print '</textarea>'
				.'<input type="hidden" name="quick-order-product-ids" id="quick-order-product-ids" value="'.implode(',', $products_can_add).'" />'
		.'</div>';

		if( !count($ref_can_add) ){
			print '<div class="quick-order-step quick-order-step-1" style="display:block">'
				.'<button type="submit" id="quick-order-analyse" name="quick-order-analyse">'._('Analyser').'</button>&nbsp;'
				.'<span class="loader" style="display:none;">'
					.'<img src="/admin/images/loader2.gif" title="'._('Chargement en cours, veuillez patienter.').'" /> '._('Veuillez patienter')
				.'</span>'
			.'</div>';
		}else{
			print '<div class="align-right quick-order-step quick-order-step-2">'
				.'<input type="checkbox" name="exclude" id="exclude" value="1" /> '
				.'<label for="exclude">'._('En cochant cette case, les références seront exclues de la promotion.').'</label>'
				.'<br />'
				.'<input type="submit" id="quick-order-validate" name="quick-order-validate" value="'._('Valider').'"/> '
				.'<input type="submit" id="quick-order-cancel" name="quick-order-cancel" value="'._('Annuler').'"/>'
			.'</div>';
		}


		print '<div class="quick-order-add-success success quick-order-step quick-order-step-2 clear-both" style="display:none;"></div>'
	.'</form>';

	if( !IS_AJAX ){
		?><script>
			var codID = <?php print $_GET['cod']; ?>;
			var ref_data = [];

			$(document).ready(function(){

				// Etape 1 - Analyse du champ de commande rapide : verification de la conformité des lignes puis recherche des produits à partir des références si OK
				$("#quick-order-analyse").click(function(e){
					$('#quick-order-product-ids').val('');

					// Textarea vide, on ne va pas plus loin
					if( !$.trim($("#quick-order-references").val()) ){
						return false;
					}


					$('.quick-order-step-1 .loader').fadeIn('fast');
					return true;
				});

				// Edition du textarea : reaffichage des elements de la 1ere etape
				$('#quick-order-references').on('change keyup paste', function(){
					$('.quick-order-step-2').hide(0, function(){
						$('.quick-order-step-1').show(0);
					});
				});

				<?php if( isset($_POST['quick-order-validate']) && !isset($error) ){ // Enregistre OK, on ferme la popup?>
					parent.pmtOngletActived('tabProducts');
					window.parent.hidePopup();
				<?php } ?>
			});
		</script><?php

		require_once('admin/skin/footer.inc.php');
	}
