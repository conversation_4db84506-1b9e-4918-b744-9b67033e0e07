<?php

	/**	\file export-gifts.php
	 * 
	 * 	Ce fichier exporte au format CSV les Cartes cadeaux
	 * 
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD');

	$ar_pmt_ids = isset($_GET['pmt']) && is_array($_GET['pmt']) && count($_GET['pmt']) ? $_GET['pmt'] : null;

	header('Content-disposition: attachment; filename="export-cartes-cadeaux.csv"');
	header('Content-type: application/octetstream');
	header('Pragma: no-cache');
	header('Expires: 0');
	
	print '"'._('Numéro client').'";"'._('Prénom').'";"'._('Nom').'";"'._('Société').'";"'._('Numéro carte cadeau').'";"'._('Numéro de pièce').'";"'._('Référence carte cadeau').'";"'._('Date création').'";"'._('Fin de validité').'";"'._('Montant TTC').'"'."\n";

	// Récupère les codes promotions
	$r_code = pmt_codes_get($ar_pmt_ids, null, false, _PMT_TYPE_GIFTS );

	if ($r_code) {
		while ($code = ria_mysql_fetch_assoc($r_code)) {
			$ord_piece  = $prd_ref = $usr_ref = $usr_lastname = $usr_firstame = $usr_society = '';

			preg_match("/Carte cadeau de la commande ([0-9]+)/", $code['desc'], $output_array);
			if (is_array($output_array) && count($output_array) == 2 && is_numeric($output_array[1]) && $output_array[1]) {
				$r_order = ord_orders_get_with_adresses( 0, $output_array[1] );
				if ($r_order && ria_mysql_num_rows($r_order)) {
					$order = ria_mysql_fetch_assoc( $r_order );

					$ord_piece = $order['id'];
					$usr_ref = gu_users_get_ref($order['user']);
					$usr_lastname = $order['inv_lastname'];
					$usr_firstame = $order['inv_firstname'];
					$usr_society = $order['inv_society'];

					$r_product = ord_products_get( $order['id'] );
					if ($r_product) {
						while ($product = ria_mysql_fetch_assoc($r_product)) {
							if (round($product['price_ht'],2) == round($code['discount'],2) && pmt_gifts_exists($product['id'])) {
								$prd_ref = $product['ref'];
								break;
							}
						}
					}
				}
			}else{
				// On récupère le premier compte client inclut dans la promotion
				$r_user = pmt_users_get( $code['id'], true );
				if ($r_user && ria_mysql_num_rows($r_user)) {
					$user = mysql_fetch_assoc( $r_user );
					
					$usr_ref = $user['ref'];
					$usr_firstame = $user['firstname'];
					$usr_lastname = $user['lastname'];
					$usr_society = $user['society'];
				}
			}

			print '"'.$usr_ref.'";"'.$usr_lastname.'";"'.$usr_firstame. '";"' . $usr_society . '";"'.htmlspecialchars( $code['code'] ).'";"'.htmlspecialchars($ord_piece).'";"'.htmlspecialchars($prd_ref).'";"'.$code['date_start']. '";"'.$code['date_stop'].'"'.';"'.round(($code['discount'] * _TVA_RATE_DEFAULT), 2) .'"'."\n";
		}
	}