<?php

	/**	\file new.php
	 *	Cette page permet d'ajouter une nouvelle promotion sur produits.
	 *	En donnant un nom à cette promotion, cela crééra automatique un groupe.
	 */

	require_once('products.inc.php');
	require_once('prices.inc.php');
	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD_ADD');

	$r_types = prc_types_get();
	$type_option = '';
	if( $r_types!==false && ria_mysql_num_rows($r_types)>0 ){
		while( $type = ria_mysql_fetch_array($r_types) )
			$type_option .= '		<option value="'.$type['id'].'" >'.$type['name'].'</option>';
	}

	// Récupère les conditions possibles
	$r_fld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array("prc_priority"), true, array(), null, null, true, true);

	$fld_opt = '<option value="-1">'  . _('Choisissez un champ personnalisé') .'</option>';
	// Construit les option des select pour l'ajout d'une condition, le selecte sera construit lors de l'appel pour avoir l'identifiant de la promotions
	if( $r_fld!==false && ria_mysql_num_rows($r_fld)>0 ){
		while( $fld = ria_mysql_fetch_array($r_fld) ){
			if( $fld['cls_id']!=CLS_PRODUCT )
				$fld_opt .= '<option value="'.$fld['id'].'">'.addslashes($fld['name']).' ('.addslashes($fld['cls_name']).')</option>';
		}

		// Retourne au premier index dans les tableaux MySQL
		ria_mysql_data_seek( $r_fld, 0 );
	}

	if( isset($_POST['save-pmt'], $_POST['prd-id']) ){

		$_POST['qte-max'] = $_POST['qte-max']=='' || $_POST['qte-max']=='∞' ? false : $_POST['qte-max'];
		$grp = 0;
		if( trim($_POST['name'])!='' ){
			$priceminister_key = isset($_POST['priceminister_key']) && is_numeric($_POST['priceminister_key']) && $_POST['priceminister_key'] > 0 ? $_POST['priceminister_key'] : 0;

			if( !$grp = prc_promotion_groups_add($_POST['name'], $_POST['date-start'].' '.$_POST['hour-start'], $_POST['date-end'].' '.$_POST['hour-end'], $_POST['qte-min'], $_POST['qte-max'], false, $priceminister_key) )
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la promotion.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		}

		if( !isset($error) ){
			$add_cdt_group = false;
			foreach( $_POST['prd-id'] as $prd ){
				$value = $_POST['prd-remise-val-'.$prd];

				if (($_POST['prd-remise-'.$prd] == 1 || $_POST['prd-remise-'.$prd] == 3)
					&& (
							( isset($_POST['prd-remise-valeur-type-'.$prd]) && $_POST['prd-remise-valeur-type-'.$prd] == 2 )
							||
							( isset($_POST['cat-remise-valeur-type-'.$prd]) && $_POST['cat-remise-valeur-type-'.$prd] == 2 )
						)
					) {
					$rprd = prd_products_get($prd);
					if ($rprd && ($p = ria_mysql_fetch_assoc($rprd)) && $p['tva_rate']) $value /= $p['tva_rate'];
				}

				if( !$prc = prc_prices_add($_POST['prd-remise-'.$prd], $value, $_POST['date-start'].' '.$_POST['hour-start'], $_POST['date-end'].' '.$_POST['hour-end'], $_POST['qte-min'], $prd, 0, false, false, $_POST['name'], array(), $_POST['qte-max'], true, $grp) )
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la promotion.\nVeuillez réessayer ou prendre contact avec l'administrateur.");

				if( !isset($error) && isset($_POST['cdt-new']) ){
					$cdt = $_POST['cdt-new'];
					$sbl = isset($_POST['sbl-new']) ? $_POST['sbl-new'] : '=';
					$val = isset($_POST['val-cdt-new']) ? $_POST['val-cdt-new'] : false;

					$count_cdt = sizeof($_POST['cdt-new']);
					foreach( $_POST['cdt-new'] as $key=>$fld ){
						if( $fld>0 ){
							// On récupère le nom et le type du champ personalisé
							$fld_name = fld_fields_get_name($fld);
							$fld_type = fld_fields_get_type($fld);

							$sb = '';
							if( $fld_type==FLD_TYPE_DATE && (isset($_POST['choose-date-'.$id]) && $_POST['choose-date-'.$id]>0 || isset($_POST['choose-date-new']) && $_POST['choose-date-new']>0) ){
								// Il s'agit d'un champ personnalisé de type date, le traitement est différent
								$chooseDate = isset($_POST['choose-date-new']) ? $_POST['choose-date-new'] : $_POST['choose-date-'.$id];
								switch( $chooseDate ){
									case 1 :
										if( $_POST['val-cdt-date-new-'.$chooseDate]==-1 )
											$error = str_replace("#param[condition_pluriel]#", ( $count_cdt>1 ? _("des conditions") :_("de la condition") ), _("Une erreur s'est produite lors de la mise à jour #param[condition_pluriel]#. \nVeuillez sélectionner une des valeurs suivantes : Aujourd'hui, Cette semaine, Ce mois-ci."));
									case 2 :
										$sb = $_POST['sbl-date-new-'.$_POST['choose-date-new']];
										$value = $_POST['val-cdt-date-new-'.$_POST['choose-date-new']];
										break;
									case 3 :
										$sb = "><";
										$value = $_POST['val-cdt-date-new-3'].';'.$_POST['val-cdt-date-new-3-2'];
										break;
								}

								if( $value=='' || $value==';' )
									$error = "Une erreur s'est produite lors de la mise à jour ".( $count_cdt>1 ? _("des conditions") : _("de la condition") ).". \nAucune valeur n'a été sélectionnée ou définie.";
							} elseif( $fld_type==FLD_TYPE_DATE && !isset($_POST['choose-date-'.$id]) && !isset($_POST['choose-date-new']) ){
								$error = str_replace("#param[condition_pluriel]#", ( $count_cdt>1 ? _("des conditions") : _("de la condition") ), _("Une erreur s'est produite lors de la mise à jour #param[condition_pluriel]#. \nAucune valeur n\'a été sélectionnée ou définie."));
							} else {
								// Il s'agit d'un champ personnalisé autre que le type date, le traitement est le même.
								$sb = isset($sbl[$key]) ? $sbl[$key] : '=';
								if( !isset($val[$key]) )
									$value = false;
								else
									$value = $sb=="><" ? $val[$key].';'.$_POST['val-cdt-new-2'][$key] : $val[$key];
							}

							if( !isset($error) && $sb!=='' ){
								// On vérifie si la condition est valable
								switch( (int)prc_conditions_is_valid($fld, $sb, $value) ){
									case FLD_NOT_EXISTS :
										$error = str_replace("#param[condition_pluriel]#", ( $count_cdt>1 ? _("des conditions") : _("de la condition") ), str_replace("#param[nom_champ]#", $fld_name, _("Une erreur s'est produite lors de l'enregistrement #param[condition_pluriel]# de la promotion.<br />Le champ personnalisé #param[nom_champ]# ne peut être utilisé.")));
										break;
									case INVALID_SYMBOL_FOR_FIELD :
										$error = str_replace("#param[condition_pluriel]#", ( $count_cdt>1 ? _("des conditions") : _("de la condition") ), str_replace("#param[operateur]#", $fld_name, _("Une erreur s'est produite lors de l'enregistrement #param[condition_pluriel]# de la promotion.<br />L'opérateur #param[operateur]# ne peut être utilisé pour ce champ personnalisé.")));
										break;
									case WRONG_SIZE_OF_VALUE :
										$error = str_replace("#param[condition_pluriel]#", ( $count_cdt>1 ? _("des conditions") : _("de la condition") ), _("Une erreur s'est produite lors de l'enregistrement #param[condition_pluriel]# de la promotion.<br />L'opérateur \"Est compris entre\" nécessite deux valeurs."));
										break;
									case VALUE_NOT_NUMERIC :
										$error = str_replace("#param[condition_pluriel]#", ( $count_cdt>1 ? _("des conditions") : _("de la condition") ), str_replace("#param[nom_champ]#", $fld_name, _("Une erreur s'est produite lors de l'enregistrement #param[condition_pluriel]# de la promotion.<br />Le champ #param[nom_champ]# nécessite une valeur de type numérique.")));
										break;
									case VALUE_NOT_INTEGER :
										$error = str_replace("#param[condition_pluriel]#", ( $count_cdt>1 ? _("des conditions") : _("de la condition") ), str_replace("#param[nom_champ]#", $fld_name, _("Une erreur s'est produite lors de l'enregistrement #param[condition_pluriel]# de la promotion.<br />Le champ #param[nom_champ]# nécessite un chiffre entier.")));
										break;
									case SELECT_VALUE_NOT_EXISTS :
										$error = str_replace("#param[condition_pluriel]#", ( $count_cdt>1 ? _("des conditions") : _("de la condition") ), str_replace("#param[nom_champ]#", $fld_name, _("Une erreur s'est produite lors de l'enregistrement #param[condition_pluriel]# de la promotion.<br />La valeur choisie ne peut être utilisée avec le champ personnalisé #param[nom_champ]#")));
										break;
									case VALUE_NOT_BOOLEAN :
										$error = str_replace("#param[condition_pluriel]#", ( $count_cdt>1 ? _("des conditions") : _("de la condition") ), str_replace("#param[nom_champ]#", $fld_name, _("Une erreur s'est produite lors de l'enregistrement #param[condition_pluriel]# de la promotion.<br />La valeur choisie ne peut être utilisée avec le champ personnalisé #param[nom_champ]#, veuillez choisir entre \"oui\" ou \"non\".")));
										break;
									default :  // la condition est valide
										if( !prc_price_conditions_add( $prc, $fld, $value, $sb ) )
											$error = str_replace("#param[condition_pluriel]#", ( $count_cdt>1 ? _("des conditions") : _("de la condition") ), _("Une erreur 2 inattendue s'est produite lors de l'enregistrement #param[condition_pluriel]# de la promotion.<br />Veuillez réessayer ou prendre contact avec l'administrateur."));
										if( $grp>0 && !$add_cdt_group ){
											if( !prc_group_conditions_add( $grp, $fld, $value, $sb ) )
												$error = str_replace("#param[condition_pluriel]#", ( $count_cdt>1 ? _("des conditions") : _("de la condition") ), _("Une erreur 3 inattendue s'est produite lors de l'enregistrement #param[condition_pluriel]# de la promotion.<br />Veuillez réessayer ou prendre contact avec l'administrateur."));
										}
										break;
								}
							}
						}
					}
					$add_cdt_group = true;
				}
			}
		}

		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Promotions'), '/admin/promotions/index.php' )
		->push( _('Promotions sur les produits'), '/admin/promotions/products/index.php' )
		->push( _('Nouvelle promotion') );

	define('ADMIN_PAGE_TITRE', _('Création') . ' - ' . _('Promotions sur les produits') . ' - ' . _('Promotions'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php echo _("Nouvelle promotion"); ?></h2>
<?php
	if( isset($error) )
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
?>
<form id="frm-add-promo" method="post" action="new.php" onsubmit="return verifPromotion(true)">
	<table id="promo-info">
		<caption><?php echo _("Informations"); ?></caption>
		<thead>
			<tr><th class="thead-none" colspan="2"></th></tr>
		</thead>
		<tfoot>
			<tr>
				<td class="td-promo-info">
					<input onclick="addCdtPromoProducts()" type="button" name="add-cdt" id="add-cdt" value="<?php echo _("Ajouter une condition"); ?>" />
				</td>
				<td>
					<input type="submit" name="save-pmt" id="save-pmt" value="<?php print _('Enregistrer'); ?>" />
					<input type="button" onclick="window.location.href='index.php';" name="cancel-pmt" id="cancel-pmt" value="<?php echo _("Annuler"); ?>" />
				</td>
			</tr>
		</tfoot>
		<tbody>
			<tr>
				<td><label for="date-start"><span class="mandatory">*</span> <?php echo _("Date de début :"); ?></label></td>
				<td>
					<input class="datepicker date" type="text" name="date-start" id="date-start" value="" autocomplete="off" />
					<label for="hour-start"><?php echo _("à"); ?></label>
					<input type="text" maxlength="5" value="00:00" id="hour-start" name="hour-start" class="hour hourpicker ac_input" />
				</td>
			</tr>
			<tr>
				<td><label for="date-end"><span class="mandatory">*</span> <?php echo _("Date de fin :"); ?></label></td>
				<td>
					<input class="datepicker date" type="text" name="date-end" id="date-end" value="" autocomplete="off" />
					<label for="hour-end"><?php echo _("à"); ?></label>
					<input type="text" maxlength="5" value="23:59" id="hour-end" name="hour-end" class="hour hourpicker ac_input" />
				</td>
			</tr>
			<tr>
				<td><label for="qte-min"><?php echo _("Pour une quantité minimale de :"); ?></label></td>
				<td><input class="qte" type="text" name="qte-min" id="qte-min" value="1" /></td>
			</tr>
			<tr>
				<td><label for="qte-max"><?php echo _("Pour une quantité maximale de :"); ?></label></td>
				<td><input class="qte" type="text" name="qte-max" id="qte-max" value="&#8734;" /></td>
			</tr>
			<tr>
				<td><label for="name"><?php echo _("Nom :"); ?></label></td>
				<td><input type="text" name="name" id="name" value="" /></td>
			</tr>
			<?php if( ctr_comparator_tenants_used(CTR_PRICEMINISTER) ){ ?>
				<tr>
					<td><label for="priceminister_key"><?php echo _("Campagne Priceminister :"); ?></label></td>
					<td><input type="text" name="priceminister_key" id="priceminister_key" value="" /></td>
				</tr>
			<?php } ?>
			<tr id="cdt-header" >
				<th colspan="2"><?php echo _("Conditions"); ?></th>
			</tr>
			<tr>
				<td colspan="2" id="cdts"></td>
			</tr>
		</tbody>
	</table>

	<div class="notice"><?php echo _("Ce tableau permet d'ajouter plusieurs produits en même temps, avec la même remise, dans cette promotion."); ?></div>
	<table id="tb-promo-remise">
		<caption><?php echo _("Groupe de produits"); ?></caption>
		<thead>
			<tr>
				<th><?php echo _("Remise du groupe"); ?></th>
			</tr>
		</thead>
		<tfoot>
			<tr>
				<td>
					<input onclick="addGroupInPromotion()" type="button" name="add-prd-promo" id="add-prd-promo" value="<?php echo _("Ajout à la promotion"); ?>" title="<?php echo _("Ajouter le groupe de produits à la promotion"); ?>" />
					<label for="ref-prd-add-1"><?php echo _("Référence produit :"); ?></label>
					<input onkeyup="searchReferencePrd('ref-prd-add-1')" type="text" name="ref-prd-add-1" id="ref-prd-add-1" value="" />
					<input type="button" name="add-prd" id="add-prd-grp" value="<?php echo _("Ajouter"); ?>" onclick="addProductInGroups()"/>
				</td>
			</tr>
		</tfoot>
		<tbody>
			<tr>
				<td>
					<select name="remise" id="remise">
						<option value="2"><?php echo _("Remise en %"); ?></option>
						<option value="3"><?php echo _("Remise en valeur"); ?></option>
					</select>
					<select name="remise-valeur-type" id="remise-valeur-type">
						<option value="1"><?php echo _("HT"); ?></option>
						<option value="2"><?php echo _("TTC"); ?></option>
					</select>
					<input type="text" name="remise-val" id="remise-val" value="" />
				</td>
			</tr>
			<tr>
				<th><?php echo _("Produits"); ?></th>
			</tr>
			<tr id="grp-none-prd">
				<td><?php echo _("Aucun produit"); ?></td>
			</tr>
			<tr>
				<td id="grp-prd-promo"></td>
			</tr>
		</tbody>
	</table>

	<div class="notice"><?php echo _("Ce tableau contient tous les produits ajoutés dans la promotion, il permet aussi d'ajouter ou modifier une remise produit par produit."); ?></div>
	<table id="tb-prd-promo">
		<caption><?php echo _("Produit(s)"); ?></caption>
		<thead>
			<tr>
				<th id="prd-ref" class="thead-none"><?php echo _('Référence'); ?></th>
				<th id="prd-remise" class="thead-none"><?php echo _("Remise"); ?></th>
				<th id="prd-remise-val" class="thead-none"><?php echo _("Valeur"); ?></th>
			</tr>
		</thead>
		<tfoot>
			<tr>
				<td colspan="3">
					<label for="ref-prd-add"><?php echo _("Référence produit :"); ?></label>
					<input onkeyup="searchReferencePrd('ref-prd-add')" type="text" name="ref-prd-add" id="ref-prd-add" value="" />
					<input type="button" name="add-prd" id="add-prd" value="<?php echo _("Ajouter"); ?>" onclick="addProductInPromotion()"/>
				</td>
			</tr>
		</tfoot>
		<tbody>
			<tr>
				<td id="none-prd" colspan="3"><?php echo _("La promotion doit contenir au minimum un produit."); ?></td>
			</tr>
		</tbody>
	</table>
</form>
<script><!--
	var fldOpt = '<?php print $fld_opt; ?>';
	var nb_new = 0;
	var typeOption = '<?php print $type_option; ?>';
	var typeOptionValeurType = '<option value="1">HT</option><option value="2">TTC</option>';
--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>