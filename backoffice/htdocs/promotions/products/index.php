<?php
	/**	\file index.php
	 *	Cette page permet d'afficher les différentes promotions appliquées sur les produits
	 */

	require_once('products.inc.php');
	require_once('prices.inc.php');
	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD');

	// Bouton Ajouter
	if( isset($_POST['promo-add']) ){
		header('Location: new.php');
		exit;
	}

	$ref_like = isset($_GET['like']) && in_array($_GET['like'], array('start', 'in', 'end', 'equal')) ? $_GET['like'] : 'equal';
	$ref_search = isset($_GET['search']) && trim($_GET['search']) != '' ? trim($_GET['search']) : '';
	$inp_date_start = isset($_GET['date_start']) && trim($_GET['date_start']) != '' ? $_GET['date_start'] : '';
	$inp_date_end = isset($_GET['date_end']) && trim($_GET['date_end']) != '' ? $_GET['date_end'] : '';

	$date_start = ($inp_date_start != '' ? $inp_date_start : false);
	$date_end = ($inp_date_end != '' ? $inp_date_end : false);

	// Récupère les conditions possibles
	$r_fld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array("prc_priority"), true, array(), null, null, true, true);

	$fld_opt = '<option value="-1">' . _("Choisissez un champ personnalisé") . '</option>';
	// Construit les option des select pour l'ajout d'une condition, le select sera construit lors de l'appel pour avoir l'identifiant du tarif
	if( $r_fld!==false && ria_mysql_num_rows($r_fld)>0 ){
		while( $fld = ria_mysql_fetch_array($r_fld) ){
			$fld_opt .= '<option value="'.$fld['id'].'">'.addslashes($fld['name']).' ('.addslashes($fld['cls_name']).')</option>';
		}
		// Retourne au premier index dans les tableaux MySQL
		ria_mysql_data_seek( $r_fld, 0 );
	}

	$_GET['group'] = isset($_GET['group']) ? $_GET['group'] : 0;

	// Détermine la période en cours de consultation
	$periode = isset($_GET['periode']) && ( $_GET['periode']>=0 || $_GET['periode']<8 ) ? $_GET['periode'] : false;
	if($periode === false){
		$periode = (session_get_periodpicker_state()) !== false ? session_get_periodpicker_state() : 6;
	}
	else{
		session_set_periodpicker_state($periode);
	}

	// Charge les promotions pour déterminer le nombre de lignes et ainsi préparer la pagination
	$rpmt = prc_prices_list_view( 0, 0, $periode, 0, true, $_GET['group'], 1, 0, 'desc',  (trim($ref_search) ? $ref_search : null), $ref_like, true, $date_start, $date_end);
	$pmt_count = $rpmt ? $rpmt : 0;
	$pages = ceil($pmt_count / 15);

	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Promotions'), '/admin/promotions/index.php' )
		->push( _('Promotions sur les produits'), '/admin/promotions/products/index.php' );

	define('ADMIN_PAGE_TITLE', _('Promotions sur les produits') . ' - ' . _('Promotions'));
	define('ADMIN_CLASS_BODY', 'app-promotions');
	require_once('admin/skin/header.inc.php');
?>

<h2>
	<?php echo _("Promotions sur les produits"); ?>
	(<?php print ria_number_format($pmt_count, NumberFormatter::DECIMAL); ?>)
</h2>

<?php if( $pages>1 ){ ?>
<form id="pmt-search" action="index.php" method="get">
	<div id="ref-search">
		<h3><?php echo _('Rechercher une promotion'); ?></h3>
		<div class="block-flex-wrap">

			<label for="search" class="label-promo-search"><?php echo _("La référence de l'article concerné :"); ?>&nbsp; </label>
			<div class="child-flex div-like">
				<select name="like" id="like">
					<option <?php print $ref_like == 'start' ? 'selected="selected"' : ''; ?> value="start"><?php echo _("Commence par"); ?></option>
					<option <?php print $ref_like == 'in' ? 'selected="selected"' : ''; ?> value="in"><?php echo _("Contient"); ?></option>
					<option <?php print $ref_like == 'end' ? 'selected="selected"' : ''; ?> value="end"><?php echo _("Se termine par"); ?></option>
					<option <?php print $ref_like == 'equal' ? 'selected="selected"' : ''; ?> value="equal"><?php echo _("Est"); ?></option>
				</select>
				<input type="text" name="search" id="search" value="<?php print $ref_search; ?>" maxlength="32" />
			</div>
		</div>
		<div class="block-flex-wrap">
			<label for="date_start"><?php echo _("Date de publication :"); ?> </label>
			<div class="child-flex">
				<label for="date_start"><?php echo _("Du :"); ?>
					<input class="datepicker date" type="text" name="date_start" id="date_start" value="<?php print $inp_date_start ?>"  maxlength="10" autocomplete="off" />
				</label>
				<label for="date_end"> <?php echo _("Au :"); ?>
					<input class="datepicker date" type="text" name="date_end" id="date_end" value="<?php print $inp_date_end ?>"  maxlength="10" autocomplete="off" />
				</label>
				<input type="submit" name="search-ref" value="<?php echo _("Rechercher"); ?>" />
			</div>
		</div>
		<div>

		</div>
	</div>
</form>
<?php } ?>

<form id="pmt-cdt" action="index.php" method="post">
	<div class="prices-menu">
		<?php
		view_state_periodpicker($periode, false, 7);
		?>
		<?php
			$groups = prc_promotion_groups_get( 0, false, $periode );
			if( $groups!=false && ria_mysql_num_rows($groups)>0 ){
		?>
		<div id="groupspromotionpicker" class="riapicker">
			<div class="selectorview">
				<div class="left">
					<span class="function_name"><?php echo _("Groupe"); ?></span><br/>
					<?php
						$modif_group = '';
						if( $_GET['group']>0 ){
							while( $group = ria_mysql_fetch_array($groups) ){
								if( $_GET['group']==$group['id'] ){
									print '<span class="view">'.$group['name'].'</span>';
									$modif_group = '<a href="edit.php?group='.$group['id'].'" class="link-modif-groupe">Modifier le groupe "'.$group['name'].'"</a>';
								}
							}
							ria_mysql_data_seek( $groups, 0 );
						} else {
							print '<span class="view">' . _("Toutes les promotions") . '</span>';
						}
					?>
				</div>
				<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" alt=""/></a>
				<div class="clear"></div>
			</div>
			<div class="selector">
				<a name="g-0&amp;periode=<?php print $periode; ?>"><?php echo _("Toutes les promotions"); ?></a>
				<?php
					while( $group = ria_mysql_fetch_array($groups) )
						print '<a name="g-'.$group['id'].'&amp;periode='.$periode.'">'.htmlspecialchars( $group['name'] ).'</a>';
				?>
			</div>
		</div>

		<?php print $modif_group; } ?>
	</div>
	<?php

		if( !isset($_GET['page']) ){
			$page = 1;
		}else{
			if( !is_numeric($_GET['page']) || $_GET['page']<0 ){
				$page = 1;
			}elseif( $_GET['page']>$pages ){
				$page = $pages;
			}else{
				$page = $_GET['page'];
			}
		}
	?>
	<div class="button-new-promotion">
		<?php if( gu_user_is_authorized('_RGH_ADMIN_PROMO_PRD_ADD') ){ ?>
		<input type="submit" name="promo-add" id="promo-add" value="<?php echo _("Nouvelle promotion"); ?>" class="btn-main" />
		<input type="button" name="import" value="<?php print _('Importer')?>" title="<?php print _('Importer des promotions')?>" onclick="window.location.href = '../../tools/imports/index.php?imp-class=45';" />
		<?php } ?>
	</div>
	<table class="prc-tva-eco checklist" id="lst-pmt">
		<caption class="thead-title-mobile">Promotions</caption>
		<thead class="thead-none">
			<tr>
				<th id="information"><?php echo _("Informations"); ?></th>
				<th id="conditions-prc"><?php echo _("Conditions"); ?></th>
				<th id="action"></th>
			</tr>
		</thead>
		<tbody>
			<?php
				// Affiche les informations sur les tarifs
				print prc_prices_list_view( 0, 0, $periode, 0, true, $_GET['group'], $page, 15, 'desc',  (trim($ref_search) ? $ref_search : null), $ref_like, false, $date_start, $date_end );
			?>
		</tbody>
		<tfoot>
			<?php if( $pages>1 ){ ?>
			<tr>
				<td class="align-left"><?php
					if( $pages==0 ){
						$pages = 1;
					}
					print $page.'/'.$pages;
				?></td>
				<td colspan="2" class="align-right"><?php
					$links = array();
					if( $page>1 ){
						$links[] = '<a href="index.php?'.( isset($_GET['group']) && is_numeric($_GET['group']) ? 'group='.$_GET['group'].'&amp;' : '' ).'periode='.$periode.'&amp;page='.($page-1).'">&laquo; ' . _('Page précédente') . '</a>';
					}
					for( $i=$page-5; $i<$page+5; $i++ )
						if( $i>=1 && $i<=$pages ){
							if( $i==$page )
								$links[] = '<b>'.$i.'</b>';
							else
								$links[] = '<a href="index.php?'.( isset($_GET['group']) && is_numeric($_GET['group']) ? 'group='.$_GET['group'].'&amp;' : '' ).'periode='.$periode.'&amp;page='.$i.'">'.$i.'</a>';
						}
					if( $page<$pages ){
						$links[] = '<a href="index.php?'.( isset($_GET['group']) && is_numeric($_GET['group']) ? 'group='.$_GET['group'].'&amp;' : '' ).'periode='.$periode.'&amp;page='.($page+1).'">' . _('Page suivante') . ' &raquo;</a>';
					}
					print implode(' | ',$links);
				?></td>
			</tr>
			<?php } ?>
		</tfoot>
	</table>
	<div class="float-right">
		<?php if( gu_user_is_authorized('_RGH_ADMIN_PROMO_PRD_ADD') ){ ?>
		<input type="submit" name="promo-add" id="promo-add2" value="<?php echo _("Nouvelle promotion"); ?>" />
		<input type="button" name="import" value="<?php print _('Importer')?>" title="<?php print _('Importer des promotions')?>" onclick="window.location.href = '../../tools/imports/index.php?imp-class=45';" />
		<?php } ?>
	</div>
	<?php
	if ($pmt_count > 0)
	{ ?>
		<span class="info-fld"><span class="color-red">*</span><?php echo _(" : Ce champ est obligatoire. Pour un nouveau tarif, la valeur doit être supérieure à 0, pour une remise en pourcentage, la valeur doit être inférieure ou égale à 100."); ?></span>
		<br /><span class="info-fld"><span class="color-red">**</span><?php echo _(" : Ce champ est obligatoire."); ?></span>
	<?php } ?>

</form>

<script><!--
	// Disable tous les champs/boutons si on accède à cette page en lecture seule
    <?php if( !gu_user_is_authorized('_RGH_ADMIN_PROMO_PRD_EDIT') ){ ?>
		$(document).ready(function(){
			$('table').find('input, select, textarea').attr('disabled', 'disabled');
			$('#promo-add').prop('disabled', false);
			$('#promo-add2').prop('disabled', false);
			$('.edit-url').attr('onclick','').unbind('click');
			$('input[onclick]').attr('onclick','').unbind('click');
		});
	<?php } ?>

	var fldOpt = '<?php print $fld_opt; ?>';
	var nb_new = 0;
	var passe = false
	var inp = '';
//--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>